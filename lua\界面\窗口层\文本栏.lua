
local 文本栏 =  窗口层:创建窗口("文本栏", 0, 0, 320, 185)
function 文本栏:初始化()
  
  self.可初始化=true

end

function 文本栏:打开(txt, xh, nt)
  self:置可见(true)
  self.提示文本:清空()
  local _,h
  _,h= self.提示文本:置文本(txt)
  if h <60 then
      h=60
  end
  self.提示文本:置高度(h)
  self.序号 = xh
  self.内容 = nt
  self.确定:置坐标(40,h+40)
  self.取消:置坐标(230,h+40)

  self:置精灵(置窗口背景("无", 0, 0, 320, h+70))
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
end
local 提示文本 = 文本栏:创建文本("提示文本",10,35,300,0)
function 提示文本:初始化()
  self:置文字(文本字体)
end

local 按钮设置={"确定","取消"}

for i, v in ipairs(按钮设置) do
  local 临时函数 = 文本栏:创建红色按钮(v, v,0,0,50,22)
 function  临时函数:左键弹起(x, y)
    if v == "确定" and 文本栏.序号 then
        请求服务(文本栏.序号, 文本栏.内容)
    end
    文本栏:置可见(false)
    鼠标层.附加=nil
  end
end
