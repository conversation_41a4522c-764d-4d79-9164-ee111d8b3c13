local 战斗动画 = class("战斗动画")
function 战斗动画:初始化()
    self.py = { 0, 0 }
    self.cp = 2
    self.动画 = {}
	  self.武器 = {}
    self.光环 = {}
    self.副武器= {}
    self.模型资源 = {}
    self.锦衣资源 = {}
    self.武器资源 = {}
    self.动作特效 = {}
    self.怪物饰品 = {}
    self.攻击特效 = {}
    self.武器数据 = ""
    self.单位模型 = ""
    self.显示武器 = false
    self.动作资源={"待战","跑去","防御","攻击","暴击","挨打","返回","死亡","施法"}
    self.影子=__res:取资源动画('dlzy',0xDCE4B562,"精灵")

end


function 战斗动画:创建动画(模型, 类型, 染色方案, 染色组, 变异, 武器, 变身, 饰品, 锦衣,武器染色, 武器染色组, 饰品染色, 饰品染色组,副武器,攻击特效)
          self.动画 = {}
		      self.武器 = {}
		      self.光环 = {}
          self.副武器= {}
          self.模型资源 = {}
          self.锦衣资源 = {}
          self.武器资源 = {}
          self.动作特效 = {}
          self.怪物饰品 = {}
          self.攻击特效 = {}
          self.武器数据 = ""
          self.单位模型 = 模型
          self.显示武器 = false
          if 变身 and  __res.配置.变身造型~=1 then
              类型="变身"
              self.单位模型=变身
              if 变异 and __染色信息[self.单位模型] then
                  染色方案 = __染色信息[self.单位模型].id
                  染色组  = __染色信息[self.单位模型].方案
              else
                  染色方案 = nil
                  染色组 = nil
              end
          end
          self.模型资源 = 取战斗模型(self.单位模型)
          if 类型=="角色" or 类型=="系统PK角色" or 类型=="系统角色" then
              if 武器  then
                  if 武器.名称 == "龙鸣寒水" or 武器.名称 == "非攻" then
                      self.武器数据 = "弓弩1"
                  else
                      self.武器数据 =  _tp:取武器子类(武器.子类)
                  end
                  self.模型资源 = 取战斗模型(self.单位模型,self.武器数据)
              end
              self.动画.待战 =__res:取资源动画(self.模型资源[10], self.模型资源[6],"置动画"):置循环(true)
              self.动画.跑去 = __res:取资源动画(self.模型资源[10], self.模型资源[8],"置动画"):置循环(true)
              self.动画.防御 = __res:取资源动画(self.模型资源[10], self.模型资源[4],"置动画")
              self.动画.攻击 = __res:取资源动画(self.模型资源[10], self.模型资源[1],"置动画")
              self.动画.暴击 = __res:取资源动画(self.模型资源[10], self.模型资源[2],"置动画")
              self.动画.挨打 = __res:取资源动画(self.模型资源[10], self.模型资源[9],"置动画")
              self.动画.返回 = __res:取资源动画(self.模型资源[10], self.模型资源[8],"置动画"):置循环(true)
              self.动画.死亡 = __res:取资源动画(self.模型资源[10], self.模型资源[3],"置动画")
              self.动画.施法 = __res:取资源动画(self.模型资源[10], self.模型资源[7],"置动画")
              if 锦衣 and 锦衣[1] and 锦衣[1].名称 and __res.配置.锦衣效果~=1 then
                  if 锦衣[1].战斗锦衣 then
                      self.锦衣资源 = 取战斗锦衣素材(锦衣[1].名称,self.单位模型)
                      self.动画.待战 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[3],"置动画"):置循环(true)
                      self.动画.跑去 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[4],"置动画"):置循环(true)
                      self.动画.返回 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[4],"置动画"):置循环(true)
                      self.动画.防御 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[3],"置动画")
                      self.动画.攻击 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[1],"置动画")
                      self.动画.暴击 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[1],"置动画")
                      self.动画.挨打 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[3],"置动画")
                      self.动画.施法 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[2],"置动画")
                      if self.锦衣资源[6] then
                          self.动画.挨打 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[6],"置动画")
                      end
                      if self.锦衣资源[7] then
                          self.动画.死亡 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[7],"置动画")
                      end
                      self.显示武器 = true
                  else
                      if 新加战斗锦衣[锦衣[1].名称] then
                          local 武器子类 =nil
                          if 武器 and 武器.子类 then
                              武器子类= 武器.子类
                          end
                          self.锦衣资源 = 取武器锦衣素材(锦衣[1].名称,self.单位模型,武器子类)
                          self.动画.待战 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[3],"置动画"):置循环(true)
                          self.动画.跑去 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[4],"置动画"):置循环(true)
                          self.动画.返回 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[4],"置动画"):置循环(true)
                          self.动画.防御 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[3],"置动画")
                          self.动画.攻击 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[1],"置动画")
                          self.动画.暴击 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[1],"置动画")
                          self.动画.挨打 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[3],"置动画")
                          self.动画.施法 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[2],"置动画")
                          if self.锦衣资源[6] then
                              self.动画.挨打 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[6],"置动画")
                          end
                          if self.锦衣资源[7] then
                              self.动画.死亡 = __res:取资源动画(self.锦衣资源[5],self.锦衣资源[7],"置动画")
                          end
                          self.显示武器 = false
                      end
                  end
                  self.动画.死亡 =__res:取资源动画("jszy/fwtb",0x001E9FE2,"置动画")
                  染色方案 = nil
                  染色组 = nil
              end
              if 锦衣 and 锦衣[2] and 锦衣[2].名称 and  __res.配置.光环足迹~=1 then
                  local n = 取光环(锦衣[2].名称)
                  for i=1,#self.动作资源 do
                      self.光环[self.动作资源[i]]=__res:取资源动画(n[4],n[1],"置动画"):置循环(true)
                  end
                  self.光环.跑去=__res:取资源动画(n[4],n[2],"置动画"):置循环(true)
                  self.光环.返回=__res:取资源动画(n[4],n[2],"置动画"):置循环(true)
              end
              if self.武器数据 and self.武器数据~="" and 武器 then
                  local m = _tp:取武器附加名称(武器.子类,武器.级别限制,武器.名称)
                  self.武器资源 = 取战斗模型(m .. "_" .. 模型)
                  if self.武器数据  == "弓弩" or self.武器数据  == "弓弩1" then
                      self.攻击方式 = 1
                  end
                  if self.武器资源[10] then
                      if 锦衣 and 锦衣[1] and 锦衣[1].名称 and __res.配置.锦衣效果~=1 then
                          local n = 取模型(m.."_"..self.单位模型)
                          self.武器.待战 = __res:取资源动画(n[3],n[1],"置动画"):置循环(true)
                          self.武器.防御 = __res:取资源动画(n[3],n[1],"置动画")
                          self.武器.挨打 = __res:取资源动画(n[3],n[1],"置动画")
                          self.武器.跑去 = __res:取资源动画(n[3],n[2],"置动画"):置循环(true)
                          self.武器.返回 = __res:取资源动画(n[3],n[2],"置动画"):置循环(true)
                      else
                          self.武器.待战 = __res:取资源动画(self.武器资源[10],self.武器资源[6],"置动画"):置循环(true)
                          self.武器.防御 = __res:取资源动画(self.武器资源[10],self.武器资源[4],"置动画")
                          self.武器.挨打 = __res:取资源动画(self.武器资源[10],self.武器资源[9],"置动画")
                          self.武器.跑去 = __res:取资源动画(self.武器资源[10],self.武器资源[8],"置动画"):置循环(true)
                          self.武器.返回 = __res:取资源动画(self.武器资源[10],self.武器资源[8],"置动画"):置循环(true)
                      end
                      self.武器.攻击 = __res:取资源动画(self.武器资源[10],self.武器资源[1],"置动画")
                      self.武器.暴击 = __res:取资源动画(self.武器资源[10],self.武器资源[2],"置动画") 
                      self.武器.施法 = __res:取资源动画(self.武器资源[10],self.武器资源[7],"置动画")
                      if self.武器资源[3] and self.武器资源[3]~=0 then
                          self.武器.死亡 = __res:取资源动画(self.武器资源[10],self.武器资源[3],"置动画")
                      end
                      if self.模型资源[15] and self.模型资源[11] and self.模型资源[12] then
                          self.动作特效 = {}
                          self.动作特效.攻击 = __res:取资源动画(self.模型资源[15],self.模型资源[11],"置动画")
                          self.动作特效.暴击 = __res:取资源动画(self.模型资源[15],self.模型资源[11],"置动画")
                          self.动作特效.施法 = __res:取资源动画(self.模型资源[15],self.模型资源[12],"置动画")
                          if self.模型资源[16] then
                              self.动作特效.暴击 = __res:取资源动画(self.模型资源[15],self.模型资源[16],"置动画")
                          end
                          if 武器.级别限制>90 then
                              if self.模型资源[13] then
                                  self.动作特效.攻击 = __res:取资源动画(self.模型资源[15],self.模型资源[13],"置动画")
                                  self.动作特效.暴击 = __res:取资源动画(self.模型资源[15],self.模型资源[13],"置动画")
                              end
                              if self.模型资源[16] then
                                  self.动作特效.暴击 = __res:取资源动画(self.模型资源[15],self.模型资源[16],"置动画")
                              end
                              if self.模型资源[17] then
                                  self.动作特效.暴击 = __res:取资源动画(self.模型资源[15],self.模型资源[17],"置动画")
                              end
                              if self.模型资源[14] then
                                  self.动作特效.施法 = __res:取资源动画(self.模型资源[15],self.模型资源[14],"置动画")
                              end
                          end
                      end
                      if 武器.染色方案 and 武器.染色方案~=0 and 武器.染色组 and 武器.染色组~=0 and #武器.染色组>0 then
                          local 调色板  = __dewpal(武器.染色方案)
                          for n=1,#self.动作资源 do
                              self.武器[self.动作资源[n]]:调色(调色板,取调色数据(武器.染色组))
                          end
                      end
                      if 武器染色 and 武器染色~=0 and 武器染色组 and 武器染色组~=0 and #武器染色组>0 then
                          local 调色板  = __dewpal(武器染色)
                          for n=1,#self.动作资源 do
                              self.武器[self.动作资源[n]]:调色(调色板,取调色数据(武器染色组))
                          end
                      end
                  end
				      end
              if 副武器 and 副武器.名称 then--and self.单位模型=="影精灵" and string.find(副武器.名称,"(坤)") and (not 武器 or string.find(武器.名称,"(乾)")) 
                  self.武器资源 = 取战斗模型(副武器.名称.."_"..self.单位模型)
                  if self.武器资源[10] then
                      if 锦衣 and 锦衣[1] and 锦衣[1].名称 and __res.配置.锦衣效果~=1 then
                          local n = 取模型(副武器.名称.."_"..self.单位模型)
                          self.副武器.待战 = __res:取资源动画(n[3],n[1],"置动画"):置循环(true)
                          self.副武器.防御 = __res:取资源动画(n[3],n[1],"置动画")
                          self.副武器.挨打 = __res:取资源动画(n[3],n[1],"置动画")
                          self.副武器.跑去 = __res:取资源动画(n[3],n[2],"置动画"):置循环(true)
                          self.副武器.返回 = __res:取资源动画(n[3],n[2],"置动画"):置循环(true)
                      else
                          self.副武器.待战 = __res:取资源动画(self.武器资源[10] ,self.武器资源[6],"置动画"):置循环(true)
                          self.副武器.防御 = __res:取资源动画(self.武器资源[10] ,self.武器资源[4],"置动画")
                          self.副武器.挨打 = __res:取资源动画(self.武器资源[10] ,self.武器资源[9],"置动画")
                          self.副武器.跑去 = __res:取资源动画(self.武器资源[10] ,self.武器资源[8],"置动画"):置循环(true)
                          self.副武器.返回 = __res:取资源动画(self.武器资源[10] ,self.武器资源[8],"置动画"):置循环(true)
                      end
                      self.副武器.攻击 = __res:取资源动画(self.武器资源[10] ,self.武器资源[1],"置动画")
                      self.副武器.暴击 = __res:取资源动画(self.武器资源[10] ,self.武器资源[2],"置动画")
                      self.副武器.施法 = __res:取资源动画(self.武器资源[10] ,self.武器资源[7],"置动画")
                      if self.武器资源[3] and self.武器资源[3]~=0 then
                          self.副武器.死亡 = __res:取资源动画(self.武器资源[10] ,self.武器资源[3],"置动画")
                      end
                      if 副武器.染色方案 and 副武器.染色方案~=0 and 副武器.染色组 and 副武器.染色组~=0 and #副武器.染色组>0 then
                          local 调色板  = __dewpal(副武器.染色方案)
                          for n=1,#self.动作资源 do
                              self.副武器[self.动作资源[n]]:调色(调色板,取调色数据(副武器.染色组))
                          end
                      end
                      if self.模型资源[22] and self.模型资源[18] and self.模型资源[19] then
                          self.攻击特效 = {}
                          self.攻击特效.攻击 = __res:取资源动画(self.模型资源[22] ,self.模型资源[18],"置动画")
                          self.攻击特效.暴击 = __res:取资源动画(self.模型资源[22] ,self.模型资源[18],"置动画")
                          self.攻击特效.施法 = __res:取资源动画(self.模型资源[22] ,self.模型资源[19],"置动画")
                          if self.模型资源[23] then
                              self.攻击特效.暴击 = __res:取资源动画(self.模型资源[22] ,self.模型资源[23],"置动画")
                          end
                          if 副武器.级别限制>90 then
                              if self.模型资源[20] then
                                  self.攻击特效.攻击 = __res:取资源动画(self.模型资源[22] ,self.模型资源[20],"置动画")
                                  self.攻击特效.暴击 = __res:取资源动画(self.模型资源[22] ,self.模型资源[20],"置动画")
                              end
                              if self.模型资源[23] then
                                  self.攻击特效.暴击 = __res:取资源动画(self.模型资源[22] ,self.模型资源[23],"置动画")
                              end
                              if self.模型资源[24] then
                                  self.攻击特效.暴击 = __res:取资源动画(self.模型资源[22] ,self.模型资源[24],"置动画")
                              end
                              if self.模型资源[21] then
                                  self.攻击特效.施法 = __res:取资源动画(self.模型资源[22] ,self.模型资源[21],"置动画")
                              end
                          end
                      end
                  end
              end
              if self.显示武器 then
                  self.武器 = {}
                  self.副武器 = {}
              end
          else-------------------宠物 self.武器.待战 = __res:取资源动画('mx',self.武器资源[6],"置动画"):置循环(true)
              self.模型资源 = 取战斗模型(self.单位模型)
              self.动画.待战 = __res:取资源动画(self.模型资源[10],self.模型资源[6],"置动画"):置循环(true)
              self.动画.跑去 = __res:取资源动画(self.模型资源[10],self.模型资源[8],"置动画"):置循环(true)
              self.动画.防御 =__res:取资源动画(self.模型资源[10],self.模型资源[4],"置动画")
              self.动画.攻击 =__res:取资源动画(self.模型资源[10],self.模型资源[1],"置动画")---人物模型第一套攻击动作
              self.动画.暴击 = __res:取资源动画(self.模型资源[10],self.模型资源[2],"置动画")---人物模型第一套攻击动作
              self.动画.挨打 =__res:取资源动画(self.模型资源[10],self.模型资源[9],"置动画")
              self.动画.返回 =__res:取资源动画(self.模型资源[10],self.模型资源[8],"置动画"):置循环(true)
              self.动画.死亡 =__res:取资源动画(self.模型资源[10],self.模型资源[3],"置动画")
              self.动画.施法 =__res:取资源动画(self.模型资源[10],self.模型资源[7],"置动画")
              if 饰品 then
                  self.武器资源 = 取战斗模型(self.单位模型.."_饰品")  ---这里显示宠物饰品的
                  if self.武器资源[10] then
                      self.怪物饰品.待战 = __res:取资源动画(self.武器资源[10],self.武器资源[6],"置动画"):置循环(true)
                      self.怪物饰品.跑去 =  __res:取资源动画(self.武器资源[10],self.武器资源[8],"置动画"):置循环(true)
                      self.怪物饰品.防御 =  __res:取资源动画(self.武器资源[10],self.武器资源[4],"置动画")
                      self.怪物饰品.攻击 =  __res:取资源动画(self.武器资源[10],self.武器资源[1],"置动画")---饰品第一套攻击动作
                      self.怪物饰品.暴击 =  __res:取资源动画(self.武器资源[10],self.武器资源[2],"置动画")---饰品第一套攻击动作
                      self.怪物饰品.挨打 =  __res:取资源动画(self.武器资源[10],self.武器资源[9],"置动画")
                      self.怪物饰品.返回 =  __res:取资源动画(self.武器资源[10],self.武器资源[8],"置动画"):置循环(true)
                      self.怪物饰品.死亡 =  __res:取资源动画(self.武器资源[10],self.武器资源[3],"置动画")
                      self.怪物饰品.施法 =  __res:取资源动画(self.武器资源[10],self.武器资源[7],"置动画")
                      if 饰品染色 and 饰品染色组 then
                          local 调色板  = __dewpal(饰品染色)
                          for n=1,#self.动作资源 do
                              if self.怪物饰品[self.动作资源[n]] then
                                  self.怪物饰品[self.动作资源[n]]:调色(调色板,取调色数据(饰品染色组))
                              end
                          end
                      end
                  end
              end
              if self.模型资源[15] and self.模型资源[11] and self.模型资源[12] then
                        self.动作特效 = {}
                        self.动作特效.攻击 = __res:取资源动画(self.模型资源[15],self.模型资源[11],"置动画")
                        self.动作特效.暴击 = __res:取资源动画(self.模型资源[15],self.模型资源[11],"置动画")
                        self.动作特效.施法 = __res:取资源动画(self.模型资源[15],self.模型资源[12],"置动画")
                        if  self.模型资源[16] then
                            self.动作特效.暴击 = __res:取资源动画(self.模型资源[15],self.模型资源[16],"置动画")
                        end
              end
              if 变异 and __染色信息[self.单位模型] then
                  染色方案 = __染色信息[self.单位模型].id
                  染色组  = __染色信息[self.单位模型].方案
              end
          end
          if 染色方案 and 染色方案~=0 and 染色组 and 染色组~=0 and #染色组>0 then
              local 调色板  = __dewpal(染色方案)
              for n=1,#self.动作资源 do
                  self.动画[self.动作资源[n]]:调色(调色板,取调色数据(染色组))
              end
           end
           if __手机 then
                self:置全部提速(1.2)
           end 
         --  self:置全部提速(0.8)
          --  if 攻击特效 then
          --      local 攻击动画 
          --      local 施法动画
          --      local 暴击动画
               
          --      if 攻击特效=="xxx" then
          --           施法动画 = __res:取资源动画(self.模型资源[15],self.模型资源[12],"置动画")
          --      elseif 攻击特效=="xxx" then

          --      end


          --     if  self.动作特效  then
          --          if 攻击动画 then
          --             self.动作特效.攻击 =  攻击动画
          --           end
          --           if 施法动画 then
          --             self.动作特效.施法 =  施法动画

          --           end

          --     else
          --         self.动作特效 = {}
          --         if 攻击动画 then
          --             self.动作特效.攻击 =  攻击动画
          --         end
          --         if 施法动画 then
          --             self.动作特效.施法 =  施法动画
          --         end
                
          --     end
          --  end


end

function 战斗动画:置全部提速(fx)
    for n=1,#self.动作资源 do
        if self.动画 and self.动画[self.动作资源[n]] then
            self.动画[self.动作资源[n]]:置提速(fx)
        end
        if self.动作特效 and self.动作特效[self.动作资源[n]] then
            self.动作特效[self.动作资源[n]]:置提速(fx)
        end
        if self.攻击特效 and self.攻击特效[self.动作资源[n]] then
            self.攻击特效[self.动作资源[n]]:置提速(fx)
        end
        if self.武器 and self.武器[self.动作资源[n]] then
            self.武器[self.动作资源[n]]:置提速(fx)
        end
        if self.副武器 and self.副武器[self.动作资源[n]] then
            self.副武器[self.动作资源[n]]:置提速(fx)
        end
        if self.怪物饰品 and self.怪物饰品[self.动作资源[n]] then
            self.怪物饰品[self.动作资源[n]]:置提速(fx)
        end
    end
end





function 战斗动画:置方向(方向, 动作)
    self.动画[动作]:置方向(方向)
    self.动画[动作]:置首帧():恢复()
    if self.动作特效 and self.动作特效[动作] then
        self.动作特效[动作]:置方向(方向)
        self.动作特效[动作]:置首帧():恢复()
    end
    if self.攻击特效 and self.攻击特效[动作] then
        self.攻击特效[动作]:置方向(方向)
        self.攻击特效[动作]:置首帧():恢复()
    end
    if self.武器 and self.武器[动作] then
        self.武器[动作]:置方向(方向)
        self.武器[动作]:置首帧():恢复()
    end
    if self.副武器 and self.副武器[动作] then
        self.副武器[动作]:置方向(方向)
        self.副武器[动作]:置首帧():恢复()
    end
    if self.怪物饰品 and self.怪物饰品[动作] then
        self.怪物饰品[动作]:置方向(方向)
        self.怪物饰品[动作]:置首帧():恢复()
    end
end

function 战斗动画:置帧率(动作, 帧率)
    self.动画[动作]:置帧率(帧率)
    if self.动作特效 and self.动作特效[动作] then
        self.动作特效[动作]:置帧率(帧率)
    end
    if self.攻击特效 and self.攻击特效[动作] then
        self.攻击特效[动作]:置帧率(帧率)
    end
    if  self.武器 and self.武器[动作] then
        self.武器[动作]:置帧率(帧率)
    end
    if  self.副武器 and self.副武器[动作] then
        self.副武器[动作]:置帧率(帧率)
    end
    if self.怪物饰品 and self.怪物饰品[动作] then
        self.怪物饰品[动作]:置帧率(帧率)
    end
    if self.光环 and self.光环[动作] then
        self.光环[动作]:置帧率(帧率)
    end

end

function 战斗动画:取当前帧(动作)
    if self.动画[动作] == nil then
        return 1
    end
    return self.动画[动作]:取当前帧()
end

function 战斗动画:取结束帧(动作)
    if self.动画[动作] == nil then
        return 1
    end
    return self.动画[动作]:取帧数()
end

function 战斗动画:取开始帧(动作)
    if self.动画[动作] == nil then
        return 1
    end
    return self.动画[动作]:取开始帧()
end

function 战斗动画:检查点(x, y, 动作)
    return self.动画[动作]:检查点( x, y)
end

function 战斗动画:是否播放(动作)
    return self.动画[动作]:是否播放()
end

function 战斗动画:更新(dt, 动作)
    self.动画[动作]:更新( dt)
    if self.动画[动作] ~= nil then
        if self.动作特效 and self.动作特效[动作] then
            self.动作特效[动作]:更新(dt)
        end
        if self.攻击特效 and self.攻击特效[动作] then
            self.攻击特效[动作]:更新(dt)
        end
        if self.武器 and self.武器[动作] then
            self.武器[动作]:更新(dt)
        end
        if self.副武器 and self.副武器[动作] then
            self.副武器[动作]:更新(dt)
        end
        if self.怪物饰品 and self.怪物饰品[动作] then
            self.怪物饰品[动作]:更新(dt)
        end
        if self.光环 and self.光环[动作] then
            self.光环[动作]:更新(dt)
        end

    end
end

function 战斗动画:显示(x, y, 动作)
    if self.光环 and self.光环[动作] then
        self.光环[动作]:显示(x, y)
    end
    self.影子:显示(x, y)
    self.动画[动作]:显示( x, y)
    if self.武器 and self.武器[动作] then
        self.武器[动作]:显示(x, y)
    end
    if self.副武器 and self.副武器[动作] then
        self.副武器[动作]:显示(x, y)
    end
    if self.动作特效 and self.动作特效[动作] then
        self.动作特效[动作]:显示(x, y)
    end
    if self.攻击特效 and self.攻击特效[动作] then
        self.攻击特效[动作]:显示(x, y)
    end
    if self.怪物饰品 and self.怪物饰品[动作] then
        self.怪物饰品[动作]:显示(x, y)
    end
   

end

function 战斗动画:置高亮(动作,...)
  
    self.动画[动作]:置高亮(...)
   
    if self.武器 and  self.武器[动作] then
        self.武器[动作]:置高亮(...)
    end
    if self.副武器 and  self.副武器[动作] then
        self.副武器[动作]:置高亮(...)
    end
    if self.动作特效 and self.动作特效[动作] then
        self.动作特效[动作]:置高亮(...)
    end
    if self.攻击特效 and self.攻击特效[动作] then
        self.攻击特效[动作]:置高亮(...)
    end
    if self.怪物饰品 and self.怪物饰品[动作] then
        self.怪物饰品[动作]:置高亮(...)
    end
    if self.光环 and self.光环[动作] then
        self.光环[动作]:置高亮(...)
    end

end








return 战斗动画



