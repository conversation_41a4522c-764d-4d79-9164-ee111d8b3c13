local 基类 = require("界面/控件层/基类/物品基类")
local 材料格子 = class("材料格子", 基类)
function 材料格子:初始化()
  self.py = {x = 0, y = 0}
end
function 材料格子:置物品(数据,符石)
  self.模型 = nil
  self.物品 = nil
  local nsf = require("SDL.图像")(55, 55)
  if 符石 then
    if 数据 then
      self:取数据(数据)
      if nsf["渲染开始"](nsf) then
        -- __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
        if self.物品.颜色区分 then
          __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"])):置颜色(检查是否有物品颜色(self.物品.属性)):显示(0, 0)
          else
          __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"])), 0, 0)
          end
        -- __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"])), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self.模型 = nsf["到精灵"](nsf)
    else
      if nsf["渲染开始"](nsf) then
        -- __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
        -- __res:getPNGCC(3, 777, 550, 45, 46)["显示"](__res:getPNGCC(3, 777, 550, 45, 46), 4, 4)
        nsf["渲染结束"](nsf)
      end
      self.模型 = nsf["到精灵"](nsf)
    end
  else
    if 数据 then
      self:取数据(数据)
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
        if self.物品.颜色区分 then
          __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"])):置颜色(检查是否有物品颜色(self.物品.属性)):显示(0, 0)
          else
          __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"])), 0, 0)
          end
        -- __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", self.物品["小模型资源"])), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self.模型 = nsf["到精灵"](nsf)
    else
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
        __res:getPNGCC(3, 777, 550, 45, 46)["显示"](__res:getPNGCC(3, 777, 550, 45, 46), 4, 4)
        nsf["渲染结束"](nsf)
      end
      self.模型 = nsf["到精灵"](nsf)
    end
  end
  self.数据 = 数据
end
function 材料格子:详情打开(x, y, w, h, lx, bh)
  __UI弹出["bb进阶详情"]["置可见"](__UI弹出["bb进阶详情"], true, true)
  __UI弹出["bb进阶详情"]["道具文本"]["清空"](__UI弹出["bb进阶详情"]["道具文本"])
  __UI弹出["bb进阶详情"]:打开(self.物品, x, y, 360, 360, Button, Button2, Button3, Button4, bh, lx)
end
function 材料格子:更新(dt)
end
function 材料格子:显示(x, y)
  if self.模型 then
    self.模型["显示"](self.模型, x + self.py.x, y + self.py.y)
  end
  if self.确定 then
    __主控["道具选中小"]["显示"](__主控["道具选中小"], x, y)
  end
end
return 材料格子
