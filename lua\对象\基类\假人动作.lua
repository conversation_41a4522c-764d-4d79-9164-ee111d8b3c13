local 假人动作 = class("假人动作")
local ggf = require("GGE.函数")
local SDL = require("SDL")
function 假人动作:初始化(t)
    self.模型 = t.模型
    self.锦衣 = t.锦衣 
    self.变身数据 = t.变身数据
    self.坐骑 = t.坐骑
    self.装备 = t.武器
    self.染色方案=t.染色方案
    self.染色组 = t.染色组
    self.武器子类 = t.武器子类
    self.变异=t.变异
    self.饰品 = t.显示饰品
    if self.变异  and __染色信息[self.模型]~=nil then
        self.染色方案 = __染色信息[self.模型].id
        self.染色组 = __染色信息[self.模型].方案
    end
    if t.武器染色方案 and t.武器染色组 and t.武器染色方案~=0 and t.武器染色组~=0 then
        self.武器染色方案 = t.武器染色方案
        self.武器染色组 = t.武器染色组
    end
    if t.模型=="宝箱" then t.方向=0 end
    if t.方向 then
	   self.方向 = t.方向+1
    else
        self.方向 =1
    end
    self.动作 = "静立"
    self.cur_action = {}
    self.模型编号 = {}
    self:置模型()
    self.影子 = __res:取资源动画('dlzy',0xDCE4B562,"精灵")


 
end



function 假人动作:置模型()
    self.人物={}
    self.坐骑显示={}
    self.武器显示={}
	local 资源 = 取模型(self.模型)
    if self.坐骑 and self.坐骑.模型 then
        local 资源组 = {}
        if 新增坐骑(self.模型,self.坐骑.模型,"站立") ~= nil and 新增坐骑(self.模型,self.坐骑.模型,"站立") ~= ""  then
            资源组.人物资源 = "jszy/xzzq"
            资源组.人物站立 = 新增坐骑(self.模型,self.坐骑.模型,"站立")
            资源组.人物行走 = 新增坐骑(self.模型,self.坐骑.模型,"奔跑")
            资源组.坐骑资源 = "jszy/xzzq"
            资源组.坐骑行走 = 新增坐骑(self.模型,self.坐骑.模型,"奔跑")
            资源组.坐骑站立 = 新增坐骑(self.模型,self.坐骑.模型,"站立")
        else
            资源组 = 坐骑库(self.模型,self.坐骑.模型,self.坐骑.饰品 or "空")
        end
        if 资源组 then
            self.坐骑显示.静立 = __res:取资源动画(资源组.坐骑资源,资源组.坐骑站立,"置动画"):置循环(true)
            self.坐骑显示.行走 = __res:取资源动画(资源组.坐骑资源,资源组.坐骑行走,"置动画"):置循环(true)
                    
            if 资源组.坐骑饰品站立 ~= nil then
            self.武器显示.静立 = __res:取资源动画(资源组.坐骑饰品资源,资源组.坐骑饰品站立,"置动画"):置循环(true)
            self.武器显示.行走 = __res:取资源动画(lssj.坐骑饰品资源,资源组.坐骑饰品站立,"置动画"):置循环(true)
            end
            self.人物.静立 = __res:取资源动画(资源组.人物资源,资源组.人物站立,"置动画"):置循环(true)
            self.人物.行走 = __res:取资源动画(资源组.人物资源,资源组.人物行走,"置动画"):置循环(true)
            local zqx, zqy = 坐骑补差(self.坐骑.模型, self.模型)
            self.人物.静立.pyx, self.人物.静立.pyy = zqx, zqy
            self.人物.行走.pyx, self.人物.行走.pyy = zqx, zqy
            self.染色方案=nil
            self.染色组=nil
        else
            self.人物.静立 =  __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
            self.人物.行走 =  __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
        end
    else
        local  显示武器 = true
         if self.锦衣 ~= nil and self.锦衣 ~= 0  then    
                if self.锦衣=="青春" or self.锦衣=="素颜" or self.锦衣=="绝色" or self.锦衣=="春秋" or  self.锦衣=="夏蚕"
                or self.锦衣=="星河" or self.锦衣=="白峨" or self.锦衣=="糖果" or self.锦衣=="青涩" or self.锦衣=="傲然"
                or self.锦衣=="牛仔" or  self.锦衣=="试剑" or self.锦衣=="骨龙战骑" or self.锦衣=="水嘟嘟·钻白"or self.锦衣=="斗战神"
                or self.锦衣=="斗战胜佛" or  self.锦衣=="八部天龙马·玄" or  self.锦衣=="龙凰·桃" or  self.锦衣=="龙凰·皑"  then
                    资源 = 取战斗锦衣素材(self.锦衣,self.模型)
                    self.人物.静立 =  __res:取资源动画(资源[5],资源[3],"置动画"):置循环(true)
                    self.人物.行走 =  __res:取资源动画(资源[5],资源[4],"置动画"):置循环(true)
                    显示武器 = false
                elseif 新加战斗锦衣[self.锦衣]~=nil  then
                    资源 = 取武器锦衣素材(self.锦衣,self.模型,self.武器子类)
                    self.人物.静立 =  __res:取资源动画(资源[5],资源[3],"置动画"):置循环(true)
                    self.人物.行走 =  __res:取资源动画(资源[5],资源[4],"置动画"):置循环(true)
                    显示武器 = true
                end
                self.染色方案=nil
                self.染色组=nil
         else
               self.人物.静立 =  __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
               self.人物.行走 =  __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
         end
         if self.装备 and 显示武器  then
             if not self.锦衣 and self.武器子类 then
                local m = _tp:取武器子类(self.武器子类)
                资源 = 取模型(self.模型, m)
                self.人物.静立 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
                self.人物.行走 = __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
             end
           
            local 物品 = 取物品(self.装备)
            local ms = _tp:取武器附加名称(物品[4], 物品[5],self.装备)
            资源 = 取模型(ms .. "_" .. self.模型)
             if 资源[1] and 资源[2] then
                self.武器显示.静立 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
                self.武器显示.行走 = __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
                    if self.武器染色方案~=nil and self.武器染色方案~=0 and self.武器染色组~=nil and #self.武器染色组>0 then
                        local 调色板  = __dewpal(self.武器染色方案)
                        self.武器显示.静立:调色(调色板,取调色数据(self.武器染色组))
                        self.武器显示.行走:调色(调色板,取调色数据(self.武器染色组))
                    end
             end

           
                -- if self.人物.行走 ~= nil and self.武器显示.行走~=nil then
                --     if not self.锦衣 then
                --         self.武器显示.行走:置差异(self.武器显示.行走:取帧数()-self.人物.行走:取帧数())
                --     end
                -- end
        elseif 取模型("武器_"..self.模型)[1]~=nil then
            资源 = 取模型("武器_"..self.模型)
            if 资源[1] and 资源[2] then
                self.武器显示.静立 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
                self.武器显示.行走 = __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
             end
         else
            if self.模型=="进阶古代瑞兽" or self.模型=="进阶雷鸟人" or self.模型=="进阶蝴蝶仙子" or self.模型=="进阶白熊"
               or self.模型=="进阶黑山老妖" or self.模型=="进阶天兵" or self.模型=="进阶天将" or self.模型=="进阶地狱战神"
               or self.模型=="进阶风伯" or self.模型=="进阶凤凰" or self.模型=="进阶碧水夜叉" or self.模型=="进阶雨师" or  self.饰品 then
                    local zl = 取战斗模型(self.模型.."_饰品")  ---这里显示宠物饰品的
                    if zl[10]~=nil then
                        self.武器显示.静立  = __res:取资源动画(zl[10],zl[6],"置动画"):置循环(true)
                        self.武器显示.行走 = __res:取资源动画(zl[10],zl[8],"置动画"):置循环(true)
                    end
             end
           
          end
    end
    if not self.锦衣  and self.染色方案 and __染色信息[self.模型]~=nil then 
        self.染色方案 = __染色信息[self.模型].id
        self.染色组 = __染色信息[self.模型].方案
        local 调色板  = __dewpal(self.染色方案)
        self.人物.静立:调色(调色板,取调色数据(self.染色组))
        self.人物.行走:调色(调色板,取调色数据(self.染色组))
    end
	
    self:置动作(self.动作)
	self:置方向(self.方向)
end




function 假人动作:置动作(v)
    self.cur_action = {}
    self.动作 = v
    local 加入数据 = ggf.insert(self.cur_action)
    if self.坐骑显示[v] then
        加入数据(self.坐骑显示[v])
    end
    if self.人物[v] then
        加入数据(self.人物[v])
    end
    if self.武器显示[v] then
        加入数据(self.武器显示[v])
    end
end

local 置当前 = function(self, k, ...)
    if self.cur_action then
        self.cur_action[k](self.cur_action, ...)
    end
end
local 置所有 = function(self, k, ...)
    for _, v in pairs(self.坐骑显示) do
        v[k](v, ...)
    end
    for _, v in pairs(self.人物) do
        v[k](v, ...)
    end
    for _, v in pairs(self.武器显示) do
        v[k](v, ...)
    end
 
    return self
end
function 假人动作:置方向(v)
    self.方向 = v
    置所有(self, "置方向", v)
end

function 假人动作:取方向()
    return self.方向
end

function 假人动作:取高亮()
    return self.cur_action and self.cur_action:取高亮()
end

function 假人动作:置高亮(...)
    置所有(self, "置高亮", ...)
    return self
end

function 假人动作:置颜色(...)
    置所有(self, "置颜色", ...)
    return self
end

function 假人动作:帧同步()
    置当前(self, "置首帧")
    置当前(self, "播放")
    return self
end

function 假人动作:播放()
    置当前(self, "播放")
    return self
end

function 假人动作:置首帧()
    置当前(self, "置首帧")
    return self
end

function 假人动作:置尾帧()
    置当前(self, "置尾帧")
    return self
end

function 假人动作:置循环(...)
    置当前(self, "置循环", ...)
    return self
end

function 假人动作:更新(dt)
    if __主显.主角.xy:取距离(self.xy) < 800 then  
        for _, v in ipairs(self.cur_action) do
            v:更新(dt, x, y)
        end
    end
end

function 假人动作:显示(x, y)
      if __主显.主角.xy:取距离(self.xy) < 800 then
          self.影子:显示(x, y)
          for _, v in ipairs(self.cur_action) do
              v:显示(x, y)
          end
      end
end

function 假人动作:检查点(x, y)
    for _, v in ipairs(self.cur_action) do
        if v:检查点(x, y) then
            return true
        end
    end
end

function 假人动作:检查透明(x, y)
    local r = false
    for _, v in ipairs(self.cur_action) do
        r = r or v:检查透明(x, y)
    end
    return r
end



function 假人动作:消息事件(t)
    if not __手机 and  t.鼠标 then
        for _, v in ipairs(t.鼠标) do
            if self:检查透明(v.x, v.y)  then
              if v.button == SDL.BUTTON_LEFT and v.type == SDL.MOUSE_DOWN and __主显 and __主显.主角  then
                    __主显.主角.按下=false
                    __主显.主角.点击移动=nil
              elseif not v.button and  v.type == SDL.MOUSE_MOTION  then
                    self:置高亮(true)
              end
            else
                self:置高亮(false)
            end
        end
    end
end





return 假人动作
