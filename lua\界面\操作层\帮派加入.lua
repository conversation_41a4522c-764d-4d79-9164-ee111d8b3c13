
local 帮派加入 = 窗口层:创建窗口("帮派加入", 0, 0, 600, 470)
function 帮派加入:初始化()
  self:创建纹理精灵(function()
    置窗口背景("加入帮派", 0, 0, 600, 470, true):显示(0, 0)
    蓝白标题背景(310, 210, true):显示(15, 35) 
    蓝白标题背景(250, 210, true):显示(335, 35)
    取白色背景(0, 0, 575, 180, true):显示(15, 255)
    标题字体:置颜色(255,255,255,255)
    标题字体:取图像("帮派"):显示(30, 40)
    标题字体:取图像("编号"):显示(140, 40)
    标题字体:取图像("规模"):显示(250, 40)
    标题字体:取图像("名称"):显示(350, 40)
    标题字体:取图像("职务"):显示(510, 40)
    标题字体:置颜色(0,0,0,255):取图像("帮派资料:"):显示(20, 270)
  end
)
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  

end
function 帮派加入:打开(数据)
  self:置可见(true)
  self.选中=nil
  self.图像=nil
  self.帮派数据 = 数据
  self.帮派成员:置数据()
  self.帮派列表:置数据()
  self.宗旨文本:清空()
  self.帮派成员:置数据()

end


local 加入按钮 = 帮派加入:创建红色按钮("加入帮派", "加入按钮", 280, 440,70, 22)
function 加入按钮:左键弹起(x, y)
  if 帮派加入.选中 and 帮派加入.帮派数据[帮派加入.选中]  then
    请求服务(6103,{帮派序号=帮派加入.选中})
  end
end

function 帮派加入:额外显示()
  if self.选中 and self.帮派数据[self.选中] then
      self.图像 = self:创建纹理精灵(function()
      文本字体:置颜色(__取颜色("紫色"))
      文本字体:取图像("帮主名称: "..self.帮派数据[self.选中].帮派名称):显示(90, 270)
      local 时间显示 = "创建日期: "..os.date("%Y", self.帮派数据[self.选中].创建时间).. "年"..os.date("%m", self.帮派数据[self.选中].创建时间).. "月" .. os.date("%d", self.帮派数据[self.选中].创建时间).."日"
      文本字体:取图像(时间显示):显示(585-文本字体:取宽度(时间显示), 270)
      文本字体:取图像("帮派规模: "..self.帮派数据[self.选中].规模):显示(20, 295)
      local 人数显示 ="帮派人数: "..self.帮派数据[self.选中].帮派人数.当前.."/"..self.帮派数据[self.选中].帮派人数.上限
      文本字体:取图像(人数显示):显示((585-说明字体:取宽度(人数显示))//2, 295)
      文本字体:取图像("帮派费用: "..self.帮派数据[self.选中].帮派费用.."/周"):显示(585-文本字体:取宽度("帮派费用: "..self.帮派数据[self.选中].帮派费用.."/周"), 295)
      文本字体:取图像("公告:"):显示(20, 320)
    end,1
  )
end

end



function 帮派加入:显示(x,y)
  if self.图像 then
    self.图像:显示(x,y)
  end

end





local 帮派列表 = 帮派加入:创建列表("帮派列表",20, 65, 300, 175)
function 帮派列表:初始化()
  self.行高度 = 35
end
function 帮派列表:置数据()
  self:清空()
 for _, v in ipairs(帮派加入.帮派数据) do
      self:添加():创建纹理精灵(function()
          文本字体:置颜色(0,0,0,255)
          文本字体:取图像(v.帮派名称):显示(0, 6)
          文本字体:取图像(v.编号):显示(105+(40-文本字体:取宽度(v.编号))//2, 6)
          文本字体:取图像(v.规模):显示(230+(40-文本字体:取宽度(v.规模))//2, 6)
        end
      )
  end
end

function 帮派列表:左键弹起(x, y,i)
  if 帮派加入.帮派数据[i] then
    帮派加入.选中=i
    帮派加入:额外显示()
    帮派加入.宗旨文本:清空()
    帮派加入.宗旨文本:置文本("       #H"..帮派加入.帮派数据[i].公告)
    帮派加入.帮派成员:置数据(帮派加入.帮派数据[i].管理数据)
  end
end







local 帮派成员 = 帮派加入:创建列表("帮派成员",340, 65, 240, 180)   
function 帮派成员:初始化()
    self.行高度 = 30
end



function 帮派成员:置数据(data)
    self:清空()
    local 管理成员={}
    if data then
        for i , v in pairs(data) do
            管理成员[#管理成员+1]={}
            管理成员[#管理成员].职务=i
            管理成员[#管理成员].是否在线 =v.是否在线
            管理成员[#管理成员].名称=v.名称
        end
    end
    for _, v in ipairs(管理成员) do
      self:添加():创建纹理精灵(function()
          if v.是否在线 then
            文本字体:置颜色(__取颜色("紫色"))
          else
            文本字体:置颜色(0,0,0,255)
          end
          文本字体:取图像(v.名称):显示(0, 6)
          文本字体:取图像(v.职务):显示(200-文本字体:取宽度(v.职务), 6)
        end)
      end
end
local 宗旨文本 = 帮派加入:创建文本("宗旨文本", 20, 322, 565, 110) 
local 关闭 = 帮派加入:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  帮派加入:置可见(false)
end

