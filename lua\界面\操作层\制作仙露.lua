--[[
LastEditTime: 2024-11-05 16:57:50
--]]



local 制作仙露 = 窗口层:创建窗口("制作仙露", 0,0, 475, 255)
function 制作仙露:初始化()
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
end



function 制作仙露:打开()
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  
    self:刷新()
end


function 制作仙露:刷新()
      self.道具网格:置数据(_tp.道具列表)
      local 丸子数量 =0
      local 神兽数量 = 0
      local 编号={}
      for k, v in pairs(_tp.道具列表) do
         if v.名称=="仙露小丸子" then
            丸子数量=丸子数量+v.数量
         elseif v.名称=="神兽涎" then
            神兽数量=神兽数量+v.数量
         else
            table.insert(编号,k)
         end
      end
      self.道具网格:置禁止(true,nil,编号)
      self:创建纹理精灵(function()
              __res:取资源动画('pic',"xianyuhec.png","图片"):显示(0,0)
              local 丸子=取物品("仙露小丸子")
              __res:取资源动画(丸子[11],丸子[12],"图像"):显示(43, 75)
              丸子=取物品("神兽涎")
              __res:取资源动画(丸子[11],丸子[12],"图像"):显示(133, 75)
              文本字体:置颜色(0,0,0,255)
              文本字体:取图像("100"):显示(90,168)
              文本字体:取图像(角色信息.体力):显示(90,195)
              文本字体:取图像(丸子数量.."/10"):显示(40+(50-文本字体:取宽度(丸子数量.."/10"))//2,110)
              文本字体:取图像(神兽数量.."/1"):显示(130+(50-文本字体:取宽度(神兽数量.."/1"))//2,110)

        end)
      
end


local 道具网格=制作仙露:创建道具网格("道具网格",202,34)

function 道具网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
              __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
          end
end

local 确认制作=制作仙露:创建红色按钮("确认制作","确认制作",67,218,80,22)
function 确认制作:左键弹起(x, y)
       请求服务(3747)
end

local 关闭 = 制作仙露:创建关闭按钮("关闭",450,2)
function 关闭:左键弹起(x, y)
  制作仙露:置可见(false)
end

