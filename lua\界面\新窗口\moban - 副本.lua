local 成长礼包 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "成长礼包", 80+67 + abbr.py.x, 20+42 + abbr.py.y, 555+29, 430)

function 成长礼包:初始化()
  local nsf = require("SDL.图像")(555+29, 430)
  if nsf["渲染开始"](nsf) then
    -- __res:getPNGCC(5, 0, 0, 683, 450):显示(0,5)
    -- 字体20:置颜色(__取颜色("浅黑"))
    -- 字体20:取图像("伙 伴"):显示(683/2-20,9)
    置窗口背景("成长礼包", 0, 12, 540+29,415, true):显示(0, 0)
    local xian=__res:getPNGCC(4, 550, 334, 140, 3):拉伸(471+23, 3)
    xian:显示(20, 122)
    xian:显示(20, 122+73)
    xian:显示(20, 122+73*2)
    xian:显示(20, 122+73*3)
    字体18:置颜色(__取颜色("白色"))
    字体18:取图像("20级"):显示(20,79)
    字体18:取图像("30级"):显示(20,79+73)
    字体18:取图像("40级"):显示(20,79+73*2)
    字体18:取图像("50级"):显示(20,79+73*3)
    字体18:取图像("60级"):显示(20,79+73*4)
    --取白色背景(0, 0, 440, 300, true)["显示"](取白色背景(0, 0, 440, 300, true), 20, 56)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 成长礼包:打开(内容)
  self:置可见(true)
end


local 关闭 = 成长礼包["创建我的按钮"](成长礼包, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 500+29, 0)
function 关闭:左键弹起(x, y, msg)
  成长礼包["置可见"](成长礼包, false)
end