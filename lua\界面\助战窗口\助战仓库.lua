-- <AUTHOR> GGELUA
-- @Last Modified by    : baidwwy
-- @Date                : 2024-09-04 16:18:55
-- @Last Modified time  : 2024-10-02 16:42:50

local 助战仓库 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "助战仓库", 97 + abbr.py.x, 21 + abbr.py.y, 777, 488)
function 助战仓库:初始化()
  self:置精灵(置窗口背景("助战道具", 0, 12, 723, 476):置透明(235))
  self.格子图=__res:getPNGCC(3, 694, 4, 338, 273)
  self.数量框=__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 80, 35)
  -- __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 12, 132)
  --   __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 372, 132)
  --   __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 80, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 80, 35), 69, 429)
end
function 助战仓库:打开(道具,道具仓库总数)
  self:置可见(true)
  self.道具仓库总数 = 道具仓库总数

  self["仓库页数"]=1
  self.道具类型 = "道具"
  self.仓库类型 = "道具"
  self.物品控件["重置"](self.物品控件, 1)
  self.物品控件["仓库网格"]["置物品"](self.物品控件["仓库网格"], 道具, 1)
  self.物品控件["道具网格"]["置物品"](self.物品控件["道具网格"], __主控["道具列表"])
  助战仓库["物品控件"].道具按钮:置选中(true)
end
function 助战仓库:刷新道具仓库总数(道具,总数)
  self.道具仓库总数 = 总数
  助战仓库["物品控件"]["重置"](助战仓库["物品控件"], 助战仓库["仓库页数"])
  助战仓库["物品控件"]["仓库网格"]["置物品"](助战仓库["物品控件"]["仓库网格"], 助战仓库["数据"], 助战仓库["仓库页数"])
  助战仓库["物品控件"]["道具网格"]["置物品"](助战仓库["物品控件"]["道具网格"], __主控["道具列表"])
  助战仓库["物品控件"].道具按钮:置选中(true)
end

function 助战仓库:刷新仓库(数据,页数)
	self.仓库页数 = 页数 or 1
  self.物品控件["重置"](self.物品控件, self.仓库页数)
  self.物品控件["仓库网格"]["置物品"](self.物品控件["仓库网格"], 数据, self.仓库页数)
 end

function 助战仓库:刷新道具(类型,数据)
  self.道具类型 = 类型
  助战仓库["道具选中"] = nil
  助战仓库["仓库选中"] = nil
  -- table.print(数据)
  助战仓库["物品控件"]["道具网格"]["置物品"](助战仓库["物品控件"]["道具网格"], 数据.道具)
end

local 物品控件 = 助战仓库["创建控件"](助战仓库, "物品控件", 0, 0, 777, 488)

local 道具按钮 = 物品控件["创建我的单选按钮"](物品控件, __res:getPNGCC(3, 511, 11, 117, 43, true), __res:getPNGCC(3, 390, 12, 118, 43, true), "道具按钮", 371, 64, "道具")
function 道具按钮:左键按下(消息, x, y)
  发送数据(3780,{道具类型="道具"}) --
end
local 行囊按钮 = 物品控件["创建我的单选按钮"](物品控件, __res:getPNGCC(3, 511, 11, 117, 43, true), __res:getPNGCC(3, 390, 12, 118, 43, true), "行囊按钮", 371+140, 64, "行囊")
function 行囊按钮:左键按下(消息, x, y)
  发送数据(3780,{道具类型="行囊"}) --
end
local 整理按钮 = 物品控件["创建我的按钮"](物品控件, __res:getPNGCC(3, 511, 11, 117, 43, true), "整理按钮", 225, 426, "整理")
function 整理按钮:左键按下(消息, x, y)
  发送数据(2104) --
end
function 物品控件:重置(total)
  助战仓库["仓库选中"] = nil
  self:置可见(true)
  local nsf = require("SDL.图像")(777, 488)
  if nsf["渲染开始"](nsf) then
    助战仓库.格子图:显示(12,132)
    助战仓库.格子图:显示(372, 132)
    助战仓库.数量框:显示(69, 429)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, 助战仓库["仓库页数"] .. "/" .. 助战仓库["道具仓库总数"]):显示(93,433+4)
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18["取图像"](字体18, "助战道具" .. total)["置混合"](字体18["取图像"](字体18, "助战道具" .. total), 0)["显示"](字体18["取图像"](字体18, "助战道具" .. total)["置混合"](字体18["取图像"](字体18, "助战道具" .. total), 0), 15, 75)
    字体18["取图像"](字体18, "双击左侧助战道具可快速取出")["置混合"](字体18["取图像"](字体18, "双击左侧助战道具可快速取出"), 0)["显示"](字体18["取图像"](字体18, "双击左侧助战道具可快速取出")["置混合"](字体18["取图像"](字体18, "双击左侧助战道具可快速取出"), 0), 505-52, 433+4)
    nsf["渲染结束"](nsf)
  end
  助战仓库["图像"] = nsf["到精灵"](nsf)
end

local 仓库网格 = 物品控件["创建网格"](物品控件, "仓库网格", 12, 132, 339, 272)
function 仓库网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 仓库网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    if 助战仓库["仓库选中"] and self.子控件[助战仓库["仓库选中"]] and self.子控件[助战仓库["仓库选中"]]._spr then
      self.子控件[助战仓库["仓库选中"]]._spr["确定"] = nil
    end
    
    if 助战仓库["仓库选中"] and 助战仓库["仓库选中"] == a then
        发送数据(2103,{页数=助战仓库.仓库页数,物品=助战仓库["仓库选中"],类型=助战仓库.道具类型})
        助战仓库["仓库选中"] = nil
    else
      助战仓库["仓库选中"] = a
      self.子控件[a]._spr["确定"] = true
      self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 470, 86, w, h, "", a)
    end

  end
end
function 仓库网格:置物品(数据, 页数)
  助战仓库["数据"] = 数据
  助战仓库["仓库页数"] = 页数
  助战仓库.物品控件:重置(页数)
  助战仓库["仓库选中"] = nil
  for i = 1, #self.子控件 do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "道具仓库")
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
local 道具网格 = 物品控件["创建网格"](物品控件, "道具网格", 373, 132, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    if 助战仓库["道具选中"] and self.子控件[助战仓库["道具选中"]] and self.子控件[助战仓库["道具选中"]]._spr then
      self.子控件[助战仓库["道具选中"]]._spr["确定"] = nil
    end
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 120, 86, w, h, "", a)
    助战仓库["道具选中"] = a
    self.子控件[a]._spr["确定"] = true
    -- end
  end
end
function 道具网格:置物品(数据)
  助战仓库["道具选中"] = nil
  for i = 1, #self.子控件 do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "道具仓库")
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
for i, v in ipairs({
  {
    name = "增加",
    x = 82+30,
    y = 63+9,
    tcp = __res:getPNGCC(1, 641, 320, 29, 29),
  },
  {
    name = "左翻",
    x = 16,
    y = 424,
    tcp = __res:getPNGCC(4, 969, 4, 46, 46)
  },
  {
    name = "右翻",
    x = 155,
    y = 424,
    tcp = __res:getPNGCC(4, 1016, 4, 46, 46)
  }
}) do
  local 临时函数 = 物品控件["创建我的按钮"](物品控件, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "左翻" and 助战仓库["仓库页数"] > 1 then
      助战仓库["仓库页数"]=助战仓库["仓库页数"]-1
      发送数据(2102,{序列=助战仓库["仓库页数"]})--
    elseif v.name == "右翻" and 助战仓库["仓库页数"] < 助战仓库["道具仓库总数"] then
      助战仓库["仓库页数"]=助战仓库["仓库页数"]+1
      发送数据(2102,{序列=助战仓库["仓库页数"]})--
    elseif v.name == "增加" then
      -- __UI界面["窗口层"]["对话栏"]:打开("","","本次租借需要消耗#R"..((助战仓库.道具仓库总数-3)*100000+300000).."#W两银子，你是否需要进行购买仓库操作？",{"确定购买仓库","让我再想想"})
      __UI界面["窗口层"]["对话栏"]:打开("","","本次扩展助战仓库需要消耗#R"..((助战仓库.道具仓库总数-3)*50000+300000).."#W两银子，你是否需要进行购买助战仓库操作？",{"确定购买背包","让我再想想"})
    end
  end
end












local 关闭 = 助战仓库["创建我的按钮"](助战仓库, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 690, 0)
function 关闭:左键弹起(x, y, msg)
  助战仓库["置可见"](助战仓库, false)
end