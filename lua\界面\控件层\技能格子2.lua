local 技能格子2 = class("技能格子2")
function 技能格子2:初始化()
  self.py = {x = 0, y = 0}
end
function 技能格子2:置数据(数据, x, y, w, h, w2, h2, mc, dj,上限)
  self.模型 = nil
  self.数据 = nil
  local nsf = require("SDL.图像")(w2, h2)
  if 数据 then
    if nsf["渲染开始"](nsf) then
      self.数据 = 取技能(数据["名称"], self.门派)
      self.数据["名称"] = 数据["名称"]
      local wenj="shape/jn/"
            if self.数据[10] then
                wenj="shape/xinzengsucai/"
            end
      __res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), w2, h2)["显示"](__res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), w2, h2), 0, 0)
      __res["取图像"](__res, __res["取地址"](__res, wenj, self.数据[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, self.数据[7])), w, h)["显示"](__res["取图像"](__res, __res["取地址"](__res, wenj, self.数据[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, self.数据[7])), w, h), x, y)
      if mc then
        字体20["置颜色"](字体20, 39, 53, 81)
        字体20["取图像"](字体20, 数据["名称"])["显示"](字体20["取图像"](字体20, 数据["名称"]), x + w + 10, 10)
        if dj then
          if 上限 then
            字体20:取图像(数据["等级"].."/"..上限):显示(x + w + 10, 30)
          else
            字体20["取图像"](字体20, 数据["等级"])["显示"](字体20["取图像"](字体20, 数据["等级"]), x + w + 10, 30)
          end
        end
      end
      nsf["渲染结束"](nsf)
    end
    self.模型 = nsf["到精灵"](nsf)
  end
end
function 技能格子2:详情打开(x, y, w, h)
  __UI弹出["技能详情"]["置可见"](__UI弹出["技能详情"], true, true)
  __UI弹出["技能详情"]["技能文本"]["清空"](__UI弹出["技能详情"]["技能文本"])
  __UI弹出["技能详情"]["打开"](__UI弹出["技能详情"], self.数据, x - 300, y - 150, 300, 300, self.模型)
end
function 技能格子2:更新(dt)
end
function 技能格子2:显示(x, y)
  if self.模型 then
    self.模型["显示"](self.模型, x + self.py.x, y + self.py.y)
  end
  if self.确定 then
    __主控["技能选中大"]["显示"](__主控["技能选中大"], x, y)
  end
end
return 技能格子2
