
local 多开仓库 = 窗口层:创建窗口("多开仓库", 0,0, 610, 450)
function 多开仓库:初始化()  
    self:创建纹理精灵(function()  
        置窗口背景("多开仓库", 0, 0, 610, 330,true):显示(0,0)
        local 画线= __res:取资源动画("dlzy",0x2BA5AE64,"图像"):置区域(0,0,70,2)
        local 条纹=__res:取资源动画("jszy/fwtb",0xabcd0204,"图像")
        画线:显示(18,58)
        画线:显示(195,58)
        画线:显示(345,58)
        画线:显示(520,58)
        条纹:置区域(0,0,16,50)
        条纹:显示(290,80)
        条纹:显示(310,80)
        条纹:显示(290,235)
        条纹:显示(310,235)
        文本字体:置颜色(255, 255, 255):取图像("数量"):显示(294,135)
        取输入背景(0, 0, 35, 22):显示(290,155) 
      end
  )

    self.起点=0
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
    if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
    else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
    end
  
end 




function 多开仓库:打开(道具,道具总数,召唤兽总数,玩家id)  
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.起点 = 0
  self.类型="道具"
  self.分类="道具"
  self.玩家id = 玩家id
  self.总页 = 道具总数
  self.宝宝总页 = 召唤兽总数
  self.仓库网格:置数据(道具)
  self.道具网格:置数据(_tp.多角色[self.玩家id].道具列表)
  self.宝宝列表={}
  self.页数 = 1
  self.召唤兽仓库:置数据()
  self.召唤兽列表:置数据()
  self:刷新显示() 
  self:道具刷新()
end



function 多开仓库:刷新仓库(数据,页数)
      if self.类型 == "道具" then
          self.仓库网格:置物品(数据)
      elseif self.类型 == "召唤兽" then
            self.宝宝列表=数据
            self.召唤兽仓库:置数据()
            self.召唤兽列表:置数据()
      end
      if 页数 and self.页数~=页数 then
          self.页数 = 页数
      end
      
      self:刷新显示()
end

function 多开仓库:道具刷新()
      local 物品={}
      for n = 1, 20 do
        if self.分类=="道具" then
            物品[n]=_tp.多角色[self.玩家id].道具列表[n+self.起点]
        else
            物品[n]=_tp.多角色[self.玩家id].行囊列表[n]
        end
      end
      self.道具网格:置物品(物品)
      self.按钮控件:刷新显示(self.类型,self.分类)
end

function 多开仓库:刷新显示()
  self[self.类型]:置选中(true)
  self.图像 =self:创建纹理精灵(function()
          if self.类型=="道具"  then
              self.整理按钮:置可见(true)
              self.道具网格:置可见(true)
              self.仓库网格:置可见(true)
              self.召唤兽仓库:置可见(false)
              self.召唤兽列表:置可见(false)
             
              文本字体:置颜色(255, 255, 255)
              文本字体:取图像("道具仓库"):显示(443,55)
              文本字体:取图像("仓库"..self.页数):显示(120,55)
              文本字体:置颜色(__取颜色("黄色")):取图像("鼠标右击道具快速转移"):显示(463,303)
              文本字体:置颜色(0, 0, 0):取图像(self.页数.."/"..self.总页):显示(292+(35-说明字体:取宽度(self.页数.."/"..self.总页))//2, 158)
          else   
              self.整理按钮:置可见(false)
              self.道具网格:置可见(false)
              self.仓库网格:置可见(false)
              self.召唤兽仓库:置可见(true)
              self.召唤兽列表:置可见(true)
              取白色背景(0, 0, 260, 210, true):显示(20, 80)
              取白色背景(0, 0, 260, 210, true):显示(340, 80)
              文本字体:置颜色(255, 255, 255)
              文本字体:取图像("已携带召唤兽"):显示(423,58)
              文本字体:取图像("召唤兽仓库"..self.页数):显示(100,55)
              文本字体:置颜色(__取颜色("黄色")):取图像("左击存入取出,右击查看信息"):显示(340,303)
              文本字体:置颜色(0, 0, 0):取图像(self.页数.."/"..self.宝宝总页):显示(292+(35-说明字体:取宽度(self.页数.."/"..self.宝宝总页))//2, 158)
          end
         
    end,1)
    self.按钮控件:刷新显示(self.类型,self.分类)
   
 end



function 多开仓库:显示(x, y)  
    if self.图像 then
      self.图像:显示(x, y)
    end
end



local 类型设置={"道具","召唤兽"}
for i, v in ipairs(类型设置) do         
    local 临时按钮 = 多开仓库:创建红色单选按钮(v.."仓库", v, 140+(i-1)*200, 29, 130,22)
    function 临时按钮:左键按下(x, y)
      多开仓库.页数 = 1
      if v=="道具" then
        多开仓库.类型 = "道具"
        多开仓库.分类 = "道具"
          请求服务(63,{参数=多开仓库.玩家id,文本="道具仓库",序列=1})
      else
          请求服务(63,{参数=多开仓库.玩家id,文本="获取宝宝仓库1"})
          多开仓库.类型=v
      end
      多开仓库:刷新显示()
        
    end
end





local 购买按钮=多开仓库:创建蓝色按钮("购买仓库","购买按钮", 195, 300,70,22)  
function 购买按钮:左键弹起(x, y)
      if 多开仓库.类型=="道具" and  多开仓库.总页<40 then
          窗口层.对话栏:打开(_tp.多角色[多开仓库.玩家id].模型,_tp.多角色[多开仓库.玩家id].名称,"增加物品仓库数量需要支付20点仙玉，每增加一间仓库将额外消耗（已增加仓库数量*20）点仙玉。本次增加仓库需要消耗#R"..((多开仓库.总页-3)*20+20).."#W点仙玉，你是否需要进行购买仓库操作？",{"确定购买该角色道具仓库","让我再想想"})
      elseif 多开仓库.宝宝总页<10 then
          窗口层.对话栏:打开(_tp.多角色[多开仓库.玩家id].模型,_tp.多角色[多开仓库.玩家id].名称,"增加召唤兽仓库数量需要支付20点仙玉，每增加一间召唤兽仓库将额外消耗（已增加仓库数量*20）点仙玉。本次增加仓库需要消耗#R"..((多开仓库.宝宝总页-1)*20+20).."#W点仙玉，你是否需要进行购买仓库操作？,你是否需要进行购买仓库操作？",{"确定购买该角色召唤兽仓库","让我再想想"})
      end
end
local 整理按钮=多开仓库:创建蓝色按钮("整理仓库","整理按钮", 270, 300,70,22) 

 function 整理按钮:左键弹起(x, y)
    if 多开仓库.类型=="道具" then
        请求服务(63,{参数=多开仓库.玩家id,文本="整理仓库",道具类型=多开仓库.分类,页数=多开仓库.页数})
    end
 end

 



local 左翻按钮=多开仓库:创建红色按钮("<-","左翻按钮", 294, 185,30,22)   		


function 左翻按钮:左键弹起(x, y)
      if 多开仓库.页数>1 then
        if 多开仓库.类型=="道具" then
              请求服务(63,{参数=多开仓库.玩家id,文本="道具仓库",序列=多开仓库.页数-1})
          else
              请求服务(63,{参数=多开仓库.玩家id,文本="获取宝宝仓库",序列=多开仓库.页数-1})
              
        end
      end
 
end
local 右翻按钮=多开仓库:创建红色按钮("->","右翻按钮", 294, 210,30,22)  

function 右翻按钮:左键弹起(x, y)
      if 多开仓库.类型=="道具" and 多开仓库.页数<多开仓库.总页 then
        请求服务(63,{参数=多开仓库.玩家id,文本="道具仓库",序列=多开仓库.页数+1})
      elseif  多开仓库.类型=="召唤兽" and 多开仓库.页数<多开仓库.宝宝总页 then
        请求服务(63,{参数=多开仓库.玩家id,文本="道具仓库",序列=多开仓库.页数+1})
      end
end





local 道具网格 = 多开仓库:创建道具网格("道具网格", 340, 80)

function 道具网格:获得鼠标(x, y,a)
      local 物品 = self:焦点物品()
      if 物品 and 物品.物品  then
          __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
      end
end




function 道具网格:右键弹起(x, y, a)
      local 物品 = self:焦点物品()
      if 物品 and 物品.物品 and self:焦点()~=0 then
        self:存入(a)
      end

end


function 道具网格:左键弹起(x, y, a)
        local 物品 = self:选中物品()
        if __手机  and 物品 and 物品.物品 and self:选中()~=0 then
            __UI弹出.道具提示:打开(物品.物品,x+20,y+20,道具网格,"存入",a) 
        end
end


function 道具网格:存入(编号)
      if 编号 and 编号~=0 then
          请求服务(63,{参数=多开仓库.玩家id,文本="存入仓库",页数=多开仓库.页数,物品=编号+多开仓库.起点,类型=多开仓库.分类})
      end
end







local 仓库网格 = 多开仓库:创建道具网格("仓库网格", 20, 80)

function 仓库网格:获得鼠标(x, y,a)
      local 物品 = self:焦点物品()
      if 物品 and 物品.物品  then
          __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
      end
end




function 仓库网格:右键弹起(x, y, a)
      local 物品 = self:焦点物品()
      if 物品 and 物品.物品 and self:焦点()~=0 then
            self:取出(a)
      end

end




function 仓库网格:左键弹起(x, y, a)
      local 物品 = self:选中物品()
      if  __手机  and 物品 and 物品.物品 and self:选中()~=0 then
            __UI弹出.道具提示:打开(物品.物品,x+20,y+20,仓库网格,"取出",a)
      end
end

function 仓库网格:取出(编号)
    if 编号 and 编号~=0 then
          请求服务(63,{参数=多开仓库.玩家id,文本="取出物品",页数=多开仓库.页数,物品=编号,类型=多开仓库.分类}) 
    end
end


local 召唤兽列表 = 多开仓库:创建列表("召唤兽列表", 340, 80, 260, 210)
  function 召唤兽列表:初始化()
        self.行高度= 37
        self.行间距 = 3
        self.数据={}
  end
  function 召唤兽列表:置数据()
    self:清空()
    self.数据= table.copy(_tp.多角色[多开仓库.玩家id].宝宝列表)
    for i, v in ipairs(self.数据) do
        self:添加():创建纹理精灵(function()
          __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
          local lssj = 取头像(v.模型)
          __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
          文本字体:置颜色(0,0,0,255)
          文本字体:取图像(v.名称):显示(40,4)
          文本字体:取图像(v.等级.."级"):显示(40,20)
        end)
    end
  end

  function 召唤兽列表:右键弹起(x, y, i)
      if self.数据[i]  then
          窗口层.召唤兽查看:打开(self.数据[i])
      end
  end
  
  function 召唤兽列表:左键弹起(x, y, i)
        if self.数据 and self.数据[i]  then
            if  __手机 then
                local 事件 =function (编号)
                    if 编号==1 then
                          窗口层.召唤兽查看:打开(self.数据[i])
                    elseif 编号==2 then
                        请求服务(63,{参数=多开仓库.玩家id,文本="存入宝宝仓库",认证码=self.数据[i].认证码,页数=多开仓库.页数})
                    end
                end
                __UI弹出.临时按钮:打开({"查看","存入"},事件,x,y)

            else
                  请求服务(63,{参数=多开仓库.玩家id,文本="存入宝宝仓库",认证码=self.数据[i].认证码,页数=多开仓库.页数})
            end
        end
  end

  local 召唤兽仓库 = 多开仓库:创建列表("召唤兽仓库", 20, 80, 260, 210)
  function 召唤兽仓库:初始化()
      self.行高度= 37
        self.行间距 = 3
  end
  function 召唤兽仓库:置数据()
      self:清空()
      for i, v in ipairs(多开仓库.宝宝列表) do
        self:添加():创建纹理精灵(function()
          __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
          local lssj = 取头像(v.模型)
          __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
          文本字体:置颜色(0,0,0,255)
          文本字体:取图像(v.名称):显示(40,4)
          文本字体:取图像(v.等级.."级"):显示(40,20)
        end)
      end
  end

  function 召唤兽仓库:右键弹起(x, y, i)
        if 多开仓库.宝宝列表[i]  then
            窗口层.召唤兽查看:打开(多开仓库.宝宝列表[i])
        end
  end
  
  function 召唤兽仓库:左键弹起(x, y, i)
        if  多开仓库.宝宝列表[i]  then
           if __手机 then
              local 事件 =function (编号)
                  if 编号==1 then
                        窗口层.召唤兽查看:打开(多开仓库.宝宝列表[i])
                  elseif 编号==2 then
                        请求服务(63,{参数=多开仓库.玩家id,文本="取出宝宝仓库",认证码=多开仓库.宝宝列表[i].认证码,页数=多开仓库.页数})
                  end
              end
              __UI弹出.临时按钮:打开({"查看","取出"},事件,x,y)

           else
                请求服务(63,{参数=多开仓库.玩家id,文本="取出宝宝仓库",认证码=多开仓库.宝宝列表[i].认证码,页数=多开仓库.页数})
           end
        end
  end







  local 按钮控件=多开仓库:创建控件("按钮控件", 0, 290,610, 160) 

  function 按钮控件:刷新显示(类型,分类)
            self.仓库精灵=nil
            local 总数=0
            self.道具控件:置可见(false)
            if 类型=="道具" then
                self[分类]:置选中(true)
                self.道具:置可见(true)
                self.行囊:置可见(true)
                总数=多开仓库.总页
                if 总数>=40 then
                    总数=40
                end
                if 分类=="道具" then
                    self.道具控件:置可见(true)
                    for i=1,5 do
                        if 多开仓库.起点==(i-1)*20 then
                            self.道具控件["按钮"..i]:置选中(true)
                        end
                    end
                end
            else
                  self.道具:置可见(false)
                  self.行囊:置可见(false)
                  总数=多开仓库.宝宝总页
                  if 总数>=10 then
                      总数=10
                  end
            end
            self.数量控件:刷新显示(总数)
  end
  
  
  local 道具=按钮控件:创建红色单选按钮("道 具","道具", 345, 10,50,22) 
  function 道具:左键弹起(x, y)
       if 多开仓库.分类~="道具" then
           多开仓库.分类="道具"
             请求服务(63,{参数=多开仓库.玩家id,文本="仓库道具",道具类型=多开仓库.分类})
       end
  end
  local 行囊=按钮控件:创建红色单选按钮("行 囊","行囊", 405, 10,50,22) 
  function 行囊:左键弹起(x, y)
          if 多开仓库.分类~="行囊" then
            多开仓库.分类="行囊"
              请求服务(63,{参数=多开仓库.玩家id,文本="仓库道具",道具类型=多开仓库.分类})
          end
  end
  
  local 数量控件=按钮控件:创建控件("数量控件",0,0,300,160) 
  function 数量控件:初始化() 
        self.仓库精灵=require('SDL.精灵')(0, 0, 0,280, 160):置颜色(0, 0, 0, 170)
  end
  
  function 数量控件:显示(x,y)
         if self.总数 and self.总数>7 then
            self.仓库精灵:显示(x,y+40) 
         end  
  end
  
  
  function 数量控件:刷新显示(总数)
            self.总数=总数 or 0
            for i=1,40 do
                if i<=self.总数 then  
                    self["按钮"..i]:置可见(true)
                else
                    self["按钮"..i]:置可见(false)
                end
            end
            self["按钮"..多开仓库.页数]:置选中(true)
  end
  
  for i=1 ,7 do
      local 临时按钮=数量控件:创建蓝色单选按钮(i,"按钮"..i,5+(i-1)*27,8,26,26)
      function 临时按钮:左键按下(x, y)
              if 多开仓库.类型=="道具" then
                  请求服务(63,{参数=多开仓库.玩家id,文本="道具仓库",序列=i})
              else
                  请求服务(63,{参数=多开仓库.玩家id,文本="获取宝宝仓库",序列=i})
              end
          
      end
  end
  
  
  local xx = 0
  local yy = 0
  for i=8 ,40 do
    local 临时按钮=数量控件:创建蓝色单选按钮(i,"按钮"..i,5+xx*27,45+yy*28,26,26)
    xx=xx+1
    if xx>=10 then
      xx=0
      yy=yy+1
    end
    function 临时按钮:左键按下(x, y)
          if 多开仓库.类型=="道具" then
              请求服务(63,{参数=多开仓库.玩家id,文本="道具仓库",序列=i})
          else
              请求服务(63,{参数=多开仓库.玩家id,文本="获取宝宝仓库",序列=i})
          end
    end
  
  end
  
  
  local 道具控件=按钮控件:创建控件("道具控件",340,43,275,30) 
  function 道具控件:初始化() 
        self:置精灵(require('SDL.精灵')(0, 0, 0,275, 30):置颜色(0, 0, 0, 170),true)
  end
  for i=1 ,5 do
      local 临时按钮=道具控件:创建蓝色单选按钮(i,"按钮"..i,5+(i-1)*30,3,26,26)
      function 临时按钮:左键按下(x, y)
          if 多开仓库.起点~=(i-1)*20 then
            多开仓库.起点=(i-1)*20
            多开仓库:道具刷新()
          end
      end
  end
  



local 关闭 = 多开仓库:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  多开仓库:置可见(false)
end









