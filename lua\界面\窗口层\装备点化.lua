local 装备点化 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "装备点化", 536 + abbr.py.x, 5 + abbr.py.y, 417, 519)
function 装备点化:初始化()
  local nsf = require("SDL.图像")(417, 519)
  if nsf["渲染开始"](nsf) then
    置窗口背景("点化星位", 0, 12, 409, 505, true)["显示"](置窗口背景("点化星位", 0, 12, 409, 505, true), 0, 0)
    取灰色背景(0, 0, 365, 275, true)["显示"](取灰色背景(0, 0, 365, 275, true), 24, 118)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "装\n备")["显示"](字体18["取图像"](字体18, "装\n备"), 150, 56)
    local lssj = 取输入背景(0, 0, 92, 23)
    字体18["取图像"](字体18, "所需现金")["显示"](字体18["取图像"](字体18, "所需现金"), 21, 406)
    字体18["取图像"](字体18, "现有现金")["显示"](字体18["取图像"](字体18, "现有现金"), 214, 406)
    字体18["取图像"](字体18, "所需体力")["显示"](字体18["取图像"](字体18, "所需体力"), 21, 438)
    字体18["取图像"](字体18, "现有体力")["显示"](字体18["取图像"](字体18, "现有体力"), 214, 438)
    lssj["显示"](lssj, 105, 404)
    lssj["显示"](lssj, 298, 404)
    lssj["显示"](lssj, 105, 436)
    lssj["显示"](lssj, 298, 436)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 装备点化:打开(data)
  self:置可见(true)
  self.数据 = data
  self.重置(self)
end
function 装备点化:重置()
  local nsf = require("SDL.图像")(409, 69)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取银子颜色(角色信息["银子"]))
    字体18["取图像"](字体18, 角色信息["银子"])["显示"](字体18["取图像"](字体18, 角色信息["银子"]), 305, 12)
    nsf["渲染结束"](nsf)
  end
  装备点化["图像"] = nsf["到精灵"](nsf)
  装备点化["图像"]["置中心"](装备点化["图像"], 0, -398)
  self.道具网格["置物品"](self.道具网格, __主控["道具列表"])
  self.材料网格["置物品"](self.材料网格, {})
end
local 关闭 = 装备点化["创建我的按钮"](装备点化, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 367, 0)
function 关闭:左键弹起(x, y, msg)
  装备点化["置可见"](装备点化, false)
end
local 道具网格 = 装备点化["创建网格"](装备点化, "道具网格", 43, 128, 318, 252)
function 道具网格:初始化()
  self:创建格子(55, 55, 10, 10, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    for i = 1, #装备点化["材料网格"]["子控件"] do
      if not 装备点化["材料网格"]["子控件"][i]._spr["物品"] then
        self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
        装备点化["材料网格"]["置物品"](装备点化["材料网格"], self.子控件[a]._spr["物品"], i)
        装备点化["材料网格"]["子控件"][i]._spr["物品"]["原始编号"] = a
        self:置物品(nil, a)
        break
      end
    end
  end
end
function 道具网格:置物品(data, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], "白格子", "战斗道具")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data, "白格子", "战斗道具")
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end
local 材料网格 = 装备点化["创建网格"](装备点化, "材料网格", 180, 55, 155, 55)
function 材料网格:初始化()
  self:创建格子(55, 55, 0, 45, 1, 1)
end
function 材料网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
    装备点化["道具网格"]["置物品"](装备点化["道具网格"], self.子控件[a]._spr["物品"], self.子控件[a]._spr["物品"]["原始编号"])
    self:置物品(nil, a)
  end
end
function 材料网格:置物品(数据, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __商店格子["创建"]()
      lssj["置物品"](lssj, 数据[i], "制造")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, 数据, "制造")
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end
for i, v in ipairs({
  {
    name = "开运",
    x = 146,
    y = 466,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 118, 41),
    font = "开运"
  }
}) do
  local 临时函数 = 装备点化["创建我的按钮"](装备点化, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "开运" then
      if 装备点化["材料网格"]["子控件"][1]._spr["物品"] then
        发送数据(3743, {
          ["格子"] = 装备点化["材料网格"]["子控件"][1]._spr["物品"]["原始编号"]
        })
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请放入材料")
      end
    end
  end
end
