local 藏宝阁出售寄存 = 窗口层:创建窗口("藏宝阁出售寄存", 0, 0, 300, 320)
function 藏宝阁出售寄存:初始化()
  self:创建纹理精灵(function()
                      置窗口背景("出售寄存", 0, 0, 300, 320,true):显示(0,0)
                      __res:取资源动画("dlzy",0xADC83326,"图像"):平铺(18,240):显示(2,40)
                      __res:取资源动画("dlzy",0xADC83326,"图像"):平铺(18,240):显示(280,40)
                      标题字体:置颜色(255,255,255,255):取图像("价格:"):显示(10,290)
                      取输入背景(0, 0, 130, 23):显示(50,287)
                    end
              )
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
    self.物品={}

    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end

end

function 藏宝阁出售寄存:打开(数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  if not 数据  then return end
   self.类型=数据.类型
   self.物品网格:置数据()
   self.宝宝控件.名称选择:置数据({})
   self.上架按钮:重置文字("上架")
   self:刷新(数据)
end

function 藏宝阁出售寄存:刷新(数据)
    self.物品网格:置可见(false)
    self.宝宝控件:置可见(false)
    self.图像 =nil
    self.价格输入:清空()
    self.价格输入:置颜色(0, 0, 0, 255)
    self.价格输入:置模式(self.价格输入.数字模式)
    self.商城类型=""
    self.识别码=nil
    if self.类型 ==1 or self.类型 ==3 then
          if 数据.开始 ==1 then
             self.物品={}
             self.物品网格:置数据()
             self.额外数据 = {}
          end
          for i=数据.开始,数据.结束 do
             self.物品[i]=数据.数据[i]
             self.额外数据[i] = {}
             if 数据.额外数据[i]~=nil then
                self.额外数据[i] = 数据.额外数据[i]
                self.额外数据[i].识别码 =数据.数据[i].识别码
                if 数据.额外数据[i].上架 then
                    self.物品[i].附加显示="上架中"
                end
            end
          end
          self.类型 =1
          self.物品网格:置可见(true)
          self.物品网格:置物品(self.物品)
      else
          self.物品={}
          self.额外数据 = {}
          self.类型 =2
          self.图像 = 取白色背景(20,40,258,240)
          if 数据.额外数据~=nil then
              for n=1,#数据.数据 do
                  self.额外数据[n] = {}
                  if 数据.额外数据[n]~=nil then
                      self.额外数据[n] = 数据.额外数据[n]
                  end
              end
          end
          self.宝宝控件:置可见(true)
          self.宝宝控件.名称选择:置数据(数据.数据)
         
      end
      
    
      
end
function 藏宝阁出售寄存:显示(x,y)
  if self.图像 then
      self.图像:显示(x, y)
  end
end

local 宝宝控件 = 藏宝阁出售寄存:创建控件("宝宝控件", 20,40,260,240)  

function 宝宝控件:初始化()
  self:创建纹理精灵(function()
                      __res.UI素材[3]:复制区域(1125, 681, 2, 30):拉伸(2,230):显示(242,5)
                      __res.UI素材[3]:复制区域(1125, 681, 2, 30):拉伸(2,230):显示(256,5)   
                    end
              )
end


local 名称选择 = 宝宝控件:创建列表("名称选择", 10, 10, 220, 220)  
  function 名称选择:初始化()
      self.行高度= 50
      self.行间距 = 5
  end
  function 名称选择:置数据(数据)
      self:清空()
      藏宝阁出售寄存.选中宝宝=nil
      self.数据 =数据
      for i, v in ipairs(数据) do
          local r = self:添加()
          r:创建纹理精灵(function()
                    local lssj = 取头像(v.模型)
                    __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(50, 50):显示(0,0)
                    标题字体:置颜色(0,0,0,255)
                    标题字体:取图像(v.名称):显示(60,10)
                    标题字体:取图像(v.等级.."级"):显示(60,30)
                    if 藏宝阁出售寄存.额外数据 and 藏宝阁出售寄存.额外数据[i] and 藏宝阁出售寄存.额外数据[i].上架 then
                          标题字体:置颜色(__取颜色("红色")):取图像("上架中"):显示(220-标题字体:取宽度("上架中")-2,32)
                    end
                  end
              )
      end
  end
  
  function 名称选择:左键弹起(x, y, i)
        藏宝阁出售寄存.商城类型=""
        藏宝阁出售寄存.识别码=nil
        藏宝阁出售寄存.选中宝宝=nil
        if self.数据 and  self.数据[i] and self.数据[i].认证码 and 藏宝阁出售寄存.额外数据 and 藏宝阁出售寄存.额外数据[i] then
                    藏宝阁出售寄存.识别码=self.数据[i].识别码
                    藏宝阁出售寄存.商城类型="宝宝商城"
                    藏宝阁出售寄存.选中宝宝=i
                    if 藏宝阁出售寄存.额外数据[i].上架 then
                          藏宝阁出售寄存.上架按钮:重置文字("下架")
                          藏宝阁出售寄存.价格输入:清空()
                          藏宝阁出售寄存.价格输入:置颜色(__取银子颜色(藏宝阁出售寄存.额外数据[i].价格))
                          藏宝阁出售寄存.价格输入:置数值(藏宝阁出售寄存.额外数据[i].价格)
                    else  
                          藏宝阁出售寄存.上架按钮:重置文字("上架")
                          藏宝阁出售寄存.价格输入:清空()
                          藏宝阁出售寄存.价格输入:置颜色(0, 0, 0, 255)
                          藏宝阁出售寄存.价格输入:置模式(藏宝阁出售寄存.价格输入.数字模式)
                    end
        end
  end

  local  上按钮= 宝宝控件:创建按钮( "上按钮", 240, 1)
  function 上按钮:初始化()
    self:创建按钮精灵(__res:取资源动画("dlzy",0xFD3D61F2),1)
  end
  function 上按钮:左键弹起(x, y)
          名称选择:向上滚动()
  end
  
  local  下按钮= 宝宝控件:创建按钮( "下按钮", 240, 220)
  function 下按钮:初始化()
    self:创建按钮精灵(__res:取资源动画("dlzy",0x09217E13),1)
  end
  function 下按钮:左键弹起(x, y)
          名称选择:向下滚动()
  end


 
  

local 物品网格=藏宝阁出售寄存:创建背包网格("物品网格",22,40)

function 物品网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
                local xx,yy=引擎:取鼠标坐标()
              __UI弹出.道具提示:打开(物品.物品,xx+20,yy+20)
          end
end
function 物品网格:左键弹起(x,y,i)
        藏宝阁出售寄存.商城类型=""
        藏宝阁出售寄存.识别码=nil
        local 物品=self:选中物品() 
        if 物品 and 物品.物品 then
              local 选中 = self:选中()
              if 选中~=0 and  藏宝阁出售寄存.额外数据[选中] then
                    藏宝阁出售寄存.识别码=藏宝阁出售寄存.额外数据[选中].识别码
                    藏宝阁出售寄存.商城类型=藏宝阁出售寄存.额外数据[选中].商城类型
                    if 藏宝阁出售寄存.额外数据[选中].上架 then
                          藏宝阁出售寄存.上架按钮:重置文字("下架")
                          藏宝阁出售寄存.价格输入:清空()
                          藏宝阁出售寄存.价格输入:置颜色(__取银子颜色(藏宝阁出售寄存.额外数据[选中].价格))
                          藏宝阁出售寄存.价格输入:置数值(藏宝阁出售寄存.额外数据[选中].价格)
                    else  
                          藏宝阁出售寄存.上架按钮:重置文字("上架")
                          藏宝阁出售寄存.价格输入:清空()
                          藏宝阁出售寄存.价格输入:置颜色(0, 0, 0, 255)
                          藏宝阁出售寄存.价格输入:置模式(藏宝阁出售寄存.价格输入.数字模式)
                    end

                    
              end
              if __手机 then 
                  __UI弹出.道具提示:打开(物品.物品,x+40,y+40)
              end
        end

end






local 价格输入 = 藏宝阁出售寄存:创建文本输入("价格输入", 55, 290, 175, 18)  
function 价格输入:初始化()
    self:取光标精灵(0, 0, 0, 255)
    self:置限制字数(12)
    self:置颜色(0, 0, 0, 255)
    self:置模式(self.数字模式)
end



local  上架按钮= 藏宝阁出售寄存:创建蓝色按钮("上架", "上架按钮", 190, 287,42,25)  
function 上架按钮:重置文字(txt,jz)
  self:置文字(42,25,txt)
  self:置禁止(false)
  if jz then
      self:置禁止(jz)
  end
end
function 上架按钮:左键弹起(x, y)
      local 选中 = 物品网格:选中()
      if 藏宝阁出售寄存.类型==2 then
          选中=藏宝阁出售寄存.选中宝宝
      end
      if 选中==0 or not  藏宝阁出售寄存.额外数据[选中] then
          __UI弹出.提示框:打开("#Y你还没有选择物品")
      else
          if 藏宝阁出售寄存.额外数据[选中].上架 then
                if 藏宝阁出售寄存.商城类型 =="" or 藏宝阁出售寄存.商城类型 == nil  then
                  __UI弹出.提示框:打开("#Y选择类型错误！")
                elseif 藏宝阁出售寄存.识别码 =="" or 藏宝阁出售寄存.识别码 == nil  then
                  __UI弹出.提示框:打开("#Y选择类型错误！")
                else
                  请求服务(69,{类型=藏宝阁出售寄存.商城类型,文本="下架商品",编号=藏宝阁出售寄存.识别码})
                end
          else
                if not 价格输入:取数值() or 价格输入:取数值()<=0  then
                    __UI弹出.提示框:打开("#Y请正确输入出售价格")
                elseif 藏宝阁出售寄存.商城类型 =="" or 藏宝阁出售寄存.商城类型 == nil  then
                    __UI弹出.提示框:打开("#Y选择类型错误！")
                elseif 藏宝阁出售寄存.识别码 =="" or 藏宝阁出售寄存.识别码 == nil  then
                     __UI弹出.提示框:打开("#Y选择类型错误！")
                else
                    请求服务(69,{类型=藏宝阁出售寄存.商城类型,文本="上架寄存商品",编号=藏宝阁出售寄存.识别码,价格=价格输入:取数值()})
                end
          end
      end
end


local  取回按钮= 藏宝阁出售寄存:创建蓝色按钮("取回", "取回按钮", 242, 287,42,25)

function 取回按钮:左键弹起(x, y)
      local 选中 = 物品网格:选中()
      if 藏宝阁出售寄存.类型==2 then
        选中=藏宝阁出售寄存.选中宝宝
    end
      if 选中==0 or not  藏宝阁出售寄存.额外数据[选中] then
           __UI弹出.提示框:打开("#Y你还没有选择物品")
      else
          if 藏宝阁出售寄存.额外数据[选中].上架 then
                __UI弹出.提示框:打开("#Y商品上架中无法取回！")
          elseif 藏宝阁出售寄存.商城类型 =="" or 藏宝阁出售寄存.商城类型 == nil  then
                __UI弹出.提示框:打开("#Y选择类型错误！")
          elseif 藏宝阁出售寄存.识别码 =="" or 藏宝阁出售寄存.识别码 == nil  then
               __UI弹出.提示框:打开("#Y选择类型错误！")
          else
            请求服务(69,{类型=藏宝阁出售寄存.商城类型,文本="取回商品",编号=藏宝阁出售寄存.识别码,类别="上架"})
          end
      end
end


local 关闭 = 藏宝阁出售寄存:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    藏宝阁出售寄存:置可见(false)
  end






