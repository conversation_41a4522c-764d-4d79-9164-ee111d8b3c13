
local SDL = require("SDL")
local SDLF = require("SDL.函数")
local GGF = require("GGE.函数")
local 资源基类 = require("GGE.资源")
local 资源配置 = require("资源/配置")
local 资源 = class("资源", 资源配置, 资源基类)
function 资源:初始化()
    self:初始化配置()
    self:添加路径("assets")
    self.wdfs = require("资源/wdf")()
    self.UI素材 = {}
    self.cache = {}
    self.mcache = setmetatable({}, { __mode = "v" })
    引擎:注册事件(self, self)
    self.更新资源 ={}
    --self.临时资源 = {}
end

function 资源:取资源动画(wj,zy,lx)
    local path = self:检查资源(wj,zy,lx)
    
    if lx=="动画" then
        return self:get(path):取动画(1):播放(true)
    elseif lx=="置动画" then
        return require("资源/动画")(self:get(path))
    elseif lx=="精灵" then
        return self:get(path):取精灵(1)
    elseif lx=="图像" then
        return self:get(path):取图像(1)
    elseif lx=="图片" then
        return self:getPNG(path)
    elseif lx=="音乐" or lx=="音效" then
        return self:取音乐(path) 
    else
        return self:get(path)
    end

end

function 资源:检查资源(wj,zy,lxx)
    if not wj then wj ="" end
    if not zy then
        if lxx=="图片" then
            return "shape/pic/rwwh.png"
        elseif lxx=="音乐" then
            return "music/1070.mp3"
        elseif lxx=="音效" then
            return "shape/sound/1EEF0B4D.wav"
        else
            return "shape/0512F7DA.was"
        end
    end
    if self:是否存在(wj.."/"..zy) then
        return wj.."/"..zy
    else
        local 后缀=""
        if lxx=="图片" then
            后缀=".png"
        elseif lxx=="音乐" then
            后缀=".mp3"
        elseif lxx=="音效" then
            后缀=".wav"
        else
            后缀=".was"
        end
        local 资源名="."
        local 文件名=wj
        if string.find(wj,".") then
            local 临时文件=分割文本(wj,"%.")
            文件名 = 临时文件[1]
        end 
        if type(zy)=="number"  then
                资源名 = string.sub(string.format("%#x", zy), 3)
                if self:是否存在("shape/"..文件名.."/"..资源名..后缀) then
                    return "shape/"..文件名.."/"..资源名..后缀
                else
                    资源名 = string.sub(string.format("%#X", zy), 3)
                end
        elseif type(zy)=="string" then
                    local len = string.len(zy)
                    if zy:sub(len - 3, len - 3)=="." then
                           后缀=string.sub(zy, len - 3)
                           local 临时文件=分割文本(zy,"%.")
                           资源名 = 临时文件[1]
                    else
                           资源名=zy
                    end 
                    if self:是否存在("shape/"..文件名.."/"..资源名..后缀) then
                        return "shape/"..文件名.."/"..资源名..后缀
                    end
         end
        
         if lxx~="图片" and lxx~="音乐" then
            local  增数 =""
            if string.len(资源名)<8 then
                for i=1,8-string.len(资源名) do
                    增数=增数.."0"
                end
                资源名 = 增数..资源名
            end
         end
        
        if self:是否存在("shape/"..文件名.."/"..资源名..后缀) then
            return "shape/"..文件名.."/"..资源名..后缀
        else

                local name= "GX"..文件名..资源名
                local 判断文件 =name
                if name:find("[/\\]") then
                    判断文件 = name:gsub("[/\\]+", "A") 
                end
                local 储存路径 ="assets/shape/"..文件名.."/"..资源名..后缀
                local 下载路径 = "/"..文件名.."/"..资源名
                if not self.更新资源[判断文件] and __Http and __Http.ip and __Http.dk and __手机 then
                    if not lxx then lxx="was" end
                    self.更新资源[判断文件] ={path = 储存路径,http = 下载路径,lx = lxx}
                end
                if __实时更新 and __实时更新.更新==1  and 角色信息  and __手机 and  not __实时更新.npdata[判断文件] and __Http and __Http.ip and __Http.dk then
                    self.实时更新 =os.time()+1
                    if  __UI界面 and  __UI界面.界面层 and __UI界面.界面层.是否可见  then
                        local 提示=文件名.."["..资源名.."]"
                        if 提示:sub(1,1)=="/" then
                            提示=string.sub(提示,1)
                        end
                        __UI界面.界面层.聊天控件:添加文本(提示.." 未找到,请求下载中","xt")
                    end
                end
                
                if lxx=="图片" then
                    return "shape/pic/rwwh.png"
                elseif lxx=="音乐" then
                    return "music/1070.mp3"
                elseif lxx=="音效" then
                    return "shape/sound/1EEF0B4D.wav"
                else
                    return "shape/0512F7DA.was"
                end

                -------------------------------------------------------------------------------------
                ---if __Http.更新==3 and not __Http.npdata[判断文件]  then
                --     table.insert(__Http.updata,{path = 储存路径,http = 下载路径,lx = lxx})
                -- elseif __实时更新  and __Http.更新==99 and not __实时更新.npdata[判断文件]  then
                --         if __实时更新.更新==1 then
                --             self.实时更新 =os.time()+1
                --         else
                --             table.insert(__实时更新.updata,{path = 储存路径,http = 下载路径,lx = lxx})
                --         end
                        
                --         if  __UI界面 and __UI界面.界面层 and __UI界面.界面层.是否可见  then
                --             local 提示=文件名.."["..资源名..后缀.."]"
                --             if 提示:sub(1,1)=="/" then
                --                 提示=string.sub(提示,1)
                --             end
                --             __UI界面.界面层.聊天控件:添加文本(提示.." 未找到,请求下载中","xt")
                --         end
                -- end
                -------------------------------------------------------------------------------------
        end
    end
end--这个函数实现的









function 资源:getPNG(path)
    local paths = self:是否存在(path)
    if paths then
         if self.cache[paths] then
             return self.cache[paths]
         elseif self.mcache[paths] then
             self.cache[paths] = self.mcache[paths] 
             return self.cache[paths]
         else
             self.cache[paths]= require("SDL.图像")(paths)
             return self.cache[paths]
         end
    else
         local data = self:取数据("assets/shape/0512F7DA.was")
         if data then
             self.cache[paths]= require("资源/tcp")(data):取图像(1)
             return self.cache[paths]
         end
    end
end

function 资源:get(path)
    local paths = self:是否存在(path)
    if self.cache[paths] then
        self.cache[paths]:更新时间()
        return self.cache[paths]
    elseif self.mcache[paths] then
        self.cache[paths] = self.mcache[paths]
        self.cache[paths]:更新时间()
        return self.cache[paths]
    else
        if paths then
            local data = self:取数据(paths)
            if data then
                self.cache[paths] = require("资源/tcp")(data)
                self.cache[paths]:更新时间()
                return self.cache[paths]
            end
        end
    end
end


function 资源:更新事件()
    for k, v in pairs(self.cache) do
        if self.cache[k].检查时间 and self.cache[k]:检查时间(os.time()) then
            self.mcache[k] = self.cache[k]
            self.mcache[k]:更新时间()
            self.cache[k] = nil
        end
        if self.mcache[k] and self.mcache[k].检查时间 and self.mcache[k]:检查时间(os.time()) then
            self.mcache[k] = nil
        end
    end 
end

function 资源:取地图(path)
    path = self:是否存在(path)
    if path then
        return require("资源/map")(path)
    end
end



function 资源:取染色(path)
    path = self:是否存在("wpal/" .. path .. ".wpal")
    if path then
        local data = self:取数据(path)
        if data then
            return self:depp(data)
        end
    end
end

function 资源:公共资源(path)
    path = self:是否存在(path)
    if path then
        self.UI素材[#self.UI素材 + 1] = require("SDL.图像")(path)
    end
end

function 资源:getPNGCC(id, x, y, w, h, txt)
    local path = id .. "*" .. x .. "*" .. y .. "*" .. w .. "*" .. h
    if self.cache[path] then
        -- self.cache[path]["更新时间"](self.cache[path])
        -- if txt then
        --     return self.cache[path]:复制()
        -- else
        return self.cache[path]
        --end
    elseif self.mcache[path] then
        self.cache[path] = self.mcache[path]
        --   self.cache[path]["更新时间"](self.cache[path])
        -- if txt then
        --     return self.cache[path]:复制()
        -- else
        return self.cache[path]
       -- end
    end
    self.cache[path] = self.UI素材[id]:复制区域(x, y, w, h)
    if txt then
        return self.cache[path]:复制()
    else
        return self.cache[path]
    end
end

function 资源:读入文件(path)
    return self:取数据(path)
end

function 资源:写出文件(path, dtat)
    path = SDLF.取内部存储路径() .. "/" .. path
    GGF.创建目录(path)
    SDL.写出文件(path, dtat)
end





return 资源
