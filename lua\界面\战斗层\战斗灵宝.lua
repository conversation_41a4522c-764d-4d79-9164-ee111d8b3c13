local 战斗灵宝 =  战斗层:创建窗口("战斗灵宝", 0, 0, 490, 280)
function 战斗灵宝:初始化()
  self:置精灵(置窗口背景("选择灵宝", 0, 12, 490, 268))

  self:置坐标(引擎.宽度-500, 引擎.高度2-140)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end



function 战斗灵宝:打开(data)
  界面层.战斗界面:置可见(false)
  self:置可见(true,true)
  self.灵宝佩戴 = {}
  self.消耗 = {[1]=1,[2]=1}
  self.当前灵元 = tonumber(data.灵元)
  self.选中=nil
  for i=1,2  do
      self.灵宝佩戴[i]={}
      if data.灵宝佩戴[i] and  data.灵宝佩戴[i].名称 then
         local xxxs= 取物品(data.灵宝佩戴[i].名称)
         self.灵宝佩戴[i].名称=data.灵宝佩戴[i].名称
         self.灵宝佩戴[i].大动画=xxxs[13]
         self.灵宝佩戴[i].资源 =xxxs[11]
         self.灵宝佩戴[i].介绍 = xxxs[1]
         if data.灵宝佩戴[i].气血==1 then
            self.消耗[i] = 1
          elseif data.灵宝佩戴[i].气血==2 then
            self.消耗[i] = 3
          elseif data.灵宝佩戴[i].气血==3 then
            self.消耗[i] = 7
          end
          self.灵宝佩戴[i].介绍 =self.灵宝佩戴[i].介绍..
									  "\n#W/【使用条件】\n".."#G"..data.灵宝佩戴[i].特技.."#G专用，消耗#G"..self.消耗[i].."#G点灵元"..
									  "\n#R【战斗效果】"..
									  "\n#W/"..xxxs[15]..
									  "\n#Y当前灵元:#G"..self.当前灵元
      end
    
  end
  self.灵宝网格:置物品(self.灵宝佩戴)

end
		


   

local 灵宝网格 = 战斗灵宝:创建网格("灵宝网格", 10, 45, 470, 220)
function 灵宝网格:初始化()
  self:创建格子(220, 220, 0, 30, 1, 2)
end
function 灵宝网格:置物品(data)
  for i = 1, #self.子控件 do
     self.子控件[i]:创建纹理精灵(function()
        __res:getPNGCC(3, 442, 931, 200, 200):拉伸(220, 220):显示(0, 0)
        if data[i]  and data[i].大动画 then
          __res:取资源动画(data[i].资源,data[i].大动画,"图像"):显示(50, 50)
        end
     end
   )
  end
end

function 灵宝网格:左键弹起(x, y, a)
  if 战斗灵宝.灵宝佩戴[a].大动画 then
    if not 战斗灵宝.选中 or 战斗灵宝.选中 ~= a then
        战斗灵宝.选中 = a
        __UI弹出.自定义提示:打开(战斗灵宝.灵宝佩戴[a],x+20,y+20)
    else
       界面层.战斗界面:设置灵宝参数(a, 战斗灵宝:取可用道具(战斗灵宝.灵宝佩戴[a].名称))
    end
  end
end






function 战斗灵宝:取可用道具(名称)
    if 名称=="风舞心经" or 名称=="寒霜盾戟"  or 名称=="战神宝典" then
      return 66
    elseif 名称=="断穹巨剑" or 名称=="相思寒针" or 名称=="锢魂命谱" or 名称=="青狮獠牙"  or 名称=="冥火炼炉" or 名称=="缚仙蛛丝" or 名称=="煞魂冥灯" or 名称=="九霄龙锥" or 名称=="化怨清莲"
      or 名称=="天雷音鼓"  then
      return 4
    elseif 名称=="静心禅珠" or 名称=="宁心道符" or 名称=="真阳令旗" or 名称=="护体灵盾" or 名称=="惊兽云尺"  or 名称=="乾坤金卷" or 名称=="乾坤木卷" or 名称=="乾坤水卷" or 名称=="乾坤火卷" or 名称=="乾坤土卷"  or 名称=="赤炎战笛" then
      return 3
    end
    return 0
 end


 local 关闭 = 战斗灵宝:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y, msg)
    战斗层.战斗灵宝:置可见( false)
    界面层.战斗界面:重置()
    界面层.战斗界面:置可见(true)
end


