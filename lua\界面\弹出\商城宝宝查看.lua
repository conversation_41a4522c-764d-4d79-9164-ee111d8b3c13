__UI弹出["商城宝宝查看"] = __UI界面["创建弹出窗口"](__UI界面, "商城宝宝查看", 194+43 + abbr.py.x, 174-48 + abbr.py.y, 428, 248+55+48)
local 商城宝宝查看 = __UI弹出["商城宝宝查看"]
local zizhi={"攻击资质","防御资质","体力资质","法力资质","速度资质","躲闪资质","成      长"}

function 商城宝宝查看:初始化()
  local nsf = require("SDL.图像")(428, 248+55+48)
  if nsf["渲染开始"](nsf) then
    取黑色背景(0, 0, 428, 248+55+48, true):置透明(180):显示(0, 0)
    local gezi=__res:getPNGCC(3, 1091, 374, 50, 50)
    字体18:置颜色(__取颜色("白色"))
    local lssj = 取输入背景(0, 0, 93, 23)
    for i=1,7 do
      字体18:取图像(zizhi[i]):显示(20,18+(i-1)*29)
      lssj:显示(20+76,18-3+(i-1)*29)
    end
    local aa,bb=1,1
    for i=1,24 do
      gezi:显示(200+(aa-1)*55,18+(bb-1)*55)
      aa=aa+1
      if aa>=5 then
        aa=1
        bb=bb+1
      end
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 商城宝宝查看:打开(data)
  self:置可见(true)
  self.数据 = data
  self.状态="物理"
  self[self.状态]:置选中(true)
  self:重置()
  -- table.print(data)
end
local jinengwangge = 商城宝宝查看["创建网格"](商城宝宝查看, "jinengwangge", 97+108-5, 18, 327, 283)
function jinengwangge:初始化()

end
function jinengwangge:左键弹起(x, y, a, b, msg)
  if not self.子控件[a] or not self.子控件[a]._spr or not self.子控件[a]._spr["模型"] then
    return
  end
  self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, 86, w, h, "大师傅地方", a)
end
function jinengwangge:置技能()
  
  local jn0=商城宝宝查看.数据[商城宝宝查看.状态].技能
  self:创建格子(55, 55, 0, 0, #jn0/4, 4)
  local jn={}
  for i = 1, #jn0 do
    jn[i]={名称=jn0[i]}
  end
  for i = 1, #jn do
    if jn[i] then
      local lssj = __40格子.创建()
      -- print(坐骑技能栏.物品组[i].名称)
      lssj:置数据(jn[i], 40,40 )
      self["子控件"][i]["置精灵"](self["子控件"][i], lssj)
    else
      local lssj = __40格子.创建()
      -- print(坐骑技能栏.物品组[i].名称)
      lssj:置数据(sdw[i], 40,40)
      self["子控件"][i]["置精灵"](self["子控件"][i], lssj)
    end
  end
end
function 商城宝宝查看:重置()
  self.图像2=nil
  self.jinengwangge:置技能()
  local nsf = require("SDL.图像")(327, 283)
  if nsf["渲染开始"](nsf) then
    字体18:置颜色(__取颜色("浅黑"))
    for i=1,7 do
      字体18:取图像(self.数据[self.状态].资质[i]):显示(20,(i-1)*29)
    end
    nsf["渲染结束"](nsf)
  end
  self.图像2 = nsf["到精灵"](nsf)
  self.图像2:置中心(-97,-18)
end
function 商城宝宝查看:显示(x,y)
  if self.图像2 then
    self.图像2:显示(x,y)
  end
end
    -- tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31),
    -- tcp2 = __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31),
local 物理按钮 = 商城宝宝查看["创建我的单选按钮"](商城宝宝查看,  __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31), __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31), "物理", 18, 220, "物理") 
function  物理按钮:左键弹起(x, y)
  商城宝宝查看.状态="物理"
  商城宝宝查看:重置()
end
local 法术按钮 = 商城宝宝查看["创建我的单选按钮"](商城宝宝查看,  __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31), __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31), "法术", 18, 220+58-20, "法术") 
function  法术按钮:左键弹起(x, y)
  商城宝宝查看.状态="法系"
  商城宝宝查看:重置()
end