-- <AUTHOR> GGELUA
-- @Last Modified by    : baidwwy
-- @Date                : 2024-09-04 16:18:56
-- @Last Modified time  : 2024-11-23 22:22:41

--[[
    <AUTHOR> GGELUA
    @Date         : 2022-11-01 00:40:44
Last Modified by: GGELUA
Last Modified time: 2024-08-03 18:31:15
--]]
local SDLF = require("SDL.函数")
local 左上角 = __UI界面.界面层:创建控件("左上角", 0, 0, 500, 160)
local lssj = {
  "子时",
  "丑时",
  "寅时",
  "卯时",
  "辰时",
  "巳时",
  "午时",
  "未时",
  "申时",
  "酉时",
  "戌时",
  "亥时"
}
function 左上角:初始化()
  self.时辰 = 1
  self.电池外框 = __res:getPNGCC(2, 1107, 18, 22, 12):到精灵()
  -- self:asdsad(操作)
end
local 昼夜 = 左上角:创建控件("昼夜", 15, 23, 44, 40+6)
function 昼夜:初始化()
  self.白昼 = __res:取图像(__res:取地址("shape/ui/", 2650201788)):拉伸(360, 40):到精灵()
  self.黑夜 = __res:取图像(__res:取地址("shape/ui/", 2574487372)):拉伸(360, 40):到精灵()
  self.计时 = 1
  self.计次 = 310
  self.小人 = __res:取动画(__res:取地址("shape/ui/", 2888856949)):取动画(1):播放(true)
end
function 昼夜:更新(dt)
  self.计时 = self.计时 + dt * 2
  if self.计时 >= 1 then
    self.计次 = self.计次 - 1
    if self.计次 < 45 then
      self.计次 = 310
    end
    self.计时 = 0
  end
  self.小人:更新(dt)
end
function 昼夜:显示(x, y)
  if 左上角.时辰 >= 11 or 左上角.时辰 <= 4 then
    self.黑夜:显示(x - self.计次, y+6)
  else
    self.白昼:显示(x - self.计次, y+6)
  end
  self.小人:显示(x + 18, y + 40+6)
end

function 左上角:asdsad(操作)
  -- self.图像5=nil
  显示战斗鼠标=true
  -- local nsf = require("SDL.图像")(500, 160)
  -- if nsf["渲染开始"](nsf) then
  --     -- nsf["渲染清除"](nsf, 3, 0, 0, 150)
  --     local wenben="请选择目标："..操作
  --     local kd = 字体18["取宽度"](字体18, wenben)
  --     __res["UI素材"][3]:复制区域(216, 1148, 224, 28):显示(136+(kd + 20) / 2-38, 5)
  --     字体18:置颜色(__取颜色("黄色"))
  --     字体18:取图像(wenben):显示(121+176 - kd / 2, 10)
  --     nsf["渲染结束"](nsf)
  -- end
  -- self.图像5 = nsf["到精灵"](nsf)
end

function 左上角:显示(x, y)
  self.电池外框:显示(x + 155, y + 1+4)
  if self.电池 then


    self.电池:显示(x + 155, y + 4)
  end
  if __主控["战斗中"] then
    if self.图像5 then
      self.图像5:显示(x , y + 2)
    end
  else
    self.图像5=nil
  end
end
local 时辰按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 3, 440, 150, 68), "时辰按钮", 1, 2+6-5)
function 时辰按钮:显示(x, y)
  if self.时辰 then
    self.时辰:显示(x, y)
  end
  字体12:显示(x + 23, y + 27, 取地图名称(角色信息.地图数据.编号))
  字体12:显示(x + 80, y + 27, string.format("(%s,%s)", math.floor(角色信息.坐标.x / 20), math.floor(角色信息.坐标.y / 20)))

  字体12:置颜色(255, 227, 132)
  字体12:显示(x + 74, y+8, os.date("%H:%M:%S", os.time()))

end

-- local 退出战斗按钮 = 左上角:创建我的按钮18(__res:getPNGCC(7, 0, 209, 118, 28):拉伸(95, 32), "退出战斗按钮", 205-14, 20,"退出战斗")
-- local fjkjdf=os.time()
-- function 退出战斗按钮:左键弹起(x, y, msg)
--   if fjkjdf-os.time()<=0 then
--     fjkjdf=os.time()+180
--     发送数据(6585)
--   else
--     __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y每次退出倒计时为3分钟，请勿频繁操作")
--   end
-- end






function 时辰按钮:重置(数据)
    local nsf = require("SDL.图像")(360, 460)
    if nsf:渲染开始() then
      字体18:置颜色(59, 10, 1)
     -- 字体18:取图像(取地图名称(角色信息.地图数据.编号)):显示(73, 18)
      字体14:置颜色(255, 255, 255)
      if 数据 then
        左上角.时辰 = 数据
      end
      字体12:取我的描边图像(lssj[左上角.时辰], 59, 10, 1, 255):显示(40, 8)
      nsf:渲染结束()
    end
    self.时辰 = nsf:到精灵()
    local zt, dl = SDLF:取电源信息()
 
    if "充电中" == zt or "已充满" == zt or "使用电池" == zt then
      local nsf = require("SDL.图像")(20, 10)
      if nsf:渲染开始() then
        dl = math.floor(20 * dl / 100)
        local qs = 20 - dl
        __res:getPNGCC(2, 1109, 31, 20, 10):置区域(qs, 0, dl, 10):显示(qs, 0+4)
        -- 字体14:置颜色(字体14, __取颜色("红色"))
        -- if "充电中" == zt then
        --   字体14:取图像(字体14, "z"):显示(字体14:取图像(字体14, "z"), 9, -5+6)
        -- elseif "已充满" == zt then
        --   字体14:取图像(字体14, "="):显示(字体14:取图像(字体14, "="), 9, -5+6)
        -- end
        nsf:渲染结束()
      end
      左上角.电池 = nsf:到精灵()
    end
   -- print(zt, dl)
end
function 时辰按钮:左键弹起(x, y, msg)

end

local 阳光按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 219, 35, 16, 15), "阳光按钮", 17, 12)
function 阳光按钮:左键弹起(x, y, msg)
end

local 隐藏按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 202, 177, 18, 14), "隐藏按钮", 11, 52)
function 隐藏按钮:左键弹起(x, y, msg)
end



local 小地图按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 135, 126, 26, 26), "小地图按钮", 65, 45)
function 小地图按钮:左键弹起(x, y, msg)
  if not __主控.战斗中 then
    __UI界面.窗口层.小地图:打开(角色信息.地图数据.编号)
  end
end

local 世界按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 187, 57, 31, 24), "世界按钮", 30, 46)
function 世界按钮:左键弹起(x, y, msg)
end

local 导航按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 135, 158, 20, 26), "导航按钮", 94, 46)
function 导航按钮:左键弹起(x, y, msg)
  发送数据(93,{"伤害排行"})

end

local 日历按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 171, 91, 21, 25), "日历按钮", 120, 46)
function 日历按钮:左键弹起(x, y, msg)
  发送数据(66)
end

-- local 商城按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 135, 190, 29, 25), "商城按钮", 5, 75)
-- function 商城按钮:左键弹起(x, y, msg)
--   __UI界面["窗口层"]["商城"]:打开()
-- end

local 助战按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 135, 221, 26, 24), "助战按钮", 5, 75)
function 助战按钮:左键弹起(x, y, msg)
  发送数据(2001)
end

local 助战仓库 = 左上角:创建我的按钮(__res:getPNGCC(10, 135, 251, 24, 24), "助战仓库", 77, 75)
function 助战仓库:左键弹起(x, y, msg)
  --发送数据(190,{序号1=1})
  发送数据(2101)
end

local 赞助按钮 = 左上角:创建我的按钮(__res:getPNGCC(10, 167, 126, 23, 25), "赞助按钮", 115, 75)
function 赞助按钮:左键弹起(x, y, msg)
  发送数据(179,{数据序列= 2})

end

local 挂机 = 左上角:创建我的按钮(__res:getPNGCC(10, 135, 281, 29, 23), "挂机按钮", 40, 75)
function 挂机:左键弹起(x, y, msg)
    if not __全局自动走路开关 then
        if __主显["场景最低等级"] then
            __全局自动走路开关 = true
            __全局自动走路秒 = 0
            __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y自动挂机已开启！")
        else
            __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y/自动挂机开启失败!当前场景不属于野外区.....")
        end
    else
        __全局自动走路开关 = false
        __全局自动走路秒 = 0
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y自动挂机已关闭！")
    end
end
-- local 左关闭 = 左上角:创建我的按钮(__res:getPNGCC(1, 675, 202, 35, 64), "左关闭", 299, 85+7)
-- function 左关闭:左键弹起(x, y, msg)
--   if 左上角.功能栏.是否可见 then
--     左上角.功能栏:置可见(false)
--     self:置坐标(10, 85+7)
--   else
--     左上角.功能栏.置可见(左上角.功能栏, true)
--     self:置坐标(134+38+38+24, 85+7)
--   end
-- end
local 功能栏 = 左上角:创建控件("功能栏", 0, 85, 500, 68)
function 功能栏:初始化()
--  self:置精灵(__res:getPNGCC(2, 1070, 305, 93, 65):拉伸(93+42+45+48+42, 65):到精灵())
  local nsf = require("SDL.图像")(500, 68)
  local fsdf=__res:getPNGCC(2, 1115, 568, 65, 65)
  if nsf["渲染开始"](nsf) then
      __res:getPNGCC(2, 1070, 305, 10, 66):显示(0, 3) --左
      for i=1,3 do
        fsdf:显示(10+(i-1)*70, 3)
        __res:getPNGCC(2, 1070+10, 305, 70, 66):显示(10+(i-1)*70, 3)
      end
      __res:getPNGCC(2, 1070+83, 305, 11, 66):显示(10+3*70, 3) --右
      nsf["渲染结束"](nsf)
      local zt, dl = SDLF:取电源信息()
      if   not 调试模式 then
        if dl < 20 then
        __UI界面.窗口层.电量过低提示:打开("您的手机电量低于20%，请尽快充电！", 285, 155-10, 390, 200)
        elseif dl < 10 then
        __UI界面.窗口层.电量过低提示:打开("您的手机电量低于10%，请尽快充电！", 285, 155-10, 390, 200)
      
        end
      end
  end
  self:置精灵(nsf["到精灵"](nsf))
end
for i, v in ipairs({
  {
    name = "商城",
    x = 15-5+3,
    y = 11,
    tcp = __res:getPNGCC(2, 985, 125, 59, 52)
    --tcp = __res:getPNGCC(2, 1114, 63, 56, 56)
  },
  {
    name = "VIP",
   x = 75-5+20+20+47+3,
    y = 9,
    tcp = __res:getPNGCC(2, 1058, 65, 49, 52)
  },
  -- {
  --   name = "藏宝阁",
  --   x = 75-5+20-3,
  --   y = 8,
  --   tcp = __res:getPNGCC(2, 1106, 62, 52, 56)
  -- },
  {
    name = "活动",
    x = 75-5+20-3,
    -- x = 75-5+20+20+47+3,
    y = 7,
    tcp = __res:getPNGCC(2, 886, 120, 42, 54)
  },

}) do
  local 临时函数 = 功能栏:创建我的按钮(v.tcp, v.name, v.x, v.y)
  function 临时函数:左键弹起(x, y)
    if not __主控.战斗中 then
      if v.name == "商城" then
        __UI界面["窗口层"]["商城"]:打开()
      elseif v.name == "日历" then
        发送数据(66)
      elseif v.name == "VIP" then
       -- 发送数据(179,{数据序列= 2})
      -- elseif v.name == "指引" then
      --   __UI界面.窗口层.梦幻指引:打开()
      end
    end
  end
end
