# PC端热更新功能说明

## 功能概述

实现了PC端和移动端统一的热更新机制，两个平台使用相同的服务器、配置和资源，但根据平台特性下载不同的可执行文件。

## 核心特性

### 1. 平台统一架构
- **相同的服务器配置**：都使用 `121.41.9.212:18886`
- **相同的版本检查**：都从 `/bbb.txt` 获取版本号
- **相同的配置文件**：共享 `config.txt` 和所有资源文件
- **相同的更新流程**：版本检查 → 下载更新 → 重启应用

### 2. 平台差异化下载

#### 移动端（Android）
- **下载路径**：`/ggelua.com`（服务器）
- **保存文件**：`/ggelua.com`（本地）
- **文件类型**：Android可执行文件

#### PC端（Windows）
- **下载路径**：`/ggelua.com`（服务器）
- **保存文件**：`/ggelua.com`（本地）
- **文件类型**：lua脚本包（由MHXY.exe引擎加载）
- **引擎文件**：`MHXY.exe`（固定，不参与热更新）

### 3. 自动平台检测
系统根据 `__手机` 变量自动判断当前平台：
- `__手机 = true`：移动端模式
- `__手机 = false`：PC端模式

## 技术实现

### 修改的核心文件

#### 1. `lua/网络/网站处理.lua`
- **移除平台限制**：PC端也能进行版本检查
- **增加平台差异化下载**：根据平台下载不同文件
- **扩展保存逻辑**：支持不同的文件名保存

#### 2. `lua/UIlayer.lua`
- **统一启动流程**：PC端和移动端使用相同逻辑
- **调用原有API**：复用 `__Http:效验版本号()` 方法

### 关键代码逻辑

```lua
-- 统一下载逻辑：PC端和移动端都下载相同的ggelua.com文件
function Http:更新主体(bbh)
    __res.配置.版本号 = bbh
    local 下载名="ggelua"
    
    if __主体名称 then
        下载名=__主体名称
    end

    -- PC端和移动端都下载相同的ggelua.com文件
    -- PC端的MHXY.exe是固定引擎，只更新ggelua.com（包含lua脚本）
    print("开始更新：", __手机 and "移动端" or "PC端", "下载ggelua.com文件")
    
    self.updata[1]= {
        path = "/ggelua.com",  -- PC端和移动端都保存为ggelua.com
        http = "/" .. 下载名,  -- 服务器路径，都下载ggelua.com
        lx = "脚本"
    }
end
```

## 服务器要求

为了支持PC端热更新，服务器需要提供以下文件：

### 必需文件
1. **`/bbb.txt`** - 版本号文件（两个平台共用）
2. **`/ggelua.com`** - lua脚本包（PC端和移动端共用）

### 可选扩展
- `/ggelua-linux.com` - Linux版本
- `/ggelua-macos.com` - macOS版本

## 使用流程

### 正常更新流程
1. **启动应用**：PC端或移动端启动
2. **平台检测**：自动识别当前运行平台
3. **版本检查**：连接服务器获取 `/bbb.txt` 中的版本号
4. **版本比较**：与本地版本号比较
5. **统一下载**：
   - 移动端：下载 `/ggelua.com`
   - PC端：下载 `/ggelua.com`（相同文件）
6. **文件保存**：
   - 移动端：保存为 `ggelua.com`
   - PC端：保存为 `ggelua.com`（由MHXY.exe加载）
7. **配置更新**：更新 `config.txt` 中的版本号
8. **自动重启**：关闭当前应用，准备使用新版本

### 错误处理
- **连接失败**：跳过更新，直接进入应用
- **文件不存在**：显示404错误，继续使用现有版本
- **下载失败**：重试机制或跳过更新

## 优势特性

### 1. 无缝兼容
- **零破坏**：完全保持原有移动端逻辑
- **统一管理**：服务器端只需维护一套配置
- **共享资源**：配置文件、素材资源完全复用

### 2. 智能适配
- **自动识别**：无需手动配置平台
- **差异化处理**：只有可执行文件不同，其他完全一致
- **扩展友好**：可轻松支持更多平台

### 3. 运维简化
- **统一版本号**：所有平台使用相同版本管理
- **统一配置**：服务器配置、更新流程完全一致
- **统一监控**：可使用相同的监控和日志系统

## 注意事项

### 1. 服务器配置
确保服务器上存在对应平台的可执行文件：
- 移动端：`/ggelua.com`
- PC端：`/ggelua-windows.com`

### 2. 文件权限
PC端下载的 `ggelua.exe` 需要有执行权限。

### 3. 版本同步
所有平台的可执行文件应该功能一致，只是编译目标不同。

## 测试验证

### 测试步骤
1. 启动应用（PC端）
2. 观察控制台输出：
   ```
   开始版本检查，平台: PC端
   PC端更新：将下载Windows版本
   更新配置：本地路径=/ggelua.exe 服务器路径=/ggelua-windows.com
   ```
3. 确认下载和更新流程正常

### 预期结果
- PC端能够正常检查版本
- 下载ggelua.com脚本包文件
- 保存为 `ggelua.com` 文件
- 配置文件正确更新
- MHXY.exe引擎重启并加载新的ggelua.com

## 未来扩展

### 1. 多平台支持
可以轻松扩展支持更多平台：
- Linux：`/ggelua-linux.com`
- macOS：`/ggelua-macos.com`
- ARM版本：`/ggelua-arm.com`

### 2. 增量更新
可以扩展支持增量更新机制，只下载变更部分。

### 3. 校验机制
可以添加文件完整性校验（MD5、SHA256等）。

---

*本文档描述了PC端热更新功能的完整实现，确保了与移动端的完全兼容性和统一性。* 