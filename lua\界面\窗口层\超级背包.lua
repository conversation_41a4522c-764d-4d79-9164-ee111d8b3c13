local 超级背包 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "超级背包", 138 + abbr.py.x, 17 + abbr.py.y, 695, 496)
function 超级背包:初始化()
  local nsf = require("SDL.图像")(695, 500)
  if nsf["渲染开始"](nsf) then
   
    


    置窗口背景("超级背包", 0, 12, 550, 500, true)["显示"](置窗口背景("超级背包", 0, 12, 550, 500, true), 0, 0)
    --取白色背景(0, 0, 650, 286, true)["显示"](取白色背景(0, 0, 650, 286, true), 17, 106)
    self.格子背景= __res:getPNGCC(3, 133, 507, 56, 56)["到精灵"]((__res:getPNGCC(3, 133, 507, 56, 56)))
    local xx= 0
    local yy = 0
    for i = 1 , 48 do 

    __res:getPNGCC(3, 133, 507, 56, 56)["显示"](__res:getPNGCC(3, 133, 507, 56, 56), 20+xx*65, 70+yy*65)
        xx= xx +1
        if xx== 8 then
            xx=0
            yy=yy+1
        end
    end
    字体18["置颜色"](字体18, __取颜色("白色"))
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
local data 

function 超级背包:打开(数据)
  self:置可见(true)
  data=数据
  self.当前页面="宝石背包"
  超级背包["宝石背包"]["重置"](超级背包["宝石背包"])
  
超级背包["宝石背包"]["置可见"](超级背包["宝石背包"],超级背包["宝石背包"]== 超级背包["宝石背包"], not 超级背包["宝石背包"])
   

end


function 超级背包:刷新(数据)
    data=数据

   -- 超级背包["材料背包"]["重置"](超级背包["材料背包"])
    超级背包[超级背包.当前页面]["重置"](超级背包[超级背包.当前页面])
end


local 杂货背包 = 超级背包["创建我的控件"](超级背包, "杂货背包", 0, 0, 650, 486)
function 杂货背包:初始化()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end
function 杂货背包:重置()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
  nsf["渲染结束"](nsf)
  end

  超级背包["杂货背包"]["道具网格"]["置物品"](超级背包["杂货背包"]["道具网格"],data.杂货背包)


end
local 道具网格 = 杂货背包["创建网格"](杂货背包, "道具网格", 25,70, 800, 400)
function 道具网格:初始化()
  self:创建格子(55, 55, 10, 10, 6, 8)
end
function 道具网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
        if not self.子控件[a]._spr["确定"] then
            __UI弹出.提示框:打开("#Y双击取出物品")
            self.子控件[a]._spr["确定"]=true
        else
            self.子控件[a]._spr["确定"]=nil
            发送数据(190,{序号1=2,选中=a,类型="杂货背包"})
        end
    end
end
function 道具网格:置物品(数据)
  for i = 1, #道具网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "超级背包")
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)
    else
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i])
    end
  end
end
---------------------------------------------------
local 书铁背包 = 超级背包["创建我的控件"](超级背包, "书铁背包", 0, 0, 650, 486)
function 书铁背包:初始化()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end
function 书铁背包:重置()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
  nsf["渲染结束"](nsf)
  end
  超级背包["书铁背包"]["道具网格"]["置物品"](超级背包["书铁背包"]["道具网格"],data.书铁背包)
 
end
local 道具网格 = 书铁背包["创建网格"](书铁背包, "道具网格", 25,70, 50, 50)
function 道具网格:初始化()
  self:创建格子(55, 55, 10, 10, 6, 8)
end
function 道具网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a]._spr and self.子控件[a]._spr["物品"]  then
        if not self.子控件[a]._spr["确定"] then
            __UI弹出.提示框:打开("#Y双击取出物品")
            self.子控件[a]._spr["确定"]=true
       --  self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 70, 86, w, h, "选择", a)
        else
            self.子控件[a]._spr["确定"]=nil
            发送数据(190,{序号1=2,选中=a,类型="书铁背包"})
        end
    end
end
function 道具网格:置物品(数据)
  for i = 1, #道具网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "超级背包")
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)
    else
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i])
    end
  end
end
local 材料背包 = 超级背包["创建我的控件"](超级背包, "材料背包", 0, 0, 650, 486)
function 材料背包:初始化()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end
function 材料背包:重置()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
  nsf["渲染结束"](nsf)
  end
  超级背包["材料背包"]["道具网格"]["置物品"](超级背包["材料背包"]["道具网格"],data.材料背包)
end
local 道具网格 = 材料背包["创建网格"](材料背包, "道具网格", 25,70, 800, 400)
function 道具网格:初始化()
  self:创建格子(55, 55, 10, 10, 6, 8)
end
function 道具网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
        if not self.子控件[a]._spr["确定"] then
            __UI弹出.提示框:打开("#Y双击取出物品")
            self.子控件[a]._spr["确定"]=true
        else
            self.子控件[a]._spr["确定"]=nil

            发送数据(190,{序号1=2,选中=a,类型="材料背包"})
        end
    end
end
function 道具网格:置物品(数据)
  for i = 1, #道具网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "超级背包")
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)
    else
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i])
    end
  end
end
local 装备背包 = 超级背包["创建我的控件"](超级背包, "装备背包", 0, 0, 650, 486)
function 装备背包:初始化()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end
function 装备背包:重置()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
  nsf["渲染结束"](nsf)
  end
  超级背包["装备背包"]["道具网格"]["置物品"](超级背包["装备背包"]["道具网格"],data.装备背包)
end
local 道具网格 = 装备背包["创建网格"](装备背包, "道具网格", 25,70, 800, 400)
function 道具网格:初始化()
  self:创建格子(55, 55, 10, 10, 6, 8)
end
function 道具网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
        if not self.子控件[a]._spr["确定"] then
            __UI弹出.提示框:打开("#Y双击取出物品")
            self.子控件[a]._spr["确定"]=true
        else
            self.子控件[a]._spr["确定"]=nil
            发送数据(190,{序号1=2,选中=a,类型="装备背包"})
        end
    end
end
function 道具网格:置物品(数据)
  for i = 1, #道具网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "超级背包")
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)
    else
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i])
    end
  end
end
---------------------------------------------------
local 宝石背包 = 超级背包["创建我的控件"](超级背包, "宝石背包", 0, 0, 650, 486)
function 宝石背包:初始化()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end
function 宝石背包:重置()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
  nsf["渲染结束"](nsf)
  end
  超级背包["宝石背包"]["道具网格"]["置物品"](超级背包["宝石背包"]["道具网格"],data.宝石背包)
 
end
local 道具网格 = 宝石背包["创建网格"](宝石背包, "道具网格", 25,70, 800, 400)
function 道具网格:初始化()
  self:创建格子(55, 55, 10, 10, 6, 8)
end
function 道具网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
        if not self.子控件[a]._spr["确定"] then
            __UI弹出.提示框:打开("#Y双击取出物品")
            self.子控件[a]._spr["确定"]=true
         --self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 70, 86, w, h, "选择", a)
        else
            self.子控件[a]._spr["确定"]=nil
            发送数据(190,{序号1=2,选中=a,类型="宝石背包"})
        end
    end
end
function 道具网格:置物品(数据)
  for i = 1, #道具网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "超级背包")
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)
    else
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i])
    end
  end
end

local 关闭 = 超级背包["创建我的按钮"](超级背包, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 505, 0)
function 关闭:左键弹起(x, y, msg)
  超级背包["置可见"](超级背包, false)
end

for i, v in ipairs({
  {
    name = "宝石背包1",
    x = 25,
    y = 42,
    tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(85, 22),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true):拉伸(85, 22),
    font = "宝石背包"
  },
  {
    name = "装备背包1",
    x = 125,
    y = 42,
    tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(85, 22),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true):拉伸(85, 22),
    font = "装备背包"
  },
  {
    name = "宝宝背包1",
    x = 225,
    y = 42,
    tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(85, 22),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true):拉伸(85, 22),
    font = "宝宝背包"
  },
  {
    name = "书铁背包1",
    x = 325,
    y = 42,
    tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(85, 22),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true):拉伸(85, 22),
    font = "书铁背包"
  },
  {
    name = "杂货背包1",
    x = 425,
    y = 42,
    tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(85, 22),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true):拉伸(85, 22),
    font = "杂货背包"
  },
  
}) do
  local 临时函数 = 超级背包["创建我的单选按钮"](超级背包, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "宝石背包1"  then 
        超级背包.当前页面="宝石背包"
    超级背包["宝石背包"]["重置"](超级背包["宝石背包"])
    超级背包["宝石背包"]["置可见"](超级背包["宝石背包"],超级背包["宝石背包"]== 超级背包["宝石背包"], not 超级背包["宝石背包"])   
    超级背包["装备背包"]["置可见"](超级背包["装备背包"],超级背包["装备背包"], false)
    超级背包["材料背包"]["置可见"](超级背包["材料背包"],超级背包["材料背包"], false)
    超级背包["书铁背包"]["置可见"](超级背包["书铁背包"],超级背包["书铁背包"], false)
    超级背包["杂货背包"]["置可见"](超级背包["杂货背包"],超级背包["杂货背包"], false)
           
      elseif    v.name == "装备背包1"  then 
        超级背包.当前页面="装备背包"
        超级背包["装备背包"]["重置"](超级背包["装备背包"])
        超级背包["装备背包"]["置可见"](超级背包["装备背包"],超级背包["装备背包"]== 超级背包["装备背包"], not 超级背包["装备背包"])   
        超级背包["宝石背包"]["置可见"](超级背包["宝石背包"],超级背包["宝石背包"], false)
        超级背包["材料背包"]["置可见"](超级背包["材料背包"],超级背包["材料背包"], false)
        超级背包["书铁背包"]["置可见"](超级背包["书铁背包"],超级背包["书铁背包"], false)
        超级背包["杂货背包"]["置可见"](超级背包["杂货背包"],超级背包["杂货背包"], false)
               
    elseif    v.name == "宝宝背包1"  then 

        超级背包.当前页面="材料背包"
        超级背包["材料背包"]["重置"](超级背包["材料背包"])
        超级背包["材料背包"]["置可见"](超级背包["材料背包"],超级背包["材料背包"]== 超级背包["材料背包"], not 超级背包["材料背包"])   
        超级背包["宝石背包"]["置可见"](超级背包["宝石背包"],超级背包["宝石背包"], false)
        超级背包["装备背包"]["置可见"](超级背包["装备背包"],超级背包["装备背包"], false)
        超级背包["书铁背包"]["置可见"](超级背包["书铁背包"],超级背包["书铁背包"], false)
        超级背包["杂货背包"]["置可见"](超级背包["杂货背包"],超级背包["杂货背包"], false)
               
    elseif    v.name == "书铁背包1"  then 

        超级背包.当前页面="书铁背包"
        超级背包["书铁背包"]["重置"](超级背包["书铁背包"])
        超级背包["书铁背包"]["置可见"](超级背包["书铁背包"],超级背包["书铁背包"]== 超级背包["书铁背包"], not 超级背包["书铁背包"])   
        超级背包["宝石背包"]["置可见"](超级背包["宝石背包"],超级背包["宝石背包"], false)
        超级背包["装备背包"]["置可见"](超级背包["装备背包"],超级背包["装备背包"], false)
        超级背包["材料背包"]["置可见"](超级背包["材料背包"],超级背包["材料背包"], false)
        超级背包["杂货背包"]["置可见"](超级背包["杂货背包"],超级背包["杂货背包"], false)
               
    elseif    v.name == "杂货背包1"  then 
        超级背包.当前页面="杂货背包"
        超级背包["杂货背包"]["重置"](超级背包["杂货背包"])
        超级背包["杂货背包"]["置可见"](超级背包["杂货背包"],超级背包["杂货背包"]== 超级背包["杂货背包"], not 超级背包["杂货背包"])   
        超级背包["宝石背包"]["置可见"](超级背包["宝石背包"],超级背包["宝石背包"], false)
        超级背包["装备背包"]["置可见"](超级背包["装备背包"],超级背包["装备背包"], false)
        超级背包["材料背包"]["置可见"](超级背包["材料背包"],超级背包["材料背包"], false)
        超级背包["书铁背包"]["置可见"](超级背包["书铁背包"],超级背包["书铁背包"], false)   
     end 
  end
end



for i, v in ipairs({
    {
      name = "整理背包",
      x = 25,
      y = 42+425,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(85, 22),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(85, 22),
      font = "整理背包"
    },
    {
      name = "清空背包",
      x = 125,
      y = 42+425,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(85, 22),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(85, 22),
      font = "清空背包"
    },
    {
      name = "一键回收",
      x = 225,
      y = 42+425,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(85, 22),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(85, 22),
      font = "一键回收"
    },
    {
      name = "一键存仓",
      x = 325,
      y = 42+425,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(85, 22),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(85, 22),
      font = "一键存仓"
    },
    {
      name = "自动存仓",
      x = 425,
      y = 42+425,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(85, 22),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(85, 22),
      font = "自动存仓"
    },
    
  }) do
    local 临时函数 = 超级背包["创建我的单选按钮"](超级背包, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
   function  临时函数:左键弹起(x, y)
      if v.name == "整理背包"  then 
        发送数据(190,{序号1=3,类型=超级背包.当前页面})
      elseif    v.name == "清空背包"  then 
        发送数据(190,{序号1=4,类型=超级背包.当前页面})
      elseif    v.name == "一键回收"  then 
        发送数据(190,{序号1=5,类型=超级背包.当前页面})
      elseif    v.name == "一键存仓"  then 
        发送数据(190,{序号1=6,类型=超级背包.当前页面})
      elseif    v.name == "自动存仓"  then 
        发送数据(190,{序号1=7,类型=超级背包.当前页面})
      end 
    end
  end