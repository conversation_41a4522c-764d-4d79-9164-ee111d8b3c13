local 每日活动 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "每日活动", 138 + abbr.py.x, 17 + abbr.py.y, 695, 496)
function 每日活动:初始化()
  local nsf = require("SDL.图像")(695, 496)
  if nsf["渲染开始"](nsf) then
    置窗口背景("每日活动", 0, 12, 686, 485, true)["显示"](置窗口背景("每日活动", 0, 12, 686, 485, true), 0, 0)
    取白色背景(0, 0, 650, 286, true)["显示"](取白色背景(0, 0, 650, 286, true), 24, 106)
    __res:getPNGCC(4, 540, 51, 92, 89):显示(23, 395)
    __res:getPNGCC(4, 540+92, 51, 467-92, 57):显示(23+92, 395)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "10")["显示"](字体18["取图像"](字体18, "10"), 167, 457)
    字体18["取图像"](字体18, "25")["显示"](字体18["取图像"](字体18, "25"), 264, 457)
    字体18["取图像"](字体18, "40")["显示"](字体18["取图像"](字体18, "40"), 362, 457)
    字体18["取图像"](字体18, "60")["显示"](字体18["取图像"](字体18, "60"), 460, 457)
    nsf["渲染结束"](nsf)
  end
  -- self.活动中=__res:getPNGCC(4, 696, 128, 55, 50)
  -- self.新手推荐=__res:getPNGCC(4, 635, 128, 59, 56)
  self:置精灵(nsf["到精灵"](nsf))
end
function 每日活动:打开(data)
  -- table.print(self.数据)
  if data.每日 then
    self:置可见(true)
    self.数据 = data
    self.活跃=data.每日.活跃度.当前
    for i, v in ipairs({
      {name = "活跃10", xz = 10},
      {name = "活跃25", xz = 25},
      {name = "活跃40", xz = 40},
      {name = "活跃60", xz = 60}
    }) do
      self[v.name]["置禁止"](self[v.name], self.活跃 <= v.xz)
    end
    self.节日活动["置选中"](self.节日活动, true)
    self.节日活动["左键弹起"](self.节日活动)
  end
end
local 关闭 = 每日活动["创建我的按钮"](每日活动, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 645, 0)
function 关闭:左键弹起(x, y, msg)
  每日活动["置可见"](每日活动, false)
end
local 每日网格 = 每日活动["创建网格"](每日活动, "每日网格", 38, 117, 620, 256)
function 每日网格:置网格(data, name)
  self:创建格子(298, 72, 10, 22, math.ceil(#data / 2), 2, math.ceil(#data / 2) > 3)
  for i = 1, #self.子控件 do
    if data[i] then
      local nsf = require("SDL.图像")(312, 72)
      
      
      
      if nsf["渲染开始"](nsf) then
        取灰色背景(0, 0, 298, 72, true)["显示"](取灰色背景(0, 0, 298, 72, true), 0, 0)
        __res:getPNGCC(3, 132, 506, 55, 55):拉伸(50, 50):显示(10+5, 6)
        __res:getPNGCC(4, 549, 149, 23, 23)["显示"](__res:getPNGCC(4, 549, 149, 23, 23), 77, 39)
        for i = 1, data[i]["星级"] do
          __res:getPNGCC(4, 576, 149, 14, 15)["显示"](__res:getPNGCC(4, 576, 149, 14, 15), 10 + (i - 1) * 16, 58)
        end
        -- if data[i].活动开关 then
        --   每日活动.活动中:显示(0,0)
        -- end
        -- if data[i].名称=="初出江湖" or data[i].名称=="钟馗捉鬼" or data[i].名称=="师门任务" or data[i].名称=="投放怪" or data[i].名称=="乌鸡国" then
        --   每日活动.新手推荐:显示(0,-1)
        -- end
        
        字体18["置颜色"](字体18, __取颜色("白色"))
        字体18["取图像"](字体18, data[i]["名称"])["显示"](字体18["取图像"](字体18, data[i]["名称"]), 74, 10)
        字体18["取图像"](字体18, "x" .. data[i]["活跃"])["显示"](字体18["取图像"](字体18, "x" .. data[i]["活跃"]), 105, 45)
        字体18["置颜色"](字体18, __取颜色("绿色"))
        local lssj = data[i].当前次数 or 0
        字体18["取图像"](字体18, lssj .. "/" .. data[i]["最大次数"])["显示"](字体18["取图像"](字体18, lssj .. "/" .. data[i]["最大次数"]), 239-20, 7)
        nsf["渲染结束"](nsf)
      end
      local 按钮 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", data[i]["地址"]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", data[i]["地址"])), 40, 40), "按钮" .. i, 15+5, 15)
      function  按钮:左键弹起(x, y)
        __UI弹出["技能详情"]["置可见"](__UI弹出["技能详情"], true, true)
        __UI弹出["技能详情"]["技能文本"]["清空"](__UI弹出["技能详情"]["技能文本"])
        __UI弹出["技能详情"]["打开"](__UI弹出["技能详情"], nil, 0, 120, 240, 300)
        __每日提示(__UI弹出["技能详情"]["技能文本"], data[i]["名称"],data[i].活动时间,data[i].活动NPC,data[i].等级限制,data[i].星级)
      end
      
      local 参加按钮 = self.子控件[i]["创建我的按钮"](self.子控件[i],  __res:getPNGCC(3, 244, 563, 78, 34, true):拉伸(88,34), "参加按钮", 100+113-20, 20+12, "参加")
      function  参加按钮:左键弹起(x, y)
        local 事件 = function()
          发送数据(6586,{名称=data[i]["名称"]})
        end
        local wb = "我可以为少侠传送至活动NPC身旁，少侠需要进行传送操作吗？"
        __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, wb, 285, 155, 390, 200, 事件)
      
      end
      self.子控件[i]["置精灵"](self.子控件[i], nsf["到精灵"](nsf))
      按钮["置可见"](按钮, true, true)
      参加按钮["置可见"](参加按钮, true, true)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
for i, v in ipairs({
  {
    name = "节日活动",
    x = 23,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 152, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 152, 43),
    font = "节日活动"
  },
  {
    name = "日常任务",
    x = 187,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 152, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 152, 43),
    font = "日常任务"
  },
  {
    name = "挑战竞技",
    x = 351,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 152, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 152, 43),
    font = "挑战竞技"
  },
  {
    name = "副本任务",
    x = 515,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 152, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 152, 43),
    font = "副本任务"
  }
}) do
  local 临时函数 = 每日活动["创建我的单选按钮"](每日活动, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
  function  临时函数:左键弹起(x, y)
    local lsb = {}
    local sj=每日活动["数据"]
    for i,o in pairs(sj.指引[v.name]) do
      lsb[#lsb+1]=o
      lsb[#lsb].当前次数=0
      if sj.每日[v.name] and sj.每日[v.name][i] then
        lsb[#lsb].当前次数=sj.每日[v.name][i]
      end
      if sj.活动时间[i] then
        lsb[#lsb].活动时间=sj.活动时间[i]
        local 开启=false
        if sj.hdkg[i] then
          开启=true
        end
        lsb[#lsb].活动开关=开启
      end
    end
    -- table.print(lsb)
    每日活动["每日网格"]["置网格"](每日活动["每日网格"], lsb, v.name)
  end
end
for i, v in ipairs({
  {
    name = "活跃10",
    x = 165,
    y = 413,
    tcp = __res:getPNGCC(4, 1039, 73, 22, 27, true)
  },
  {
    name = "活跃25",
    x = 262,
    y = 413,
    tcp = __res:getPNGCC(4, 1039, 73, 22, 27, true)
  },
  {
    name = "活跃40",
    x = 360,
    y = 413,
    tcp = __res:getPNGCC(4, 1039, 73, 22, 27, true)
  },
  {
    name = "活跃60",
    x = 459,
    y = 413,
    tcp = __res:getPNGCC(4, 1039, 73, 22, 27, true)
  },
  -- {
  --   name = "签到",
  --   x = 593,
  --   y = 413,
  --   tcp = __res:getPNGCC(4, 1098, 59, 76, 51, true)
  -- }
}) do
  local 临时函数 = 每日活动["创建我的按钮"](每日活动, v.tcp, v.name, v.x, v.y)
 function  临时函数:左键弹起(x, y)
    -- if v.name == "活跃10" then
    --   发送数据(81, {
    --     ["类型"] = 1
    --   })
    -- elseif v.name == "活跃25" then
    --   发送数据(81, {
    --     ["类型"] = 2
    --   })
    -- elseif v.name == "活跃40" then
    --   发送数据(81, {
    --     ["类型"] = 3
    --   })
    -- elseif v.name == "活跃60" then
    --   发送数据(81, {
    --     ["类型"] = 4
    --   })
    -- elseif v.name == "签到" then
    --   发送数据(6305)
    -- end
  end
end
