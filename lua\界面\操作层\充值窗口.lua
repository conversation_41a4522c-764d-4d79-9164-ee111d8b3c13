

local 充值窗口 = 窗口层:创建窗口("充值窗口", 0, 0, 690, 410)
function 充值窗口:初始化()
  self:创建纹理精灵(function()
    __res:取资源动画("pic", "cdktuihuan.png","图片"):显示(-60,-70)
    __res:取资源动画("ui",0x01000158,"图像"):拉伸(100,50):显示(405,110)
    取输入背景(0, 0, 150, 24):显示(385, 163)
    取输入背景(0, 0, 150, 24):显示(385, 193)
    任务字体:置颜色( 0, 0, 0)
    任务字体:取图像("卡  号："):显示( 320, 160)
    任务字体:取图像("密  码："):显示( 320, 190)
    任务字体:取图像("注 释：请核对好购买的的卡号和密码再进行充值."):显示( 240, 330)
  end
)
  self.输入文本 = nil
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true

  
end
local CDK输入 = 充值窗口:创建文本输入("CDK输入", 390, 165, 295, 18)
function CDK输入:初始化()
  self:取光标精灵()
  self:置限制字数(20)
  self:置颜色(39, 53, 81, 255)
end


function 充值窗口:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.输入文本 = nil
  self:重置()
end



function 充值窗口:重置()
    CDK输入:置文本("")
end

local 充值 = 充值窗口:创建红色按钮("充值","充值", 60, 70,120,40,说明字体)
function 充值:左键弹起(x, y, msg)
    if  CDK输入:取文本() ~= "" and  CDK输入:取文本() ~=nil then
        请求服务(36,{领取= "确定充值",卡号 = CDK输入:取文本()})
    end
end


local 抽奖 = 充值窗口:创建红色按钮("抽奖","抽奖", 60, 135,120,40,说明字体)
function 抽奖:左键弹起(x, y, msg)
      if not _tp.战斗中 then
          
        请求服务(61,{文本="打开"})
      end

end
local 查看累充 = 充值窗口:创建红色按钮("查看累充","查看累充", 60, 200,120,40,说明字体)
function 查看累充:左键弹起(x, y, msg)
        请求服务(36,{领取= "打开"})
        充值窗口:置可见(false)
end
local 关闭 = 充值窗口:创建按钮("关闭",644,2)
function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/jmtb", 0x81DD40D3))
end

function 关闭:左键弹起(x, y)
  充值窗口:置可见(false)
end

