-- <AUTHOR> GGELUA
-- @Last Modified by    : baidwwy
-- @Date                : 2024-09-04 16:18:56
-- @Last Modified time  : 2024-10-16 20:03:22

--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-31 22:57:27
Last Modified by: GGELUA
Last Modified time: 2023-02-12 21:10:17
--]]
local 右下角 = __UI界面.界面层:创建控件("右下角",360+ios,390,705,165)
function 右下角:初始化()
end
function 右下角:重置界面()
  self:置可见(true, true)
  self.快捷技能控件:置可见(__res.配置["快捷技能"]==1)
  self.临时道具网格:置可见(__主控.临时背包闪烁)
end
-- local 隐藏 = 右下角:创建我的按钮(__res:getPNGCC(2, 1122, 419, 49, 48), "隐藏", 576-40, 70+20)
-- function 隐藏:左键弹起(x, y, msg)
--   if 右下角.功能栏.是否可见 then
--     右下角.功能栏:置可见(false)
--   else
--     右下角.功能栏:置可见(true)
--   end
-- end





local 功能栏 = __UI界面.界面层.右下角:创建控件("功能栏", 0, 66, 705, 105)
function 功能栏:初始化()
end


local 背景 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(9, 7, 520, 94), "背景", 90+ios, 1-10)
function 背景:左键弹起(x, y, msg)
end
local 系统 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(719, 5, 48, 54), "系统", 6+50+61+40*11+ios, 5+30-10)
function 系统:左键弹起(x, y, msg)
  __UI界面.窗口层.系统设置:打开()
end
local 攻击 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(811, 5, 48, 54), "攻击", 10+50+61*1-40+ios, 5+30-10)
function 攻击:左键弹起(x, y, msg)
  __UI界面.界面层:重置("攻击")
end
local 给予 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(857, 5, 48, 54), "给予", 10+50+61+40*1+ios, 5+30-10)
function 给予:左键弹起(x, y, msg)
  __UI界面.界面层:重置("给予")
end

local 交易 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(995, 5, 48, 54), "交易", 10+50+61+40*2+ios, 5+30-10)
function 交易:左键弹起(x, y, msg)
  __UI界面.界面层:重置("给予")
end

local 好友 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(1133, 5, 48, 42), "好友", 9+50+61+40*8+ios, 15+30-8)
function 好友:左键弹起(x, y, msg)
  if __UI界面.窗口层.好友.是否可见 then
    __UI界面.窗口层.好友.关闭:左键弹起()
  else
    发送数据(6956)
  end
end

function 好友:显示(x, y)
  if self.消息提醒 then
    __主控.消息提醒:显示(x+20, y)
  end
end
local 动作 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(766, 5, 48, 54), "动作", 7+50+61+40*10+ios, 4+30-10)
function 动作:左键弹起(x, y, msg)
end
local 帮派 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(949, 5, 48, 54), "帮派", 10+50+61+40*6+ios, 5+30-10)
function 帮派:左键弹起(x, y, msg)
  发送数据(36)
end
-- local 绘卷 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(314, 201, 46, 50), "绘卷", 283, 3)
-- function 绘卷:左键弹起(x, y, msg)
-- end
local 成就 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(1087, 5, 48, 50), "成就", 8+50+61+40*9+ios, 15+30-10)
function 成就:左键弹起(x, y, msg)
end


local 组队 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(534, 5, 48, 56), "组队", 10+50+61+40*3+ios, 5+30-10)
function 组队:显示(x, y)
  if self.消息提醒 then
    __主控.消息提醒:显示(x+10, y)
  end
end
function 组队:左键弹起(x, y, msg)
  if __主显.主角.是否队长 or __主显.主角.是否组队 then
    发送数据(4001)
  else
    __UI弹出.队伍弹出:打开()
  end
end

local 宠物 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(673, 5, 48, 55), "宠物", 10+50+61+40*4+ios, 5+30-10)
function 宠物:左键弹起(x, y, msg)
    发送数据(5006) --宠物界面
end

local 任务 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(903, 5, 48, 55), "任务", 10+50+61+40*5+ios, 5+30-9)
function 任务:左键弹起(x, y, msg)
  发送数据(10)
end


local 法术 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(1041, 5, 48, 55), "法术", 10+50+61+40*7+ios, 5+30-8)
function 法术:左键弹起(x, y, msg)
  右下角.快捷技能控件:打开()
end

local 商城 = 右下角:创建我的按钮(__res:getPNGCC(11, 3, 3, 40, 40), "商城",50+61+40*11+ios, 5+30-10)

function 商城:左键弹起(x, y, msg)
  __UI界面["窗口层"]["商城"]:打开()
end


local 道具 = 功能栏:创建我的按钮(__res.UI素材[9]:复制区域(627, 5, 48, 55), "道具", 10+50+61*1+ios, 5+30-6)
function 道具:左键弹起(x, y, msg)
  if not __UI界面.窗口层.道具行囊.是否可见 then
    发送数据(3699)
  end
end
-- local 共享 = 右下角:创建我的按钮(__res.UI素材[1]:复制区域(720, 140, 52, 44), "共享", 10+50+61*6, 0+20+11)
-- -- local 共享 = 右下角:创建我的按钮(__res.UI素材[1]:复制区域(469, 203, 45, 48), "共享", 10+50+61*6, 0+20+11)
-- function 共享:左键弹起(x, y, msg)
--   if not __UI界面.窗口层.伙伴共享背包.是否可见 then
--     发送数据(6751)
--   end
-- end
local 快捷技能控件 = 右下角:创建控件("快捷技能控件", 45, 0)
function 快捷技能控件:初始化()
  -- print("快捷技能",__res.配置["快捷技能"],__res.配置["快捷技能"]==1)
  self:置可见(__res.配置["快捷技能"]==1)
end

function 快捷技能控件:打开()
  if self.是否可见 then
    self:置可见(false)
    __res.配置["快捷技能"]=0
    __res["写出文件"](__res, "configure.txt", zdtostring(__res["配置"]))
    return
  end
  self:置可见(true)
  __res.配置["快捷技能"]=1
  __res["写出文件"](__res, "configure.txt", zdtostring(__res["配置"]))
  self.技能网格:重置()
end
local 技能网格 = 快捷技能控件:创建网格("技能网格", 10, 10, 520, 55)
function 技能网格:初始化()
  --快捷技能控件:置精灵(__res:getPNGCC(2, 230, 964, 402, 51, true):拉伸(530, 75):到精灵())
  快捷技能控件:置中心(55, -35)
end
function 技能网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr then
    发送数据(13, {
      序列 = a
    })
  end
end
function 技能网格:重置()
  self:创建格子(55, 55, 0, 10, 1, 8)
  for i, v in ipairs(self.子控件) do
    if 角色信息.快捷技能[i] then
      local lssj = 取技能(角色信息.快捷技能[i].名称, 角色信息.门派)
      v:置精灵(__res:取图像(__res:取地址("shape/jn/", lssj[7])):拉伸(55, 55):到精灵())
      local 按钮 = v:创建我的按钮(__res:getPNGCC(1, 713, 283, 27, 20), "按钮" .. i, 27, 0)
      function 按钮:左键弹起(x, y)
        发送数据(92, {
          位置 = i,
          名称 = 角色信息.快捷技能[i].名称
        })
      end
      v:置可见(true, true)
    else
      local 按钮 = v:创建我的按钮(__res:getPNGCC(9, 1314, 5, 55, 55), "添加快捷技能", 0, 0)
      function 按钮:左键弹起(x, y)
        __UI弹出.快捷技能选择:打开(i)
      end
      v:置可见(true, true)
    end
  end
end
local 临时道具网格 = 右下角["创建网格"](右下角, "临时道具网格", 483-7, 57-22, 50, 50)
function 临时道具网格:初始化()
  self:创建格子(50, 50, 0, 0, 1, 1)
  self:置可见(false)
end
function 临时道具网格:可见逻辑(逻辑)
  self:置可见(逻辑)
end
function 临时道具网格:左键弹起(x, y, a, b, msg)
  if __主控.临时背包闪烁 then 
    发送数据(3749, {
      ["方式"] = "索取"
    })
  end
end
function 临时道具网格:显示(x, y)
  if __主控.临时背包闪烁 then 
    __主控.临时背包ui:更新(0.02)
    __主控.临时背包ui:显示(x+0, y+0)
  end
end