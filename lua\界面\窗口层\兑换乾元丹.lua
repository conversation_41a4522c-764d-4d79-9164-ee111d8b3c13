local 兑换乾元丹 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "兑换乾元丹", 210 + abbr.py.x, 60 + abbr.py.y, 520, 410)
function 兑换乾元丹:初始化()
  local nsf = require("SDL.图像")(520, 410)
  if nsf["渲染开始"](nsf) then
    置窗口背景("乾元丹", 0, 12, 500, 400, true)["显示"](置窗口背景("乾元丹", 0, 12, 500, 400, true), 0, 0)
    -- __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35), 72, 60)
    取灰色背景(0, 0, 465, 225, true)["显示"](取灰色背景(0, 0, 465, 225, true), 18, 141-23)
    local lssj = 取输入背景(0, 0, 138, 23)
    lssj:显示(173-34, 136)
    lssj:显示(173-34, 136+38)
    字体16["置颜色"](字体16, __取颜色("白色"))
    字体16["取图像"](字体16, "为师近来窥一法门，可贯通奇经八脉，修得上乘妙法。欲习得此法"):显示(18, 56)
    字体16["取图像"](字体16, "需等级达到一定条件，消耗经验与金钱炼化"):显示(18, 56+23)
    字体16["取图像"](字体16, "每次重置乾元丹需消耗一定的体力和活力。"):显示(18, 56+40+258)
    local xh = {"炼化所需经验","可用经验","炼化所需金钱","可用现金","存  款","储 备 金","炼化乾元丹所需等级","剩余可炼化乾元丹","当前已炼化乾元丹"}
    local 行数 = 0
    local 列数 = 1
    字体18["置颜色"](字体18, __取颜色("白色"))
    for i=1,9 do
      行数=行数+1
      字体18:取图像(xh[i]):显示(行数 * 270 - 245, 64+38 + 列数 * 38)
      if 行数 >= 2 then
        行数 = 0
        列数 = 列数 + 1
      end
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 兑换乾元丹:打开()
  self.主角=角色信息
  self:置可见(true)
  self:刷新显示()
end
function 兑换乾元丹:刷新显示()
  local nsf = require("SDL.图像")(501, 206)
  if nsf:渲染开始() then
    -- 字体18:置颜色(__取银子颜色(角色信息["银子"]))
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18:取图像(self:兑换消耗().经验):置混合(0):显示(151,0)
    字体18:置颜色(__取银子颜色(self:兑换消耗().金钱))
    字体18:取图像(self:兑换消耗().金钱):置混合(0):显示(151,38)
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18:取图像(角色信息.存银):置混合(0):显示(151,38*2)
    字体18:取图像("69"):置混合(0):显示(151+51,38*3)
    字体18:取图像(self.主角.QYD.已换乾元丹+self.主角.QYD.额外乾元丹):置混合(0):显示(151+51,38*4)
    字体18:取图像(self.主角.当前经验):置混合(0):显示(145+230,38*0)
    字体18:取图像(角色信息["银子"]):置混合(0):显示(145+230,38*1)
    字体18:取图像(角色信息["储备"]):置混合(0):显示(145+230,38*2)
    字体18:取图像(self.主角.QYD.已换乾元丹+self.主角.QYD.额外乾元丹):置混合(0):显示(145+230+72,38*3)
    nsf:渲染结束()
  end
  -- print(123123)
  self.图像 = nsf:到精灵()
  self.图像:置中心(0, -139)
end

local 兑换按钮 = 兑换乾元丹["创建我的按钮"](兑换乾元丹, __res:getPNGCC(1, 401, 65, 175, 43, true)["拉伸"](__res:getPNGCC(1, 401, 65, 175, 43, true), 125, 43), "兑换按钮", 341, 357, "炼化乾元丹")
function 兑换按钮:左键弹起(x, y, msg)
  if 兑换乾元丹.主角.QYD.可换乾元丹>0 then
    发送数据(34)
  else
    __UI弹出.提示框:打开("#Y/当前等级可兑换的乾元丹已达上限！")
    return
  end
end

function 兑换乾元丹:兑换消耗()
	local dj = self.主角.QYD.已换乾元丹 +1
	local fhz = {}
	if dj == 1 then
		fhz.经验 = 22340000
		fhz.金钱 = 4470000
	elseif dj == 2 then
		fhz.经验 = 27850000
		fhz.金钱 = 5570000
	elseif dj == 3 then
		fhz.经验 = 34350000
		fhz.金钱 = 6910000
	elseif dj == 4 then
		fhz.经验 = 42520000
		fhz.金钱 = 8500000
	elseif dj == 5 then
		fhz.经验 = 51920000
		fhz.金钱 = 10380000
	elseif dj == 6 then
		fhz.经验 = 62850000
		fhz.金钱 = 12570000
	elseif dj == 7 then
		fhz.经验 = 75420000
		fhz.金钱 = 15080000
	elseif dj == 8 then
		fhz.经验 = 75980000
		fhz.金钱 = 15200000
	elseif dj == 9 then
		fhz.经验 = 72200000
		fhz.金钱 = 14440000
	end
	return fhz
end



local 关闭 = 兑换乾元丹["创建我的按钮"](兑换乾元丹, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 403+40+15, 0)
function 关闭:左键弹起(x, y, msg)
  兑换乾元丹["置可见"](兑换乾元丹, false)
end
