local 帮派加入 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "帮派加入", 98 + abbr.py.x, 20 + abbr.py.y, 773, 487)
local function 取繁荣等级(zz)
	if zz<8000 then
	    return "无"
    elseif zz>=8000 then
	    return "初显"
    elseif zz>=25000 then
	    return "名门"
    elseif zz>=35000 then
	    return "鼎盛"
    elseif zz>=45000 then
	    return "泰山北斗"
    elseif zz>=50000 then
	    return "天下盟主"
	end
end
function 帮派加入:初始化()
  local nsf = require("SDL.图像")(773, 487)
  if nsf["渲染开始"](nsf) then
    置窗口背景("加入帮派", 0, 12, 766, 472, true)["显示"](置窗口背景("加入帮派", 0, 12, 766, 472, true), 0, 0)
    取白色背景(0, 0, 346, 365, true)["显示"](取白色背景(0, 0, 346, 365, true), 16, 61)
    取白色背景(0, 0, 380, 169, true)["显示"](取白色背景(0, 0, 380, 169, true), 371, 61)
    取白色背景(0, 0, 380, 187, true)["显示"](取白色背景(0, 0, 380, 187, true), 371, 239)
    __res:getPNGCC(4, 719, 281, 41, 35):显示(40-25, 60)
    __res:getPNGCC(4, 719+39, 281, 270, 35):显示(40-25+39, 60)
    __res:getPNGCC(4, 719+(351-39), 281, 39, 35):显示(40-25+39+270, 60)
    local shu=__res:getPNGCC(4,1085, 281, 3, 35)
    shu:显示(159, 60)
    shu:显示(159+100, 60)
    __res:getPNGCC(4, 719, 281, 41, 35):显示(371, 60)
    __res:getPNGCC(4, 719+39, 281, 270, 35):显示(371+39, 60)
    local 尾巴=72
    local 前段=270
    __res:getPNGCC(4, 719+(351-尾巴), 281, 尾巴, 35):显示(371+39+前段, 60)
    shu:显示(159+100+277, 60)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, "帮派")["显示"](字体18["取图像"](字体18, "帮派"), 68, 67)
    字体18["取图像"](字体18, "编号")["显示"](字体18["取图像"](字体18, "编号"), 192, 67)
    字体18["取图像"](字体18, "战力")["显示"](字体18["取图像"](字体18, "战力"), 291, 67)
    字体18["取图像"](字体18, "名称")["显示"](字体18["取图像"](字体18, "名称"), 425, 67)
    字体18["取图像"](字体18, "职务")["显示"](字体18["取图像"](字体18, "职务"), 614, 67)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 帮派加入:打开(data)
  self:置可见(true)
  self.帮派列表["重置"](self.帮派列表, data)
  self.数据 = data
end
function 帮派加入:重置(data)
  -- table.print(data)
  local nsf = require("SDL.图像")(376, 356)
  if nsf["渲染开始"](nsf) then
    -- 字体18["置颜色"](字体18, __取颜色("绿色"))
    置轮廓文字(字体18,data["现任帮主"].名称,"黑色","绿色",20+28, 50)

    置轮廓文字(字体18,"帮主","黑色","绿色",238, 50)

    if data.副帮主 then
      -- 字体18["取图像"](字体18, data["副帮主"].名称):显示(20+28, 50+30)
      置轮廓文字(字体18,data["副帮主"].名称,"黑色","绿色",20+25, 50+30)
      置轮廓文字(字体18,"副帮主","黑色","绿色",235, 50+30)
      -- 字体18["取图像"](字体18, "副帮主")["显示"](字体18["取图像"](字体18, "副帮主"), 235, 50+30)
    end
    字体16["置颜色"](字体16, __取颜色("黑色"))
    字体16["取图像"](字体16, "帮派规模："):显示(19, 190)
    字体16["取图像"](字体16, "帮费标准："):显示(19, 190+33*1)
    字体16["取图像"](字体16, "繁荣等级："):显示(19, 190+33*2)
    字体16["取图像"](字体16, "帮战情况："):显示(19, 190+33*3)
    字体16["取图像"](字体16, "帮派活动："):显示(19, 190+33*4)

    字体16["取图像"](字体16, "成员数量："):显示(19+195, 190)
    字体16["取图像"](字体16, "开设奖励："):显示(19+195, 190+33*1)
    字体16["取图像"](字体16, "修炼项目："):显示(19+195, 190+33*2)

    字体16["置颜色"](字体16, __取颜色("紫色"))
    字体16["取图像"](字体16, data.帮派规模):显示(19+79, 190)
    字体16["取图像"](字体16, "暂无"):显示(19+79, 190+33*1)
    字体16["取图像"](字体16, 取繁荣等级(data.繁荣度)):显示(19+79, 190+33*2)
    字体16["取图像"](字体16, "帮派试炼赛"):显示(19+79, 190+33*3)
    字体16["取图像"](字体16, "帮派竞赛、帮派迷宫"):显示(19+79, 190+33*4)

    字体16["取图像"](字体16, data.成员数量.当前):显示(19+195+79, 190)
    字体16["取图像"](字体16, "青龙、跑商"):显示(19+195+79, 190+33*1)
    字体16["取图像"](字体16, "修炼全开"):显示(19+195+79, 190+33*2)
    -- 字体16["取图像"](字体16, "帮派规模：")["显示"](字体16["取图像"](字体16, "帮派规模："), 19, 190)
    -- 字体16["取图像"](字体16, "成员数量：")["显示"](字体16["取图像"](字体16, "成员数量："), 204, 190)
    -- 字体16["取图像"](字体16, data["帮派等级"])["显示"](字体16["取图像"](字体16, data["帮派等级"]), 112, 190)
    -- 字体16["取图像"](字体16, data["成员数量"])["显示"](字体16["取图像"](字体16, data["成员数量"]), 295, 190)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.图像["置中心"](self.图像, -373, -62)
end
local 关闭 = 帮派加入["创建我的按钮"](帮派加入, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 723, 0)
function 关闭:左键弹起(x, y, msg)
  帮派加入["置可见"](帮派加入, false)
  帮派加入["帮派列表"]["重置"](帮派加入["帮派列表"], {})
  self.图像 = nil
  self.数据 = nil
  self.选中 = nil
end
local 帮派列表 = 帮派加入["创建列表"](帮派加入, "帮派列表", 17, 97, 343, 322)
function 帮派列表:初始化()
  self:置文字(字体20)
  self.行高度 = 50
  self.行间距 = 0
end
function 帮派列表:重置(data)
  self.清空(self)
  for _, v in ipairs(data) do
    local nsf = require("SDL.图像")(343, 50)
    if nsf["渲染开始"](nsf) then
      字体16["置颜色"](字体16, __取颜色("浅黑"))
      字体16["取图像"](字体16, v["名称"])["显示"](字体16["取图像"](字体16, v["名称"]), 44, 15)
      字体16["取图像"](字体16, v["编号"])["显示"](字体16["取图像"](字体16, v["编号"]), 188, 15)
      字体16["取图像"](字体16, v["实力"])["显示"](字体16["取图像"](字体16, v["实力"]), 283, 15)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 帮派列表:左键弹起(x, y, i, item, msg)
  帮派加入["重置"](帮派加入, 帮派加入["数据"][i])
  -- 帮派加入["帮派宗旨"]["清空"](帮派加入["帮派宗旨"])
  -- 帮派加入["帮派宗旨"]["置文本"](帮派加入["帮派宗旨"], "#K" .. 帮派加入["数据"][i]["宗旨"])
  帮派加入["选中"] = i
end
-- local 帮派宗旨 = 帮派加入["创建我的文本"](帮派加入, "帮派宗旨", 392, 286, 335, 120)
local 取消申请 = 帮派加入["创建我的按钮"](帮派加入, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "取消申请", 625-139, 435, "取消申请")
function 取消申请:左键弹起(x, y, msg)
end
local 加入 = 帮派加入["创建我的按钮"](帮派加入, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "加入", 625, 435, "申请加入")
function 加入:左键弹起(x, y, msg)
  if 帮派加入["数据"][帮派加入["选中"]] then
    发送数据(38, {
      ["编号"] = 帮派加入["数据"][帮派加入["选中"]]["编号"]
    })
  end
end
