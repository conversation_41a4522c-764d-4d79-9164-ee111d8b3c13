-- 取Bgm = function(map)
--   map = 取地图id(map)
--   if 1209 == map or 6002 == map then
--     return 1208
--   elseif 6001 == map then
--     return 1131
--   elseif 1137 == map then
--     return 1135
--   elseif 1511 == map then
--     return 1231
--   elseif 1123 == map or 1124 == map then
--     return 1122
--   elseif 1211 == map then
--     return 1210
--   elseif 1227 == map then
--     return 1226
--   elseif 1119 == map or 1120 == map or 1121 == map or 1532 == map then
--     return 1119
--   elseif 1178 == map or 1179 == map or 1180 == map or 1181 == map or 1182 == map or 1183 == map then
--     return 1178
--   elseif 1187 == map or 1188 == map or 1189 == map or 1190 == map or 1191 == map or 1192 == map then
--     return 1187
--   elseif map >= 1600 and map <= 1620 then
--     return 1514
--   elseif 5001 == map then
--     return 1042
--   elseif map >= 40000000 then
--     return 1401
--   end
--   return map
-- end

function 取Bgm(map)
  map = 取地图id(map)
  if map == 1209 or map==6002 then
    return 1208
  elseif map==6001 then
    return 1131
  elseif map == 1137 then
    return 1135
  elseif map == 1511 then
    return 1231
  elseif map == 1123 or map == 1124 then
    return 1122
  elseif map == 1211 then
    return 1210
  elseif map == 1227 then
    return 1226
  elseif map == 1119 or map == 1120 or map == 1121 or map == 1532 then
    return 1118
  elseif map>=1600 and map<=1620 then
    return 1514
  elseif map>=6003 and map<=6009 then
  	return 1197
  elseif map>=6010 and map<=6019 then
  	return 1876
  elseif map == 1147 then
    return 1146
  elseif map==6020 then
  	return 1514
  elseif map == 6021 then
	return 1204
  elseif map == 6022 then
	return 1137
  elseif map == 6023 then
	return 1111
  elseif map == 6024 then
  	return 1002
  elseif map == 6025 then
  	return 1001
  elseif map == 6026 then
  	return 1211
  elseif map == 6027 then
  	return 1070
  elseif map == 6028 then
  	return 1140
  elseif map == 6029 then
  	return 1116
  elseif map == 6030 then
  	return 1202
  elseif map==5001 then
    return 1042
  elseif map== 7001 then
    return 1205
  elseif map== 7002 then
    return 1170
  elseif map== 7003 then
    return 1412
  elseif map== 7004 then
    return 1041
  elseif map== 8000 then
    return 1208
  elseif map== 8001 then
    return 1876
  elseif map== 8002 then
    return 1876
  elseif map== 10000 then
    return 1197
  elseif map== 10001 then
    return 1197
  elseif map== 10002 then
    return 1142
  elseif map== 10012 then
    return 1142
  elseif map== 1143 then
    return 1142
  elseif map== 10003 then
    return 1131
  elseif map== 10013 then
    return 1131
  elseif map== 10004 then
    return 1218
  elseif map== 10014 then
    return 1218
  elseif map== 10005 then
    return 1514
  elseif map== 10015 then
    return 1514
  elseif map== 10006 then
    return 1116
  elseif map== 10016 then
    return 1116
  elseif map== 10007 then
    return 1211
  elseif map== 10017 then
    return 1211
  elseif map== 10008 then
    return 1114
  elseif map== 10018 then
    return 1114
  elseif map== 10009 then
    return 1201
  elseif map== 1154 then
    return 1138
  elseif map== 1528 then
	return 1002
  elseif map== 1043 then
	return 1002
  elseif map== 1054 then
	return 1198
  elseif map== 1144 then
	return 1513
  elseif map== 1156 then
	return 1139
  elseif map== 1145 then
	return 1512
  elseif map== 1134 then
	return 1131
  elseif map== 1112 then
	return 1111
  elseif map== 1141 then
	return 1140
  elseif map== 1117 then
	return 1116
  elseif map== 1020 or map==1017 or map== 1022 or map== 1028 or map== 1029 or map== 1030 or map== 1044 or map== 1033  or map== 1025  or map== 1016  or map== 1024  or map== 1026 then
	return 1001
  elseif map== 1502 or map== 1503 or map== 1504 or map== 1505 or map== 1524 or map== 1525 or map== 1526 or map== 1527 or map== 1534 or map== 1537 then
	return 1501
  elseif map== 1101 or map== 1102 or map== 1100 or map== 1095 or map== 1099 or map== 1105 or map== 1093 then
	return 1092
  end
  return map
end




function 取地图名称(id)
	if id==nil then
	    return "未知"
	end
	if id>=1600 and id <=1620 then
       return "迷宫"..(id-1600).."层"
    elseif id==5001 then
    	return "宝藏山"
    elseif id>=6010 and id<=6019 and id~=6011 then
    	return "帮派竞赛"
   elseif id==6011 then
    	return "帮派奖励"
   	elseif id==6020 then
   		return "中立区"
	end
	if id == 1501 then
		return "建邺城"
	elseif id == 1502 then
		return "建邺兵铁铺"
	elseif id == 1503 then
		return "李记布庄"
	elseif id == 1504 then
		return "回春堂分店"
	elseif id == 1505 then
		return "东升货栈"
	elseif id == 1523 then
		return "合生记"
	elseif id == 1524 then
		return "万宇钱庄"
	elseif id == 1526 or id == 1525 or id == 1527 then
		return "建邺民居"
	elseif id == 1534 then
		return "民居内室"
	elseif id == 1537 then
		return "建邺衙门"
	elseif id == 1506 then
		return "东海湾"
	elseif id == 1116 then
		return "龙宫"
	elseif id == 1126 then
		return "东海岩洞"
	elseif id == 1507 then
		return "东海海底"
	elseif id == 1508 then
		return "沉船"
	elseif id == 1509 then
		return "沉船内室"
	elseif id == 1193  then
		return "江南野外"
  elseif id == 1621  then
    return "会员地图"
  elseif id == 1622  then
    return "轮回归墟"
	elseif id == 1001 or id == 4001 then
		return "长安城"
	elseif id == 1002 then
		return "化生寺"
	elseif id == 1198 then
		return "大唐官府"
	elseif id == 1054 then
		return "程咬金府"
	elseif id == 1004 then
		return "大雁塔一层"
	elseif id == 1005 then
		return "大雁塔二层"
	elseif id == 1006 then
		return "大雁塔三层"
	elseif id == 1007 then
		return "大雁塔四层"
	elseif id == 1008 then
		return "大雁塔五层"
	elseif id == 1090 then
		return "大雁塔六层"
	elseif id == 1009 then
		return "大雁塔七层"
	elseif id == 1028 then
		return "长安酒店一楼"
	elseif id == 1029 then
		return "长安酒店二楼"
	elseif id == 1020 then
		return "万胜武器店"
	elseif id == 1017 then
		return "锦绣饰品店"
	elseif id == 1022 then
		return "张记布庄"
	elseif id == 1030 then
		return "云来酒店"
	elseif id == 1043 then
		return "藏经阁"
	elseif id == 1528 then
		return "光华殿"
	elseif id == 1110 then
		return "大唐国境"
	elseif id == 1104 then
		return "傲来药店"
	elseif id == 1105 then
		return "傲来杂货"
	elseif id == 1150 then
		return "凌波城"
	elseif id == 1116 then
		return "龙宫"
	elseif id == 1117 then
		return "水晶宫"
	elseif id == 1122 then
		return "阴曹地府"
	elseif id == 1140 then
		return "普陀山"
	elseif id == 1092 then
		return "傲来国"
	elseif id == 1101 then
		return "傲来武器店"
	elseif id == 1100 then
		return "傲来圣殿"
	elseif id == 1514 then
		return "花果山"
	elseif id == 1174 then
		return "北俱芦洲"
	elseif id == 1091 then
		return "长寿郊外"
	elseif id == 1177 then
		return "龙窟一层"
	elseif id == 1178 then
		return "龙窟二层"
	elseif id == 1179 then
		return "龙窟三层"
	elseif id == 1180 then
		return "龙窟四层"
	elseif id == 1181 then
		return "龙窟五层"
	elseif id == 1182 then
		return "龙窟六层"
	elseif id == 1183 then
		return "龙窟七层"
	elseif id == 1186 then
		return "凤巢一层"
	elseif id == 1187 then
		return "凤巢二层"
	elseif id == 1188 then
		return "凤巢三层"
	elseif id == 1189 then
		return "凤巢四层"
	elseif id == 1190 then
		return "凤巢五层"
	elseif id == 1191 then
		return "凤巢六层"
	elseif id == 1192 then
		return "凤巢七层"
	elseif id == 1142 then
		return "女儿村"
	elseif id == 1143 then
		return "女儿村村长家"
	elseif id == 1127 then
		return "地狱迷宫一层"
	elseif id == 1128 then
		return "地狱迷宫二层"
	elseif id == 1129 then
		return "地狱迷宫三层"
	elseif id == 1130 then
		return "地狱迷宫四层"
	elseif id == 1118 then
		return "海底迷宫一层"
	elseif id == 1119 then
		return "海底迷宫二层"
	elseif id == 1120 then
		return "海底迷宫三层"
	elseif id == 1121 then
		return "海底迷宫四层"
	elseif id == 1532 then
		return "海底迷宫五层"
	elseif id == 1111 then
		return "天宫"
	elseif id == 1070 then
		return "长寿村"
	elseif id == 1081 then
		return "长寿村钱庄"
	elseif id == 1084 then
		return "长寿村辛匠"
	elseif id == 1099 then
		return "傲来钱庄"
	elseif id == 1135 then
		return "方寸山"
	elseif id == 1137 then
		return "灵台宫"
	elseif id == 1223 then
		return "观星台"
	elseif id == 1173 then
		return "大唐境外"
	elseif id == 1153 then
		return "金山寺"
	elseif id == 1168 then
		return "江州府"
	elseif id == 1123 then
		return "森罗殿"
	elseif id == 1124 then
		return "地藏王府"
	elseif id == 1112 then
		return "凌霄宝殿"
	elseif id == 1512 then
		return "魔王寨"
	elseif id == 1145 then
		return "魔王居"
	elseif id == 1141 then
		return "潮音洞"
	elseif id == 1146 then
		return "五庄观"
	elseif id == 1147 then
		return "乾坤殿"
	elseif id == 1131 then
		return "狮驼岭"
	elseif id == 1132 then
		return "大象洞"
	elseif id == 1133 then
		return "老雕洞"
	elseif id == 1134 then
		return "狮王洞"
	elseif id == 1202 then
		return "无名鬼城"
	elseif id == 1201 then
		return "女娲神迹"
	elseif id == 1138 then
		return "神木林"
	elseif id == 1139 then
		return "无底洞"
	elseif id == 1513 then
		return "盘丝岭"
	elseif id == 1144 then
		return "盘丝洞"
	elseif id == 1205 then
		return "战神山"
	elseif id == 1154 then
		return "神木屋"
	elseif id == 1228 then
		return "碗子山"
	elseif id == 1156 then
		return "琉璃殿"
	elseif id == 1103 then
		return "花果山"
	elseif id == 1040 then
		return "西梁女国"
	elseif id == 1208 then
		return "朱紫国"
	elseif id == 1209 then
		return "朱紫国皇宫"
	elseif id == 1226 then
		return "宝象国"
	elseif id == 1227 then
		return "宝象国皇宫"
	elseif id == 1235 then
		return "丝绸之路"
	elseif id == 1042 then
		return "解阳山"
	elseif id == 1041 then
		return "子母河底"
	elseif id == 1210 then
		return "麒麟山"
	elseif id == 1211 then
		return "太岁府"
	elseif id == 1242 then
		return "须弥东界"
	elseif id == 1232 then
		return "比丘国"
	elseif id == 1207 then
		return "蓬莱仙岛"
	elseif id == 1229 then
		return "波月洞"
	elseif id == 1233 then
		return "柳林坡"
	elseif id == 1114 then
		return "月宫"
	elseif id == 1231 then
		return "蟠桃园"
	elseif id == 1203 then
		return "小西天"
	elseif id == 1204 then
		return "小雷音寺"
	elseif id == 1218 then
		return "墨家村"
	elseif id == 1221 then
		return "墨家禁地"

	elseif id == 1920 then
		return "凌云渡"
	elseif id == 1016 then
		return "回春堂药店"
	elseif id == 1033 then
		return "留香阁"
	elseif id == 1024 then
		return "长风镖局"
	elseif id == 1026 then
		return "国子监书库"
	elseif id == 1044 then
		return "金銮殿"
	elseif id == 1400 then
		return "幻境"
	elseif id == 1511 then
		return "蟠桃园"
	elseif id == 1197 then
		return "比武场"
	elseif id == 1113 then
		return "兜率宫"
	elseif id == 1095 then
		return "傲来服饰店"
	elseif id == 1093 then
		return "傲来国客栈"
	elseif id == 1083 then
		return "长寿村服装店"
	elseif id == 1085 then
		return "长寿村武器店"
	elseif id == 1082 then
		return "长寿村神庙"
	elseif id == 1003 then
		return "桃源村"
	elseif id == 1013 then
		return "广源钱庄"
	elseif id == 1019 then--长安-书香斋
		return "书香斋"
	elseif id == 1025 then--长安-铁铺
		return "冯记铁铺"
	elseif id == 1249 then
		return "女魃墓"
	elseif id == 1216 then
		return "仙缘洞府"
	elseif id == 1250 then
		return "天机城"
	elseif id == 16050 then
		return "天鸣洞天"
	elseif id == 1125 then
		return "轮回司"
	elseif id == 1237 then
		return "四方城"
	elseif id == 1876 then
		return "南岭山"
	--============
	elseif id == 1212 then
		return "琅嬛福地"
	elseif id == 1213 then
		return "炼焰谷地"
	elseif id == 1214 then
		return "冰风秘境"
	elseif id == 1215 then
		return "钟乳石窟"
	elseif id == 1810 then
		return "一级帮派金库_门左"
	elseif id == 1811 then
		return "一级帮派金库_门右"
	elseif id == 1812 then
		return "二、三级帮派金库_门左"
	elseif id == 1813 then
		return "二、三级帮派金库_门右"
	elseif id == 1814 then
		return "帮派金库"
	elseif id == 1815 then
		return "帮派金库"
	elseif id == 1820 then
		return "一级帮派书院_门左"
	elseif id == 1821 then
		return "一级帮派书院_门右"
	elseif id == 1822 then
		return "二、三级帮派书院_门左"
	elseif id == 1823 then
		return "二、三级帮派书院_门右"
	elseif id == 1824 then
		return "帮派书院"
	elseif id == 1825 then
		return "帮派书院"
	elseif id == 1830 then
		return "一级帮派兽室_门左"
	elseif id == 1831 then
		return "一级帮派兽室_门右"
	elseif id == 1832 then
		return "二、三级帮派兽室_门左"
	elseif id == 1833 then
		return "二、三级帮派兽室_门右"
	elseif id == 1834 then
		return "帮派兽室"
	elseif id == 1835 then
		return "帮派兽室"
	elseif id == 1840 then
		return "一级帮派厢房_门左"
	elseif id == 1841 then
		return "一级帮派厢房_门右"
	elseif id == 1842 then
		return "二、三级帮派厢房_门左"
	elseif id == 1843 then
		return "二、三级帮派厢房_门右"
	elseif id == 1844 then
		return "帮派厢房"
	elseif id == 1845 then
		return "帮派厢房_"
	elseif id == 1850 then
		return "一级帮派药房_门左"
	elseif id == 1851 then
		return "一级帮派药房_门右"
	elseif id == 1852 then
		return "二、三级帮派药房_门左"
	elseif id == 1853 then
		return "二、三级帮派药房_门右"
	elseif id == 1854 then
		return "帮派药房"
	elseif id == 1855 then
		return "帮派药房"
	elseif id == 1860 then
		return "一级帮派青龙堂_门左"
	elseif id == 1861 then
		return "一级帮派青龙堂_门右"
	elseif id == 1862 then
		return "二、三级帮派青龙堂_门左"
	elseif id == 1863 then
		return "二、三级帮派青龙堂_门右"
	elseif id == 1864 then
		return "帮派青龙堂"
	elseif id == 1865 then
		return "帮派青龙堂"
	elseif id == 1870 then
		return "一级帮派聚义堂_门左"
	elseif id == 1871 then
		return "一级帮派聚义堂_门右"
	elseif id == 1872 then
		return "二、三级帮派聚义堂_门左"
	elseif id == 1873 then
		return "二、三级帮派聚义堂_门右"
	elseif id == 1874 then
		return "帮派聚义堂"
	elseif id == 1875 then
		return "帮派聚义堂"
    elseif id == 6001 then
	  return "废弃的御花园"
	elseif id == 6002 then
	  return "乌鸡国皇宫"
	elseif id == 6003 then
	  return "单人竞技赛"
	elseif id == 6004 then
	  return "多人竞技赛"
	elseif id == 6005 then
	  return "神威组"
	elseif id == 6006 then
	  return "天科组"
	elseif id == 6007 then
	  return "天启组"
	elseif id == 6008 then
	  return "天元组"
	elseif id == 6009 then
	  return "首席争霸"
	elseif id == 1125 then
		return "轮回司"
	elseif id == 6021 then
	  return "三清道观"
	elseif id == 6022 then
	  return "道观大殿"
	elseif id == 6023 then
	  return "九霄云外"
	elseif id == 6024 then
	  return "水陆道场"
	elseif id == 6025 then
	  return "繁华京城"
	elseif id == 6026 then
	  return "妖魔巢穴"
	elseif id == 6027 then
	  return "陈家庄"
	elseif id == 6028 then
	  return "普陀山"
	elseif id == 6029 then
	  return "通天河底"
	elseif id == 6030 then
	  return "灵感之腹"


	-- elseif id == 1306 then
	--   return "园林水榭"
	-- elseif id == 1446 then
	--   return "农家小院"
	-- elseif id == 1380 then
	--   return "欢乐童年"
	-- elseif id == 1382 then
	--   return "欢乐冬日"
 --   elseif id == 1892 then
	--   return "高级牧场"


	elseif id == 6036 then
		return "花果山前"
	elseif id == 6037 then
		return "地府"
	elseif id == 6038 then
		return "天庭"
	elseif id == 6039 then
		return "七宝玲珑塔"
	elseif id == 7001 then
		return "四方城"
	elseif id == 7002 then
		return "崔府正厅"
	elseif id == 7003 then
		return "顶级豪宅"
	elseif id == 7004 then
		return "沧浪墟"
   elseif id == 8000 then
		return "跨服争霸准备"
   elseif id == 8001 then
		return "跨服争霸单人"
   elseif id == 8002 then
		return "跨服争霸组队"
	elseif id == 10000 then
	  return "云影准备室"
    elseif id == 10001 then
	  return "虹光准备室"
    elseif id == 10002 then
	  return "云影之红境"
    elseif id == 10003 then
	  return "云影之橙境"
    elseif id == 10004 then
	  return "云影之黄境"
    elseif id == 10005 then
	  return "云影之绿境"
    elseif id == 10006 then
	  return "云影之蓝境"
    elseif id == 10007 then
	  return "云影之靛境"
    elseif id == 10008 then
	  return "云影之紫境"
    elseif id == 10009 then
	  return "彩虹仙境"
    elseif id == 10012 then
	  return "虹光之红境"
    elseif id == 10013 then
	  return "虹光之橙境"
	elseif id == 10014 then
	  return "虹光之黄境"
	elseif id == 10015 then
	  return "虹光之绿境"
	elseif id == 10016 then
	  return "虹光之蓝境"
	elseif id == 10017 then
	  return "虹光之靛境"
	elseif id == 10018 then
	  return "虹光之紫境"
	elseif id == 5003 then
	  return "妖魔巢穴"

	 elseif id == 1340 then
	  return "初级庭院"
	elseif id == 1341 then
	  return "中级庭院"
	elseif id == 1342 then--高级庭院
	  return "高级庭院"

	elseif id == 1332 then
	  return "普通房屋"


	 elseif id == 1885 then
		return "庭院"
	elseif id == 1401 then
		return "普通民宅"
	elseif id == 1402 then
		return "高级华宅"
	elseif id == 1403 then
		return "顶级豪宅"
	elseif id == 1404 then
		return "普通民宅"--+1J地板
	elseif id == 1405 then
		return "高级华宅"--+1J地板
	elseif id == 1406 then
		return "顶级豪宅"--+1J地板
	elseif id == 1407 then
		return "普通民宅"--+2J地板
	elseif id == 1408 then
		return "高级华宅"--+2J地板
	elseif id == 1409 then
		return "顶级豪宅"--+2J地板
	elseif id == 1410 then
		return "普通民宅"--+3J地板
	elseif id == 1411 then
		return "高级华宅"--+3J地板
	elseif id == 1412 then
		return "顶级豪宅"--+3J地板
	elseif id == 1413 then
		return "海蓝系民宅"
	elseif id == 1414 then
		return "海蓝系华宅"
	elseif id == 1415 then
		return "海蓝系豪宅"
	elseif id == 1420 then
		return "普通庭院"
	elseif id == 1421 then
		return "中级庭院"
	elseif id == 1422 then
		return "高级庭院"
	elseif id == 2007 then
		return "鬼蜮深渊"
	elseif id == 2008 then
		return "九黎城"
	--============================================
	elseif id >= 100000  then
		if _tp.房屋数据.庭院ID == id  then
            if _tp.房屋数据.庭院地图==1420 then
				return "初级庭院"
		    elseif _tp.房屋数据.庭院地图 == 1421 then
		    	   return "中级庭院"
		    elseif _tp.房屋数据.庭院地图 == 1422 then
		    	   return "高级庭院"
            elseif _tp.房屋数据.庭院地图 == 1424 then
		    	   return "顶级庭院"
			elseif _tp.房屋数据.庭院地图==1306 then
				return "园林水榭"
		    elseif _tp.房屋数据.庭院地图 == 1885 then
		    	   return "农家小院"
		    elseif _tp.房屋数据.庭院地图 == 1380 then
		    	   return "欢乐童年"
		    elseif _tp.房屋数据.庭院地图 == 1382 then
		    	   return "白雪皑皑"
		   end
		elseif _tp.房屋数据.房屋ID == id then
			if _tp.房屋数据.房屋地图  == 1310 or _tp.房屋数据.房屋地图  == 1320 or _tp.房屋数据.房屋地图  == 1933 or _tp.房屋数据.房屋地图  == 1407 or _tp.房屋数据.房屋地图  == 1408 or _tp.房屋数据.房屋地图  == 1409  then --高级
				return "大理石"
			elseif _tp.房屋数据.房屋地图  == 1311 or _tp.房屋数据.房屋地图  == 1321 or _tp.房屋数据.房屋地图  == 1935  or _tp.房屋数据.房屋地图  == 1404 or _tp.房屋数据.房屋地图  == 1405 or _tp.房屋数据.房屋地图  == 1406  then
			    return "青砖"
			elseif _tp.房屋数据.房屋地图  == 1312 or _tp.房屋数据.房屋地图  == 1322 or _tp.房屋数据.房屋地图  == 1937 or _tp.房屋数据.房屋地图  == 1401 or _tp.房屋数据.房屋地图  == 1402 or _tp.房屋数据.房屋地图  == 1403  then
			    return "复古"
			elseif _tp.房屋数据.房屋地图  == 1313 or _tp.房屋数据.房屋地图  == 1323 or _tp.房屋数据.房屋地图  == 1939 or _tp.房屋数据.房屋地图  == 1410 or _tp.房屋数据.房屋地图  == 1411 or _tp.房屋数据.房屋地图  == 1412 then
			    return "红地板"
			elseif _tp.房屋数据.房屋地图  == 1314 or _tp.房屋数据.房屋地图  == 1324 or _tp.房屋数据.房屋地图  == 1941 or _tp.房屋数据.房屋地图  == 1413 or _tp.房屋数据.房屋地图  == 1414 or _tp.房屋数据.房屋地图  == 1415 then
			    return "海洋系"
			elseif _tp.房屋数据.房屋地图  == 1315 or _tp.房屋数据.房屋地图  == 1325 or _tp.房屋数据.房屋地图  == 1943  or _tp.房屋数据.房屋地图  == 1416 or _tp.房屋数据.房屋地图  == 1417 or _tp.房屋数据.房屋地图  == 1418 then
			    return "粉红兔"
			elseif _tp.房屋数据.房屋地图  == 1316 or _tp.房屋数据.房屋地图  == 1326 or _tp.房屋数据.房屋地图  == 1945  or _tp.房屋数据.房屋地图  == 1330 or _tp.房屋数据.房屋地图  == 1331 or _tp.房屋数据.房屋地图  == 1332  then
			    return "中国风"
            elseif _tp.房屋数据.房屋地图  == 1317 or _tp.房屋数据.房屋地图  == 1327 or _tp.房屋数据.房屋地图  == 1947  or _tp.房屋数据.房屋地图  == 1333 or _tp.房屋数据.房屋地图  == 1334 or _tp.房屋数据.房屋地图  == 1335  then
			    return "卡通猫"
			elseif _tp.房屋数据.房屋地图  == 1318 or _tp.房屋数据.房屋地图  == 1328 or _tp.房屋数据.房屋地图  == 1949  or _tp.房屋数据.房屋地图  == 1413 or _tp.房屋数据.房屋地图  == 1337 or _tp.房屋数据.房屋地图  == 1338  then
			    return "冰雪屋"
            elseif _tp.房屋数据.房屋地图  == 1319 or _tp.房屋数据.房屋地图  == 1329 or _tp.房屋数据.房屋地图  == 1951  or _tp.房屋数据.房屋地图  == 1953 or _tp.房屋数据.房屋地图  == 1954 or _tp.房屋数据.房屋地图  == 1955  then
			    return "蓝色永恒"
			elseif _tp.房屋数据.房屋地图  == 1958 or _tp.房屋数据.房屋地图  == 1959 or _tp.房屋数据.房屋地图  == 1960  or _tp.房屋数据.房屋地图  == 1961 or _tp.房屋数据.房屋地图  == 1962 or _tp.房屋数据.房屋地图  == 1963  then
			    return "星空蓝"
			elseif _tp.房屋数据.房屋地图  == 1970 or _tp.房屋数据.房屋地图  == 1969 or _tp.房屋数据.房屋地图  == 1968  or _tp.房屋数据.房屋地图  == 1967 or _tp.房屋数据.房屋地图  == 1966 or _tp.房屋数据.房屋地图  == 1965  then
			    return "咖啡屋"

			end
		elseif _tp.房屋数据.阁楼ID == id then
			   if _tp.房屋数据.阁楼地图  == 1934  then --高级
					return "复古大理石"
				elseif _tp.房屋数据.阁楼地图  == 1936 then
				    return "复古青砖"
				elseif _tp.房屋数据.阁楼地图  == 1938 then
				    return "复古地板"
				elseif _tp.房屋数据.阁楼地图  == 1940 then
				    return "复古红地板"
				elseif _tp.房屋数据.阁楼地图  == 1942 then
				    return "海洋系"
				elseif _tp.房屋数据.阁楼地图  == 1944 then
				    return "粉红兔"
				elseif _tp.房屋数据.阁楼地图  == 1946 then
				    return "中国风"
	            elseif _tp.房屋数据.阁楼地图  == 1948 then
				    return "卡通猫"
				elseif _tp.房屋数据.阁楼地图  == 1950 then
				    return "冰雪屋"
	            elseif _tp.房屋数据.阁楼地图  == 1952 then
				    return "蓝色永恒"
                elseif _tp.房屋数据.阁楼地图  == 1964 then
				    return "星空蓝"
				elseif _tp.房屋数据.阁楼地图  == 1971 then
				    return "咖啡屋"
				end
		elseif _tp.房屋数据.牧场ID == id then
			   return "高级牧场"
		end
	else
		return "未知地图"
	end
end





取地图id = function(地图)
  if  地图== 6001 then
    return 1131
  elseif 地图 == 6002 then
    return 1209
  elseif  地图>=6003 and 地图<=6009   then
    return 1197
  elseif  地图>=6010 and 地图<=6019   then
    return 1876
  elseif  地图 == 6020 then
    return 1514
  elseif  地图 == 6021 then
    return 1204
  elseif  地图 == 6022 then
    return 1137
  elseif  地图 == 6023 then
    return 1111
  elseif  地图 == 6024 then
    return 1002
  elseif  地图 == 6025 then
    return 1001
  elseif  地图 == 6026 then
    return 1211
  elseif  地图 == 6027 then
    return 1070
  elseif  地图 == 6028 then
    return 1140
  elseif  地图 == 6029 then
    return 1116
  elseif  地图 == 6030 then
    return 1202
  elseif  地图 == 6031 then
    return 1511
  elseif  地图 == 6032 then
    return 1231
  elseif  地图 == 6033 then
    return 1514
  elseif  地图 == 6034 then
    return 1007
  elseif  地图 == 6035 then
    return 1111
  elseif  地图 == 6036 then
    return 1514
  elseif  地图 == 6037 then
    return 1122
  elseif  地图 == 6038 then
    return 1111
  elseif  地图 == 6039 then
    return 1009
  elseif  地图 == 7001 then
    return 1237
  elseif  地图 == 7002 then
    return 1170
  elseif  地图 == 7003 then
    return 1412
  elseif  地图 == 7004 then
    return 1041
  elseif  地图 == 5003 then
    return 1221
  elseif  地图 == 10000 or 地图 == 10001  then
    return 1197
  elseif  地图 == 10002 or 地图 == 10012 then
    return 1142
  elseif  地图 == 10003 or 地图 == 10013 then
    return 1131
  elseif  地图 == 10004 or 地图 == 10014 then
    return 1218
  elseif  地图 == 10005 or 地图 == 10015 then
    return 1514
  elseif  地图 == 10006 or 地图 == 10016 then
    return 1116
  elseif  地图 == 10007 or 地图 == 10017 then
    return 1211
  elseif  地图 == 10008 or 地图 == 10018 then
    return 1114
  elseif  地图 == 10009  then
    return 1201
  elseif  地图 == 8000  then
    return 1206
  elseif  地图 == 8001 or 地图 == 8002 then
    return 1876
  elseif 5001 == 地图 then
    return 1042
  elseif 1621 == 地图 then
    return 1193
  elseif 1622 == 地图 then
    return 1090
  elseif 地图 >= 1600 and 地图 <= 1620 then
    return 1514

  -- elseif 5002 == 地图 then
  --   return 1231
  -- elseif 5003 == 地图 then
  --   return 1221
  -- elseif 5004 == 地图 then
  --   return 1174
  -- elseif 5005 == 地图 then
  --   return 1212
  -- elseif 5006 == 地图 then
  --   return 1231
  elseif 1310 == 地图 or 1311 == 地图 or 1312 == 地图 or 1313 == 地图 or 1314 == 地图 or 1315 == 地图 or 1316 == 地图 or 1317 == 地图 or 1318 == 地图 or 1319 == 地图 then
    return 1310
  elseif 1320 == 地图 or 1321 == 地图 or 1322 == 地图 or 1323 == 地图 or 1324 == 地图 or 1325 == 地图 or 1326 == 地图 or 1327 == 地图 or 1328 == 地图 or 1329 == 地图 then
    return 1320
  elseif 1933 == 地图 or 1935 == 地图 or 1937 == 地图 or 1939 == 地图 or 1941 == 地图 or 1943 == 地图 or 1945 == 地图 or 1947 == 地图 or 1949 == 地图 or 1951 == 地图 then
    return 1933
  elseif 1934 == 地图 or 1936 == 地图 or 1938 == 地图 or 1940 == 地图 or 1942 == 地图 or 1944 == 地图 or 1946 == 地图 or 1948 == 地图 or 1950 == 地图 or 1952 == 地图 then
    return 1934
  elseif 地图>100000 then
      if _tp.房屋数据.庭院ID == 地图 then
          return _tp.房屋数据.庭院地图
      elseif _tp.房屋数据.房屋ID == 地图 then
            return _tp.房屋数据.房屋地图
      elseif _tp.房屋数据.阁楼ID == 地图 then
            return _tp.房屋数据.阁楼地图
      elseif _tp.房屋数据.牧场ID == 地图 then
          return _tp.房屋数据.牧场地图
      end
  end
  return 地图
end









__小地图资源加载 = function(map)
	if map==nil then return  end
  map = 取地图id(map)
	if map == 1001 or map == 6025 then
	   return 'jszy/fwtb',0xCEBC4615
		--return 'jszy/wptb',0xCEBC4616
	elseif map == 1002 or map == 6024 then
		return 'jszy/wptb',3566134173
	elseif map == 1004 then
		return 'jszy/wptb',3038955283
	elseif map == 1005 then
		return 'jszy/wptb',3043296434
	elseif map == 1006 then
		return 'jszy/wptb',1128733891
	elseif map == 1007 then
		return 'jszy/wptb',3690484265
	elseif map == 1008 then
		return 'jszy/wptb',117994174
	elseif map == 1009 then
		return 'jszy/wptb',2866991463
	elseif map == 1040 then
		return 'jszy/wptb',4269786701
	elseif map == 1041 or map == 7004 then
		return 'jszy/wptb',2914736228
	elseif map == 1042 or map==5001 then
		return 'jszy/wptb',502306430
	elseif map == 1070 or map == 6027 then
		return 'jszy/wptb',2766803400
	elseif map == 1090 or map == 1622 then
		return 'jszy/wptb',975993013
	elseif map == 1091 then
		return 'jszy/wptb',647087968
	elseif map == 1092 then
		return 'jszy/wptb',2930683699
	elseif map == 1110 then
		return 'jszy/wptb',1209177817
	elseif map == 1111 or map == 6023 or map == 6035  or map == 6038 then
		return 'jszy/wptb',2159282308
	elseif map == 1114 or map == 10008 or map == 10018  then
		return 'jszy/wptb',0x6B7904C6--1803093190
	elseif map == 1116 or map == 6029 or map == 10006  or map == 10016 then
		return 'jszy/wptb',545106797
	elseif map == 1118 then
		return 'jszy/wptb',3579377767
	elseif map == 1119 then
		return 'jszy/wptb',2802830440
	elseif map == 1120 then
		return 'jszy/wptb',4174697543
	elseif map == 1121 then
		return 'jszy/wptb',1236030791
	elseif map == 1122 then
		return 'jszy/wptb',2600205256
	elseif map == 1127 then
		return 'jszy/wptb',1795111495
	elseif map == 1128 then
		return 'jszy/wptb',353472097
	elseif map == 1129 then
		return 'jszy/wptb',3380408013
	elseif map == 1130 then
		return 'jszy/wptb',3387504035
	elseif map == 1131  or map==6001  or map==10003  or map==10013 then
		return 'jszy/wptb',1632323059
	elseif map == 1135 then
		return 'jszy/wptb',2990212588
	elseif map == 1138 then
		return 'jszy/wptb',1157479725
	elseif map == 1139 then
		return 'jszy/wptb',2744072561
	elseif map == 1140 or map == 6028 then
		return 'jszy/wptb',3787222136
	elseif map == 1142  or map== 10002 or map== 10012  then
		return 'jszy/wptb',3394273965
	elseif map == 1146 then
		return 'jszy/wptb',206717537
	elseif map == 1150 then
		return 'jszy/wptb',1267498697
	elseif map == 1173 then
		return 'jszy/wptb',1389007272
	elseif map == 1174 then
		return 'jszy/wptb',3293723566
	elseif map == 1177 then
		return 'jszy/wptb',1051142943
	elseif map == 1178 then
		return 'jszy/wptb',4288252057
	elseif map == 1179 then
		return 'jszy/wptb',695155995
	elseif map == 1180 then
		return 'jszy/wptb',584117065
	elseif map == 1181 then
		return 'jszy/wptb',1732546758
	elseif map == 1182 then
		return 'jszy/wptb',3528941268
	elseif map == 1183 then
		return 'jszy/wptb',3940428598
	elseif map == 1186 then
		return 'jszy/wptb',1408014958
	elseif map == 1187 then
		return 'jszy/wptb',2284090720
	elseif map == 1188 then
		return 'jszy/wptb',1381541766
	elseif map == 1189 then
		return 'jszy/wptb',254963317
	elseif map == 1190 then
		return 'jszy/wptb',801213330
	elseif map == 1191 then
		return 'jszy/wptb',2500598663
	elseif map == 1192 then
		return 'jszy/wptb',2730614272
	elseif map == 1193 or map==1621 then
		return 'jszy/wptb',606476023
	elseif map == 1198 then
		return 'jszy/wptb',544232211
	elseif map == 1201  or map == 10009 then
		return 'jszy/wptb',2027355398
	elseif map == 1202 or map == 6030 then
		return 'jszy/wptb',1070238810
	elseif map == 1203 then
		return 'jszy/wptb',4040069189
	elseif map == 1204 or map == 6021 then
		return 'jszy/wptb',3355516657
	elseif map == 1205 then
		return 'jszy/wptb',3409668280
	elseif map == 1207 then
		return 'jszy/wptb',3262911017
	elseif map == 1208 then
		return 'jszy/wptb',1428468522
	elseif map == 1210 then
		return 'jszy/wptb',664467571
	elseif map == 1211 or map == 6026 or map == 10007 or map == 10017 then
		return 'jszy/wptb',923673984
	elseif map == 1218 or map == 10004 or map == 10014 then
		return 'jszy/wptb',3524163775
	elseif map == 1221 or map == 5003 then
		return 'jszy/wptb',638705710
	elseif map == 1226 then
		return 'jszy/wptb',1186890765
	elseif map == 1228 then
		return 'jszy/wptb',2308357598
	elseif map == 1229 then
		return 'jszy/wptb',2642996577
	elseif map == 1231 then
		return 'jszy/wptb',1530927343
	elseif map == 1232 then
		return 'jszy/wptb',867489325
	elseif map == 1233 then
		return 'jszy/wptb',3059022311
	elseif map == 1235 then
		return 'jszy/wptb',3029783778
	elseif map == 1242 then
		return 'jszy/wptb',1412756833
	elseif map == 1501  then
		return 'jszy/wptb',2697825210
	elseif map == 1506  then
		return 'jszy/wptb',855890337
	elseif map == 1507 then
		return 'jszy/wptb',3363079713
	elseif map == 1508 then
		return 'jszy/wptb',2421848661
	elseif map == 1511 then
		return 'jszy/wptb',3118617574
	elseif map == 1512 then
		return 'jszy/wptb',876001261
	elseif map == 1513 then
		return 'jszy/wptb',3729625326
	elseif map == 1514 or (map>=1600 and map<=1620) or map==6020 or map==6033 or map==6036 or map==10005 or map==10015 then
		return 'jszy/wptb',818673142
	elseif map == 1532 then
		return 'jszy/wptb',1797992103
	elseif map == 1920 then
		return 'jszy/wptb',964351559
	elseif map == 1126 then
		return 'jszy/wptb',0x63033A32
	elseif map == 1003 then
		return 'jszy/wptb',0x1F812A17
	elseif map == 1216 then--仙缘洞府
		return 'jszy/wptb',0x184AA512
	elseif map == 16050 then--天鸣洞天
		return 'jszy/wptb',0x8FA4C62A
	elseif map == 1237 or map == 7001 then--天鸣洞天
		return 'jszy/wptb',0xDFE8D3CD
	elseif map == 1876 or (map>=6010 and map<=6019) or map == 8001 or map == 8002 then--天鸣洞天
		return 'jszy/wptb',0x099565A8
		-----------------------------------------
    elseif map == 1446  then--天鸣洞天
		return 'jszy/ddck',0x46f99747
    elseif map == 1380  then--天鸣洞天
		return 'jszy/ddck',0x46f99747
    elseif map == 1382  then--天鸣洞天
		return 'jszy/ddck',0x1b4a19ea
    elseif map == 1885  then--天鸣洞天
		return 'jszy/ddck',0x64ebe33d
    elseif map == 1886  then--天鸣洞天
		return 'jszy/ddck',0x9888189a
    elseif map == 1887  then--天鸣洞天
		return 'jszy/ddck',0x477d2153
    elseif map == 1888  then--天鸣洞天
		return 'jszy/ddck',0xdf6d2b20
    elseif map == 1892  then--天鸣洞天
		return 'jszy/ddck',0x267812dd
    elseif map == 2008  then--九黎城
		return 'jszy/ddck',0x00000020


	end
end




取场景等级 = function(map)
  if 1506 == map then
    return 0, 9
  elseif 1507 == map then
    return 2, 12
  elseif 1508 == map then
    return 2, 12
  elseif 1126 == map then
    return 2, 12
  elseif 1193 == map or 1621==map then
    return 6, 16
  elseif 1004 == map then
    return 8, 18
  elseif 1005 == map then
    return 12, 22
  elseif 1006 == map then
    return 16, 26
  elseif 1007 == map then
    return 20, 30
  elseif 1008 == map then
    return 24, 34
  elseif 1090 == map or 1622 == map then
    return 28, 38
  elseif 1110 == map then
    return 11, 21
  elseif 1173 == map then
    return 20, 30
  elseif 1091 == map then
    return 26, 36
  elseif 1512 == map then
    return 32, 42
  elseif 1140 == map then
    return 36, 46
  elseif 1513 == map then
    return 38, 48
  elseif 1131 == map then
    return 40, 50
  elseif 1514 == map then
    return 29, 39
  elseif 1118 == map then
    return 33, 43
  elseif 1119 == map then
    return 35, 45
  elseif 1120 == map then
    return 37, 47
  elseif 1121 == map then
    return 40, 55
  elseif 1532 == map then
    return 55, 65
  elseif 1127 == map then
    return 33, 43
  elseif 1128 == map then
    return 35, 45
  elseif 1129 == map then
    return 37, 47
  elseif 1130 == map then
    return 40, 55
  elseif 1202 == map then
    return 100, 110
  elseif 1174 == map then
    return 40, 55
  elseif 1177 == map then
    return 42, 52
  elseif 1178 == map then
    return 44, 54
  elseif 1179 == map then
    return 46, 56
  elseif 1180 == map then
    return 48, 58
  elseif 1181 == map then
    return 50, 60
  elseif 1182 == map then
    return 60, 70
  elseif 1183 == map then
    return 80, 90
  elseif 1186 == map then
    return 42, 52
  elseif 1187 == map then
    return 44, 54
  elseif 1188 == map then
    return 46, 56
  elseif 1189 == map then
    return 48, 58
  elseif 1190 == map then
    return 50, 60
  elseif 1191 == map then
    return 70, 80
  elseif 1192 == map then
    return 80, 90
  elseif 1201 == map then
    return 100, 110
  elseif 1207 == map then
    return 130, 145
  elseif 1203 == map then
    return 115, 125
  elseif 1204 == map then
    return 125, 135
  elseif 1114 == map then
    return 40, 50
  elseif 1231 == map then
    return 150, 160
  elseif 1221 == map then
    return 140, 150
  elseif 1042 == map then
    return 80, 90
  elseif 1041 == map then
    return 70, 80
  elseif 1210 == map then
    return 90, 100
  elseif 1228 == map then
    return 130, 140
  elseif 1229 == map then
    return 150, 160
  elseif 1233 == map then
    return 150, 155
  elseif 1232 == map then
    return 155, 160
  elseif 1242 == map then
    return 170, 180
  elseif 1605 == map then
    return 50, 55
  elseif 1223 == map then
    return 50, 55
  elseif 1876 == map then
    return 30, 45
  elseif 1920 == map then
    return 170, 180
  end
end