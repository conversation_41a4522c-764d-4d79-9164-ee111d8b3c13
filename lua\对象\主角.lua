--[[
LastEditTime: 2024-04-18 04:32:32
--]]

local 动作 = require('对象/基类/角色动作')
local 控制 = require('对象/基类/控制')
local 状态 = require('对象/基类/状态')
local 主角 = class('主角', 动作, 控制, 状态)
local SDL = require("SDL")
function 主角:初始化(t)
    self.id=t.ID
    self.点击动画组={}
end

function 主角:更新(dt)
    self[动作]:更新(dt)
    self[控制]:更新(dt)
    self[状态]:更新(dt)
    if self:是否可移动()  then
        if  __全局自动走路开关 and __全局自动走路秒 > 10 and not self.是否移动  then
                __全局自动走路秒 = 0
                local xxQ, yyQ, mmxQ, mmyQ
                mmxQ, mmyQ = math.floor(__主显.地图.宽度), math.floor(__主显.地图.高度)
                xxQ = math.random(3, mmxQ)
                yyQ = math.random(3, mmyQ)
                local xy = require('GGE.坐标')(xxQ, yyQ)
                local route = __主显.地图:寻路(self.xy, xy)
                if self.飞行 then
                    route =__主显.地图:飞行寻路(self.xy, xy)
                end
                if #route > 0 then
                        请求服务(1001, { x = math.floor(xy.x / 20), y = math.floor(xy.y / 20), 距离 = 0 })
                        self:路径移动(route)
                end
            elseif self.点击移动 and os.clock()-self.点击移动>=0.175 and not __手机 then
                    local x,y=引擎:取鼠标坐标()
                    local xy = require("GGE.坐标")(x, y) - __主显.屏幕坐标
                        请求服务(1001, { x = math.floor(xy.x / 20), y = math.floor(xy.y / 20), 距离 = 0 })
                        self:设置路径(xy)
                        local 播放动画 = {
                            xy = xy,
                            动画=__res:取资源动画('dlzy',0x0D98AC0A,"置动画")
                        }
                    table.insert(self.点击动画组,播放动画)
                    self.点击移动=os.clock()
            end
    end

    if self.点击动画组~=nil and self.点击动画组[1]~=nil then
        for i, v in ipairs(self.点击动画组) do
            v.动画:更新(dt)
        end
    end


end

function 主角:是否可移动()
        if _tp.战斗中 then
              return false
        elseif self.是否摆摊 then
              return false
        -- elseif 角色信息.自动遇怪 then
        --       return false
        elseif 鼠标层.附加 then
              return false
        elseif 窗口层.对话栏.是否可见 then
            return false
        elseif 界面层.返回.是否可见 then
              return false
        elseif 窗口层.交易.是否可见 then
              return false
        elseif _tp.队伍数据 and #_tp.队伍数据~=0 and not self.是否队长 then
              return false
        end
        return true
end



function 主角:消息事件(t)
        self[状态]:消息事件(t)
        self[动作]:消息事件(t)
        if t.摆摊 == self then
            请求服务(3720)
            return
        end
        if not t.鼠标 then return end
        for _, v in ipairs(t.鼠标) do
            if v.type == SDL.MOUSE_UP and self:检查透明(v.x, v.y) and v.button == SDL.BUTTON_LEFT and 界面层.类型 then
                v.type = nil
                if 界面层.类型 == '组队' then
                        请求服务(4002, {
                            id = 角色信息.数字id
                        })
                end
                界面层:重置()
                鼠标层:正常形状()
            end
            local x, y = v.x, v.y
            if x < 0 then
                break
            end
            if v.button == SDL.BUTTON_LEFT then
                  if v.type == SDL.MOUSE_DOWN and self:是否可移动()  then
                      self.按下 = os.time()
                      self._定时长按 = 引擎:定时(
                              2500,
                              function()
                                  if 引擎:取鼠标状态()==SDL.BUTTON_LEFT and self:是否可移动() and self.按下 then
                                        self.按下=false
                                        self.点击移动=os.clock()
                                  end
                              end
                          )
                  elseif v.type == SDL.MOUSE_UP and self.按下 then
                            self.按下 = false
                            self.点击移动=nil
                            local xy = require("GGE.坐标")(x, y) - __主显.屏幕坐标
                            请求服务(1001, { x = math.floor(xy.x / 20), y = math.floor(xy.y / 20), 距离 = 0 })
                            self:设置路径(xy)
                            local 播放动画 = {
                                  xy = xy,
                                  动画=__res:取资源动画('dlzy',0x0D98AC0A,"置动画")
                                }
                            table.insert(self.点击动画组,播放动画)
                            窗口层.小地图.终点=nil
                            return
                  end
            elseif v.button == SDL.BUTTON_RIGHT and v.type == SDL.MOUSE_UP and  not 界面层.类型 then
                      local xy = require("GGE.坐标")(x, y) - __主显.屏幕坐标
                      local 角度 = self.xy:取角度(xy)
                      if self.变身数据 then
                          self:置方向(direction4(角度))
                      else
                          self:置方向(direction8(角度))
                      end

            end
        end






   




end





function 主角:显示(pys)
    local p = self.xy + pys

    self[状态]:显示底层(p)
    self[动作]:显示(p)
    self[状态]:显示(p)
    self[状态]:显示顶层(p)
    if self.点击动画组~=nil and self.点击动画组[1]~=nil then
        for i, v in ipairs(self.点击动画组) do
            v.动画:显示(v.xy+pys)
            if v.动画:取当前帧()==v.动画:取帧数() then
                table.remove(self.点击动画组,i)
            end
        end
    end
end


function 主角:设置路径(xy,重置)
    if not _tp.战斗中 and not self.是否摆摊 then
        -- if __全局自动走路开关 then
        --     __全局自动走路开关 = false
        --     __全局自动走路秒 = 0
        --     __UI弹出.提示框:打开('#Y自动挂机已关闭！')
        -- end
        if 重置== nil then
            __跨地图寻径={}
            __跨地图寻径开关=false
        end
        if xy.距离 then
            xy = require("GGE.坐标")(xy.x * 20, xy.y * 20)
        end
        local route = __主显.地图:寻路(self.xy, xy)
        if self.飞行 then
            self.移动速度 = 220
            route =__主显.地图:飞行寻路(self.xy, xy)
        else
            self.移动速度 = 180
        end
        if #route > 0 then
            self:路径移动(route)
            return route
        end
    end
end

function 主角:开始移动(xy)
    self[控制]:开始移动(xy)
end

return 主角
