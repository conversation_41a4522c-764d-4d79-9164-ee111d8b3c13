local 剑会天下 = 窗口层:创建窗口("剑会天下", 0, 0, 0, 0)



function 剑会天下:初始化()

  local dwzy = {0x1A23FA11,0x1A23FA12,0x1A23FA13,0x1A23FA14,0x1A23FA15,0x1A23FA16,0x1A23FA17,0x1A23FA18}
	self.段位组={}
	for n=1,8 do
	    self.段位组[n] =__res:取资源动画("dlzy", dwzy[n],"图像")
	end

   -- self:置精灵(__res:取资源动画("dlzy", 0x1A23FA01,"图像"):拉伸(572, 468):到精灵())--440 360

end


function 剑会天下:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.分类="首页"
  self.当前积分 = 1200
  self.升级积分=1800
  self.当前段位=1
  self.剑会称谓="剑会天下·新秀"
end

function 剑会天下:加载数据(内容)
	self.当前积分 = 内容.当前积分 or 1200
    if self.当前积分<1800 then
    	self.升级积分=1800
    	self.当前段位=1
    	self.剑会称谓="剑会天下·新秀"
    elseif self.当前积分<2400 then
    	self.升级积分=2400
    	self.当前段位=2
    	self.剑会称谓="剑会天下·百战"
	elseif self.当前积分<3000 then
		self.升级积分=3000
		self.当前段位=3
		self.剑会称谓="剑会天下·千胜"
	elseif self.当前积分<3600 then
		self.升级积分=3600
		self.当前段位=4
		self.剑会称谓="剑会天下·万军"
	elseif self.当前积分<4200 then
		self.升级积分=4200
		self.当前段位=5
		self.剑会称谓="剑会天下·豪杰"
	elseif self.当前积分<4800 then
		self.升级积分=4800
		self.当前段位=6
		self.剑会称谓="剑会天下·英雄"
	elseif self.当前积分<5400 then
		self.升级积分=5400
		self.当前段位=7
		self.剑会称谓="剑会天下·传说"
	else
		self.升级积分=9999
		self.当前段位=8
		self.剑会称谓="剑会天下·神话"
	end


  self:显示刷新()
end

local 模式选择 = 剑会天下:创建按钮("模式选择", 40, 52)
function 模式选择:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x1A23FA03),1)
end
function 模式选择:左键弹起(x, y)
  剑会天下.分类="模式"
  剑会天下:显示刷新()

end

local 玩法介绍 = 剑会天下:创建按钮("玩法介绍", 380, 135)
function 玩法介绍:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x1A23FA05),1)
end
function 玩法介绍:左键弹起(x, y)
  剑会天下.分类="介绍"
  剑会天下:显示刷新()
  
end
local 返回按钮 = 剑会天下:创建蓝色按钮("返回按钮", "返回按钮", 15,290, 100, 30)
function 返回按钮:左键弹起(x, y)
  剑会天下.分类="首页"
  剑会天下:显示刷新()
  
end

local 单人按钮 = 剑会天下:创建按钮("单人按钮", 260, 106)
function 单人按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x1A23FA07),1)
end
function 单人按钮:左键弹起(x, y)
  if (_tp.队伍数据 and #_tp.队伍数据 == 1) or not _tp.队伍数据 then
      请求服务(66,{人数=1,模式="单人匹配"})
  else
    __UI弹出.提示框:打开('#Y请组队单人或者不要组队进行匹配。')
  end
  
end

local 三人按钮 = 剑会天下:创建按钮("三人按钮", 370, 106)
function 三人按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x1A23FA08),1)
end
function 三人按钮:左键弹起(x, y)
  if _tp.队伍数据 and #_tp.队伍数据 == 3 then
    请求服务(66,{人数=3,模式="三人匹配"})
else
  __UI弹出.提示框:打开('#Y请组3人队伍进行匹配，不能少于3人或者超过3人')
end
  
end
local 五人按钮 = 剑会天下:创建按钮("五人按钮", 480, 106)
function 五人按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x1A23FA09),1)
end
function 五人按钮:左键弹起(x, y)
  if _tp.队伍数据 and #_tp.队伍数据 == 5 then
    请求服务(66,{人数=5,模式="五人匹配"})
else
  __UI弹出.提示框:打开('#Y请组5人队伍进行匹配，不能少于5人。')
end
  
end




function 剑会天下:显示刷新()
  self.模式选择:置可见(false)
  self.玩法介绍:置可见(false)
  self.返回按钮:置可见(false)
  self.单人按钮:置可见(false)
  self.三人按钮:置可见(false)
  self.五人按钮:置可见(false)
  if self.分类=="首页" then
        self:置宽高(630,265)
        self:置坐标(引擎.宽度2-315,引擎.高度2-130)
        self.模式选择:置可见(true)
        self.玩法介绍:置可见(true)
    else
        self:置宽高(630,330)
        self:置坐标(引擎.宽度2-315,引擎.高度2-160)
        self.返回按钮:置可见(true)
   end
   self:创建纹理精灵(function()
     if self.分类=="首页" then
          __res:取资源动画("dlzy", 0x1A23FA01,"图像"):显示(0,0)
          __res:取资源动画("dlzy", 0x1A23FA04,"图像"):显示(380,50)
      elseif self.分类=="模式" then
        self.单人按钮:置可见(true)
        self.三人按钮:置可见(true)
        self.五人按钮:置可见(true)
        __res:取资源动画("dlzy", 0x1A23FA02,"图像"):显示(0,0)
        __res:取资源动画("dlzy", 0x1A23FA06,"图像"):显示(40,40)
        for n=1,5 do
          __res:取资源动画("dlzy", 0x1A23FA06,"图像"):显示(270+(n-1)*65,40)
        end
        __res:取资源动画("dlzy", 0x1A23FA10,"图像"):显示(35,105)
        self.段位组[self.当前段位]:显示(90,133)
        local lssj = 取头像(角色信息.模型)
        __res:取资源动画(lssj[7],lssj[2],"图像"):显示(44, 44)
        if _tp.队伍数据 and _tp.队伍数据[1] then
              for n=1,5 do
                  if _tp.队伍数据[n]~=nil then
                    local lssjs = 取头像(_tp.队伍数据[n].模型)
                     __res:取资源动画(lssj[7],lssjs[2],"图像"):显示(274+(n-1)*65, 44)
                 end
              end
          end
          标题字体:置颜色(__取颜色("紫色"))
          标题字体:取图像(角色信息.名称):显示(100, 45)
          标题字体:取图像("等级:"..角色信息.等级.." 门派:"..角色信息.门派):显示(100, 75)
          标题字体:取图像("当前段位:"..self.剑会称谓):显示(146-标题字体:取宽度("当前段位:"..self.剑会称谓)//2, 115)
          if self.当前积分>=1900 then
              标题字体:取图像("剑会积分:"..self.当前积分):显示(146-标题字体:取宽度("剑会积分:"..self.当前积分)//2, 260)
          else
              标题字体:取图像("剑会积分:"..self.当前积分.."/"..self.升级积分):显示(146-标题字体:取宽度("剑会积分:"..self.当前积分.."/"..self.升级积分)//2, 260)
          end 
      elseif self.分类=="介绍" then
        __res:取资源动画("dlzy", 0x1A23FA02,"图像"):显示(0,0)
        标题字体:置颜色(__取颜色("紫色"))
        标题字体:取图像("剑会天下匹配模式分为：1V1、3V3、5V5"):显示(45, 55)
        标题字体:取图像("剑会天下段位积分分为："):显示(45, 85)
        标题字体:取图像("剑会天下·新秀：1200-1799分、剑会天下·百战：1800-2399分、"):显示(100, 110)
        标题字体:取图像("剑会天下·千胜：2400-2999分、剑会天下·万军：3000-3599分、"):显示(100, 135)
        标题字体:取图像("剑会天下·豪杰：3600-4199分、剑会天下·英雄：4200-4799分、"):显示(100, 160)
        标题字体:取图像("剑会天下·传说：4800-5399分、剑会天下·神话：5400分以上"):显示(100, 185)
        标题字体:取图像("剑会天下每日奖励分为：每日PK3场奖励（无论输赢）、每日首胜奖励。"):显示(45, 210)
        标题字体:取图像("剑会天下赛季奖励：每周清空一次，1300分以上每阶段都有赛季奖励。"):显示(45, 240)
     end
    end)
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-39, 4)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-38, 4)
    end

    




end


local 关闭 = 剑会天下:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  剑会天下:置可见(false)
end







