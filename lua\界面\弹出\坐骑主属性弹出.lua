__UI弹出["坐骑主属性弹出"] = __UI界面["创建弹出窗口"](__UI界面, "坐骑主属性弹出", 339+45 + abbr.py.x, 284-30-64-78-50 + abbr.py.y, 150, 192+64+64)
local 坐骑主属性弹出 = __UI弹出["坐骑主属性弹出"]
function 坐骑主属性弹出:初始化()
  self:置精灵(取黑色背景(0, 0, 150, 192+64+64))
end
function 坐骑主属性弹出:打开(lx)
  self.认证码=lx
  if self.是否可见 then
    self:置可见(false)
  else
    self:置可见(true)
  end
end
local lsan = {
  "体质",
  "力量",
  "魔力",
  "敏捷",
  "耐力",
}

for i = 1, #lsan do
  local 临时函数 = 坐骑主属性弹出["创建我的按钮"](坐骑主属性弹出, __res:getPNGCC(2, 368, 906, 126, 52, true), lsan[i], 12, 11 + (i - 1) * 60, lsan[i])
  function  临时函数:左键弹起(x, y, msg)
    if lsan[i] then
      发送数据(11.2,{属性=lsan[i],认证码=坐骑主属性弹出.认证码})
    end
    坐骑主属性弹出["置可见"](坐骑主属性弹出, false)
  end
end