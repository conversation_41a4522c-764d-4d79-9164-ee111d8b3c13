
local 奖励物品 = {"秘制红罗羹","光芒石","魔兽要诀","特赦令牌","秘制绿罗羹","神兜兜","召唤兽内丹","元宵","黑宝石","星辉石","高级魔兽要诀","5级宝石礼包","舍利子",
"秘制绿罗羹","特赦令牌","神兜兜","高级召唤兽内丹","秘制红罗羹","魔兽要诀","太阳石","秘制绿罗羹","元宵","高级魔兽要诀","秘制红罗羹","5级宝石礼包","神兜兜","秘制绿罗羹",
"特赦令牌","太阳石","魔兽要诀","太阳石","秘制红罗羹","光芒石","魔兽要诀","特赦令牌","秘制绿罗羹","神兜兜","召唤兽内丹","元宵","黑宝石","星辉石","高级魔兽要诀"}
local 签到界面 = 窗口层:创建窗口("签到界面")
function 签到界面:初始化()
        local tcp =__res:取资源动画("pic","qdbj.png","图片")
        self:置宽高(tcp.宽度,tcp.高度)
        self:置精灵(tcp:到精灵())
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        self.标题=文本字体:置颜色(255,255,255,255):取精灵("每日签到")
        self.关闭:置坐标(self.宽度-20, 2)
      
end
function 签到界面:更新(dt)
        self.时间显示= 文本字体:置颜色(255, 255, 255,255):取精灵(os.date("%H:%M:%S", os.time()),100,100,100,100)
end
function 签到界面:显示(x,y)
    self.标题:显示(x+280,y+2)
    if self.年月显示 then
        self.年月显示:显示(x+(self.宽度-self.年月显示.宽度-10),y+25)
    end
    if self.时间显示 then
        self.时间显示:显示(x+(self.宽度-self.时间显示.宽度-20),y+40)
    end
    


    if self.物品 then
        local xx = 0
        local yy = 0
        for i=1,42 do
            if xx>=7 then
                yy=yy+1
                xx=0
            end
            if i >= self.起始位置+0 and i<=self.当月天数+self.周几 and self.物品[i-self.周几] and self.物品[i-self.周几].小动画 then
                self.物品[i-self.周几].小动画:显示(x+10+xx*86,y+85+yy*61)
            end
            xx=xx+1
        end
    end 




end

function 签到界面:获得鼠标(x,y)
        if self.物品 and self.数据 then
            for i = 1, self.当月天数 do
                if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画:检查透明(x, y)  then
                    __UI弹出.自定义提示:打开(self.物品[i],x+20,y+20)
                end
            end
        end
end


function 签到界面:左键弹起(x,y)
  if self.物品 and self.数据 then
      for i = 1, self.当月天数 do
          if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画:检查透明(x, y)  then
                if __手机 then
                    local 事件 =function (编号)
                            if 编号==1 then
                                请求服务(47,{月份=self.数据.月份,几号=self.数据.几号})
                            else
                                 __UI弹出.自定义提示:打开(self.物品[i],x,y)
                            end
                        end
                        __UI弹出.临时按钮:打开({"签到","查看"},事件,x,y)
                else
                    请求服务(47,{月份=self.数据.月份,几号=self.数据.几号})
                end
              
          end
      end
  end
end


function 签到界面:打开(数据)
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.数据=nil
    self:刷新(数据)
end

function 签到界面:刷新(数据)
        self.数据 = 数据
        self.当月天数=tonumber(self.数据.当月天数) 
        self.周几 = os.date("%w",os.time()-(os.date("%d", os.time())-1)*86400)
        self.年月显示=文本字体:置颜色(255, 255, 255,255):取精灵(os.date("%Y", os.time()).."年"..self.数据.月份.."月"..self.数据.几号.."日",100,100,100,100)
        
        self.起始位置 = self.周几 + 1
        self.物品={}
        for i=1,self.当月天数 do
            local lssj = 取物品(奖励物品[i])
            self.物品[i]={}
            self.物品[i].小动画=self:创建纹理精灵(function()
                if self.数据[i] then
                    __res:取资源动画("pic","qdxz.png","图片"):显示(0,0)
                    __res:取资源动画("pic","qdylq.png","图片"):显示(62,37)
                    self.物品[i].备注="\n#Y已签到"
                else
                    __res:取资源动画("pic","qdwlqbj.png","图片"):显示(0,0)
                    __res:取资源动画("pic","qdwlq.png","图片"):显示(65,40)
                    self.物品[i].备注="\n#Y未签到"
                end
                __res:取资源动画(lssj[11],lssj[12],"图像"):显示(15,5)
                文本字体:置颜色(255, 255, 255,255):取描边图像(i,0,0,0,255):显示(3,2)
            end,1,85,60)
            self.物品[i].大动画 = lssj[13]
            self.物品[i].资源 = lssj[11]
            self.物品[i].名称=奖励物品[i]
            self.物品[i].介绍=lssj[1]
           
        end








end



local 关闭 = 签到界面:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
      签到界面:置可见(false)
end



  


















