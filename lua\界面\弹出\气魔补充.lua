__UI弹出["气魔补充"] = __UI界面["创建弹出窗口"](__UI界面, "气魔补充", 615 + abbr.py.x, 39, 290, 157)
local 气魔补充 = __UI弹出["气魔补充"]
function 气魔补充:初始化()
  self:置精灵(取黑色背景(0, 0, 290, 157))
end
function 气魔补充:显示(x, u)
  if self.图像 then
    self.图像["显示"](self.图像, x, u)
  end
end
function 气魔补充:打开(lx)
  self:置可见(true)
  self:重置(lx)
  self.类型 = lx
end
function 气魔补充:重置(lx)
  local nsf = require("SDL.图像")(290, 157)
  if "人物" == lx then
    if nsf["渲染开始"](nsf) then
      字体18["置颜色"](字体18, __取颜色("黄色"))
      字体18["取图像"](字体18, string.format("气血 ：%s/%s/%s", 角色信息["气血"], 角色信息["气血上限"], 角色信息["最大气血"])):置混合(0):显示(20, 78)
      字体18["取图像"](字体18, string.format("魔法 ：%s/%s", 角色信息["魔法"], 角色信息["最大魔法"])):置混合(0):显示(20, 99)
      字体18["取图像"](字体18, string.format("愤怒 ：%s/%s", 角色信息["愤怒"], 150)):置混合(0):显示(20, 120)
      nsf["渲染结束"](nsf)
    end
  elseif "召唤兽" == lx and nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18["取图像"](字体18, string.format("气血 ：%s/%s", 角色信息["参战宝宝"]["气血"], 角色信息["参战宝宝"]["最大气血"])):置混合(0):显示(20, 78)
    字体18["取图像"](字体18, string.format("魔法 ：%s/%s", 角色信息["参战宝宝"]["魔法"], 角色信息["参战宝宝"]["最大魔法"])):置混合(0):显示(20, 99)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
for i, v in ipairs({
  {
    name = "补充气血",
    x = 17,
    y = 15,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 109, 36),
    font = "补充气血"
  },
  {
    name = "补充魔法",
    x = 150,
    y = 15,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 109, 36),
    font = "补充魔法"
  }
}) do
  local 临时函数 = 气魔补充["创建我的按钮"](气魔补充, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "补充气血" then
      气魔补充["置可见"](气魔补充, false)
      if 气魔补充["类型"] == "人物" then
        发送数据(3727, {
          ["类型"] = 1
        })
      elseif 气魔补充["类型"] == "召唤兽" then
        发送数据(3727, {
          ["类型"] = 2
        })
      end
    elseif v.name == "补充魔法" then
      气魔补充["置可见"](气魔补充, false)
      if 气魔补充["类型"] == "人物" then
        发送数据(3728, {
          ["类型"] = 1
        })
      elseif 气魔补充["类型"] == "召唤兽" then
        发送数据(3727, {
          ["类型"] = 2
        })
      end
    end
  end
end
