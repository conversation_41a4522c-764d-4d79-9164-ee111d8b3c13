
local 多开创建 = __UI界面.窗口层:创建我的窗口("多开创建", 220 + abbr.py.x, 100 + abbr.py.y, 565, 345)
function 多开创建:初始化()
  self:置精灵(置窗口背景("创建角色", 0, 12, 565, 345)) 
end

function 多开创建:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.角色控件:置可见(true)
  self.门派选择:置可见(false)
end


local 模型 ={"逍遥生","剑侠客","偃无师","飞燕女","英女侠","巫蛮儿","巨魔王","虎头怪","杀破狼","狐美人","骨精灵","鬼潇潇","羽灵神","神天兵","龙太子","舞天姬","玄彩娥","桃夭夭"}
local 角色控件= 多开创建:创建我的控件("角色控件", 10, 45, 545, 295)
function 角色控件:初始化()
      local nsf = require("SDL.图像")(545, 295)
      if nsf:渲染开始() then
         -- 置窗口背景("创建伙伴", 0, 12, 525, 305, true):显示(0, 0)
         取白色背景(0, 0, 545, 295, true):显示(0, 0)
        __res:getPNG("shape/pic/rmx.png"):显示(20, 30)
          local xx = 0
          local yy = 0
          for i =1,18 do
          __res:getPNG("shape/pic/fgbj.png"):显示(70+xx*70, 30+yy*90)
            xx = xx + 1
            if xx>=6  then
              xx = 0
               yy = yy +1
            end
          end
          __res:getPNG("shape/pic/fgbj.png"):显示(70+415, 30+90)
          nsf:渲染结束()
      end
      self.图像 = nsf:到精灵()
end

local 门派选择= 多开创建:创建我的控件("门派选择", 20, 45, 545, 295)
function 门派选择:初始化()
      local nsf = require("SDL.图像")(545, 295)
      if nsf:渲染开始() then
        取白色背景(0, 0, 220, 300, true):显示(0, 0)
        取白色背景(0, 0, 300, 200, true):显示(230, 95)
        __res:getPNG("shape/pic/jsdt.png"):显示(230, 5)
        字体14:置颜色(__取颜色("黑色")):取图像("未选择"):显示(380,55)
        字体14:置颜色(__取颜色("黑色")):取图像("输入名称:"):显示(240,120)
        取输入背景(0, 0, 260, 23):显示(245,150)
        nsf:渲染结束()
      end
      self.图像 = nsf:到精灵()
end





function 门派选择:重置(角色)
   self.图像2= nil
   self.角色 = 角色
   local 模型1={模型=角色,装备={}}
   self.模型格子= __UI模型格子:创建()
   self.模型格子:清空()
   self.模型格子:置数据(模型1, "角色", 100,200)  
   self.名称输入:置文本("")
   local nsf = require("SDL.图像")(525, 295)
   if nsf:渲染开始() then
       字体14:置颜色(__取颜色("黑色")):取图像(角色):显示(380,25)
        nsf:渲染结束()
    end
  self.图像2 = nsf:到精灵()
end


local 名称输入 = 门派选择:创建输入("名称输入", 250, 153, 250, 18)
function 名称输入:初始化()
  self:取光标精灵()
  self:置限制字数(11)
  self:置颜色(39, 53, 81, 255)
end


local 重选按钮 = 门派选择:创建我的按钮(__res:getPNGCC(3, 2, 507, 124, 41,true):拉伸(74,40), "重选", 430, 220, "重选")
function 重选按钮:左键弹起(x, y)
      角色控件:置可见(true)
      门派选择:置可见(false)
end










local 创建按钮 = 门派选择:创建我的按钮(__res:getPNGCC(3, 2, 507, 124, 41,true):拉伸(74,40), "创建", 260, 220, "创建")
function 创建按钮:左键弹起(x, y)
   if 门派选择.角色 then
      if 门派选择.名称输入:取文本()~=nil and 门派选择.名称输入:取文本()~="" then
          发送数据(211,{参数=角色信息.数字id,文本="创建角色",模型=门派选择.角色,名称=门派选择.名称输入:取文本()})
          多开创建:置可见(false)
      else
        __UI弹出.提示框:打开("#Y/请输入正确的名称")
      end
   else
      __UI弹出.提示框:打开("#Y/请重新选择角色")
    end

end
local xx = 0
local yy = 0
for i = 1, 18 do
    local 临时函数名 = 角色控件:创建按钮(模型[i],75+xx*70,35+yy*90)
    function 临时函数名:初始化()
       local lssj = 取头像(模型[i])
       self:置精灵(__res:取精灵(__res:取地址("shape/mx/", lssj[2])))
    end
    function 临时函数名:左键弹起(x, y)
         角色控件:置可见(false)
         门派选择:置可见(true)
         门派选择:重置(模型[i])
    end
    xx = xx + 1
    if xx>=6  then
       xx = 0
       yy = yy +1
    end
end

local 影精灵 = 角色控件:创建按钮("影精灵",75+415,35+90)
function 影精灵:初始化()
       local lssj = 取头像("影精灵")
       self:置精灵(__res:取精灵(__res:取地址("shape/mx/", lssj[2])))
end
function 影精灵:左键弹起(x, y)
         角色控件:置可见(false)
         门派选择:置可见(true)
         门派选择:重置("影精灵")
end




local 关闭 = 多开创建:创建我的按钮(__res:getPNGCC(1, 401, 0, 46, 46), "关闭", 520, 0)
function 关闭:左键弹起(x, y, msg)
  多开创建:置可见(false)
end






