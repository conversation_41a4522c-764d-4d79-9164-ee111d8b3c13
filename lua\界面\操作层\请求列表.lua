
local 请求列表 = 窗口层:创建窗口("请求列表", 0, 0, 620, 290)
function 请求列表:初始化()
  self:置精灵(置窗口背景("请求列表", 0, 0, 620, 290))
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end

end
function 请求列表:打开(数据)
      self:置可见(true)
      self:刷新(数据)
end
function 请求列表:刷新(数据)
      self.选中 = nil
      self.队员网格:置数据(数据)
      self.图像=self:创建纹理精灵(function()
              for i = 1, 5 do
                  取输入背景(0, 0, 116, 22):显示((i-1)*121,0)
                  取输入背景(0, 0, 116, 22):显示((i-1)*121,25)
                  取输入背景(0, 0, 116, 22):显示((i-1)*121,50)
                  if 数据[i] then
                    文本字体:置颜色(39, 53, 81)
                    文本字体:取图像(数据[i].名称):显示((116 - 文本字体:取宽度(数据[i].名称)) // 2 + (i-1)*121, 4)
                    文本字体:取图像(数据[i].门派):显示((116 - 文本字体:取宽度(数据[i].门派)) // 2 + (i-1)*121, 29)
                    文本字体:取图像(数据[i].等级 .. "级"):显示((116 - 文本字体:取宽度(数据[i].等级 .. "级")) // 2 + (i-1)*121, 54)
                  end
              end
        end,1,600,80)
        if not 数据 or #数据==0 then
            界面层.玩家界面.按钮控件.组队.消息提醒= false
        end
end
function 请求列表:显示(x,y)
      if self.图像 then
        self.图像:显示(x+10,y+180)
      end
end

local 队员网格 = 请求列表:创建网格("队员网格",10, 35,600, 145)  
function 队员网格:初始化()
  self:创建格子(116, 145, 0, 5, 1, 5)
end
function 队员网格:左键弹起(x, y, a)
        if 请求列表.选中 then
            self.子控件[请求列表.选中]._spr.确定 = nil
        end
        if self.子控件[a]._spr.数据 then
            请求列表.选中 = a
            self.子控件[a]._spr.确定 = true
        end
end
function 队员网格:置数据(数据)
  for i = 1, #self.子控件 do
      local lssj = __队伍格子:创建()
      lssj:置数据(数据[i], i, "请求列表")
      self.子控件[i]:置精灵(lssj)
  end
end
function 队员网格:获得鼠标(x, y, a)
    for i, v in ipairs(self.子控件) do
          v._spr.焦点=nil
    end
    self.子控件[a]._spr.焦点=true
end
function 队员网格:失去鼠标(x, y)
    for i, v in ipairs(self.子控件) do
          v._spr.焦点=nil
    end
end

function 队员网格:右键弹起(x, y)
  请求列表:置可见(false)
end

local 按钮设置={"同意申请","拒绝申请"}
for i, v in ipairs(按钮设置) do
      local 临时按钮=请求列表:创建红色按钮(v,v,130+(i-1)*280,257,74,22) 
      function 临时按钮:左键弹起(x,y)
            if v == "同意申请" then
                if 请求列表.选中 and 队员网格.子控件[请求列表.选中]._spr.数据 then
                    请求服务(4004, {序列 = 请求列表.选中})
                end
            elseif v == "拒绝申请" then
                  if 请求列表.选中 and 队员网格.子控件[请求列表.选中]._spr.数据 then
                      请求服务(4005, {序列 = 请求列表.选中})
                  end
            end
      end
end



local 关闭 = 请求列表:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  请求列表:置可见(false)
end
