
local 商店 = 窗口层:创建窗口("商店", 0, 0, 270, 440)
function 商店:初始化()
          self:创建纹理精灵(function()
          置窗口背景("无", 0, 0, 270, 440, true):显示(0, 0)
          文本字体:置颜色(255, 255, 255)
          文本字体:取图像("单价"):显示(15, 315)
          文本字体:取图像("总额"):显示(15,345)
          文本字体:取图像("现金"):显示(15,375)
          文本字体:取图像("数量"):显示(15,405)
          取输入背景(0, 0, 205, 22):显示(50,312)
          取输入背景(0, 0, 205, 22):显示(50,342)
          取输入背景(0, 0, 205, 22):显示(50,372)
          取输入背景(0, 0, 135, 22):显示(50,402)
      end
  )



  self.标题=文本字体:置颜色(255,255,255,255):取精灵("商店")
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
  else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
  end
end


function 商店:显示(x, y)
        self.标题:显示(x+(self.宽度-self.标题.宽度) // 2, y+4)
        if self.图像 then
            self.图像:显示(x, y)
        end


end


function 商店:打开(道具组,名称)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.标题=文本字体:置颜色(255,255,255,255):取精灵("商店")
  if 名称 then
     self.标题=文本字体:置颜色(255,255,255,255):取精灵(名称)
  end
  self.图像 = nil
  self.选中物品=nil
  self.选中数量=1
  self.道具列表 ={}
  for k, v in pairs(道具组) do
    local xxx=分割文本(v,"*")
    self.道具列表[k] ={名称=xxx[1],价格=tonumber(xxx[2]),原始商品=v}
  end
  self.道具网格:置数据(self.道具列表)
  self:刷新货币()
end




function 商店:刷新货币()

  if self.选中物品 and not self.道具列表[self.选中物品] then
      self.选中物品=nil
      self.选中数量=1
  end
  self.数量输入:置数值(self.选中数量)
  self.图像 = self:创建纹理精灵(function()
    标题字体:置颜色(__取银子颜色(角色信息.银子)):取图像(角色信息.银子):显示(55, 375)
    if self.选中物品 and self.道具列表[self.选中物品] and self.道具列表[self.选中物品].价格 then
        标题字体:置颜色(__取银子颜色(self.道具列表[self.选中物品].价格)):取图像(self.道具列表[self.选中物品].价格):显示(55, 315)
        local 显示总数 = self.道具列表[self.选中物品].价格*self.选中数量
        标题字体:置颜色(__取银子颜色(显示总数)):取图像(显示总数):显示(55, 345)
    end
  end,1
)
end




local 道具网格 = 商店:创建商店网格("道具网格",5,30)
function 道具网格:获得鼠标(x, y,a)
        local 物品 = self:焦点物品()
        if 物品 and 物品.物品  then
            local xx,yy=引擎:取鼠标坐标()
            __UI弹出.道具提示:打开(物品.物品,xx+20,yy+20)
        end
end


function 道具网格:右键弹起(x, y, a)
        local 物品 = self:焦点物品()
        if 物品 and 物品.物品 and self:焦点()~=0 then
            if 商店.选中物品 and 商店.道具列表[商店.选中物品] and 商店.选中物品==a then
                商店.选中数量 = 商店.选中数量 - 1
                if 商店.选中数量<=1 then
                    商店.选中数量 = 1
                end
            else
                商店.选中数量=1
                self.选中编号=a
                商店.选中物品=a
              
            end
        else
            商店.选中物品=nil
        end
        商店:刷新货币()
end


function 道具网格:左键弹起(x, y, a)
    local 物品 = self:选中物品()
    if 物品 and 物品.物品 and self:选中()~=0 then
        if 商店.选中物品 and 商店.道具列表[商店.选中物品] and 商店.选中物品==a then
            商店.选中数量 = 商店.选中数量 + 1
            if 商店.选中数量>99 then
                商店.选中数量 = 99
            end
        else
            商店.选中数量=1
            商店.选中物品=a
            if __手机 then
              __UI弹出.道具提示:打开(物品.物品,x,y)
            end
        end
    else
      商店.选中物品=nil
    end
    商店:刷新货币()
end







local 数量输入 = 商店:创建文本输入("数量输入", 55, 405, 50, 18)
function 数量输入:初始化()
  self:取光标精灵()
  self:置限制字数(3)
  self:置颜色(0, 0, 0, 255)
  self:置模式(self.数字模式)
end
function 数量输入:输入事件()
    商店.选中数量=self:取数值()
    if  商店.选中数量>99 then
        商店.选中数量=99
    end
    商店:刷新货币()
end

local 购买 = 商店:创建红色按钮("购买", "购买", 195, 403,60,22) 
function  购买:左键弹起(x, y)
    if 商店.选中物品 and 商店.道具列表[商店.选中物品] then
            请求服务(1503,{数量=商店.选中数量,商品=商店.道具列表[商店.选中物品].原始商品})
    else
        __UI弹出.提示框:打开("#Y请选择你要购买的商品")
    end
end


local 关闭 = 商店:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  商店:置可见(false)
end


