
__UI弹出.气魔补充 = 界面:创建弹出窗口("气魔补充", 0, 0, 290, 157)
local 气魔补充 = __UI弹出.气魔补充
function 气魔补充:初始化()
  self.可初始化=true
  self:置坐标(引擎.宽度-295,60)
  self:置精灵(取黑色背景(0, 0, 290, 157))
end
function 气魔补充:显示(x, u)
  if self.图像 then
    self.图像:显示(x, u)
  end
end
function 气魔补充:打开(lx)
    self:置可见(true)
    self:重置(lx)
    self.类型 = lx
    self:置坐标(引擎.宽度-295,60)
end
function 气魔补充:重置(lx)
        self.图像 = self:创建纹理精灵(function()
                  if "人物" == lx then
                    说明字体:置颜色(__取颜色("黄色"))
                    说明字体:取描边图像(string.format("气血 ：%s/%s/%s", 角色信息.气血, 角色信息.气血上限, 角色信息.最大气血)):显示(20, 78)
                    说明字体:取描边图像( string.format("魔法 ：%s/%s", 角色信息.魔法, 角色信息.最大魔法)):显示(20, 99)
                    说明字体:取描边图像(string.format("愤怒 ：%s/%s", 角色信息.愤怒, 150)):显示(20, 120)
                  elseif "召唤兽" == lx  then
                    说明字体:置颜色(__取颜色("黄色"))
                    说明字体:取描边图像(string.format("气血 ：%s/%s", 角色信息.参战宝宝.气血, 角色信息.参战宝宝.最大气血)):显示(20, 78)
                    说明字体:取描边图像( string.format("魔法 ：%s/%s", 角色信息.参战宝宝.魔法, 角色信息.参战宝宝.最大魔法)):显示(20, 99)
                  end

            end,1
      )
end
for i, v in ipairs({
  {
    name = "补充气血",
    x = 17,
    y = 15,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(109, 36),
    font = "补充气血"
  },
  {
    name = "补充魔法",
    x = 150,
    y = 15,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(109, 36),
    font = "补充魔法"
  }
}) do
  local 临时函数 = 气魔补充:创建蓝色按钮(v.font, v.name, v.x, v.y, 109, 36,标题字体)
 function  临时函数:左键弹起(x, y)
    if v.name == "补充气血" then
      气魔补充:置可见(false)
      if 气魔补充.类型 == "人物" then
        请求服务(3727, {
          类型 = 1
        })
      elseif 气魔补充.类型== "召唤兽" then
        请求服务(3727, {类型 = 2})
      end
    elseif v.name == "补充魔法" then
      气魔补充:置可见( false)
      if 气魔补充.类型 == "人物" then
        请求服务(3728, {类型 = 1})
      elseif 气魔补充.类型 == "召唤兽" then
        请求服务(3727, {类型 = 2})
      end
    end
  end
end
