


local 回收系统 = 窗口层:创建窗口("回收系统", 0,0, 490, 360)
function 回收系统:初始化()
   self:创建纹理精灵(function()
    置窗口背景("回收系统", 0, 0, 490, 360, true):显示(0, 0)
    取白色背景(0, 0, 470, 290, true):显示(10, 50)  
    文本字体:置颜色(__取颜色("绿色"))
    文本字体:取图像("回收物品可获得大量金钱.战斗结束后结算并扣除物品"):显示((490-文本字体:取宽度("回收物品可获得大量金钱.战斗结束后结算并扣除物品"))//2, 30)
    文本字体:置颜色(0,0,0,255)
    文本字体:取图像("低级兽决"):显示(52, 65)
    文本字体:取图像("高级兽决"):显示(52, 95)
    文本字体:取图像("暗    器"):显示(52, 125)
    文本字体:取图像("环    装"):显示(52, 155)
    文本字体:取图像("二    级"):显示(52, 185)
    文本字体:取图像("五    宝"):显示(52, 215)
    文本字体:取图像("宝    石"):显示(52, 245)
    文本字体:取图像("书    铁"):显示(52, 275)
    文本字体:取图像("超级柳露"):显示(52, 305)
    文本字体:取图像("修 练 果"):显示(292, 65)
    文本字体:取图像("强 化 石"):显示(292, 95)
    文本字体:取图像("月 华 露"):显示(292, 125)
    文本字体:取图像("清灵净瓶"):显示(292, 155)
    文本字体:取图像("九转金丹"):显示(292, 185)
    文本字体:取图像("低级内丹"):显示(292, 215)
    文本字体:取图像("高级内丹"):显示(292, 245)
  end
)
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
  
end



function 回收系统:打开(数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.回收设置={}
  self.价格数据 ={兽决=10000,高级兽决=50000,二级药=1000,五宝=5000,宝石=10000,超级金柳露=10000,环装=10000,暗器=1000,书铁=1000,修练果=100000,强化石=1000,月华露=5,清灵净瓶=3000,九转金丹=500,低级内丹=10000,高级内丹=50000}
end



function 回收系统:刷新(数据)
  if 数据 and 数据.自动回收 then
    self.回收设置=数据.自动回收
  end

  if 数据 and 数据.回收价格 then
    self.价格数据=数据.回收价格
  end
  self:显示设置()
end

function 回收系统:显示设置()
  self.图像 =self:创建纹理精灵(function()
    文本字体:置颜色(__取颜色("蓝色"))
    文本字体:取图像("每本"..数额尾数转换(self.价格数据.兽决).."银子"):显示(240-文本字体:取宽度("每本"..数额尾数转换(self.价格数据.兽决).."银子"), 65)
    文本字体:取图像("每本"..数额尾数转换(self.价格数据.高级兽决).."银子"):显示(240-文本字体:取宽度("每本"..数额尾数转换(self.价格数据.高级兽决).."银子"), 95)
    文本字体:取图像("每个"..数额尾数转换(self.价格数据.暗器).."银子"):显示(240-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.暗器).."银子"), 125)
    文本字体:取图像(数额尾数转换(self.价格数据.环装).."-"..数额尾数转换(self.价格数据.环装*3).."银子"):显示(240-文本字体:取宽度(数额尾数转换(self.价格数据.环装).."-"..数额尾数转换(self.价格数据.环装*3).."银子"), 155)
    文本字体:取图像("每个"..数额尾数转换(self.价格数据.二级药).."银子"):显示(240-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.二级药).."银子"), 185)
    文本字体:取图像("每个"..数额尾数转换(self.价格数据.五宝).."银子"):显示(240-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.五宝).."银子"), 215)
    文本字体:取图像("每级"..数额尾数转换(self.价格数据.宝石).."银子"):显示(240-文本字体:取宽度("每级"..数额尾数转换(self.价格数据.宝石).."银子"), 245)
    文本字体:取图像("每级"..数额尾数转换(self.价格数据.书铁).."银子"):显示(240-文本字体:取宽度("每级"..数额尾数转换(self.价格数据.书铁).."银子"), 275)
    文本字体:取图像("每个"..数额尾数转换(self.价格数据.超级金柳露).."银子"):显示(240-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.超级金柳露).."银子"), 305)

    文本字体:取图像("每个"..数额尾数转换(self.价格数据.修练果).."银子"):显示(475-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.修练果).."银子"), 65)
    文本字体:取图像("每个"..数额尾数转换(self.价格数据.强化石).."银子"):显示(475-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.强化石).."银子"), 95)
    文本字体:取图像("每品"..数额尾数转换(self.价格数据.月华露).."银子"):显示(475-文本字体:取宽度("每品"..数额尾数转换(self.价格数据.月华露).."银子"), 125)
    文本字体:取图像("每个"..数额尾数转换(self.价格数据.清灵净瓶).."银子"):显示(475-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.清灵净瓶).."银子"), 155)
    文本字体:取图像("每品"..数额尾数转换(self.价格数据.九转金丹).."银子"):显示(475-文本字体:取宽度("每品"..数额尾数转换(self.价格数据.九转金丹).."银子"), 185)
    文本字体:取图像("每个"..数额尾数转换(self.价格数据.低级内丹).."银子"):显示(475-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.低级内丹).."银子"), 215)
    文本字体:取图像("每个"..数额尾数转换(self.价格数据.高级内丹).."银子"):显示(475-文本字体:取宽度("每个"..数额尾数转换(self.价格数据.高级内丹).."银子"), 245)



  end,1
)
  for k, v in pairs(self.回收设置) do
      if self[k] then
        if v==1 then
          self[k]:置选中(true)
        else
          self[k]:置选中(false)
        end
      end
  end
end

function 回收系统:显示(x,y)
      if self.图像 then
        self.图像:显示(x,y)
      end

end





local 第一排={"兽决","高级兽决","暗器","环装","二级药","五宝","宝石","书铁","超级金柳露"}
for i, v in ipairs(第一排) do
    local 临时按钮=回收系统:创建多选按钮(v, 17, 60+(i-1)*30)
    function 临时按钮:初始化()
          self:创建圆形选中精灵()
    end
    function 临时按钮:左键弹起(x, y)
        请求服务(43,{序列=v})
    end
end


local 第二排={"修练果","强化石","月华露","清灵净瓶","九转金丹","低级内丹","高级内丹"}
for i, v in ipairs(第二排) do
    local 临时按钮=回收系统:创建多选按钮(v, 257, 60+(i-1)*30)
    function 临时按钮:初始化()
          self:创建圆形选中精灵()
    end
    function 临时按钮:左键弹起(x, y)
        请求服务(43,{序列=v})
    end
end







local 回收勾选 = 回收系统:创建蓝色按钮("回收勾选", "回收勾选",315, 290, 90,30,标题字体)
function 回收勾选:左键弹起(x, y)
  请求服务(3759)
end



local 关闭 = 回收系统:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  回收系统:置可见(false)
end

