
local 自选灵饰系统 = 窗口层:创建窗口("自选灵饰系统", 0,0, 300, 280)

function 自选灵饰系统:初始化()
  self:创建纹理精灵(function()
              置窗口背景("自选灵饰", 0, 0, 300, 280, true):显示(0, 0)
              for i=1, 6 do
                取输入背景(0, 0, 200, 22):显示(80, 65+(i-1)*30)
              end
              标题字体:置颜色(255,255,255,255)
              标题字体:取图像("基础属性:"):显示(10,68)
              for i=1, 4 do
                标题字体:取图像("附加属性:"):显示(10,98+(i-1)*30)
              end
              标题字体:取图像("选择特效:"):显示(10,218)
          end
  )


	self.灵饰属性={}
	self.灵饰属性.手镯={主属性={"封印命中等级","抵抗封印等级"},副属性={"气血","防御","格挡值","法术防御","气血回复效果","抵抗封印等级","抗法术暴击等级","抗物理暴击等级"}}
	self.灵饰属性.佩饰={主属性={"速度"},副属性={"气血","防御","格挡值","法术防御","气血回复效果","抵抗封印等级","抗法术暴击等级","抗物理暴击等级"}}
	self.灵饰属性.戒指={主属性={"伤害","防御"},副属性={"伤害","速度","固定伤害","法术伤害","治疗能力","狂暴等级","穿刺等级","封印命中等级","法术暴击等级","物理暴击等级","法术伤害结果"}}
	self.灵饰属性.耳饰={主属性={"法术伤害","法术防御"},副属性={"伤害","速度","固定伤害","法术伤害","治疗能力","狂暴等级","穿刺等级","封印命中等级","法术暴击等级","物理暴击等级","法术伤害结果"}}
	self.级别 = 0
	self.部位="手镯"
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end

end
local 选项按钮={"基础","属性1","属性2","属性3","属性4","特效"}
function 自选灵饰系统:打开(数据)
      self:置可见(not self.是否可见)
      if not self.是否可见 then
        return
      end
      self.部位="手镯"
      self.级别= 数据.级别 or 0
      if 数据.部位~=nil then
        self.部位=数据.部位
      end
      if __UI弹出.弹出列表.是否可见 then
          __UI弹出.弹出列表:置可见(false)
      end
      for i, v in ipairs(选项按钮) do
          self[v]="" 
      end
      self:显示设置()
      self.图像=标题字体:置颜色(255,255,255,255):取精灵("当前部位:  "..self.部位.."    礼包等级: "..self.级别.." 级")
end
function 自选灵饰系统:显示(x,y)
      if self.图像 then
        self.图像:显示(x+10,y+40)
      end
      for i, v in ipairs(选项按钮) do
          if self[v.."显示"] then
              self[v.."显示"]:显示(x+85, y+68+(i-1)*30)
          end
      end
end

function 自选灵饰系统:显示设置()
      for i, v in ipairs(选项按钮) do
          if self[v]=="" then
             self[v.."显示"]=nil
          else
              self[v.."显示"]=文本字体:置颜色(0,0,0,255):取精灵(self[v])
          end
      end
end

for i, v in ipairs(选项按钮) do
    local 临时按钮=自选灵饰系统:创建按钮(v.."按钮",265,66+(i-1)*30)
    function 临时按钮:初始化()
        self:创建按钮精灵(__res:取资源动画("jszy/xjiem",0x00000050),1)
    end
    function 临时按钮:左键弹起(x, y)
            local 列表数据={}
            if v=="基础" then
                列表数据=table.copy(自选灵饰系统.灵饰属性[自选灵饰系统.部位].主属性)
            elseif v=="特效" then
                列表数据={"无级别限制","超级简易","简易"}
            else
                列表数据=table.copy(自选灵饰系统.灵饰属性[自选灵饰系统.部位].副属性)
            end
            local 事件 =function (a)
                  if 列表数据[a] then
                      自选灵饰系统[v] = 列表数据[a]
                      自选灵饰系统:显示设置()
                  end
            end
              local xx,yy=self:取坐标()
            __UI弹出.弹出列表:打开(列表数据,nil,事件,xx-190,yy+20,200,nil,标题字体,5)
    end
  
end



local 确定 = 自选灵饰系统:创建红色按钮("确定", "确定", 115,245, 50, 22)
function 确定:左键弹起(x, y) 
    if 自选灵饰系统.特效== nil or 自选灵饰系统.特效==""  then
      __UI弹出.提示框:打开('#Y请选择特效！')
		else
        local 发送内容={等级=自选灵饰系统.级别}
        for i, v in ipairs(选项按钮) do
            发送内容[v]=自选灵饰系统[v]
        end
        请求服务(3861,发送内容)
        自选灵饰系统:置可见(false)
		end
    
  end

  local 关闭 = 自选灵饰系统:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    自选灵饰系统:置可见(false)
  end






