local GGF = require("GGE.函数")
---local _HASH = require("gxy2.hash")

local wdf = class("wdf")
function wdf:初始化()
  self._WDFS={}
  self._hash = {}
  self.data={}

end

function wdf:加载(path)
    if not GGF.判断文件(path) then
        return false
    end
    local name = GGF.取文件名(path, true)
    if path:sub(-3, -2) == "wd" and not self._WDFS[name] then
        local wdfs = require("gxyq.wdf")(path)
        if wdfs then
            self._WDFS[name] = wdfs:GetList()
            return true
        else
            self._WDFS[name]=nil
        end
    end
    return false
end



function wdf:取数据(path)
    local r = path
    if type(path) == "string" then
        r = self:是否存在(path)
    end
    if type(r) == "table" and r.wdf then
        return r.wdf:GetData(r.id)
    end
end

-- function wdf:是否存在(path)

--     if self._hash[path] then
--         return self._hash[path]
--     end
--     local name, hash = gge.utf8togbk(path):match("(%w+)/(.+)")
--     if name and self._WDFS[name] and hash then
--         if hash:sub(1, 2) == "0x" then
--             hash = tonumber(hash:match("(.+)%.%w%w%w"))
--         else
--             local path2 = hash
--             hash = _HASH(hash)
--             if self._WDFS[name][hash] then
--                 self._hash[path] = self._WDFS[name][hash]
--                 return self._WDFS[name][hash], string.format("%s/0x%X.%s", name, hash, path:sub(-3))
--             else
--             end
--         end
--         return self._WDFS[name][hash]
--     end
-- end

function wdf:是否存在(path)
    local hash = gge.hash(path)
    return self._WDFS[hash]
end

-- function wdf:导出(path)
--     if not GGF.判断文件("assets/" .. path) then
--         if path:sub(-3) == "map" then
--             GGF.复制文件("res/" .. path, "assets/" .. path)
--         else
--             local name, hash = gge.utf8togbk(path):match("(%w+)/(.+)")
--             if name and self._WDFS[name] and hash then
--                 if hash:sub(1, 2) == "0x" then
--                     hash = tonumber(hash:match("(.+)%.%w%w%w"))
--                 else
--                     hash = _HASH(hash)
--                 end
--                 local r = self._WDFS[name][hash]
--                 if r then
--                     if not GGF.判断文件("../out/" .. path) and not path:find("/0x") then
                 
--                         GGF.写出文件("../out/" .. path, r.wdf:GetData(r.id))
--                     end
--                     path = string.format("%s/0x%08X.%s", name, hash, path:sub(-3))
--                     if not GGF.判断文件("assets/" .. path) then
                   
--                         GGF.写出文件("assets/" .. path, r.wdf:GetData(r.id))
--                         return path
--                     end
--                 end
--             end
--         end
--     end
--     return path
-- end

function wdf:allwas(name, path)
    if name and self._WDFS[name] then
        for k, v in pairs(self._WDFS[name]) do
            local lzpath = string.format("%s/%s.%s", path, string.sub(string.format("%#x", k), 3), "was")
            if not __res:是否存在(lzpath) then
                __res:写出文件(lzpath, v.wdf:GetData(v.id))
            end
        end
    end
    self._WDFS = {}
end

return wdf
