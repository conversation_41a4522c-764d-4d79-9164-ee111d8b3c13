local 助战宝宝查看 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "助战宝宝查看", 112 + abbr.py.x, 5 + abbr.py.y, 744, 520)
function 助战宝宝查看:初始化()
  local nsf = require("SDL.图像")(744, 520)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("助战宝宝查看", 0, 12, 737, 506, true):置透明(240):显示(0, 0)
    __res:getPNGCC(3, 761, 370, 308, 163)["拉伸"](__res:getPNGCC(3, 761, 370, 308, 163), 217, 162)["显示"](__res:getPNGCC(3, 761, 370, 308, 163)["拉伸"](__res:getPNGCC(3, 761, 370, 308, 163), 217, 162), 20, 92)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 250, 94)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 250, 163)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 250, 229)
    取输入背景(0, 0, 183, 23)["显示"](取输入背景(0, 0, 183, 23), 23, 263)
    取输入背景(0, 0, 105, 23)["显示"](取输入背景(0, 0, 105, 23), 99, 292)
    取输入背景(0, 0, 55, 23)["显示"](取输入背景(0, 0, 55, 23), 253, 292)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "参战等级")["显示"](字体18["取图像"](字体18, "参战等级"), 20, 296)
    字体18["取图像"](字体18, "等级")["显示"](字体18["取图像"](字体18, "等级"), 212, 296)
    local lssj = 取输入背景(0, 0, 93, 23)
    for _, v in ipairs({
      {
        name = "气血",
        x = 22,
        y = 327,
        pyx = 40
      },
      {
        name = "体质",
        x = 175,
        y = 327,
        pyx = 40
      },
      {
        name = "魔法",
        x = 22,
        y = 357,
        pyx = 40
      },
      {
        name = "法力",
        x = 175,
        y = 357,
        pyx = 40
      },
      {
        name = "攻击",
        x = 22,
        y = 387,
        pyx = 40
      },
      {
        name = "力量",
        x = 175,
        y = 387,
        pyx = 40
      },
      {
        name = "防御",
        x = 22,
        y = 417,
        pyx = 40
      },
      {
        name = "耐力",
        x = 175,
        y = 417,
        pyx = 40
      },
      {
        name = "速度",
        x = 22,
        y = 447,
        pyx = 40
      },
      {
        name = "敏捷",
        x = 175,
        y = 447,
        pyx = 40
      },
      {
        name = "灵力",
        x = 22,
        y = 477,
        pyx = 40
      },
      {
        name = "潜能",
        x = 175,
        y = 477,
        pyx = 40
      },
      {
        name = "攻击资质",
        x = 330,
        y = 99,
        pyx = 80
      },
      {
        name = "防御资质",
        x = 526,
        y = 99,
        pyx = 80
      },
      {
        name = "体力资质",
        x = 330,
        y = 129,
        pyx = 80
      },
      {
        name = "法力资质",
        x = 526,
        y = 129,
        pyx = 80
      },
      {
        name = "速度资质",
        x = 330,
        y = 159,
        pyx = 80
      },
      {
        name = "躲闪资质",
        x = 526,
        y = 159,
        pyx = 80
      },
      {
        name = "寿  命",
        x = 330,
        y = 189,
        pyx = 80
      },
      {
        name = "成  长",
        x = 526,
        y = 189,
        pyx = 80
      },
      {
        name = "五  行",
        x = 330,
        y = 219,
        pyx = 80
      }
    }) do
      if v.name~="寿  命" and  v.name~="五  行" and  v.name~="成  长" then
        字体18["置颜色"](字体18, __取颜色("白色"))
      else
        字体18["置颜色"](字体18, __取颜色("黄色"))
      end
      字体18["取图像"](字体18, v.name)["显示"](字体18["取图像"](字体18, v.name), v.x, v.y)
      lssj["显示"](lssj, v.x + v.pyx, v.y - 2)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.模型格子 = __UI模型格子["创建"]()
end
function 助战宝宝查看:打开(data,助战编号)
  self.助战编号=助战编号
  if data then
    self:置可见(true)
    self:重置(data)
    self.技能控件.技能按钮["置选中"](self.技能控件.技能按钮, true)
    self.技能控件["技能"]["重置"](self.技能控件["技能"])
  end
end

function 助战宝宝查看:刷新宝宝(ssd)
	self:重置(ssd)
end
function 助战宝宝查看:重置(data)
  self.数据 = data
  self.模型格子["清空"](self.模型格子)
  local nsf = require("SDL.图像")(744, 520)
  if nsf["渲染开始"](nsf) then
    
    for _, v in ipairs({
      {
        name = "气血",
        x = 22,
        y = 327,
        pyx = 50
      },
      {
        name = "体质",
        x = 175,
        y = 327,
        pyx = 50
      },
      {
        name = "魔法",
        x = 22,
        y = 357,
        pyx = 50
      },
      {
        name = "魔力",
        x = 175,
        y = 357,
        pyx = 50
      },
      {
        name = "伤害",
        x = 22,
        y = 387,
        pyx = 50
      },
      {
        name = "力量",
        x = 175,
        y = 387,
        pyx = 50
      },
      {
        name = "防御",
        x = 22,
        y = 417,
        pyx = 50
      },
      {
        name = "耐力",
        x = 175,
        y = 417,
        pyx = 50
      },
      {
        name = "速度",
        x = 22,
        y = 447,
        pyx = 50
      },
      {
        name = "敏捷",
        x = 175,
        y = 447,
        pyx = 50
      },
      {
        name = "灵力",
        x = 22,
        y = 477,
        pyx = 50
      },
      {
        name = "潜力",
        x = 175,
        y = 477,
        pyx = 50
      },
      {
        name = "攻击资质",
        x = 330,
        y = 99,
        pyx = 90
      },
      {
        name = "防御资质",
        x = 526,
        y = 99,
        pyx = 90
      },
      {
        name = "体力资质",
        x = 330,
        y = 129,
        pyx = 90
      },
      {
        name = "法力资质",
        x = 526,
        y = 129,
        pyx = 90
      },
      {
        name = "速度资质",
        x = 330,
        y = 159,
        pyx = 90
      },
      {
        name = "躲闪资质",
        x = 526,
        y = 159,
        pyx = 90
      },
      {
        name = "寿命",
        x = 330,
        y = 189,
        pyx = 90
      },
      {
        name = "成长",
        x = 526,
        y = 189,
        pyx = 90
      },
      {
        name = "五行",
        x = 330,
        y = 219,
        pyx = 90
      }
    }) do
      字体18["置颜色"](字体18, __取颜色("黑色"))
      if v.name=="寿命" and data.种类=="神兽" then
        字体18["取图像"](字体18, "★永生★")["显示"](字体18["取图像"](字体18, "★永生★"), v.x + v.pyx, v.y)
      else
        字体18["取图像"](字体18, data[v.name])["显示"](字体18["取图像"](字体18, data[v.name]), v.x + v.pyx, v.y)
      end
    end
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, data["模型"]):置混合(0):显示(22, 66)
    local kd = 字体18["取宽度"](字体18, data["模型"])
    字体18["置颜色"](字体18, __取颜色("绿色"))
    字体18["取图像"](字体18, "："..data["种类"]):置混合(0):显示(28+kd, 66)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, data["名称"]):显示(30, 265)
    字体18["取图像"](字体18, data["参战等级"])["显示"](字体18["取图像"](字体18, data["参战等级"]), 110, 295)
    字体18["取图像"](字体18, data["等级"])["显示"](字体18["取图像"](字体18, data["等级"]), 262, 295)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.模型格子["置数据"](self.模型格子, data, "召唤兽", 126, 203)
  self.装备网格["置物品"](self.装备网格, data["装备"])
end
local 关闭 = 助战宝宝查看["创建我的按钮"](助战宝宝查看, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 694, 0)
function 关闭:左键弹起(x, y, msg)
  助战宝宝查看.数据=nil
  助战宝宝查看["置可见"](助战宝宝查看, false)
end
local 装备网格 = 助战宝宝查看["创建网格"](助战宝宝查看, "装备网格", 250, 94, 55, 190)
function 装备网格:初始化()
end
function 装备网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520, 86, w, h, nil, a)
  end
end
function 装备网格:置物品(data)
  self:创建格子(55, 55, 15, 0, #data, 1)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], "白格子", "装备")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
local 技能控件 = 助战宝宝查看["创建控件"](助战宝宝查看, "技能控件", 330, 212, 360, 265)
local jcx = 0
local jcy = 0
local lsb3 = {
  "技能",
  "内丹",
  "进阶"
}
local lsb4 = {
  "技\n能",
  "内\n丹",
  "进\n阶"
}

for i = 1, #lsb3 do
  local 临时函数 = 技能控件["创建我的单选按钮"](技能控件, __res:getPNGCC(1, 686, 0, 48, 98, true):拉伸(48, 68), __res:getPNGCC(1, 1132, 0, 54, 99, true):拉伸(48, 68), lsb3[i] .. "按钮", 306, 50 + (i - 1) * 70, lsb4[i])
 function  临时函数:左键按下(消息, x, y)
    技能控件[lsb3[i]]["重置"](技能控件[lsb3[i]])
  end
  local 临时函数2 = 技能控件["创建控件"](技能控件, lsb3[i], jcx, jcy + 45, 310, 220)
 function  临时函数2:初始化()
    if "技能" == lsb3[i] then
      local nsf = require("SDL.图像")(310, 220)
      if nsf["渲染开始"](nsf) then
        取灰色背景(0, 0, 308, 220, true)["显示"](取灰色背景(0, 0, 308, 220, true), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 22, 12, 273, 202)
     function  临时函数3:初始化()
        self:创建格子(55, 55, 18, 18, 9, 4, true)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(数据, 认证)
        for i = 1, #临时函数3["子控件"] do
          local lssj = __召唤兽技能格子["创建"]()
          if 认证 and i == 认证 then
            认证 = 数据[i]
          end
          local 是否赐福= nil 
          if 助战宝宝查看.数据 and 助战宝宝查看.数据.赐福技能_生效 then
            是否赐福=判断是否赐福技能(助战宝宝查看.数据.赐福技能_生效  ,  数据[i])
          end
          lssj:置数据(数据[i], 55, 55, nil, 认证,是否赐福)
          --lssj["置数据"](lssj, 数据[i], 55, 55, nil, 认证)
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
        end
      end
    elseif "内丹" == lsb3[i] then
      local nsf = require("SDL.图像")(310, 220)
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 653, 931, 315, 218)["显示"](__res:getPNGCC(3, 653, 931, 315, 218), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 0, 0, 315, 218)
     function  临时函数3:初始化()
        self:创建格子(55, 55, 8, 8, 1, 6)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(zjcz)
        local 坐标 = {
          {129, 7}, --1
          {129-67, 7+41}, --2
          {129+65, 7+41}, --3
          {129+65, 7+80+33}, --4
          {129, 7+150}, --5
          {129-67, 7+80+33}, --6
        }
        for i = 1, #临时函数3["子控件"] do
          local lssj = __内丹格子["创建"]()
          lssj:置数据(zjcz,助战宝宝查看["数据"],i, 55, 55) 
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
          临时函数3["子控件"][i]["置坐标"](临时函数3["子控件"][i], 坐标[i][1], 坐标[i][2])
        end
      end
    elseif "进阶" == lsb3[i] then
      local nsf = require("SDL.图像")(310, 220)
      if nsf["渲染开始"](nsf) then
        取灰色背景(0, 0, 308, 220, true)["显示"](取灰色背景(0, 0, 308, 220, true), 0, 0)
        __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094)), 196, 210)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094)), 196, 210), 55, 7)
        -- __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212), 55, 7)
        nsf["渲染结束"](nsf)
      end
      self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 76, 52, 126, 102)
     function  临时函数3:初始化()
        self:创建格子(126, 102, 0, 0, 1, 1)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(数据, zjcz, bb, nsgz)
        for i = 1, #临时函数3["子控件"] do
          local lssj = __特性格子["创建"]()
          lssj["置数据"](lssj, 数据, 126, 102)
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
        end
      end
    end
    self:置可见(false)
  end
 function  临时函数2:显示(x, y)
    if self.数据 then
      self.数据["显示"](self.数据, x, y)
    end
  end
 function  临时函数2:重置()
    if "技能" == lsb3[i] then
      技能控件["技能"]["置可见"](技能控件["技能"], true)
      技能控件["内丹"]["置可见"](技能控件["内丹"], false)
      技能控件["进阶"]["置可见"](技能控件["进阶"], false)
      self.数据 = nil
      临时函数2["技能网格"]["置数据"](临时函数2["技能网格"], 助战宝宝查看["数据"]["技能"], 助战宝宝查看["数据"]["法术认证"])
    elseif "内丹" == lsb3[i] then
      技能控件["技能"]["置可见"](技能控件["技能"], false)
      技能控件["内丹"]["置可见"](技能控件["内丹"], true)
      技能控件["进阶"]["置可见"](技能控件["进阶"], false)
      self.数据 = nil
      local zjcz = 0
      if 助战宝宝查看["数据"] and 助战宝宝查看["数据"].内丹 and 助战宝宝查看["数据"].内丹.格子 and 助战宝宝查看["数据"].内丹.内丹上限 then
        zjcz=0
        for n = 1,#角色信息.坐骑列表 do
          if 角色信息.坐骑列表[n].统御召唤兽[1] == 助战宝宝查看["数据"].认证码 or 角色信息.坐骑列表[n].统御召唤兽[2] == 助战宝宝查看["数据"].认证码 then
            zjcz = 角色信息.坐骑列表[n].初始成长
            break
          end
        end
        临时函数2["内丹网格"]["置数据"](临时函数2["内丹网格"], zjcz)
      else
        临时函数2["内丹网格"]["置数据"](临时函数2["内丹网格"])
      end
    elseif "进阶" == lsb3[i] then
      技能控件["技能"]["置可见"](技能控件["技能"], false)
      技能控件["内丹"]["置可见"](技能控件["内丹"], false)
      技能控件["进阶"]["置可见"](技能控件["进阶"], true)
      self.数据 = nil
      -- table.print(助战宝宝查看["数据"])
      if 助战宝宝查看["数据"] and 助战宝宝查看["数据"]["进阶"] then
        local jinjie=助战宝宝查看["数据"]["进阶"]
        local lx=jinjie.灵性
        local nsf = require("SDL.图像")(310, 220)
        if nsf["渲染开始"](nsf) then
          if lx > 0 and lx <= 10 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869)), 200, 212), 55, 7)
          elseif lx > 10 and lx <= 20 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230)), 200, 212), 55, 7)
          elseif lx > 20 and lx <= 30 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528)), 200, 212), 55, 7)
          elseif lx > 30 and lx <= 40 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212), 55, 7)
          elseif lx > 40 and lx <= 50 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520)), 200, 212), 55, 7)
          elseif lx > 50 and lx <= 60 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105)), 200, 212), 55, 7)
          elseif lx > 60 and lx <= 70 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892)), 200, 212), 55, 7)
          elseif lx > 70 and lx <= 80 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450)), 200, 212), 55, 7)
          elseif lx > 80 and lx <= 90 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895)), 200, 212), 155, 7)
          elseif lx > 90 and lx <= 91 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119)), 200, 212), 55, 7)
          elseif lx > 91 and lx <= 93 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218)), 200, 212), 55, 7)
          elseif lx > 93 and lx <= 97 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179)), 200, 212), 155, 7)
          elseif lx >= 98 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070)), 200, 212), 55, 7)
          end
          字体18["置颜色"](字体18, 255, 255, 255)
          字体18["取图像"](字体18, "灵性:" .. lx):置混合(0):显示(15, 7)
          if jinjie.特性 and jinjie.特性~="无" then
            if jinjie.开启 then
              字体18["置颜色"](字体18, __取颜色("黄色"))
            else
              字体18["置颜色"](字体18, __取颜色("白色"))
            end
            字体18["取图像"](字体18, jinjie.特性):置混合(0):显示(15+117, 7+89)
          end
          nsf["渲染结束"](nsf)
        end
        self.数据 = nsf["到精灵"](nsf)
        临时函数2["进阶网格"]["置数据"](临时函数2["进阶网格"], 助战宝宝查看["数据"])
      else
        临时函数2["进阶网格"]["置数据"](临时函数2["进阶网格"], {})
      end
    end
  end
end
local 鉴定宝宝 = 助战宝宝查看["创建我的按钮"](助战宝宝查看, __res:getPNGCC(3, 536, 560, 102, 34, true), "鉴定宝宝", 25, 25, "鉴定宝宝")
function 鉴定宝宝:左键弹起(x, y, msg)
  if 助战宝宝查看["数据"] and 助战宝宝查看["数据"].元宵 then
    local bb=助战宝宝查看["数据"]
    __UI弹出.提示框:打开("#Y/这只召唤兽还可以用喂食#G"..bb.元宵.可用.."#Y个元宵，#G"..bb.元宵.炼兽真经.."#Y本炼兽珍经，#G"..bb.元宵.如意丹.."#Y个如意丹，#G"..bb.元宵.千金露.."#Y个千金露，#G"..bb.元宵.水晶糕.."#Y个水晶糕。")
  end
end

local 加点按钮 = 助战宝宝查看:创建我的按钮(__res:getPNGCC(1, 626, 28, 58, 34, true):拉伸(69, 34), "加点按钮", 257, 471, "加点")
function 加点按钮:左键弹起(x, y, msg)
  if 助战宝宝查看["数据"] then
    __UI界面.窗口层.助战宝宝加点:打开(助战宝宝查看.数据,助战宝宝查看.助战编号)
  end
end