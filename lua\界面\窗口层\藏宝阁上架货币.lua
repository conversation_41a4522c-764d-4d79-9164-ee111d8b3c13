--[[
LastEditTime: 2024-09-13 18:34:41
--]]

local 藏宝阁上架货币 = 窗口层:创建窗口("藏宝阁上架货币", 0,0, 300, 180)

function 藏宝阁上架货币:初始化()
      self.类型 = ""
      self.数量 = 0
      self.价格输入:清空()
      self.价格输入:置模式(self.价格输入.数字模式)
      self:创建纹理精灵(function()
                      置窗口背景("上架货币", 0, 0, 300, 180, true):显示(0, 0)
                      标题字体:置颜色(255,255,255,255)
                      标题字体:取图像("上架数额:"):显示(10,70)
                      标题字体:取图像("上架价格:"):显示(10,100)
                      取输入背景(0, 0, 165, 23):显示(90, 67)
                      取输入背景(0, 0, 165, 23):显示(90, 97)
                    end
              )
      self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
      self.可初始化=true
      if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
      else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-18,2)
      end
end

function 藏宝阁上架货币:打开(类型,数量)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
     return
  end
  self.类型 = 类型
  self.数量 = 数量
  
  self.图像=文本字体:置颜色(255,255,255,255):取精灵("你当前的"..self.类型.."余额为:"..self.数量)

end
function 藏宝阁上架货币:显示(x,y)
    if self.图像 then
       self.图像:显示(x+(self.宽度-self.图像.宽度)//2,y+35)
    end

end

local 价格输入 = 藏宝阁上架货币:创建文本输入("价格输入", 95, 70, 155, 18)  
function 价格输入:初始化()
    self:取光标精灵(0, 0, 0, 255)
    self:置颜色(0, 0, 0, 255)
    self:置模式(self.数字模式)
end
local 数量输入 = 藏宝阁上架货币:创建文本输入("数量输入", 95, 100, 155, 18)  
function 数量输入:初始化()
    self:取光标精灵(0, 0, 0, 255)
    self:置颜色(0, 0, 0, 255)
    self:置模式(self.数字模式)
end
local  上架按钮= 藏宝阁上架货币:创建红色按钮("确定", "上架按钮", 40, 140,42,22)
function 上架按钮:左键弹起(x, y)
      if not 价格输入:取数值() or 价格输入:取数值()<=0  then
          __UI弹出.提示框:打开("#Y请正确输入出售价格")
      elseif  not 数量输入:取数值() or 数量输入:取数值()<=0 then
          __UI弹出.提示框:打开("#Y请正确输入出售价格") 
      elseif 数量输入:取数值() > 数量输入:取数值(self.数量)  then 
         __UI弹出.提示框:打开("#Y你的数量没有那么多") 
    
      else
        请求服务(69,{类型=藏宝阁上架货币.类型,文本="上架货币",数量=数量输入:取数值(),价格=价格输入:取数值()})        
      end
end
local  取消按钮= 藏宝阁上架货币:创建红色按钮("取消", "取消按钮", 210, 140,42,22)
function 取消按钮:左键弹起(x, y)
     藏宝阁上架货币:置可见(false)
end

local 关闭 =藏宝阁上架货币:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    藏宝阁上架货币:置可见(false)
    
  end





