__UI弹出["快捷技能选择"] = __UI界面["创建弹出窗口"](__UI界面, "快捷技能选择", 0, 0, 525, 447)
local 快捷技能选择 = __UI弹出["快捷技能选择"]
function 快捷技能选择:初始化()
  local nsf = require("SDL.图像")(525, 447)
  if nsf["渲染开始"](nsf) then
    取黑色背景(0, 0, 525, 447, true)["显示"](取黑色背景(0, 0, 525, 447, true), 0, 0)
    取灰色背景(0, 0, 415, 425, true)["显示"](取灰色背景(0, 0, 415, 425, true), 100, 10)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 快捷技能选择:打开(i)
  self:置可见(true)
  self.师门["置选中"](self.师门, true)
  self.师门["左键弹起"](self.师门)
  self.序号 = i
end
for i, v in ipairs({
  {
    name = "全部",
    x = 12,
    y = 12,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 82, 62),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true)["拉伸"](__res:getPNGCC(3, 876, 289, 85, 36, true), 82, 62),
    font = "全部"
  },
  {
    name = "师门",
    x = 12,
    y = 78,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 82, 62),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true)["拉伸"](__res:getPNGCC(3, 876, 289, 85, 36, true), 82, 62),
    font = "师门"
  },
  {
    name = "生活",
    x = 12,
    y = 144,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 82, 62),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true)["拉伸"](__res:getPNGCC(3, 876, 289, 85, 36, true), 82, 62),
    font = "生活"
  },
  {
    name = "剧情",
    x = 12,
    y = 210,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 82, 62),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true)["拉伸"](__res:getPNGCC(3, 876, 289, 85, 36, true), 82, 62),
    font = "剧情"
  },
  {
    name = "其他",
    x = 12,
    y = 276,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 82, 62),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true)["拉伸"](__res:getPNGCC(3, 876, 289, 85, 36, true), 82, 62),
    font = "其他"
  }
}) do
  local 临时函数 = 快捷技能选择["创建我的单选按钮"](快捷技能选择, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "全部" then
    elseif v.name == "师门" then
      local lssj = {}
      for i, v in ipairs(角色信息["师门技能"]) do
        for k, s in ipairs(v["包含技能"]) do
          table.insert(lssj, s)
          lssj[#lssj]["类型"] = 1
        end
      end
      快捷技能选择["技能网格"]["重置"](快捷技能选择["技能网格"], lssj)
    elseif v.name == "生活" then
    elseif v.name == "剧情" then
      local lssj = {}
      for i, v in ipairs(角色信息["剧情技能"]) do
        table.insert(lssj, v)
        lssj[#lssj]["类型"] = 3
      end
      快捷技能选择["技能网格"]["重置"](快捷技能选择["技能网格"], lssj)
    end
  end
end
local 技能网格 = 快捷技能选择["创建网格"](快捷技能选择, "技能网格", 105, 15, 404, 414)
function 技能网格:左键弹起(x, y, a, b, msg)
  if #角色信息["快捷技能"] < 7 and self.子控件[a]._spr and (0 == self.数据[a][3] or 12 == self.数据[a][3] or 108 == self.数据[a][3]) then
    发送数据(11, {
      ["位置"] = 快捷技能选择["序号"],
      ["名称"] = self.数据[a]["名称"],
      ["类型"] = self.数据[a]["类型"]
    })
    快捷技能选择["置可见"](快捷技能选择, false)
  end
end
function 技能网格:重置(data)
  self:创建格子(198, 64, 8, 8, math.ceil(#data / 2), 2, true)
  self.数据 = {}
  for _, v in ipairs(self.子控件) do
    if data[_] then
      local nsf = require("SDL.图像")(198, 64)
      if nsf["渲染开始"](nsf) then
        self.数据[_] = 取技能(data[_]["名称"], 角色信息["门派"])
        self.数据[_]["类型"] = data[_]["类型"]
        self.数据[_]["名称"] = data[_]["名称"]
        local wenj="shape/jn/"
            if self.数据[_][10] then
                wenj="shape/xinzengsucai/"
            end
        __res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 198, 64)["显示"](__res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 198, 64), 0, 0)
        __res["取图像"](__res, __res["取地址"](__res, wenj, self.数据[_][7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, self.数据[_][7])), 40, 40)["显示"](__res["取图像"](__res, __res["取地址"](__res, wenj, self.数据[_][7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, self.数据[_][7])), 40, 40), 11, 10)
        字体16["置颜色"](字体16, __取颜色("浅黑"))
        字体16["取图像"](字体16, data[_]["名称"])["显示"](字体16["取图像"](字体16, data[_]["名称"]), 63, 24)
        nsf["渲染结束"](nsf)
      end
      v["置精灵"](v, nsf["到精灵"](nsf))
    else
      v["置精灵"](v)
    end
  end
end
