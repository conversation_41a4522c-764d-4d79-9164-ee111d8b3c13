--[[
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2024-09-09 03:50:56
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2024-09-17 22:58:10
FilePath: \XYQCStiaoshi\lua\界面\窗口层\好友消息.lua
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
--]]
local 好友消息 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "好友消息", 154 + abbr.py.x, 15 + abbr.py.y, 648, 526)
function 好友消息:初始化()
  local nsf = require("SDL.图像")(648, 526)
  if nsf["渲染开始"](nsf) then
    置窗口背景("好友消息", 0, 12, 642, 516, true)["显示"](置窗口背景("好友消息", 0, 12, 642, 516, true), 0, 0)
    __res:getPNGCC(1, 792, 0, 270, 380)["显示"](__res:getPNGCC(1, 792, 0, 270, 380), 10, 130)
    取白色背景(0, 0, 345, 420, true)["显示"](取白色背景(0, 0, 345, 420, true), 285, 40)
    __res:getPNGCC(3, 736, 1155, 199, 37, true)["拉伸"](__res:getPNGCC(3, 736, 1155, 199, 37, true), 147, 36)["显示"](__res:getPNGCC(3, 736, 1155, 199, 37, true)["拉伸"](__res:getPNGCC(3, 736, 1155, 199, 37, true), 147, 36), 335, 474)
    字体18["置颜色"](字体18, __取颜色("白色"))
    local lssj = 取输入背景(0, 0, 80, 23)["置颜色"](取输入背景(0, 0, 80, 23), __取颜色("黑色"))
    字体18["取图像"](字体18, "ID")["显示"](字体18["取图像"](字体18, "ID"), 81, 72)
    lssj["显示"](lssj, 104, 70)
    字体18["取图像"](字体18, "友好")["显示"](字体18["取图像"](字体18, "友好"), 22, 101)
    lssj["显示"](lssj, 65, 99)
    字体18["取图像"](字体18, "关系")["显示"](字体18["取图像"](字体18, "关系"), 152, 101)
    lssj["显示"](lssj, 198, 99)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.聊天记录 = {}
end
function 好友消息:打开(data)
  self:置可见(true)
  self:重置(data)
  self.聊天文本["重置"](self.聊天文本)
end
function 好友消息:重置(data)
  local nsf = require("SDL.图像")(648, 504)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 757, 291, 57, 57)["拉伸"](__res:getPNGCC(3, 757, 291, 57, 57), 50, 50)["显示"](__res:getPNGCC(3, 757, 291, 57, 57)["拉伸"](__res:getPNGCC(3, 757, 291, 57, 57), 50, 50), 20, 44)
    local lssj = 取头像(data["模型"])
    if 0 == lssj[2] then
      lssj[2] = lssj[1]
    end
    __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), 48, 48)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), 48, 48), 21, 45)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, data["名称"])["显示"](字体18["取图像"](字体18, data["名称"]), 81, 44)
    字体18["取图像"](字体18, data.id)["显示"](字体18["取图像"](字体18, data.id), 111, 72)
    字体18["取图像"](字体18, data["好友度"] or 0)["显示"](字体18["取图像"](字体18, data["好友度"] or 0), 73, 101)
    字体18["取图像"](字体18, data["关系"] or "陌生人")["显示"](字体18["取图像"](字体18, data["关系"] or "陌生人"), 203, 101)
    local lssj = 取头像(data["模型"])
    __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[6]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[6])), 153, 470)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.数据 = data
end
local 关闭 = 好友消息["创建我的按钮"](好友消息, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 598, 0)
function 关闭:左键弹起(x, y, msg)
  好友消息["置可见"](好友消息, false)
end
for i, v in ipairs({
  {
    name = "表情",
    x = 496,
    y = 468,
    tcp = __res:getPNGCC(4, 539, 2, 45, 45)
  },
  {
    name = "发送",
    x = 551,
    y = 472,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 77, 41),
    font = "发送"
  }
}) do
  local 临时函数 = 好友消息["创建我的按钮"](好友消息, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "表情" then
    elseif v.name == "发送" then
      if 好友消息["消息输入"]["取文本"](好友消息["消息输入"]) then
        发送数据(6964, {
          id = 好友消息["数据"].id,
          ["内容"] = 好友消息["消息输入"]["取文本"](好友消息["消息输入"])
        })
        好友消息["消息输入"]["清空"](好友消息["消息输入"])
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "Y您想说点什么给对方听呢？")
      end
    end
  end
end
local 消息输入 = 好友消息["创建输入"](好友消息, "消息输入", 344, 482, 144, 18)
function 消息输入:初始化()
  self.取光标精灵(self)
  self:置限制字数(30)
  self:置颜色(__取颜色("黑色"))
end
local 聊天文本 = 好友消息["创建我的文本"](好友消息, "聊天文本", 298, 55, 317, 395, true)
function 聊天文本:重置()
  self.清空(self)
  if 好友消息["聊天记录"][好友消息["数据"].id] then
    for i, v in ipairs(好友消息["聊天记录"][好友消息["数据"].id]) do
      self:置文本(v)
    end
  end
end
