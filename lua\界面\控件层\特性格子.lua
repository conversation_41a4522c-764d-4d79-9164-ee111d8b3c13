local 特性格子 = class("特性格子")
function 特性格子:初始化()
  self.py = {x = 0, y = 0}
  self.文字 = false
end
function 特性格子:置数据(数据, w, h)
  self.数据 = nil
  self.模型 = nil
  self.图像 = nil
  if 数据["特性"] and 数据["特性"] ~= "无" then
    self.数据 = {}
    self.数据["说明"] = 取特性(数据["特性"], 数据["等级"], 数据["特性几率"])
    self.数据["名称"] = 数据["特性"]
    self.数据["类型"] = "召唤兽特性"
    local nsf = require("SDL.图像")(w, h)
    if nsf["渲染开始"](nsf) then
      __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 669142266))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 669142266)), 122, 102)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 669142266))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 669142266)), 122, 102), 4, 0)
      字体25["置颜色"](字体25, 252, 252, 8)
      字体25["取图像"](字体25, "特性")["显示"](字体25["取图像"](字体25, "特性"), 50, 35)
      nsf["渲染结束"](nsf)
    end
    self.模型 = nsf["到精灵"](nsf)
  else
    local nsf = require("SDL.图像")(w, h)
    if nsf["渲染开始"](nsf) then
      __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2948784481))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2948784481)), 126, 102)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2948784481))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2948784481)), 126, 102), 0, 0)
      nsf["渲染结束"](nsf)
    end
    self.模型 = nsf["到精灵"](nsf)
  end
end
function 特性格子:详情打开(x, y, w, h)
  __UI弹出["技能详情"]["置可见"](__UI弹出["技能详情"], true, true)
  __UI弹出["技能详情"]["技能文本"]["清空"](__UI弹出["技能详情"]["技能文本"])
  __UI弹出["技能详情"]["打开"](__UI弹出["技能详情"], self.数据, x - 240, y - 125, 240, 300)
end
function 特性格子:更新(dt)
end
function 特性格子:显示(x, y)
  if self.模型 then
    self.模型["显示"](self.模型, x + self.py.x, y + self.py.y)
  end
end
return 特性格子
