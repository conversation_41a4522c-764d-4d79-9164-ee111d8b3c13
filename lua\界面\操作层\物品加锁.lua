--[[
LastEditTime: 2024-10-22 14:48:06
--]]



local 物品加锁 = 窗口层:创建窗口("物品加锁", 0,0, 320, 300)
function 物品加锁:初始化()
  self:置精灵(置窗口背景("物品加锁", 23, 0, 297, 300))
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  self.模型格子 = __UI模型格子.创建()
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
  
end



function 物品加锁:打开()
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.状态="物品"
  self.分类="道具"
  self.起点=0
  self.选中=nil
  self:道具刷新()
end



function 物品加锁:重置抓取()
  self.分类="道具"
  self.起点=0
  self.物品网格.选中编号=nil
  self.物品网格.焦点编号=nil
  self.选中=nil
  self:道具刷新()
end


function 物品加锁:道具刷新()
  self.模型格子:清空()

  if self.状态~="宝宝" then
      local 物品={}
      for n = 1, 20 do
        if self.状态=="物品" then
            if self.分类=="道具" then
                物品[n]=_tp.道具列表[n+self.起点]
            elseif self.分类=="行囊" then
                物品[n]=_tp.行囊列表[n]
            elseif self.分类=="法宝" then
                  物品[n]=角色信息.法宝[n]
            end
        elseif self.状态=="装备" then
              物品[n]=角色信息.装备[n]
        elseif self.状态=="灵饰" then
              物品[n]=角色信息.灵饰[n]
        end
      end
      self.物品网格:置物品(物品)
  end
  self:显示设置()
end



function 物品加锁:显示设置()
  self.图像=nil
  self[self.状态]:置选中(true)
  self.加锁:置禁止(true)
  self.解锁:置禁止(true)
  self.装备网格:置数据()
  self.物品控件:置可见(false)
  self.名称选择:置可见(false)
  self.名称滑块:置可见(false)
  self.装备网格:置可见(false)
  self.物品网格:置坐标(42,60)
  self.加锁:置坐标(31,273)
  self.解锁:置坐标(88,273)
  self.全面加锁:置坐标(145,273)
  self.全面解锁:置坐标(232,273)
  if self.状态=="宝宝" then
      self.物品网格:置可见(false)
      self.名称选择:置可见(true)
      self.名称滑块:置可见(true)
      self.装备网格:置可见(true)
      self.名称选择:置数据()
      self.图像 =self:创建纹理精灵(function()
        取白色背景(0, 0, 140, 134, true):显示(33, 30)
        蓝白标题背景(125, 180, true):显示(178, 30) 
        标题字体:置颜色(255,255,255,255):取图像("宝宝列表"):显示(210,34)
        文本字体:置颜色(255,255,255,255)
        文本字体:取图像("气血"):显示(33,170)
        文本字体:取图像("魔法"):显示(33,195)
        取输入背景(0, 0, 112, 22):显示(63, 166)
        取输入背景(0, 0, 112, 22):显示(63, 191)
        if self.选中 and 角色信息 and 角色信息.宝宝列表[self.选中] then
            self.加锁:置禁止(false)
            self.解锁:置禁止(false)
            self.模型格子:置数据(角色信息.宝宝列表[self.选中], "召唤兽", 105,140)
            self.名称选择:置选中(self.选中)
            self.装备网格:置数据(角色信息.宝宝列表[self.选中].装备)
            文本字体:置颜色(0,0,0,0)
            文本字体:取图像(角色信息.宝宝列表[self.选中].气血.."/"..角色信息.宝宝列表[self.选中].最大气血):显示(68,170)
            文本字体:取图像(角色信息.宝宝列表[self.选中].魔法.."/"..角色信息.宝宝列表[self.选中].最大魔法):显示(68,195)
        end
        
      end,1
    )




  else
      self.物品网格:置可见(true)
      if self.状态=="物品" then
          self.物品控件:置可见(true)
          self.物品控件.道具控件:置可见(false)
          if self.分类=="道具" then
              self.物品控件.道具控件:置可见(true)
              self.物品控件[self.分类]:置选中(true)
              for i=1,5 do
                  if self.起点==(i-1)*20 then
                    self.物品控件.道具控件["按钮"..i]:置选中(true)
                  end
              end
          end
      else
          self.加锁:置坐标(31,268)
          self.解锁:置坐标(88,268)
          self.全面加锁:置坐标(145,268)
          self.全面解锁:置坐标(232,268)
          self.物品网格:置坐标(42,45)
      end
      local 物品 = self.物品网格:选中物品()
      if 物品 and 物品.物品 and self.物品网格:选中()~=0 then
          self.加锁:置禁止(false)
          self.解锁:置禁止(false)
      end
  end


 
 
end


function 物品加锁:更新(dt)
  if self.模型格子 and self.状态=="宝宝" then
      self.模型格子:更新(dt)
  end
  
end

function 物品加锁:显示(x,y)
      if self.图像 then
        self.图像:显示(x,y)
      end
      if self.模型格子 and self.状态=="宝宝" then
            self.模型格子:显示(x, y)
      end

end


local 物品网格 = 物品加锁:创建道具网格("物品网格", 42, 60)
function 物品网格:获得鼠标(x, y,a)
      local 物品 = self:焦点物品()
      if 物品 and 物品.物品  then
  
          __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
      end
end
function 物品网格:左键弹起(x, y)
      local 物品 = self:选中物品()
      if 物品 and 物品.物品 and self:选中()~=0 then
            物品加锁.加锁:置禁止(false)
            物品加锁.解锁:置禁止(false)
            if __手机 then
                __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
            end
      end
end

---------------------------------------------------------------------------------
local 物品控件 = 物品加锁:创建控件("物品控件", 23, 30,297,30)
local 道具1=物品控件:创建红色单选按钮("道具","道具",8,4,50,22)
function 道具1:左键弹起(x, y)
    if 物品加锁.分类~="道具" then
        物品加锁.分类="道具"
        物品加锁.起点=0
        物品加锁:道具刷新()
    end
end
local 行囊=物品控件:创建红色单选按钮("行囊","行囊",61,4,50,22)
function 行囊:左键弹起(x, y)
    if 物品加锁.分类~="行囊" then
        物品加锁.分类="行囊"
        物品加锁.起点=0
        local 数量 = 0
        if _tp.行囊列表 then
            for k, v in pairs(_tp.行囊列表) do
              数量=数量+1
            end
        end
        if 数量==0 then
            请求服务(3700)
        else
            物品加锁:道具刷新()
        end
    end
end
local 法宝=物品控件:创建红色单选按钮("法宝","法宝",116,4,50,22)
function 法宝:左键弹起(x, y)
    if 物品加锁.分类~="法宝" then
        物品加锁.分类="法宝"
        物品加锁.起点=0
        物品加锁:道具刷新()
    end
end
local 道具控件 = 物品控件:创建控件("道具控件", 170, 0,125,30)
for i = 1, 5 do
      local 临时按钮=道具控件:创建蓝色单选按钮(i,"按钮"..i,(i-1)*25,4,22,22)
      function 临时按钮:左键弹起(x, y)
          if 物品加锁.分类=="道具" and 物品加锁.起点~=(i-1)*20 then
              物品加锁.起点=(i-1)*20
              物品加锁:道具刷新()
          end
      end
end
--------------------------------------------------


local 名称选择 = 物品加锁:创建列表("名称选择", 180, 57, 113, 150)
function 名称选择:初始化()
    self.行高度= 37
    self.行间距 = 3
end
local 滑块=物品加锁:创建竖向滑块("名称滑块",293,55,10,155,true)
名称选择:绑定滑块(滑块.滑块)
--local 滑块=名称选择:创建竖向滑块("名称滑块",180,35,10,190)

function 名称选择:置数据()
      self:清空()
      for i, v in ipairs(角色信息.宝宝列表) do

          self:添加():创建纹理精灵(function()
            __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
            local lssj = 取头像(v.模型)
            __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
            if v.加锁 then
              __res:取资源动画("jszy/xjiem",0X85655274,"图像"):显示(100,20)
            end
            文本字体:置颜色(0,0,0,255)
            文本字体:取图像(v.名称):显示(40,4)
            文本字体:取图像(v.等级.."级"):显示(40,20)
          end)
      end
end

  
function 名称选择:左键弹起(x, y, i)
      if 角色信息.宝宝列表[i] then
         if __手机 then
              local 事件 =function (编号)
                  if 编号==1 then
                      窗口层.召唤兽查看:打开(角色信息.宝宝列表[i])
                  elseif 编号==2 then
                        物品加锁.选中 = i
                        物品加锁:显示设置()
                  end
              end
              __UI弹出.临时按钮:打开({"查看","选择"},事件,x,y)
        else
            物品加锁.选中 = i
            物品加锁:显示设置()
        end
      end
end

function 名称选择:右键弹起(x, y, i)
    if 角色信息.宝宝列表[i]  then
        窗口层.召唤兽查看:打开(角色信息.宝宝列表[i])
    end
end

local 装备网格 = 物品加锁:创建网格("装备网格", 41, 217, 255, 50)
function 装备网格:初始化()
  self:创建格子(50, 50, 17, 17,1,4)
end




function 装备网格:置数据(数据)
  if not 数据 then 数据={} end
    for i = 1, #self.子控件 do
        local lssj = __物品格子:创建()
        lssj:置物品(数据[i],50,50,nil,true)
        self.子控件[i]:置精灵(lssj)
    end
end

function 装备网格:获得鼠标(x,y,a)
    self.子控件[a]._spr.焦点=true
    if self.子控件[a]._spr and self.子控件[a]._spr.物品  then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end
function 装备网格:失去鼠标(x,y)
    for i = 1, #self.子控件 do
        self.子控件[i]._spr.焦点=nil
    end
end
function 装备网格:左键弹起(x, y, a)
    if self.子控件[a]._spr and self.子控件[a]._spr.物品 and __手机 then
          __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end


local 加锁=物品加锁:创建红色按钮("加锁","加锁",31,273,50,22)
function 加锁:左键弹起(x, y)
         
        if 物品加锁.状态=="宝宝" then
            if not 物品加锁.选中 or 物品加锁.选中==0 then
                __UI弹出.提示框:打开("#Y请先选中要设置的召唤兽")
            else
                请求服务(3751,{操作="加锁",主类=物品加锁.状态,分类=物品加锁.分类,道具=物品加锁.选中})
            end
        else
            local 编号 = 物品加锁.物品网格:选中()
            if not 编号 or 编号==0 then
                  __UI弹出.提示框:打开("#Y请先选中要设置的物品")
            else
                    请求服务(3751,{操作="加锁",主类=物品加锁.状态,分类=物品加锁.分类,道具=编号+物品加锁.起点})
            end
        end
end

local 解锁=物品加锁:创建红色按钮("解锁","解锁",88,273,50,22)
function 解锁:左键弹起(x, y)
          if 物品加锁.状态=="宝宝" then
              if not 物品加锁.选中 or 物品加锁.选中==0 then
                  __UI弹出.提示框:打开("#Y请先选中要设置的召唤兽")
              else
                  请求服务(3751,{操作="解锁",主类=物品加锁.状态,分类=物品加锁.分类,道具=物品加锁.选中})
              end
          else
              local 编号 = 物品加锁.物品网格:选中()
              if not 编号 or 编号==0 then
                    __UI弹出.提示框:打开("#Y请先选中要设置的物品")
              else
                      请求服务(3751,{操作="解锁",主类=物品加锁.状态,分类=物品加锁.分类,道具=编号+物品加锁.起点})
              end
          end
end

local 全面加锁=物品加锁:创建红色按钮("全面加锁","全面加锁",145,273,80,22)
function 全面加锁:左键弹起(x, y)
        请求服务(3751,{操作="全面加锁",分类=物品加锁.分类,主类=物品加锁.状态})
end 

local 全面解锁=物品加锁:创建红色按钮("全面解锁","全面解锁",232,273,80,22)
function 全面解锁:左键弹起(x, y)
      请求服务(3751,{操作="全面解锁",分类=物品加锁.分类,主类=物品加锁.状态})
end

local 道具=物品加锁:创建单选按钮("物品",0,18)
function 道具:初始化()
        self:创建按钮精灵(__res:取资源动画("dlzy",0xF6B14F50),nil,"道\n具")
end

function 道具:左键弹起(x, y)
      if 物品加锁.状态~="物品" then
          物品加锁.状态="物品"
          物品加锁:重置抓取()
      end
end

local 召唤兽=物品加锁:创建单选按钮("宝宝",0,87)
function 召唤兽:初始化()
        self:创建按钮精灵(__res:取资源动画("dlzy",0xF6B14F50),nil,"召\n唤\n兽")
end

function 召唤兽:左键弹起(x, y)
      if  物品加锁.状态~="宝宝" then
            物品加锁.状态="宝宝"
            物品加锁:重置抓取()
      end
end

local 装备=物品加锁:创建单选按钮("装备",0,156)
function 装备:初始化()
        self:创建按钮精灵(__res:取资源动画("dlzy",0xF6B14F50),nil,"装\n备")
end

function 装备:左键弹起(x, y)
       if 物品加锁.状态~="装备" then
            物品加锁.状态="装备"
            物品加锁:重置抓取()
        end
end




local 灵饰=物品加锁:创建单选按钮("灵饰",0,225)

function 灵饰:初始化()
      self:创建按钮精灵(__res:取资源动画("dlzy",0xF6B14F50),nil,"灵\n饰")
end

function 灵饰:左键弹起(x, y)
        if 物品加锁.状态~="灵饰" then
            物品加锁.状态="灵饰"
            物品加锁:重置抓取()
        end
end

local 关闭 = 物品加锁:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  物品加锁:置可见(false)
end

