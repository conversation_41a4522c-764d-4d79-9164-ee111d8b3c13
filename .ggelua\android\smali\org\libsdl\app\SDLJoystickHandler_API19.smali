.class Lorg/libsdl/app/SDLJoystickHandler_API19;
.super Lorg/libsdl/app/SDLJoystickHandler_API16;
.source "SDLControllerManager.java"


# direct methods
.method constructor <init>()V
    .locals 0

    .line 328
    invoke-direct {p0}, Lorg/libsdl/app/SDLJoystickHandler_API16;-><init>()V

    return-void
.end method


# virtual methods
.method public getAxisMask(Ljava/util/List;)I
    .locals 7
    .annotation system Ldalvik/annotation/Signature;
        value = {
            "(",
            "Ljava/util/List<",
            "Landroid/view/InputDevice$MotionRange;",
            ">;)I"
        }
    .end annotation

    .line 344
    .local p1, "ranges":Ljava/util/List;, "Ljava/util/List<Landroid/view/InputDevice$MotionRange;>;"
    const/4 v0, 0x0

    .line 345
    .local v0, "axis_mask":I
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x2

    if-lt v1, v2, :cond_0

    .line 347
    or-int/lit8 v0, v0, 0x3

    .line 349
    :cond_0
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x4

    if-lt v1, v2, :cond_1

    .line 351
    or-int/lit8 v0, v0, 0xc

    .line 353
    :cond_1
    invoke-interface {p1}, Ljava/util/List;->size()I

    move-result v1

    const/4 v2, 0x6

    if-lt v1, v2, :cond_2

    .line 355
    or-int/lit8 v0, v0, 0x30

    .line 359
    :cond_2
    const/4 v1, 0x0

    .line 360
    .local v1, "have_z":Z
    const/4 v2, 0x0

    .line 361
    .local v2, "have_past_z_before_rz":Z
    invoke-interface {p1}, Ljava/util/List;->iterator()Ljava/util/Iterator;

    move-result-object v3

    :goto_0
    invoke-interface {v3}, Ljava/util/Iterator;->hasNext()Z

    move-result v4

    if-eqz v4, :cond_5

    invoke-interface {v3}, Ljava/util/Iterator;->next()Ljava/lang/Object;

    move-result-object v4

    check-cast v4, Landroid/view/InputDevice$MotionRange;

    .line 362
    .local v4, "range":Landroid/view/InputDevice$MotionRange;
    invoke-virtual {v4}, Landroid/view/InputDevice$MotionRange;->getAxis()I

    move-result v5

    .line 363
    .local v5, "axis":I
    const/16 v6, 0xb

    if-ne v5, v6, :cond_3

    .line 364
    const/4 v1, 0x1

    goto :goto_1

    .line 365
    :cond_3
    if-le v5, v6, :cond_4

    const/16 v6, 0xe

    if-ge v5, v6, :cond_4

    .line 366
    const/4 v2, 0x1

    .line 368
    .end local v4    # "range":Landroid/view/InputDevice$MotionRange;
    .end local v5    # "axis":I
    :cond_4
    :goto_1
    goto :goto_0

    .line 369
    :cond_5
    if-eqz v1, :cond_6

    if-eqz v2, :cond_6

    .line 372
    const v3, 0x8000

    or-int/2addr v0, v3

    .line 374
    :cond_6
    return v0
.end method

.method public getButtonMask(Landroid/view/InputDevice;)I
    .locals 6
    .param p1, "joystickDevice"    # Landroid/view/InputDevice;

    .line 379
    const/4 v0, 0x0

    .line 380
    .local v0, "button_mask":I
    const/16 v1, 0x26

    new-array v2, v1, [I

    fill-array-data v2, :array_0

    .line 422
    .local v2, "keys":[I
    new-array v1, v1, [I

    fill-array-data v1, :array_1

    .line 463
    .local v1, "masks":[I
    invoke-virtual {p1, v2}, Landroid/view/InputDevice;->hasKeys([I)[Z

    move-result-object v3

    .line 464
    .local v3, "has_keys":[Z
    const/4 v4, 0x0

    .local v4, "i":I
    :goto_0
    array-length v5, v2

    if-ge v4, v5, :cond_1

    .line 465
    aget-boolean v5, v3, v4

    if-eqz v5, :cond_0

    .line 466
    aget v5, v1, v4

    or-int/2addr v0, v5

    .line 464
    :cond_0
    add-int/lit8 v4, v4, 0x1

    goto :goto_0

    .line 469
    .end local v4    # "i":I
    :cond_1
    return v0

    :array_0
    .array-data 4
        0x60
        0x61
        0x63
        0x64
        0x4
        0x52
        0x6e
        0x6c
        0x6a
        0x6b
        0x66
        0x67
        0x13
        0x14
        0x15
        0x16
        0x6d
        0x17
        0x68
        0x69
        0x62
        0x65
        0xbc
        0xbd
        0xbe
        0xbf
        0xc0
        0xc1
        0xc2
        0xc3
        0xc4
        0xc5
        0xc6
        0xc7
        0xc8
        0xc9
        0xca
        0xcb
    .end array-data

    :array_1
    .array-data 4
        0x1
        0x2
        0x4
        0x8
        0x10
        0x40
        0x20
        0x40
        0x80
        0x100
        0x200
        0x400
        0x800
        0x1000
        0x2000
        0x4000
        0x10
        0x1
        0x8000
        0x10000
        0x20000
        0x40000
        0x100000
        0x200000
        0x400000
        0x800000
        0x1000000
        0x2000000
        0x4000000
        0x8000000
        0x10000000
        0x20000000
        0x40000000    # 2.0f
        -0x80000000
        -0x1
        -0x1
        -0x1
        -0x1
    .end array-data
.end method

.method public getProductId(Landroid/view/InputDevice;)I
    .locals 1
    .param p1, "joystickDevice"    # Landroid/view/InputDevice;

    .line 332
    invoke-virtual {p1}, Landroid/view/InputDevice;->getProductId()I

    move-result v0

    return v0
.end method

.method public getVendorId(Landroid/view/InputDevice;)I
    .locals 1
    .param p1, "joystickDevice"    # Landroid/view/InputDevice;

    .line 337
    invoke-virtual {p1}, Landroid/view/InputDevice;->getVendorId()I

    move-result v0

    return v0
.end method
