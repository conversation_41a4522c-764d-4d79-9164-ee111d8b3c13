

local t={}
local x,y=-485,-15
for l=1,4 do
    t[l]={}
    local x1,y1=x,y
    for h=1-2,5+2 do
        t[l][h] = 生成XY(x1,y1)
        x1=x1+65;y1=y1-40
    end
    x=x+45;y=y+45
end

local x,y= -130,200
for l=5,8 do
    t[l]={}
    local x1,y1=x,y
    for h=1-2,5+2 do
        t[l][h] = 生成XY(x1,y1)
        x1=x1+65;y1=y1-40
    end
    x=x+40;y=y+40
end


local r = {}
r.普通={
    {t[6][3],t[6][4],t[6][2],t[6][5],t[6][1],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1],
        t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[3][3],t[3][2],t[3][4],t[3][1],t[3][5],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5],
        t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}


r.天覆阵={
    {t[7][3],t[6][4],t[6][2],t[6][5],t[6][1],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[2][3],t[3][2],t[3][4],t[3][1],t[3][5],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.地载阵={
   {t[7][3],t[6][3],t[7][4],t[7][2],t[8][3],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[2][3],t[3][3],t[2][2],t[2][4],t[1][3],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.风扬阵={
    {t[7][3],t[7][4],t[7][2],t[6][4],t[6][2],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[2][3],t[2][2],t[2][4],t[3][2],t[3][4],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.云垂阵={
    {t[8][3],t[6][4],t[6][2],t[6][5],t[6][1],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[1][3],t[3][2],t[3][4],t[3][1],t[3][5],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.龙飞阵={
    {t[7][3],t[8][3],t[6][5],t[6][2],t[6][3],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[2][3],t[1][3],t[3][1],t[3][4],t[3][3],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.虎翼阵={
    {t[8][3],t[7][4],t[7][2],t[6][5],t[6][1],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[1][3],t[2][2],t[2][4],t[3][1],t[3][5],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.鸟翔阵={
    {t[6][3],t[7][4],t[7][2],t[6][5],t[6][1],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[3][3],t[2][2],t[2][4],t[3][1],t[3][5],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.蛇蟠阵={
    {t[7][3],t[7][2],t[7][4],t[6][2],t[8][3],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[2][3],t[2][4],t[2][2],t[3][4],t[1][3],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.鹰啸阵={
    {t[7][3],t[6][4],t[6][2],t[6][3],t[8][3],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[2][3],t[3][2],t[3][4],t[3][3],t[1][3],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}
r.雷绝阵={
    {t[8][3],t[7][2],t[7][4],t[6][2],t[6][4],
        t[5][3],t[5][4],t[5][2],t[5][5],t[5][1]
        ,t[5][0],t[5][6],t[5][-1],t[5][7],t[6][0],t[6][6]},--己方召唤
    {t[1][3],t[2][4],t[2][2],t[3][4],t[3][2],
        t[4][3],t[4][2],t[4][4],t[4][1],t[4][5]
        ,t[4][0],t[4][6],t[4][-1],t[4][7],t[3][0],t[3][6]},--敌方召唤

}



return r




-- local 敌方 = {
--     [6] = {x = 235, y = 235},
--     [11] = {x = 280, y = 280},
--     [12] = {x = 215, y = 320},
--     [13] = {x = 345, y = 240},
--     [14] = {x = 150, y = 360},
--     [15] = {x = 410, y = 200},
--     [16] = {x = 385, y = 70},
--     [17] = {x = 430, y = 125},
--     [18] = {x = 485, y = 170},
--     [19] = {x = 40, y = 355},
--     [20] = {x = 90, y = 400},

-- }
-- local 我方 = {
--       [6] = {x = 280, y = 255},
--       [11] = {x = 350, y = 300},
--       [12] = {x = 295, y = 355},
--       [13] = {x = 405, y = 245},
--       [14] = {x = 240, y = 410},
--       [15] = {x = 460, y = 190},
--       [16] = {x = 70, y = 375},
--       [17] = {x = 140, y = 420},
--       [18] = {x = 210, y = 465},
--       [19] = {x = 445, y = 100},
--       [20] = {x = 515, y = 145}
-- }










-- local 阵法位置 = {}

-- 阵法位置.普通 = {
--       { [1] = {x = 210, y = 210},--70 55
--         [2] = {x = 160, y = 265},
--         [3] = {x = 265, y = 155},
--         [4] = {x = 115, y = 320},
--         [5] = {x = 320, y = 100},
--         [7] = {x = 225, y = 310},
--         [8] = {x = 335, y = 200},
--         [9] = {x = 170, y = 365},
--         [10] = {x = 390, y = 145},
--       },
--       { [1] = {x = 190, y = 190},--45 --45
--         [2] = {x = 125, y = 230},
--         [3] = {x = 255, y = 150},
--         [4] = {x = 60 , y = 270},
--         [5] = {x = 320, y = 110},
--         [7] = {x = 170, y = 275},
--         [8] = {x = 300, y = 195},
--         [9] = {x = 105, y = 315},
--         [10] = {x = 365, y = 155},
--       }
-- }


-- 阵法位置.天覆阵 = {
--     {   [1] = {x = 140, y = 155},
--         [2] = {x = 160, y = 265},
--         [3] = {x = 265, y = 155},
--         [4] = {x = 115, y = 320},
--         [5] = {x = 320, y = 100},
--         [7] = {x = 225, y = 310},
--         [8] = {x = 335, y = 200},
--         [9] = {x = 170, y = 365},
--         [10] = {x = 390, y = 145},
--     },
--     {   [1] = {x = 145, y = 145},
--         [2] = {x = 125, y = 230},
--         [3] = {x = 255, y = 150},
--         [4] = {x = 60 , y = 270},
--         [5] = {x = 320, y = 110},
--         [7] = {x = 170, y = 275},
--         [8] = {x = 300, y = 195},
--         [9] = {x = 105, y = 315},
--         [10] = {x = 365, y = 155},
--       }
-- }

-- 阵法位置.云垂阵 = {
--     {   [1] = {x = 70, y = 100},
--         [2] = {x = 160, y = 265},
--         [3] = {x = 265, y = 155},
--         [4] = {x = 115, y = 320},
--         [5] = {x = 320, y = 100},
--         [7] = {x = 225, y = 310},
--         [8] = {x = 335, y = 200},
--         [9] = {x = 170, y = 365},
--         [10] = {x = 390, y = 145},
--     },
--     {   [1] = {x = 100, y = 100},
--         [2] = {x = 125, y = 230},
--         [3] = {x = 255, y = 150},
--         [4] = {x = 60 , y = 270},
--         [5] = {x = 320, y = 110},
--         [7] = {x = 170, y = 275},
--         [8] = {x = 300, y = 195},
--         [9] = {x = 105, y = 315},
--         [10] = {x = 365, y = 155},
--     }
-- }

-- 阵法位置.风扬阵 = {
--      {  [1] = {x = 140, y = 155},
--         [2] = {x = 90, y = 210},
--         [3] = {x = 195, y = 100},
--         [4] = {x = 160, y = 265},
--         [5] = {x = 265, y = 155},
--         [7] = {x = 170, y = 365},
--         [8] = {x = 390, y = 145},
--         [9] = {x = 225, y = 310},
--         [10] = {x = 335, y = 200},
--       },
--       { [1] = {x = 145, y = 145},
--         [2] = {x = 80, y = 185},
--         [3] = {x = 210, y = 105},
--         [4] = {x = 125, y = 230},
--         [5] = {x = 255, y = 150},
--         [7] = {x = 105, y = 315},
--         [8] = {x = 365, y = 155},
--         [9] = {x = 170, y = 275},
--         [10] = {x = 300, y = 195},
--       }
-- }

-- 阵法位置.虎翼阵 = {
--       { [1] = {x = 70, y = 100},
--         [2] = {x = 90, y = 210},
--         [3] = {x = 195, y = 100},
--         [4] = {x = 115, y = 320},
--         [5] = {x = 320, y = 100},
--         [7] = {x = 225, y = 310},
--         [8] = {x = 335, y = 200},
--         [9] = {x = 170, y = 365},
--         [10] = {x = 390, y = 145},
--       },
--       { [1] = {x = 100, y = 100},
--         [2] = {x = 80, y = 185},
--         [3] = {x = 210, y = 105},
--         [4] = {x = 60 , y = 270},
--         [5] = {x = 320, y = 110},
--         [7] = {x = 170, y = 275},
--         [8] = {x = 300, y = 195},
--         [9] = {x = 105, y = 315},
--         [10] = {x = 365, y = 155},
--       }
-- }

-- 阵法位置.鸟翔阵 = {
--       { [1] = {x = 210, y = 210},
--         [2] = {x = 90, y = 210},
--         [3] = {x = 195, y = 100},
--         [4] = {x = 115, y = 320},
--         [5] = {x = 320, y = 100},
--         [7] = {x = 225, y = 310},
--         [8] = {x = 335, y = 200},
--         [9] = {x = 170, y = 365},
--         [10] = {x = 390, y = 145},
--       },
--       { [1] = {x = 190, y = 190},
--         [2] = {x = 80, y = 185},
--         [3] = {x = 210, y = 105},
--         [4] = {x = 60 , y = 270},
--         [5] = {x = 320, y = 110},
--         [7] = {x = 170, y = 275},
--         [8] = {x = 300, y = 195},
--         [9] = {x = 105, y = 315},
--         [10] = {x = 365, y = 155},
--       }
-- }

-- 阵法位置.地载阵 = {
--       { [1] = {x = 140, y = 155},
--         [2] = {x = 210, y = 210},
--         [3] = {x = 90, y = 210},
--         [4] = {x = 195, y = 100},
--         [5] = {x = 70, y = 100},
--         [7] = {x = 170, y = 365},
--         [8] = {x = 225, y = 310},
--         [9] = {x = 335, y = 200},
--         [10] = {x = 390, y = 145},
--       },
--       { [1] = {x = 145, y = 145},
--         [2] = {x = 190, y = 190},
--         [3] = {x = 80, y = 185},
--         [4] = {x = 210, y = 105},
--         [5] = {x = 100, y = 100},
--         [7] = {x = 105, y = 315},
--         [8] = {x = 170, y = 275},
--         [9] = {x = 300, y = 195},
--         [10] = {x = 365, y = 155},
--       }
-- }

-- 阵法位置.龙飞阵 = {
--       { [1] = {x = 140, y = 155},
--         [2] = {x = 70, y = 100},
--         [3] = {x = 115, y = 320},
--         [4] = {x = 265, y = 155},
--         [5] = {x = 210, y = 210},
--         [7] = {x = 225, y = 310},
--         [8] = {x = 170, y = 365},
--         [9] = {x = 335, y = 200},
--         [10] = {x = 390, y = 145},
--       },
--       { [1] = {x = 145, y = 145},
--         [2] = {x = 100, y = 100},
--         [3] = {x = 60 , y = 270},
--         [4] = {x = 255, y = 150},
--         [5] = {x = 190, y = 190},
--         [7] = {x = 170, y = 275},
--         [8] = {x = 105, y = 315},
--         [9] = {x = 300, y = 195},
--         [10] = {x = 365, y = 155},
--       }
-- }




-- 阵法位置.蛇蟠阵 = {
--       { [1] = {x = 140, y = 155},
--         [2] = {x = 195, y = 100},
--         [3] = {x = 90, y = 210},
--         [4] = {x = 265, y = 155},
--         [5] = {x = 70, y = 100},
--         [7] = {x = 390, y = 145},
--         [8] = {x = 225, y = 310},
--         [9] = {x = 335, y = 200},
--         [10] = {x = 170, y = 365},
--       },
--       { [1] = {x = 145, y = 145},
--         [2] = {x = 210, y = 105},
--         [3] = {x = 80, y = 185},
--         [4] = {x = 255, y = 150},
--         [5] = {x = 100, y = 100},
--         [7] = {x = 365, y = 155},
--         [8] = {x = 170, y = 275},
--         [9] = {x = 300, y = 195},
--         [10] = {x = 105, y = 315},
--       }
-- }

-- 阵法位置.鹰啸阵 = {
--       { [1] = {x = 140, y = 155},
--         [2] = {x = 265, y = 155},
--         [3] = {x = 160, y = 265},
--         [4] = {x = 210, y = 210},
--         [5] = {x = 70, y = 100},
--         [7] = {x = 335, y = 200},
--         [8] = {x = 225, y = 310},
--         [9] = {x = 170, y = 365},
--         [10] = {x = 390, y = 145},
--       },
--       { [1] = {x = 145, y = 145},
--         [2] = {x = 255, y = 150},
--         [3] = {x = 125, y = 230},
--         [4] = {x = 190, y = 190},
--         [5] = {x = 100, y = 100},
--         [7] = {x = 300, y = 195},
--         [8] = {x = 170, y = 275},
--         [9] = {x = 105, y = 315},
--         [10] = {x = 365, y = 155},
--       }
-- }

-- 阵法位置.雷绝阵 = {
--       { [1] = {x = 70, y = 100},
--         [2] = {x = 195, y = 100},
--         [3] = {x = 90, y = 210},
--         [4] = {x = 265, y = 155},
--         [5] = {x = 160, y = 265},
--         [7] = {x = 390, y = 145},
--         [8] = {x = 170, y = 365},
--         [9] = {x = 335, y = 200},
--         [10] = {x = 225, y = 310},
--       },
--       { [1] = {x = 100, y = 100},
--         [2] = {x = 210, y = 105},
--         [3] = {x = 80, y = 185},
--         [4] = {x = 255, y = 150},
--         [5] = {x = 125, y = 230},
--         [7] = {x = 365, y = 155},
--         [8] = {x = 105, y = 315},
--         [9] = {x = 300, y = 195},
--         [10] = {x = 170, y = 275},
--       }
-- }





-- for k,v in pairs(阵法位置) do
--     for i,z in pairs(我方) do
--         v[1][i]=z
--     end
--     for i,z in pairs(敌方) do
--         v[2][i]=z
--     end
-- end
