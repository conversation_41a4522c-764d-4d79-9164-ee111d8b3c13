--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-31 22:57:27
    @Last Modified by: GGELUA
    @Last Modified time: 2022-11-01 06:38:27
--]]
local 帮派技能学习 = __UI界面.窗口层:创建我的窗口("帮派技能学习", 98 + abbr.py.x, 14 + abbr.py.y, 713, 480)

function 帮派技能学习:初始化()
  local nsf = require("SDL.图像")(713, 480)
  if nsf:渲染开始() then
    置窗口背景("帮派技能学习", 0, 12, 689, 442, true):显示(0, 0)
    取白色背景(0, 0, 437, 290, true)["显示"](取白色背景(0, 0, 437, 290, true), 7, 4+48)
    取白色背景(0, 0, 233, 290, true)["显示"](取白色背景(0, 0, 233, 290, true), 447, 4+48)
    local lssj = 取输入背景(0, 0, 124, 23)
    字体18:置颜色(__取颜色("白色"))
    for _, v in ipairs({
      {
        name = "可用经验",
        x = 325,
        y = 357,

      },
      {
        name = "学习所需经验",
        x = 540+60,
        y = 357,
        input = true
      },
      {
        name = "可用现金",
        x = 325,
        y = 388,

      },
      {
        name = "学习所需金钱",
        x = 540+60,
        y = 388,
        input = true
      },
      {
        name = "储 备 金",
        x = 325,
        y = 419,
      },
      {
        name = "学习所需帮贡",
        x = 540+60,
        y = 419,
        input = true
      }
    }) do
      字体18:取图像(v.name):显示(v.x-314, v.y)
      local py=0
      if v.input then
        py=38
      end
      lssj:显示(v.x + 85-314+py, v.y - 2)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 帮派技能学习:打开(银子,储备,bpjn)
  self.帮派技能=bpjn
  self:置可见(true)
  
--  table.print(self.帮派技能)
  self:重载技能()
  -- table.print(bpjn)
  self.选中技能 = nil
end
function 帮派技能学习:重载技能()
  -- self.角色信息.辅助技能=角色信息.辅助技能
  self.辅助技能网格:置技能()
end



function 帮派技能学习:chongzhi(xh)
  local 临时消耗=xh
  if not 临时消耗 and self["选中技能"] then
    临时消耗=生活技能消耗(角色信息["辅助技能"][帮派技能学习["选中技能"]].等级 + 1,角色信息["辅助技能"][帮派技能学习["选中技能"]].名称)
  elseif not self["选中技能"] then
    return
  end
  local nsf = require("SDL.图像")(541, 98)
  if nsf:渲染开始() then
    字体18:置颜色(__取颜色("黑色"))
    字体18:取图像(角色信息.当前经验):显示(147-44,6+0*31)
    字体18:取图像(角色信息.银子):显示(147-44,6+1*31)
    字体18:取图像(角色信息.储备):显示(147-44,6+2*31)
    字体18:取图像(临时消耗.经验):显示(372+44,6+0*31)
    字体18:取图像(临时消耗.金钱):显示(372+44,6+1*31)
    字体18:取图像(临时消耗.帮贡.." / "..临时消耗.需求):显示(372+44,6+2*31)
    nsf:渲染结束()
  end
  -- print(123123)
  self.图像 = nsf:到精灵()
  self.图像:置中心(0, -351)
end

function 帮派技能学习:取消耗(a)
  self["技能文本"]["清空"](self["技能文本"])
  self["技能文本"]["置文本"](self["技能文本"], "#R" .. 角色信息["辅助技能"][a]["名称"])
  self["技能文本"]["置文本"](self["技能文本"], "#K" .. self["辅助技能网格"]["子控件"][a]._spr["数据"][1])
  local 临时消耗 = 生活技能消耗(角色信息["辅助技能"][a].等级 + 1,角色信息["辅助技能"][a].名称)--生活技能消耗(角色信息["辅助技能"][a]["等级"] + 1) --
  self["技能文本"]["置文本"](self["技能文本"], "#Y/学习消耗：" .. 临时消耗["经验"] .. "点人物经验、" .. 临时消耗["帮贡"] .. "点帮贡、" .. 临时消耗["需求"] .. "点需要帮贡、" .. 临时消耗["金钱"] .. "两银子")
  self:chongzhi(临时消耗)
end



local 辅助技能网格 = 帮派技能学习["创建网格"](帮派技能学习, "辅助技能网格", 20, 10+48, 437, 275)
function 辅助技能网格:初始化()
  self:创建格子(190, 61, 10, 10, 10, 2, true)
end
function 辅助技能网格:左键弹起(x, y, a, b, msg)
  if not self.子控件[a] or not self.子控件[a]._spr or not self.子控件[a]._spr["模型"] then
    return
  end
  帮派技能学习:取消耗(a)
  if 帮派技能学习["选中技能"] and self.子控件[帮派技能学习["选中技能"]] then
    self.子控件[帮派技能学习["选中技能"]]._spr["确定"] = nil
  end
  帮派技能学习["选中技能"] = a
  self.子控件[a]._spr["确定"] = true
end
function 辅助技能网格:置技能(是否选中)
  for i = 1, #辅助技能网格["子控件"] do
    local lssj = __技能格子2["创建"]()
    lssj["门派"] = "生活"
    local 上限=40

    for k,v in pairs(帮派技能学习.帮派技能) do
      if 角色信息.辅助技能[i] and k==角色信息.辅助技能[i].名称 then
        上限=v.当前
        break
      end
    end

    lssj["置数据"](lssj, 角色信息.辅助技能[i], 2, 2, 55, 55, 190, 61, true, true,上限)
    辅助技能网格["子控件"][i]["置精灵"](辅助技能网格["子控件"][i], lssj)
    if 是否选中 then
      self.子控件[是否选中]._spr["确定"] = true
    end
  end
end
local 技能文本 = 帮派技能学习["创建文本"](帮派技能学习, "技能文本", 448+11, 16+48, 219, 270)
function 技能文本:初始化()
end


local 学习 = 帮派技能学习:创建我的按钮(__res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(123, 41), "学习", 552, 382, "学习")
function 学习:左键弹起(x, y, msg)
  if 帮派技能学习.选中技能 and 帮派技能学习.选中技能~=0 then
    发送数据(45,{序列=帮派技能学习.选中技能})
  end
end


function 帮派技能学习:更新技能(序列,dj)
  if 帮派技能学习.选中技能==序列 then
    帮派技能学习:取消耗(帮派技能学习.选中技能)
    帮派技能学习.辅助技能网格:置技能(序列)
  end

end


local 关闭 = 帮派技能学习:创建我的按钮(__res:getPNGCC(1, 401, 0, 46, 46), "关闭", 662, 0)
function 关闭:左键弹起(x, y, msg)
  帮派技能学习:置可见(false)
  帮派技能学习.技能文本:清空()
end
