--[[
LastEditTime: 2024-09-18 10:19:05
--]]
local 任务追踪 = 界面层:创建控件("任务追踪")
function 任务追踪:初始化()
    self:背景设置()
    self.图标数据={}
    self.任务数据={}
    self.追踪列表:置数据()
    self.可初始化=true
    self.显示开关=true
end



function 任务追踪:背景设置()
    if __手机 then 
            self:置精灵()
            self:置宽高(195,self.追踪列表.高度+45)
            self:置坐标(引擎.宽度-200, 100)
            self.追踪列表:置坐标(30, 40)
    else
            self:置宽高(160,self.追踪列表.高度+25)
            self:创建纹理精灵(function()
                __res:getPNGCC(1, 680, 383, 217, 126):拉伸(217, 23):显示(0, 0)
                __res:取资源动画("pic","rwwh.png","图片"):显示(125, 2)
                标题字体:置颜色(255,240,225,255):取投影图像("任务追踪"):显示(53,3)
              end
            )
            self:置坐标(引擎.宽度-160, 140)
            self.追踪列表:置坐标(0, 25)
    end
end



local 时钟 = 任务追踪:创建按钮("时钟")
function 时钟:初始化()
    local tcp=__res:取资源动画("pic","rwsz.png","图片")
    if __手机 then
        tcp=__res:getPNGCC(2, 1037, 236, 54, 40)
        self:置坐标(30,0)
    else
        self:置坐标(5,0)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end


local 任务 = 任务追踪:创建按钮("任务")
function 任务:初始化()
    local tcp=__res:取资源动画("pic","rwsez.png","图片")
    if __手机 then
        tcp=__res:getPNGCC(2, 1093, 237, 106, 39)
        self:置坐标(87,0)
    else
        self:置坐标(30,1)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end

function 任务:左键弹起(x, y)
      if not _tp.战斗中 then
          if 窗口层.任务提示.是否可见 then
             窗口层.任务提示:打开()
          else
                请求服务(10)
          end
      end
end





local 开关 = 任务追踪:创建按钮("开关")
function 开关:初始化()
    local tcp=__res:取资源动画("pic","rwsh.png","图片")
    if __手机 then
        tcp=__res:getPNGCC(2, 1082, 406, 31, 64)
        self:置坐标(0,80)
    else
        self:置坐标(-(tcp.宽度+5),0)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end

function 开关:左键弹起(x, y, msg)
    if 任务追踪.显示开关 then
        self:置坐标(-(self.宽度+5), 0)
        任务追踪:置精灵()
        任务追踪.追踪列表:置可见(false)
        任务追踪.任务:置可见(false)
        任务追踪.时钟:置可见(false)
        任务追踪.显示开关=false
       
    else
        任务追踪.显示开关=true
        任务追踪.追踪列表:置可见(true)
        任务追踪.任务:置可见(true)
        任务追踪.时钟:置可见(true)
        if __手机 then
            self:置坐标(0,80)
        else
            self:置坐标(-(self.宽度+5), 0)
        end
        任务追踪:背景设置()
    end
end


function 任务追踪:刷新(mc,js,ms,zjsj)
    if mc=="摄妖香" then
        table.insert(self.图标数据, {名称="摄妖香",外框="橙色", 图标="摄妖香",介绍=js})
    elseif mc=="变身卡"  then
        table.insert(self.图标数据, {名称="变身卡",外框="绿色", 图标="变身卡",介绍=js})
    elseif  mc=="双倍时间" then
        table.insert(self.图标数据, {名称="双倍时间",外框="橙色", 图标="火",介绍=js})
    elseif  mc=="精修时间" then
        table.insert(self.图标数据, {名称="精修时间",外框="橙色", 图标="翅膀",介绍=js})
    elseif  mc=="罗羹效果" then
        table.insert(self.图标数据, {名称="罗羹效果",外框="橙色", 图标="心",介绍=js})
    elseif  mc=="一倍经验丹" then
        table.insert(self.图标数据, {名称="一倍经验丹",外框="橙色", 图标="火",介绍=js})
    elseif  mc=="双倍经验丹" then
        table.insert(self.图标数据, {名称="双倍经验丹",外框="橙色", 图标="翅膀",介绍=js})
    elseif  mc=="双倍掉宝符" then
        table.insert(self.图标数据, {名称="双倍掉宝符",外框="橙色", 图标="钻石",介绍=js})
    elseif mc=="离线经验加成" then
        table.insert(self.图标数据, {名称="离线经验加成",外框="黑色", 图标="宝箱",介绍=js})
    elseif mc=="离线储备加成" then
        table.insert(self.图标数据, {名称="离线储备加成",外框="黑色", 图标="宝箱2",介绍=js})
    else
        table.insert(self.任务数据,{名称=mc,介绍=js,描述=ms,取中间数据=zjsj})
    end
    界面层.状态图标.状态网格:置数据(self.图标数据)
    界面层.状态图标:置可见(true,true)
    self.追踪列表:置数据()
    if self.显示开关 then
        if __手机 then 
            self:置宽高(195,self.追踪列表.高度+45)
        else
            self:置宽高(160,self.追踪列表.高度+25)
        end
    end
end




local 追踪列表 = 任务追踪:创建列表('追踪列表')
function 追踪列表:初始化()
        self.选中精灵 = nil
        self.焦点精灵 = nil
        self.行间距 = 1
end

function 追踪列表:置数据()
    self:清空()
    local h = 0
    if #任务追踪.任务数据>0 then
        for i, v in ipairs(任务追踪.任务数据) do
            h =h + self:添加内容(v)
        end
    else
        local r = self:添加()
        local 名称 = r:创建文本('文本', 5, 2, self.宽度-10, 20)
        名称:置文字(标题字体)
        名称:置文本("#W当前追踪列表没有任务")
        if __手机 then
            r:置精灵(require('SDL.精灵')(0, 0, 0, self.宽度-2, 20):置颜色(20, 20, 60, 150))
        else
            r:置精灵(require('SDL.精灵')(0, 0, 0, self.宽度, 20):置颜色(20, 20, 60, 150))
        end
        h = 20
        r:置高度(20)
        r:置可见(true, true)
    end
    if h>240 then h = 240 end
    if __手机 then
        self:置宽高(165,h)
    else
        self:置宽高(160,h)
    end
end

function 追踪列表:添加内容(文本)
    local r = self:添加()
    local 名称 = r:创建文本('文本', 5, 2, self.宽度-10, 20)
    名称:置文字(标题字体)
    名称:置文本("#G" .. 文本.名称)
    local 内容 = r:丰富文本('内容', 5, 25, self.宽度-10,(self.高度-30),true)
    内容:置文字(文本字体)
    local 添加内容= ""
    if 文本.取中间数据~=nil and 文本.取中间数据.x~=nil then
        local 断句 = 分割文本(文本.介绍,"@")
        local 回调 = "#["..文本.取中间数据.名称.."@"..文本.取中间数据.x.."@"..文本.取中间数据.y.."@"..文本.取中间数据.地图.."$"..文本.取中间数据.名称.."#]"
        添加内容=断句[1].."#G#u"..回调.."#u#W"..断句[2]
    else
        添加内容=文本.介绍
    end
    local _,h=  内容:置文本(添加内容)
    内容.获得回调 = 追踪列表.获得回调
    内容.回调左键弹起 = 追踪列表.回调左键弹起
  --  内容.回调右键弹起 = 追踪列表.回调右键弹起
    内容:置高度(h)
    if __手机 then
        r:置精灵(require('SDL.精灵')(0, 0, 0, self.宽度-2, h+30):置颜色(20, 20, 60, 150))
    else
        r:置精灵(require('SDL.精灵')(0, 0, 0, self.宽度, h+30):置颜色(20, 20, 60, 150))
    end
    r:置高度(h+30)
    r:置可见(true, true)
    return h+30
end


function 追踪列表:获得回调(x,y,cb)

    local lssj = 分割文本(cb, "@")
    __UI弹出.自定义:打开(x+20,y+20,"#Y"..lssj[1].."在"..取地图名称(tonumber(lssj[4])).."("..lssj[2]..","..lssj[3]..")")
end

function 追踪列表:回调左键弹起(cb,x,y)
        local lssj = 分割文本(cb, "@")
        if __手机 then
            __UI弹出.自定义:打开(x+20,y+20,"#Y"..lssj[1].."在"..取地图名称(tonumber(lssj[4])).."("..lssj[2]..","..lssj[3]..")")
        end
        local 发送内容 = {名称=lssj[1],x=lssj[2],y=lssj[3],地图=tonumber(lssj[4])}
        请求服务(41,发送内容)

 end






