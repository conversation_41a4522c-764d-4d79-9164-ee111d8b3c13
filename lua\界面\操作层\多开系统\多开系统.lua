

local 多开系统 = 窗口层:创建窗口("多开系统",0,0, 620, 280)

function 多开系统:初始化()
        self:置精灵(置窗口背景("多开系统",0,0,620,280))
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-30, 5)
        else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-21, 5)
        end
end

function 多开系统:打开(数据)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
          return
    end
    self.选中=nil
    self:刷新(数据)
end
function 多开系统:刷新(数据)
      self.数据=数据
      self.队员网格:置数据(数据)
      self.图像=self:创建纹理精灵(function()
            for i = 1, 5 do
                取输入背景(0, 0, 116, 22):显示((i-1)*121,0)
                取输入背景(0, 0, 116, 22):显示((i-1)*121,25)
                取输入背景(0, 0, 116, 22):显示((i-1)*121,50)
                if 数据[i] then
                  文本字体:置颜色(39, 53, 81)
                  文本字体:取图像(数据[i].名称):显示((116 - 文本字体:取宽度(数据[i].名称)) // 2 + (i-1)*121, 4)
                  文本字体:取图像(数据[i].门派):显示((116 - 文本字体:取宽度(数据[i].门派)) // 2 + (i-1)*121, 29)
                  文本字体:取图像(数据[i].等级 .. "级"):显示((116 - 文本字体:取宽度(数据[i].等级 .. "级")) // 2 + (i-1)*121, 54)
                end
            end
      end,1,600,80)
end
function 多开系统:显示(x,y)
  if self.图像 then
    self.图像:显示(x+10,y+175)
  end
end



local 按钮设置={"创建角色","切换角色","角色参战","角色退出","角色操作","一键退出"}
for i,v in ipairs(按钮设置) do
      local 临时函数 = 多开系统:创建红色按钮(v, v, 20+(i-1)*125, 255,70,22)
      function 临时函数:左键弹起(x, y)
          if v =="创建角色" then
              窗口层.多开创建:打开()
              多开系统:置可见(false)
          elseif v =="一键退出" then
              请求服务(63,{参数=角色信息.数字id,文本="一键退出"})
          else
              if 多开系统.选中 and 多开系统.数据[多开系统.选中] then
                    请求服务(63,{参数=多开系统.数据[多开系统.选中].id,文本=v})
              end
          end
         
      end
end
local 队员网格 = 多开系统:创建网格("队员网格", 10, 30, 600, 145)
function 队员网格:初始化()
  self:创建格子(116,145,0,5,1,5)    
end
function 队员网格:左键弹起(x, y, a)
        if 多开系统.选中  and 多开系统.选中 ==a and 多开系统.数据[多开系统.选中] and self.子控件[a]._spr and self.子控件[a]._spr.数据 then
              请求服务(63,{参数=多开系统.数据[多开系统.选中].id,文本="角色参战"})
             --多开系统:置可见(false)
        else
              for k, v in pairs(self.子控件) do
                  if v._spr and v._spr.确定 then
                    v._spr.确定=nil
                  end
              end
              多开系统.选中=nil
              if 多开系统.数据[a] then
                  多开系统.选中 = a
                  self.子控件[a]._spr.确定 = true
              end
        end
end
function 队员网格:获得鼠标(x, y, a)
        for i, v in ipairs(self.子控件) do
              v._spr.焦点=nil
        end
        self.子控件[a]._spr.焦点=true
end
function 队员网格:失去鼠标(x, y)
        for i, v in ipairs(self.子控件) do
              v._spr.焦点=nil
        end
end
function 队员网格:置数据(数据)
    for i = 1, #self.子控件 do
          if 数据 and 数据[i] and _tp.队伍数据 then
              数据[i].已参战=nil
              for n=1,#_tp.队伍数据 do
                if 数据[i].id ==_tp.队伍数据[n].id then
                    数据[i].已参战 =true
                end
              end
          end
          local lssj = __队伍格子:创建()
          lssj:置数据(数据[i], i, "多开系统")
          self.子控件[i]:置精灵(lssj)
    end
end

function 队员网格:右键弹起(x, y)
    多开系统:置可见(false)
end
local 关闭 = 多开系统:创建关闭按钮("关闭")
function 关闭:左键弹起(x,y,msg)
    多开系统:置可见(false)
end