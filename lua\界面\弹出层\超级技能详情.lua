
if __手机 then
  __UI弹出.超级技能详情 = 界面:创建弹出窗口("超级技能详情", 0, 0, 0, 0)
else
  __UI弹出.超级技能详情 = 界面:创建提示控件("超级技能详情", 0, 0)
end
local 技能详情 = __UI弹出.超级技能详情 
function 技能详情:初始化()
end
function 技能详情:左键弹起()
  self:置可见(false)
end


function 技能详情:打开(x,y,数据)
  self:置可见(true)
  self.数据 = 数据
  self.图片=nil
  self.名称=nil 
  self.名称1=nil
  self.图片1=nil
  self.宽度 = 300
  self.高度 = 170
  self.介绍文本:置文字(文本字体):清空()
  self.介绍文本2:置文字(文本字体):清空()
  local _,h=0,0
  local h1 = 0
  if self.数据 and self.数据.名称 then
      local 超级名称=self.数据.名称
      if self.数据.名称=="奔雷咒"or self.数据.名称=="泰山压顶"or self.数据.名称=="水漫金山"or self.数据.名称=="壁垒击破"or self.数据.名称=="地狱烈火" then
        超级名称="超级"..self.数据.名称
      else
          if string.find(self.数据.名称, "高级")~= nil then
            local 临时名称=分割文本(self.数据.名称, "高级")
            超级名称="超级"..临时名称[2]
        end
      end
      local lxxs = 取技能(超级名称)
      self.名称=超级名称
      self.图片= __res:取资源动画(lxxs[6], lxxs[7],"图像"):拉伸(60,60)
      _,h=self.介绍文本:置文本("")
      _,h=self.介绍文本:置文本(lxxs[1])
      self.介绍文本:置高度(h)
      self.介绍文本2:置坐标(100,90+self.介绍文本.高度)
      local lxxs1 = 取技能(self.数据.名称)
      self.名称1=self.数据.名称
      self.图片1= __res:取资源动画(lxxs1[6], lxxs1[7],"图像"):拉伸(60,60)
      _,h1=self.介绍文本2:置文本("")
      _,h1=self.介绍文本2:置文本(lxxs1[1])
      self.介绍文本2:置高度(h1)
  end

  if self.高度<h+h1+100 then
      self.高度= h+h1+100
  end
  if x+self.宽度 >引擎.宽度 then
    x = 引擎.宽度 - self.宽度- 5
elseif x<0 then
    x = 0
end
if y+self.高度 >引擎.高度 then
    y = 引擎.高度 - self.高度- 5
elseif y<0 then
    y = 0
end
self:置坐标(x, y)
self:置宽高(self.宽度, self.高度)
self:提示显示(self.宽度, self.高度)
end


function 技能详情:提示显示(w,h)
  --local nsf = 取九宫图像(__res:getPNGCC(2, 230, 964, 401, 52),w,h,10,true)
  local nsf = 取九宫图像(__res:取资源动画("dlzy", 0xB5FDF1AC,"图像"),w,h,20,true)
  if nsf:渲染开始() then
      if self.图片 then
          self.图片:显示(20,20)
      end
      if self.图片1 then
          self.图片1:显示(20,70+self.介绍文本.高度)
      end
      if  self.名称 then
          道具字体:置颜色(__取颜色("紫色"))
          道具字体:取图像(self.名称):显示(100,20)
      end
      if self.名称1 then
        道具字体:置颜色(252, 252, 8)
        道具字体:取图像(self.名称1,252, 252, 8,180):显示(100,70+self.介绍文本.高度)
      end
      __res:取资源动画("dlzy",0x381EAF65,"图像"):拉伸(w, 2):显示(0,50+self.介绍文本.高度)

    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
  

end


local 介绍文本 = 技能详情:创建文本("介绍文本", 100, 40, 190, 0)
function 介绍文本:初始化()
end
local 介绍文本2 = 技能详情:创建文本("介绍文本2", 100, 100, 180, 0)
function 介绍文本2:初始化()
end