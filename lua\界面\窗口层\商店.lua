local 商店 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "商店", 573 + abbr.py.x, 15 + abbr.py.y, 377, 515)
local lsb = {
  "单价",
  "总价",
  "数量",
  "现金"
}

local ggf = require("GGE.函数")
function 商店:初始化()
  local nsf = require("SDL.图像")(377, 515)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("商店", 0, 12, 372, 500, true)["显示"](xiao置窗口背景("商店", 0, 12, 372, 500, true), 0, 0)
    取灰色背景(0, 0, 340, 320, true)["显示"](取灰色背景(0, 0, 340, 320, true), 17, 50)
    字体18["置颜色"](字体18, 255, 255, 255)
    local lssj = 取输入背景(0, 0, 122, 23)
    local pyx = 0
    local pyy = 0
    for i = 1, #lsb do
      if i > 2 then
        pyx = 176
        pyy = -66
      end
      lssj["显示"](lssj, 58 + pyx, 387 + (i - 1) * 33 + pyy)
      -- 字体18["取图像"](字体18, lsb[i])["显示"](字体18["取图像"](字体18, lsb[i]), 15 + pyx, 387 + (i - 1) * 33 + pyy)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.道具选中 = nil
  self.临时道具 = {}
  self.单价 = 0
  self.数量 = 0
  self.总价 = 0
  self.现金 = 0
  self.标题宽度 = 字体20["取宽度"](字体20, "商店")
  self.标题 = "商店"
end
function 商店:显示(x, y)
  if self.图像 then
    self.图像["显示"](self.图像, x, y)
  end
  if self.图像2 then
    self.图像2["显示"](self.图像2, x, y)
  end
  -- 字体20["显示"](字体20, x + 186.0 - self.标题宽度 / 2, y + 15, self.标题)
end
function 商店:打开(sp, mc, lx, yz)
  self:置可见(true)
  self.道具选中 = nil
  self.临时道具 = {}
  self.单价 = 0
  self.数量 = 0
  self.总价 = 0
  self.现金 = yz
  self.当前积分=nil
  if nil ~= lx then
    self.类型 = lx
    self.标题 = lx
  else
    self.类型 = "银子"
    self.标题 = "商店"
  end
  self:tdsgsg()
  self.标题宽度 = 字体20["取宽度"](字体20, self.标题)
  for i = 1, #sp do
    local lssj = ggf["分割文本"](sp[i], "*")
    -- table.print(lssj)
    self.临时道具[i] = {
      ["名称"] = lssj[1],
      ["价格"] = lssj[2],
      ["子类"] = lssj[3],
      ["原始商品"] = sp[i]
    }
  end
  self.道具网格["置物品"](self.道具网格, self.临时道具)
  self.数量输入["置数值"](self.数量输入, 1)
end
local 关闭 = 商店["创建我的按钮"](商店, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 327, 0)
function 关闭:左键弹起(x, y, msg)
  商店["置可见"](商店, false)
end
function 商店:tdsgsg()
  local nsf = require("SDL.图像")(377, 510)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, 39, 53, 81)
    if self.类型=="比武积分" then
      local lsb = {
        "单价",
        "总价",
        "数量",
        "积分"
      }
      字体18["置颜色"](字体18, 255, 255, 255)
      local pyx = 0
      local pyy = 0
      for i = 1, #lsb do
        if i > 2 then
          pyx = 176
          pyy = -66
        end
        字体18["取图像"](字体18, lsb[i])["显示"](字体18["取图像"](字体18, lsb[i]), 15 + pyx, 387 + (i - 1) * 33 + pyy)
      end
    else
      local lsb = {
        "单价",
        "总价",
        "数量",
        "银子"
      }
      字体18["置颜色"](字体18, 255, 255, 255)
      local pyx = 0
      local pyy = 0
      for i = 1, #lsb do
        if i > 2 then
          pyx = 176
          pyy = -66
        end
        字体18["取图像"](字体18, lsb[i])["显示"](字体18["取图像"](字体18, lsb[i]), 15 + pyx, 387 + (i - 1) * 33 + pyy)
      end
      字体18["置颜色"](字体18, 39, 53, 81)
      字体18["取图像"](字体18, self.现金)["显示"](字体18["取图像"](字体18, self.现金), 250, 422)
    end
    
    nsf["渲染结束"](nsf)
  end
  self.图像2 = nsf["到精灵"](nsf)
end
function 商店:重置(数据)
  local nsf = require("SDL.图像")(377, 510)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, 39, 53, 81)
    字体18["取图像"](字体18, 数据["价格"])["显示"](字体18["取图像"](字体18, 数据["价格"]), 70, 390)
    字体18["取图像"](字体18, (数据["价格"] + 0) * self.数量输入["取数值"](self.数量输入))["显示"](字体18["取图像"](字体18, (数据["价格"] + 0) * self.数量输入["取数值"](self.数量输入)), 70, 422)
    if self.类型=="比武积分" then
      字体18:置颜色(__取颜色("紫色"))
      字体18:取图像(self.现金):显示(250, 422)
    else
      字体18["取图像"](字体18, self.现金)["显示"](字体18["取图像"](字体18, self.现金), 250, 422)
    end
    
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
local 道具网格 = 商店["创建网格"](商店, "道具网格", 23, 57, 326, 305)
function 道具网格:初始化()
  self:创建格子(55, 55, 9, 13, 5, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    if 商店["选中道具"] and self.子控件[商店["选中道具"]]._spr["物品"] then
      self.子控件[商店["选中道具"]]._spr["确定"] = nil
    end
    商店["选中道具"] = a
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170-24, 86, w, h, "选择", a)
    商店["重置"](商店, 商店["临时道具"][商店["选中道具"]])
  end
end
function 道具网格:置物品(数据, zl, fl)
  -- table.print(数据)
  for i = 1, #道具网格["子控件"] do
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, 数据[i], "NPC商店")
    道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)
  end
end
local 数量输入 = 商店["创建输入"](商店, "数量输入", 285, 388, 50, 18)
function 数量输入:初始化()
  self.取光标精灵(self)
  self:置限制字数(2)
  self:置颜色(39, 53, 81, 255)
  self:置模式(2)
end
local 临时函数 = 商店["创建我的按钮"](商店, __res:getPNGCC(3, 2, 507, 124, 41):拉伸(153, 41), "mg按钮",105, 456 ,"购 买")
function  临时函数:左键弹起(x, y, msg)
   if  商店["选中道具"] and 道具网格["子控件"][商店["选中道具"]]._spr["物品"] and 商店["数量输入"]["取数值"](商店["数量输入"]) then
     发送数据(1503, {
       ["数量"] = 商店["数量输入"]["取数值"](商店["数量输入"]),
       ["商品"] = 商店["道具网格"]["子控件"][商店["选中道具"]]._spr["物品"]["原始商品"],
       ["类型"] = 商店["类型"]
     })
   end
 end