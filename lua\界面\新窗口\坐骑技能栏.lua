local 坐骑技能栏 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "坐骑技能栏", 224, 20+42-54+78 + abbr.py.y, 603-96, 357)

local function 取升级消耗(dj)
	if dj==0 then
	    return "你确认要将技能提升么？#Y提升技能需要消耗220000经验和1000000金钱，以及1点技能点。"
    elseif dj==1 then
	    return "你确认要将技能提升么？#Y提升技能需要消耗240000经验和1500000金钱，以及1点技能点。"
    elseif dj==2 then
	    return "你确认要将技能提升么？#Y提升技能需要消耗260000经验和2000000金钱，以及1点技能点。"
	else
		return "当前数据不同步，请重新打开坐骑界面再进行技能学习！"
	end
end



function 坐骑技能栏:初始化()
  local nsf = require("SDL.图像")(603-96, 357)
  if nsf["渲染开始"](nsf) then
    置窗口背景("坐骑技能", 0, 12, 603-20-96, 353-10, true):显示(0, 0)
    字体20:置颜色(__取颜色("白色"))
    字体20:取图像("可用技能点"):显示(161,55)
    取输入背景(0, 0, 130, 23):显示(178+88,54)
    -- local gezi=__res:getPNGCC(3, 1091, 374, 50, 50)
    -- for i=1,5 do
    --   gezi:显示(161+(i-1)*60,88)
    -- end
    取白色背景(0, 0, 288, 166-30, true):显示(161,88+62)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 坐骑技能栏:打开(内容)
  self:置可见(true)
  
  self.状态="辅助"
  self[self.状态]:置选中(true)
  self.学习按钮:置禁止(true)
  self.选中=0
  self.技能组={}
  self:刷新(内容)
  self.jinengwangge:置技能(self.数据.坐骑技能)
end
function 坐骑技能栏:刷新(内容)
  self.数据 = 内容
  if self.选中~=0 and self.技能组[self.选中] then
    self.jinengwangge:左键弹起(x, y, self.选中, b, msg)
    -- self.技能[self.选中].技能.等级 = 内容.坐骑技能[self.技能[self.选中].技能.名称]
    -- self:修改内容(self.技能[self.选中].技能.名称,self.技能[self.选中].技能.等级,self.技能[self.选中].技能.介绍,bb)
  end
  local nsf = require("SDL.图像")(345, 81)
  if nsf["渲染开始"](nsf) then
    字体18:置颜色(__取颜色("浅黑"))
    字体18:取图像(self.数据.可用技能点):显示(284,55)
    nsf["渲染结束"](nsf)
  end
  self.图像2 = nsf["到精灵"](nsf)
end
function 坐骑技能栏:取包含()
  if self.状态=="辅助" then
    return {"忠贯日月","正身清心","延年益寿"}
  elseif self.状态=="天火" then
    return {"开天辟地","破釜沉州"}
  elseif self.状态=="苍雷" then
    return {"大浪淘沙","炫火乱舞"}
  elseif self.状态=="赤电" then
    return {"金刚护身","偃旗息鼓"}
  elseif self.状态=="寒水" then
    return {"铜墙铁壁","水来土掩"}
  elseif self.状态=="疾风" then
    return {"飞火流星","乾坤借速"}
  end
end

local jinengwangge = 坐骑技能栏["创建网格"](坐骑技能栏, "jinengwangge", 160, 91-5, 300, 300)
function jinengwangge:初始化()
  self:创建格子(55, 55, 5, 5, 1, 5)
end

function jinengwangge:左键弹起(x, y, a, b, msg)
  if not self.子控件[a] or not self.子控件[a]._spr or not self.子控件[a]._spr["模型"] then
    return
  end
  if 坐骑技能栏.技能组[a] and 坐骑技能栏.数据.坐骑技能[坐骑技能栏.技能组[a]] then
    坐骑技能栏:修改内容(self.子控件[a]._spr.数据.名称,坐骑技能栏.数据.坐骑技能[坐骑技能栏.技能组[a]],self.子控件[a]._spr.数据[1],坐骑技能栏.数据)
    if 坐骑技能栏["选中"] and self.子控件[坐骑技能栏["选中"]] and self.子控件[坐骑技能栏["选中"]]._spr then
      self.子控件[坐骑技能栏["选中"]]._spr["确定"] = nil
    end
    坐骑技能栏["选中"] = a
    坐骑技能栏.学习按钮:置禁止(false)
    self.子控件[a]._spr["确定"] = true
  end
end

function jinengwangge:置技能()
  坐骑技能栏.技能组=坐骑技能栏:取包含()
  local sdw= {}
  for i=1,#坐骑技能栏.技能组 do
    sdw[i]={}
    sdw[i].名称=坐骑技能栏.技能组[i]
  end
  for i = 1, #jinengwangge["子控件"] do
    if sdw[i] then
      local lssj = __小技能格子.创建()
      -- print(坐骑技能栏.物品组[i].名称)
      lssj:置数据(sdw[i], 55,55 )
      self["子控件"][i]["置精灵"](self["子控件"][i], lssj)
    else
      local lssj = __小技能格子.创建()
      -- print(坐骑技能栏.物品组[i].名称)
      lssj:置数据(sdw[i], 55,55)
      self["子控件"][i]["置精灵"](self["子控件"][i], lssj)
    end
  end
end











for i, v in ipairs({
  {
    name = "辅助",
    x = 11,
    y = 56+(1-1)*46,
    tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31),
    tcp2 = __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31),
    font = "辅助"
  },
  {
    name = "天火",
    x = 11,
    y = 56+(2-1)*46,
     tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31),
    tcp2 = __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31),
    font = "天火"
  },
  {
    name = "苍雷",
    x = 11,
    y = 56+(3-1)*46,
     tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31),
    tcp2 = __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31),
    font = "苍雷"
  },
  {
    name = "赤电",
    x = 11,
    y = 56+(4-1)*46,
     tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31),
    tcp2 = __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31),
    font = "赤电"
  },
  {
    name = "寒水",
    x = 11,
    y = 56+(5-1)*46,
     tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31),
    tcp2 = __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31),
    font = "寒水"
  },
  {
    name = "疾风",
    x = 11,
    y = 56+(6-1)*46,
     tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(114,31),
    tcp2 = __res:getPNGCC(1, 964, 459, 173, 35, true):拉伸(114,31),
    font = "疾风"
  },
}) do
  local 临时函数 = 坐骑技能栏["创建我的单选按钮"](坐骑技能栏, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
  function  临时函数:左键弹起(x, y)
    坐骑技能栏.状态=v.name
    坐骑技能栏.jinengwangge:置技能()
    坐骑技能栏.介绍文本:清空()
    坐骑技能栏["选中"]=0
    坐骑技能栏.学习按钮:置禁止(true)
  end
end
local 介绍文本 = 坐骑技能栏["创建文本"](坐骑技能栏, "介绍文本", 161+23,88+62+23, 237, 126-30)
function 介绍文本:初始化()
end

function 坐骑技能栏:修改内容(名称,等级,描述,bb)
	-- print(名称,等级,描述)
	if 名称 ~= nil then
		self.介绍文本:清空()
		if 描述 ~= nil then
		-- local 坐骑技能表 = {"忠贯日月","正身清心","延年益寿","开天辟地","破釜沉州","大浪淘沙","炫火乱舞","金刚护身","偃旗息鼓","铜墙铁壁","水来土掩","飞火流星","乾坤借速"}
		-- K=0xffCC0033, --淡红
		-- M=0xFF222222, --淡黑
			self.介绍文本:置文本("#K/"..名称.."#Y/".." 等级: "..等级.."——3")
			local xxx = bb.饱食度
			if xxx>100 then
				xxx=100
			end
			self.介绍文本:置文本("#Y/".."实际效果(饱食度影响): "..xxx.."%")

			if 名称 == "忠贯日月" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/召唤兽在战斗中忠诚的消耗变为"..(3+等级).."场战斗掉1点忠诚")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/召唤兽在战斗中忠诚的消耗变为"..(3+等级+1).."场战斗掉1点忠诚")
				end
			elseif 名称 == "正身清心" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/每天前"..(等级).."次食用长寿面，豆斋果不会中毒。")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/每天前"..(等级+1).."次食用长寿面，豆斋果不会中毒。")
				end
			elseif 名称 == "延年益寿" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭召唤兽食用长寿面、桂花丸和豆斋果时额外再增加寿命"..(等级+1).."%")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭召唤兽食用长寿面、桂花丸和豆斋果时额外再增加寿命"..(等级+2).."%")
				end
			elseif 名称 == "开天辟地" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭的召唤兽力量点数*"..((等级)*3).."%的伤害力")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭的召唤兽力量点数*"..((等级+1)*3).."%的伤害力")
				end
			elseif 名称 == "破釜沉州" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭的召唤兽致命几率"..(等级).."%，命中率增加"..(等级).."%")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭的召唤兽致命几率"..(等级+1).."%，命中率增加"..(等级+1).."%")
				end
			elseif 名称 == "大浪淘沙" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭的召唤兽对（水攻、水漫金山、落岩、泰山压顶）水系土系法术伤害结果减免"..(等级*3+等级).."%")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭的召唤兽对（水攻、水漫金山、落岩、泰山压顶）水系土系法术伤害结果减免"..((等级+1)*3+等级+1).."%")
				end
			elseif 名称 == "炫火乱舞" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭的召唤兽对（烈火、地狱烈火、雷、奔雷咒）火系雷系法术伤害结果减免"..(等级*3+等级).."%")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭的召唤兽对（烈火、地狱烈火、雷、奔雷咒）火系雷系法术伤害结果减免"..((等级+1)*3+等级+1).."%")
				end
			elseif 名称 == "金刚护身" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/统驭的召唤兽受到伤害时有"..(等级*20).."%的几率减少4%所受伤害。")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/统驭的召唤兽受到伤害时有"..((等级+1)*20).."%的几率减少4%所受伤害。")
				end
			elseif 名称 == "偃旗息鼓" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/统御的召唤兽受到的所有伤害降低"..(等级*0.8).."%")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/统御的召唤兽受到的所有伤害降低"..((等级+1)*0.8).."%")
				end
			elseif 名称 == "铜墙铁壁" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/敌人对统御的召唤兽物理暴击和法术暴击倍率降低"..((等级)*10).."%")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/敌人对统御的召唤兽物理暴击和法术暴击倍率降低"..((等级+1)*10).."%")
				end
			elseif 名称 == "飞火流星" then
				if 等级~=0 then
					self.介绍文本:置文本("#Y/当前等级: #M/统驭的召唤兽速度降低"..(等级*3).."%")
				end
				if 等级~=3 then
					self.介绍文本:置文本("#Y/下一等级: #M/统驭的召唤兽速度降低"..((等级+1)*3).."%")
				end
			elseif 名称 == "乾坤借速" then
				if 等级==0 then
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭的召唤兽闪躲3%，同时在物理攻击对方目标时有20%几率自身获得提升5%速度效果持续三回合")
				elseif 等级==1 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭的召唤兽闪躲3%，同时在物理攻击对方目标时有20%几率自身获得提升5%速度效果持续三回合")
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭的召唤兽闪躲3%，同时在物理攻击对方目标时有30%几率自身获得提升8%速度效果持续三回合")
				elseif 等级==2 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭的召唤兽闪躲3%，同时在物理攻击对方目标时有30%几率自身获得提升8%速度效果持续三回合")
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭的召唤兽闪躲3%，同时在物理攻击对方目标时有50%几率自身获得提升10%速度效果持续三回合")
				elseif 等级==3 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭的召唤兽闪躲3%，同时在物理攻击对方目标时有50%几率自身获得提升10%速度效果持续三回合")
				end
			elseif 名称 == "水来土掩" then
				if 等级==0 then
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭召唤兽20%的中毒抵抗几率")
				elseif 等级==1 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭召唤兽20%的中毒抵抗几率")
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭召唤兽50%的中毒抵抗几率")
				elseif 等级==2 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭召唤兽50%的中毒抵抗几率")
					self.介绍文本:置文本("#Y/下一等级: #M/增加统驭召唤兽80%的中毒抵抗几率")
				elseif 等级==3 then
					self.介绍文本:置文本("#Y/当前等级: #M/增加统驭召唤兽80%的中毒抵抗几率")
				end
			end
		end
	end
end
local 学习按钮 = 坐骑技能栏["创建我的按钮"](坐骑技能栏, __res:getPNGCC(3, 2, 507, 124, 41):拉伸(123+16, 37), "学习按钮", 217, 303,"升 级")
function 学习按钮:左键弹起(x, y, msg)
  if 坐骑技能栏.技能组[坐骑技能栏["选中"]] and 坐骑技能栏.数据.坐骑技能[坐骑技能栏.技能组[坐骑技能栏["选中"]]] and 坐骑技能栏.数据.坐骑技能[坐骑技能栏.技能组[坐骑技能栏["选中"]]]<3 then
    local 事件 = function()
      发送数据(63,{认证码=坐骑技能栏.数据.认证码,技能=坐骑技能栏.技能组[坐骑技能栏["选中"]]})
    end
    __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, 取升级消耗(坐骑技能栏.数据.坐骑技能[坐骑技能栏.技能组[坐骑技能栏["选中"]]]), 285, 155, 390, 200, 事件)
  end
end

local 关闭 = 坐骑技能栏["创建我的按钮"](坐骑技能栏, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 500+37-96, 0)
function 关闭:左键弹起(x, y, msg)
  坐骑技能栏["置可见"](坐骑技能栏, false)
end