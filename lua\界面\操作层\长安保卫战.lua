--[[
LastEditTime: 2024-11-17 20:51:01
--]]

local 长安保卫战 = 窗口层:创建窗口("长安保卫战", 0, 0, 450, 295)
function 长安保卫战:初始化()
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end



function 长安保卫战:打开(数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
    return
  end
  self.状态="最新战况"
  self.积分=0
  self:刷新(数据)


  

end
function 长安保卫战:刷新(数据)
      self.先锋=""
      self.头目=""
      self.大王=""
      self.文本:清空()
      if 数据.积分 then
         self.积分=数据.积分
      end
      if 数据.刷怪记录 and type(数据.刷怪记录)=="string" then
          self.文本:置文本(数据.刷怪记录)
          self.文本._py = -self.文本._max
      end
      if 数据.先锋 then
          for k,v in pairs(数据.先锋) do
              if v>10 then
                  self.先锋=self.先锋..取地图名称(k).."、"
              end
          end
      end
      if 数据.头目 then
          for k,v in pairs(数据.头目) do
            if v>5 then
                self.头目=self.头目..取地图名称(k).."、"
            end
          end
      end
      if 数据.大王 then
        for k,v in pairs(数据.大王) do
          if v>=2 then
              self.大王=self.大王..取地图名称(k).."、"
          end
        end
      end
      self:显示设置()
end

function 长安保卫战:显示设置()
  self[self.状态]:置选中(true)
  self:创建纹理精灵(function()
    置窗口背景("长安保卫战", 0, 0, 450, 295, true):显示(0, 0)
    取白色背景(0, 0, 330, 160, true):显示(102, 30)
    if  self.状态=="最新战况" then
        self.文本:置可见(true)
        取白色背景(0, 0, 330, 80, true):显示(102, 200)
        文本字体:置颜色(0,0,0,255)
        文本字体:取图像("当前剩余怪物最多的场景：\n先锋：\n头目：\n大王："):显示(112, 210)
        文本字体:置颜色(__取颜色("红色"))
        文本字体:取图像(self.先锋):显示(155, 225)
        文本字体:取图像(self.头目):显示(155, 240)
        文本字体:取图像(self.大王):显示(155, 255)
    elseif self.状态=="个人积分" then
        self.文本:置可见(false)
        文本字体:置颜色(0,0,0,255):取图像("当前你的积分为："):显示(115, 40)
        文本字体:置颜色(__取颜色("红色")):取图像(self.积分):显示(240, 42)
    end
  end
)

end
local 按钮配置={"最新战况","个人积分", "长安功臣", "活动说明"}
for i = 1, 2 do
  local 临时按钮=长安保卫战:创建红色单选按钮(按钮配置[i], 按钮配置[i],17,33+(i-1)*35,75,20) 
  function 临时按钮:左键弹起(x,y)
        if 长安保卫战.状态~=按钮配置[i] then
            长安保卫战.状态=按钮配置[i]
            长安保卫战:显示设置()
        end 
  end
end

for i = 3, 4 do
  local 临时按钮=长安保卫战:创建红色按钮(按钮配置[i], 按钮配置[i],17,33+(i-1)*35,75,20) 
end




local 文本 = 长安保卫战:丰富文本("文本", 107, 35, 320, 150)

local 关闭 = 长安保卫战:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  长安保卫战:置可见(false)
end

