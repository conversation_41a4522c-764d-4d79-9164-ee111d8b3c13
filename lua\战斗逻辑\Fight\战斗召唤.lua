local 战斗召唤 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "战斗召唤", 669 + abbr.py.x, 30 + abbr.py.y, 271, 482)
function 战斗召唤:初始化()
  local nsf = require("SDL.图像")(271, 482)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(2, 622, 66, 263, 475)["显示"](__res:getPNGCC(2, 622, 66, 263, 475), 0, 12)
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18["取图像"](字体18, "最多召唤12只召唤兽参战")["显示"](字体18["取图像"](字体18, "最多召唤12只召唤兽参战"), 18, 452)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 战斗召唤:打开(pet, data,助战id)
  self:置可见(true)
  self.助战id=助战id
  if self.助战id then
    __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], false)
  else
    __UI界面["界面层"]["助战操作界面"]["置可见"](__UI界面["界面层"]["助战操作界面"], false)
  end
  self.召唤数据 = data
  self.召唤列表["重置"](self.召唤列表, pet)
  self.选中 = nil
end
local 关闭 = 战斗召唤["创建我的按钮"](战斗召唤, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 221, 0)
function 关闭:左键弹起(x, y, msg)
  if 战斗召唤.助战id then
    __UI界面["窗口层"]["战斗召唤"]["置可见"](__UI界面["窗口层"]["战斗召唤"], false)
    __UI界面["窗口层"]["战斗召唤详情"]["置可见"](__UI界面["窗口层"]["战斗召唤详情"], false)
  else
    __UI界面["窗口层"]["战斗召唤"]["置可见"](__UI界面["窗口层"]["战斗召唤"], false)
    __UI界面["窗口层"]["战斗召唤详情"]["置可见"](__UI界面["窗口层"]["战斗召唤详情"], false)
    __UI界面["界面层"]["战斗界面"]["重置"](__UI界面["界面层"]["战斗界面"])
    __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], true)
  end
end
function 战斗召唤:取是否召唤(id)
  for n = 1, #self.召唤数据 do
    if self.召唤数据[n] == id then
      return true
    end
  end
  return false
end
local 召唤列表 = 战斗召唤["创建列表"](战斗召唤, "召唤列表", 20, 60, 225, 337)
function 召唤列表:初始化()
  self:置文字(字体18)
  self.行高度 = 67
  self.行间距 = 5
end
function 召唤列表:左键弹起(x, y, i, item, msg)
  战斗召唤["选中"] = i
  __UI界面["窗口层"]["战斗召唤详情"]["打开"](__UI界面["窗口层"]["战斗召唤详情"], self.子控件[i]._spr["数据"])
end
function 召唤列表:重置(pet)
  self.清空(self)
  for i, v in ipairs(pet) do
    local 允许召唤 = true
    self.添加(self)
    local nsf = require("SDL.图像")(224, 67)
    if nsf["渲染开始"](nsf) then
      __res:getPNGCC(3, 1091, 374, 49, 49)["拉伸"](__res:getPNGCC(3, 1091, 374, 49, 49), 60, 60)["显示"](__res:getPNGCC(3, 1091, 374, 49, 49)["拉伸"](__res:getPNGCC(3, 1091, 374, 49, 49), 60, 60), 2, 2)
      local lssj = 取头像(v["模型"])
      __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 53, 53)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 53, 53), 6, 6)
      字体18["置颜色"](字体18, 255, 255, 255)
      字体18["取图像"](字体18, v["名称"])["显示"](字体18["取图像"](字体18, v["名称"]), 70, 8)
      字体18["取图像"](字体18, v["等级"] .. "级")["显示"](字体18["取图像"](字体18, v["等级"] .. "级"), 70, 30)
      if 战斗召唤["取是否召唤"](战斗召唤, i) then
        允许召唤 = false
        字体18["置颜色"](字体18, __取颜色("黄色"))
        字体18["取图像"](字体18, "已战斗")["显示"](字体18["取图像"](字体18, "已战斗"), 150, 30)
      end
      nsf["渲染结束"](nsf)
    end
    self.子控件[i]["置精灵"](self.子控件[i], nsf["到精灵"](nsf))
    self.子控件[i]._spr["允许召唤"] = 允许召唤
    self.子控件[i]._spr["数据"] = v
  end
end
local 召唤按钮 = 战斗召唤["创建我的按钮"](战斗召唤, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 150, 40), "召唤按钮", 58, 407, "召唤")
function 召唤按钮:左键弹起(x, y, msg)
  if 战斗召唤["选中"] and 召唤列表["子控件"][战斗召唤["选中"]]._spr["允许召唤"] then
    if 战斗召唤.助战id then
      __UI界面["界面层"]["助战操作界面"]["设置召唤"](__UI界面["界面层"]["助战操作界面"], 战斗召唤["选中"])
      __UI界面["窗口层"]["战斗召唤详情"]["置可见"](__UI界面["窗口层"]["战斗召唤详情"], false)
      战斗召唤:置可见(false)
    else
      __UI界面["界面层"]["战斗界面"]["设置召唤"](__UI界面["界面层"]["战斗界面"], 战斗召唤["选中"])
      __UI界面["窗口层"]["战斗召唤详情"]["置可见"](__UI界面["窗口层"]["战斗召唤详情"], false)
    end
  end
end
