

local 装备开运 = 窗口层:创建窗口("装备开运", 0,0, 580, 390)
function 装备开运:初始化()
  self:置精灵(置窗口背景("装备开运", 0, 0, 580, 390))
  self.格子背景 = __res:getPNGCC(3, 694, 4, 338, 273):到精灵()
  self.起点=0
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end


  local 开运 = 装备开运:创建蓝色按钮("开运", "开运", 105,65,90, 40,说明字体)
  function 开运:左键弹起(x, y)
      if 装备开运.材料网格.子控件[1].原始编号 then
          if 装备开运.类型==1 then
              请求服务(4502,{序列=装备开运.材料网格.子控件[1].原始编号})
          else
            请求服务(4502.1,{序列=装备开运.材料网格.子控件[1].原始编号})
          end
      end
  end

function 装备开运:打开(数据)
  self:置可见(true,true)
  self.起点 = 0
  self.数据 = 数据
  self.编号= 0
  self.体力=0
  self.银子= 0
  self.类型=数据.类型
  if 数据.编号 then
    self.编号=数据.编号
  end
  if 数据.道具.体力 then
    self.体力 = 数据.道具.体力
  end
  if 数据.道具.银子 then
    self.银子 = 数据.道具.银子
  end
  self.道具列表 = 数据.道具.道具
  self.道具网格:置物品(self.道具列表)
  self.材料网格:置物品()
  self:显示刷新()
end



function 装备开运:显示(x, y)
    self.格子背景:显示(x + 230, y + 55)
    if self.图像 then
        self.图像:显示(x, y)
    end
end


function 装备开运:显示刷新()
  self.图像 = self:创建纹理精灵(function()
    for i = 1, 4 do
      取输入背景(0, 0, 190, 23):显示(20,170 + (i - 1) * 60)
    end
    说明字体:置颜色(255, 255, 255)
    说明字体:取图像("所需现金"):显示(20, 140)
    说明字体:取图像("现有现金"):显示(20, 200)
    说明字体:取图像("所需体力"):显示(20, 260)
    说明字体:取图像("现有体力"):显示(20, 320)
    说明字体:置颜色(__取银子颜色(装备开运.银子)):取图像(装备开运.银子):显示(25, 233)
    说明字体:置颜色(0,0,0,255):取图像(装备开运.体力):显示(25, 353)
    if self.材料网格.子控件[1]._spr and self.材料网格.子控件[1]._spr.物品 then
        local 消耗银子 =  (self.材料网格.子控件[1]._spr.物品.开运孔数.当前 + 1)*1000000
        说明字体:置颜色(__取银子颜色(消耗银子)):取图像(消耗银子):显示(25, 173)
        local 消耗体力 =  (self.材料网格.子控件[1]._spr.物品.开运孔数.当前 + 1)*30
        说明字体:置颜色(0,0,0,255):取图像(消耗体力):显示(25, 293)
        
    end
  end,1,230,390
)

end




local 道具网格 = 装备开运:创建网格("道具网格", 230, 55, 339, 272)
function 道具网格:初始化()
    self:创建格子(67, 67, 0, 0, 4, 5)
end




function 道具网格:左键弹起(x, y, a)
  if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not self.子控件[a]._spr.物品禁止  then
       __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
       if 装备开运.材料网格.子控件[1]._spr and 装备开运.材料网格.子控件[1]._spr.物品 then
           装备开运.道具列表[装备开运.材料网格.子控件[1].原始编号]=装备开运.材料网格.子控件[1]._spr.物品
       end
       装备开运.材料网格:置物品(self.子控件[a]._spr.物品)
       装备开运.材料网格.子控件[1].原始编号 = a + 装备开运.起点
       装备开运.道具列表[a+装备开运.起点]=nil
       self:置物品(装备开运.道具列表)
       装备开运:显示刷新()
    end
end




function 道具网格:置物品(数据)
  for i = 1, #self.子控件 do
      if 数据[i+装备开运.起点] then
          local lssj = __物品格子:创建()
          lssj:置物品(数据[i+装备开运.起点], 67,67,"数量")
          lssj:置偏移(10, 10)
          lssj:置禁止({2})
          self.子控件[i]:置精灵(lssj)   
      else
          self.子控件[i]:置精灵()
      end
    end
end


local 材料网格 = 装备开运:创建网格("材料网格", 20, 55, 55, 55)
function 材料网格:初始化()
  self:创建格子(55, 55, 0, 45, 1, 1)
end
function 材料网格:左键弹起(x, y, a)
  if self.子控件[a]._spr and self.子控件[a]._spr.物品 and self.子控件[a].原始编号 then
      装备开运.道具列表[self.子控件[a].原始编号]=self.子控件[a]._spr.物品
      装备开运.道具网格:置物品(装备开运.道具列表)
      self:置物品(nil)
      装备开运:显示刷新()
  end
end
function 材料网格:置物品(数据)
      local lssj = __物品格子:创建()
      lssj:置物品(数据,55,55,nil,true)
      self.子控件[1]:置精灵(lssj)
end





local 格子设置={
  {
    name = "一",
    x = 230,
    y = 340,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(60, 35),
    font = "壹"
    },    
    {
    name = "二",
    x = 300,
    y = 340,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(60, 35),
    font = "贰"
    },
    {
    name = "三",
    x = 370,
    y = 340,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(60, 35),
    font = "叁"
    },
    {
    name = "四",
    x = 440,
    y = 340,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(60, 35),
    font = "肆"
    },
    {
    name = "五",
    x = 510,
    y = 340,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(60, 35),
    font = "伍"
    }

}




for i, v in ipairs(格子设置) do
  local 临时函数 = 装备开运:创建蓝色按钮(v.font,v.name, v.x, v.y,60, 35,标题字体)
 function  临时函数:左键弹起(x, y)
    if v.name == "一" then
      装备开运.起点 = 0
    elseif v.name == "二" then
      装备开运.起点 = 20
    elseif v.name == "三" then
      装备开运.起点 = 40
    elseif v.name == "四" then
      装备开运.起点 = 60 
    elseif v.name == "五" then
      装备开运.起点 = 80 
    end
    装备开运.道具网格:置物品(装备开运.道具列表)
  end
end

local 关闭 = 装备开运:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  装备开运:置可见(false)
end





