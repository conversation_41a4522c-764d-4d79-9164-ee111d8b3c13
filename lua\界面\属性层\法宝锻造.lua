local 法宝锻造 = 窗口层:创建窗口("法宝锻造", 0, 0, 410, 464)
function 法宝锻造:初始化()
  local lsb = {
    {
      x = 60,
      y = 70,
      font = "内丹"
    },
    {
      x = 293,
      y = 71,
      font = "材料1"
    },
    {
      x = 180,
      y = 147,
      font = "材料2"
    },
    {
      x = 60,
      y = 214,
      font = "法宝"
    },
    {
      x = 293,
      y = 214,
      font = "天珠"
    }
  }
  self:创建纹理精灵(function()
              置窗口背景("法宝锻造", 0, 0, 410, 454, true):显示(0, 0)
              local lssj = 取输入背景(0, 0, 55, 23)
              说明字体:置颜色(255,255,255,255)
              for i, v in ipairs(lsb) do
                说明字体:取图像(v.font):显示(v.x + 10, v.y + 60)
              end
          end
        )

  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
end
function 法宝锻造:打开()
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.材料网格:置物品( nil)
  self.材料网格:重置()
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
end

for i, v in ipairs({
  {
    name = "开始",
    x = 111,
    y = 345,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(175, 41),
    font = "开始"
  }
}) do
  local 临时函数 =法宝锻造:创建按钮(v.name, v.x, v.y)
  function 临时函数:初始化()
      self:创建按钮精灵(v.tcp,1,v.font)
  end
 function  临时函数:左键弹起(x, y)
    if v.name == "开始" and 法宝锻造.材料网格.子控件[1]._spr.原始编号 and 法宝锻造.材料网格.子控件[2]._spr.原始编号 and 法宝锻造.材料网格.子控件[3]._spr.原始编号 then
      local 临时格子 = {}
      for i = 1, 5 do
        if 法宝锻造.材料网格.子控件[i]._spr.原始编号 ~= nil then
          临时格子[i] = 法宝锻造.材料网格.子控件[i]._spr.原始编号
        end
      end
      请求服务(3715, {
        ["格子"] = 临时格子,
        ["类型"] = "道具"
      })
      法宝锻造.关闭:左键弹起()
    end
  end
end
local 材料网格 = 法宝锻造:创建网格("材料网格", 0, 0, 366, 275)
function 材料网格:初始化()
  self:创建格子(55, 55, 0, 0, 1, 5)
end
function 材料网格:重置()
  self.已加材料 = {}
end
function 材料网格:左键弹起(x, y, a, b, msg)
  local xx, yy = 法宝锻造["取坐标加宽"](法宝锻造)
  if 1 == a then
    __UI弹出.道具选择:打开( _tp.道具列表, xx, yy, "法宝合成", 1003, 5, a)
  elseif 4 == a then
    __UI弹出.道具选择:打开( _tp.道具列表, xx, yy, "法宝合成", 1000, nil, a)
  elseif 5 == a then
    __UI弹出.道具选择:打开( _tp.道具列表, xx, yy, "法宝合成", 3, 11, a)
  else
    __UI弹出.道具选择:打开( _tp.道具列表, xx, yy, "法宝合成", 1003, nil, a)
  end
end
function 材料网格:置物品(data, bh, pid)
  local lsb = {
    {x = 60, y = 70},
    {x = 293, y = 71},
    {x = 180, y = 147},
    {x = 60, y = 214},
    {x = 293, y = 214}
  }
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __材料格子:创建()
      lssj:置物品(data)
      self.子控件[i]:置精灵(lssj)
      self.子控件[i]:置坐标(lsb[i].x, lsb[i].y)
    end
  elseif not self.已加材料[pid] then
    if self.子控件[bh]._spr.原始编号 then
      self.已加材料[self.子控件[bh]._spr.原始编号] = nil
    end
    local lssj = __材料格子:创建()
    lssj:置物品( data)
    lssj.原始编号 = pid
    self.已加材料[pid] = bh
    self.子控件[bh]:置精灵(lssj)
    self.子控件[bh]:置坐标( lsb[bh].x, lsb[bh].y)
  else
    __UI弹出.提示框:打开( "#Y这个道具已经添加过无法在添加")
  end
end

local 关闭 = 法宝锻造:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  法宝锻造:置可见(false)
end

