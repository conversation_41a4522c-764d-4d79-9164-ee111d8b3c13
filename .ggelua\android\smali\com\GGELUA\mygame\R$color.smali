.class public final Lcom/GGELUA/mygame/R$color;
.super Ljava/lang/Object;


# annotations
.annotation system Ldalvik/annotation/EnclosingClass;
    value = Lcom/GGELUA/mygame/R;
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x19
    name = "color"
.end annotation


# static fields
.field public static final colorAccent:I = 0x7f010000

.field public static final colorPrimary:I = 0x7f010001

.field public static final colorPrimaryDark:I = 0x7f010002


# direct methods
.method private constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method
