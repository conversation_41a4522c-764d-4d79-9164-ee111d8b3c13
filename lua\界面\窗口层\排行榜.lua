local 排行榜  = 窗口层:创建窗口("排行榜", 0, 0, 700, 500)



function 排行榜:初始化()

  self:创建纹理精灵(function()
    __res:取资源动画("pic", "phb.png","图片"):显示(0,0)
    __res:取资源动画("dlzy", 0X75B260A7,"图像"):显示(240,0)
    __res:取资源动画("pic", "11.png","图片"):显示(75,135)
    __res:取资源动画("pic", "22.png","图片"):显示(75,170)
    __res:取资源动画("pic", "33.png","图片"):显示(81,205)
    排行标题:置颜色(0,0,0,255)
    排行标题:取投影图像("4"):显示(85, 240)
    排行标题:取投影图像("5"):显示(85, 271)
    排行标题:取投影图像("6"):显示(85, 302)
    排行标题:取投影图像("7"):显示(85, 333)
    排行标题:取投影图像("8"):显示(85, 364)
    排行标题:取投影图像("9"):显示(85, 395)
    排行标题:取投影图像("10"):显示(80, 426)
  end)
  self.排行榜数据={}
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true

end  




local 排行设置={"伤害排行","法伤排行","收徒排行","剑会排行","剑会季度","镇妖之塔"}

for i, v in ipairs(排行设置) do   
  local 临时函数 = 排行榜:创建单选按钮(v, 54+(i-1)*100, 75)
  function 临时函数:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/fwtb", 0X10000030),1,排行标题:置颜色(255,255,255,255):取投影图像(v,255,255,255,255))
  end
  function 临时函数:左键弹起(x, y)
   排行榜.类型=v
   排行榜:显示设置()
  end
end



function 排行榜:打开(内容)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
      return
    end
   self.图像=nil
   self.类型="伤害排行"
   self.排行榜数据={}
   if 内容 then
    self.排行榜数据=内容
   end
   

   self:显示设置()

end 
function 排行榜:显示设置()
  self[self.类型]:置选中(true)
  self.图像=self:创建纹理精灵(function()
      if self.类型=="伤害排行" then
            排行标题:置颜色(255,255,255,255)
            排行标题:取描边投影图像("排  名"):显示(67, 115)
            排行标题:取描边投影图像("名  称"):显示(158, 115)
            排行标题:取描边投影图像("I   D"):显示(308, 115)
            排行标题:取描边投影图像("伤  害"):显示(465, 115)
            if self.排行榜数据.玩家伤害排行 and #self.排行榜数据.玩家伤害排行>0 then
              排行字体:置颜色(0,0,0,255)
                for i = 1, 10 do
                    if self.排行榜数据.玩家伤害排行[i] then
                      排行字体:取投影图像(self.排行榜数据.玩家伤害排行[i].名称):显示(160, 146+math.floor((i-1)*31))
                      排行字体:取投影图像(self.排行榜数据.玩家伤害排行[i].id):显示(310, 146+math.floor((i-1)*31))
                      排行字体:取投影图像(self.排行榜数据.玩家伤害排行[i].分数):显示(470, 146+math.floor((i-1)*31))
                    end
                end
            end
       elseif self.类型=="法伤排行" then
            排行标题:置颜色(255,255,255,255)
            排行标题:取描边投影图像("排  名"):显示(67, 115)
            排行标题:取描边投影图像("名  称"):显示(158, 115)
            排行标题:取描边投影图像("I   D"):显示(308, 115)
            排行标题:取描边投影图像("法  伤"):显示(465, 115)
            if self.排行榜数据.玩家灵力排行 and #self.排行榜数据.玩家灵力排行>0 then
              排行字体:置颜色(0,0,0,255)
              for i = 1, 10 do
                  if self.排行榜数据.玩家灵力排行[i] then
                    排行字体:取投影图像(self.排行榜数据.玩家灵力排行[i].名称):显示(160,146+math.floor((i-1)*31))
                    排行字体:取投影图像(self.排行榜数据.玩家灵力排行[i].id):显示(310, 146+math.floor((i-1)*31))
                    排行字体:取投影图像(self.排行榜数据.玩家灵力排行[i].分数):显示(470, 146+math.floor((i-1)*31))
                  end
                end
            end
       elseif self.类型=="收徒排行" then
            排行标题:置颜色(255,255,255,255)
            排行标题:取描边投影图像("排  名"):显示(67, 115)
            排行标题:取描边投影图像("师傅名称"):显示(158, 115)
            排行标题:取描边投影图像("I   D"):显示(308, 115)
            排行标题:取描边投影图像("出师人数"):显示(463, 115)
            if self.排行榜数据.玩家师傅排行 and #self.排行榜数据.玩家师傅排行>0 then
              排行字体:置颜色(0,0,0,255)
              for i = 1, 10 do
                  if self.排行榜数据.玩家师傅排行[i] then
                    排行字体:取投影图像(self.排行榜数据.玩家师傅排行[i].名称):显示(160, 146+math.floor((i-1)*31))
                    排行字体:取投影图像(self.排行榜数据.玩家师傅排行[i].id):显示(310, 146+math.floor((i-1)*31))
                    排行字体:取投影图像(self.排行榜数据.玩家师傅排行[i].出师):显示(470, 146+math.floor((i-1)*31))
                  end
                end
            end
       elseif self.类型=="剑会排行" then
            排行标题:置颜色(255,255,255,255)
            排行标题:取描边投影图像("排  名"):显示(67, 115)
            排行标题:取描边投影图像("名  称 "):显示(158, 115)
            排行标题:取描边投影图像("等  级"):显示(260, 115)
            排行标题:取描边投影图像("门  派"):显示(370, 115)
            排行标题:取描边投影图像("积  分"):显示(490, 115)
            if self.排行榜数据.玩家剑会排行 and #self.排行榜数据.玩家剑会排行>0 then
              排行字体:置颜色(0,0,0,255)
              for i = 1, 10 do
                  if self.排行榜数据.玩家剑会排行[i] then
                    排行字体:取投影图像(self.排行榜数据.玩家剑会排行[i].剑会名称):显示(160, 146+math.floor((i-1)*31))
                    排行字体:取投影图像(self.排行榜数据.玩家剑会排行[i].剑会等级):显示(265, 146+math.floor((i-1)*31))
                    排行字体:取投影图像(self.排行榜数据.玩家剑会排行[i].剑会门派):显示(370, 146+math.floor((i-1)*31))
                    排行字体:取投影图像(self.排行榜数据.玩家剑会排行[i].剑会积分):显示(492, 146+math.floor((i-1)*31))
                  end
                end
            end
        elseif self.类型=="剑会季度" then
              排行标题:置颜色(255,255,255,255)
              排行标题:取描边投影图像("排  名"):显示(67, 115)
              排行标题:取描边投影图像("名  称 "):显示(158, 115)
              排行标题:取描边投影图像("等  级"):显示(260, 115)
              排行标题:取描边投影图像("积  分"):显示(370, 115)
              排行标题:取描边投影图像("是否领取"):显示(490, 115)
              if self.排行榜数据.玩家剑会季度 and #self.排行榜数据.玩家剑会季度>0 then
                排行字体:置颜色(0,0,0,255)
                for i = 1, 10 do
                    if self.排行榜数据.玩家剑会季度[i] then
                      排行字体:取投影图像(self.排行榜数据.玩家剑会季度[i].剑会名称1):显示(160, 146+math.floor((i-1)*31))
                      排行字体:取投影图像(self.排行榜数据.玩家剑会季度[i].剑会等级1):显示(265, 146+math.floor((i-1)*31))
                      排行字体:取投影图像(self.排行榜数据.玩家剑会季度[i].剑会积分1):显示(370, 146+math.floor((i-1)*31))
                      排行字体:取投影图像(self.排行榜数据.玩家剑会季度[i].是否领取):显示(492, 146+math.floor((i-1)*31))
                    end
                  end
              end




        elseif self.类型=="镇妖之塔" then
                  self.查看类型=""
                  排行标题:置颜色(255,255,255,255)
                  排行标题:取描边投影图像("排  名"):显示(67, 115)
                  排行标题:取描边投影图像("名  称 "):显示(158, 115)
                  排行标题:取描边投影图像("等  级"):显示(260, 115)
                  排行标题:取描边投影图像("门  派"):显示(370, 115)
                  排行标题:取描边投影图像("层  数"):显示(490, 115)
                  if self.排行榜数据.玩家镇妖层数 and #self.排行榜数据.玩家镇妖层数>0 then
                    排行字体:置颜色(0,0,0,255)
                    for i = 1, 10 do
                        if self.排行榜数据.玩家镇妖层数[i] then
                          排行字体:取投影图像(self.排行榜数据.玩家镇妖层数[i].剑会名称2):显示(160, 146+math.floor((i-1)*31))
                          排行字体:取投影图像(self.排行榜数据.玩家镇妖层数[i].剑会等级2):显示(265, 146+math.floor((i-1)*31))
                          排行字体:取投影图像(self.排行榜数据.玩家镇妖层数[i].剑会门派2):显示(370, 146+math.floor((i-1)*31))
                          排行字体:取投影图像(self.排行榜数据.玩家镇妖层数[i].层数):显示(492, 146+math.floor((i-1)*31))
                        end
                      end
                  end


  
        end 


 
end,1)


end  



function 排行榜:显示(x,y)
  if self.图像 then
    self.图像:显示(x,y)
  end

end
         

          
        

local 关闭 = 排行榜:创建按钮("关闭",631,8)
function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/jmtb", 0x81DD40D3))
end

function 关闭:左键弹起(x, y)
  排行榜:置可见(false)
end



