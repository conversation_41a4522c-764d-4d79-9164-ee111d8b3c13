
local map = class("map")
function map:初始化(path)
    local ud, info = require('gxyq.map')(path)
    self.ud = ud
    for k, v in pairs(info) do
        self[k] = v
    end
    self.mapco = {}
    self.maskco = {}
    self.mask = {}
    self.mapcache= {}
end



function map:更新()
  local r = {self.ud:GetResult()}
  local t = table.remove(r, 1)

  if t == 1 then

      local sf, mask, id = table.unpack(r)
      for k, v in pairs(mask) do
          local mid = v.x .. '_' .. v.y
          if not self.mask[mid] then
              local 遮罩 = require('地图/遮罩')(v,id,mid,__主显.地图.id)
              v.遮罩 = 遮罩
              table.insert(self.maskco, v)
              mask[k] = 遮罩
              self.mask[mid] = 遮罩
          else
              mask[k] = self.mask[mid]
          end
      end
      coroutine.xpcall(self.mapco[id],
          require('SDL.精灵')(sf),
          mask
      )
      self.mapco[id] = nil
      --print('地图加载完成')
  elseif t == 2 then
        local sf, info = table.unpack(r)
        info.遮罩:置图像(sf)
  end

  if not next(self.mapco) and next(self.maskco) then
      local mask = table.remove(self.maskco, 1)
      self.ud:GetMask(mask, true)
  end

end

function map:取障碍()
    return self.ud:GetCell()
end


function map:取精灵(id)
  if self.mapcache[id] then
    return table.unpack(self.mapcache[id])
end
    local co, main = coroutine.running()
    if co and not main then
        self.ud:GetMap(id, true)
        self.mapco[id] = co
        self.mapcache[id] = {
          coroutine.yield()
      }
        return table.unpack(self.mapcache[id])
    end
end


function map:取精灵2(id)

  if self.mapcache[id] then
      return table.unpack(self.mapcache[id])
  end
  local sf, mask = self.ud:GetMap(id)
  if  mask then
      for k, v in pairs(mask) do
          local mid = v.x .. '_' .. v.y
          if not self.mask[mid] then
              local 遮罩 = require('地图/遮罩')(v,id,mid,__主显.地图.id)
              v.遮罩 = 遮罩
              table.insert(self.maskco, v)
              mask[k] = 遮罩
              self.mask[mid] = 遮罩
          else
              mask[k] = self.mask[mid]
          end
      end
  else
      mask = {}
  end
  if sf then
      self.mapcache[id] = {
          require('SDL.精灵')(sf),
          mask,
      }
      return table.unpack(self.mapcache[id])
  end
end




function map:清空缓存()
    --self.ud:SetMode(0x9527)
    self.ud:Clear()
end

function map:不缓存()
  self.ud:SetMode(0x9527)
  return self
end

return map
