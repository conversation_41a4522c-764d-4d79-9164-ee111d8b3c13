--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-30 05:21:12
Last Modified by: GGELUA
Last Modified time: 2024-08-26 03:51:31
--]]
local 战斗界面 = __UI界面["界面层"]["创建我的控件"](__UI界面["界面层"], "战斗界面",490-84 + abbr.py.x, 90 + abbr.py.y, 440+84, 450)
function 战斗界面:初始化()
    self.单挑圈圈 = {
		[2] =__res:取动画(__res:取地址("shape/tx/", 0xAABBCC10)):取动画(1):置当前帧(1):播放(true),
		[3] =__res:取动画(__res:取地址("shape/tx/", 0xAABBCC10)):取动画(1):置当前帧(1):播放(true),
		[4] =__res:取动画(__res:取地址("shape/tx/", 0xAABBCC11)):取动画(1):置当前帧(1):播放(true),
		[5] =__res:取动画(__res:取地址("shape/tx/", 0xAABBCC11)):取动画(1):置当前帧(1):播放(true),
		}
    local tab = {00000600,00000601,00000602,00000603,00000604}
    self.法术次数图片 = {}
    for n = 1, 5 do
        self.法术次数图片[n] = __res:取图像(__res:取地址("shape/mx/", tab[n]))
    end
end

function 战斗界面:重置()
    self.命令类型 = nil
    self.单挑召唤=nil
    self.命令附加 = 4
    if not self.是否可见 then
        self:置可见(true)
    end
    if 战斗界面["操作类"]["是否可见"] then
        战斗界面["操作类"]["置可见"](战斗界面["操作类"], false)
        显示战斗鼠标=false
    end
    local 允许默认法术 = false
    local tcp
    if self.参战单位[self.单位编号[self.操作对象]].数据["自动指令"] and
        self.参战单位[self.单位编号[self.操作对象]].数据["自动指令"]["类型"] == "法术" then
        for i, v in ipairs(self.参战单位[self.单位编号[self.操作对象]]["主动技能"]) do
            if v["名称"] == self.参战单位[self.单位编号[self.操作对象]].数据["自动指令"]["参数"] then
                允许默认法术 = v
                local lssj
                if self.参战单位[self.单位编号[self.操作对象]]["单位类型"] == "角色" then
                    lssj = "人物"
                elseif self.参战单位[self.单位编号[self.操作对象]]["单位类型"] == "bb" then
                    lssj = "召唤兽"
                elseif self.参战单位[self.单位编号[self.操作对象]]["单位类型"] == "子女" then
                    lssj = "子女"
                end
                local nsf = require("SDL.图像")(45, 45)
                local lssc = 取技能(v["名称"], lssj)
                if nsf["渲染开始"](nsf) then
                    nsf["渲染清除"](nsf, 0, 0, 0, 255)
                    if v["剩余冷却回合"] then
                        __res:取图像(__res:取地址("shape/jn/", lssc[7])):到灰度():拉伸(45, 45):显示(0,0)
                    else
                        __res:取图像(__res:取地址("shape/jn/", lssc[7])):拉伸(45, 45):显示(0,0)
                    end

                    -- __主控["公用蒙版3"]["显示"](__主控["公用蒙版3"], 0, 0)
                    nsf["渲染结束"](nsf)
                end
                tcp = nsf["到精灵"](nsf)
                -- tcp["置混合"](tcp, 2)
            end
        end
    end
    if self.参战单位[self.单位编号[self.操作对象]]["单位类型"] == "角色" then
        战斗界面["召唤兽指令类"]["置可见"](战斗界面["召唤兽指令类"], false)
        战斗界面["人物指令类"]["置可见"](战斗界面["人物指令类"], true)
        if self.参战单位[self.单位编号[self.操作对象]].门派 == "九黎城" and self.参战单位[self.单位编号[self.操作对象]].黎魂 then
            if self.九黎多次[self.单位编号[self.操作对象]] == nil then
                序列 = 0
            else                                                                            
                序列 = #self.九黎多次[self.单位编号[self.操作对象]]
            end
            local 序列 = self.参战单位[self.单位编号[self.操作对象]].黎魂 - 序列
            if 序列 > 0 then
                local nsf = require("SDL.图像")(960, 540)
                if nsf["渲染开始"](nsf) then
                    local tab = {00000600,00000601,00000602,00000603,00000604}
                    __res:取图像(__res:取地址("shape/mx/", tab[math.min(序列, 5)])):显示(504,40)
                    nsf["渲染结束"](nsf)
                end
                self.图像 = nsf["到精灵"](nsf)
            end
        end

        if 允许默认法术 then
            self.人物指令类["默认法术"]["置正常精灵"](self.人物指令类["默认法术"],tcp["置中心"](tcp, 0, 0))
            self.人物指令类["默认法术"]["置按下精灵"](self.人物指令类["默认法术"],tcp["置中心"](tcp, -1, -1))
            self.人物指令类["默认法术"]["置可见"](self.人物指令类["默认法术"], true)
            self.人物指令类["默认法术"]["技能信息"] = 允许默认法术
        else
            self.人物指令类["默认法术"]["置可见"](self.人物指令类["默认法术"], false)
            self.人物指令类["默认法术"]["技能信息"] = nil
        end
    else
        战斗界面["召唤兽指令类"]["置可见"](战斗界面["召唤兽指令类"], true)
        战斗界面["人物指令类"]["置可见"](战斗界面["人物指令类"], false)
        if 允许默认法术 then
            self.召唤兽指令类["默认法术按钮"]["置正常精灵"](self.召唤兽指令类.默认法术按钮, tcp["置中心"](tcp, 0, 0))
            self.召唤兽指令类["默认法术按钮"]["置按下精灵"](self.召唤兽指令类["默认法术按钮"], tcp["置中心"](tcp, -1, -1))
            self.召唤兽指令类["默认法术按钮"]["置可见"](self.召唤兽指令类["默认法术按钮"],
                true)
            self.召唤兽指令类["默认法术按钮"]["技能信息"] = 允许默认法术
        else
            self.召唤兽指令类["默认法术按钮"]["置可见"](self.召唤兽指令类["默认法术按钮"],
                false)
            self.召唤兽指令类["默认法术按钮"]["技能信息"] = nil
        end
    end
    -- nsf = require("SDL.图像")(300, 60)
    -- if nsf["渲染开始"](nsf) then
    --     字体14["置颜色"](字体14, __取颜色("红色"))
    --     字体14["取描边图像"](字体14, "操作对象", 247, 247, 247)["显示"](字体14["取描边图像"](字体14
    --         , "操作对象", 247, 247, 247), 200, 0)
    --     字体14["取描边图像"](字体14, self.参战单位[self.单位编号[self.操作对象]]["名称"], 247, 247
    --         , 247)["显示"](字体14["取描边图像"](字体14, self.参战单位[self.单位编号[self.操作对象
    --         ]]["名称"], 247, 247, 247), 200, 15)
    --     nsf["渲染结束"](nsf)
    -- end
    -- self.图像 = nsf["到精灵"](nsf)
end

function 战斗界面:更新类型(sj)
    战斗界面["置可见"](战斗界面, true, true)
    self.单位编号 = sj.mb
    self.单位id = sj.id
    self.操作对象 = 1
    self.完成指令 = {
        mb = sj.mb
    }
    self.对象上限 = #sj.mb
    self.参战单位 = __战斗主控["战斗单位"]
    self.命令参数 = ""
    self.九黎多次 = {}
    战斗连击显示 = {}
    战斗连击单位 = 0
    for i, v in ipairs(self.单位编号) do
        self.参战单位[v]["主动技能"] = sj["zdjn" .. i]
        self.参战单位[v].数据["自动指令"] = sj.zdzl[i]
    end
    for n = 1, #sj.mb do
        self.完成指令[n] = {}
    end
    self.重置(self)
end

function 战斗界面:操作重置()
    if not self.是否可见 then
        self:置可见(true)
    end
    if self.人物指令类["是否可见"] then
        战斗界面["人物指令类"]["置可见"](战斗界面["人物指令类"], false)
    end
    if self.召唤兽指令类["是否可见"] then
        战斗界面["召唤兽指令类"]["置可见"](战斗界面["召唤兽指令类"], false)
    end
    self.操作类["置可见"](self.操作类, true)
    显示战斗鼠标=true
end

function 战斗界面:完成命令()
    发送数据(5502, self.完成指令)
    __战斗主控["进程"] = "等待"
    self:置可见(false)
    self.图像 = nil
    显示战斗鼠标=false
end

function 战斗界面:取类型选择(敌我, bh)
    -- if self.命令类型=="法术" or self.命令类型=="道具" or self.命令类型=="灵宝" then
	-- 	if 敌我==2 and  (self.命令附加==4 or self.命令附加==99 ) then
	-- 		return true
	-- 	elseif 敌我==1 and (self.命令附加==5 or self.命令附加==6 or self.命令附加==3 or self.命令附加==99) then
	-- 		return true
	-- 	end
	-- elseif self.命令类型=="攻击" or self.命令类型=="捕捉"  then
	-- 	if 敌我==2 then
	-- 		return true
	-- 	end
	-- end
	-- return false
    if self.命令类型 == "法术" or self.命令类型 == "特技" or self.命令类型 == "道具" or
        self.命令类型 == "灵宝" then
        if 2 == 敌我 and 4 == self.命令附加 then
            return true
        elseif 1 == 敌我 and (5 == self.命令附加 or 6 == self.命令附加 or 3 == self.命令附加) then
            return true
        end
    elseif self.命令类型 == "捕捉" and 2 == 敌我 then
        return true
    elseif self.命令类型 == "保护" and 1 == 敌我 and bh ~= self.操作对象 then
        return true
    elseif self.命令类型 == "攻击" then
        return true
    elseif not self.命令类型 and 2 == 敌我 then
        self.命令类型 = "攻击"
        return true
    end
    return false
end

function 战斗界面:设置指令0(编号)
    if self.命令类型 == "攻击" and 1 == self.参战单位[编号]["敌我"] then
        if 编号 == self.单位编号[self.操作对象] then
            self.重置(self)
            return
        else
            self.命令类型 = "攻击"
            self.命令附加 = "友伤"
        end
    elseif not self.命令类型 then
        self.命令类型 = "攻击"
    end
    --print(self.命令类型)
    self.完成指令[self.操作对象] = {
        ["类型"] = self.命令类型,
        ["目标"] = 编号,
        ["敌我"] = 0,
        ["参数"] = self.命令参数,
        ["附加"] = self.命令附加,
        id = self.单位id[self.操作对象]
    }
    local 继续 = true
    if self.参战单位[self.单位编号[self.操作对象]].门派 == "九黎城" and self.命令类型 == "法术" and self.参战单位[self.单位编号[self.操作对象]].黎魂 ~= nil and 九黎城攻击技能[self.命令参数] ~= nil then
        if self.九黎多次[self.单位编号[self.操作对象]] == nil then
            self.九黎多次[self.单位编号[self.操作对象]] = {
                {
                    self.命令参数,
                    编号
                }
            }
            继续 = false
        elseif #self.九黎多次[self.单位编号[self.操作对象]] < self.参战单位[self.单位编号[self.操作对象]].黎魂 - 1 then
            self.九黎多次[self.单位编号[self.操作对象]][#self.九黎多次[self.单位编号[self.操作对象]] + 1] = {
                self.命令参数,
                编号
            }
            继续 = false
        else
            self.九黎多次[self.单位编号[self.操作对象]][#self.九黎多次[self.单位编号[self.操作对象]] + 1] = {
                self.命令参数,
                编号
            }
            self.完成指令[self.操作对象] = {
                敌我 = 0,
                类型 = self.命令类型,
                目标 = 编号,
                参数 = self.九黎多次[self.单位编号[self.操作对象]],
                附加 = self.命令附加
            }
        end
    end

    if 继续 then
        if self.操作对象 >= self.对象上限 then
            self.完成命令(self)
        else
            self.操作对象 = self.操作对象 + 1

        end
    end
    self.重置(self)
end

function 战斗界面:设置指令1(编号)
    self.完成指令[self.操作对象] = {
        ["类型"] = self.命令类型,
        ["目标"] = 编号,
        ["敌我"] = 0,
        ["参数"] = self.命令参数,
        ["附加"] = self.命令附加,
        id = self.单位id[self.操作对象]
    }
    local 继续 = true
    if self.参战单位[self.单位编号[self.操作对象]].门派 == "九黎城" and self.命令类型 == "法术" and self.参战单位[self.单位编号[self.操作对象]].黎魂 ~= nil and 九黎城攻击技能[self.命令参数] ~= nil then
        if self.九黎多次[self.单位编号[self.操作对象]] == nil then
            self.九黎多次[self.单位编号[self.操作对象]] = {
                {
                    self.命令参数,
                    编号
                }
            }
            继续 = false
        elseif #self.九黎多次[self.单位编号[self.操作对象]] < self.参战单位[self.单位编号[self.操作对象]].黎魂 - 1 then
            self.九黎多次[self.单位编号[self.操作对象]][#self.九黎多次[self.单位编号[self.操作对象]] + 1] = {
                self.命令参数,
                编号
            }
            继续 = false
        else
            self.九黎多次[self.单位编号[self.操作对象]][#self.九黎多次[self.单位编号[self.操作对象]] + 1] = {
                self.命令参数,
                编号
            }
            self.完成指令[self.操作对象] = {
                敌我 = 0,
                类型 = self.命令类型,
                目标 = 编号,
                参数 = self.九黎多次[self.单位编号[self.操作对象]],
                附加 = self.命令附加
            }
        end
    end
    if 继续 then
        if self.操作对象 >= self.对象上限 then
            self.完成命令(self)
        else
            self.操作对象 = self.操作对象 + 1

        end
    end
    self.重置(self)
end

function 战斗界面:设置法术参数(法术, silllx)
    -- print("设置法术参数",法术.剩余冷却回合)
    if nil == 法术 or 法术.剩余冷却回合 then
        return
    end
    self.命令类型 = "法术"
    if "特技" == silllx then
        self.命令类型 = "特技"
    end
    self.命令参数 = 法术["名称"]
    local 临时种类 = 取技能(法术["名称"])
    self.法术名称 = 法术["名称"]
    self.命令附加 = 临时种类[3]
    self.命令版面 = false
    self.法术开关 = false
    if 法术["名称"] == "妙手空空" then
        self.命令附加 = 4
    end
    if 2 == self.命令附加 then
        self:设置指令0(self.单位编号[self.操作对象])
    else
        self.操作重置(self)
    end
end

function 战斗界面:设置披坚执锐(法术,可选编号)
    if nil == 法术 or not 法术.可选 then
        return
    end
    self.完成指令[self.操作对象]={类型="披坚执锐",目标=1,敌我=0,参数=法术.名称,附加=可选编号,id=self.单位id[self.操作对象]}
    if self.操作对象 >= self.对象上限 then
        发送数据(5502, self.完成指令)
        __战斗主控["进程"] = "等待"
        self:置可见(false)
        self.图像 = nil
        显示战斗鼠标=false
    else
        self.操作对象 = self.操作对象 + 1
        self.重置(self)
    end
end

function 战斗界面:设置道具(data, lx)
    __UI界面["窗口层"]["战斗道具"]["打开"](__UI界面["窗口层"]["战斗道具"], data["道具"], lx)
end

function 战斗界面:设置灵宝(data)
    __UI界面["窗口层"]["战斗灵宝"]["打开"](__UI界面["窗口层"]["战斗灵宝"], data)
end

function 战斗界面:设置道具参数(编号, 对象)
    self.命令类型 = "道具"
    self.命令附加 = 对象
    self.命令参数 = 编号
    __UI界面["窗口层"]["战斗道具"]["置可见"](__UI界面["窗口层"]["战斗道具"], false)
    if 2 == self.命令附加 then
        self:设置指令0(self.单位编号[self.操作对象])
    else
        self.操作重置(self)
    end
end

function 战斗界面:设置灵宝参数(编号, 对象)
    self.命令类型 = "道具"
    self.命令附加 = 对象
    self.命令参数 = 编号
    __UI界面["窗口层"]["战斗灵宝"]["置可见"](__UI界面["窗口层"]["战斗灵宝"], false)
    if 2 == self.命令附加 then
        self:设置指令0(self.单位编号[self.操作对象])
    else
        self.操作重置(self)
    end
end

function 战斗界面:设置逃跑(id)
    self.完成指令[self.操作对象] = {
        ["类型"] = "逃跑",
        ["目标"] = 1,
        ["敌我"] = 0,
        ["参数"] = self.命令参数,
        ["附加"] = 1,
        id = self.单位id[self.操作对象]
    }
    if self.操作对象 >= self.对象上限 then
        self.完成命令(self)
    else
        self.操作对象 = self.操作对象 + 1
        self.重置(self)
    end
end

function 战斗界面:设置召唤(id)
    self.命令类型 = "召唤"
    self.完成指令[self.操作对象]={类型=self.命令类型,目标=id,敌我=0,参数=self.命令参数,附加=self.命令附加,id=self.单位id[self.操作对象],位置=self.召唤位置}
    self:置可见(true)
    __UI界面["窗口层"]["战斗召唤"]["置可见"](__UI界面["窗口层"]["战斗召唤"], false)
    if self.操作对象 >= self.对象上限 then
        self.完成命令(self)
    else
        self.操作对象 = self.操作对象 + 1
        self.重置(self)
    end
end

local 人物指令类 = 战斗界面["创建控件"](战斗界面, "人物指令类", 84, 0, 440, 450)
function 人物指令类:初始化()
end

for i, v in ipairs({
    {
        name = "背景",
        x = 376,
        y = 62,
        tcp = __res:getPNGCC(10, 3, 3, 63, 297)
    },

    {
        name = "捕捉",
        x = 379,
        y = 66+24*10,
        tcp = __res:getPNGCC(10, 191, 3, 57, 26)
    },
    {
        name = "逃跑",
        x = 379,
        y = 66+24*11,
        tcp = __res:getPNGCC(10, 124, 59, 57, 26)
    },
    {
        name = "召还",
        x = 379,
        y = 66+24*8,
        tcp = __res:getPNGCC(10, 72, 357, 57, 26)
    },
    {
        name = "保护",
        x = 379,
        y = 66+24*6,
        tcp = __res:getPNGCC(10, 108, 94, 57, 26)
    },
    {
        name = "防御",
        x = 379,
        y = 66+24*5,
        tcp = __res:getPNGCC(10, 72, 325, 57, 26)
    },
    {
        name = "自动",
        x = 379,
        y = 66+24*9,
        tcp = __res:getPNGCC(10, 72, 389, 57, 26)
    },
    {
        name = "召唤",
        x = 379,
        y = 66+24*7,
        tcp = __res:getPNGCC(10, 72, 293, 57, 26)
    },
    {
        name = "道具",
        x = 379,
        y = 66+24*3,
        tcp = __res:getPNGCC(10, 128, 3, 57, 26)
    },
    {
        name = "特技",
        x = 379,
        y = 66+24*2,
        tcp = __res:getPNGCC(10, 72, 197, 57, 26)
    },
    {
        name = "灵宝",
        x = 379,
        y = 66+24*4,
        tcp = __res:getPNGCC(10, 72, 133, 57, 26)
    },
    {
        name = "法宝",
        x = 379,
        y = 66+24,
        tcp = __res:getPNGCC(10, 72, 165, 57, 26)
    },
    {
        name = "法术",
        x = 379,
        y = 66,
        tcp = __res:getPNGCC(10, 72, 229, 57, 26)
    },
    -- {
    --     name = "攻击",
    --     x = 379,
    --     y = 200,
    --     tcp = __res:getPNGCC(10, 780, 3, 57, 26)
    -- },
    {
        name = "默认法术",
        x = 302,
        y = 0,
        tcp = __res:getPNGCC(2, 780, 3, 54, 54)
    }
}) do
    local 临时函数 = 人物指令类["创建我的按钮"](人物指令类, v.tcp, v.name, v.x, v.y)
    function 临时函数:左键弹起(x, y)
        if v.name == "捕捉" then
            战斗界面["命令类型"] = "捕捉"
            战斗界面["命令附加"] = 4
            战斗界面["操作重置"](战斗界面)
        elseif v.name == "逃跑" then
            战斗界面["设置逃跑"](战斗界面)
        elseif v.name == "召还" then
        elseif v.name == "保护" then
            战斗界面["命令类型"] = "保护"
            战斗界面["操作重置"](战斗界面)
        elseif v.name == "防御" then
            战斗界面["命令类型"] = "防御"
            战斗界面["设置指令1"](战斗界面, 0)
        elseif v.name == "自动" then
            发送数据(5522)
        elseif v.name == "召唤" then
            if 战斗界面.单挑召唤 then
                战斗界面.单挑召唤=nil
                显示战斗鼠标=false
                return
            end
            if __战斗主控.单挑模式 then
                战斗界面.命令类型="单挑召唤"
                显示战斗鼠标=true
                战斗界面.单挑召唤=true
            else
                发送数据(5505, {
                    ["玩家id"] = 战斗界面["单位id"][战斗界面["操作对象"]]
                })
            end
        elseif v.name == "道具" then
            发送数据(5504, {
                ["玩家id"] = 战斗界面["单位id"][战斗界面["操作对象"]]
            })
        elseif v.name == "特技" then
            战斗界面["命令类型"] = "特技"
            __UI界面["窗口层"]["战斗法术"]:打开(战斗界面["参战单位"][ 战斗界面["单位编号"][战斗界面["操作对象"]]]["特技技能"], 
            战斗界面["参战单位"][战斗界面["单位编号"][战斗界面["操作对象"]]]["单位类型"],"法术")
        -- elseif v.name == "灵宝" then
        --     发送数据(5519, {
        --         ["玩家id"] = 战斗界面["单位id"][战斗界面["操作对象"]]
        --     })
        elseif v.name == "法宝" then
            发送数据(5508, {
                ["玩家id"] = 战斗界面["单位id"][战斗界面["操作对象"]]
            })
        elseif v.name == "法术" then
            战斗界面["命令类型"] = "法术"
            __UI界面.窗口层.战斗法术:打开(
                战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].主动技能, 
                战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].单位类型, 
                战斗界面.命令类型,
                战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].追加法术)
        elseif v.name == "攻击" then
            战斗界面["命令类型"] = "攻击"
            战斗界面["操作重置"](战斗界面)
        elseif v.name == "默认法术" then
            战斗界面["命令类型"] = "法术"
            __UI界面["界面层"]["战斗界面"]["设置法术参数"](__UI界面["界面层"]["战斗界面"],
                临时函数["技能信息"])
        end
    end
end
local 召唤兽指令类 = 战斗界面["创建控件"](战斗界面, "召唤兽指令类", 84, 0, 440, 450)
function 召唤兽指令类:初始化()
    self:置可见(false)
end

for i, v in ipairs({
    {
        name = "背景按钮",
        x = 376,
        y = 100,
        tcp = __res:getPNGCC(10, 3, 306, 63, 128)
    },
    {
        name = "逃跑按钮",
        x = 379,
        y = 103+24*4,
        tcp = __res:getPNGCC(10, 124, 59, 57, 26)
    },
    {
        name = "保护按钮",
        x = 379,
        y = 103+24*3,
        tcp = __res:getPNGCC(10, 108, 94, 57, 26)
    },
    {
        name = "防御按钮",
        x = 379,
        y = 103+24*2,
        tcp = __res:getPNGCC(10, 72, 325, 57, 26)

    },
    -- {
    --     name = "自动按钮",
    --     x = 376,
    --     y = 373,
    --     tcp = __res:getPNGCC(10, 72, 325, 57, 26)
    -- },
    {
        name = "道具按钮",
        x = 379,
        y = 103+24,
        tcp = __res:getPNGCC(10, 128, 3, 57, 26)
    },
    {
        name = "法术按钮",
        x = 379,
        y = 103,
        tcp = __res:getPNGCC(10, 72, 229, 57, 26)
    },
    -- {
    --     name = "攻击按钮",
    --     x = 376,
    --     y = 188,
    --     tcp = __res:getPNGCC(2, 780, 3, 54, 54)
    -- },
    {
        name = "默认法术按钮",
        x = 302,
        y = 188,
        tcp = __res:getPNGCC(2, 780, 3, 54, 54)
    }
}) do
    local 临时函数 = 召唤兽指令类["创建我的按钮"](召唤兽指令类, v.tcp, v.name, v.x, v.y)
    function 临时函数:左键弹起(x, y)
        if v.name == "逃跑按钮" then
            战斗界面["设置逃跑"](战斗界面)
        elseif v.name == "保护按钮" then
            战斗界面["命令类型"] = "保护"
            战斗界面["操作重置"](战斗界面)
        elseif v.name == "防御按钮" then
            战斗界面["命令类型"] = "防御"
            战斗界面["设置指令1"](战斗界面, 0)
        elseif v.name == "自动按钮" then
        elseif v.name == "道具按钮" then
            发送数据(5504, {
                ["玩家id"] = 战斗界面["单位id"][战斗界面["操作对象"]]
            })
        elseif v.name == "法术按钮" then
            战斗界面["命令类型"] = "法术"
            __UI界面["窗口层"]["战斗法术"]:打开(战斗界面["参战单位"][战斗界面["单位编号"][战斗界面["操作对象"]]]["主动技能"], 战斗界面["参战单位"][战斗界面["单位编号"][战斗界面["操作对象"]]]["单位类型"], 战斗界面["命令类型"])
        elseif v.name == "攻击按钮" then
            战斗界面["命令类型"] = "攻击"
            战斗界面["操作重置"](战斗界面)
        elseif v.name == "默认法术按钮" then
            战斗界面["命令类型"] = "法术"
            __UI界面["界面层"]["战斗界面"]["设置法术参数"](__UI界面["界面层"]["战斗界面"],临时函数["技能信息"])
        end
    end
end
local 操作类 = 战斗界面["创建控件"](战斗界面, "操作类", 0, 0, 440, 450)
function 操作类:初始化()
    self:置可见(false)
end

for i, v in ipairs({
    -- {
    --     name = "随机目标",
    --     x = 376,
    --     y = 312,
    --     tcp = __res:getPNGCC(2, 903, 1, 68, 55)
    -- },
    {
        name = "返回",
        x = 376,
        y = 373,
        tcp = __res:getPNGCC(2, 973, 1, 56, 56)
    }
}) do
    local 临时函数 = 操作类["创建我的按钮"](操作类, v.tcp, v.name, v.x, v.y)
    function 临时函数:左键弹起(x, y)
        if v.name == "随机目标" then
        elseif v.name == "返回" then
            战斗界面["重置"](战斗界面)
        end
    end
end

function 战斗界面:单挑显示(x,y)
    self.ssdjfj=false
    if self.单挑召唤 then
        for i=2,5 do
            self.单挑圈圈[i]:更新(0.05)
            self.单挑圈圈[i]:显示(__战斗主控.阵型位置.单挑.我方[i].x-180+(引擎.宽度/2-400),__战斗主控.阵型位置.单挑.我方[i].y-25+(引擎.高度/2-300))
            if self.单挑圈圈[i]:检查点(x, y) then
                self.召唤位置=i
                self.ssdjfj=true
            end
        end
    elseif self.召唤兽指令类["是否可见"] then
        if self.参战单位[self.单位编号[self.操作对象]].单位类型 == "bb" then
			local bh = self.单位编号[self.操作对象]
			-- print(self.参战单位[bh].排序位置)
			self.单挑圈圈[self.参战单位[bh].排序位置]:更新(0.05)
			self.单挑圈圈[self.参战单位[bh].排序位置]:显示(__战斗主控.阵型位置.单挑.我方[self.参战单位[bh].排序位置].x-180+(引擎.宽度/2-400),__战斗主控.阵型位置.单挑.我方[self.参战单位[bh].排序位置].y-25+(引擎.高度/2-300))
		end
    end
end

function 战斗界面:左键弹起(x, y)
    if self.单挑召唤 and self.ssdjfj then
        显示战斗鼠标=false
        战斗界面.单挑召唤=nil
        发送数据(5505, {
            ["玩家id"] = 战斗界面["单位id"][战斗界面["操作对象"]]
        })
    end
end