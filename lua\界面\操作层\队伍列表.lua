--[[
LastEditTime: 2024-05-01 04:14:32
--]]

local 队伍列表 = 窗口层:创建窗口("队伍列表",0, 0, 630, 315)
local 队伍加成 ={
    天覆阵={[1]="加成类型:全伤,加成数值:20%",[2]="加成类型:全伤,加成数值:20%",[3]="加成类型:全伤,加成数值:20%",[4]="加成类型:全伤,加成数值:20%",[5]="加成类型:全伤,加成数值:20%"},
    风扬阵={[1]="加成类型:全伤、速度,加成数值:20%、5%",[2]="加成类型:全伤,加成数值:10%",[3]="加成类型:全伤,加成数值:10%",[4]="加成类型:全伤,加成数值:10%",[5]="加成类型:全伤,加成数值:10%"},
    虎翼阵={[1]="加成类型:全伤,加成数值:25%",[2]="加成类型:全防,加成数值:20%",[3]="加成类型:全防,加成数值:20%",[4]="加成类型:物伤,加成数值:20%",[5]="加成类型:物伤,加成数值:20%"},
    云垂阵={[1]="加成类型:全防,加成数值:25%",[2]="加成类型:物防,加成数值:10%",[3]="加成类型:全伤,加成数值:20%",[4]="加成类型:速度,加成数值:10%",[5]="加成类型:速度,加成数值:10%"},
    鸟翔阵={[1]="加成类型:速度,加成数值:20%",[2]="加成类型:速度,加成数值:10%",[3]="加成类型:速度,加成数值:10%",[4]="加成类型:速度,加成数值:15%",[5]="加成类型:速度,加成数值:15%"},
    地载阵={[1]="加成类型:全防,加成数值:15%",[2]="加成类型:全伤,加成数值:15%",[3]="加成类型:全防,加成数值:15%",[4]="加成类型:全防,加成数值:15%",[5]="加成类型:速度,加成数值:10%"},
    龙飞阵={[1]="加成类型:法防,加成数值:20%",[2]="加成类型:物防,加成数值:20%",[3]="加成类型:速度,加成数值:20%",[4]="加成类型:法伤,加成数值:20%",[5]="加成类型:全伤,加成数值:10%"},
    蛇蟠阵={[1]="加成类型:法伤,加成数值:15%",[2]="加成类型:法伤,加成数值:15%",[3]="加成类型:法伤,加成数值:15%",[4]="加成类型:全伤,加成数值:10%",[5]="加成类型:全伤,加成数值:10%"},
    鹰啸阵={[1]="加成类型:物防,加成数值:10%",[2]="加成类型:速度,加成数值:15%",[3]="加成类型:速度,加成数值:15%",[4]="加成类型:全伤,加成数值:15%",[5]="加成类型:全伤,加成数值:10%"},
    雷绝阵={[1]="加成类型:固伤、宝宝全伤,加成数值:20%、10%",[2]="加成类型:固伤、宝宝全伤,加成数值:20%、10%",[3]="加成类型:固伤、宝宝全伤,加成数值:20%、10%",[4]="加成类型:固伤、宝宝全伤,加成数值:10%、10%",[5]="加成类型:固伤、宝宝全伤,加成数值:10%、10%"},
    普通={[1]="加成类型:无,加成数值:无",[2]="加成类型:无,加成数值:无",[3]="加成类型:无,加成数值:无",[4]="加成类型:无,加成数值:无",[5]="加成类型:无,加成数值:无"},
     }

function 队伍列表:初始化()
      self:创建纹理精灵(function()
      置窗口背景("队伍列表",0,0,640,315,true):显示(0,0)
      取输入背景(0, 0, 90, 22):显示(150,50)
      取输入背景(0, 0, 40, 22):显示(280,50)
      取输入背景(0, 0, 40, 22):显示(345,50)
      文本字体:置颜色(255,255,255)
      文本字体:取图像("令牌"):显示(25,53)
      文本字体:取图像("至"):显示(325,53)
      文本字体:取图像("等级:"):显示(245,53)
   
    end
  )
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.令牌图标= __res:取资源动画('dlzy',0x2231EBB4,"动画"):置中心(0,0)
    self.可初始化=true
    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-30, 5)
      else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-21, 5)
    end
end

function 队伍列表:打开()
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end

    self:重置()
end







function 队伍列表:重置()
        self.选中 = nil
        self.阵型显示=文本字体:置颜色(0,0,0,255):取精灵(_tp.队伍数据.阵型)
        self.最小等级:置数值(_tp.队伍数据.限制等级[1] or 0)
        self.最大等级:置数值(_tp.队伍数据.限制等级[2] or 175)
        self.队员网格:置数据()
        self.图像=self:创建纹理精灵(function()
              for i = 1, 5 do
                  取输入背景(0, 0, 116, 22):显示((i-1)*121,0)
                  取输入背景(0, 0, 116, 22):显示((i-1)*121,25)
                  取输入背景(0, 0, 116, 22):显示((i-1)*121,50)
                  if _tp.队伍数据[i] then
                    文本字体:置颜色(39, 53, 81)
                    文本字体:取图像(_tp.队伍数据[i].名称):显示((116 - 文本字体:取宽度(_tp.队伍数据[i].名称)) // 2 + (i-1)*121, 4)
                    文本字体:取图像(_tp.队伍数据[i].门派):显示((116 - 文本字体:取宽度(_tp.队伍数据[i].门派)) // 2 + (i-1)*121, 29)
                    文本字体:取图像(_tp.队伍数据[i].等级 .. "级"):显示((116 - 文本字体:取宽度(_tp.队伍数据[i].等级 .. "级")) // 2 + (i-1)*121, 54)
                  end
              end
        end,1,600,80)



        self:按钮设置()
end





function 队伍列表:按钮设置()
        self.查看:置禁止(true)
        self.踢出队伍:置禁止(true)
        self.转让队长:置禁止(true)
        if self.选中 and self.选中~=1 then
            self.查看:置禁止(false)
            self.踢出队伍:置禁止(false)
            self.转让队长:置禁止(false)
        end



end
function 队伍列表:显示(x,y)
      if  self.令牌图标 then
            self.令牌图标:显示(x+70,y+65)
      end  
      if self.阵型显示 then
          self.阵型显示:显示(x+155,y+53)
      end
      if self.图像 then
          self.图像:显示(x+15,y+235)
      end
end
function 队伍列表:更新(dt)
    if  self.令牌图标 then
          self.令牌图标:更新(dt)
    end   
end




local 最小等级 = 队伍列表:创建文本输入("最小等级", 285, 53, 30, 18)
function 最小等级:初始化()
    self:取光标精灵()
    self:置限制字数(3)
    self:置颜色(0, 0, 0, 255)
    self:置模式(2)
end
function 最小等级:输入事件()
    if self:取数值()<=0 then
        self:置数值(0)
    elseif self:取数值()>=175 then
        self:置数值(175)
        队伍列表.最大等级:置数值(175)
    elseif self:取数值()>=队伍列表.最大等级:取数值() then
            self:置数值(队伍列表.最大等级:取数值())
    end
end
local 最大等级 = 队伍列表:创建文本输入("最大等级", 350, 53, 30, 18)
function 最大等级:初始化()
    self:取光标精灵()
    self:置限制字数(3)
    self:置颜色(0, 0, 0, 255)
    self:置模式(2)
end
function 最大等级:输入事件()
    if self:取数值()<=0 then
        self:置数值(0)
        队伍列表.最小等级:置数值(0)
    elseif self:取数值()>=175 then
        self:置数值(175)
    end
end

local 阵型 =  队伍列表:创建红色按钮("阵型", "阵型",85, 50,60,22) --红色
function 阵型:左键弹起(x, y)
    请求服务(4008)
end


local 等级 =  队伍列表:创建红色按钮("等级", "等级", 390, 35,60,22) --红色
function 等级:左键弹起(x, y)
 
        if 队伍列表.最小等级:取文本()=="" or 队伍列表.最大等级:取文本()=="" or (队伍列表.最小等级:取数值() == 队伍列表.最大等级:取数值()) then
            __UI弹出.提示框:打开("#Y输入等级错误!")
        elseif 队伍列表.最小等级:取数值()>队伍列表.最大等级:取数值() then
             __UI弹出.提示框:打开("#Y设置的最低等级不能比最高等级低噢!")
        else
            if 队伍列表.最大等级:取数值()<=队伍列表.最小等级:取数值() then
                    队伍列表.最大等级:置数值(队伍列表.最小等级:取数值())
            end
            请求服务(4012,{序列={队伍列表.最小等级:取数值(),队伍列表.最大等级:取数值()}})
        end

end
local 踢出队伍 =队伍列表:创建红色按钮("踢出队伍", "踢出队伍", 455, 35,74,22)
function 踢出队伍:左键弹起(x, y)
        请求服务(4007, {序列 = 队伍列表.选中})
end
local 离队队伍 =队伍列表:创建红色按钮("离队队伍", "离队队伍", 534, 35,74,22)
function 离队队伍:左键弹起(x, y)
        请求服务(4006)
end
local 查看 =  队伍列表:创建红色按钮("查看", "查看", 390, 60,60,22) --红色
function 查看:左键弹起(x, y)
        请求服务(4011, {序列 = 队伍列表.选中})
end
local 申请列表 =队伍列表:创建红色按钮("申请列表", "申请列表", 455, 60,74,22)
function 申请列表:左键弹起(x, y)
        请求服务(4003)  
end
local 转让队长 =队伍列表:创建红色按钮("转让队长", "转让队长", 534, 60,74,22)
function 转让队长:左键弹起(x, y)
        请求服务(4010, {序列 = 队伍列表.选中})
        队伍列表:置可见(false)
end


local 队员网格 = 队伍列表:创建网格("队员网格",15, 90,600, 145)  
function 队员网格:初始化()
  self:创建格子(116, 145, 0, 5, 1, 5)
end
function 队员网格:左键弹起(x, y, a)
  if _tp.队伍数据 and  _tp.队伍数据[a]  then
        if 队伍列表.选中 then
            self.子控件[队伍列表.选中]._spr.确定 =nil
            if __主显.主角.是否队长 and a~=1 and _tp.队伍数据[a].id ~= 角色信息.数字id then
                if 队伍列表.选中==a and _tp.多角色[_tp.队伍数据[a].id] then
                    请求服务(63,{参数=_tp.队伍数据[a].id,文本="切换角色"})
                elseif 队伍列表.选中~=a  then
                    请求服务(4013, {序列 = 队伍列表.选中,目标 = a})
                end
            end
        end
        if _tp.队伍数据[a].id ~= 角色信息.数字id  then
            队伍列表.选中=a
            self.子控件[a]._spr.确定 = true
        else
           队伍列表.选中=nil
        end
        队伍列表:按钮设置()
  else
      if 队伍列表.选中 and self.子控件[队伍列表.选中]._spr then
          self.子控件[队伍列表.选中]._spr.确定 =nil
      end
      队伍列表.选中=nil
      if not 窗口层.多开系统.是否可见 and a~=1 then
          请求服务(63,{参数=角色信息.数字id,文本="获取角色信息"})
      end
  end
end
function 队员网格:置数据()
  for i = 1, #self.子控件 do
      local lssj = __队伍格子:创建()
      lssj:置数据(_tp.队伍数据[i], i, "队伍列表")
      self.子控件[i]:置精灵(lssj)
  end
end
function 队员网格:获得鼠标(x, y, a)
    for i, v in ipairs(self.子控件) do
          v._spr.焦点=nil
    end
    self.子控件[a]._spr.焦点=true
end
function 队员网格:失去鼠标(x, y)
    for i, v in ipairs(self.子控件) do
          v._spr.焦点=nil
    end
end

function 队员网格:右键弹起(x, y)
      队伍列表:置可见(false)
end

local 阵法显示 = 队伍列表:创建控件("阵法显示",95, 90,520, 50)
function 阵法显示:初始化()
        self.阵法图标={}
        for i=1,5 do
            self.阵法图标[i] = __res:取资源动画("pic", "guigzi.png","图片"):到精灵()  
        end
end
function 阵法显示:显示(x,y)
    for i=1,5 do
        self.阵法图标[i]:显示(x+(i-1)*121,y)  
    end
end


function 阵法显示:获得鼠标(x, y)
    for i=1,5 do
        if self.阵法图标[i]:检查透明(x, y)  then  
            __UI弹出.自定义:打开(x+20, y+20,"#Y"..队伍加成[_tp.队伍数据.阵型][i])
        end     
    end
end
  

function 阵法显示:左键弹起(x, y)
    if __手机 then
        for i=1,5 do
            if self.阵法图标[i]:检查透明(x, y)  then  
                __UI弹出.自定义:打开(x+20, y+20,"#Y"..队伍加成[_tp.队伍数据.阵型][i])
            end     
        end
    end 
end
  

local 关闭 = 队伍列表:创建关闭按钮("关闭")
function 关闭:左键弹起(x,y)
    窗口层.队伍列表:置可见(false)
end