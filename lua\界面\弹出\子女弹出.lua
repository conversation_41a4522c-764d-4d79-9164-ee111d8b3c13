__UI弹出["子女弹出"] = __UI界面["创建弹出窗口"](__UI界面, "子女弹出", 375 + abbr.py.x, 196 + abbr.py.y, 439, 331)
local 子女弹出 = __UI弹出["子女弹出"]
function 子女弹出:初始化()
  local nsf = require("SDL.图像")(439, 331)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 439, 331, true)["显示"](取黑透明背景(0, 0, 439, 331, true), 0, 0)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 子女弹出:打开(data)
  self:置可见(true)
  self:重置(data)
  self:切换控件(self[data["项目"] .. "控件"])
end
function 子女弹出:切换控件(控件)
  self.工作控件["置可见"](self.工作控件, 控件 == self.工作控件, not self.工作控件["是否实例"])
  self.学习控件["置可见"](self.学习控件, 控件 == self.学习控件, not self.学习控件["是否实例"])
  self.生活控件["置可见"](self.生活控件, 控件 == self.生活控件, not self.生活控件["是否实例"])
end
function 子女弹出:重置(data)
  local nsf = require("SDL.图像")(439, 40)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, data["项目"])["显示"](字体18["取图像"](字体18, data["项目"]), 200, 15)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.数据 = data
end
local 工作控件 = 子女弹出["创建控件"](子女弹出, "工作控件", 0, 0, 439, 331)
for i, v in ipairs({
  {
    name = "房屋打扫",
    x = 21,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "房屋打扫"
  },
  {
    name = "花果山采药",
    x = 162,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "花果山采药"
  },
  {
    name = "酒店帮工",
    x = 302,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "酒店帮工"
  },
  {
    name = "铁匠铺帮工",
    x = 21,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "铁匠铺帮工"
  },
  {
    name = "书香斋帮工",
    x = 162,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "书香斋帮工"
  },
  {
    name = "渔场捕鱼",
    x = 302,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "渔场捕鱼"
  },
  {
    name = "地府捉鬼",
    x = 21,
    y = 162,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "地府捉鬼"
  },
  {
    name = "猪场养猪",
    x = 162,
    y = 162,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "猪场养猪"
  }
}) do
  local 临时函数 = 工作控件["创建我的按钮"](工作控件, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    发送数据(6701, {
      ["选中"] = 子女弹出["数据"]["选中"],
      ["项目"] = 子女弹出["数据"]["项目"],
      ["类别"] = v.name
    })
    子女弹出["置可见"](子女弹出, false)
  end
end
local 学习控件 = 子女弹出["创建控件"](子女弹出, "学习控件", 0, 0, 439, 331)
for i, v in ipairs({
  {
    name = "体术",
    x = 21,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "体术"
  },
  {
    name = "建筑",
    x = 162,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "建筑"
  },
  {
    name = "诗文",
    x = 302,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "诗文"
  },
  {
    name = "算术",
    x = 21,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "算术"
  },
  {
    name = "武术",
    x = 162,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "武术"
  },
  {
    name = "骑术",
    x = 302,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "骑术"
  },
  {
    name = "书法",
    x = 21,
    y = 162,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "书法"
  },
  {
    name = "弈棋",
    x = 162,
    y = 162,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "弈棋"
  },
  {
    name = "轻功",
    x = 302,
    y = 162,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "轻功"
  },
  {
    name = "音律",
    x = 21,
    y = 219,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "音律"
  },
  {
    name = "奇门",
    x = 162,
    y = 219,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "奇门"
  },
  {
    name = "炼丹",
    x = 302,
    y = 219,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "炼丹"
  },
  {
    name = "舞蹈",
    x = 21,
    y = 275,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "舞蹈"
  },
  {
    name = "绘画",
    x = 162,
    y = 275,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "绘画"
  },
  {
    name = "弓箭",
    x = 302,
    y = 275,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "弓箭"
  }
}) do
  local 临时函数 = 学习控件["创建我的按钮"](学习控件, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    发送数据(6701, {
      ["选中"] = 子女弹出["数据"]["选中"],
      ["项目"] = 子女弹出["数据"]["项目"],
      ["类别"] = v.name
    })
    子女弹出["置可见"](子女弹出, false)
  end
end
local 生活控件 = 子女弹出["创建控件"](子女弹出, "生活控件", 0, 0, 439, 331)
for i, v in ipairs({
  {
    name = "睡觉休息",
    x = 21,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "睡觉休息"
  },
  {
    name = "东海湾度假",
    x = 162,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "东海湾度假"
  },
  {
    name = "江南郊游",
    x = 302,
    y = 52,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "江南郊游"
  },
  {
    name = "长安赏烟花",
    x = 21,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "长安赏烟花"
  },
  {
    name = "酒店大吃大喝",
    x = 162,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "酒店大吃大喝"
  },
  {
    name = "宝象国皮影戏",
    x = 302,
    y = 108,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "宝象国皮影戏"
  },
  {
    name = "地府玩闹",
    x = 21,
    y = 162,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "地府玩闹"
  },
  {
    name = "游乐园玩耍",
    x = 162,
    y = 162,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 122, 42),
    font = "游乐园玩耍"
  }
}) do
  local 临时函数 = 生活控件["创建我的按钮"](生活控件, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    发送数据(6701, {
      ["选中"] = 子女弹出["数据"]["选中"],
      ["项目"] = 子女弹出["数据"]["项目"],
      ["类别"] = v.name
    })
    子女弹出["置可见"](子女弹出, false)
  end
end
