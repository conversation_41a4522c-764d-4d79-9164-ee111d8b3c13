--[[
LastEditTime: 2024-10-22 18:20:37
--]]
--[[
LastEditTime: 2024-10-22 17:34:49
--]]


local 物品解锁 = 窗口层:创建窗口("物品解锁", 0, 0, 280, 275)
function 物品解锁:初始化()
  self:创建纹理精灵(function()
    置窗口背景("解除密码", 0, 0, 280, 275, true):显示(0, 0)
    取输入背景(0, 0,225,22):显示(26,80)

    文本字体:置颜色( 255, 255, 255,255)
    文本字体:取图像("请用右边的软键盘入您物品锁密码的\n前3位,剩余的位数请用键盘输入。"):显示(26, 40)
    文本字体:取图像("请输入密码以解除物品及钱庄的加锁\n限制。如果你忘了密码，可以点击“"):显示(26, 115)
    文本字体:取图像("”按钮，但需要等待15天，15"):显示(70, 144)
    文本字体:取图像("天后自动解除密码。15天内记起密码\n可以通过再次输入立刻解除密码。申\n请强行解除密码需要消耗体力100点(\n0至9级的游戏角色仅需消耗体力10点\n)。修改密码需要消耗体力20点。"):显示(26, 158)



    文本字体:置颜色(__取颜色("黄色")):取图像("强行解除"):显示(26,144)



end
)
 
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
  else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
  end

  
end


function 物品解锁:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.密码输入:置文本("")


end


local 密码输入 = 物品解锁:创建文本输入( "密码输入", 30, 83,215, 16)
function 密码输入:初始化()
    self:取光标精灵()
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(0,0,0,255)
end






local 确认=物品解锁:创建红色按钮("确认","确认",7,240,50,22)  
function 确认:左键弹起(x, y)
          if not 物品解锁.密码输入:取文本() or 物品解锁.密码输入:取文本()=="" then
                __UI弹出.提示框:打开("#Y你还没有填写密码呢！")
          elseif string.len(物品解锁.密码输入:取文本())>12 then
                 __UI弹出.提示框:打开("#Y密码过长，请重新填写")
          else
                请求服务(3753,{密码=物品解锁.密码输入:取文本()})
                物品解锁:置可见(false)
          end
end

local 修改密码=物品解锁:创建红色按钮("修改密码","修改密码", 62, 240,74, 22)  
function 修改密码:左键弹起(x, y)
     窗口层.解锁修改:打开()
end

local 强行解锁=物品解锁:创建红色按钮("强行解锁","强行解锁", 142, 240,74, 22)  
function 强行解锁:左键弹起(x, y)
      窗口层.文本栏:打开("真的要强行删除密码吗？需要等待15天后系统自动为你解除密码哦！",3755,{密码=12})
end




local 取消=物品解锁:创建红色按钮("取消","取消", 222, 240,50, 22)  
function 取消:左键弹起(x, y)
  物品解锁:置可见(false)
end





local 关闭 = 物品解锁:创建关闭按钮("关闭")

function 关闭:左键弹起(x, y)
  物品解锁:置可见(false)
end

