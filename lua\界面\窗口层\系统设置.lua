local 系统设置 = 窗口层:创建窗口("系统设置", 0, 0, 460, 430)
function 系统设置:初始化()
      self:创建纹理精灵(function()
          置窗口背景("系统设置",0,0,460,430,true):显示(0,0)
          白色文字栏背景(315,320,true):显示(130,35)
          __res:取资源动画("jszy/fwtb",0xabcd0204,"图像"):平铺(16,397):显示(100,25)
      end) 
      self:置坐标(引擎.宽度2-230, 引擎.高度2-215)
      self.可初始化=true
      if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-27, 2)
      else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-18, 2)
      end
end


function 系统设置:打开()
    self:置可见(true)
    self.设置按钮:置选中(true)
    self.显示控件.状态 ="设置"
    self.显示控件:显示刷新()
    if 窗口层.对话栏.是否可见 then
        窗口层.对话栏:置可见(false)
    end
   
end

local 显示控件 = 系统设置:创建控件("显示控件", 160,60, 280,260)
function 显示控件:初始化()
  self.状态 = "设置"
end
local  左边选项 ={"屏蔽玩家(F9)","开启动态特效","屏蔽变身造型","屏蔽传闻消息","给予接受开关","自动遇怪"}

-- 如果是移动端，添加摇杆控制选项
if __手机 then
    table.insert(左边选项, "显示虚拟摇杆(F10)")
end
local  右边选项 ={"屏蔽摊位(F11)","战斗显示蓝条","屏蔽活动消息","屏蔽锦衣效果","屏蔽光环足迹","坐骑显示"}


function 显示控件:显示刷新()

  for i, v in ipairs(左边选项) do
    self[v]:置可见(false)
    self[v]:置选中(false)
    if v=="屏蔽玩家(F9)" and __res.配置.显示玩家==1 then
        self[v]:置选中(true)
    elseif v=="开启动态特效" and __res.配置.地图特效==1 then
        self[v]:置选中(true)
    elseif v=="屏蔽变身造型" and __res.配置.变身造型==1 then
        self[v]:置选中(true)
    elseif v=="屏蔽传闻消息" and __res.配置.传闻消息==1 then
        self[v]:置选中(true)
    elseif v=="给予接受开关" and 角色信息 and 角色信息.接受给予 then
        self[v]:置选中(true)
    elseif v=="自动遇怪" and 角色信息 and 角色信息.自动遇怪 then
         self[v]:置选中(true)
    elseif v=="显示虚拟摇杆(F10)" and __res.配置.显示摇杆==1 then
         self[v]:置选中(true)
    end
  end
  for i, v in ipairs(右边选项) do
      self[v]:置可见(false)
      self[v]:置选中(false)
      if v=="屏蔽摊位(F11)" and __res.配置.屏蔽摊位==1 then
          self[v]:置选中(true)
      elseif v=="战斗显示蓝条" and __res.配置.显示蓝条==1 then
          self[v]:置选中(true)
      elseif v=="屏蔽活动消息" and __res.配置.活动消息==1 then
          self[v]:置选中(true)
      elseif v=="屏蔽锦衣效果" and __res.配置.锦衣效果==1 then
          self[v]:置选中(true)
      elseif v=="屏蔽光环足迹" and  __res.配置.光环足迹==1 then
          self[v]:置选中(true)
      elseif v=="坐骑显示" and  __res.配置.坐骑显示==1 then
          self[v]:置选中(true)
      end
  end
  for i, v in ipairs(__分辨率设置) do
     self["分辨率"..i]:置可见(false)
  end

  self.音乐按钮:置可见(false)
  self.音效按钮:置可见(false)
  self.音乐滑块:置可见(false)
  self.音效滑块:置可见(false)
  if __res.配置.音乐 and __res.配置.音乐>0 then
      self.音乐按钮:置选中(true)
      self.音乐滑块:置位置(__res.配置.音乐)
  else
      self.音乐按钮:置选中(false)
      self.音乐滑块:置位置(0)
  end
  if __res.配置.音效 and __res.配置.音效>0 then
      self.音效按钮:置选中(true)
      self.音效滑块:置位置(__res.配置.音效)
  else
      self.音效按钮:置选中(false)
      self.音效滑块:置位置(0)
  end
 
 
  if not __res.配置.分辨率 then
    __res.配置.分辨率=1
    __res:写出文件( "config.txt", zdtostring(__res.配置))
  end

  文本字体:置颜色(0,0,0,255)
  self:创建纹理精灵(function()
      if self.状态=="设置" then
        
        文本字体:取图像("聊天框背景"):显示(70,0)
        for i, v in ipairs(左边选项) do
           文本字体:取图像(v):显示(0,70+(i-1)*30)
           self[v]:置可见(true)
        end
        for i, v in ipairs(右边选项) do
            文本字体:取图像(v):显示(130,70+(i-1)*30)
            self[v]:置可见(true)
       end
      elseif self.状态=="音频" then
            文本字体:取图像("音频"):显示(0,30)
            文本字体:取图像("游戏音乐"):显示(30,60)
            文本字体:取图像("游戏音效"):显示(30,90)
            self.音乐按钮:置可见(true)
            self.音效按钮:置可见(true)
            self.音乐滑块:置可见(true)
            self.音效滑块:置可见(true)
      else
          if __手机  then
               文本字体:取图像("当前分辨率: "..引擎.宽度.." * "..引擎.高度):显示(0,30)
          else
                  文本字体:取图像("设置分辨率 "):显示(0,0)
                  local x =0
                  local y = 0
                  for i, v in ipairs(__分辨率设置) do
                      文本字体:取图像(v[1].."*"..v[2]):显示(25+x*95,30+y*30)
                      self["分辨率"..i]:置可见(true)
                     
                      if __res.配置.分辨率==i then
                          self["分辨率"..i]:置选中(true)
                          if 引擎.宽度~=v[1] and 引擎.高度~=v[2] then
                            引擎:置宽高(v[1],v[2])
                          end
                      else
                         self["分辨率"..i]:置选中(false)
                      end
                      
                    

                      x =x +1 
                      if  x>=3 then
                        x =0
                        y = y+1
                      end
                  end

          end 
      end
    end) 
 end

 


 for i, v in ipairs(左边选项) do
  local 临时选项=显示控件:创建多选按钮(v, 90, 70+(i-1)*30)
  function 临时选项:初始化()
      self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000009))
  end
  function 临时选项:左键弹起(x, y)
         if v=="屏蔽玩家(F9)" then
              if  __res.配置.显示玩家==1 then
                  __res.配置.显示玩家=0
              else
                  __res.配置.显示玩家=1
              end

        elseif v=="开启动态特效" then
              if  __res.配置.地图特效==1 then
                  __res.配置.地图特效=0
                  __主显:清除场景特效()
              else
                  __res.配置.地图特效=1
                  __主显:加载场景特效()
              end
        elseif v=="屏蔽变身造型" then
              if  __res.配置.变身造型==1 then
                  __res.配置.变身造型=0
              else
                  __res.配置.变身造型=1
              end
              if __主显 and __主显.主角 then
                __主显.主角:置模型()
              end
        elseif v=="屏蔽传闻消息" then
              if  __res.配置.传闻消息==1 then
                  __res.配置.传闻消息=0
              else
                  __res.配置.传闻消息=1
              end
        elseif v=="给予接受开关" then
                请求服务(118)
        elseif v=="自动遇怪" then
                请求服务(15)
        elseif v=="显示虚拟摇杆(F10)" then
                if __res.配置.显示摇杆==1 then
                    __res.配置.显示摇杆=0
                else
                    __res.配置.显示摇杆=1
                end
                -- 更新摇杆显示状态
                if __手机 and 界面层.玩家界面 and 界面层.玩家界面.移动摇杆 then
                    界面层.玩家界面.移动摇杆:设置可见(__res.配置.显示摇杆==1)
                end
             
        end
        __res:写出文件( "config.txt", zdtostring(__res.配置))
  end

end

for i, v in ipairs(右边选项) do
  local 临时选项1=显示控件:创建多选按钮(v, 220, 70+(i-1)*30)
  function 临时选项1:初始化()
      self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000009))
  end
  function 临时选项1:左键弹起(x, y)
      
    if v=="屏蔽摊位(F11)" then
          if  __res.配置.屏蔽摊位==1 then
              __res.配置.屏蔽摊位=0
          else
              __res.配置.屏蔽摊位=1
          end
    elseif v=="战斗显示蓝条" then
          if  __res.配置.显示蓝条==1 then
              __res.配置.显示蓝条=0
          else
              __res.配置.显示蓝条=1
          end
    elseif v=="屏蔽活动消息" then
            if  __res.配置.活动消息==1 then
                __res.配置.活动消息=0
            else
                __res.配置.活动消息=1
            end
    elseif v=="屏蔽锦衣效果" then
            if  __res.配置.锦衣效果==1 then
                __res.配置.锦衣效果=0
            else
                __res.配置.锦衣效果=1
            end
            if __主显 and __主显.主角 then
                __主显.主角:置模型()
            end
    elseif v=="屏蔽光环足迹" then
            if  __res.配置.光环足迹==1 then
                __res.配置.光环足迹=0
            else
                __res.配置.光环足迹=1
            end
            if __主显 and __主显.主角 then
                __主显.主角:置模型()
            end
    elseif v=="坐骑显示" then
            if  __res.配置.坐骑显示==1 then
                __res.配置.坐骑显示=0
            else
                __res.配置.坐骑显示=1
            end
            if __主显 and __主显.主角 then
                __主显.主角:置模型()
            end
    end
    __res:写出文件( "config.txt", zdtostring(__res.配置))
    
  end
end


local xx =0
local yy = 0
for i, v in ipairs(__分辨率设置) do
  local 临时选项2=显示控件:创建单选按钮("分辨率"..i,xx*95,27+yy*30)
  function 临时选项2:初始化()
    self:创建圆形选中精灵()
end
  function 临时选项2:左键弹起(x, y)
        if not _tp.战斗中 then
            __res.配置.分辨率=i
            __res:写出文件( "config.txt", zdtostring(__res.配置))
            引擎:置宽高(v[1],v[2])
        end
  end
  xx =xx +1 
    if  xx>=3 then
      xx =0
      yy = yy+1
    end
end


local 音乐按钮=显示控件:创建多选按钮("音乐按钮",0,56)

function 音乐按钮:初始化()
    self:创建圆形选中精灵()
end

function 音乐按钮:左键弹起(x, y)
        if __res.配置.音乐 and __res.配置.音乐>0 then
              __res.配置.音乐=0
              显示控件.音乐滑块:置位置(0)
              _tp:停止音乐()
              _tp:停止战斗音乐()
        else
              __res.配置.音乐=50
              显示控件.音乐滑块:置位置(50)
              if not _tp.战斗中 then
                  _tp:恢复音乐()
              else
                  _tp:恢复战斗音乐()
              end
        end
        __res:写出文件( "config.txt", zdtostring(__res.配置))
end



local 音效按钮=显示控件:创建多选按钮("音效按钮",0,86)
  function 音效按钮:初始化()
      self:创建圆形选中精灵()
  end


function 音效按钮:左键弹起(x, y)
        if __res.配置.音效 and __res.配置.音效>0 then
            __res.配置.音效=0
            显示控件.音效滑块:置位置(0)
        else
            __res.配置.音效=50
            显示控件.音效滑块:置位置(50)
        end
        __res:写出文件( "config.txt", zdtostring(__res.配置))

end



local 音乐滑块=显示控件:创建横向滑块("音乐滑块",90,58,170,19)
function 音乐滑块:滚动事件(x, y,a)
        __res.配置.音乐=a
    
      if a<=0 then
          显示控件.音乐按钮:置选中(false)
          _tp:停止音乐()
          _tp:停止战斗音乐()
      else
          显示控件.音乐按钮:置选中(true)
         
          _tp:置音乐音量(a)
      end
      __res:写出文件( "config.txt", zdtostring(__res.配置))
end

local 音效滑块=显示控件:创建横向滑块("音效滑块",90,88,170,19)
function 音效滑块:滚动事件(x, y,a)
        __res.配置.音效=a
        if a<=0 then
            显示控件.音效按钮:置选中(false)
        else
            显示控件.音效按钮:置选中(true)
        end
        __res:写出文件( "config.txt", zdtostring(__res.配置))
end




local 设置按钮=系统设置:创建红色单选按钮("系统设置", "设置按钮", 10, 40,74,20) 
function 设置按钮:左键弹起(x, y)
        显示控件.状态 ="设置"
        显示控件:显示刷新()
end
local 音频按钮=系统设置:创建红色单选按钮("音频设置", "音频按钮", 10, 80,74,20) 
function 音频按钮:左键弹起(x, y)
        显示控件.状态 ="音频"
        显示控件:显示刷新()

end
local 画面按钮=系统设置:创建红色单选按钮("画面偏好", "画面按钮", 10, 120,74,20) 
function 画面按钮:左键弹起(x, y)
    显示控件.状态 ="画面"
    显示控件:显示刷新()

end

local 充值中心=系统设置:创建红色按钮("充值中心", "充值中心", 10, 160,74,20) 
function 充值中心:左键弹起(x, y)
  if not  _tp.战斗中 then
      窗口层.充值窗口:打开()
  end 
end



local 退出游戏=系统设置:创建红色按钮("退出游戏", "退出游戏", 235, 370,74,20) 
function 退出游戏:左键弹起(x, y)
  引擎:关闭()
end

local 关闭 = 系统设置:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
   系统设置:置可见(false)
end