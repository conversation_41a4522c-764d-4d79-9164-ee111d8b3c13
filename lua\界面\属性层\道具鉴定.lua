local 道具鉴定 = 窗口层:创建窗口("道具鉴定", 0, 0, 290, 280)  
function 道具鉴定:初始化()
  self:置精灵(置窗口背景("鉴定", 0, 0, 290, 280))
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end

end


function 道具鉴定:打开(数据)
      self:置可见(not self.是否可见)
      if not self.是否可见 then
          return
      end
      self.编号= 0
      self.数据 = 数据
      self.道具列表={}
      if 数据.编号 then
          self.编号=tonumber(数据.编号)
      end
      self.道具网格:置数据()
      self:道具刷新(数据.道具.道具)
     
end

function 道具鉴定:道具刷新(道具)
        local 道具数据 = table.copy(_tp.道具列表)
        if 道具 then
            道具数据 =table.copy(道具)
        end
       if self.编号~=0 and self.道具列表[self.编号] then
            if not 道具数据[self.编号] or self.道具列表[self.编号].识别码~=道具数据[self.编号].识别码 then
                  local 找到=0
                  for k, v in pairs(道具数据) do
                      if self.道具列表[self.编号].识别码==v.识别码 then
                          找到=k
                      end
                  end
                  self.编号=找到
            end
       end
      self.道具列表 =道具数据
      self.道具网格:置物品(self.道具列表)
      self.道具网格:置禁止({2})
end

local 道具网格=道具鉴定:创建背包网格("道具网格",15,35)
function 道具网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
              __UI弹出.道具提示:打开(物品.物品,x+25,y+25)
          end
end



function 道具网格:左键弹起(x, y, a)
      local 物品=self:选中物品() 
      if 物品 and 物品.物品 then
            if __手机 then
                  __UI弹出.道具提示:打开(物品.物品,x+25,y+25,道具网格,"选择",1)
            else
                  self:选择(1)
            end
      end

end

function 道具网格:选择(编号)
      if 编号 and 编号~=0 then
            道具鉴定:物品鉴定(self:选中物品(),self:选中())
      end
end



function 道具鉴定:物品鉴定(物品,编号)
      
      if not 物品.物品禁止  then
         local 物品数据= self.道具列表[self.编号]
        if not 物品.物品.鉴定  then
              if 物品数据 and  物品数据.名称 == "神兵图鉴" and (物品.物品.分类 == 3 or 物品.物品.分类 == 4) then
                  if 物品.物品.级别限制>物品数据.子类 then
                      __UI弹出.提示框:打开("#Y神兵图鉴的等级过低无法鉴定该装备")
                    return
                  else
                        请求服务(4510,{序列=物品.物品,序列1=self.编号,序列2=编号})
                  end
              elseif 物品数据 and 物品数据.名称 == "灵宝图鉴" and (物品.物品.分类 == 1 or 物品.物品.分类 == 2 or 物品.物品.分类 == 5 or 物品.物品.分类 == 6) then
                    if 物品.物品.级别限制>物品数据.子类 then
                          __UI弹出.提示框:打开("#Y灵宝图鉴的等级过低无法鉴定该装备")
                          return
                    else
                          请求服务(4510,{序列=物品.物品,序列1=self.编号,序列2=编号})
                    end
              elseif 物品数据 and 物品数据.名称 == "灵饰图鉴" and 物品.物品.灵饰 then
                    if 物品.物品.级别限制>物品数据.子类 then
                          __UI弹出.提示框:打开("#Y灵宝图鉴的等级过低无法鉴定该装备")
                          return
                    else
                          请求服务(4510,{序列=物品.物品,序列1=self.编号,序列2=编号})
                    end
              elseif self.数据 and self.数据.名称 and self.数据.名称 == "兵器谱" and (物品.物品.分类 == 3 or 物品.物品.分类 == 4) then
                    if 物品.物品.级别限制>self.数据.等级 then
                        __UI弹出.提示框:打开("#Y神兵图鉴的等级过低无法鉴定该装备")
                      return
                    else
                        请求服务(4510,{序列=物品.物品,数据=self.数据,序列2=编号})
                    end
              elseif self.数据 and self.数据.名称 and self.数据.名称 == "堪察令" and (物品.物品.分类 == 1 or 物品.物品.分类 == 2 or 物品.物品.分类 == 5 or 物品.物品.分类 == 6) then
                    if 物品.物品.级别限制>self.数据.等级 then
                        __UI弹出.提示框:打开("#Y灵宝图鉴的等级过低无法鉴定该装备")
                      return
                    else
                        请求服务(4510,{序列=物品.物品,数据=self.数据,序列2=编号})
                    end
              else
                    __UI弹出.提示框:打开("#Y该道具无法鉴定")
                    return
              end
        elseif 物品数据 and 物品数据.名称 == "仙灵丹" and not 物品.物品.灵饰 and 物品.物品.分类<7  then
                  if not 物品.物品.赐福 then
                      窗口层.文本栏:打开("未被赐福的装备只有5%几率添加是否添加赐福",4518,{序列=物品.物品,序列1=self.编号,序列2=编号})
                  else
                        请求服务(4518,{序列=物品.物品,序列1=self.编号,序列2=编号})
                  end
        elseif 物品数据 and 物品数据.名称 == "特效点化石" and not 物品.物品.灵饰 and 物品.物品.分类<7  then
                  if  物品.物品.特效 then
                          窗口层.文本栏:打开("你确定改变该装备的特效么",4519,{序列=物品.物品,序列1=self.编号,序列2=编号})
                    else
                          请求服务(4519,{序列=物品.物品,序列1=self.编号,序列2=编号})
                    end
        elseif 物品数据 and 物品数据.名称 == "特技点化石" and not 物品.物品.灵饰 and 物品.物品.分类<7  then
                if  物品.物品.特技 then
                        窗口层.文本栏:打开("你确定改变该装备的特技么",4520,{序列=物品.物品,序列1=self.编号,序列2=编号})
                else
                      请求服务(4520,{序列=物品.物品,序列1=self.编号,序列2=编号})
                end

        elseif 物品数据 and 物品数据.名称 == "无级别点化石" and not 物品.物品.灵饰 and 物品.物品.分类<7  then
              请求服务(4521,{序列=物品.物品,序列1=self.编号,序列2=编号})

        elseif 物品数据 and 物品数据.名称 == "灵饰点化石" and  物品.物品.灵饰 then
                请求服务(4522,{序列=物品.物品,序列1=self.编号,序列2=编号})
        elseif 物品数据 and 物品数据.名称 == "灵饰洗炼石" and  物品.物品.灵饰 then
                请求服务(4523,{序列=物品.物品,序列1=self.编号,序列2=编号})
        elseif 物品数据 and (物品数据.名称 == "鸿蒙灵宝" or 物品数据.名称 == "鸿蒙灵宝" 
                or 物品数据.名称 == "鸿蒙仙宝" or 物品数据.名称 == "鸿蒙神宝" 
                or 物品数据.名称 == "太初灵石" or 物品数据.名称 == "太初仙石" 
                or 物品数据.名称 == "太初神石" or 物品数据.名称 == "鸿蒙原石" 
                or  物品数据.名称 == "太初原石") and not 物品.物品.灵饰 and 物品.物品.分类<7  then
                请求服务(4524,{序列=物品.物品,序列1=self.编号,序列2=编号})
     
        else
            __UI弹出.提示框:打开("#Y该道具无法鉴定")
            return
        end
        self:置可见(false)
    else
      __UI弹出.提示框:打开("#Y该道具无法鉴定")
    end

end


local 关闭 = 道具鉴定:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  道具鉴定:置可见(false)
end


