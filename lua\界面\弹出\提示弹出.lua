__UI弹出["提示弹出"] = __UI界面["创建弹出窗口"](__UI界面, "提示弹出", 284 + abbr.py2.x, 171 + abbr.py2.y, 392, 200)
local 提示弹出 = __UI弹出["提示弹出"]
function 提示弹出:初始化()
  self:置精灵(置窗口背景(nil, 0, 0, 392, 200))
end
function 提示弹出:打开(txt, xh, nt)
  self:置可见(true)
  self.提示文本["清空"](self.提示文本)
  self.提示文本["置文本"](self.提示文本, txt)
  self.序号 = xh
  self.内容 = nt
end
local 提示文本 = 提示弹出["创建文本"](提示弹出, "提示文本", 18, 52, 358, 75)
function 提示文本:初始化()
end
for i, v in ipairs({
  {
    name = "确定",
    x = 30,
    y = 140,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    font = "确定"
  },
  {
    name = "取消",
    x = 239,
    y = 140,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    font = "取消"
  }
}) do
  local 临时函数 = 提示弹出["创建我的按钮"](提示弹出, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "确定" then
      发送数据(提示弹出["序号"], 提示弹出["内容"])
      提示弹出["置可见"](提示弹出, false)
    elseif v.name == "取消" then
      提示弹出["置可见"](提示弹出, false)
    end
  end
end
