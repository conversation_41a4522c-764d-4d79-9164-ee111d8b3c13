local SDL = require("SDL")

local 取可用道具 = function(名称)
  if skill战斗道具[名称] then
		return skill战斗道具[名称]
	end
	return 0
end

local 可用门派 = function(名称, 门派)
  -- if "罗汉珠" == 名称 and "化生寺" ~= 门派 then
  --   return false
  -- elseif "分水" == 名称 and "龙宫" ~= 门派 then
  --   return false
  -- elseif "赤焰" == 名称 and "魔王寨" ~= 门派 then
  --   return false
  -- elseif "天煞" == 名称 and "凌波城" ~= 门派 then
  --   return false
  -- elseif "神木宝鼎" == 名称 and "神木林" ~= 门派 then
  --   return false
  -- elseif "干将莫邪" == 名称 and "大唐官府" ~= 门派 then
  --   return false
  -- elseif "金蟾" == 名称 and "无底洞" ~= 门派 then
  --   return false
  -- end
  return true
end
local 战斗道具 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "战斗道具", 547 + abbr.py.x, 110 + abbr.py.y, 365, 404)
function 战斗道具:初始化()
  local nsf = require("SDL.图像")(354, 406)
  if nsf["渲染开始"](nsf, 0, 0, 0, 0) then
    __res:getPNGCC(2, 0, 63, 354, 396)["显示"](__res:getPNGCC(2, 0, 63, 354, 396), 0, 10)
    local 宽度 = 字体20["取宽度"](字体20, "道具")
    字体20["置颜色"](字体20, 255, 255, 255)
    字体20["取图像"](字体20, "道具")["显示"](字体20["取图像"](字体20, "道具"), 177.0 - 宽度 / 2, 16)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 战斗道具:打开(data, lx,伙伴id)
  self:置可见(true)
  __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], false)
  __UI界面["界面层"]["助战操作界面"]["置可见"](__UI界面["界面层"]["助战操作界面"], false)
  self.伙伴id=伙伴id
  for i = 1, #self.道具网格["子控件"] do
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data[i], "白格子", "战斗道具")
    self.道具网格["子控件"][i]["置精灵"](self.道具网格["子控件"][i], lssj)
    if "道具" == lx then
      if data[i] then
        if 0 == 取可用道具(data[i]["名称"]) then
          self.道具网格["子控件"][i]._spr["物品禁止"] = true
        else
          self.道具网格["子控件"][i]._spr["命令附加"] = 取可用道具(data[i]["名称"])
        end
      end
    elseif "法宝" == lx and data[i] then
      if 0 == 取可用道具(data[i]["名称"]) or not 可用门派(data[i]["名称"], __UI界面["界面层"]["战斗界面"]["参战单位"][__UI界面["界面层"]["战斗界面"]["单位编号"][__UI界面["界面层"]["战斗界面"]["操作对象"]]]["门派"]) then
        self.道具网格["子控件"][i]._spr["物品禁止"] = true
      else
        self.道具网格["子控件"][i]._spr["命令附加"] = 取可用道具(data[i]["名称"])
      end
    end
  end
  self.选中 = nil
end
local 关闭 = 战斗道具["创建我的按钮"](战斗道具, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 318, 0)
function 关闭:左键弹起(x, y, msg)
  __UI界面["窗口层"]["战斗道具"]["置可见"](__UI界面["窗口层"]["战斗道具"], false)
  __UI界面["界面层"]["战斗界面"]["重置"](__UI界面["界面层"]["战斗界面"])
  __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], true)
end
local 道具网格 = 战斗道具["创建网格"](战斗道具, "道具网格", 20, 72, 318, 252)
function 道具网格:初始化()
  self:创建格子(55, 55, 10, 10, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if 战斗道具["选中"] then
    self.子控件[战斗道具["选中"]]._spr["确定"] = nil
  end
  if self.子控件[a]._spr["物品"] and not self.子控件[a]._spr["物品禁止"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 190, 86, w, h)
    self.子控件[a]._spr["确定"] = true
    战斗道具["选中"] = a
  end
end
local 使用按钮 = 战斗道具["创建我的按钮"](战斗道具, __res:getPNGCC(2, 493, 765, 118, 35, true), "使用按钮", 210, 330, "使用")
function 使用按钮:左键弹起(x, y, msg)
  if 战斗道具["选中"] and 0 ~= 战斗道具["道具网格"]["子控件"][战斗道具["选中"]]._spr["命令附加"] then
   -- table.print(战斗道具["道具网格"]["子控件"][战斗道具["选中"]]._spr)
    if 战斗道具.伙伴id then
      __UI界面["界面层"]["助战操作界面"]:设置道具参数(战斗道具["选中"], 战斗道具["道具网格"]["子控件"][战斗道具["选中"]]._spr["命令附加"],战斗道具.伙伴id, 战斗道具["道具网格"]["子控件"][战斗道具["选中"]]._spr["物品"].名称)--,战斗道具.物品[n].物品.名称)
    else
      __UI界面["界面层"]["战斗界面"]:设置道具参数(战斗道具["选中"], 战斗道具["道具网格"]["子控件"][战斗道具["选中"]]._spr["命令附加"])
    end
  end
end
