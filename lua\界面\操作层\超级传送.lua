--[[
LastEditTime: 2024-05-05 23:05:07
--]]
local 超级传送 = 窗口层:创建窗口("超级传送", 0, 0, 770, 490)
function 超级传送:初始化()
  self:置精灵(__res:取资源动画("dlzy", 0xAABBCC8E,"精灵"))
  self.聊天 = __res:取资源动画("dlzy", 0x98AAC807,"精灵") 
  self.动态1 = __res:取资源动画("dlzy", 0x103FA0FF,"动画")
  self.对话1 ="哥哥的腿不是腿，\n     塞纳河畔的清水~"
  self.动态2 = __res:取资源动画("dlzy", 0x8E28D6F3,"动画")
  self.对话2 ="别爱我，没结果，\n     除非花手摇过我~"
  self.动态3 = __res:取资源动画("dlzy", 0x76F0D6C6,"动画")
  self.对话3 ="你说我是败类，\n     我是败类中的败类！~"
  self.动态4 = __res:取资源动画("dlzy", 0x7F01F6E7,"动画")
  self.对话4 ="画画的baby~~，\n     画画的baby~~"
  self.动态5 = __res:取资源动画("dlzy", 0x71321C77,"动画")
  self.对话5 ="一出门我就感觉我，\n     仙气儿~飘飘~"
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
  
end

local 提示文本 = 超级传送:创建文本("提示文本", 210, 365, 180, 40)
function 提示文本:初始化()
  self:置文字(说明字体)
end

function 超级传送:更新(dt)
  if  os.time() - self.信息变换>=math.random(5,10) then
    self.信息变换 = os.time()
    self.信息 = math.random(1,5)
    self.提示文本:清空()
    self.提示文本:置文本(self["对话"..self.信息])
  end
  self["动态"..self.信息]:更新(dt)
end
function 超级传送:显示(x,y)
  self["动态"..self.信息]:显示(x+100,y+345)
  self.聊天:显示(x+190,y+355)
end



local 主城设置 = {"主城传送","NPC传送","野外地图","高级地图","门派传送"}
for i, v in ipairs(主城设置) do
  local 临时函数 = 超级传送:创建单选按钮(v, 100, 60+(i-1)*45) 
  function 临时函数:初始化()
    self:创建按钮精灵( __res:取资源动画("dlzy", 0xCFC32179),1,v,nil,nil,标题字体)
  end
  function 临时函数:左键按下(x, y)
      超级传送.分类=v
      超级传送:显示设置()
  end
end


local 分类配置={}
分类配置.主城传送={"建邺城","长安城","傲来国","长寿村","西梁女国","宝象国","朱紫国"}
local xx = 0
local yy = 0
for i, v in ipairs(分类配置.主城传送) do
  local 临时函数 = 超级传送:创建按钮(v, 280+xx*110, 75+yy*35) 
  function 临时函数:初始化()
    self:创建按钮精灵( __res:取资源动画("dlzy", 0x4BB58ED4),1,v)
  end
  xx=xx+1
  if xx>=4 then
    xx=0
    yy=yy+1
  end
  function 临时函数:左键按下(x, y)
        if v=="建邺城" then
          请求服务(48,{1501,92,96})
        elseif v=="长安城" then
          请求服务(48,{1001,207,111})
        elseif v=="傲来国" then
          请求服务(48,{1092,73,53})
        elseif v=="长寿村" then
          请求服务(48,{1070,91,151})
        elseif v=="西梁女国" then
          请求服务(48,{1040,108,98})
        elseif v=="宝象国" then
          请求服务(48,{1226,117,48})
        elseif v=="朱紫国" then
          请求服务(48,{1208,128,36})
        end
        超级传送:置可见(false)
  end
end

分类配置.NPC传送={
  "钱庄老板","钟 馗","袁天罡","冯铁匠","商会总管","杜少海","镖 局","帮派总管",
  "土地公公","闯关使者","仙缘洞天","月宫吴刚","镇塔之神","慧觉和尚","疥癞和尚","吴举人"
}
xx = 0
yy = 0
for i, v in ipairs(分类配置.NPC传送) do
  local 临时函数 = 超级传送:创建按钮(v, 280+xx*110, 75+yy*35) 
  function 临时函数:初始化()
    self:创建按钮精灵( __res:取资源动画("dlzy", 0x4BB58ED4),1,v)
  end
  xx=xx+1
  if xx>=4 then
    xx=0
    yy=yy+1
  end
  function 临时函数:左键按下(x, y)
      if v=="钱庄老板" then
        请求服务(48,{1524,36,23})
      elseif v=="钟 馗" then
        请求服务(48,{1122,57,63})
      elseif v=="袁天罡" then
        请求服务(48,{1001,204,120})
      elseif v=="冯铁匠" then
        请求服务(48,{1025,27,23})
      elseif v=="商会总管" then
        请求服务(48,{1001,323,264})
      elseif v=="杜少海" then
        请求服务(48,{1001,223,102})
      elseif v=="镖 局" then
        请求服务(48,{1024,31,24})
      elseif v=="帮派总管" then
        请求服务(48,{1001,380,14})
      elseif v=="土地公公" then
        请求服务(48,{1226,117,15})
      elseif v=="闯关使者" then
        请求服务(48,{1001,130,90})
      elseif v=="仙缘洞天" then
        请求服务(48,{1216,90,80})
      elseif v=="月宫吴刚" then
        请求服务(48,{1114,12,60})
      elseif v=="镇塔之神" then
        请求服务(48,{1009,30,27})
      elseif v=="慧觉和尚" then
        请求服务(48,{1070,130,144})
      elseif v=="疥癞和尚" then
        请求服务(48,{1002,39,56})
      elseif v=="吴举人" then
        请求服务(48,{1026,26,26})
      end
      超级传送:置可见(false)
  end
end
分类配置.野外地图={
  "东海湾","江南野外","大唐国境","大唐境外","墨家村","长寿郊外","碗子山","生死劫","观星台",
  "仙缘洞天1","柳林坡","北俱芦洲","丝绸之路","比丘国","须弥东界","海底5层","地狱5层","龙窟7层",
  "凤巢7层", "女娲神迹"
 }


xx = 0
yy = 0
for i, v in ipairs(分类配置.野外地图) do
  local 临时函数 = 超级传送:创建按钮(v, 280+xx*110, 75+yy*35) 
  function 临时函数:初始化()
    self:创建按钮精灵( __res:取资源动画("dlzy", 0x4BB58ED4),1,v)
  end
  xx=xx+1
  if xx>=4 then
    xx=0
    yy=yy+1
  end
  function 临时函数:左键按下(x, y)
    if v=="东海湾" then
      请求服务(48,{1506,76,94})
    elseif v=="江南野外" then
      请求服务(48,{1193,72,69})
    elseif v=="大唐国境" then
      请求服务(48,{1110,141,166})
    elseif v=="大唐境外" then
      请求服务(48,{1173,345,72})
    elseif v=="墨家村" then
      请求服务(48,{1218,45,12})
    elseif v=="长寿郊外" then
      请求服务(48,{1091,77,98})
    elseif v=="碗子山" then
      请求服务(48,{1228,59,19})
    elseif v=="生死劫" then
      请求服务(48,{1204,20,44})
    elseif v=="观星台" then
      请求服务(48,{1223,56,32})
    elseif v=="仙缘洞天1" then
      请求服务(48,{1216,85,75})
    elseif v=="柳林坡" then
      请求服务(48,{1233,50,50})
    elseif v=="北俱芦洲" then
      请求服务(48,{1174,50,50})
    elseif v=="丝绸之路" then
      请求服务(48,{1235,50,50})
    elseif v=="比丘国" then
      请求服务(48,{1232,50,50})
    elseif v=="须弥东界" then
      请求服务(48,{1242,50,50})
    elseif v=="海底5层" then
      请求服务(48,{1532,33,27})
    elseif v=="地狱5层" then
      请求服务(48,{1130,50,50})
    elseif v=="龙窟7层" then
      请求服务(48,{1183,50,50})
    elseif v=="凤巢7层" then
      请求服务(48,{1192,50,50})
    elseif v=="女娲神迹" then
      请求服务(48,{1201,50,50})
    end 
    超级传送:置可见(false)
  end
end



分类配置.门派传送={
  "方寸山","盘丝洞","天 宫","天机城","女儿村","阴曹地府","普陀山","女魃墓","神木林",
  "无底洞","凌波城","花果山","化生寺","魔王寨","五庄观","大唐官府","狮驼岭","龙 宫","九黎城"
  }


xx = 0
yy = 0
for i, v in ipairs(分类配置.门派传送) do
  local 临时函数 = 超级传送:创建按钮(v, 280+xx*110, 75+yy*35) 
  function 临时函数:初始化()
    self:创建按钮精灵( __res:取资源动画("dlzy", 0x4BB58ED4),1,v)
  end
  xx=xx+1
  if xx>=4 then
    xx=0
    yy=yy+1
  end
  function 临时函数:左键按下(x, y)
    if v=="方寸山" then
      请求服务(48,{1135,72,63})
    elseif v=="盘丝洞" then
      请求服务(48,{1513,174,31})
    elseif v=="天 宫" then
      请求服务(48,{1111,175,122})
    elseif v=="天机城" then
      请求服务(48,{1250,63,92})
    elseif v=="女儿村" then
      请求服务(48,{1142,37,37})
    elseif v=="阴曹地府" then
      请求服务(48,{1122,101,102})
    elseif v=="普陀山" then
      请求服务(48,{1140,20,18})
    elseif v=="女魃墓" then
      请求服务(48,{1249,51,44})
    elseif v=="神木林" then
      请求服务(48,{1138,46,121})
    elseif v=="无底洞" then
      请求服务(48,{1139,61,125})
    elseif v=="凌波城" then
      请求服务(48,{1150,33,67})
    elseif v=="花果山" then
      请求服务(48,{1251,38,76})
    elseif v=="化生寺" then
      请求服务(48,{1002,7,88})
    elseif v=="魔王寨" then
      请求服务(48,{1512,76,29})
    elseif v=="五庄观" then
      请求服务(48,{1146,26,55})
    elseif v=="大唐官府" then
      请求服务(48,{1198,131,82})
    elseif v=="狮驼岭" then
      请求服务(48,{1131,109,77})
    elseif v=="龙 宫" then
      请求服务(48,{1116,71,77})
    elseif v=="九黎城" then
      请求服务(48,{2008,67,82}) 
    end 
    超级传送:置可见(false)
  end
end


分类配置.高级地图={
  "蓬莱仙岛","凌云渡","解阳山","麒麟山","子母河底","太岁府","月 宫"
   }


xx = 0
yy = 0
for i, v in ipairs(分类配置.高级地图) do
  local 临时函数 = 超级传送:创建按钮(v, 280+xx*110, 75+yy*35) 
  function 临时函数:初始化()
    self:创建按钮精灵( __res:取资源动画("dlzy", 0x4BB58ED4),1,v)
  end
  xx=xx+1
  if xx>=4 then
    xx=0
    yy=yy+1
  end
  function 临时函数:左键按下(x, y)
    if v=="蓬莱仙岛" then
      请求服务(48,{1207,50,50})
    elseif v=="凌云渡" then
      请求服务(48,{1920,50,50})
    elseif v=="解阳山" then
      请求服务(48,{1042,50,50})
    elseif v=="麒麟山" then
      请求服务(48,{1210,50,50})
    elseif v=="子母河底" then
      请求服务(48,{1041,50,50})
    elseif v=="太岁府" then
      请求服务(48,{1211,50,50})
    elseif v=="月 宫" then
      请求服务(48,{1114,50,50})
    end 
    超级传送:置可见(false)
  end
end


function 超级传送:打开(数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.分类="主城传送"
  self.信息=math.random(1,5)
  self.信息变换 = os.time()
  self.提示文本:清空()
  self.提示文本:置文本(self["对话"..self.信息])
  self:显示设置()
end



function 超级传送:显示设置()
  self[self.分类]:置选中(true)
  for k, v in pairs(分类配置) do
    if k~=self.分类 then
        for i, n in ipairs(v) do
          self[n]:置可见(false)
        end
    else
        for i, n in ipairs(v) do
          self[n]:置可见(true)  
        end
    end
  end
  
end





-- local 子按键 = __res:取资源动画("dlzy", 0x4BB58ED4,"精灵")
-- function 子按键:右键按下(x, y, msg)
     
-- end



local 关闭 = 超级传送:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  超级传送:置可见(false)
end

