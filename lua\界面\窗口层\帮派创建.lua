local 帮派创建 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "帮派创建", 258 + abbr.py.x, 60 + abbr.py.y, 453, 410)
function 帮派创建:初始化()
  local nsf = require("SDL.图像")(445, 410)
  if nsf["渲染开始"](nsf) then
    置窗口背景("创建帮派", 0, 12, 445, 400, true)["显示"](置窗口背景("创建帮派", 0, 12, 445, 400, true), 0, 0)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35), 72, 60)
    取白色背景(0, 0, 410, 172, true)["显示"](取白色背景(0, 0, 410, 172, true), 18, 141)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "名称")["显示"](字体18["取图像"](字体18, "名称"), 18, 66)
    字体18["取图像"](字体18, "帮派宗旨")["显示"](字体18["取图像"](字体18, "帮派宗旨"), 183, 109)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 帮派创建:打开()
  self:置可见(true)
end
local 关闭 = 帮派创建["创建我的按钮"](帮派创建, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 403, 0)
function 关闭:左键弹起(x, y, msg)
  帮派创建["置可见"](帮派创建, false)
  帮派创建["名称输入"]["清空"](帮派创建["名称输入"])
  帮派创建["宗旨输入"]["清空"](帮派创建["宗旨输入"])
end
local 名称输入 = 帮派创建["创建我的输入"](帮派创建, "名称输入", 81, 67, 254, 24, nil, 8, "黑色", 字体20)
local 宗旨输入 = 帮派创建["创建我的编辑"](帮派创建, "宗旨输入", 25, 165, 392, 162, nil, 100, "黑色")
local 创建 = 帮派创建["创建我的按钮"](帮派创建, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "创建", 153, 337, "创建")
function 创建:左键弹起(x, y, msg)
  if 名称输入["取文本"](名称输入) and 宗旨输入["取文本"](宗旨输入) then
    发送数据(35, {
      ["名称"] = 名称输入["取文本"](名称输入),
      ["公告"] = 宗旨输入["取文本"](宗旨输入)
    })
    帮派创建["置可见"](帮派创建, false)
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请输入名字和宗旨")
  end
end
