local 好友属性 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "好友属性", 178 + abbr.py.x, 5 + abbr.py.y, 512, 519)
function 好友属性:初始化()
  local nsf = require("SDL.图像")(512, 519)
  if nsf["渲染开始"](nsf) then
    置窗口背景("好友属性", 0, 14, 505, 506, true)["显示"](置窗口背景("好友属性", 0, 14, 505, 506, true), 0, 0)
    字体18["置颜色"](字体18, __取颜色("白色"))
    local lssj = 取输入背景(0, 0, 162, 23)
    字体18["取图像"](字体18, "名称")["显示"](字体18["取图像"](字体18, "名称"), 253, 67)
    lssj["显示"](lssj, 325, 65)
    字体18["取图像"](字体18, "数字ID")["显示"](字体18["取图像"](字体18, "数字ID"), 253, 105)
    lssj["显示"](lssj, 325, 103)
    字体18["取图像"](字体18, "等级")["显示"](字体18["取图像"](字体18, "等级"), 253, 143)
    lssj["显示"](lssj, 325, 141)
    字体18["取图像"](字体18, "门派")["显示"](字体18["取图像"](字体18, "门派"), 253, 181)
    lssj["显示"](lssj, 325, 179)
    字体18["取图像"](字体18, "曾用名")["显示"](字体18["取图像"](字体18, "曾用名"), 253, 219)
    lssj["显示"](lssj, 325, 217)
    字体18["取图像"](字体18, "帮派")["显示"](字体18["取图像"](字体18, "帮派"), 253, 257)
    lssj["显示"](lssj, 325, 255)
    字体18["取图像"](字体18, "称谓")["显示"](字体18["取图像"](字体18, "称谓"), 253, 295)
    lssj["显示"](lssj, 325, 293)
    字体18["取图像"](字体18, "关系")["显示"](字体18["取图像"](字体18, "关系"), 253, 333)
    lssj["显示"](lssj, 325, 331)
    字体18["取图像"](字体18, "友好度")["显示"](字体18["取图像"](字体18, "友好度"), 253, 371)
    lssj["显示"](lssj, 325, 369)
    字体18["取图像"](字体18, "好友备注")["显示"](字体18["取图像"](字体18, "好友备注"), 22, 208)
    __res:getPNGCC(3, 736, 1155, 199, 37, true)["拉伸"](__res:getPNGCC(3, 736, 1155, 199, 37, true), 228, 36)["显示"](__res:getPNGCC(3, 736, 1155, 199, 37, true)["拉伸"](__res:getPNGCC(3, 736, 1155, 199, 37, true), 228, 36), 19, 238)
    字体18["取图像"](字体18, "好友评价")["显示"](字体18["取图像"](字体18, "好友评价"), 22, 287)
    取白色背景(0, 0, 229, 74, true)["显示"](取白色背景(0, 0, 229, 74, true), 19, 317)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 好友属性:打开(data)
  self:置可见(true)
  self:重置(data)
end
function 好友属性:重置(data)
  local nsf = require("SDL.图像")(512, 519)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 683, 284, 73, 73, true)["拉伸"](__res:getPNGCC(3, 683, 284, 73, 73, true), 128, 114)["显示"](__res:getPNGCC(3, 683, 284, 73, 73, true)["拉伸"](__res:getPNGCC(3, 683, 284, 73, 73, true), 128, 114), 36, 70)
    local lssj = 取头像(data["模型"])
    if 0 == lssj[3] then
      lssj[3] = lssj[1]
    end
    __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[3]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[3])), 123, 108)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[3]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[3])), 123, 108), 37, 71)
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18["取图像"](字体18, data["名称"])["显示"](字体18["取图像"](字体18, data["名称"]), 332, 67)
    字体18["取图像"](字体18, data.id)["显示"](字体18["取图像"](字体18, data.id), 332, 105)
    字体18["取图像"](字体18, data["等级"])["显示"](字体18["取图像"](字体18, data["等级"]), 332, 143)
    字体18["取图像"](字体18, data["门派"] or "无门派")["显示"](字体18["取图像"](字体18, data["门派"] or "无门派"), 332, 181)
    字体18["取图像"](字体18, data["曾用名"] or "无")["显示"](字体18["取图像"](字体18, data["曾用名"] or "无"), 332, 219)
    字体18["取图像"](字体18, data["帮派"] or "无帮派")["显示"](字体18["取图像"](字体18, data["帮派"] or "无帮派"), 332, 257)
    字体18["取图像"](字体18, data["称谓"] or "无")["显示"](字体18["取图像"](字体18, data["称谓"] or "无"), 332, 295)
    字体18["取图像"](字体18, data["关系"])["显示"](字体18["取图像"](字体18, data["关系"]), 332, 333)
    字体18["取图像"](字体18, data["好友度"] or 0)["显示"](字体18["取图像"](字体18, data["好友度"] or 0), 332, 371)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.数据 = data
end
local 关闭 = 好友属性["创建我的按钮"](好友属性, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 462, 0)
function 关闭:左键弹起(x, y, msg)
  好友属性["置可见"](好友属性, false)
end
for i, v in ipairs({
  {
    name = "断交",
    x = 20,
    y = 409,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "断交"
  },
  {
    name = "私聊",
    x = 195,
    y = 409,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "私聊"
  },
  {
    name = "组队",
    x = 367,
    y = 409,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "组队"
  },
  {
    name = "成就",
    x = 20,
    y = 460,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "成就"
  },
  {
    name = "交易",
    x = 195,
    y = 460,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "交易"
  },
  {
    name = "给予",
    x = 367,
    y = 460,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "给予"
  }
}) do
  local 临时函数 = 好友属性["创建我的按钮"](好友属性, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "断交" then
    elseif v.name == "私聊" then
    elseif v.name == "组队" then
    elseif v.name == "成就" then
    end
  end
end
