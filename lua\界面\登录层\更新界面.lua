--[[
LastEditTime: 2024-10-17 03:46:05
--]]

local 更新界面 = 登录层:创建控件("更新界面",引擎.宽度2-550//2, 引擎.高度2-250,550, 360)
function 更新界面:初始化()
  self:创建纹理精灵(function()
        __res:取资源动画("ui",0x01000158,"图像"):显示(155,0)
        __res:取资源动画("pic", "gengxin.png","图片"):显示(5, 130)
      end
    )
    self.更新完成=false
    self.更新进度 = self:创建我的进度(__res:取资源动画("pic", "jindu.png","图片"):拉伸(510,9),"更新进度", 20, 182, 510, 9)
    local 开始游戏 =  self:创建按钮("开始游戏", 210, 320)
    function 开始游戏:初始化()
      self:创建按钮精灵(__res:取资源动画("ui",0x01000258), 1)
    end
    开始游戏:置禁止(true)
    function 开始游戏:左键弹起(x, y)
        登录层.更新界面:置可见(false)
        登录层.公告界面:置可见(true, true)
        
    end
   
end

function 更新界面:显示(x, y)
    self.更新进度:置位置((#__Http.receivedata +  #__Http.tablereceivedata * 10024000)/__Http.upsize*100)
    local  显示内容 = ((#__Http.receivedata +  #__Http.tablereceivedata * 10024000)//1000) .. "KB".."/"..__Http.upsize // 1000 .. "KB"
    说明字体:显示(x + 525 - 说明字体:取宽度(显示内容), y + 198,显示内容)
end


function 更新界面:更新(dt)
    if self.更新完成 then
        self.开始游戏:置禁止(false)
        self.更新完成=false
    end
end