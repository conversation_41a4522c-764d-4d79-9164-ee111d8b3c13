

function 取符石组合说明(名称,level)
  -- print(名称,level)
  local 效果说明 = "无"
  if level==1 then
    if 名称=="真元护体" then
      效果说明 = "减伤受到所有伤害降低1%"
    elseif 名称=="风卷残云" then
      效果说明 = "击倒鬼魂目标时\n#X有5%几率将其打飞出场"
    elseif 名称=="万丈霞光" then
      效果说明 = "增加3点恢复气血效果"
    elseif 名称=="无所畏惧" then
      效果说明 = "五行克制抵御能力+1%\nX#X该组合没有效果"
    elseif 名称=="回眸一笑" then
      效果说明 = "五行克制能力+1%\nX#X该组合没有效果"
    elseif 名称=="柳暗花明" then
      效果说明 = "中毒时降低10%的气血及魔法损失\n#X该组合没有效果"
    elseif 名称=="雪照云光" then
      效果说明 = "增加2级追捕技巧\n#X该组合没有效果"
    elseif 名称=="心灵手巧" then
      效果说明 = "增加5%捕获召唤兽宝宝的几率\n#X该组合没有效果"
    elseif 名称=="点石成金" then
      效果说明 = "遭受物理攻击有5%几率\n#X伤害降低10%"
    elseif 名称=="百步穿杨" then
      效果说明 = "物理类攻击目标\n#X额外造成25点伤害"
    elseif 名称=="隔山打牛" then
      效果说明 = "法术攻击时有一定几率对目标\n#X加成20法术伤害"
    elseif 名称=="心随我动" then
      效果说明 = "遭受物理类攻击时有一定几率\n#X抵挡25点伤害"
    elseif 名称=="云随风舞" then
      效果说明 = "遭受法术攻击时有一定几率\n#X抵挡20点伤害"
    elseif 名称=="无懈可击" then
      效果说明 = "提升自身6点防御"
    elseif 名称=="望穿秋水" then
      效果说明 = "提升自身3点灵力"
    elseif 名称=="万里横行" then
      效果说明 = "提升自身4点伤害"
    elseif 名称=="日落西山" then
      效果说明 = "提升自身4点速度"
    elseif 名称=="网罗乾坤" then
      效果说明 = "使用天罗地网时\n#X增加人物等级*1的伤害"
    elseif 名称=="石破天惊" then
      效果说明 = "使用落雷符时\n#X增加人物等级%2的伤害"
    elseif 名称=="天雷地火" then
      效果说明 = "使用天雷斩、雷霆万钧时\n#X增加人物等级%2的伤害"
    elseif 名称=="凤舞九天" then
      效果说明 = "使用雨落寒沙时\n#X增加人物等级*1的伤害"
    elseif 名称=="烟雨飘摇" then
      效果说明 = "使用烟雨剑法、飘渺式时\n#X增加人物等级%2的伤害"
    elseif 名称=="索命无常" then
      效果说明 = "使用阎罗令时\n#X增加人物等级*1的伤害"
    elseif 名称=="行云流水" then
      效果说明 = "使用五行法术时\n#X增加人物等级*1的伤害"
    elseif 名称=="福泽天下" then
      效果说明 = "使用唧唧歪歪时\n#X增加人物等级%2的伤害"
    -- elseif 名称=="势如破竹" then
    --   效果说明 = "与NPC战斗时，使用破击，增加人物等级*37%的固定伤害能力"
    elseif 名称=="销魂噬骨" then
      效果说明 = "使用夺命咒或追魂刺\n#X增加人物等级*1的伤害"
    elseif 名称=="无心插柳" then
      效果说明 = "你的溅射伤害加成提高15%\n#X从而获得溅射效果"
    end
  elseif level==2 then
    if 名称=="真元护体" then
      效果说明 = "减伤受到所有伤害降低3%"
    elseif 名称=="风卷残云" then
      效果说明 = "击倒鬼魂目标时\n#X有10%几率将其打飞出场"
    elseif 名称=="降妖伏魔" then
      效果说明 = "增加对鬼魂系目标伤害增加8%"
    elseif 名称=="万丈霞光" then
      效果说明 = "增加5点恢复气血效果"
    elseif 名称=="无所畏惧" then
      效果说明 = "五行克制抵御能力+2%\n#X该组合没有效果"
    elseif 名称=="回眸一笑" then
      效果说明 = "五行克制能力+2%\n#X该组合没有效果"
    elseif 名称=="柳暗花明" then
      效果说明 = "中毒时降低15%的气血及魔法损失\n#X该组合没有效果"
    elseif 名称=="飞檐走壁" then
      效果说明 = "释放法术时降低8%的MP消耗\n#X该组合没有效"
    elseif 名称=="雪照云光" then
      效果说明 = "增加4级追捕技巧\n#X该组合没有效果"
    elseif 名称=="心灵手巧" then
      效果说明 = "增加8%捕获召唤兽宝宝的几率\n#X该组合没有效果"
    elseif 名称=="点石成金" then
      效果说明 = "遭受物理攻击有5%几率伤\n#X害降低20%"
    elseif 名称=="百步穿杨" then
      效果说明 = "物理类攻击目标有一定几率\n#X额外造成45点伤害"
    elseif 名称=="隔山打牛" then
      效果说明 = "法术攻击时有一定几率对目标\n#X加成30法术伤害"
    elseif 名称=="心随我动" then
      效果说明 = "遭受物理类攻击时有一定几率\n#X抵挡45点伤害"
    elseif 名称=="云随风舞" then
      效果说明 = "遭受法术攻击时有一定几率\n#X抵挡40点伤害"
    elseif 名称=="天降大任" then
      效果说明 = "无视召唤兽5%的物理防御进行攻击。(该组合全身只有一件装备起效)"
    elseif 名称=="高山流水" then
      效果说明 = "对召唤兽增加人物等级/3+30的法术伤害。(该组合全身只有一件装备起效)"
    elseif 名称=="百无禁忌" then
      效果说明 = "提高自身4%对抗封印类技能的能力"
    elseif 名称=="为官之道符石" then
      效果说明 = "增加门派技能为官之道等级2级"
    elseif 名称=="黄庭经符石" then
      效果说明 = "增加门派技能黄庭经等级2级"
    elseif 名称=="小乘佛法符石" then
      效果说明 = "增加门派技能小乘佛法等级2级"
    elseif 名称=="毒经符石" then
      效果说明 = "增加门派技能毒经等级2级"
    elseif 名称=="天罡气符石" then
      效果说明 = "增加门派技能天罡气等级2级"
    elseif 名称=="九龙诀符石" then
      效果说明 = "增加门派技能九龙诀等级2级"
    elseif 名称=="周易学符石" then
      效果说明 = "增加门派技能周易学等级2级"
    elseif 名称=="灵性符石" then
      效果说明 = "增加门派技能灵性等级2级"
    elseif 名称=="灵通术符石" then
      效果说明 = "增加门派技能灵通术等级2级"
    elseif 名称=="牛逼神功符石" then
      效果说明 = "增加门派技能牛逼神功等级2级"
    elseif 名称=="魔兽神功符石" then
      效果说明 = "增加门派技能魔兽神功等级2级"
    elseif 名称=="蛛丝阵法符石" then
      效果说明 = "增加门派技能蛛丝阵法等级2级"
    -- elseif 名称=="天火献誓符石" then
    --   效果说明 = "增加门派技能天火献誓等级2级"
    elseif 名称=="燃灯灵宝符石" then
      效果说明 = "增加门派技能燃灯灵宝等级2级"
    -- elseif 名称=="神工无形符石" then
    --   效果说明 = "增加门派技能神工无形等级2级"
    elseif 名称=="巫咒符石" then
      效果说明 = "增加门派技能巫咒等级2级"
    elseif 名称=="文韬武略符石" then
      效果说明 = "增加门派技能文韬武略等级2级"
    elseif 名称=="归元心法符石" then
      效果说明 = "增加门派技能归元心法等级2级"
    elseif 名称=="佛光普照符石" then
      效果说明 = "增加门派技能佛光普照等级2级"
    elseif 名称=="倾国倾城符石" then
      效果说明 = "增加门派技能倾国倾城等级2级"
    elseif 名称=="傲世诀符石" then
      效果说明 = "增加门派技能傲世诀等级2级"
    elseif 名称=="逆鳞符石" then
      效果说明 = "增加门派技能逆鳞等级2级"
    elseif 名称=="明性修身符石" then
      效果说明 = "增加门派技能明性修身等级2级"
    elseif 名称=="五行扭转符石" then
      效果说明 = "增加门派技能五行扭转等级2级"
    elseif 名称=="拘魂诀符石" then
      效果说明 = "增加门派技能拘魂诀等级2级"
    elseif 名称=="回身击符石" then
      效果说明 = "增加门派技能回身击等级2级"
    elseif 名称=="魔兽反噬符石" then
      效果说明 = "增加门派技能魔兽反噬等级2级"
    elseif 名称=="盘丝大法符石" then
      效果说明 = "增加门派技能盘丝大法等级2级"
    -- elseif 名称=="藻光灵狱符石" then
    --   效果说明 = "增加门派技能藻光灵狱等级2级"
    elseif 名称=="地冥妙法符石" then
      效果说明 = "增加门派技能地冥妙法等级2级"
    -- elseif 名称=="千机奇巧符石" then
    --   效果说明 = "增加门派技能千机奇巧等级2级"
    elseif 名称=="万物轮转符石" then
      效果说明 = "增加门派技能万物轮转等级2级"
    elseif 名称=="十方无敌符石" then
      效果说明 = "增加门派技能十方无敌等级2级"
    elseif 名称=="符之术符石" then
      效果说明 = "增加门派技能符之术等级2级"
    elseif 名称=="歧黄之术符石" then
      效果说明 = "增加门派技能歧黄之术等级2级"
    elseif 名称=="闭月羞花符石" then
      效果说明 = "增加门派技能闭月羞花等级2级"
    elseif 名称=="乾坤塔符石" then
      效果说明 = "增加门派技能乾坤塔等级2级"
    elseif 名称=="呼风唤雨符石" then
      效果说明 = "增加门派技能呼风唤雨等级2级"
    elseif 名称=="乾坤袖符石" then
      效果说明 = "增加门派技能乾坤袖等级2级"
    elseif 名称=="金刚经符石" then
      效果说明 = "增加门派技能金刚经等级2级"
    elseif 名称=="幽冥术符石" then
      效果说明 = "增加门派技能幽冥术等级2级"
    elseif 名称=="火牛阵符石" then
      效果说明 = "增加门派技能火牛阵等级2级"
    elseif 名称=="生死搏符石" then
      效果说明 = "增加门派技能生死搏等级2级"
    elseif 名称=="催情大法符石" then
      效果说明 = "增加门派技能催情大法等级2级"
    elseif 名称=="煌火无明符石" then
      效果说明 = "增加门派技能煌火无明等级2级"
    elseif 名称=="阴风绝章符石" then
      效果说明 = "增加门派技能阴风绝章等级2级"
    elseif 名称=="瞬息万变符石" then
      效果说明 = "增加门派技能瞬息万变等级2级"
    elseif 名称=="匠心不移符石" then
      效果说明 = "增加门派技能匠心不移等级2级"
    elseif 名称=="神兵鉴赏符石" then
      效果说明 = "增加门派技能神兵鉴赏等级2级"
    elseif 名称=="霹雳咒符石" then
      效果说明 = "增加门派技能霹雳咒等级2级"
    elseif 名称=="诵经符石" then
      效果说明 = "增加门派技能诵经等级2级"
    elseif 名称=="沉鱼落雁符石" then
      效果说明 = "增加门派技能沉鱼落雁等级2级"
    elseif 名称=="宁气诀符石" then
      效果说明 = "增加门派技能宁气诀等级2级"
    elseif 名称=="破浪诀符石" then
      效果说明 = "增加门派技能破浪诀等级2级"
    elseif 名称=="潇湘仙雨符石" then
      效果说明 = "增加门派技能潇湘仙雨等级2级"
    elseif 名称=="五行学说符石" then
      效果说明 = "增加门派技能五行学说等级2级"
    elseif 名称=="尸腐恶符石" then
      效果说明 = "增加门派技能尸腐恶等级2级"
    elseif 名称=="牛虱阵符石" then
      效果说明 = "增加门派技能牛虱阵等级2级"
    elseif 名称=="阴阳二气诀符石" then
      效果说明 = "增加门派技能阴阳二气诀等级2级"
    elseif 名称=="姊妹相随符石" then
      效果说明 = "增加门派技能姊妹相随等级2级"
    elseif 名称=="无双一击符石" then
      效果说明 = "增加门派技能无双一击等级2级"
    elseif 名称=="磐龙灭法符石" then
      效果说明 = "增加门派技能磐龙灭法等级2级"
    elseif 名称=="金刚伏魔符石" then
      效果说明 = "增加门派技能金刚伏魔等级2级"
    elseif 名称=="玉质冰肌符石" then
      效果说明 = "增加门派技能玉质冰肌等级2级"
    elseif 名称=="混天术符石" then
      效果说明 = "增加门派技能混天术等级2级"
    elseif 名称=="龙附符石" then
      效果说明 = "增加门派技能龙附等级2级"
    elseif 名称=="修仙术符石" then
      效果说明 = "增加门派技能修仙术等级2级"
    elseif 名称=="护法金刚符石" then
      效果说明 = "增加门派技能护法金刚等级2级"
    elseif 名称=="六道轮回符石" then
      效果说明 = "增加门派技能六道轮回等级2级"
    elseif 名称=="震天诀符石" then
      效果说明 = "增加门派技能震天诀等级2级"
    elseif 名称=="狂兽诀符石" then
      效果说明 = "增加门派技能狂兽诀等级2级"
    elseif 名称=="秋波暗送符石" then
      效果说明 = "增加门派技能秋波暗送等级2级"
    elseif 名称=="化神以灵符石" then
      效果说明 = "增加门派技能化神以灵等级2级"
    elseif 名称=="弹指成烬符石" then
      效果说明 = "增加门派技能弹指成烬等级2级"
    elseif 名称=="攻玉以石符石" then
      效果说明 = "增加门派技能攻玉以石等级2级"
    elseif 名称=="擎天之械符石" then
      效果说明 = "增加门派技能擎天之械等级2级"
    elseif 名称=="混元神功符石" then
      效果说明 = "增加门派技能混元神功等级2级"
    elseif 名称=="秘影迷踪符石" then
      效果说明 = "增加门派技能秘影迷踪等级2级"
    elseif 名称=="神木恩泽符石" then
      效果说明 = "增加门派技能神木恩泽等级2级"
    elseif 名称=="紫薇之术符石" then
      效果说明 = "增加门派技能紫薇之术等级2级"
    elseif 名称=="神道无念符石" then
      效果说明 = "增加门派技能神道无念等级2级"
    elseif 名称=="大慈大悲符石" then
      效果说明 = "增加门派技能大慈大悲等级2级"
    elseif 名称=="香飘兰麝符石" then
      效果说明 = "增加门派技能香飘兰麝等级2级"
    elseif 名称=="清明自在符石" then
      效果说明 = "增加门派技能清明自在等级2级"
    elseif 名称=="龙腾符石" then
      效果说明 = "增加门派技能龙腾等级2级"
    elseif 名称=="混元道果符石" then
      效果说明 = "增加门派技能混元道果等级2级"
    elseif 名称=="观音咒符石" then
      效果说明 = "增加门派技能观音咒等级2级"
    elseif 名称=="九幽阴魂符石" then
      效果说明 = "增加门派技能九幽阴魂等级2级"
    elseif 名称=="火云术符石" then
      效果说明 = "增加门派技能火云术等级2级"
    elseif 名称=="训兽诀符石" then
      效果说明 = "增加门派技能训兽诀等级2级"
    elseif 名称=="天外魔音符石" then
      效果说明 = "增加门派技能天外魔音等级2级"
    elseif 名称=="天罚之焰符石" then
      效果说明 = "增加门派技能天罚之焰等级2级"
    elseif 名称=="万灵诸念符石" then
      效果说明 = "增加门派技能万灵诸念等级2级"
    elseif 名称=="探奥索隐符石" then
      效果说明 = "增加门派技能探奥索隐等级2级"
    elseif 名称=="枯骨心法符石" then
      效果说明 = "增加门派技能枯骨心法等级2级"
    elseif 名称=="疾风步符石" then
      效果说明 = "增加门派技能疾风步等级2级"
    elseif 名称=="斜月步符石" then
      效果说明 = "增加门派技能斜月步等级2级"
    elseif 名称=="渡世步符石" then
      效果说明 = "增加门派技能渡世步等级2级"
    elseif 名称=="清歌妙舞符石" then
      效果说明 = "增加门派技能清歌妙舞等级2级"
    elseif 名称=="云霄步符石" then
      效果说明 = "增加门派技能云霄步等级2级"
    elseif 名称=="游龙术符石" then
      效果说明 = "增加门派技能游龙术等级2级"
    elseif 名称=="七星遁符石" then
      效果说明 = "增加门派技能七星遁等级2级"
    elseif 名称=="莲花宝座符石" then
      效果说明 = "增加门派技能莲花宝座等级2级"
    elseif 名称=="无常步符石" then
      效果说明 = "增加门派技能无常步等级2级"
    elseif 名称=="裂石步符石" then
      效果说明 = "增加门派技能裂石步等级2级"
    elseif 名称=="大鹏展翅符石" then
      效果说明 = "增加门派技能大鹏展翅等级2级"
    elseif 名称=="盘丝步符石" then
      效果说明 = "增加门派技能盘丝步等级2级"
    elseif 名称=="离魂符石" then
      效果说明 = "增加门派技能离魂等级2级"
    elseif 名称=="驭灵咒符石" then
      效果说明 = "增加门派技能驭灵咒等级2级"
    elseif 名称=="运思如电符石" then
      效果说明 = "增加门派技能运思如电等级2级"
    elseif 名称=="鬼蛊灵蕴符石" then
      效果说明 = "增加门派技能鬼蛊灵蕴等级2级"
    elseif 名称=="天地无极符石" then
      效果说明 = "增加门派技能天地无极等级2级"
    elseif 名称=="啸嗷符石" then
      效果说明 = "增加门派技能啸嗷等级2级"
    elseif 名称=="法天象地符石" then
      效果说明 = "增加门派技能法天象地等级2级"
    elseif 名称=="武神显圣符石" then
      效果说明 = "增加门派技能武神显圣等级2级"
    elseif 名称=="诛魔符石" then
      效果说明 = "增加门派技能诛魔等级2级"
    elseif 名称=="九转玄功符石" then
      效果说明 = "增加门派技能九转玄功等级2级"
    elseif 名称=="气吞山河符石" then
      效果说明 = "增加门派技能气吞山河等级2级"

    elseif 名称=="兵铸乾坤符石" then
      效果说明 = "增加门派技能兵铸乾坤等级2级"
    elseif 名称=="魂枫战舞符石" then
      效果说明 = "增加门派技能魂枫战舞等级2级"
    elseif 名称=="九黎战歌符石" then
      效果说明 = "增加门派技能九黎战歌等级2级"
    elseif 名称=="战火雄魂符石" then
      效果说明 = "增加门派技能战火雄魂等级2级"
    elseif 名称=="燃铁飞花符石" then
      效果说明 = "增加门派技能燃铁飞花等级2级"
    elseif 名称=="魔神降世符石" then
      效果说明 = "增加门派技能魔神降世等级2级"
    elseif 名称=="风行九黎符石" then
      效果说明 = "增加门派技能风行九黎等级2级"

    elseif 名称=="网罗乾坤" then
      效果说明 = "使用天罗地网时\n#X增加人物等级*1.5的伤害"
    elseif 名称=="石破天惊" then
      效果说明 = "使用落雷符时\n#X增加人物等级%1.5的伤害"
    elseif 名称=="天雷地火" then
      效果说明 = "使用天雷斩、雷霆万钧时\n#X增加人物等级%1.5的伤害"
    elseif 名称=="凤舞九天" then
      效果说明 = "使用雨落寒沙时\n#X增加人物等级*1.5的伤害"
    elseif 名称=="烟雨飘摇" then
      效果说明 = "使用烟雨剑法、飘渺式时\n#X增加人物等级%1.5的伤害"
    elseif 名称=="索命无常" then
      效果说明 = "使用阎罗令时\n#X增加人物等级*1.5的伤害"
    elseif 名称=="行云流水" then
      效果说明 = "使用五行法术时\n#X增加人物等级*1.5的伤害"
    elseif 名称=="福泽天下" then
      效果说明 = "使用唧唧歪歪时\n#X增加人物等级%1.5的伤害"
    -- elseif 名称=="势如破竹" then
    --   效果说明 = "与NPC战斗时，使用破击，增加人物等级*37%的固定伤害能力"
    elseif 名称=="销魂噬骨" then
      效果说明 = "使用夺命咒或追魂刺\n#X增加人物等级*1.5的伤害"
    elseif 名称=="无心插柳" then
      效果说明 = "你的溅射伤害加成提高20%\n#X从而获得溅射效果"
  


    end
  elseif level==3 then
    if 名称=="真元护体" then
      效果说明 = "减伤受到所有伤害降低5%"
    elseif 名称=="风卷残云" then
      效果说明 = "击倒鬼魂目标时\n#X有15%几率将其打飞出场"
    elseif 名称=="降妖伏魔" then
      效果说明 = "增加对鬼魂系目标伤害增加15%"
    elseif 名称=="万丈霞光" then
      效果说明 = "增加8点恢复气血效果"
    elseif 名称=="无所畏惧" then
      效果说明 = "五行克制抵御能力+3%\n#X该组合没有效果"
    elseif 名称=="回眸一笑" then
      效果说明 = "五行克制能力+3%\n#X该组合没有效果"
    elseif 名称=="柳暗花明" then
      效果说明 = "中毒时降低20%的气血及魔法损失\n#X该组合没有效果"
    elseif 名称=="飞檐走壁" then
      效果说明 = "释放法术时降低12%的MP消耗\n#X该组合没有效果"
    elseif 名称=="雪照云光" then
      效果说明 = "增加6级追捕技巧\n#X该组合没有效果"
    elseif 名称=="心灵手巧" then
      效果说明 = "增加10%捕获召唤兽宝宝的几率\n#X该组合没有效果"
    elseif 名称=="点石成金" then
      效果说明 = "遭受物理攻击有5%几率\n#X伤害降低25%"
    elseif 名称=="百步穿杨" then
      效果说明 = "物理类攻击目标时有一定几率额外\n#X造成75点伤害"
    elseif 名称=="隔山打牛" then
      效果说明 = "法术攻击时有一定几率对目标\n#X加成50法术伤害"
    elseif 名称=="心随我动" then
      效果说明 = "遭受物理类攻击时有一定几率\n#X抵挡70点伤害"
    elseif 名称=="云随风舞" then
      效果说明 = "遭受法术攻击时有一定几率\n#X抵挡70点伤害"
    elseif 名称=="天降大任" then
      效果说明 = "无视召唤兽10%的物理防御进行攻击。(该组合全身只有一件装备起效)"
    elseif 名称=="高山流水" then
      效果说明 = "对召唤兽增加人物等级/2+30的法术伤害。(该组合全身只有一件装备起效)"
    elseif 名称=="百无禁忌" then
      效果说明 = "提高自身8%对抗封印类技能的能力"
    elseif 名称=="为官之道符石" then
      效果说明 = "增加门派技能为官之道等级4级"
    elseif 名称=="黄庭经符石" then
      效果说明 = "增加门派技能黄庭经等级4级"
    elseif 名称=="小乘佛法符石" then
      效果说明 = "增加门派技能小乘佛法等级4级"
    elseif 名称=="毒经符石" then
      效果说明 = "增加门派技能毒经等级4级"
    elseif 名称=="天罡气符石" then
      效果说明 = "增加门派技能天罡气等级4级"
    elseif 名称=="九龙诀符石" then
      效果说明 = "增加门派技能九龙诀等级4级"
    elseif 名称=="周易学符石" then
      效果说明 = "增加门派技能周易学等级4级"
    elseif 名称=="灵性符石" then
      效果说明 = "增加门派技能灵性等级4级"
    elseif 名称=="灵通术符石" then
      效果说明 = "增加门派技能灵通术等级4级"
    elseif 名称=="牛逼神功符石" then
      效果说明 = "增加门派技能牛逼神功等级4级"
    elseif 名称=="魔兽神功符石" then
      效果说明 = "增加门派技能魔兽神功等级4级"
    elseif 名称=="蛛丝阵法符石" then
      效果说明 = "增加门派技能蛛丝阵法等级4级"
    -- elseif 名称=="天火献誓符石" then
    --   效果说明 = "增加门派技能天火献誓等级4级"
    elseif 名称=="燃灯灵宝符石" then
      效果说明 = "增加门派技能燃灯灵宝等级4级"
    -- elseif 名称=="神工无形符石" then
    --   效果说明 = "增加门派技能神工无形等级4级"
    elseif 名称=="巫咒符石" then
      效果说明 = "增加门派技能巫咒等级4级"
    elseif 名称=="文韬武略符石" then
      效果说明 = "增加门派技能文韬武略等级4级"
    elseif 名称=="归元心法符石" then
      效果说明 = "增加门派技能归元心法等级4级"
    elseif 名称=="佛光普照符石" then
      效果说明 = "增加门派技能佛光普照等级4级"
    elseif 名称=="倾国倾城符石" then
      效果说明 = "增加门派技能倾国倾城等级4级"
    elseif 名称=="傲世诀符石" then
      效果说明 = "增加门派技能傲世诀等级4级"
    elseif 名称=="逆鳞符石" then
      效果说明 = "增加门派技能逆鳞等级4级"
    elseif 名称=="明性修身符石" then
      效果说明 = "增加门派技能明性修身等级4级"
    elseif 名称=="五行扭转符石" then
      效果说明 = "增加门派技能五行扭转等级4级"
    elseif 名称=="拘魂诀符石" then
      效果说明 = "增加门派技能拘魂诀等级4级"
    elseif 名称=="回身击符石" then
      效果说明 = "增加门派技能回身击等级4级"
    elseif 名称=="魔兽反噬符石" then
      效果说明 = "增加门派技能魔兽反噬等级4级"
    elseif 名称=="盘丝大法符石" then
      效果说明 = "增加门派技能盘丝大法等级4级"
    -- elseif 名称=="藻光灵狱符石" then
    --   效果说明 = "增加门派技能藻光灵狱等级4级"
    elseif 名称=="地冥妙法符石" then
      效果说明 = "增加门派技能地冥妙法等级4级"
    -- elseif 名称=="千机奇巧符石" then
    --   效果说明 = "增加门派技能千机奇巧等级4级"
    elseif 名称=="万物轮转符石" then
      效果说明 = "增加门派技能万物轮转等级4级"
    elseif 名称=="十方无敌符石" then
      效果说明 = "增加门派技能十方无敌等级4级"
    elseif 名称=="符之术符石" then
      效果说明 = "增加门派技能符之术等级4级"
    elseif 名称=="歧黄之术符石" then
      效果说明 = "增加门派技能歧黄之术等级4级"
    elseif 名称=="闭月羞花符石" then
      效果说明 = "增加门派技能闭月羞花等级4级"
    elseif 名称=="乾坤塔符石" then
      效果说明 = "增加门派技能乾坤塔等级4级"
    elseif 名称=="呼风唤雨符石" then
      效果说明 = "增加门派技能呼风唤雨等级4级"
    elseif 名称=="乾坤袖符石" then
      效果说明 = "增加门派技能乾坤袖等级4级"
    elseif 名称=="金刚经符石" then
      效果说明 = "增加门派技能金刚经等级4级"
    elseif 名称=="幽冥术符石" then
      效果说明 = "增加门派技能幽冥术等级4级"
    elseif 名称=="火牛阵符石" then
      效果说明 = "增加门派技能火牛阵等级4级"
    elseif 名称=="生死搏符石" then
      效果说明 = "增加门派技能生死搏等级4级"
    elseif 名称=="催情大法符石" then
      效果说明 = "增加门派技能催情大法等级4级"
    elseif 名称=="煌火无明符石" then
      效果说明 = "增加门派技能煌火无明等级4级"
    elseif 名称=="阴风绝章符石" then
      效果说明 = "增加门派技能阴风绝章等级4级"
    elseif 名称=="瞬息万变符石" then
      效果说明 = "增加门派技能瞬息万变等级4级"
    elseif 名称=="匠心不移符石" then
      效果说明 = "增加门派技能匠心不移等级4级"
    elseif 名称=="神兵鉴赏符石" then
      效果说明 = "增加门派技能神兵鉴赏等级4级"
    elseif 名称=="霹雳咒符石" then
      效果说明 = "增加门派技能霹雳咒等级4级"
    elseif 名称=="诵经符石" then
      效果说明 = "增加门派技能诵经等级4级"
    elseif 名称=="沉鱼落雁符石" then
      效果说明 = "增加门派技能沉鱼落雁等级4级"
    elseif 名称=="宁气诀符石" then
      效果说明 = "增加门派技能宁气诀等级4级"
    elseif 名称=="破浪诀符石" then
      效果说明 = "增加门派技能破浪诀等级4级"
    elseif 名称=="潇湘仙雨符石" then
      效果说明 = "增加门派技能潇湘仙雨等级4级"
    elseif 名称=="五行学说符石" then
      效果说明 = "增加门派技能五行学说等级4级"
    elseif 名称=="尸腐恶符石" then
      效果说明 = "增加门派技能尸腐恶等级4级"
    elseif 名称=="牛虱阵符石" then
      效果说明 = "增加门派技能牛虱阵等级4级"
    elseif 名称=="阴阳二气诀符石" then
      效果说明 = "增加门派技能阴阳二气诀等级4级"
    elseif 名称=="姊妹相随符石" then
      效果说明 = "增加门派技能姊妹相随等级4级"
    elseif 名称=="无双一击符石" then
      效果说明 = "增加门派技能无双一击等级4级"
    elseif 名称=="磐龙灭法符石" then
      效果说明 = "增加门派技能磐龙灭法等级4级"
    elseif 名称=="金刚伏魔符石" then
      效果说明 = "增加门派技能金刚伏魔等级4级"
    elseif 名称=="玉质冰肌符石" then
      效果说明 = "增加门派技能玉质冰肌等级4级"
    elseif 名称=="混天术符石" then
      效果说明 = "增加门派技能混天术等级4级"
    elseif 名称=="龙附符石" then
      效果说明 = "增加门派技能龙附等级4级"
    elseif 名称=="修仙术符石" then
      效果说明 = "增加门派技能修仙术等级4级"
    elseif 名称=="护法金刚符石" then
      效果说明 = "增加门派技能护法金刚等级4级"
    elseif 名称=="六道轮回符石" then
      效果说明 = "增加门派技能六道轮回等级4级"
    elseif 名称=="震天诀符石" then
      效果说明 = "增加门派技能震天诀等级4级"
    elseif 名称=="狂兽诀符石" then
      效果说明 = "增加门派技能狂兽诀等级4级"
    elseif 名称=="秋波暗送符石" then
      效果说明 = "增加门派技能秋波暗送等级4级"
    elseif 名称=="化神以灵符石" then
      效果说明 = "增加门派技能化神以灵等级4级"
    elseif 名称=="弹指成烬符石" then
      效果说明 = "增加门派技能弹指成烬等级4级"
    elseif 名称=="攻玉以石符石" then
      效果说明 = "增加门派技能攻玉以石等级4级"
    elseif 名称=="擎天之械符石" then
      效果说明 = "增加门派技能擎天之械等级4级"
    elseif 名称=="混元神功符石" then
      效果说明 = "增加门派技能混元神功等级4级"
    elseif 名称=="秘影迷踪符石" then
      效果说明 = "增加门派技能秘影迷踪等级4级"
    elseif 名称=="神木恩泽符石" then
      效果说明 = "增加门派技能神木恩泽等级4级"
    elseif 名称=="紫薇之术符石" then
      效果说明 = "增加门派技能紫薇之术等级4级"
    elseif 名称=="神道无念符石" then
      效果说明 = "增加门派技能神道无念等级4级"
    elseif 名称=="大慈大悲符石" then
      效果说明 = "增加门派技能大慈大悲等级4级"
    elseif 名称=="香飘兰麝符石" then
      效果说明 = "增加门派技能香飘兰麝等级4级"
    elseif 名称=="清明自在符石" then
      效果说明 = "增加门派技能清明自在等级4级"
    elseif 名称=="龙腾符石" then
      效果说明 = "增加门派技能龙腾等级4级"
    elseif 名称=="混元道果符石" then
      效果说明 = "增加门派技能混元道果等级4级"
    elseif 名称=="观音咒符石" then
      效果说明 = "增加门派技能观音咒等级4级"
    elseif 名称=="九幽阴魂符石" then
      效果说明 = "增加门派技能九幽阴魂等级4级"
    elseif 名称=="火云术符石" then
      效果说明 = "增加门派技能火云术等级4级"
    elseif 名称=="训兽诀符石" then
      效果说明 = "增加门派技能训兽诀等级4级"
    elseif 名称=="天外魔音符石" then
      效果说明 = "增加门派技能天外魔音等级4级"
    elseif 名称=="天罚之焰符石" then
      效果说明 = "增加门派技能天罚之焰等级4级"
    elseif 名称=="万灵诸念符石" then
      效果说明 = "增加门派技能万灵诸念等级4级"
    elseif 名称=="探奥索隐符石" then
      效果说明 = "增加门派技能探奥索隐等级4级"
    elseif 名称=="枯骨心法符石" then
      效果说明 = "增加门派技能枯骨心法等级4级"
    elseif 名称=="疾风步符石" then
      效果说明 = "增加门派技能疾风步等级4级"
    elseif 名称=="斜月步符石" then
      效果说明 = "增加门派技能斜月步等级4级"
    elseif 名称=="渡世步符石" then
      效果说明 = "增加门派技能渡世步等级4级"
    elseif 名称=="清歌妙舞符石" then
      效果说明 = "增加门派技能清歌妙舞等级4级"
    elseif 名称=="云霄步符石" then
      效果说明 = "增加门派技能云霄步等级4级"
    elseif 名称=="游龙术符石" then
      效果说明 = "增加门派技能游龙术等级4级"
    elseif 名称=="七星遁符石" then
      效果说明 = "增加门派技能七星遁等级4级"
    elseif 名称=="莲花宝座符石" then
      效果说明 = "增加门派技能莲花宝座等级4级"
    elseif 名称=="无常步符石" then
      效果说明 = "增加门派技能无常步等级4级"
    elseif 名称=="裂石步符石" then
      效果说明 = "增加门派技能裂石步等级4级"
    elseif 名称=="大鹏展翅符石" then
      效果说明 = "增加门派技能大鹏展翅等级4级"
    elseif 名称=="盘丝步符石" then
      效果说明 = "增加门派技能盘丝步等级4级"
    elseif 名称=="离魂符石" then
      效果说明 = "增加门派技能离魂等级4级"
    elseif 名称=="驭灵咒符石" then
      效果说明 = "增加门派技能驭灵咒等级4级"
    elseif 名称=="运思如电符石" then
      效果说明 = "增加门派技能运思如电等级4级"
    elseif 名称=="鬼蛊灵蕴符石" then
      效果说明 = "增加门派技能鬼蛊灵蕴等级4级"
    elseif 名称=="天地无极符石" then
      效果说明 = "增加门派技能天地无极等级4级"
    elseif 名称=="啸嗷符石" then
      效果说明 = "增加门派技能啸嗷等级4级"
    elseif 名称=="法天象地符石" then
      效果说明 = "增加门派技能法天象地等级4级"
    elseif 名称=="武神显圣符石" then
      效果说明 = "增加门派技能武神显圣等级4级"
    elseif 名称=="诛魔符石" then
      效果说明 = "增加门派技能诛魔等级4级"
     elseif 名称=="九转玄功符石" then
      效果说明 = "增加门派技能九转玄功等级4级"
    elseif 名称=="兵铸乾坤符石" then
      效果说明 = "增加门派技能兵铸乾坤等级4级"
    elseif 名称=="魂枫战舞符石" then
      效果说明 = "增加门派技能魂枫战舞等级4级"
    elseif 名称=="九黎战歌符石" then
      效果说明 = "增加门派技能九黎战歌等级4级"
    elseif 名称=="战火雄魂符石" then
      效果说明 = "增加门派技能战火雄魂等级4级"
    elseif 名称=="燃铁飞花符石" then
      效果说明 = "增加门派技能燃铁飞花等级4级"
    elseif 名称=="魔神降世符石" then
      效果说明 = "增加门派技能魔神降世等级4级"
    elseif 名称=="风行九黎符石" then
      效果说明 = "增加门派技能风行九黎等级4级"
    elseif 名称=="网罗乾坤" then
      效果说明 = "使用天罗地网时\n#X增加人物等级*2的伤害"
    elseif 名称=="石破天惊" then
      效果说明 = "使用落雷符时\n#X增加人物等级*1的伤害"
    elseif 名称=="天雷地火" then
      效果说明 = "使用天雷斩、雷霆万钧时\n#X增加人物等级*1的伤害"
    elseif 名称=="凤舞九天" then
      效果说明 = "使用雨落寒沙时\n#X增加人物等级*2的伤害"
    elseif 名称=="烟雨飘摇" then
      效果说明 = "使用烟雨剑法、飘渺式时\n#X增加人物等级*1的伤害"
    elseif 名称=="索命无常" then
      效果说明 = "使用阎罗令时\n#X增加人物等级*2的伤害"
    elseif 名称=="行云流水" then
      效果说明 = "使用五行法术时\n#X增加人物等级*2的伤害"
    elseif 名称=="福泽天下" then
      效果说明 = "使用唧唧歪歪时\n#X增加人物等级*1的伤害"
    -- elseif 名称=="势如破竹" then
    --   效果说明 = "与NPC战斗时，使用破击，增加人物等级*50%的固定伤害能力"
    elseif 名称=="销魂噬骨" then
      效果说明 = "使用夺命咒或追魂刺\n#X增加人物等级*2的伤害"
    elseif 名称=="无心插柳" then
      效果说明 = "你的溅射伤害加成提高25%\n#X从而获得溅射效果"
    end
  elseif level==4 then
    if 名称=="风卷残云" then
      效果说明 = "击倒鬼魂目标时\n#X有20%几率将其打飞出场"
    elseif 名称=="万丈霞光" then
      效果说明 = "增加10点恢复气血效果"
    elseif 名称=="无所畏惧" then
      效果说明 = "五行克制抵御能力+4%\n#X该组合没有效果"
    elseif 名称=="回眸一笑" then
      效果说明 = "五行克制能力+4%\n#X该组合没有效果"
    elseif 名称=="柳暗花明" then
      效果说明 = "中毒时降低25%的气血及魔法损失\n#X该组合没有效果"
    elseif 名称=="飞檐走壁" then
      效果说明 = "释放法术时降低16%的MP消耗\n#X该组合没有效果"
    elseif 名称=="点石成金" then
      效果说明 = "遭受物理攻击有5%几率\n#X伤害降低25%"
    elseif 名称=="百步穿杨" then
      效果说明 = "物理类攻击目标时有一定几率额外\n#X造成100点伤害"
    elseif 名称=="隔山打牛" then
      效果说明 = "法术攻击时有一定几率对目标\n#X加成70法术伤害"
    elseif 名称=="心随我动" then
      效果说明 = "遭受物理类攻击时有一定几率\n#X抵挡90点伤害"
    elseif 名称=="云随风舞" then
      效果说明 = "遭受法术攻击时有一定几率\n#X抵挡70点伤害"
    elseif 名称=="暗渡陈仓" then
      效果说明 = "受到物理攻击时，降低3%的所受伤害"
    elseif 名称=="化敌为友" then
      效果说明 = "受到法术攻击时，降低3%的所受伤害"
    elseif 名称=="天降大任" then
      效果说明 = "无视召唤兽15%的物理防御进行攻击。(该组合全身只有一件装备起效)"
    elseif 名称=="高山流水" then
      效果说明 = "对召唤兽增加人物等级+30的法术伤害。(该组合全身只有一件装备起效)"
    elseif 名称=="百无禁忌" then
      效果说明 = "提高自身12%对抗封印类技能的能力"
    elseif 名称=="为官之道符石" then
      效果说明 = "增加门派技能为官之道等级6级"
    elseif 名称=="黄庭经符石" then
      效果说明 = "增加门派技能黄庭经等级6级"
    elseif 名称=="小乘佛法符石" then
      效果说明 = "增加门派技能小乘佛法等级6级"
    elseif 名称=="毒经符石" then
      效果说明 = "增加门派技能毒经等级6级"
    elseif 名称=="天罡气符石" then
      效果说明 = "增加门派技能天罡气等级6级"
    elseif 名称=="九龙诀符石" then
      效果说明 = "增加门派技能九龙诀等级6级"
    elseif 名称=="周易学符石" then
      效果说明 = "增加门派技能周易学等级6级"
    elseif 名称=="灵性符石" then
      效果说明 = "增加门派技能灵性等级6级"
    elseif 名称=="灵通术符石" then
      效果说明 = "增加门派技能灵通术等级6级"
    elseif 名称=="牛逼神功符石" then
      效果说明 = "增加门派技能牛逼神功等级6级"
    elseif 名称=="魔兽神功符石" then
      效果说明 = "增加门派技能魔兽神功等级6级"
    elseif 名称=="蛛丝阵法符石" then
      效果说明 = "增加门派技能蛛丝阵法等级6级"
    -- elseif 名称=="天火献誓符石" then
    --   效果说明 = "增加门派技能天火献誓等级6级"
    elseif 名称=="燃灯灵宝符石" then
      效果说明 = "增加门派技能燃灯灵宝等级6级"
    -- elseif 名称=="神工无形符石" then
    --   效果说明 = "增加门派技能神工无形等级6级"
    elseif 名称=="巫咒符石" then
      效果说明 = "增加门派技能巫咒等级6级"
    elseif 名称=="文韬武略符石" then
      效果说明 = "增加门派技能文韬武略等级6级"
    elseif 名称=="归元心法符石" then
      效果说明 = "增加门派技能归元心法等级6级"
    elseif 名称=="佛光普照符石" then
      效果说明 = "增加门派技能佛光普照等级6级"
    elseif 名称=="倾国倾城符石" then
      效果说明 = "增加门派技能倾国倾城等级6级"
    elseif 名称=="傲世诀符石" then
      效果说明 = "增加门派技能傲世诀等级6级"
    elseif 名称=="逆鳞符石" then
      效果说明 = "增加门派技能逆鳞等级6级"
    elseif 名称=="明性修身符石" then
      效果说明 = "增加门派技能明性修身等级6级"
    elseif 名称=="五行扭转符石" then
      效果说明 = "增加门派技能五行扭转等级6级"
    elseif 名称=="拘魂诀符石" then
      效果说明 = "增加门派技能拘魂诀等级6级"
    elseif 名称=="回身击符石" then
      效果说明 = "增加门派技能回身击等级6级"
    elseif 名称=="魔兽反噬符石" then
      效果说明 = "增加门派技能魔兽反噬等级6级"
    elseif 名称=="盘丝大法符石" then
      效果说明 = "增加门派技能盘丝大法等级6级"
    -- elseif 名称=="藻光灵狱符石" then
    --   效果说明 = "增加门派技能藻光灵狱等级6级"
    elseif 名称=="地冥妙法符石" then
      效果说明 = "增加门派技能地冥妙法等级6级"
    -- elseif 名称=="千机奇巧符石" then
    --   效果说明 = "增加门派技能千机奇巧等级6级"
    elseif 名称=="万物轮转符石" then
      效果说明 = "增加门派技能万物轮转等级6级"
    elseif 名称=="十方无敌符石" then
      效果说明 = "增加门派技能十方无敌等级6级"
    elseif 名称=="符之术符石" then
      效果说明 = "增加门派技能符之术等级6级"
    elseif 名称=="歧黄之术符石" then
      效果说明 = "增加门派技能歧黄之术等级6级"
    elseif 名称=="闭月羞花符石" then
      效果说明 = "增加门派技能闭月羞花等级6级"
    elseif 名称=="乾坤塔符石" then
      效果说明 = "增加门派技能乾坤塔等级6级"
    elseif 名称=="呼风唤雨符石" then
      效果说明 = "增加门派技能呼风唤雨等级6级"
    elseif 名称=="乾坤袖符石" then
      效果说明 = "增加门派技能乾坤袖等级6级"
    elseif 名称=="金刚经符石" then
      效果说明 = "增加门派技能金刚经等级6级"
    elseif 名称=="幽冥术符石" then
      效果说明 = "增加门派技能幽冥术等级6级"
    elseif 名称=="火牛阵符石" then
      效果说明 = "增加门派技能火牛阵等级6级"
    elseif 名称=="生死搏符石" then
      效果说明 = "增加门派技能生死搏等级6级"
    elseif 名称=="催情大法符石" then
      效果说明 = "增加门派技能催情大法等级6级"
    elseif 名称=="煌火无明符石" then
      效果说明 = "增加门派技能煌火无明等级6级"
    elseif 名称=="阴风绝章符石" then
      效果说明 = "增加门派技能阴风绝章等级6级"
    elseif 名称=="瞬息万变符石" then
      效果说明 = "增加门派技能瞬息万变等级6级"
    elseif 名称=="匠心不移符石" then
      效果说明 = "增加门派技能匠心不移等级6级"
    elseif 名称=="神兵鉴赏符石" then
      效果说明 = "增加门派技能神兵鉴赏等级6级"
    elseif 名称=="霹雳咒符石" then
      效果说明 = "增加门派技能霹雳咒等级6级"
    elseif 名称=="诵经符石" then
      效果说明 = "增加门派技能诵经等级6级"
    elseif 名称=="沉鱼落雁符石" then
      效果说明 = "增加门派技能沉鱼落雁等级6级"
    elseif 名称=="宁气诀符石" then
      效果说明 = "增加门派技能宁气诀等级6级"
    elseif 名称=="破浪诀符石" then
      效果说明 = "增加门派技能破浪诀等级6级"
    elseif 名称=="潇湘仙雨符石" then
      效果说明 = "增加门派技能潇湘仙雨等级6级"
    elseif 名称=="五行学说符石" then
      效果说明 = "增加门派技能五行学说等级6级"
    elseif 名称=="尸腐恶符石" then
      效果说明 = "增加门派技能尸腐恶等级6级"
    elseif 名称=="牛虱阵符石" then
      效果说明 = "增加门派技能牛虱阵等级6级"
    elseif 名称=="阴阳二气诀符石" then
      效果说明 = "增加门派技能阴阳二气诀等级6级"
    elseif 名称=="姊妹相随符石" then
      效果说明 = "增加门派技能姊妹相随等级6级"
    elseif 名称=="无双一击符石" then
      效果说明 = "增加门派技能无双一击等级6级"
    elseif 名称=="磐龙灭法符石" then
      效果说明 = "增加门派技能磐龙灭法等级6级"
    elseif 名称=="金刚伏魔符石" then
      效果说明 = "增加门派技能金刚伏魔等级6级"
    elseif 名称=="玉质冰肌符石" then
      效果说明 = "增加门派技能玉质冰肌等级6级"
    elseif 名称=="混天术符石" then
      效果说明 = "增加门派技能混天术等级6级"
    elseif 名称=="龙附符石" then
      效果说明 = "增加门派技能龙附等级6级"
    elseif 名称=="修仙术符石" then
      效果说明 = "增加门派技能修仙术等级6级"
    elseif 名称=="护法金刚符石" then
      效果说明 = "增加门派技能护法金刚等级6级"
    elseif 名称=="六道轮回符石" then
      效果说明 = "增加门派技能六道轮回等级6级"
    elseif 名称=="震天诀符石" then
      效果说明 = "增加门派技能震天诀等级6级"
    elseif 名称=="狂兽诀符石" then
      效果说明 = "增加门派技能狂兽诀等级6级"
    elseif 名称=="秋波暗送符石" then
      效果说明 = "增加门派技能秋波暗送等级6级"
    elseif 名称=="化神以灵符石" then
      效果说明 = "增加门派技能化神以灵等级6级"
    elseif 名称=="弹指成烬符石" then
      效果说明 = "增加门派技能弹指成烬等级6级"
    elseif 名称=="攻玉以石符石" then
      效果说明 = "增加门派技能攻玉以石等级6级"
    elseif 名称=="擎天之械符石" then
      效果说明 = "增加门派技能擎天之械等级6级"
    elseif 名称=="混元神功符石" then
      效果说明 = "增加门派技能混元神功等级6级"
    elseif 名称=="秘影迷踪符石" then
      效果说明 = "增加门派技能秘影迷踪等级6级"
    elseif 名称=="神木恩泽符石" then
      效果说明 = "增加门派技能神木恩泽等级6级"
    elseif 名称=="紫薇之术符石" then
      效果说明 = "增加门派技能紫薇之术等级6级"
    elseif 名称=="神道无念符石" then
      效果说明 = "增加门派技能神道无念等级6级"
    elseif 名称=="大慈大悲符石" then
      效果说明 = "增加门派技能大慈大悲等级6级"
    elseif 名称=="香飘兰麝符石" then
      效果说明 = "增加门派技能香飘兰麝等级6级"
    elseif 名称=="清明自在符石" then
      效果说明 = "增加门派技能清明自在等级6级"
    elseif 名称=="龙腾符石" then
      效果说明 = "增加门派技能龙腾等级6级"
    elseif 名称=="混元道果符石" then
      效果说明 = "增加门派技能混元道果等级6级"
    elseif 名称=="观音咒符石" then
      效果说明 = "增加门派技能观音咒等级6级"
    elseif 名称=="九幽阴魂符石" then
      效果说明 = "增加门派技能九幽阴魂等级6级"
    elseif 名称=="火云术符石" then
      效果说明 = "增加门派技能火云术等级6级"
    elseif 名称=="训兽诀符石" then
      效果说明 = "增加门派技能训兽诀等级6级"
    elseif 名称=="天外魔音符石" then
      效果说明 = "增加门派技能天外魔音等级6级"
    elseif 名称=="天罚之焰符石" then
      效果说明 = "增加门派技能天罚之焰等级6级"
    elseif 名称=="万灵诸念符石" then
      效果说明 = "增加门派技能万灵诸念等级6级"
    elseif 名称=="探奥索隐符石" then
      效果说明 = "增加门派技能探奥索隐等级6级"
    elseif 名称=="枯骨心法符石" then
      效果说明 = "增加门派技能枯骨心法等级6级"
    elseif 名称=="疾风步符石" then
      效果说明 = "增加门派技能疾风步等级6级"
    elseif 名称=="斜月步符石" then
      效果说明 = "增加门派技能斜月步等级6级"
    elseif 名称=="渡世步符石" then
      效果说明 = "增加门派技能渡世步等级6级"
    elseif 名称=="清歌妙舞符石" then
      效果说明 = "增加门派技能清歌妙舞等级6级"
    elseif 名称=="云霄步符石" then
      效果说明 = "增加门派技能云霄步等级6级"
    elseif 名称=="游龙术符石" then
      效果说明 = "增加门派技能游龙术等级6级"
    elseif 名称=="七星遁符石" then
      效果说明 = "增加门派技能七星遁等级6级"
    elseif 名称=="莲花宝座符石" then
      效果说明 = "增加门派技能莲花宝座等级6级"
    elseif 名称=="无常步符石" then
      效果说明 = "增加门派技能无常步等级6级"
    elseif 名称=="裂石步符石" then
      效果说明 = "增加门派技能裂石步等级6级"
    elseif 名称=="大鹏展翅符石" then
      效果说明 = "增加门派技能大鹏展翅等级6级"
    elseif 名称=="盘丝步符石" then
      效果说明 = "增加门派技能盘丝步等级6级"
    elseif 名称=="离魂符石" then
      效果说明 = "增加门派技能离魂等级6级"
    elseif 名称=="驭灵咒符石" then
      效果说明 = "增加门派技能驭灵咒等级6级"
    elseif 名称=="运思如电符石" then
      效果说明 = "增加门派技能运思如电等级6级"
    elseif 名称=="鬼蛊灵蕴符石" then
      效果说明 = "增加门派技能鬼蛊灵蕴等级6级"
    elseif 名称=="天地无极符石" then
      效果说明 = "增加门派技能天地无极等级6级"
    elseif 名称=="啸嗷符石" then
      效果说明 = "增加门派技能啸嗷等级6级"
    elseif 名称=="法天象地符石" then
      效果说明 = "增加门派技能法天象地等级6级"
    elseif 名称=="武神显圣符石" then
      效果说明 = "增加门派技能武神显圣等级6级"
    elseif 名称=="诛魔符石" then
      效果说明 = "增加门派技能诛魔等级6级"
    elseif 名称=="九转玄功符石" then
      效果说明 = "增加门派技能九转玄功等级6级"

    elseif 名称=="兵铸乾坤符石" then
      效果说明 = "增加门派技能兵铸乾坤等级6级"
    elseif 名称=="魂枫战舞符石" then
      效果说明 = "增加门派技能魂枫战舞等级6级"
    elseif 名称=="九黎战歌符石" then
      效果说明 = "增加门派技能九黎战歌等级6级"
    elseif 名称=="战火雄魂符石" then
      效果说明 = "增加门派技能战火雄魂等级6级"
    elseif 名称=="燃铁飞花符石" then
      效果说明 = "增加门派技能燃铁飞花等级6级"
    elseif 名称=="魔神降世符石" then
      效果说明 = "增加门派技能魔神降世等级6级"
    elseif 名称=="风行九黎符石" then
      效果说明 = "增加门派技能风行九黎等级6级"
    end
  end
  return 效果说明
end

function 取符石部位(部位)
  if 部位==1 then
    return "头盔/发钗"
  elseif 部位==2 then
    return "项链"
  elseif 部位==3 then
    return "武器"
  elseif 部位==4 then
    return "铠甲/女衣"
  elseif 部位==5 then
    return "腰带"
  elseif 部位==6 then
    return "靴"
  else
    return "无"
  end
end

function 取星位颜色(部位)
  if 部位==1 then
    return {[1]="黑",[2]="白"}
  elseif 部位==2 then
    return {[1]="红",[2]="蓝"}
  elseif 部位==3 then
    return {[1]="红",[2]="金"}
  elseif 部位==4 then
    return {[1]="白",[2]="黑"}
  elseif 部位==5 then
    return {[1]="蓝",[2]="绿"}
  elseif 部位==6 then
    return {[1]="蓝",[2]="红"}
  else
    return {[1]="无效道具",[2]="无效道具"}
  end
end

function 取星石部位(部位)
  if 部位==1 then
    return "头盔"
  elseif 部位==2 then
    return "饰物"
  elseif 部位==3 then
    return "武器"
  elseif 部位==4 then
    return "衣甲"
  elseif 部位==5 then
    return "腰带"
  elseif 部位==6 then
    return "靴子"
  else
    return "无效道具"
  end
end

function 取星位相互(部位)
    if 部位==1 then
        return "力量"
    elseif 部位==2 then
        return "魔力"
    elseif 部位==3 then
        return "体质"
    elseif 部位==4 then
        return "耐力"
    elseif 部位==5 then
        return "体质"
    elseif 部位==6 then
        return "敏捷"
    else
        return "力量"
    end
end

function 取星位组合(sj,level,强制)
  if sj==nil or type(sj)~="table" then
    return nil
  end
  if not 强制 then
    if sj[1] ==nil or sj[1].颜色==nil or sj[2] ==nil or sj[2].颜色==nil then
      return nil
    elseif sj[3] ==nil or sj[3].颜色==nil then
      level = 1
    elseif sj[4] ==nil or sj[4].颜色==nil then
      level = 2
    elseif  sj[5] ==nil or sj[5].颜色==nil then
      level = 3
    else
      level = 4
    end
  end
  local 返回数据 = nil
  -- 部位限制 头=1 项链=2 武器=3 衣服=4 腰带=5 鞋子=6
  if level==1 then
    --一级符石组合
    if sj[1].颜色 == "红色" and sj[2].颜色 == "蓝色" then
      返回数据={组合="真元护体",部位=nil,门派=nil}
    elseif sj[1].颜色 == "红色" and sj[2].颜色 == "黄色" then
      返回数据={组合="风卷残云",部位=nil,门派=nil}
    elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "紫色" then
      返回数据={组合="万丈霞光",部位=nil,门派=nil}
    elseif sj[1].颜色 == "红色" and sj[2].颜色 == "白色" then
      返回数据={组合="无所畏惧",部位=nil,门派=nil}
    elseif sj[1].颜色 == "白色" and sj[2].颜色 == "蓝色" then
      返回数据={组合="回眸一笑",部位=nil,门派=nil}
    elseif sj[1].颜色 == "红色" and sj[2].颜色 == "绿色" then
      返回数据={组合="柳暗花明",部位=nil,门派=nil}
    elseif sj[1].颜色 == "黑色" and sj[2].颜色 == "黄色" then
      返回数据={组合="雪照云光",部位=nil,门派=nil}
    elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "紫色" then
      返回数据={组合="心灵手巧",部位=nil,门派=nil}
    elseif sj[1].颜色 == "紫色" and sj[2].颜色 == "绿色" then
      返回数据={组合="点石成金",部位=nil,门派=nil}
    elseif sj[1].颜色 == "黑色" and sj[2].颜色 == "紫色" then
      返回数据={组合="百步穿杨",部位=nil,门派=nil}
    elseif sj[1].颜色 == "白色" and sj[2].颜色 == "红色" then
      返回数据={组合="隔山打牛",部位=nil,门派=nil}
    elseif sj[1].颜色 == "黑色" and sj[2].颜色 == "蓝色" then
      返回数据={组合="心随我动",部位=nil,门派=nil}
    elseif sj[1].颜色 == "白色" and sj[2].颜色 == "黄色" then
      返回数据={组合="云随风舞",部位=nil,门派=nil}
    elseif sj[1].颜色 == "蓝色" and sj[2].颜色 == "红色" then
      返回数据={组合="无懈可击",部位=nil,门派=nil}
    elseif sj[1].颜色 == "蓝色" and sj[2].颜色 == "白色" then
      返回数据={组合="望穿秋水",部位=nil,门派=nil}
    elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "黑色" then
      返回数据={组合="万里横行",部位=nil,门派=nil}
    elseif sj[1].颜色 == "红色" and sj[2].颜色 == "紫色" then
      返回数据={组合="日落西山",部位=nil,门派=nil}
    elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "绿色" then
      返回数据={组合="网罗乾坤",部位=nil,门派="盘丝洞"}
    elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "红色" then
      返回数据={组合="石破天惊",部位=nil,门派="方寸山"}
    elseif sj[1].颜色 == "红色" and sj[2].颜色 == "红色" then
      返回数据={组合="天雷地火",部位=nil,门派="天宫"}
    elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "黄色" then
      返回数据={组合="凤舞九天",部位=nil,门派="女儿村"}
    elseif sj[1].颜色 == "蓝色" and sj[2].颜色 == "黄色" then
      返回数据={组合="烟雨飘摇",部位=nil,门派="五庄观"}
    elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "蓝色" then
      返回数据={组合="索命无常",部位=nil,门派="阴曹地府"}
    elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "黄色" then
      返回数据={组合="行云流水",部位=nil,门派="普陀山"}
    elseif sj[1].颜色 == "黄色" and sj[2].颜色 == "红色" then
      返回数据={组合="福泽天下",部位=nil,门派="化生寺"}
    -- elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "绿色" then
    --  返回数据={组合="势如破竹",部位=nil,门派="天机城"}
    elseif sj[1].颜色 == "绿色" and sj[2].颜色 == "蓝色" then
      返回数据={组合="销魂噬骨",部位=nil,门派="无底洞"}
    elseif sj[1].颜色 == "蓝色" and sj[2].颜色 == "蓝色" then
      返回数据={组合="无心插柳",部位=nil,门派=nil}
    end
  elseif level==2 then
    --二级符石效果
    if  sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" then
      返回数据={组合="真元护体",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" then
      返回数据={组合="风卷残云",部位=nil,门派=nil}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" then
      返回数据={组合="降妖伏魔",部位=nil,门派=nil}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="白色" then
      返回数据={组合="万丈霞光",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" then
      返回数据={组合="无所畏惧",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" then
      返回数据={组合="回眸一笑",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" then
      返回数据={组合="柳暗花明",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" then
      返回数据={组合="飞檐走壁",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" then
      返回数据={组合="雪照云光",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" then
      返回数据={组合="心灵手巧",部位=nil,门派=nil}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
      返回数据={组合="点石成金",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="百步穿杨",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" then
      返回数据={组合="隔山打牛",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
      返回数据={组合="心随我动",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" then
      返回数据={组合="云随风舞",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" then
      返回数据={组合="天降大任",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="高山流水",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" then
      返回数据={组合="百无禁忌",部位=6,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" then
      返回数据={组合="为官之道符石",部位=1,门派="大唐官府"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" then
      返回数据={组合="黄庭经符石",部位=1,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" then
      返回数据={组合="小乘佛法符石",部位=1,门派="化生寺"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
      返回数据={组合="毒经符石",部位=1,门派="女儿村"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" then
      返回数据={组合="天罡气符石",部位=1,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" then
      返回数据={组合="九龙诀符石",部位=1,门派="龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" then
      返回数据={组合="周易学符石",部位=1,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="灵性符石",部位=1,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" then
      返回数据={组合="灵通术符石",部位=1,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" then
      返回数据={组合="牛逼神功符石",部位=1,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
      返回数据={组合="魔兽神功符石",部位=1,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="蛛丝阵法符石",部位=1,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" then
    --  返回数据={组合="天火献誓符石",部位=1,门派="女魃墓"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" then
      返回数据={组合="燃灯灵宝符石",部位=1,门派="无底洞"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="绿色" then
    --  返回数据={组合="神工无形符石",部位=1,门派="天机城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" then
      返回数据={组合="巫咒符石",部位=1,门派="神木林"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" then
      返回数据={组合="文韬武略符石",部位=5,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" then
      返回数据={组合="归元心法符石",部位=5,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" then
      返回数据={组合="佛光普照符石",部位=5,门派="化生寺"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" then
      返回数据={组合="倾国倾城符石",部位=5,门派="女儿村"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" then
      返回数据={组合="傲世诀符石",部位=5,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" then
      返回数据={组合="逆鳞符石",部位=5,门派="龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
      返回数据={组合="明性修身符石",部位=5,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" then
      返回数据={组合="五行扭转符石",部位=5,门派="普陀山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" then
      返回数据={组合="拘魂诀符石",部位=5,门派="阴曹地府"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" then
      返回数据={组合="回身击符石",部位=5,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" then
      返回数据={组合="魔兽反噬符石",部位=5,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" then
      返回数据={组合="盘丝大法符石",部位=5,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
    --  返回数据={组合="藻光灵狱符石",部位=5,门派="女魃墓"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" then
      返回数据={组合="地冥妙法符石",部位=5,门派="无底洞"}
    -- elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" then
    --  返回数据={组合="千机奇巧符石",部位=5,门派="天机城"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
      返回数据={组合="万物轮转符石",部位=5,门派="神木林"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" then
      返回数据={组合="十方无敌符石",部位=4,门派="大唐官府"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="符之术符石",部位=4,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" then
      返回数据={组合="歧黄之术符石",部位=4,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" then
      返回数据={组合="闭月羞花符石",部位=4,门派="女儿村"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
      返回数据={组合="乾坤塔符石",部位=4,门派="天宫"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="呼风唤雨符石",部位=4,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" then
      返回数据={组合="乾坤袖符石",部位=4,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="金刚经符石",部位=4,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
      返回数据={组合="幽冥术符石",部位=4,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" then
      返回数据={组合="火牛阵符石",部位=4,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" then
      返回数据={组合="生死搏符石",部位=4,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="催情大法符石",部位=4,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" then
    --  返回数据={组合="煌火无明符石",部位=4,门派="女魃墓"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" then
      返回数据={组合="阴风绝章符石",部位=4,门派="无底洞"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="瞬息万变符石",部位=4,门派="神木林"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
    --  返回数据={组合="匠心不移符石",部位=4,门派="天机城"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" then
      返回数据={组合="神兵鉴赏符石",部位=3,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="霹雳咒符石",部位=3,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" then
      返回数据={组合="诵经符石",部位=3,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" then
      返回数据={组合="沉鱼落雁符石",部位=3,门派="女儿村"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
      返回数据={组合="宁气诀符石",部位=3,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="破浪诀符石",部位=3,门派="龙宫"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" then
      返回数据={组合="潇湘仙雨符石",部位=3,门派="五庄观"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="五行学说符石",部位=3,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" then
      返回数据={组合="尸腐恶符石",部位=3,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" then
      返回数据={组合="牛虱阵符石",部位=3,门派="魔王寨"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="阴阳二气诀符石",部位=3,门派="狮驼岭"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
      返回数据={组合="姊妹相随符石",部位=3,门派="盘丝洞"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" then
      返回数据={组合="无双一击符石",部位=3,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" then
      返回数据={组合="磐龙灭法符石",部位=3,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" then
      返回数据={组合="金刚伏魔符石",部位=3,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" then
      返回数据={组合="玉质冰肌符石",部位=3,门派="女儿村"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" then
      返回数据={组合="混天术符石",部位=3,门派="天宫"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" then
      返回数据={组合="龙附符石",部位=3,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" then
      返回数据={组合="修仙术符石",部位=3,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="护法金刚符石",部位=3,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
      返回数据={组合="六道轮回符石",部位=3,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
      返回数据={组合="震天诀符石",部位=3,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="狂兽诀符石",部位=3,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" then
      返回数据={组合="秋波暗送符石",部位=3,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" then
    --  返回数据={组合="化神以灵符石",部位=3,门派="女魃墓"}
    -- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" then
    --  返回数据={组合="弹指成烬符石",部位=3,门派="女魃墓"}
    -- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" then
    --  返回数据={组合="攻玉以石符石",部位=3,门派="天机城"}
    -- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
    --  返回数据={组合="擎天之械符石",部位=3,门派="天机城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="白色" then
      返回数据={组合="混元神功符石",部位=3,门派="无底洞"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="秘影迷踪符石",部位=3,门派="无底洞"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" then
      返回数据={组合="神木恩泽符石",部位=3,门派="神木林"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" then
      返回数据={组合="紫薇之术符石",部位=2,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" then
      返回数据={组合="神道无念符石",部位=2,门派="方寸山"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" then
      返回数据={组合="大慈大悲符石",部位=2,门派="化生寺"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" then
      返回数据={组合="香飘兰麝符石",部位=2,门派="女儿村"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" then
      返回数据={组合="清明自在符石",部位=2,门派="天宫"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" then
      返回数据={组合="龙腾符石",部位=2,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" then
      返回数据={组合="混元道果符石",部位=2,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" then
      返回数据={组合="观音咒符石",部位=2,门派="普陀山"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" then
      返回数据={组合="九幽阴魂符石",部位=2,门派="阴曹地府"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" then
      返回数据={组合="火云术符石",部位=2,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" then
      返回数据={组合="训兽诀符石",部位=2,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" then
      返回数据={组合="天外魔音符石",部位=2,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
    --  返回数据={组合="天罚之焰符石",部位=2,门派="女魃墓"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
      返回数据={组合="万灵诸念符石",部位=2,门派="神木林"}
    -- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" then
    --  返回数据={组合="探奥索隐符石",部位=2,门派="天机城"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" then
      返回数据={组合="枯骨心法符石",部位=2,门派="无底洞"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
      返回数据={组合="疾风步符石",部位=6,门派="大唐官府"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
      返回数据={组合="斜月步符石",部位=6,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" then
      返回数据={组合="渡世步符石",部位=6,门派="化生寺"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" then
      返回数据={组合="清歌妙舞符石",部位=6,门派="女儿村"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" then
      返回数据={组合="云霄步符石",部位=6,门派="天宫"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" then
      返回数据={组合="游龙术符石",部位=6,门派="  龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" then
      返回数据={组合="七星遁符石",部位=6,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
      返回数据={组合="莲花宝座符石",部位=6,门派="普陀山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" then
      返回数据={组合="无常步符石",部位=6,门派="阴曹地府"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" then
      返回数据={组合="裂石步符石",部位=6,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" then
      返回数据={组合="大鹏展翅符石",部位=6,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" then
      返回数据={组合="盘丝步符石",部位=6,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" then
    --  返回数据={组合="离魂符石",部位=6,门派="女魃墓"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" then
      返回数据={组合="驭灵咒符石",部位=6,门派="神木林"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" then
    --  返回数据={组合="运思如电符石",部位=6,门派="天机城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
      返回数据={组合="鬼蛊灵蕴符石",部位=6,门派="无底洞"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" then
      返回数据={组合="网罗乾坤",部位=nil,门派="盘丝洞"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" then
      返回数据={组合="石破天惊",部位=nil,门派="方寸山"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="天雷地火",部位=nil,门派="天宫"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" then
      返回数据={组合="凤舞九天",部位=nil,门派="女儿村"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黄色" then
      返回数据={组合="烟雨飘摇",部位=nil,门派="五庄观"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
      返回数据={组合="索命无常",部位=nil,门派="阴曹地府"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" then
      返回数据={组合="行云流水",部位=nil,门派="普陀山"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="福泽天下",部位=nil,门派="化生寺"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" then
      返回数据={组合="销魂噬骨",部位=nil,门派="无底洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" then
    --  返回数据={组合="势如破竹",部位=nil,门派="天机城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" then
      返回数据={组合="无心插柳",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" then
      返回数据={组合="天地无极符石",部位=3,门派="凌波城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="啸嗷符石",部位=3,门派="凌波城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="法天象地符石",部位=6,门派="凌波城"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="气吞山河符石",部位=4,门派="凌波城"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" then
      返回数据={组合="武神显圣符石",部位=5,门派="凌波城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" then
      返回数据={组合="诛魔符石",部位=1,门派="凌波城"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="九转玄功符石",部位=2,门派="凌波城"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色"  then
      返回数据={组合="兵铸乾坤符石",部位=1,门派="九黎城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" then
      返回数据={组合="魂枫战舞符石",部位=2,门派="九黎城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色"  then
      返回数据={组合="九黎战歌符石",部位=3,门派="九黎城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色"  then
      返回数据={组合="战火雄魂符石",部位=3,门派="九黎城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色"  then
      返回数据={组合="燃铁飞花符石",部位=4,门派="九黎城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" then
      返回数据={组合="魔神降世符石",部位=5,门派="九黎城"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色"  then
      返回数据={组合="风行九黎符石",部位=6,门派="九黎城"}
    end
  elseif level==3 then
    --三级符石效果
    if  sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" then
      返回数据={组合="真元护体",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="白色" then
      返回数据={组合="风卷残云",部位=nil,门派=nil}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
      返回数据={组合="降妖伏魔",部位=nil,门派=nil}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" then
      返回数据={组合="万丈霞光",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="无所畏惧",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
      返回数据={组合="回眸一笑",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="柳暗花明",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="飞檐走壁",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="雪照云光",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" then
      返回数据={组合="心灵手巧",部位=nil,门派=nil}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
      返回数据={组合="点石成金",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" then
      返回数据={组合="百步穿杨",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="隔山打牛",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" then
      返回数据={组合="心随我动",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" then
      返回数据={组合="云随风舞",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" then
      返回数据={组合="天降大任",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
      返回数据={组合="高山流水",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" then
      返回数据={组合="百无禁忌",部位=6,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" then
      返回数据={组合="为官之道符石",部位=1,门派="大唐官府"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="黄庭经符石",部位=1,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
      返回数据={组合="小乘佛法符石",部位=1,门派="化生寺"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" then
      返回数据={组合="毒经符石",部位=1,门派="女儿村"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="天罡气符石",部位=1,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="九龙诀符石",部位=1,门派="龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黄色" then
      返回数据={组合="周易学符石",部位=1,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
      返回数据={组合="灵性符石",部位=1,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
      返回数据={组合="灵通术符石",部位=1,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
      返回数据={组合="牛逼神功符石",部位=1,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
      返回数据={组合="魔兽神功符石",部位=1,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" then
      返回数据={组合="蛛丝阵法符石",部位=1,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" then
    --  返回数据={组合="天火献誓符石",部位=1,门派="女魃墓"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
      返回数据={组合="燃灯灵宝符石",部位=1,门派="无底洞"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" then
    --  返回数据={组合="神工无形符石",部位=1,门派="天机城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
      返回数据={组合="巫咒符石",部位=1,门派="神木林"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
      返回数据={组合="文韬武略符石",部位=5,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
      返回数据={组合="归元心法符石",部位=5,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
      返回数据={组合="佛光普照符石",部位=5,门派="化生寺"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" then
      返回数据={组合="倾国倾城符石",部位=5,门派="女儿村"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="傲世诀符石",部位=5,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
      返回数据={组合="逆鳞符石",部位=5,门派="龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="明性修身符石",部位=5,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="五行扭转符石",部位=5,门派="普陀山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" then
      返回数据={组合="拘魂诀符石",部位=5,门派="阴曹地府"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
      返回数据={组合="回身击符石",部位=5,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
      返回数据={组合="魔兽反噬符石",部位=5,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
      返回数据={组合="盘丝大法符石",部位=5,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
    --  返回数据={组合="藻光灵狱符石",部位=5,门派="女魃墓"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
      返回数据={组合="地冥妙法符石",部位=5,门派="无底洞"}
    -- elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" then
    --  返回数据={组合="千机奇巧符石",部位=5,门派="天机城"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" then
      返回数据={组合="万物轮转符石",部位=5,门派="神木林"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="白色" then
      返回数据={组合="十方无敌符石",部位=4,门派="大唐官府"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
      返回数据={组合="符之术符石",部位=4,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" then
      返回数据={组合="歧黄之术符石",部位=4,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" then
      返回数据={组合="闭月羞花符石",部位=4,门派="女儿村"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" then
      返回数据={组合="乾坤塔符石",部位=4,门派="天宫"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" then
      返回数据={组合="呼风唤雨符石",部位=4,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
      返回数据={组合="乾坤袖符石",部位=4,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" then
      返回数据={组合="金刚经符石",部位=4,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
      返回数据={组合="幽冥术符石",部位=4,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
      返回数据={组合="火牛阵符石",部位=4,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" then
      返回数据={组合="生死搏符石",部位=4,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" then
      返回数据={组合="催情大法符石",部位=4,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" then
    --  返回数据={组合="煌火无明符石",部位=4,门派="女魃墓"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
      返回数据={组合="阴风绝章符石",部位=4,门派="无底洞"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="瞬息万变符石",部位=4,门派="神木林"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" then
    --  返回数据={组合="匠心不移符石",部位=4,门派="天机城"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
      返回数据={组合="神兵鉴赏符石",部位=3,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" then
      返回数据={组合="霹雳咒符石",部位=3,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黄色" then
      返回数据={组合="诵经符石",部位=3,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" then
      返回数据={组合="沉鱼落雁符石",部位=3,门派="女儿村"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" then
      返回数据={组合="宁气诀符石",部位=3,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色"  then
      返回数据={组合="破浪诀符石",部位=3,门派="龙宫"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" then
      返回数据={组合="潇湘仙雨符石",部位=3,门派="五庄观"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" then
      返回数据={组合="五行学说符石",部位=3,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" then
      返回数据={组合="尸腐恶符石",部位=3,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" then
      返回数据={组合="牛虱阵符石",部位=3,门派="魔王寨"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色"  then
      返回数据={组合="阴阳二气诀符石",部位=3,门派="狮驼岭"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" then
      返回数据={组合="姊妹相随符石",部位=3,门派="盘丝洞"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
      返回数据={组合="无双一击符石",部位=3,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="磐龙灭法符石",部位=3,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" then
      返回数据={组合="金刚伏魔符石",部位=3,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" then
      返回数据={组合="玉质冰肌符石",部位=3,门派="女儿村"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="紫色" then
      返回数据={组合="混天术符石",部位=3,门派="天宫"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
      返回数据={组合="龙附符石",部位=3,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
      返回数据={组合="修仙术符石",部位=3,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" then
      返回数据={组合="护法金刚符石",部位=3,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
      返回数据={组合="六道轮回符石",部位=3,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
      返回数据={组合="震天诀符石",部位=3,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" then
      返回数据={组合="狂兽诀符石",部位=3,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" then
      返回数据={组合="秋波暗送符石",部位=3,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" then
    --  返回数据={组合="化神以灵符石",部位=3,门派="女魃墓"}
    -- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" then
    --  返回数据={组合="弹指成烬符石",部位=3,门派="女魃墓"}
    -- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
    --  返回数据={组合="攻玉以石符石",部位=3,门派="天机城"}
    -- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" then
    --  返回数据={组合="擎天之械符石",部位=3,门派="天机城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色"  then
      返回数据={组合="混元神功符石",部位=3,门派="无底洞"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" then
      返回数据={组合="秘影迷踪符石",部位=3,门派="无底洞"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="神木恩泽符石",部位=3,门派="神木林"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
      返回数据={组合="紫薇之术符石",部位=2,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" and sj[4].颜色 =="紫色" then
      返回数据={组合="神道无念符石",部位=2,门派="方寸山"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
      返回数据={组合="大慈大悲符石",部位=2,门派="化生寺"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="香飘兰麝符石",部位=2,门派="女儿村"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="清明自在符石",部位=2,门派="天宫"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="龙腾符石",部位=2,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
      返回数据={组合="混元道果符石",部位=2,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="观音咒符石",部位=2,门派="普陀山"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" then
      返回数据={组合="九幽阴魂符石",部位=2,门派="阴曹地府"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" then
      返回数据={组合="火云术符石",部位=2,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="训兽诀符石",部位=2,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="天外魔音符石",部位=2,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" then
    --  返回数据={组合="天罚之焰符石",部位=2,门派="女魃墓"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" then
      返回数据={组合="万灵诸念符石",部位=2,门派="神木林"}
    -- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" then
    --  返回数据={组合="探奥索隐符石",部位=2,门派="天机城"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" then
      返回数据={组合="枯骨心法符石",部位=2,门派="无底洞"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" then
      返回数据={组合="疾风步符石",部位=6,门派="大唐官府"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
      返回数据={组合="斜月步符石",部位=6,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="渡世步符石",部位=6,门派="化生寺"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" then
      返回数据={组合="清歌妙舞符石",部位=6,门派="女儿村"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="云霄步符石",部位=6,门派="天宫"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" then
      返回数据={组合="游龙术符石",部位=6,门派="  龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
      返回数据={组合="七星遁符石",部位=6,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
      返回数据={组合="莲花宝座符石",部位=6,门派="普陀山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" then
      返回数据={组合="无常步符石",部位=6,门派="阴曹地府"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="裂石步符石",部位=6,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
      返回数据={组合="大鹏展翅符石",部位=6,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="盘丝步符石",部位=6,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" then
    --  返回数据={组合="离魂符石",部位=6,门派="女魃墓"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" then
      返回数据={组合="驭灵咒符石",部位=6,门派="神木林"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黄色" then
    --  返回数据={组合="运思如电符石",部位=6,门派="天机城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黑色" then
      返回数据={组合="鬼蛊灵蕴符石",部位=6,门派="无底洞"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" then
      返回数据={组合="网罗乾坤",部位=nil,门派="盘丝洞"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" then
      返回数据={组合="石破天惊",部位=nil,门派="方寸山"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" then
      返回数据={组合="天雷地火",部位=nil,门派="天宫"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" then
      返回数据={组合="凤舞九天",部位=nil,门派="女儿村"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" then
      返回数据={组合="烟雨飘摇",部位=nil,门派="五庄观"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" then
      返回数据={组合="索命无常",部位=nil,门派="阴曹地府"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" then
      返回数据={组合="行云流水",部位=nil,门派="普陀山"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
      返回数据={组合="福泽天下",部位=nil,门派="化生寺"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黑色" then
      返回数据={组合="销魂噬骨",部位=nil,门派="无底洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" then
    --  返回数据={组合="势如破竹",部位=nil,门派="盘丝洞"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" then
      返回数据={组合="无心插柳",部位=nil,门派=nil}

    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" then
      返回数据={组合="天地无极符石",部位=3,门派="凌波城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
      返回数据={组合="啸嗷符石",部位=3,门派="凌波城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" then
      返回数据={组合="法天象地符石",部位=6,门派="凌波城"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
      返回数据={组合="气吞山河符石",部位=4,门派="凌波城"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="武神显圣符石",部位=5,门派="凌波城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="紫色" then
      返回数据={组合="诛魔符石",部位=1,门派="凌波城"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
      返回数据={组合="九转玄功符石",部位=2,门派="凌波城"}

    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色"  then
      返回数据={组合="兵铸乾坤符石",部位=1,门派="九黎城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" then
      返回数据={组合="魂枫战舞符石",部位=2,门派="九黎城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" then
      返回数据={组合="九黎战歌符石",部位=3,门派="九黎城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" then
      返回数据={组合="战火雄魂符石",部位=3,门派="九黎城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" then
      返回数据={组合="燃铁飞花符石",部位=4,门派="九黎城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" then
      返回数据={组合="魔神降世符石",部位=5,门派="九黎城"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" then
      返回数据={组合="风行九黎符石",部位=6,门派="九黎城"}
    end
  elseif level==4 then
    --四级符石效果
    if sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="白色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="风卷残云",部位=nil,门派=nil}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黄色" then
      返回数据={组合="万丈霞光",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黑色" then
      返回数据={组合="无所畏惧",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
      返回数据={组合="回眸一笑",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
      返回数据={组合="柳暗花明",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
      返回数据={组合="飞檐走壁",部位=nil,门派=nil}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="点石成金",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="绿色" then
      返回数据={组合="百步穿杨",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
      返回数据={组合="隔山打牛",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="白色" then
      返回数据={组合="心随我动",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="黑色" then
      返回数据={组合="云随风舞",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" and sj[5].颜色 =="紫色" then
      返回数据={组合="暗渡陈仓",部位=nil,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="白色" then
      返回数据={组合="化敌为友",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="白色" then
      返回数据={组合="天降大任",部位=nil,门派=nil}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" and sj[5].颜色 =="红色" then
      返回数据={组合="高山流水",部位=nil,门派=nil}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="红色" then
      返回数据={组合="百无禁忌",部位=6,门派=nil}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黑色" then
      返回数据={组合="为官之道符石",部位=1,门派="大唐官府"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="红色" then
      返回数据={组合="黄庭经符石",部位=1,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="小乘佛法符石",部位=1,门派="化生寺"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="白色" then
      返回数据={组合="毒经符石",部位=1,门派="女儿村"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="白色" then
      返回数据={组合="天罡气符石",部位=1,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="绿色" then
      返回数据={组合="九龙诀符石",部位=1,门派="龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="黑色" then
      返回数据={组合="周易学符石",部位=1,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
      返回数据={组合="灵性符石",部位=1,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="白色" then
      返回数据={组合="灵通术符石",部位=1,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="白色" then
      返回数据={组合="牛逼神功符石",部位=1,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
      返回数据={组合="魔兽神功符石",部位=1,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="黄色" then
      返回数据={组合="蛛丝阵法符石",部位=1,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="红色" then
    --  返回数据={组合="天火献誓符石",部位=1,门派="女魃墓"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
      返回数据={组合="燃灯灵宝符石",部位=1,门派="无底洞"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" and sj[5].颜色 =="红色" then
    --  返回数据={组合="神工无形符石",部位=1,门派="天机城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
      返回数据={组合="巫咒符石",部位=1,门派="神木林"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
      返回数据={组合="文韬武略符石",部位=5,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黑色" then
      返回数据={组合="归元心法符石",部位=5,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
      返回数据={组合="佛光普照符石",部位=5,门派="化生寺"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="倾国倾城符石",部位=5,门派="女儿村"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
      返回数据={组合="傲世诀符石",部位=5,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="逆鳞符石",部位=5,门派="龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="白色" then
      返回数据={组合="明性修身符石",部位=5,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="白色" then
      返回数据={组合="五行扭转符石",部位=5,门派="普陀山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黄色" then
      返回数据={组合="拘魂诀符石",部位=5,门派="阴曹地府"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="回身击符石",部位=5,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" and sj[5].颜色 =="紫色" then
      返回数据={组合="魔兽反噬符石",部位=5,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="盘丝大法符石",部位=5,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
    --  返回数据={组合="藻光灵狱符石",部位=5,门派="女魃墓"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黄色" then
      返回数据={组合="地冥妙法符石",部位=5,门派="无底洞"}
    -- elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="白色" then
    --  返回数据={组合="千机奇巧符石",部位=5,门派="天机城"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="黄色" then
      返回数据={组合="万物轮转符石",部位=5,门派="神木林"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="白色" and sj[5].颜色 =="红色" then
      返回数据={组合="十方无敌符石",部位=4,门派="大唐官府"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 =="白色" then
      返回数据={组合="符之术符石",部位=4,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" and sj[5].颜色 =="紫色" then
      返回数据={组合="歧黄之术符石",部位=4,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="紫色" then
      返回数据={组合="闭月羞花符石",部位=4,门派="女儿村"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
      返回数据={组合="乾坤塔符石",部位=4,门派="天宫"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="白色" then
      返回数据={组合="呼风唤雨符石",部位=4,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="红色" then
      返回数据={组合="乾坤袖符石",部位=4,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黄色" then
      返回数据={组合="金刚经符石",部位=4,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="白色" then
      返回数据={组合="幽冥术符石",部位=4,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
      返回数据={组合="火牛阵符石",部位=4,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黄色" then
      返回数据={组合="生死搏符石",部位=4,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
      返回数据={组合="催情大法符石",部位=4,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="黑色" then
    --  返回数据={组合="煌火无明符石",部位=4,门派="女魃墓"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="绿色" then
      返回数据={组合="阴风绝章符石",部位=4,门派="无底洞"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
      返回数据={组合="瞬息万变符石",部位=4,门派="神木林"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="白色" then
    --  返回数据={组合="匠心不移符石",部位=4,门派="天机城"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="神兵鉴赏符石",部位=3,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="黑色" then
      返回数据={组合="霹雳咒符石",部位=3,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="紫色" then
      返回数据={组合="诵经符石",部位=3,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="黄色" then
      返回数据={组合="沉鱼落雁符石",部位=3,门派="女儿村"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="紫色" then
      返回数据={组合="宁气诀符石",部位=3,门派="天宫"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="绿色" then
      返回数据={组合="破浪诀符石",部位=3,门派="龙宫"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
      返回数据={组合="潇湘仙雨符石",部位=3,门派="五庄观"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
      返回数据={组合="五行学说符石",部位=3,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" and sj[5].颜色 =="紫色" then
      返回数据={组合="尸腐恶符石",部位=3,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
      返回数据={组合="牛虱阵符石",部位=3,门派="魔王寨"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="绿色" then
      返回数据={组合="阴阳二气诀符石",部位=3,门派="狮驼岭"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
      返回数据={组合="姊妹相随符石",部位=3,门派="盘丝洞"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
      返回数据={组合="无双一击符石",部位=3,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="白色" then
      返回数据={组合="磐龙灭法符石",部位=3,门派="方寸山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="绿色" then
      返回数据={组合="金刚伏魔符石",部位=3,门派="化生寺"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="白色" then
      返回数据={组合="玉质冰肌符石",部位=3,门派="女儿村"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="绿色" then
      返回数据={组合="混天术符石",部位=3,门派="天宫"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="白色" then
      返回数据={组合="龙附符石",部位=3,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
      返回数据={组合="修仙术符石",部位=3,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="黑色" then
      返回数据={组合="护法金刚符石",部位=3,门派="普陀山"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="绿色" then
      返回数据={组合="六道轮回符石",部位=3,门派="阴曹地府"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="黄色" then
      返回数据={组合="震天诀符石",部位=3,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="白色" then
      返回数据={组合="狂兽诀符石",部位=3,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="白色" and sj[5].颜色 =="黑色" then
      返回数据={组合="秋波暗送符石",部位=3,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="绿色" then
    --  返回数据={组合="化神以灵符石",部位=3,门派="女魃墓"}
    -- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="白色" then
    --  返回数据={组合="弹指成烬符石",部位=3,门派="女魃墓"}
    -- elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" and sj[5].颜色 =="紫色" then
    --  返回数据={组合="攻玉以石符石",部位=3,门派="天机城"}
    -- elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="红色" and sj[5].颜色 =="红色" then
    --  返回数据={组合="擎天之械符石",部位=3,门派="天机城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="红色"  then
      返回数据={组合="混元神功符石",部位=3,门派="无底洞"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
      返回数据={组合="秘影迷踪符石",部位=3,门派="无底洞"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
      返回数据={组合="神木恩泽符石",部位=3,门派="神木林"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="紫薇之术符石",部位=2,门派="大唐官府"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="绿色" then
      返回数据={组合="神道无念符石",部位=2,门派="方寸山"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="大慈大悲符石",部位=2,门派="化生寺"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
      返回数据={组合="香飘兰麝符石",部位=2,门派="女儿村"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
      返回数据={组合="清明自在符石",部位=2,门派="天宫"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="红色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="紫色" then
      返回数据={组合="龙腾符石",部位=2,门派="龙宫"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黄色" then
      返回数据={组合="混元道果符石",部位=2,门派="五庄观"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="红色" then
      返回数据={组合="观音咒符石",部位=2,门派="普陀山"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="红色" and sj[5].颜色 =="紫色" then
      返回数据={组合="九幽阴魂符石",部位=2,门派="阴曹地府"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
      返回数据={组合="火云术符石",部位=2,门派="魔王寨"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
      返回数据={组合="训兽诀符石",部位=2,门派="狮驼岭"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黑色" then
      返回数据={组合="天外魔音符石",部位=2,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黑色" then
    --  返回数据={组合="天罚之焰符石",部位=2,门派="女魃墓"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="白色" and sj[5].颜色 =="紫色" then
      返回数据={组合="万灵诸念符石",部位=2,门派="神木林"}
    -- elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="紫色" then
    --  返回数据={组合="探奥索隐符石",部位=2,门派="天机城"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="绿色" and sj[5].颜色 =="红色" then
      返回数据={组合="枯骨心法符石",部位=2,门派="无底洞"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="红色" then
      返回数据={组合="疾风步符石",部位=6,门派="大唐官府"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="白色" then
      返回数据={组合="斜月步符石",部位=6,门派="方寸山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黑色" then
      返回数据={组合="渡世步符石",部位=6,门派="化生寺"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="清歌妙舞符石",部位=6,门派="女儿村"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黑色" then
      返回数据={组合="云霄步符石",部位=6,门派="天宫"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黑色" then
      返回数据={组合="游龙术符石",部位=6,门派="  龙宫"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="七星遁符石",部位=6,门派="五庄观"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="莲花宝座符石",部位=6,门派="普陀山"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="黄色" then
      返回数据={组合="无常步符石",部位=6,门派="阴曹地府"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="红色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
      返回数据={组合="裂石步符石",部位=6,门派="魔王寨"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="红色" and sj[4].颜色 =="白色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="大鹏展翅符石",部位=6,门派="狮驼岭"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="黄色" then
      返回数据={组合="盘丝步符石",部位=6,门派="盘丝洞"}
    -- elseif sj[1].颜色 =="白色" and sj[2].颜色 =="红色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="紫色" then
    --  返回数据={组合="离魂符石",部位=6,门派="女魃墓"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="紫色" and sj[4].颜色 =="白色" and sj[5].颜色 =="绿色" then
      返回数据={组合="驭灵咒符石",部位=6,门派="神木林"}
    -- elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="白色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="黑色" then
    --  返回数据={组合="运思如电符石",部位=6,门派="天机城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="红色" and sj[4].颜色 =="黑色" and sj[5].颜色 =="紫色" then
      返回数据={组合="鬼蛊灵蕴符石",部位=6,门派="无底洞"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黑色" and sj[4].颜色 =="黄色" and sj[5].颜色 =="蓝色" then
      返回数据={组合="天地无极符石",部位=3,门派="凌波城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="白色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 =="紫色" then
      返回数据={组合="啸嗷符石",部位=3,门派="凌波城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="红色" then
      返回数据={组合="法天象地符石",部位=6,门派="凌波城"}
    elseif sj[1].颜色 =="白色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 =="红色" then
      返回数据={组合="气吞山河符石",部位=4,门派="凌波城"}
    elseif sj[1].颜色 =="黑色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="蓝色" and sj[5].颜色 =="红色" then
      返回数据={组合="武神显圣符石",部位=5,门派="凌波城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="白色" and sj[4].颜色 =="紫色" and sj[5].颜色 =="紫色" then
      返回数据={组合="诛魔符石",部位=1,门派="凌波城"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="黄色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 =="黑色" then
      返回数据={组合="九转玄功符石",部位=2,门派="凌波城"}
    elseif sj[1].颜色 =="紫色" and sj[2].颜色 =="红色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 == "白色" then
      返回数据={组合="兵铸乾坤符石",部位=1,门派="九黎城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="红色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="白色" and sj[5].颜色 == "紫色" then
      返回数据={组合="魂枫战舞符石",部位=2,门派="九黎城"}
    elseif sj[1].颜色 =="绿色" and sj[2].颜色 =="蓝色" and sj[3].颜色 =="白色" and sj[4].颜色 =="红色" and sj[5].颜色 == "黑色" then
      返回数据={组合="九黎战歌符石",部位=3,门派="九黎城"}
    elseif sj[1].颜色 =="蓝色" and sj[2].颜色 =="紫色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="红色" and sj[5].颜色 == "黑色" then
      返回数据={组合="战火雄魂符石",部位=3,门派="九黎城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="绿色" and sj[3].颜色 =="蓝色" and sj[4].颜色 =="红色" and sj[5].颜色 == "白色" then
      返回数据={组合="燃铁飞花符石",部位=4,门派="九黎城"}
    elseif sj[1].颜色 =="黄色" and sj[2].颜色 =="白色" and sj[3].颜色 =="绿色" and sj[4].颜色 =="红色" and sj[5].颜色 == "蓝色" then
      返回数据={组合="魔神降世符石",部位=5,门派="九黎城"}
    elseif sj[1].颜色 =="红色" and sj[2].颜色 =="黑色" and sj[3].颜色 =="黄色" and sj[4].颜色 =="蓝色" and sj[5].颜色 == "绿色" then
      返回数据={组合="风行九黎符石",部位=6,门派="九黎城"}


    end
  end
  if 返回数据~=nil and 返回数据.组合~=nil then
    返回数据.等级=level
  elseif 返回数据==nil then
    if level~=1 then
      return 取星位组合(sj,level-1,true)
    end
  end
  return 返回数据
end

function 取动物套加成(名称,等级)
  local 动物套数据 = {}
  --力量套
  动物套数据["强盗"] = {类型="力量",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["海毛虫"] = {类型="力量",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["蛤蟆精"] = {类型="力量",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["老虎"] = {类型="力量",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["马面"] = {类型="力量",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["天将"] = {类型="力量",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["地狱战神"] = {类型="力量",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["巡游天神"] = {类型="力量",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["鬼将"] = {类型="力量",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["夜罗刹"] = {类型="力量",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["噬天虎"] = {类型="力量",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["狂豹人形"] = {类型="力量",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["巨力神猿"] = {类型="力量",属性=math.floor(等级/3),件数={5,15}}
  动物套数据["修罗傀儡鬼"] = {类型="力量",属性=math.floor(等级/3),件数={5,15}}
  --体质套
  动物套数据["大海龟"] = {类型="体质",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["树怪"] = {类型="体质",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["山贼"] = {类型="体质",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["野猪"] = {类型="体质",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["黑熊"] = {类型="体质",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["野鬼"] = {类型="体质",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["龟丞相"] = {类型="体质",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["黑熊精"] = {类型="体质",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["僵尸"] = {类型="体质",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["白熊"] = {类型="体质",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["黑山老妖"] = {类型="体质",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["大力金刚"] = {类型="体质",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["踏云兽"] = {类型="体质",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["机关兽"] = {类型="体质",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["金身罗汉"] = {类型="体质",属性=math.floor(等级/3),件数={5,15}}
  动物套数据["蔓藤妖花"] = {类型="体质",属性=math.floor(等级/3),件数={5,15}}
  --魔力套
  动物套数据["巨蛙"] = {类型="魔力",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["花妖"] = {类型="魔力",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["小龙女"] = {类型="魔力",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["蜘蛛精"] = {类型="魔力",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["蝴蝶仙子"] = {类型="魔力",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["古代瑞兽"] = {类型="魔力",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["蛟龙"] = {类型="魔力",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["雨师"] = {类型="魔力",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["如意仙子"] = {类型="魔力",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["星灵仙子"] = {类型="魔力",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["净瓶女娲"] = {类型="魔力",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["灵符女娲"] = {类型="魔力",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["灵鹤"] = {类型="魔力",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["炎魔神"] = {类型="魔力",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["葫芦宝贝"] = {类型="魔力",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["混沌兽"] = {类型="魔力",属性=math.floor(等级/3),件数={5,15}}
  动物套数据["长眉灵猴"] = {类型="魔力",属性=math.floor(等级/3),件数={5,15}}
  动物套数据["蜃气妖"] = {类型="魔力",属性=math.floor(等级/3),件数={5,15}}
  --耐力套
  动物套数据["护卫"] = {类型="耐力",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["羊头怪"] = {类型="耐力",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["牛妖"] = {类型="耐力",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["蟹将"] = {类型="耐力",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["牛头"] = {类型="耐力",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["天兵"] = {类型="耐力",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["芙蓉仙子"] = {类型="耐力",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["律法女娲"] = {类型="耐力",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["幽萤娃娃"] = {类型="耐力",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["红萼仙子"] = {类型="耐力",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["龙龟"] = {类型="耐力",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["连弩车"] = {类型="耐力",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["蝎子精"] = {类型="耐力",属性=math.floor(等级/3),件数={5,15}}
  动物套数据["曼珠沙华"] = {类型="耐力",属性=math.floor(等级/3),件数={5,15}}
  --敏捷套
  动物套数据["赌徒"] = {类型="敏捷",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["大蝙蝠"] = {类型="敏捷",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["骷髅怪"] = {类型="敏捷",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["狐狸精"] = {类型="敏捷",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["狼"] = {类型="敏捷",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["虾兵"] = {类型="敏捷",属性=math.floor(等级/6),件数={5,10}}
  动物套数据["兔子怪"] = {类型="敏捷",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["雷鸟人"] = {类型="敏捷",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["风伯"] = {类型="敏捷",属性=math.floor(等级/5),件数={10,15}}
  动物套数据["凤凰"] = {类型="敏捷",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["幽灵"] = {类型="敏捷",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["吸血鬼"] = {类型="敏捷",属性=math.floor(等级/4),件数={10,15}}
  动物套数据["画魂"] = {类型="敏捷",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["雾中仙"] = {类型="敏捷",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["机关鸟"] = {类型="敏捷",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["巴蛇"] = {类型="敏捷",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["猫灵人形"] = {类型="敏捷",属性=math.floor(等级/4),件数={15,25}}
  动物套数据["修罗傀儡妖"] = {类型="敏捷",属性=math.floor(等级/3),件数={5,15}}
  return 动物套数据[名称]
end
