
-- __UI弹出["提示框"] = __UI界面["创建提示控件"](__UI界面, "提示框", math.floor(引擎["宽度"] / 2)-110, 50, 470, 200)--math.floor(引擎["高度"] / 2)-100)
-- local 提示框 = __UI弹出["提示框"]
-- function 提示框:初始化()
--   self.文本 = {}
-- end
-- local remove = table.remove
-- local insert = table.insert
-- local FDGFFHA={}
-- function 提示框:更新()
--   if self.关闭时间 and self.关闭时间 <= os.time() then
--     self.关闭时间 = nil
--     FDGFFHA={}
--     self:置可见(false,false)
--   end
-- end


-- function 提示框:打开(txt)
--  -- print(#FDGFFHA)
--   if #FDGFFHA > 4 then
-- 		remove(FDGFFHA, 1)
-- 	end
--   insert(FDGFFHA,txt)
--   self.提示列表:重置()
--   self:置可见(true, true)
--   -- self.提示文本["清空"](self.提示文本)
--   -- -- self.清空()
--   -- local w, h = self.提示文本["置文本"](self.提示文本, 数据)
--   -- -- print(w,h)
--   self.关闭时间 = os.time() + 5
--   -- self:置可见(true, true)
--   -- self:置精灵(取提示背景(0,0,w + 20, h + 10))--__res:getPNGCC(2, 230, 965, 402, 51):拉伸(w + 20, h + 10):到精灵()) 取提示背景(x,y,w,h,lx)
--   -- -- self:置中心(0, 0)
--   -- self:置宽高(w + 20, h + 10)
-- end
-- -- local 提示文本 = 提示框["创建文本"](提示框, "提示文本", 10, 5, 390, 42)
-- -- function 提示文本:初始化()
-- --   self:置文字(字体16)
-- -- end

-- local 提示列表 = 提示框["创建列表"](提示框, "提示列表", 0, 0, 470, 200)--(显示x，显示y)
-- function 提示列表:初始化()
--     self:置文字(字体16)
--     self.行高度 = 56
--     self.选中精灵 = nil
--     self.焦点精灵 = nil
--     self.行间距 = 1
-- end
-- function 提示列表:重置()
--   self.清空(self)
--   for i, v in ipairs(FDGFFHA) do
--       local r = self:添加(i)
--       local 文本 = r["创建我的文本"](r, "提示列表", 10, 5, 390, 42)
--       local w, h =文本["置文本"](文本, "#Y" .. v)
--      -- print(h)
--       -- r:置精灵(取提示背景(0,0,w + 20, h + 10))
--       r:置精灵(取提示背景(0,0,w + 20, h + 10))
--       r["置可见"](r, true, true)
--       r:置宽高(w + 20, h + 10+5)
--   end
-- end








--创建界面 --创建弹出控件  创建提示控件
__UI弹出["提示框"] = __UI界面["创建提示控件"](__UI界面, "提示框", math.floor(引擎["宽度"] / 2)-130, 50, 470, 200)--math.floor(引擎["高度"] / 2)-100)
local 提示框 = __UI弹出["提示框"]

function 提示框:初始化()
  self.文本 = {}
end
local remove = table.remove
local insert = table.insert
local FDGFFHA={}
function 提示框:更新()
  if self.关闭时间 and self.关闭时间 <= os.time() then
    self.关闭时间 = nil
    FDGFFHA={}
    self:置可见()
  end
end

function 提示框:显示(x,y)
  if self.图像1 then
      self.图像1:显示(x,y+(1-1)*50)
  end
  if self.图像2 then
      self.图像2:显示(x,y+(2-1)*50)
  end
  if self.图像3 then
      self.图像3:显示(x,y+(3-1)*50)
  end
  if self.图像4 then
    self.图像4:显示(x,y+(4-1)*50)
  end
end

function 提示框:打开(txt)
  if #FDGFFHA >= 4 then
		remove(FDGFFHA, 1)
	end
  insert(FDGFFHA,txt)
  self:置可见(true, true)
  for i=1,4 do
    self["提示文本"..i]:清空()
    self["图像"..i]=nil
    if FDGFFHA[i] then
      local w, h = self["提示文本"..i]:置文本(FDGFFHA[i])
      self["提示文本"..i]:置坐标(10,(i-1)*50+5)
      local nsf = require("SDL.图像")(470, 42)
      if nsf["渲染开始"](nsf) then
        取提示背景(0,0,w + 20, h + 10,true):显示(0,0)
      end
      self["图像"..i] = nsf["到精灵"](nsf)
    end
  end
  self.关闭时间 = os.time() + 5
end
local 提示文本1 = 提示框["创建文本"](提示框, "提示文本1", 10, 5, 390, 42)
function 提示文本1:初始化()
  self:置文字(字体16)
end
local 提示文本2 = 提示框["创建文本"](提示框, "提示文本2", 10, 5, 390, 42)
function 提示文本2:初始化()
  self:置文字(字体16)
end
local 提示文本3 = 提示框["创建文本"](提示框, "提示文本3", 10, 5, 390, 42)
function 提示文本3:初始化()
  self:置文字(字体16)
end
local 提示文本4 = 提示框["创建文本"](提示框, "提示文本4", 10, 5, 390, 42)
function 提示文本4:初始化()
  self:置文字(字体16)
end

