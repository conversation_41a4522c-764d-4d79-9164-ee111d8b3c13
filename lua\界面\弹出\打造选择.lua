__UI弹出["打造选择"] = __UI界面["创建弹出窗口"](__UI界面, "打造选择", 194 + abbr.py.x, 174 + abbr.py.y, 254, 310)
local 打造选择 = __UI弹出["打造选择"]
function 打造选择:初始化()
  local nsf = require("SDL.图像")(254, 310)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 254, 310, true)["显示"](取黑透明背景(0, 0, 254, 310, true), 0, 0)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 打造选择:打开(data)
  self:置可见(true)
  self.数据 = data
  self.打造列表["重置"](self.打造列表, data)
end
local 打造列表 = 打造选择["创建列表"](打造选择, "打造列表", 10, 10, 234, 310)
function 打造列表:初始化()
  self:置文字(字体20)
  self.行高度 = 50
  self.行间距 = 0
end
function 打造列表:重置(data)
  self.清空(self)
  for _, v in ipairs(data) do
    local nsf = require("SDL.图像")(234, 310)
    if nsf["渲染开始"](nsf) then
      if 1 == _ % 2 then
      end
      local tsf = 字体20["置颜色"](字体20, __取颜色("白色"))["取图像"](字体20["置颜色"](字体20, __取颜色("白色")), v)
      tsf["显示"](tsf, 116 - tsf["宽度"] / 2, 18)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 打造列表:左键弹起(x, y, i, item, msg)
  __UI界面["窗口层"]["打造"]["请选择"]["我的按钮置文字"](__UI界面["窗口层"]["打造"]["请选择"], __UI界面["窗口层"]["打造"]["请选择"], __res:getPNGCC(3, 378, 347, 143, 37, true)["拉伸"](__res:getPNGCC(3, 378, 347, 143, 37, true), 233, 37), 打造选择["数据"][i])
  __UI界面["窗口层"]["打造"]["分类"] = 打造选择["数据"][i]
  __UI界面["窗口层"]["打造"]["刷新"](__UI界面["窗口层"]["打造"], __UI界面["窗口层"]["打造"]["数据"])
  
  打造选择["置可见"](打造选择, false)
end
