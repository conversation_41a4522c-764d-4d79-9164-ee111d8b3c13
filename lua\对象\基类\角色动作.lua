local 角色动作 = class("角色动作")
local ggf = require("GGE.函数")
local SDL = require("SDL")
function 角色动作:初始化(t)
    self.模型 = t.模型
    self.锦衣 = t.锦衣 or {}
    self.变身数据 = t.变身数据
    self.坐骑 = t.坐骑
    self.装备={}
    if t.装备 then
        self.装备=t.装备
    elseif t.武器 and t.武器.名称 then
        self.装备[3]=t.武器
    end

    if t.副武器 and t.副武器.名称 then
        self.装备[4]=t.副武器
    end

    if t.染色方案 and t.染色方案~=0 and t.染色组 and t.染色组~=0 then
        self.染色组 = t.染色组
        self.染色方案 = t.染色方案
    end
    if t.地图数据 and t.地图数据.方向 then
        self.方向 = t.地图数据.方向 + 1
    elseif t.方向 then
        self.方向 = t.方向 + 1
    else
        self.方向 = 1
    end
    self.离线摆摊=t.离线摆摊
   
    self.动作 = "静立"
    self.cur_action = {}
    self.模型编号 = {}
    self:置模型()
    self.影子 = __res:取资源动画('dlzy',0xDCE4B562,"精灵")
    self.飞行偏移 = {x=0,y=0}
    self.飞行计数 = 0
    self.飞行 = false
    if t.飞行 then
        self.飞行 =t.飞行
    end
end



function 角色动作:置模型()
    self.方向=1
    self:置模型显示()
end


function 角色动作:置模型显示()
        self.人物={}
        self.坐骑显示={}
        self.武器显示={}
        self.副武器={}
        self.光环={}
        self.脚印={}
        if self.离线摆摊 then
            local 资源 = 取模型("小孩_飞儿")
            self.人物.静立 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
            self.人物.行走 = __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
            self.人物.静立:置提速(0.25)
            self.人物.行走:置提速(0.25)
            self.染色方案 = nil
            self.染色组 = nil

        elseif self.变身数据~=nil and  __res.配置.变身造型~=1 then
                local 资源 = 取模型(self.变身数据)
                self.人物.静立 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
                self.人物.行走 = __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
                self.染色方案 = nil
                self.染色组 = nil
                if self.变身数据=="进阶古代瑞兽" or self.变身数据=="进阶雷鸟人" or self.变身数据=="进阶蝴蝶仙子" or self.变身数据=="进阶白熊"
                or self.变身数据=="进阶黑山老妖" or self.变身数据=="进阶天兵" or self.变身数据=="进阶天将" or self.变身数据=="进阶地狱战神"
                or self.变身数据=="进阶风伯" or self.变身数据=="进阶凤凰" or self.变身数据=="进阶碧水夜叉" or self.变身数据=="进阶雨师" then
                    local zl = 取战斗模型(self.变身数据.."_饰品")  ---这里显示宠物饰品的
                    self.武器显示.静立  = __res:取资源动画(zl[10],zl[6],"置动画"):置循环(true)
                    self.武器显示.行走 = __res:取资源动画(zl[10],zl[8],"置动画"):置循环(true)
                    self.武器显示.行走:置提速(1.5)
                end
                if self.变异 and __染色信息[self.变身数据]~=nil then
                    self.染色方案 = __染色信息[self.变身数据].id
                    self.染色组 = __染色信息[self.变身数据].方案
                end
                self.人物.行走:置提速(1.5)
        elseif self.坐骑 and self.坐骑.模型  and (not self.锦衣 or not self.锦衣[1] or not self.锦衣[1].名称 or  __res.配置.锦衣效果==1) and __res.配置.坐骑显示==1 then
                local 资源组 = {}
                if 新增坐骑(self.模型,self.坐骑.模型,"站立") ~= nil and 新增坐骑(self.模型,self.坐骑.模型,"站立") ~= ""  then
                    资源组.人物资源 = "jszy/xzzq"
                    资源组.人物站立 = 新增坐骑(self.模型,self.坐骑.模型,"站立")
                    资源组.人物行走 = 新增坐骑(self.模型,self.坐骑.模型,"奔跑")
                    资源组.坐骑资源 = "jszy/xzzq"
                    资源组.坐骑行走 = 新增坐骑(self.模型,self.坐骑.模型,"奔跑")
                    资源组.坐骑站立 = 新增坐骑(self.模型,self.坐骑.模型,"站立")
                else
                    资源组 = 坐骑库(self.模型,self.坐骑.模型,self.坐骑.饰品 or "空")
                end
                if 资源组 then
                    self.坐骑显示.静立 = __res:取资源动画(资源组.坐骑资源,资源组.坐骑站立,"置动画"):置循环(true)
                    self.坐骑显示.行走 = __res:取资源动画(资源组.坐骑资源,资源组.坐骑行走,"置动画"):置循环(true)
                    if 资源组.坐骑饰品站立 ~= nil then
                        self.武器显示.静立 = __res:取资源动画(资源组.坐骑饰品资源,资源组.坐骑饰品站立,"置动画"):置循环(true)
                        self.武器显示.行走 = __res:取资源动画(资源组.坐骑饰品资源,资源组.坐骑饰品站立,"置动画"):置循环(true)
                        self.武器显示.行走:置提速(1.5)
                    end
                    self.人物.静立 = __res:取资源动画(资源组.人物资源,资源组.人物站立,"置动画"):置循环(true)
                    self.人物.行走 = __res:取资源动画(资源组.人物资源,资源组.人物行走,"置动画"):置循环(true)
                    local zqx, zqy = 坐骑补差(self.坐骑.模型, self.模型)
                    self.人物.静立.pyx, self.人物.静立.pyy = zqx, zqy
                    self.人物.行走.pyx, self.人物.行走.pyy = zqx, zqy
                    self.染色方案=nil
                    self.染色组=nil
                    self.人物.行走:置提速(1.5)
                    self.坐骑显示.行走:置提速(1.5)
                else
                    local 资源 = 取模型(self.模型)
                    self.人物.静立 =  __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
                    self.人物.行走 =  __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
                    self.人物.行走:置提速(1.5)
                end 
        else
                local 资源 = 取模型(self.模型)
                local  m
                if self.装备 and self.装备[3] ~= nil then
                    m= _tp:取武器子类(self.装备[3].子类)
                    if self.装备[3].名称 == "龙鸣寒水" or self.装备[3].名称 == "非攻" then
                        m = "弓弩1"
                    end
                    资源 = 取模型(self.模型, m)
                end
                local 是否显示武器 = false
                if self.锦衣[1] ~= nil and self.锦衣[1].名称 ~= nil and __res.配置.锦衣效果~=1 then
                        local 锦衣名称 = self.锦衣[1].名称
                        if 锦衣名称=="青春" or 锦衣名称=="素颜" or 锦衣名称=="绝色" or 锦衣名称=="春秋" or  锦衣名称=="夏蚕"
                        or 锦衣名称=="星河" or 锦衣名称=="白峨" or 锦衣名称=="糖果" or 锦衣名称=="青涩" or 锦衣名称=="傲然"
                        or 锦衣名称=="牛仔" or  锦衣名称=="试剑" or 锦衣名称=="骨龙战骑" or 锦衣名称=="水嘟嘟·钻白"or 锦衣名称=="斗战神"
                        or 锦衣名称=="斗战胜佛" or  锦衣名称=="八部天龙马·玄" or  锦衣名称=="龙凰·桃" or  锦衣名称=="龙凰·皑"   then
                            资源 = 取战斗锦衣素材(self.锦衣[1].名称,self.模型)
                            self.人物.静立 =  __res:取资源动画(资源[5],资源[3],"置动画"):置循环(true)
                            self.人物.行走 =  __res:取资源动画(资源[5],资源[4],"置动画"):置循环(true)
                            是否显示武器 = true
                        elseif 新加战斗锦衣[锦衣名称]~=nil  then
                                资源 = 取武器锦衣素材(self.锦衣[1].名称,self.模型,m)
                                self.人物.静立 =  __res:取资源动画(资源[5],资源[3],"置动画"):置循环(true)
                                self.人物.行走 =  __res:取资源动画(资源[5],资源[4],"置动画"):置循环(true)
                                是否显示武器 = false
                        end
                        self.染色方案=nil
                        self.染色组=nil
                else
                    self.人物.静立 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
                    self.人物.行走 = __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
                end
                self.人物.行走:置提速(1.5)

                if self.装备 and self.装备[3] and self.装备[3].名称 then
                        local ms = _tp:取武器附加名称(self.装备[3].子类, self.装备[3].级别限制,self.装备[3].名称)
                        资源 = 取模型(ms .. "_" .. self.模型)
                        self.武器显示.静立 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
                        self.武器显示.行走 = __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
                        if self.装备[3].染色方案~=nil and self.装备[3].染色方案~=0 and self.装备[3].染色组~=nil and self.装备[3].染色组~=0 and #self.装备[3].染色组>0 then
                            local 调色板  = __dewpal(self.装备[3].染色方案)
                            self.武器显示.静立:调色(调色板,取调色数据(self.装备[3].染色组))
                            self.武器显示.行走:调色(调色板,取调色数据(self.装备[3].染色组))
                        end
                        self.武器显示.行走:置提速(1.5)
                        if 是否显示武器 and __res.配置.锦衣效果~=1 then
                            self.武器显示 = {}
                        end
                end

                if self.装备 and self.装备[4]~=nil and self.模型=="影精灵" and string.find(self.装备[4].名称,"(坤)") and (not self.装备[3] or string.find(self.装备[3].名称,"(乾)")) then
                    资源 = 取模型(self.装备[4].名称 .. "_" .. self.模型)
                    self.副武器.静立 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
                    self.副武器.行走 = __res:取资源动画(资源[3],资源[2],"置动画"):置循环(true)
                    if self.装备[4].染色方案~=nil and self.装备[4].染色方案~=0 and self.装备[4].染色组~=nil and self.装备[4].染色组~=0 and #self.装备[4].染色组>0 then
                        local 调色板  = __dewpal(self.装备[4].染色方案)
                        self.副武器.静立:调色(调色板,取调色数据(self.装备[4].染色组))
                        self.副武器.行走:调色(调色板,取调色数据(self.装备[4].染色组))
                    end
                    self.副武器.行走:置提速(1.5)
                    if 是否显示武器 and __res.配置.锦衣效果~=1 then
                        self.副武器 = {}
                    end
                end


                if self.锦衣[2] ~= nil and self.锦衣[2].名称 ~= nil and  __res.配置.光环足迹~=1  then
                    local n = 取光环(self.锦衣[2].名称)
                    self.光环.静立 = __res:取资源动画(n[4],n[1],"置动画"):置循环(true)
                    self.光环.行走 =  __res:取资源动画(n[4],n[2],"置动画"):置循环(true)
                    self.光环.行走:置提速(1.5)
                end
                if self.锦衣[3] ~= nil and self.锦衣[3].名称 ~= nil and  __res.配置.光环足迹~=1 then
                        local n = 取足迹(self.锦衣[3].名称)
                        self.脚印.行走=__res:取资源动画(n[4],n[1],"置动画"):置循环(true)
                        self.脚印.行走:置提速(1.5)
                end
        end




        if self.染色方案~=nil and self.染色方案~=0 and self.染色组~=nil and #self.染色组>0 then
            local 调色板  = __dewpal(self.染色方案)
            self.人物.静立:调色(调色板,取调色数据(self.染色组))
            self.人物.行走:调色(调色板,取调色数据(self.染色组))
        end

        -- self:置调色()
        self:置动作(self.动作)
        self:置方向(self.方向)
end

-- function 角色动作:置染色(编号,染色组)
--       if 编号~=nil  and 编号~=0 and 染色组~=nil and #染色组>0 then
--         local 调色板  = __dewpal(编号)
--         self.人物.静立:调色(调色板,取调色数据(染色组))
--         self.人物.行走:调色(调色板,取调色数据(染色组))      
--     end
-- end






function 角色动作:置动作(v)
    self.cur_action = {}
    self.动作 = v
    local 加入数据 = ggf.insert(self.cur_action)

    if self.脚印[v] then
        加入数据(self.脚印[v])
    end
    if self.光环[v] then
        加入数据(self.光环[v])
    end
    if self.坐骑显示[v] then
        加入数据(self.坐骑显示[v])
    end
    if self.人物[v] then
        加入数据(self.人物[v])
    end
    if self.武器显示[v] then
        加入数据(self.武器显示[v])
    end
    if self.副武器[v] then
        加入数据(self.副武器[v])
    end
  

end

local 置当前 = function(self, k, ...)
    if self.cur_action then
        self.cur_action[k](self.cur_action, ...)
    end
end
local 置所有 = function(self, k, ...)
    for _, v in pairs(self.坐骑显示) do
        v[k](v, ...)
    end
    for _, v in pairs(self.人物) do
        v[k](v, ...)
    end
    for _, v in pairs(self.武器显示) do
        v[k](v, ...)
    end
    for _, v in pairs(self.副武器) do
        v[k](v, ...)
    end
    for _, v in pairs(self.光环) do
        v[k](v, ...)
    end
    for _, v in pairs(self.脚印) do
        v[k](v, ...)
    end
 
    return self
end
function 角色动作:置方向(v)
    self.方向 = v
    置所有(self, "置方向", v)
end

function 角色动作:取方向()
    return self.方向
end

function 角色动作:取高亮()
    return self.cur_action and self.cur_action:取高亮()
end

function 角色动作:置高亮(...)
    置所有(self, "置高亮", ...)
    return self
end

function 角色动作:置颜色(...)
    置所有(self, "置颜色", ...)
    return self
end

function 角色动作:帧同步()
    置当前(self, "置首帧")
    置当前(self, "播放")
    return self
end

function 角色动作:播放()
    置当前(self, "播放")
    return self
end

function 角色动作:置首帧()
    置当前(self, "置首帧")
    return self
end

function 角色动作:置尾帧()
    置当前(self, "置尾帧")
    return self
end

function 角色动作:置循环(...)
    置当前(self, "置循环", ...)
    return self
end

function 角色动作:更新(dt)
      if ggetype(self)=="主角" or (__res.配置.显示玩家~=1 and __主显.主角.xy:取距离(self.xy) < 800) then
          if self.飞行 then
                  if  self.飞行计数 < 150 then
                      self.飞行计数 = self.飞行计数 + 1
                      self.飞行偏移.y = self.飞行偏移.y - (self.飞行计数/8000)*80
                      if self.飞行偏移.y < -100 then
                          self.飞行偏移.y = -100
                          self.飞行计数 = 0
                      end
                  end
            else
                  if  self.飞行计数 < 150 then
                          self.飞行计数 = self.飞行计数 + 1
                          self.飞行偏移.y = self.飞行偏移.y + (self.飞行计数/8000)*80
                          if self.飞行偏移.y > 0 then
                              self.飞行偏移.y = 0
                              self.飞行计数 = 0
                          end
                      end
            end
            for _, v in ipairs(self.cur_action) do
                v.pyx=self.飞行偏移.x
                v.pyy=self.飞行偏移.y
                v:更新(dt, x, y)
            end
        end
  
end

function 角色动作:显示(x, y)
    if ggetype(self)=="主角" or (__res.配置.显示玩家~=1 and __主显.主角.xy:取距离(self.xy) < 800) then
        self.影子:显示(x, y)
        for _, v in ipairs(self.cur_action) do
            v:显示(x, y)
        end
    end
end

function 角色动作:检查点(x, y)
    for _, v in ipairs(self.cur_action) do
        if v:检查点(x, y) then
            return true
        end
    end
end

function 角色动作:检查透明(x, y)
    local r = false
    for _, v in ipairs(self.cur_action) do
       r = r or v:检查透明(x, y)
    end
    return r
end

function 角色动作:消息事件(t)
        if not __手机 and t.鼠标 then
            for _, v in ipairs(t.鼠标) do
                if self:检查透明(v.x, v.y)  then
                    if v.button == SDL.BUTTON_LEFT and v.type == SDL.MOUSE_DOWN and __主显 and __主显.主角  then
                      __主显.主角.按下=false
                      __主显.主角.点击移动=nil
                    elseif not v.button and  v.type == SDL.MOUSE_MOTION  then
                          self:置高亮(true)
                    end
                else
                    self:置高亮(false)
                end
            end
        end
end









return 角色动作
