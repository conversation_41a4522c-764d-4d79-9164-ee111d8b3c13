--[[
更新状态管理器
功能: 管理更新过程的各种状态，状态切换，进度跟踪，错误记录
作者: AI Assistant  
日期: 2025-06-23
版本: v1.0
--]]

local 更新状态 = class("更新状态管理器")

-- 状态常量定义
更新状态.状态类型 = {
    未初始化 = "未初始化",
    就绪 = "就绪", 
    检查版本 = "检查版本",
    无需更新 = "无需更新",
    准备下载 = "准备下载",
    正在下载 = "正在下载",
    校验文件 = "校验文件",
    安装更新 = "安装更新",
    更新完成 = "更新完成",
    更新失败 = "更新失败",
    已取消 = "已取消"
}

-- 状态转换规则
更新状态.状态转换 = {
    [更新状态.状态类型.未初始化] = {
        更新状态.状态类型.就绪
    },
    [更新状态.状态类型.就绪] = {
        更新状态.状态类型.检查版本
    },
    [更新状态.状态类型.检查版本] = {
        更新状态.状态类型.无需更新,
        更新状态.状态类型.准备下载,
        更新状态.状态类型.更新失败
    },
    [更新状态.状态类型.准备下载] = {
        更新状态.状态类型.正在下载,
        更新状态.状态类型.更新失败,
        更新状态.状态类型.已取消
    },
    [更新状态.状态类型.正在下载] = {
        更新状态.状态类型.校验文件,
        更新状态.状态类型.更新失败,
        更新状态.状态类型.已取消
    },
    [更新状态.状态类型.校验文件] = {
        更新状态.状态类型.安装更新,
        更新状态.状态类型.更新失败
    },
    [更新状态.状态类型.安装更新] = {
        更新状态.状态类型.更新完成,
        更新状态.状态类型.更新失败
    }
}

function 更新状态:构造函数()
    self.当前状态 = self.状态类型.未初始化
    self.状态历史 = {}
    self.状态时间戳 = {}
    self.状态数据 = {}
    self.状态监听器 = {}
    self.错误信息 = {}
    
    -- 进度跟踪
    self.进度信息 = {
        当前步骤 = "",
        已完成步骤 = 0,
        总步骤数 = 0,
        百分比 = 0,
        详细信息 = ""
    }
    
    self:初始化()
end

-- 初始化状态管理器
function 更新状态:初始化()
    self:记录状态(self.状态类型.未初始化, "状态管理器初始化")
    self:切换状态(self.状态类型.就绪)
end

-- 切换状态
function 更新状态:切换状态(新状态, 状态数据)
    if not self:验证状态转换(self.当前状态, 新状态) then
        local 错误信息 = string.format("非法状态转换: %s -> %s", self.当前状态, 新状态)
        print("更新状态错误:", 错误信息)
        return false
    end
    
    local 旧状态 = self.当前状态
    self.当前状态 = 新状态
    
    -- 记录状态变化
    self:记录状态(新状态, 状态数据)
    
    -- 通知监听器
    self:通知状态变化(旧状态, 新状态, 状态数据)
    
    print("更新状态:", 旧状态, "->", 新状态)
    return true
end

-- 验证状态转换是否合法
function 更新状态:验证状态转换(当前状态, 目标状态)
    local 允许转换 = self.状态转换[当前状态]
    if not 允许转换 then
        return false
    end
    
    for _, 允许状态 in ipairs(允许转换) do
        if 允许状态 == 目标状态 then
            return true
        end
    end
    
    return false
end

-- 记录状态
function 更新状态:记录状态(状态, 数据)
    local 时间戳 = os.time()
    
    table.insert(self.状态历史, 状态)
    self.状态时间戳[状态] = 时间戳
    
    if 数据 then
        self.状态数据[状态] = 数据
    end
    
    -- 限制历史记录长度
    if #self.状态历史 > 50 then
        table.remove(self.状态历史, 1)
    end
end

-- 获取当前状态
function 更新状态:获取当前状态()
    return self.当前状态
end

-- 获取状态历史
function 更新状态:获取状态历史()
    return self.状态历史
end

-- 获取状态数据
function 更新状态:获取状态数据(状态)
    状态 = 状态 or self.当前状态
    return self.状态数据[状态]
end

-- 设置进度信息
function 更新状态:设置进度(当前步骤, 已完成, 总数, 详细信息)
    self.进度信息.当前步骤 = 当前步骤 or ""
    self.进度信息.已完成步骤 = 已完成 or 0
    self.进度信息.总步骤数 = 总数 or 0
    self.进度信息.详细信息 = 详细信息 or ""
    
    if 总数 > 0 then
        self.进度信息.百分比 = math.floor((已完成 / 总数) * 100)
    else
        self.进度信息.百分比 = 0
    end
    
    -- 通知进度更新
    self:通知进度更新()
end

-- 获取进度信息
function 更新状态:获取进度()
    return self.进度信息
end

-- 添加状态监听器
function 更新状态:添加监听器(监听器)
    table.insert(self.状态监听器, 监听器)
end

-- 移除状态监听器
function 更新状态:移除监听器(监听器)
    for i, v in ipairs(self.状态监听器) do
        if v == 监听器 then
            table.remove(self.状态监听器, i)
            break
        end
    end
end

-- 通知状态变化
function 更新状态:通知状态变化(旧状态, 新状态, 数据)
    for _, 监听器 in ipairs(self.状态监听器) do
        if 监听器.状态变化 then
            监听器:状态变化(旧状态, 新状态, 数据)
        end
    end
end

-- 通知进度更新
function 更新状态:通知进度更新()
    for _, 监听器 in ipairs(self.状态监听器) do
        if 监听器.进度更新 then
            监听器:进度更新(self.进度信息)
        end
    end
end

-- 记录错误
function 更新状态:记录错误(错误代码, 错误信息, 详细信息)
    local 错误记录 = {
        代码 = 错误代码,
        信息 = 错误信息,
        详细信息 = 详细信息,
        时间戳 = os.time(),
        状态 = self.当前状态
    }
    
    table.insert(self.错误信息, 错误记录)
    
    -- 限制错误记录长度
    if #self.错误信息 > 20 then
        table.remove(self.错误信息, 1)
    end
    
    print("更新错误:", 错误代码, 错误信息)
end

-- 获取错误信息
function 更新状态:获取错误信息()
    return self.错误信息
end

-- 获取最新错误
function 更新状态:获取最新错误()
    return self.错误信息[#self.错误信息]
end

-- 清除错误信息
function 更新状态:清除错误()
    self.错误信息 = {}
end

-- 重置状态
function 更新状态:重置()
    self.当前状态 = self.状态类型.就绪
    self.状态历史 = {}
    self.状态时间戳 = {}
    self.状态数据 = {}
    self.错误信息 = {}
    
    self.进度信息 = {
        当前步骤 = "",
        已完成步骤 = 0,
        总步骤数 = 0,
        百分比 = 0,
        详细信息 = ""
    }
    
    print("更新状态: 状态已重置")
end

-- 是否处于活动状态
function 更新状态:是否活动()
    return self.当前状态 == self.状态类型.检查版本 or
           self.当前状态 == self.状态类型.准备下载 or
           self.当前状态 == self.状态类型.正在下载 or
           self.当前状态 == self.状态类型.校验文件 or
           self.当前状态 == self.状态类型.安装更新
end

-- 是否可以取消
function 更新状态:可以取消()
    return self.当前状态 == self.状态类型.检查版本 or
           self.当前状态 == self.状态类型.准备下载 or
           self.当前状态 == self.状态类型.正在下载
end

-- 是否完成
function 更新状态:是否完成()
    return self.当前状态 == self.状态类型.更新完成 or
           self.当前状态 == self.状态类型.无需更新 or
           self.当前状态 == self.状态类型.更新失败 or
           self.当前状态 == self.状态类型.已取消
end

-- 获取状态描述
function 更新状态:获取状态描述()
    local 描述表 = {
        [self.状态类型.未初始化] = "正在初始化...",
        [self.状态类型.就绪] = "准备就绪",
        [self.状态类型.检查版本] = "正在检查版本...",
        [self.状态类型.无需更新] = "版本已是最新",
        [self.状态类型.准备下载] = "准备下载更新...",
        [self.状态类型.正在下载] = "正在下载更新文件...",
        [self.状态类型.校验文件] = "正在校验文件...",
        [self.状态类型.安装更新] = "正在安装更新...",
        [self.状态类型.更新完成] = "更新完成",
        [self.状态类型.更新失败] = "更新失败",
        [self.状态类型.已取消] = "更新已取消"
    }
    
    return 描述表[self.当前状态] or self.当前状态
end

-- 导出状态报告
function 更新状态:导出报告()
    local 报告 = {
        当前状态 = self.当前状态,
        状态描述 = self:获取状态描述(),
        状态历史 = self.状态历史,
        进度信息 = self.进度信息,
        错误信息 = self.错误信息,
        生成时间 = os.date("%Y-%m-%d %H:%M:%S")
    }
    
    return 报告
end

return 更新状态 