--[[
LastEditTime: 2024-10-09 20:44:15
--]]
--[[
LastEditTime: 2024-10-09 20:44:15
--]]
--[[
LastEditTime: 2024-05-06 22:06:31
--]]

local  大地图 = 窗口层:创建窗口("大地图",0, 0, 670, 510)
function  大地图:初始化()
  self:创建纹理精灵(function()
                       取黑色背景(0, 0, 670, 510, true):显示(0, 0)
                       __res:取资源动画('dlzy',0x93E77F54,"图像"):显示(15, 15)
                    end
              )
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true

    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
end

function  大地图:打开(传送) 
    if 窗口层.大地图a.是否可见 then
        窗口层.大地图a:置可见(false)
    end
    if 窗口层.大地图b.是否可见 then
        窗口层.大地图b:置可见(false)
    end
    if 窗口层.大地图c.是否可见 then
        窗口层.大地图c:置可见(false)
    end
    if 窗口层.大地图d.是否可见 then
        窗口层.大地图d:置可见(false)
    end
    if 窗口层.世界小地图.是否可见 then
        窗口层.世界小地图:置可见(false)
    end
    self:置可见(true)
    self.超级传送=nil
    if 传送 then
      self.超级传送=传送
    end
end





local 花果山 = 大地图:创建按钮("花果山", 513, 177)
function 花果山:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xCC1742A6), 1)
end
function 花果山:左键弹起(x, y)
    窗口层.大地图c:打开(大地图.超级传送)
    大地图:置可见(false)
end
local 长安 = 大地图:创建按钮("长安", 87+15, 103+15)
function 长安:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x7FFAB47A),1)
end
function 长安:左键弹起(x, y)
    窗口层.大地图b:打开(大地图.超级传送)
    大地图:置可见(false)
end
local 北俱芦洲 = 大地图:创建按钮("北俱芦洲", 270+15, 10+15)
function 北俱芦洲:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xEA1F50E2),1)
end
function 北俱芦洲:左键弹起(x, y)
    窗口层.大地图d:打开(大地图.超级传送)
    大地图:置可见(false)
end
local 龙宫 = 大地图:创建按钮("龙宫", 390+15, 376+15)
function 龙宫:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xCE11152D),1)
end
function 龙宫:左键弹起(x, y)
    窗口层.世界小地图:打开(1116,大地图.超级传送)
    大地图:置可见(false)
end

local 朱紫国 = 大地图:创建按钮("朱紫国", 0+15, 55+15)
function 朱紫国:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x0AF24A29),1)
end
function 朱紫国:左键弹起(x, y)
    窗口层.大地图a:打开(大地图.超级传送)
    大地图:置可见(false)
end

local 月宫 = 大地图:创建按钮( "月宫", 73+15, 4+15)
function 月宫:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xF63C410E),1)
end
function 月宫:左键弹起(x, y)

    窗口层.世界小地图:打开(1114,大地图.超级传送)
    大地图:置可见(false)
end
local 天宫 = 大地图:创建按钮( "天宫", 166+15, 23+15)
function 天宫:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x5ABFF907),1)
end
function 天宫:左键弹起(x, y)
  
    窗口层.世界小地图:打开(1111,大地图.超级传送)
    大地图:置可见(false)
end
local 蟠桃园 = 大地图:创建按钮("蟠桃园", 199+15, 14)
function 蟠桃园:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xF8A67A7A),1)
end
function 蟠桃园:左键弹起(x, y)
    窗口层.世界小地图:打开(1231,大地图.超级传送)
    大地图:置可见(false)
end
local 关闭 = 大地图:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  大地图:置可见(false)
end


