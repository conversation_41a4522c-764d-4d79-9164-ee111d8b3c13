--[[
LastEditTime: 2024-12-24 04:40:04
--]]

local 好友查看 = 窗口层:创建窗口("好友查看", 0, 0, 380, 360)
function 好友查看:初始化()


  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end
function 好友查看:打开(数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self:更新数据(数据)
  
end

function 好友查看:更新数据(内容)
  self.数据=nil
  if 内容 and 内容.名称 and 内容.数字ID then
    self.数据 = 内容
  end
  local 属性内容={"名称","数字ID","等级","门派","曾用名","帮派","称号","关系","好友度"}
  local 字体显示={"名  称","数字ID","等  级","门  派","曾用名","帮  派","称  谓","关  系","好友度"}
  self:创建纹理精灵(function()
        置窗口背景("好友属性", 0, 0, 380, 360, true):显示(0, 0)
        取白色背景(0, 0, 130, 115, true):显示(15, 32)
        取白色背景(0, 0, 130, 70, true):显示(15, 217)
        取输入背景(0, 0, 130, 22):显示(15,168)
        文本字体:置颜色(255,255,255,255)
        文本字体:取图像("好友备注"):显示(15, 150)
        文本字体:取图像("好友评价"):显示(15, 195)
        if self.数据 and self.数据.模型 then
            local lssj = 取头像(self.数据.模型)
          __res:取资源动画(lssj[7],lssj[3],"图像"):显示(15, 32)
        end
        for i=1,#字体显示 do
            文本字体:置颜色(255,255,255,255):取图像(字体显示[i]):显示(160, 8+i*29)
            取输入背景(0, 0, 150, 22):显示(210,5+i*29)
            if self.数据[属性内容[i]] then
              文本字体:置颜色(0,0,0,255):取图像(self.数据[属性内容[i]]):显示(215, 8+i*29)
            end
        end
    





  end
  )

end




local 类型设置 = {"断交","历史","分组","私聊","成就","组队","交易" ,"给予"}

for i, v in ipairs(类型设置) do
    local 临时函数 =好友查看:创建红色按钮(v,v,0,0,74,22)
    if i<=4 then
       临时函数:置坐标(15+(i-1)*89,300)
    else
       临时函数:置坐标(15+(i-5)*89,330)
    end
    function  临时函数:左键弹起(x, y)
            if v=="断交"  and 好友查看.数据 and 好友查看.数据.数字ID then
                请求服务(20,{id=好友查看.数据.数字ID})
            elseif  v=="分组" and 好友查看.数据 and 好友查看.数据.数字ID then
                请求服务(21,{id=好友查看.数据.数字ID})
            end
    end
end






local 关闭 = 好友查看:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  好友查看:置可见(false)
end

