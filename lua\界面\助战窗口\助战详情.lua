-- <AUTHOR> GGELUA
-- @Last Modified by    : baidwwy
-- @Date                : 2024-09-04 16:18:55
-- @Last Modified time  : 2024-10-02 19:55:41

local 助战详情 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "助战详情", 80+110 + abbr.py.x, 30 + abbr.py.y, 682+23, 430+57)
local format = string.format
local sdr1={"魔法","伤害","防御","速度","法伤","法防"}
local sdr2={"体质","魔力","力量","耐力","敏捷","潜力"}
local jd3= {"体质","魔力","力量","耐力","敏捷"}
local zuoce= {"气血","魔法","命中","伤害","防御","速度","法伤","法防"}
local 临时潜力=0
local 总潜力=0
local 临时加点 = {体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
local 临时属性 = {气血=0,魔法=0,命中=0,伤害=0,防御=0,速度=0,躲避=0,灵力=0,法伤=0,法防=0,体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
function 助战详情:初始化()
  local nsf = require("SDL.图像")(682+20, 430+57)
  local ggd1 = huang取输入背景(0, 0, 247, 23)
  local ggd2 = huang取输入背景(0, 0, 98, 23)
  local ggd3 = huang取输入背景(0, 0, 106, 23)
  
  if nsf["渲染开始"](nsf) then
    huang置窗口背景("小号配置", 0, 12, 612+4, 432+11, true):显示(0, 0)
    __res:getPNGCC(7, 0, 486, 282, 143):显示(11,28+18)
    __res:getPNGCC(7, 938, 112, 50, 50):显示(11+12,28+18+12) --rwtx
    __res:getPNGCC(7, 804, 0, 131, 396):显示(11+290,28+19)
    __res:getPNGCC(7, 639, 0, 165, 397):显示(11+290+140,28+19)
    __res:getPNGCC(1, 400, 108, 184, 18):显示(71-15-10, 287+134) --经验背景

    ggd1:显示(45,179+18)
    字体15:置颜色(159,33,51)
    字体15:取图像("气血"):显示(11, 179+18+3+0*27)
    for i=1,6 do
      ggd2:显示(45,179+i*27+18)
      ggd3:显示(185,179+i*27+18)
      字体15:取图像(sdr1[i]):显示(11, 179+18+3+i*27)
      字体15:取图像(sdr2[i]):显示(11+142, 179+18+3+i*27)
    end
    字体15:取图像("经验"):显示(11, 179+3+222+18)
    --经验背景

  end
  -- self.格子背景 = __res:getPNGCC(3, 694, 4, 338, 273)["到精灵"]((__res:getPNGCC(3, 694, 4, 338, 273)))
  self:置精灵(nsf["到精灵"](nsf))
end

function 助战详情:打开(内容)
  self.bb基础属性=内容.bb基础属性 or {}
  self.助战编号=内容.助战编号
  
  self:刷新属性(内容.角色)
  self:置可见(true)
  self:刷新灵饰(内容.灵饰)
  self:刷新装备(内容.装备)
  self:刷新bb()
end
function 助战详情:刷新bb()
  self["bb网格"]:置数据()
end
function 助战详情:刷新灵饰(内容)
  self.灵饰=内容
  self["灵饰网格"]:置物品(内容)
end
function 助战详情:刷新装备(内容)
  self.装备=内容
  self["装备网格"]:置物品(内容)
end

function 助战详情:刷新宝宝参战信息(xxwdw)
  self.bb基础属性=xxwdw
  self["bb网格"]:置数据()
end

function 助战详情:刷新属性(内容)
	临时潜力=内容.潜力
	总潜力=内容.潜力
	self.角色=内容
	self.角色.助战编号=self.助战编号
  self:重置1()
  if __UI界面["窗口层"].助战技能学习.是否可见 then
		__UI界面["窗口层"].助战技能学习:刷新技能(self.角色.师门技能)
	end
end

function 助战详情:重置1()
  local nsf = require("SDL.图像")(290, 424)
  if nsf["渲染开始"](nsf) then
    local lssj = 取头像(self.角色.模型)
    __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])):显示(11+12+2,28+18+12+3)
    字体15:置颜色(179,33,31)
    字体15:取图像(self.角色.名称):显示(29+56, 135+6+10-87)
    for i=1,6 do
      if sdr2[i]~="潜力" then
        字体15:取图像(self.角色[sdr2[i]]):显示(11+41+142,179+18+3+i*27)
      else
        字体15:取图像(临时潜力):显示(11+41+142,179+18+3+i*27)
      end
    end
    for i=2,6 do
      字体15:取图像(self.角色[sdr1[i]]):显示(11+41,179+18+3+i*27)
    end
    
    字体15:取图像(format("%d/%d/%d",self.角色.气血,self.角色.气血上限,self.角色.最大气血)):显示(11+41,179+18+3)
    字体15:取图像(format("%d/%d",self.角色.魔法,self.角色.最大魔法)):显示(11+41,179+18+3+1*27)
    --字体15:置颜色(-65536)
    -- if 临时属性.气血 and  临时属性.气血>0 then
    --   字体15:取图像(" +" ..(临时属性.气血 or "")):显示(self.x+15+43+105+26,self.y+129-18+3+42+1*27)
    -- end
    -- if 临时属性.魔法 and  临时属性.魔法>0 then
    --   字体15:取图像(" +" ..(临时属性.魔法 or "")):显示(self.x+11+118,self.y+129-18+3+42+2*27)
    -- end
    -- for i=4,#zuoce do
    --   if 临时属性[zuoce[i]] and  临时属性[zuoce[i]]>0 then
    --     字体15:取图像(" +" ..临时属性[zuoce[i]]):显示(self.x+11+100,self.y+129-18+3+42+(i-1)*27) --"命中","伤害","防御","速度","法伤","法防"
    --   end
    -- end
    -- if 临时加点.体质 and  临时加点.体质>0 then
    --   字体15:取图像(" +" ..(临时加点.体质 or "")):显示(self.x+217,self.y+179+3+1*27)
    -- end
    -- if 临时加点.魔力 and  临时加点.魔力>0 then
    --   字体15:取图像(" +" ..(临时加点.魔力 or "")):显示(self.x+217,self.y+179+3+2*27)
    -- end
    -- if 临时加点.力量 and  临时加点.力量>0 then
    --   字体15:取图像(" +" ..(临时加点.力量 or "")):显示(self.x+217,self.y+179+3+3*27)
    -- end
    -- if 临时加点.耐力 and  临时加点.耐力>0 then
    --   字体15:取图像(" +" ..(临时加点.耐力 or "")):显示(self.x+217,self.y+179+3+4*27)
    -- end
    -- if 临时加点.敏捷 and  临时加点.敏捷>0 then
    --   字体15:取图像(" +" ..(临时加点.敏捷 or "")):显示(self.x+217,self.y+179+3+5*27)
    -- end

    字体15:置颜色(__取颜色("浅黑"))
    if self.角色.奇经八脉 and self.角色.奇经八脉["当前流派"] then
      字体15:取图像(self.角色.等级.."级   "..self.角色.门派.."  "..self.角色.奇经八脉["当前流派"]):显示(29+56,135+6+10-87+24)
    else
      字体15:取图像(self.角色.等级.."级   "..self.角色.门派):显示(29+56,135+6+10-87+24)
    end
    
    nsf["渲染结束"](nsf)
  end
  self:刷新面板()
  助战详情["图像"] = nsf["到精灵"](nsf)
end
function 助战详情:刷新面板()
  助战详情["经验条"]:置位置(math.floor(助战详情.角色["当前经验"] / 助战详情.角色["最大经验"] * 100))
end
local 经验条 = 助战详情["创建进度"](助战详情, "经验条", 71-15-10, 287+135, 183, 18)
function 经验条:初始化()
  self:置精灵(__res:getPNGCC(1, 587, 108, 183, 18)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 18))))
end
function 经验条:显示(x, y)
  字体13["显示"](字体13, x + 5, y - 1, string.format("%s/%s", 助战详情.角色["当前经验"], 助战详情.角色["最大经验"]))
end
-- local 转换门派 = 助战详情["创建我的按钮16"](助战详情, __res:getPNGCC(7, 0, 242, 120, 29):拉伸(90,25), "转换门派", 11+12+135,28+18+12+58, "转换门派")
-- function  转换门派:左键弹起(x, y, msg)
--   local 可选门派 = __主控["队伍角色"](__主控, 助战详情.角色["模型"]).门派
--   for i=1,#可选门派 do
--     if 助战详情.角色.门派==可选门派[i] then
--       table.remove(可选门派,i)
--     end
--   end
--   __UI界面["窗口层"]["对话栏"]:打开("","助战转换门派","助战只能进行门派转换（需支付200仙玉,首次加入无需支付），如需进行角色种族转换，请使用PC端进行此项操作，请选择你需要转换的门派：",可选门派)
-- end
local 道具操作 = 助战详情["创建我的按钮16"](助战详情, __res:getPNGCC(7, 0, 242, 120, 29):拉伸(90,25), "道具操作", 11+322-8,370+18, "道具操作")
function  道具操作:左键弹起(x, y, msg)
  if __UI界面["窗口层"].助战道具栏.是否可见 then
    __UI界面["窗口层"].助战道具栏:置可见(false)
  else
    发送数据(2012,{助战编号=助战详情.助战编号})
  end
end
local 查看经脉 = 助战详情["创建我的按钮16"](助战详情, __res:getPNGCC(7, 0, 242, 120, 29):拉伸(90,25), "查看经脉", 11+12,28+18+12+58, "查看经脉")
function  查看经脉:左键弹起(x, y, msg)
  __UI界面["窗口层"].助战经脉:打开(助战详情.角色,助战详情.助战编号)
end
local 师门技能 = 助战详情["创建我的按钮16"](助战详情, __res:getPNGCC(7, 0, 242, 120, 29):拉伸(90,25), "师门技能", 11+12+100,28+18+12+58, "师门技能")
function  师门技能:左键弹起(x, y, msg)
  __UI界面["窗口层"].助战技能学习:打开(助战详情.角色,助战详情.助战编号)
end
if not __lkz then
  local 修炼技能 = 助战详情["创建我的按钮16"](助战详情, __res:getPNGCC(7, 0, 242, 120, 29):拉伸(90,25), "修炼技能", 11+12,28+18+12+58+34, "修炼技能")
  function  修炼技能:左键弹起(x, y, msg)
    __UI界面["窗口层"].助战修炼学习:打开(助战详情.角色,助战详情.助战编号)
  end
end
local 更多属性 = 助战详情["创建我的按钮16"](助战详情, __res:getPNGCC(7, 0, 242, 120, 29):拉伸(90,25), "更多属性", 11,370+18, "更多属性")
function  更多属性:左键弹起(x, y, msg)
  __UI弹出["助战更多属性弹出"]:打开(助战详情.角色)
end
local 加点按钮 = 助战详情["创建我的按钮16"](助战详情, __res:getPNGCC(7, 0, 209, 120, 29):拉伸(60,25), "加点按钮", 233, 179+6*27+17, "加点",nil,"白色")
function  加点按钮:左键弹起(x, y, msg)
  __UI界面["窗口层"]["助战加点"]:打开(助战详情.角色)
end
local 升级按钮 = 助战详情["创建我的按钮16"](助战详情, __res:getPNGCC(7, 0, 209, 120, 29):拉伸(60,25), "升级按钮", 233, 179+6*27+17+60, "升级",nil,"白色")
function  升级按钮:左键弹起(x, y, msg)
  if 助战详情.角色.等级==39 or 助战详情.角色.等级==59 or 助战详情.角色.等级==69 or 助战详情.角色.等级==89 or 助战详情.角色.等级==109 or 助战详情.角色.等级==129 then
    local 事件 = function()
      发送数据(2007,{助战编号=助战详情.助战编号})
    end
    __UI界面.窗口层.文本栏:打开("确定将等级提升至#R "..(助战详情.角色.等级+1).."#W 级吗？", 285, 155, 390, 200, 事件)
  else
    发送数据(2007,{助战编号=助战详情.助战编号})
  end
end


local 灵饰网格 = 助战详情["创建网格"](助战详情, "灵饰网格", 30-20+306-5, 67+119-116+9, 124, 134)
function 灵饰网格:初始化()
  self:创建格子(60, 60, 0, 0, 2, 2)
end
function 灵饰网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520-487, 86, w, h, "助战灵饰", a,助战详情.助战编号)
  end
end
function 灵饰网格:置物品(数据)
  for i = 1, #灵饰网格["子控件"] do
    if 数据[i] then
      -- print(数据[i].分类)
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "助战装备")
      灵饰网格["子控件"][i]["置精灵"](灵饰网格["子控件"][i], lssj)
    else
      灵饰网格["子控件"][i]["置精灵"](灵饰网格["子控件"][i])
    end
  end
end


local 装备网格 = 助战详情["创建网格"](助战详情, "装备网格", 30-20+306-5, 67+119-116+9+119, 124, 180)
function 装备网格:初始化()
  self:创建格子(60, 60, 0, 0, 3, 2)
end
function 装备网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520-487, 86, w, h, "助战装备", a,助战详情.助战编号)
  end
end
function 装备网格:置物品(数据)
  for i = 1, #装备网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "助战装备")
      装备网格["子控件"][i]["置精灵"](装备网格["子控件"][i], lssj)
    else
      装备网格["子控件"][i]["置精灵"](装备网格["子控件"][i])
    end
  end
end

local bbxuanz=0
local bb网格 = 助战详情["创建网格"](助战详情, "bb网格", 30-20+306-5+137, 67+119-116+9, 155, 363)
function bb网格:初始化()
  -- self:创建格子(328, 140, 0, 0, 3, 2,true)
end
function bb网格:左键弹起(x, y, a, b, msg)
  -- if self.子控件[a]._spr["数据"] then
  --   助战详情["选中"] = a
  --   self.子控件[a]._spr["确定"] = true
  -- end
  if not self.子控件[a] or not self.子控件[a]._spr or not self.子控件[a]._spr["数据"] then
    return
  end
  if bbxuanz and self.子控件[bbxuanz] then
    self.子控件[bbxuanz]._spr["确定"] = nil
  end
  bbxuanz = a
  self.子控件[a]._spr["确定"] = true
  发送数据(2005,{助战编号=助战详情.助战编号,认证码=助战详情.bb基础属性[a].认证码})
end

function bb网格:置数据()
  self:创建格子(155, 52, 0, 0, #助战详情.bb基础属性, 1)
  for i, v in ipairs(self.子控件) do
    if 助战详情.bb基础属性[i] then
      local lssj = __助战bb格子["创建"]()
      lssj["置头像"](lssj, 助战详情.bb基础属性[i])
      local clol="橙红"
      local wz="参战"
      if 助战详情.bb基础属性[i].参战信息 then
        clol="红色"
        wz="休息"
      end
      local 参战按钮 = self.子控件[i]["创建我的按钮16"](self.子控件[i], __res:getPNGCC(7, 0, 273, 80, 23):拉伸(50,23), "参战按钮" .. i, 96, 22, wz,nil,clol)
      function 参战按钮:左键单击(x, y, msg)
        发送数据(2006,{助战编号=助战详情.助战编号,认证码=助战详情.bb基础属性[i].认证码})
      end
      v:置可见(true, true)
      bb网格["子控件"][i]["置精灵"](bb网格["子控件"][i], lssj)
    end
  end
end



local 关闭 = 助战详情["创建我的按钮"](助战详情, __res:getPNGCC(7, 203, 32, 22, 22), "关闭", 612+4-26, 14)
function 关闭:左键弹起(x, y, msg)
  助战详情["置可见"](助战详情, false)
end

-- function 助战详情:刷新临时信息(是否,体质,魔力)
-- 	local 五维属性 = self:临时取属性(self.角色.种族,{临时加点.体质,临时加点.魔力,临时加点.力量,临时加点.耐力,临时加点.敏捷})
-- 	临时属性 = {气血=五维属性["气血"],魔法=五维属性["法力"],命中=五维属性["命中"],伤害=五维属性["伤害"],防御=五维属性["防御"],速度=五维属性["速度"],躲避=五维属性["躲避"],灵力=五维属性["灵力"],法伤=五维属性["灵力"],法防=五维属性["灵力"],体质=五维属性.体质,魔力=五维属性.魔力,力量=五维属性.力量,耐力=五维属性.耐力,敏捷=五维属性.敏捷}
-- end
-- function 助战详情:临时取属性(种族,五维,技能)
-- 	local 属性={}
-- 	local 力量 = 五维[3]
-- 	local 体质 = 五维[1]
-- 	local 魔力 = 五维[2]
-- 	local 耐力 = 五维[4]
-- 	local 敏捷 = 五维[5]
-- 	技能 = 技能 or {0,0,0,0}
-- 	if 种族 == "人" or 种族 == 1 then
-- 		属性 = {
-- 			命中 = floor(力量 * 2.01),
-- 			伤害 = floor(力量 * 0.67),
-- 			防御 = floor(耐力 * 1.5),
-- 			速度 = floor((体质 * 0.1) + (耐力 * 0.1) + (力量 * 0.1) + (敏捷 * 0.7)),
-- 			灵力 = floor((体质 * 0.3) + (魔力 * 0.7) + (耐力 * 0.2) + (力量 * 0.4)),
-- 			躲避 = floor(敏捷),
-- 			气血 = floor((体质 * 5)),
-- 			法力 = floor((魔力 * 3)),
-- 		}
-- 	elseif 种族 == "魔" or 种族 == 2 then
-- 		属性 = {
-- 			命中 = floor(力量 * 2.31),
-- 			伤害 = floor(力量 * 0.77),
-- 			防御 = floor(耐力 * 1.4),
-- 			速度 = floor((体质 * 0.1) + (耐力 * 0.1) + (力量 * 0.1) + (敏捷 * 0.7)),
-- 			灵力 = floor((体质 * 0.3) + (魔力 * 0.7) + (耐力 * 0.2) + (力量 * 0.4)),
-- 			躲避 = floor(敏捷),
-- 			气血 = floor((体质 * 6)),
-- 			法力 = floor((魔力 * 2.5)),
-- 		}
-- 	elseif 种族 == "仙" or 种族 == 3 then
-- 		属性 = {
-- 			命中 = floor(力量 * 1.71),
-- 			伤害 = floor(力量 * 0.57),
-- 			防御 = floor(耐力 * 1.6),
-- 			速度 = floor((体质 * 0.1) + (耐力 * 0.1) + (力量 * 0.1) + (敏捷 * 0.7)),
-- 			灵力 = floor((体质 * 0.3) + (魔力 * 0.7) + (耐力 * 0.2) + (力量 * 0.4)),
-- 			躲避 = floor(敏捷),
-- 			气血 = floor((体质 * 4.5)),
-- 			法力 = floor((魔力 * 3.5)),
-- 		}
-- 	end
-- 	return 属性
-- end
-- for i = 1, 5 do
--   local 加加加 = 助战详情["创建我的按钮"](助战详情, __res:getPNGCC(7, 206, 0, 26, 24), i .. "加", 243, 179+i*27+18)
--   function  加加加:左键弹起(x, y, msg)
--   end
--   local 贱贱贱 = 助战详情["创建我的按钮"](助战详情, __res:getPNGCC(7, 206, 100, 26, 24), i .. "减", 243+27, 179+i*27+18)
--   function  贱贱贱:左键弹起(x, y, msg)
--   end
-- end