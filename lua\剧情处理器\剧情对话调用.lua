local 随机序列=0
local random = 取随机数
local JQsj={}
JQsj[1]=0
JQsj[2]=0
JQsj[3]=25
JQsj[4]=9999
JQsj[5]=9999

function 取剧情是否追踪(主线)
	if __ahao then
		JQsj[1]=0
		JQsj[2]=9999
		JQsj[3]=9999
		JQsj[4]=9999
		JQsj[5]=9999
		JQsj[6]=9999
		JQsj[7]=9999
		JQsj[8]=9999
		JQsj[9]=9999
		JQsj[10]=9999
		JQsj[11]=9999
	end
	if JQsj[主线] and 角色信息.等级>=JQsj[主线] then
		return true
	end
end

function 取剧情对话内容(ID,编号)
	local 剧情主线= 角色信息.剧情.主线
	if not 取剧情是否追踪(剧情主线) then
		return
	end
	local wb = {}
	local wb2 = {}
	local xx = {}
	local xx2 = {}
	local 剧情地图 = 角色信息.剧情.地图
	local 剧情NPC编号 = 角色信息.剧情.编号
	local 玩家模型 = 角色信息.模型
	local 玩家名称 = 角色信息.名称
	local 剧情进度 = 角色信息.剧情.进度
	local 假人信息 = 场景取假人表(剧情地图,剧情NPC编号,1)
	--table.print(假人信息)
	local function 第一页 (类型,对话,选项)
		if 选项 == nil then
			选项 = {}
		end
		xx = 选项
		if 类型 ==1 then
			wb={假人信息.模型,假人信息.名称,对话}
		else
			wb={玩家模型,玩家名称,对话}
		end
	end
	local function 下一页 (类型,对话,选项)
		if 选项 == nil then
			选项 = {}
		end
		local 模型,名称
		if 类型 ==1 then
			模型,名称 = 假人信息.模型,假人信息.名称,对话
		else
			模型,名称 = 玩家模型,玩家名称
		end
		if wb2[1] == nil then
			wb2 = {模型,名称,对话,选项}
		else
			if wb2[5] == nil then
				wb2[5] = {模型,名称,对话,选项}
			else
				if wb2[5][5] == nil then
					wb2[5][5] = {模型,名称,对话,选项}
				else
					if wb2[5][5][5] == nil then
						wb2[5][5][5] = {模型,名称,对话,选项}
					else
						if wb2[5][5][5][5] == nil then
							wb2[5][5][5][5] = {模型,名称,对话,选项}
						else
							if wb2[5][5][5][5][5] == nil then
								wb2[5][5][5][5][5] = {模型,名称,对话,选项}
							else
								if wb2[5][5][5][5][5][5] == nil then
									wb2[5][5][5][5][5][5] = {模型,名称,对话,选项}
								else
									if wb2[5][5][5][5][5][5][5] == nil then
										wb2[5][5][5][5][5][5][5] = {模型,名称,对话,选项}
									else
										if wb2[5][5][5][5][5][5][5][5] == nil then
											wb2[5][5][5][5][5][5][5][5] = {模型,名称,对话,选项}
										else
											if wb2[5][5][5][5][5][5][5][5][5] == nil then
												wb2[5][5][5][5][5][5][5][5][5] = {模型,名称,对话,选项}
											else
												if wb2[5][5][5][5][5][5][5][5][5][5] == nil then
													wb2[5][5][5][5][5][5][5][5][5][5] = {模型,名称,对话,选项}
												else
													if wb2[5][5][5][5][5][5][5][5][5][5][5] == nil then
														wb2[5][5][5][5][5][5][5][5][5][5][5] = {模型,名称,对话,选项}
													else
														if wb2[5][5][5][5][5][5][5][5][5][5][5][5] == nil then
															wb2[5][5][5][5][5][5][5][5][5][5][5][5] = {模型,名称,对话,选项}
														else
															if wb2[5][5][5][5][5][5][5][5][5][5][5][5][5] == nil then
																wb2[5][5][5][5][5][5][5][5][5][5][5][5][5] = {模型,名称,对话,选项}
															end
														end
													end
												end
											end
										end
									end
								end
							end
						end
					end
				end
			end
		end
	end
	if  剧情主线 == 1 then
		--print(ID , 剧情地图 , 编号, 剧情NPC编号 , 剧情进度 )
		if ID == 剧情地图 and 编号== 剧情NPC编号 then
			if 剧情进度 == 2 then
				第一页(2,"#52")
				下一页 (1,"待老夫看看，无妨，只是有些气血不足，吃几个包子就好了。")
				下一页 (2,"#Y包子#W还有这效果啊?但我这也没有包子，大夫您、您有包子吗?")
				下一页 (1,"俗话说，授人以鱼不如授人以渔，你己做一个不就好了，这可是在三界闯荡的必备技能!")
				下一页 (1,"少侠可感到好些了?")
				下一页 (2,"#Y包子#W真好吃")
				下一页 (2,"呀!快去村长家看看，谭村长和孙猎户好像吵起来了。")
				角色信息.剧情={主线=1,编号 = 10,地图 = 1003,进度 = 2,附加={}}
				return {wb[1],wb[2],wb[3],xx,nil,nil,wb2,true}
			elseif  剧情进度 == 3 then
				第一页(1,"不行，我不同意!")
				第一页(1,"你说不行就不行，那我们村子就要这样与世隔绝了吗?!")
				下一页 (2,"谭村长和孙猎户争执不下，此时你……")
				xx={"当然是支持孙猎户","让我再想想"}
				return{wb[1],wb[2],wb[3],xx,nil,nil,nil}
			elseif  剧情进度 == 4 then
				local 武器={}
				第一页(1,"等你好一会儿了，这里有一些趁手的兵器我看挺适合你，挑个顺手的吧!")
				xx={"我看看是什么兵器","暂时不用"}
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,nil}
			elseif  剧情进度 == 5 then
				第一页(1,"由几根木头简单拼凑成的假人，看起来有些破损。",{"吃我一招！（战斗）","咦,我武器呢?"})
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
			elseif  剧情进度 == 6 then
				第一页(1,"由几根木头简单拼凑成的假人，看起来有些破损。",{"嘿嘿,我又来了！（战斗）","咦,我宝宝呢?"})
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
			elseif  剧情进度 == 7 then
				第一页(1,"少侠红光满面可是有好消息告诉我呀",{"缘起东海湾"})
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
			elseif  剧情进度 == 8 then
				第一页(1,"不管怎样，既然少侠已经通过了试炼，自然可以拜入本族门派，不知少侠可心有所属？",{"门派选择","我等会儿再找你。"})
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
			end
		end
	elseif 剧情主线 == 2 then
		--print(ID , 剧情地图 , 编号, 剧情NPC编号 , 剧情进度 , 角色信息.剧情.给予)
		if ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 1 then
			wb[1] = "学习本领要虚心，可不能心浮气躁".."#"..random(1,110)
			xx = {"新手主线","我们后会有期","只是路过"}
			local 剧情名称 = 场景取假人表(剧情地图,剧情NPC编号,1)
			return{剧情名称.模型,剧情名称.名称,wb[取随机数(1,#wb)],xx}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 2 then
			wb={假人信息.模型,假人信息.名称,"近几年哇，临近建邺城的东海湾就有闹鬼的传闻，靠近海边的村子里，不是百姓走失，就是家畜死于非命！李善人为此出资做过好几次法事了，可没想到，这事情一点平息的迹象也没有....竟然有人在建邺城里撞到了鬼！如今大家人心惶惶，说是城里风水不好，要出大事了呢！"}
			wb2 = {玩家模型,玩家名称,"真的有人撞到鬼了吗#24",{},
			{假人信息.模型,假人信息.名称,"可不是！撞到鬼的#Y/牛大胆#W/还是个道士呢，你说邪门不邪门！",{},
			{玩家模型,玩家名称,"心想：既然牛大胆撞到鬼了，那牛大胆处一定会有线索。",xx2,nil}}}
			-- 角色信息.剧情={主线=2,编号 = 12,地图 = 1501,进度 = 3,附加={}}
			-- 角色信息:添加经验伙伴(qz(26784),"主线剧情")
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,true}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 3 then
			wb={玩家模型,玩家名称,"我是来调查建邺城闹鬼的事儿的。听说老神仙您撞到鬼了？那是怎么个情形啊"}
			wb2 = {假人信息.模型,假人信息.名称,"你这少侠怎么这么不会说话，什么是撞到鬼了#4老道我那是抓鬼去了，谁曾想，那只鬼脚底抹油，竟然让他给跑了！",{},
			{玩家模型,玩家名称,"那您可看清楚他长什么样子了？知道他是什么来历吗？",{},
			{假人信息.模型,假人信息.名称,"咳咳咳……这个鬼啊……真是……咳咳咳……有点饿了怎么办#17我最爱吃#Y/王大嫂#W/做的烤鸭了，现在好想吃#80吃饱了说不定我能想起什么来#24",{},
			{玩家模型,玩家名称,"好好好，老神仙等着我这就去给你找！",{},nil}}}}
			-- 角色信息.剧情={主线=2,编号 = 7,地图 = 1501,进度 = 4,附加={}}
			-- 角色信息:添加经验伙伴(qz(38726),"主线剧情")
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 4 then
			wb={玩家模型,玩家名称,"大娘大娘，听说您这的除了烤鸭还有特色红烧鱼哇#24"}
			wb2 = {假人信息.模型,假人信息.名称,"呔，定是那馋嘴的牛大胆告诉你的吧，要想吃红烧鱼你得给我一个熊胆作为交换！",{},
			{玩家模型,玩家名称,"啊？好好好我马上去找",{},nil}}
			-- 角色信息.剧情={主线=2,编号 = 7,地图 = 1501,进度 = 5,附加={物品="熊胆",数量=1}}
			-- 角色信息:添加经验伙伴(qz(51013),"主线剧情")
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 5 then
			if not  角色信息.剧情.给予 then
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
			end
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 7 then
			wb={玩家模型,玩家名称,"管家您好，听闻牛大胆说李善人生病了是吗？"}
			wb2 = {假人信息.模型,假人信息.名称,"哎，我们老爷的身体本来就不大好，但每次生病，细心调养下也就好了，没想到这次……就是那个牛大胆说自己撞了鬼之后，老爷的病就愈演愈烈，这几日，眼见着就有些神志不清……大夫说必须要#Y/松风灵芝#W/做药引。我们找遍了建邺城，东海湾还是一无所获，你说这可怎么办呀#52听说城里来了个云游商人叫#Y/马全有#W/，我去找他好几次，都没有见到他的人影。如今老爷病情越来越重，我也走不开，少侠你能不能再去帮我求求他#52",{},
			{玩家模型,玩家名称,"好的，我这就去#80",{},nil}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 8 then
			wb={假人信息.模型,假人信息.名称,"松风灵芝？我倒是有，只是这东西甚是难得，少侠你想要拿它做什么？"}
			wb2 = {玩家模型,玩家名称,"李善人生了重病，大夫说，须得松风灵芝做药引，方可药到病除。",{},
			{假人信息.模型,假人信息.名称,"听闻李善人可是个大好人，这松风灵芝给他，也算是物尽其用啦！少侠你赶紧把他拿给管家吧！",{},
			{玩家模型,玩家名称,"谢谢马全有先生，再会",{},nil}}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 9 and 角色信息.剧情.给予 then
			wb={玩家模型,玩家名称,"管家，松风林芝送来了~"}
			wb2 = {假人信息.模型,假人信息.名称,"这下老爷有救了，谢谢少侠#52请少侠一道和我进去看看老爷吧",{},nil}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 10 then
			wb={假人信息.模型,假人信息.名称,"多谢少侠救命之恩，说起来东海湾闹鬼之事，皆是因为老朽我一人所起……"}
			wb2 = {玩家模型,玩家名称,"老先生何出此言？",{},
			{假人信息.模型,假人信息.名称,"七年前，我与一个北俱芦洲的朋友一道前往西牛贺洲收货。回程时我们讲定，先回建邺城卸下我的货物，他也顺便在我这儿小住几日。可是没想到，船开到东海湾，竟遇上一场大风暴，货船也是触了礁。",{},
			{玩家模型,玩家名称,"这。。。。",{},
			{假人信息.模型,假人信息.名称,"当时，我们二人都掉进了海里。幸运的是，我抱住了一个大木桶，尚能浮在海面。风雨里，我听着他呼救着我的名字。可是我想，这木桶怕是承受不了我们两人的重量……于是就眼睁睁地看着他……沉入了海里……",{},
			{玩家模型,玩家名称,"然后，东海湾就出现了闹鬼的传闻？",{},
			{假人信息.模型,假人信息.名称,"是啊，我回到建邺城后不足一月，便有人说在东海湾撞见了鬼……最近，我便是夜夜梦见他对我怒目而视。紧接着，就传出了建邺城闹鬼的事儿。我知道，大错已近铸成，我虽然每年都托人带财物给他的家人，还请道士做法事超度他，但依旧无法减轻我的罪孽。可是，要报仇找老夫便是了，何苦伤害无辜的百姓呢？少侠，我想请你替我去问个明白。",{},
			{玩家模型,玩家名称,"那，我该去何处寻找他呢？",{},
			{假人信息.模型,假人信息.名称, "那艘船是在东海湾沉没的，大家现在都叫他#Y/东海沉船#W/，去哪儿说不定能够找到一些线索，去哪儿要路过#Y/东海湾#W，那么还是有些危险，还请少侠出行之前，点燃一炷#Y/摄妖香吧#W/！",{},nil}}}}}}}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 11 then
			wb={假人信息.模型,假人信息.名称,"鬼魂却不分由说开始攻击你，你无奈之下唯有击倒他（需战斗）"}
			xx = {"清醒清醒！（战斗）","稍等。"}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 12 then
			wb={假人信息.模型,假人信息.名称,"看少侠受了点小伤可是被那鬼魂所伤少侠？"}
			wb2 = {玩家模型,玩家名称,"多谢李善人关心，确实被那鬼魂所伤不过那鬼魂已经被消灭",{},
			{假人信息.模型,假人信息.名称,"哎...可怜我那朋友，希望他投胎能托个好人家，多谢少侠的帮助，我观少侠气宇非凡我这正好有几件少侠能用的趁手武器和防具请少侠不要客气",{},
			{玩家模型,玩家名称,"那就多谢善人的厚礼了，那在下就告辞了",{},nil}}}
			-- 礼包奖励类:剧情奖励20(数字id)
			-- 角色信息.剧情={主线=2,编号 = 9,地图 = 1501,进度 = 13,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 13 then
			wb={假人信息.模型,假人信息.名称,"少侠红光满面可是有好消息告诉老朽"}
			wb2 = {玩家模型,玩家名称,"老头还真让你猜对了，闹鬼的事情解决了(事情经过...)",{},
			{假人信息.模型,假人信息.名称,"想那鬼魂也是个可怜人，老朽看少侠一身本领何不出去闯荡闯荡",{},
			{玩家模型,玩家名称,"我正有次打算，那在下就告辞了",{"休息休息准备启程（剧情动画）"},nil}}}
			-- 角色信息.剧情={主线=3,编号 = 9,地图 = 1070,进度 = 1,附加={}}
			-- 角色信息.剧情.暂停=true --测试模式
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		end
	elseif 剧情主线 == 3 and 角色信息.等级 >= 25 then
	--	print(ID , 剧情地图 , 编号, 剧情NPC编号 , 剧情进度 , 角色信息.剧情.给予)
		if ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 1 then
			wb={假人信息.模型,假人信息.名称,"我的鹿儿啊，你跑哪去了？"}
			wb2 = {玩家模型,玩家名称,"老仙家，发生了什么事？",{},
			{假人信息.模型,假人信息.名称,"哎，前两天我和东华帝君下棋，那孽障竟私自挣脱了缰绳，不知跑到哪儿去了，害得我这么一大把年纪还要自己走路回家，腰疼腿疼头疼肚子疼都发作了，苦不堪言啊……我看你腿脚挺灵便的，能不能帮我把那白鹿儿追回来",{"没问题，老仙家，你告诉我白鹿往哪跑了？","老仙家，你太缺乏锻炼了，没了坐骑，正好给你个天天马拉松的机会~"},
			nil}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,false}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 2 then
			wb={玩家模型,玩家名称,"你就是南极仙翁的坐骑白鹿？化成人形作乱人间也拜托你有创意一点阿！这个造型怎么看怎么像个流氓,你的妆是恶心嘛,生我气我也要说:还不如回去跟仙翁老头混日子吧！"}
			wb2 = {假人信息.模型,假人信息.名称,"我不会生气的，你真是有眼光，我这么潇洒的流氓造型都被你看出来了，不如以后我和我老婆就跟着你混吧，我们到比丘国去过快乐的日子，用小心肝炼不老药，大家一起长生不老！",{},
			{玩家模型,玩家名称,"哇，这么有搞头，听的我热血沸腾啊…………咳咳，不过我好歹是正义之士耶，这么做似乎不太好……",{},
			{假人信息.模型,假人信息.名称,"那些神仙整天婆婆妈妈唧唧歪歪，我本事这么大，凭什么被他呼来喝去啊，我是不会和你回去的！",{"这么狂妄？好，打赢我就放你走","说的好，至情至性，我早就看那些神仙不顺眼了！"},nil}}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,false}
		elseif 剧情进度 == 3 then
			local 假人信息2 = 场景取假人表(ID,编号,1)
			if 假人信息2.名称=="玉面狐狸" and 剧情进度 == 3 and 角色信息.剧情.附加.玉面狐狸 == nil  then
				wb={假人信息2.模型,假人信息2.名称,"我老公说要用小儿心肝做药，只不过是一个设想，你要抓他也要等他真娶了小儿心肝再说啊。你现在要我老公回去，我不就要守活寡了？我跟你拼了！"}
				xx = {"狐狸精，谁怕你！","啧，好凶悍的狐狸精，先撤退！"}
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,false}
			elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 3 then
				wb={假人信息.模型,假人信息.名称,"真是太感谢你了，找回我的鹿儿，明天就可以骑着它去看看那个孩子了！要说这孩子的身世还真是离奇曲折呢，不过他身为天命取经人中的一个，也……啊，好像说漏嘴了。"}
				wb2 = {玩家模型,玩家名称,"什么！天命取经人！",{},
				{假人信息.模型,假人信息.名称,"这个……我不知道你在说什么，呵呵，哈哈",{},
				{玩家模型,玩家名称,"你这老头真不够意思，我帮你找回白鹿，你还如此吞吞吐吐，哼哼唧唧！",{},
				{假人信息.模型,假人信息.名称,"咳咳，怎么我的白鹿儿看起来精神不太好，这么可爱的动物你都要虐待真是太没有人性了！",{},
				{玩家模型,玩家名称,"什么！明明是你看管不严才……",{},
				{假人信息.模型,假人信息.名称,"还和老人顶嘴真没礼貌！咳咳，除非找到百色花来给我的鹿儿疗伤，否则不但别想从我这里得到取经人的消息，我还要找你问罪呢！",{},
				{玩家模型,玩家名称,"~~$&(*$(……真是蛮不讲理！",{},
				nil}}}}}}}
				-- 角色信息.剧情={主线=3,编号 = 9,地图 = 1070,进度 = 4,附加={物品="百色花",数量=1}}
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
			end
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 4 and 角色信息.剧情.给予 then --获取道具江湖夜雨
			wb={玩家模型,玩家名称,"百色花在这里，这下你该满意了吧！"}
			wb2 = {假人信息.模型,假人信息.名称,"呵呵，好说好说，当年金蝉子被贬下凡，是我奉观音法旨将他托生为殷温娇之子，他出生时父亲被奸人所害，母亲也被霸占，不过天有吉人之相，他被一位禅师救起，现在江州为僧，法名玄奘，算来也有十八年了，最近听说他以成长为一位高僧，还在金山寺主持讲经大会……",{},
			{玩家模型,玩家名称,"原来如此……谢谢你老仙翁，天色不早我也该告辞了！",{},
			{玩家模型,玩家名称,"等等，不管怎么说我也是神仙啊，你帮了我的忙我也得意思意思，来来来，这个给你，顺便提醒一下，听说金山寺的酒肉和尚十分凶蛮，你最好带柱香去装作香客，莫惹了他~",{},
			nil}}}
			-- 玩家数据[数字id].道具:给予道具(数字id,"江湖夜雨")
			-- 角色信息.剧情={主线=3,编号 = 1,地图 = 1153,进度 = 5,附加={物品="香",数量=1,战斗 = 1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 5 then
			if 角色信息.剧情.给予 then
				wb={假人信息.模型,假人信息.名称,"佛法我讲不过你，输了……，你姓名也不识，父母也不识，还在此捣什么鬼！"}
				wb2 = {玩家模型,玩家名称,"和尚，你怎么能出口伤人？",{},
				{假人信息.模型,假人信息.名称,"真不知道法明长老为什么会收养玄奘那野种！",{},
				{玩家模型,玩家名称,"你太过分了！",{},
				{假人信息.模型,假人信息.名称,"过分？我刚才只是骂他，我现在还要打他呢！",{},
				{玩家模型,玩家名称,"你敢！你要打玄奘法师，先要问问我手中的兵刃答不答应！",{"教训一下这恶和尚","不要和出家人争吵……善哉善哉……"},
				nil}}}}}
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
			else
				    wb={假人信息.模型,假人信息.名称,"既然来了就把香先上了吧！"}
				    return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
			end
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 6 then
			wb={假人信息.模型,假人信息.名称,"我是被法明长老收养的野种……我是……野……种……"}
			wb2 = {玩家模型,玩家名称,"玄奘法师你不要难过么，不要听那个酒肉和尚胡说。",{},
			{假人信息.模型,假人信息.名称,"我枉自为人十八年，始终不知道亲生父母的名字，愧为人子，愧为人子啊……",{},
			{玩家模型,玩家名称,"其实玄奘法师啊，你身世的确有些曲折，不过这都是因为你时上天注定的天命取经人，这个就说来话长了……事情牵涉到你的前前前前前世不知多少个轮回之前，你还是一个天真无邪、活泼可爱的……",{},
			{假人信息.模型,假人信息.名称,"施主你那么啰嗦有完没完，完全不理人家受得了受不了，贫僧现在的首要任务是找法明长老问明身世，别的一概免谈！",{},
			{玩家模型,玩家名称,"#24这么有型，那你想怎样？",{},
			{假人信息.模型,假人信息.名称,"法明长老目前去四方云游交流佛法了。贫僧又要主持讲经大会走不开身，施主能否帮我找到法明长老问明贫僧的身世，一日不知身世，贫僧一日不得安心啊……",{},
			{玩家模型,玩家名称,"看来只好跑一趟了……",{},
			nil}}}}}}}
			角色信息.剧情={主线=3,编号 = 1,地图 = 1528,进度 = 7,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 7 then
			wb={假人信息.模型,假人信息.名称,"唉，前两天居然有个自称白琉璃的女子，抢走了寺中珍藏的佛光舍利子，还打伤了从金山寺来我寺交流佛法的法明长老，幸亏我空慈师父法术精深，才暂时将他驱走了……"}
			xx = {"什么，她还打伤了法明长老？快带我去看看！","什么人敢来化生寺抢东西，太嚣张了，我去问问空慈方丈，看有什么可以帮忙的！"}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 8 and 角色信息.剧情.分支 == 1 then
			wb={玩家模型,玩家名称,"法明长老"}
			wb2 = {假人信息.模型,假人信息.名称,"…… ……",{},
			{玩家模型,玩家名称,"禅师！",{},
			 {假人信息.模型,假人信息.名称,"…… ……",{},
			nil}}}
			-- 角色信息.剧情={主线=3,编号 = 4,地图 = 1002,进度 = 9,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 8 and 角色信息.剧情.分支 == 2 then
			wb={玩家模型,玩家名称,"空慈方丈，白琉璃抢走佛宝的事情我都知道了，作为一个上进的有为热血英雄，我决定~帮~助~你，怎么样，感动吧。"}
			wb2 = {假人信息.模型,假人信息.名称,"那白琉璃能够闯我化生寺，自是来历非常，施主若没有十足的把握，且莫轻易去招惹他",{},
			{玩家模型,玩家名称,"哼，让我会会她，看看她究竟有多厉害！",{},
			nil}}
			-- 角色信息.剧情={主线=3,编号 = 9,地图 = 1110,进度 = 8,分支=3,附加={战斗 = 1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 8 and 角色信息.剧情.分支 == 3 then
			wb={玩家模型,玩家名称,"就是你硬闯化生寺抢夺舍利子，还打伤了金山寺的法明长老？"}
			wb2 = {假人信息.模型,假人信息.名称,"不错。",{"哼，让我看看你有何本事！","等我准备好再来收拾你！"},
			nil}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 8 and 角色信息.剧情.分支 == 4 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"施主能收服琉璃女，果真颇有佛缘，这些不成敬意，还望施主收纳。"}
			wb2 = {玩家模型,玩家名称,"多谢方丈，其实在下本事受人之托，来贵宝找法明长老问明一件事由的。",{},
			{假人信息.模型,假人信息.名称,"法明长老被白琉璃所伤，现在还昏迷不醒，施主进去看看他把。",{},
			nil}}
			角色信息.剧情={主线=3,编号 = 2,地图 = 1528,进度 = 8,分支=1,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 9 then
			wb={假人信息.模型,假人信息.名称,"唉，法明长老被白琉璃打伤，一直昏迷不醒，除非能找到能够妙手回春的侯医仙，配成特效药定神香，否则恐怕就难好了……"}
			wb2 = {假人信息.模型,假人信息.名称,"听说侯医仙喜欢吃新鲜水果。",{},
			nil}
			角色信息.剧情={主线=3,编号 = 4,地图 = 1514,进度 = 10,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 10 then
			wb={玩家模型,玩家名称,"侯……医……仙？果然是猴……"}
			wb2 = {假人信息.模型,假人信息.名称,"怎么啦，没见过我这么酷这么帅这么有个性的猴子啊！",{},
			{玩家模型,玩家名称,"没没……听说只有您会配置定神香，在下特来求您配药，救治法明长老！",{},
			{假人信息.模型,假人信息.名称,"定神香？难，难……少一味餐风饮露，我也没办法。",{},
			nil}}}
			角色信息.剧情={主线=3,编号 = 4,地图 = 1514,进度 = 11,附加={物品="餐风饮露",数量=1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 11 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"餐风饮露！你果然有心，好，这定神香你拿去吧！"}
			角色信息.剧情={主线=3,编号 = 2,地图 = 1528,进度 = 12,附加={物品="定神香"}}
			-- 玩家数据[数字id].道具:给予道具(数字id,"定神香",1,10)
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 12 and 角色信息.剧情.给予 then
			wb={玩家模型,玩家名称,"法明长老！"}
			wb2 = {假人信息.模型,假人信息.名称,"不要管我，快帮化生寺化解劫难，咳咳……",{},
			{玩家模型,玩家名称,"法明长老，我是受玄奘所托，来向您请教……",{},
			{假人信息.模型,假人信息.名称,"唉……十八年了，他也应该知道了。当年我从河边救起还是婴儿的玄奘，在他圣上发现一封血书，那上面源源本本记述了他的身世，你将这血书拿给他看，他自然回明白，至于他知晓自己身世之后会怎么做，就要看他心性机缘了。",{},
			{玩家模型,玩家名称,"多谢禅师相告，我这就动身回金山寺。",{},
			nil}}}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"玄奘的血书")
			角色信息.剧情={主线=3,编号 = 2,地图 = 1153,进度 = 13,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 13 then
			wb={玩家模型,玩家名称,"玄奘法师，我已经从法明长老处问明你的身世了。"}
			wb2 = {假人信息.模型,假人信息.名称,"四是四，十是十，十四是十四，四十是四十，不要把四十说成十四也不要把十四说成四十……",{},
			{玩家模型,玩家名称,"~％％~（***（NMSL）……玄奘大师你怎么了？",{},
			{假人信息.模型,假人信息.名称,"我叫小明，我是好孩子，我要去上学！！",{},
			{玩家模型,玩家名称,"玄奘……",{},
			nil}}}}
			角色信息.剧情={主线=3,编号 = 1,地图 = 1153,进度 = 14,附加={战斗 = 1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 14 then
			wb={玩家模型,玩家名称,"玄奘法师怎么满口胡话？"}
			wb2 = {假人信息.模型,假人信息.名称,"哈哈哈，因为他中了世上第一奇毒------七日丧命散，是用其中毒虫提炼七七四十九日而成，杀人于无影无踪。他这两天还是疯疯癫癫，不久就要筋脉逆流，胡思乱想，走火入魔，血管爆裂，一命呜呼，回归西天……实在是居家旅行，杀人灭口，必备良药！",{},
			{玩家模型,玩家名称,"精彩精彩，那么……玄奘法师怎么有幸中到这世上第一奇毒呢？",{},
			{假人信息.模型,假人信息.名称,"这个惊天地泣鬼神的原因就是------我放进他茶水里的！",{},
			{玩家模型,玩家名称,"鼓掌鼓掌，这个原因实在太感人了……我决不会放过你这黑心和尚！",{},
			{假人信息.模型,假人信息.名称,"啊，说漏了……",{"看招！","先准备一下在收拾他！"},
			nil}}}}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 15 then
			wb={玩家模型,玩家名称,"仙女姐姐，我来请您炼丹……"}
			wb2 = {假人信息.模型,假人信息.名称,"你就是为了金蝉子转世托生的天命取经人而来的把？",{},
			{玩家模型,玩家名称,"仙女姐姐你都知道了！",{},
			{假人信息.模型,假人信息.名称,"观音菩萨早已算到金蝉子有此一劫，已经命我炼成灵丹等候你多时了，不过这九转回魂丹要用孟婆酒服下，方可见效。你最好到阴曹地府跑一遭把，不过孟婆酒可不是那么容易能够拿到的，你可要分外小心。",{},
			nil}}}
			-- 玩家数据[数字id].道具:给予道具(数字id,"九转回魂丹",1,10)
			角色信息.剧情={主线=3,编号 = 2,地图 = 1122,进度 = 16,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 16 and 角色信息.等级 >= 40 then
			wb={假人信息.模型,假人信息.名称,"幽冥鬼就在附近，你自己找找把。"}
			角色信息.剧情={主线=3,编号 = 1,地图 = 1127,进度 = 17,附加={战斗 = 1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 17 then
			wb={假人信息.模型,假人信息.名称,"文秀……文秀……我等你等的你好苦，你为什么负我……"}
			wb2 = {玩家模型,玩家名称,"这位老兄，所谓人走阳关道，鬼渡奈何桥，你已经死了这么久了，为什么还不去投胎呢？",{},
			{假人信息.模型,假人信息.名称,"谁？！谁说我已经死了？！我要等我的文秀姑娘，你们为什么都说我死了，可恶！",{"哇，居然杀过来了，我抵挡","好可怕！快逃走！"},
			nil}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 18 then
			wb={玩家模型,玩家名称,"请问这位姑娘可是认识一位尾生？"}
			wb2 = {假人信息.模型,假人信息.名称,"尾生他已经死了，鸣鸣吗……",{},
			{玩家模型,玩家名称,"姑娘你不要哭嘛，我只是想研究一下人与人之间微妙的感情。尾生现在变成冤鬼还对你念念不忘，你能不能告诉我，你们约定的当天发生了什么事情？",{},
			{假人信息.模型,假人信息.名称,"那天我们约好在江州桥下见面,谁知突然下起了暴雨,路上泥泞难走,令人寸步难行。等雨停我赶去的时候，发现河水暴涨,而他在桥下苦苦守候,已经抱柱溺死了,呜鸣,是我对不起他…",{},
			{玩家模型,玩家名称,"唉，原来那笨鬼，也是个痴情的人……第二个问题，他现在还不肯去投胎，姑娘你可有什么办法？",{},
			{假人信息.模型,假人信息.名称,"他走了之后，我也决心终身不嫁，这是定情是他送我的一对手镯，你拿去给他看看，让他安心去投胎把",{},
			nil}}}}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"少女的手镯")
			角色信息.剧情={主线=3,编号 = 1,地图 = 1127,进度 = 19,附加={物品="少女的手镯"}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 19 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"这……这不是我送给文秀的手镯吗？她人在哪里？她为什么不来见我？啊，我记起来了……那天暴雨倾盆，河水越涨越高，但等不到文秀我无论如何不能离开，后来，后来……，呜，我明白了，怪不得我一直等不到她，原来我已经淹死了啊……"}
			wb2 = {玩家模型,玩家名称,"文秀并没有负你，她已经立誓终身不嫁，她还希望你安心去投胎，不要在阴间游荡了。",{},
			{假人信息.模型,假人信息.名称,"我明白了……原来不是文秀的错……我已经可以安心去投胎了，不过也希望你告诉文秀，让他找个好人嫁了吧，不要为我孤独一辈子，我会真心祝福他们的……",{},
			{玩家模型,玩家名称,"呵呵，你这傻鬼，也会说那么令人感动的话呢……",{},
			nil}}}
			角色信息.剧情={主线=3,编号 = 2,地图 = 1122,进度 = 20,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 20  then
			wb={假人信息.模型,假人信息.名称,"想不到你真的做到了，也好，听不到那冤鬼的哭声，今晚我也可以睡个好觉呢，这壶孟婆酒，你就拿去吧。"}
			角色信息.剧情={主线=3,编号 = 2,地图 = 1153,进度 = 21,附加={}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"孟婆酒")
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 21 then --交互
			--wb={假人信息.模型,假人信息.名称,"七日丧命散的毒果然厉害！"}
			--角色信息.剧情={主线=3,编号 = 2,地图 = 1153,进度 = 21,附加={}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"孟婆酒")
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,true}
			-- if 玩家数据[数字id].道具:判定背包道具(数字id,"九转回魂丹",1) and 玩家数据[数字id].道具:判定任务道具(玩家数据[数字id].连接id,数字id,"孟婆酒",1) and 玩家数据[数字id].道具:判定任务道具(玩家数据[数字id].连接id,数字id,"玄奘的血书",1) then
			-- 	玩家数据[数字id].道具:消耗背包道具(玩家数据[数字id].连接id,数字id,"九转回魂丹",1)
			-- 	玩家数据[数字id].道具:消耗任务道具(玩家数据[数字id].连接id,数字id,"玄奘的血书",1)
			-- 	玩家数据[数字id].道具:消耗任务道具(玩家数据[数字id].连接id,数字id,"孟婆酒",1)
			-- 	wb={假人信息.模型,假人信息.名称,"我？我这是怎么了？"}
			-- 	wb2 = {玩家模型,玩家名称,"你中了天下第一奇毒七日丧命散，是我找来灵药救了你性命。法明长老是故意这封血书里有你身世的全部秘密，请看。",{},
			-- 	{假人信息.模型,假人信息.名称,"书云：此儿父讳陈光蕊，官除江州令，妻命殷温娇，亦为相门女，贼子刘洪杀夫霸妾，冒官江州，妾恐贼加害此遗腹子，忍痛弃之江中，若蒙善人拾养，妾必感深恩，街环以报……原来我身世中藏此深仇大恨，父母之仇不能不报，玄奘又何以为人？我，我，我要拿起屠刀，还俗复仇！",{},
			-- 	{玩家模型,玩家名称,"还俗？！玄奘大师你身为天命取经人担负着拯救天下众生的大任，你还能够还俗？",{},
			-- 	{假人信息.模型,假人信息.名称,"我父被害，母亲深陷贼府，你让我拯救天下众生，谁来拯救我先？",{},
			-- 	{玩家模型,玩家名称,"谁……谁……就是我了！我来帮你救出母亲，并报杀父之仇！",{},
			-- 	{假人信息.模型,假人信息.名称,"大侠，我就知道你会这么说的……可否请你将此书送于我母殷温娇，共谋雪恨杀贼！",{},
			-- 	{玩家模型,玩家名称,"啊，被骗上了贼船……完了……",{},
			-- 	nil}}}}}}}
			-- 	角色信息.剧情={主线=3,编号 = 26,地图 = 1110,进度 = 22,附加={战斗 = 1}}
			-- 	return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
			-- end
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 22 then
			wb={假人信息.模型,假人信息.名称,"要进衙门告状的人听好，玉树临风的站左边，倾国倾城的站右边，獐头鼠目的站中间，你，就是说你呢，中间那个，在这里探头探脑的干嘛？"}
			wb2 = {玩家模型,玩家名称,"你说我？我有事要剑你们夫人。",{},
			{假人信息.模型,假人信息.名称,"我们夫人是朝廷诰命，哪能让你说见就见？先拿五千两来孝敬老子。",{},
			{玩家模型,玩家名称,"五千两，你不如去抢啊！",{},
			{假人信息.模型,假人信息.名称,"谁让你是找夫人？如果是找老爷，到可以给你打个八折。",{"门子大哥，五千两您拿去喝茶把！","给你银子去做医药费把，看招","让我再想想"},
			nil}}}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 23 then
			wb={假人信息.模型,假人信息.名称,"你是何人？来为何事？"}
			wb2 = {玩家模型,玩家名称,"夫人，你可识得此信：”隐恨温娇娘，藏冤江流子，死别复生逢，泪侵当年血。“",{},
			{假人信息.模型,假人信息.名称,"啊！……，妾身不知道上面说的是什么……",{},
			{玩家模型,玩家名称,"你怎么可能不知道！",{},
			{假人信息.模型,假人信息.名称,"日前切身得一奇梦，梦见十八年前，龙王搭救了一个被奸贼所害的陈姓公子，少侠若有空，可否去龙宫去替妾身问问？",{},
			{玩家模型,玩家名称,"看来不帮她这个忙，殷小姐是不会对我说什么的",{},
			nil}}}}}
			角色信息.剧情={主线=3,编号 = 11,地图 = 1110,进度 = 24,附加={战斗 = 1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 24 then
			wb={假人信息.模型,假人信息.名称,"走过路过不要错过，千古难逢的宝贝啊，不买都来看一看啊。"}
			wb2 = {玩家模型,玩家名称,"什么好东西啊？",{},
			{假人信息.模型,假人信息.名称,"嘿嘿，是你闻所未闻见所未见的宝贝----定颜珠！你若出的起5万两，我就让给你。",{"呸，这么贵，一颗珠子哪里值这么多？","你认为我买不起啊，5万两算什么，我要了","我手头不宽裕，暂时不要了"},
			nil}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 25 then
			wb={假人信息.模型,假人信息.名称,"好想再见一次会眨眼的金鲤啊……"}
			wb2 = {玩家模型,玩家名称,"渔翁老伯，你在说笑吧，世上怎么会有会眨眼的鲤鱼？",{},
			{假人信息.模型,假人信息.名称,"这就是少侠你孤陋寡闻了，十八年前，我就曾捕到过一尾会眨眼的金色鲤鱼，提取街上叫卖时，被陈姓公子一贯钱买了，还说鱼蛇眨眼，必非等闲之物，拿去放生了。后来听人说那种鱼只有龙宫里才有，非常珍贵，早知道就不卖了",{},
			{玩家模型,玩家名称,"哦，世上真有如此奇特之物，我也想去龙宫看看。",{},
			{假人信息.模型,假人信息.名称,"去龙宫必须要拿到辟水宝珠，那可是神仙才有的东西/",{},
			nil}}}}
			角色信息.剧情={主线=3,编号 = 10,地图 = 1110,进度 = 26,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 26 then
			wb={假人信息.模型,假人信息.名称,"来者何人，所求何事？"}
			wb2 = {玩家模型,玩家名称,"哇，这神仙果然不是盖的耶，连我的来意都才得到！我是来求辟水宝珠的。",{},
			{假人信息.模型,假人信息.名称,"什么？辟水宝珠？我还以为你是来求财求子求姻缘的，没想到是这么复杂的东西……这个么，我可不能轻易答应你哦！",{},
			{玩家模型,玩家名称,"我要怎么样才可以拿到辟水宝珠呢？",{},
			{假人信息.模型,假人信息.名称,"其实也简单，我在这里每天接受凡人的香火，供奉的无非是些馒头点血，吃得我身材都变差了，你若能找点臭豆腐给我吃，我就把辟水宝珠给你。",{},
			{玩家模型,玩家名称,"神仙都这么馋嘴……",{},
			{假人信息.模型,假人信息.名称,"你说什么？",{},
			{玩家模型,玩家名称,"没什么，嘻嘻我说我马上就去",{},
			nil}}}}}}}
			角色信息.剧情={主线=3,编号 = 10,地图 = 1110,进度 = 27,附加={物品="臭豆腐",战斗 = 1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 27 and 角色信息.剧情.给予 then
			if 角色信息.剧情.给予 then
				wb={假人信息.模型,假人信息.名称,"嗯，真好吃，好想再喝点梅花酒啊~~"}
				wb2 = {玩家模型,玩家名称,"你不会把，还想喝酒？我的辟水宝珠呢？",{},
				{假人信息.模型,假人信息.名称,"我说我想喝点梅花酒，看少侠你印堂饱满冰雪聪明，该不会是个聋子吧。",{"好吧我带来梅花酒之后你一定要把辟水宝珠给我哦","你耍赖，快交出辟水宝珠，别逼我出手！","我就是路过看看"},
				nil}}
				return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
			else
			   wb={假人信息.模型,假人信息.名称,"我说我想吃臭豆腐，看少侠你印堂饱满冰雪聪明，该不会是个聋子吧。"}
			   return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
			end
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 28 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"嗯，真好吃~~"}
			wb2 = {玩家模型,玩家名称,"这回该把，辟水宝珠给我了把！",{},
			{假人信息.模型,假人信息.名称,"给你收好了，这可是很珍贵的东西！",{},
			nil}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"辟水宝珠")
			角色信息.剧情={主线=3,编号 = 2,地图 = 1116,进度 = 29,附加={战斗 = 1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 29 then
			wb={假人信息.模型,假人信息.名称,"来者何人，通报姓名！"}
			wb2 = {玩家模型,玩家名称,"听说龙宫里有一种会眨眼睛的金色鲤鱼，想来捉条当宠物。还有……",{},
			{假人信息.模型,假人信息.名称,"大胆！竟敢捉我们龙王，还说要回去当宠物！此哦那个地们给我上，扁他！",{"啊~我怎么知道那条鱼是你们龙王变的……听我说完……","哼，英雄好汉不与龟斗，我撤退"},
			nil}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 30 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"定颜珠！太谢谢了，若不是你帮我找到它，我真不知道怎么和都领交代！我该怎么谢谢你呢？"}
			wb2 = {玩家模型,玩家名称,"呵呵，小事一桩，无足挂齿。",{},
			{假人信息.模型,假人信息.名称,"那么……请问阁下能否再帮我一个忙？",{},
			{玩家模型,玩家名称,"愿意效劳！（这时候是不是该去重新找下山神呢？）",{},
			nil}}}
			角色信息.剧情={主线=3,编号 = 10,地图 = 1110,进度 = 31,附加={物品="辟水宝珠"}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"碧水神石（绿）")
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 31 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"宝贝用完了？看来你果然是个守信之人，这点钱你留做纪念吧！"}
			角色信息.剧情={主线=3,编号 = 28,地图 = 1110,进度 = 32,附加={物品="碧水神石（绿）"}}
			-- 角色信息:添加银子(100000,"玄奘的身世",1)
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 32 and 角色信息.剧情.给予 then
			wb={玩家模型,玩家名称,"夫人，你可识得此石：石上有字”未享画眉恩，先遇刘贼狼，一念凶杀起，数载隔阴阳“殷小姐，你可还记得十八年前的往事……"}
			wb2 = {假人信息.模型,假人信息.名称,"未有一时能忘……不错，妾身就是殷温娇，乃丞相殷开山之女，妾夫乃是当朝新科状元陈光蕊，当年妾夫带领家小江州赴任，不料却为宵小刘洪，李彪图谋打死，夺了官凭，霸妾为妻。妾为留陈家血脉，屈身侍贼十八年……",{},
			{玩家模型,玩家名称,"无耻刘贼！我要去杀了他！",{},
			{假人信息.模型,假人信息.名称,"英雄，刘洪奸诈狡猾，且又人多势众，你奈何不了他的，不如我们从长计议，让世人都明白他的真面目。",{},
			{玩家模型,玩家名称,"也好，你有什么办法？",{},
			{假人信息.模型,假人信息.名称,"和刘洪硬拼是行不通的，英雄，我与你一柄紫竹萧，你径到洪州万花店，当年我和光蕊夫君上任途中，曾留下张氏婆婆那里，如今不知生死，英雄帮我去探望一下吧。",{},
			{玩家模型,玩家名称,"我一定会找到婆婆的",{},
			nil}}}}}}
			-- 玩家数据[数字id].道具:给予道具(数字id,"萧")
			角色信息.剧情={主线=3,编号 = 3,地图 = 1110,进度 = 33,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 33 then
			wb={假人信息.模型,假人信息.名称,"客观是打尖还是住店？"}
			wb2 = {玩家模型,玩家名称,"我是来找人的，昔年江州陈客官的母亲张氏住在你店中，你可还记得？",{},
			{假人信息.模型,假人信息.名称,"陈……没，没这人……",{},
			{玩家模型,玩家名称,"哦……是么？告诉你我就是人称一树梨花压海棠玉树临风赛潘安名动全球的金牌捕头一一追命无情冷血铁手。你可听过我的名号？",{},
			{假人信息.模型,假人信息.名称,"这么长的名字，没，没听过……",{},
			{玩家模型,玩家名称,"有内线告诉我那位张婆婆是被你们这黑点的人杀害，还做成人肉叉烧包……有无此事！",{},
			{假人信息.模型,假人信息.名称,"哇，这么恐怖的事情我们怎么会做，张婆婆不是我们害的啊……",{},
			{玩家模型,玩家名称,"那是谁害的？说！你要是不说我就抓住你挤破你的肚皮把你的肠子扯出来再用你的肠子勒住你的脖子用力一拉……",{},
			{假人信息.模型,假人信息.名称,"饶命啊，是江州令刘洪让我往张婆婆茶饭里下毒的，小的不敢不从啊，不过我还是一念之仁只放了一半毒药，他现在被毒瞎了，每天在街头乞讨度日……",{},
			{玩家模型,玩家名称,"不要再说了！等我找到婆婆，决不会放过刘洪！",{},
			nil}}}}}}}}}
			角色信息.剧情={主线=3,编号 = 30,地图 = 1110,进度 = 34,附加={物品="萧"}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 34 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"好心人，赏我孤老婆子一口饭吃吧~"}
			wb2 = {玩家模型,玩家名称,"想不到陈光蕊一家被刘洪害的如此凄惨，张婆婆，是你儿媳殷温娇让我来寻你的。",{},
			{假人信息.模型,假人信息.名称,"我已经快……饿……死……了……",{},
			{玩家模型,玩家名称,"婆婆看起来已经几天没吃东西了，我去买烤鸭给他吃。",{},
			nil}}}
			角色信息.剧情={主线=3,编号 = 30,地图 = 1110,进度 = 35,附加={物品="烤鸭"}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 35 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"嗯……好饱，谢谢你年轻人，你刚才说谁让你来寻我的？~"}
			wb2 = {玩家模型,玩家名称,"是你儿媳殷温娇让我来寻你的。",{},
			{假人信息.模型,假人信息.名称,"儿子？儿媳？我没有儿子，十八年了，我就当从未生过那个背义忘恩的畜生！",{},
			{玩家模型,玩家名称,"婆婆，你儿并非背义忘恩，他十八年前被奸贼谋死，你儿媳也被贼人强占为妻，就是婆婆你的眼睛，也都是被贼子刘洪下了毒才……这是你儿媳的紫竹萧一柄婆婆你摸摸看……",{},
			{假人信息.模型,假人信息.名称,"果然是当初我送给儿媳的竹萧。我只当光蕊得了功名就忘了老母，谁知他被贼人所害……我的儿啊……",{},
			{玩家模型,玩家名称,"婆婆不要悲伤，你儿子被龙王所救，现在龙宫做都领之位，你还有个孙子在金山寺出家。",{},
			{假人信息.模型,假人信息.名称,"太好了，我儿还活着，我还有了个孙子……可惜我老婆子眼睛不行了，不能见到他们的面……",{},
			{玩家模型,玩家名称,"婆婆我一定会找城里最好的医生只好你的眼睛。",{},
			{假人信息.模型,假人信息.名称,"不管用的，我听人家说只有龙宫的丁香水洗眼才能治好，凡人又怎么去得了龙哦给你呢？",{},
			{玩家模型,玩家名称,"这有何难，我马上就去取丁香水",{},
			nil}}}}}}}}}
			角色信息.剧情={主线=3,编号 = 5,地图 = 1116,进度 = 36,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 36 then
			wb={假人信息.模型,假人信息.名称,"我家都领上次拜托阁下送的信送到了么？"}
			wb2 = {玩家模型,玩家名称,"在下幸不辱命，不但送到了信，还找到了陈都领的母亲张氏，不过张氏亦为刘洪毒瞎了双眼，只有看龙宫的丁香水洗眼睛才能够医治，还望丞相上禀都领，不吝赐药。",{},
			{假人信息.模型,假人信息.名称,"呵呵，这个不难只盼英雄帮陈家报的大仇，让我们都领早日还魂与家人相见。",{},
			nil}}
			-- 玩家数据[数字id].道具:给予道具(数字id,"丁香水")
			角色信息.剧情={主线=3,编号 = 30,地图 = 1110,进度 = 37,附加={物品="丁香水"}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 37 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"啊，我的眼睛好了，这……恩人，太谢谢了！"}
			wb2 = {玩家模型,玩家名称,"婆婆你先去万花店再住二日，我现在回去吧你的消息告诉殷温娇，等我帮光蕊报了大仇，你们全家就可以团圆了",{},nil}
			角色信息.剧情={主线=3,编号 = 28,地图 = 1110,进度 = 38,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 38 then
			wb={假人信息.模型,假人信息.名称,"殷小姐，我找到婆婆的消息了。她现在一切安好，只等大仇得雪，你们全家团聚的一天。"}
			wb2 = {玩家模型,玩家名称,"太好了，谢谢你，现在我再写一封书信与你，径到皇城之内殷开山丞相家。将此书递与丞相，叫他奏上唐王，统领人马，擒杀刘贼，与夫报仇，那时也能救得妾身出来了。",{},
			{玩家模型,玩家名称,"事不宜迟，我马上动身。",{},
			nil}}
			角色信息.剧情={主线=3,编号 = 1,地图 = 1049,进度 = 39,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 39 then
			wb={假人信息.模型,假人信息.名称,"殷丞相，你快请皇上发兵去救你女儿温娇！这是她给你的亲笔书信！"}
			wb2 = {假人信息.模型,假人信息.名称,"岂有此理！我要杀了……啊，夫人，你怎么了？",{},
			nil}
			角色信息.剧情={主线=3,编号 = 2,地图 = 1049,进度 = 40,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 40 then
			wb={假人信息.模型,假人信息.名称,"我心爱的女儿啊……气死我了……我的老毛病又发作了……哎呦……"}
			wb2 = {玩家模型,玩家名称,"夫人！这，这可怎么办~",{},
			{假人信息.模型,假人信息.名称,"我这病，只有金香玉才可以医治，寻常药店里是没得卖的……",{},
			nil}}
			角色信息.剧情={主线=3,编号 = 1,地图 = 1193,进度 = 41,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 41 then
			wb={假人信息.模型,假人信息.名称,"瞧一瞧看一看啊，金香玉补血养颜了啊，减肥美容家庭必备了啊~~"}
			wb2 = {玩家模型,玩家名称,"你这里有金香玉？",{},
			{假人信息.模型,假人信息.名称,"是啊，北俱芦洲进的特效药，少侠你来一颗？五万二不二价！",{"不管多少钱，我要了！","我手头不宽裕，暂时不要了"},
			nil}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 42 and 角色信息.剧情.给予 then
			wb={玩家模型,玩家名称,"夫人，快请服药吧！"}
			wb2 = {假人信息.模型,假人信息.名称,"哎呦，这下舒服多了……相爷，快设法救我们的女儿啊……",{},
			{"魏征","殷丞相","夫人你休得烦恼，我即刻写折，请这位少侠代奏主上，亲自统兵剿贼，定要救出女儿，与女婿报仇。",{},
			{玩家模型,玩家名称,"事不宜迟，丞相请块动笔！",{},
			nil}}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"奏折")
			角色信息.剧情={主线=3,编号 = 5,地图 = 1044,进度 = 43,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 43  then
			wb={假人信息.模型,假人信息.名称,"……"}
			角色信息.剧情={主线=3,编号 = 130,地图 = 1001,进度 = 44,附加={}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 44  then
			wb={假人信息.模型,假人信息.名称,"哎，自从能臣魏征杀了那条孽龙，皇宫里整夜听见鬼哭神嚎，伸冤索命，不光皇上吓得龙体不适，连我们这些宫人也个个难以安寝。"}
			wb2 = {假人信息.模型,假人信息.名称,"皇上已经好几天睡不好觉了，都是被那个什么鬼闹的。",{},nil}
			角色信息.剧情={主线=3,编号 = 1,地图 = 1044,进度 = 45,附加={物品="奏折"}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 45 and 角色信息.剧情.给予 then
			wb={假人信息.模型,假人信息.名称,"皇上龙体欠安，命我督办内务，你持折觐见所为何事？"}
			wb2 = {玩家模型,玩家名称,"昔有状元陈光蕊，带领家小赴任江州被宵小刘洪打死，刘洪复又霸占其妻，假冒光蕊为官多年，事属异变，我今奏乞陛下立发人马，剿除贼寇！",{},
			{假人信息.模型,假人信息.名称,"竟有这等事，我调拨御林军六万，着你即刻领旨出朝，督兵灭寇！",{},
			{玩家模型,玩家名称,"谢丞相，我即刻动身！",{},
			nil}}}
			角色信息.剧情={主线=3,编号 = 1,地图 = 1168,进度 = 46,附加={战斗=1}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 46  then
			wb={玩家模型,玩家名称,"善恶到头终有报，刘洪你十八年前因为一念之贪害得陈光蕊家破人亡之时，可曾想过有今天！"}
			wb2 = {假人信息.模型,假人信息.名称,"哼，江州兵马尽归我管辖，区区六万御林军，能奈我何！",{},
			{玩家模型,玩家名称,"没想到你死到临头还不知悔改，殷丞相已奉圣旨收了你的兵权，着我为督军来拿你归案！",{"拿下刘洪！","还是别跟当官的作对，我闪！"},
			nil}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 47  then
			wb={假人信息.模型,假人信息.名称,"哇哈哈哈……大王我杀人吃人百十年，从来没受过什么法律！"}
			wb2 = {玩家模型,玩家名称,"你……原来是个骷髅怪成精，你根本就不是人！",{},
			{假人信息.模型,假人信息.名称,"不错，当年本大王化作人形游戏人间，想人肉吃时恰与陈光蕊同舟，正逢刘洪贪恋陈妻美色，我便怂恿他将陈光蕊打死，可惜他入水便沉，大王我只得有打死书童解馋，你既然来问此事，不如入我腹中和那书童相会相会去吧。",{"无耻妖邪，天道不容！","最怕妖魔鬼怪了，我逃"},
			nil}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 48  then
			wb={假人信息.模型,假人信息.名称,"李彪大王饶命啊，大侠救我，我愿改过自新，千万不要让这妖怪吃了我……"}
			xx = {"你若真的愿意改过，我就救你一命！李彪看招！","哼，你们自行残杀去我才不管呢"}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 49  then
			wb={玩家模型,玩家名称,"刘洪，你既然愿意伏罪，就同我去见陈光蕊一家！"}
			wb2 = {假人信息.模型,假人信息.名称,"大侠，我当年因为一念之贪受妖怪控制，精气已经被他吸取的差不多了，这也是我应得的报应，只是我江州家中还有一位老母，年纪打了没人供养，希望大侠能够去照顾照顾她，我也就瞑目了…………",{},
			{玩家模型,玩家名称,"哎，一念之贪，落到如今下场，真是可怜可叹……",{},
			nil}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"江州府大印")
			角色信息.剧情={主线=3,编号 = 28,地图 = 1110,进度 = 50,附加={物品="江州府大印"}}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 50 and 角色信息.剧情.给予 then
			wb={玩家模型,玩家名称,"殷小姐，昔年的凶手刘洪李彪都已经伏法了！"}
			wb2 = {假人信息.模型,假人信息.名称,"想当初只因恶人心魔一念，我全家离散哀苦十八年，如今终于大仇得报……我夫昨日托梦于我，说他近日将重返阳世，婆婆已经接来身边，我现将亲手抄录的一本般若波罗蜜心经与你，请你带与我那出家为僧的孩儿，告诉他家人平安团聚，让他安心修法，渡化世人……",{},
			{玩家模型,玩家名称,"我一定会转告他的。",{},
			nil}}
			角色信息.剧情={主线=3,编号 = 2,地图 = 1153,进度 = 51,附加={物品="般若多罗蜜心经"}}
			-- 玩家数据[数字id].道具:给予任务道具(数字id,"般若多罗蜜心经")
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 51 and 角色信息.剧情.给予 then
			wb={玩家模型,玩家名称,"玄奘法师，我已帮你报仇了，还有这是你母亲交给你的，她还让我告诉你家人一切安好，让你安心修法，渡化世人……"}
			wb2 = {假人信息.模型,假人信息.名称,"玄奘家仇得报，复知父母平安，更欲何求？从今以后发愿立意安禅：原以此功德，庄严佛净土，上报四重恩，下济三途苦，若有见闻者，悉发菩提心。同生极乐国，尽报此一生。",{},
			nil}
			return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
		end
	-- -- elseif 剧情主线 == 12 and 角色信息.等级 >= 135 then
	-- -- 	if ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 1 then
	-- -- 		第一页(1,"万物变化，虚而不屈，相生相克，无穷无已，欲强者先弱，欲扬者先抑，欲刚者先柔，欲取者先与，欲升者先降，欲张者先翕。入于化境之后会变的暂时弱些，但是不久你就可以达到更强的境界。")
	-- -- 		 下一页(2,"如果你准备好了，可以去找去明长老来指导你如何进入化境！等你通过了所有的修炼后再来找我，我会带你入于化境.")
	-- -- 		角色信息.剧情={主线=12,编号 = 2,地图 = 1528,进度 = 2,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 2 then
	-- -- 		第一页(1,"你现在已经是绝顶高手，但是如此求得称雄三界，却也如水中捞月。我这里有易经洗髓，脱胎换骨之门，学成之后，可入化境，天下无敌，不知道你愿意学不？",{"我愿意","我还是再想想"})
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 3 then
	-- -- 		 第一页(1,"欲入化境，必先明道。为道者将强先弱，欲扬先抑。入于化境之后会变的暂时弱些，但是不久你就可以达到更强的境界，携带更加厉害的名唤兽，拥有更有威力的法术和更强的修炼。既然我传授给你这些道理，你也应该为我道门做些事情。我这几日要炼仙山需要古代瑞兽和凤凰做药引，你去取些来。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1113,进度 = 4,附加={宠物="古代瑞兽"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 4 then
	-- -- 		第一页(1,"找到药引了吗？",{"你看看是不是这些","还没有呢"})
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 5 then
	-- -- 		第一页(1,"找到药引了吗？",{"你看看是不是这些","还没有呢"})
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 6 then
	-- -- 		第一页(1,"恩，听太上老君说起你潜心向道，有望打破玄关，登入化境。朕要考验你的诚心和能力，现在有几件事需要你去做。不知道你能行否？",{"我可以","我在考虑下"})
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 7 then
	-- -- 		第一页(1,"既是玉帝有旨，我就给你吧。不可将此物传给别人。")
	-- -- 		玩家数据[数字id].道具:给予任务道具(数字id,"避火诀")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1251,进度 = 8,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 8 then
	-- -- 		第一页(1,"紫金冠？好说好说，此物现在宝库之中，少侠去找就是了。")
	-- -- 		角色信息.剧情={主线=12,编号 = 4,地图 = 1251,进度 = 9,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 9 then
	-- -- 		第一页(1,"少侠要来寻找紫金冠？只是此物在宝库深处，还请少侠自行将它移出来吧！")
	-- -- 		玩家数据[数字id].道具:给予任务道具(数字id,"紫金冠")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1117,进度 = 10,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 10 then
	-- -- 		第一页(1,"虽然有王帝旨意，但是此物乃龙、真海之宝，不可轻与。你可章玉龙换来镇海。否则飓风狂浪无法平息，毐人也吃不消。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1117,进度 = 11,附加={物品="玉龙"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 11 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"这是镇海针，你要好好护送它到玉帝那！")
	-- -- 		玩家数据[数字id].道具:给予任务道具(数字id,"镇海针")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1150,进度 = 12,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 12 then
	-- -- 		第一页(1,"你晚来了一步，开山钺被三太子借走了，他此时应该已经用完了，你直接找他去要吧。")
	-- -- 		角色信息.剧情={主线=12,编号 = 5,地图 = 1112,进度 = 13,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 13 then
	-- -- 		第一页(1,"来得正好，不用再跑一趟了，你就拿去吧。")
	-- -- 		玩家数据[数字id].道具:给予任务道具(数字id,"开山钺")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1147,进度 = 14,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 14 then
	-- -- 		第一页(1,"呵呵。想不到竟有人朝我这里讨宝贝,就是掀翻了老君八卦炉的孙猴子，想要我的人参果也不能白给。不过既然有玉帝旨意，就破例赏你这个面子。我现在炼丹到紧要处，缺两味药材五龙丹和佛光舍利子，你去取来，我自然把炼金鼎给你。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1147,进度 = 15,附加={物品="五龙丹"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 15 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"不错不错，帮我找到了五龙丹，另一个呢？")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1147,进度 = 16,附加={物品="佛光舍利子"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 16 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"恩,好！这是你要的炼金鼎！")
	-- -- 		玩家数据[数字id].道具:给予任务道具(数字id,"炼金鼎")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1141,进度 = 17,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 17 then
	-- -- 		第一页(1,"真是不巧呀。我将此物给了我的镇山大神黑熊精砍竹子去了。他们很厉害，看看你能不能要到了。")
	-- -- 		角色信息.剧情={主线=12,编号 = 0,地图 = 1140,进度 = 18,附加={野外战斗=110047}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 19 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"不错不错，拿到了避火诀，紫金冠呢？")
	-- -- 		角色信息.剧情={主线=12,编号 = 3,地图 = 1112,进度 = 20,附加={物品="紫金冠"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 20 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"不错不错，拿到了紫金冠，镇海针呢？")
	-- -- 		角色信息.剧情={主线=12,编号 = 3,地图 = 1112,进度 = 21,附加={物品="镇海针"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 21 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"不错不错，拿到了镇海针，开山钺呢？")
	-- -- 		角色信息.剧情={主线=12,编号 = 3,地图 = 1112,进度 = 22,附加={物品="开山钺"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 22 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"不错不错，拿到了开山钺，炼金鼎呢？")
	-- -- 		角色信息.剧情={主线=12,编号 = 3,地图 = 1112,进度 = 23,附加={物品="炼金鼎"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 23 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"不错不错，拿到了炼金鼎，修篁斧呢？")
	-- -- 		角色信息.剧情={主线=12,编号 = 3,地图 = 1112,进度 = 24,附加={物品="修篁斧"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 24 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"要你找的这几样宝贝暗含五行之妙，可以激发你的潜力，如果再加上地藏王的不死壤，正应了六合之数。可惜自从大禹治水后，就再也没有人见过不死壤了。天庭已经没有什么好教你的了，你再去人界修炼妙法吧。")
	-- -- 		角色信息.剧情={主线=12,编号 = 5,地图 = 1044,进度 = 25,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 25 then
	-- -- 		第一页(1,"听说你将要博采诸家之长，将武学发挥到出神入化的境界。你需要证明你的能力，获得各大门派的掌门人的推荐。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1054,进度 = 26,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 26 then
	-- -- 		第一页(1,"要获得俺的推荐，很简单。有一横行霸道的土匪在大唐国境地方胡作非为，不知道修了什么妖术，伤了俺门下不少弟子。俺本来准备亲自出马搞享他。现在正好就交给你办吧。")
	-- -- 		角色信息.剧情={主线=12,编号 = 0,地图 = 1110,进度 = 27,附加={野外战斗=110048}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 28 then
	-- -- 		第一页(1,"这是你的推荐信，收好吧。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1043,进度 = 29,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 29 then
	-- -- 		第一页(1,"阿弥陀佛。要获得佛门推荐，施主可章此枚丹药去傲来国驱除:瘟疫。这药功效甚为奇特，只要轻轻一点病人,病人就痊愈了。")
	-- -- 		角色信息.剧情={主线=12,编号 = 10,地图 = 1092,进度 = 30,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 30 then
	-- -- 		第一页(1,"我以为自己必死无疑了，没想到遇到少侠你了！我会天天求神拜佛，保佑你长命百岁。")
	-- -- 		角色信息.剧情={主线=12,编号 = 7,地图 = 1092,进度 = 31,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 31 then
	-- -- 		第一页(1,"我以为自己必死无疑了，没想到遇到少侠你了！我会天天求神拜佛，保佑你长命百岁。")
	-- -- 		角色信息.剧情={主线=12,编号 = 9,地图 = 1092,进度 = 32,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 32 then
	-- -- 		第一页(1,"我以为自己必死无疑了，没想到遇到少侠你了！我会天天求神拜佛，保佑你长命百岁。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1154,进度 = 33,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 33 then
	-- -- 		第一页(1,"我神木林善用药物激发人类与自然自米系，要想得到神木林的推荐，就要看看你对药物的熟悉程度。",{"我对药物向来熟悉，请您出题","我这就去学习关于药物的知识。"})
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 34 then
	-- -- 		第一页(1,"我女儿村轻功身法天下第一，徒弟个个身轻如燕，所以与敌争斗中应先发制人。想获得我的推荐，得看看你的轻功功夫。去！依次给这4个人带封口信，然后再回到我这里。")
	-- -- 		角色信息.剧情={主线=12,编号 = 5,地图 = 1070,进度 = 35,附加={禁止飞行=1}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 35 then
	-- -- 		第一页(1,"孙婆婆的口信带给长安城的二宝")
	-- -- 		角色信息.剧情={主线=12,编号 = 133,地图 = 1001,进度 = 36,附加={禁止飞行=1}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 36 then
	-- -- 		第一页(1,"孙婆婆的口信带给建邺城的吹牛王")
	-- -- 		角色信息.剧情={主线=12,编号 = 3,地图 = 1501,进度 = 37,附加={禁止飞行=1}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 37 then
	-- -- 		第一页(1,"孙婆婆的口信带给傲来国的九头精怪")
	-- -- 		角色信息.剧情={主线=12,编号 = 7,地图 = 1092,进度 = 38,附加={禁止飞行=1}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 38 then
	-- -- 		第一页(1,"跟孙婆婆说我收到她的口信了")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1143,进度 = 39,附加={禁止飞行=1}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 39 then
	-- -- 		第一页(1,"瞧你那臭汗淋漓，气喘吁吁的样子，哪有一点轻灵的样子？回去每天跑几圈吧,等什么时候跑到身轻如燕，也就大功告成了。")
	-- -- 		角色信息.剧情={主线=12,编号 = 5,地图 = 1044,进度 = 40,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 40 then
	-- -- 		第一页(1,"恩！这样快就获得了各大门派的推荐，真是自古英雄出少年，看来少侠前途不可限量。可惜有一门派，虽也在寰宇之中，却傲世独立，不染红尘。他的道观在灵台方寸山，斜月三星洞。少侠如果能够得他密传的修心封魔之法，才算将人界的功夫学全了。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1137,进度 = 41,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 41 then
	-- -- 		第一页(1,"心魔其实是你的影子。只有心中不起争强好胜之念，则心魔息，心魔息则外魔自灭。这就是修心封魔的要决。你记住了吗？现在对于武学，你已经“三分天下得其二”。可持此书信去见魔界大总管地藏王大菩萨，他会教你怎么做。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1124,进度 = 42,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 42 then
	-- -- 		第一页(1,"魔族之中山头林立，门派复杂，他们桀骜不驯，多不服我管辖。但是要获得不死壤，必须征得他们的同意，否则他们会从中作梗。当他们都同意后，我自然会指引你去取不死H。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1156,进度 = 43,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 43 then
	-- -- 		第一页(1,"我这无底洞中，常年不见天日，整日要蜡烛常明，眼看蜡烛又不够了。这样吧,给夫人我带来一个蜡烛来，不死壤就是你的了。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1156,进度 = 44,附加={物品="蜡烛"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 44 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"这蜡烛本是寻常之物，可无底洞中却比什么不死壤珍贵的多，此中还是有些道理")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1134,进度 = 45,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 45 then
	-- -- 		第一页(1,"我寨子里这么多飞离走兽，需要找个头领来管管。帮俺捉一只古代瑞兽，一只凤凰，俺就同意你去取不死壤。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1134,进度 = 46,附加={宠物="古代瑞兽"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 46  then
	-- -- 		第一页(1,"我要的凤凰和瑞兽抓到了吗？",{"抓到了","还没呢"})
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 47 then
	-- -- 		第一页(1,"我要的凤凰和瑞兽抓到了吗？",{"抓到了","还没呢"})
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 48 then --牛妖
	-- -- 		第一页(1,"俺最近被两个夫人弄的怪闷的，你跟俺手下的小妖打一架，让俺也解解闷。")
	-- -- 		战斗准备类:创建战斗(数字id+0,110046,剧情地图..剧情主线..剧情进度)
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 49 then
	-- -- 		第一页(1,"很好，那你就先弄件回龙摄魂镖给我消遣下。")
	-- -- 		角色信息.剧情={主线=12,编号 = 2,地图 = 1144,进度 = 50,附加={物品="回龙镊魂镖"}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 50 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"恩，东西不错。我没什么意见了，反正不死壤我也没兴趣。")
	-- -- 		角色信息.剧情={主线=12,编号 = 1,地图 = 1124,进度 = 51,附加={}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 51 then
	-- -- 		第一页(1,"恩，他们都同意了，真不容易呀。接下来你要去鬼域取不死壤。那土壤会不停地再生，是我地府弟子死而复生的源泉。当年鲧盗出此物，用以堵塞洪水，结果酿成大祸。所以天帝命我将它放在鬼域的最深处的一个小宝箱内，但那里可是由鬼将看守。你可要小心呀。")
	-- -- 		角色信息.剧情={主线=12,编号 = 0,地图 = 1202,进度 = 52,附加={野外战斗=110049}}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 53 and 角色信息.剧情.给予 then
	-- -- 		第一页(1,"恩，能够融会各大门派妙法，集齐五行神器，看来你的确不简单。但欲入化境,避开天地造化，五行轮转，必遭忌恨。故天宫四象五行二十八星宿布下玄妙阵法，考验你是否有个资格。此五阵按周天变化，各含五行之妙法，如果想知道此密法，可以去问问太上老君。",{"我准备好要破阵法了","还是等等"})
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	-- -- 	elseif ID == 剧情地图 and 编号== 剧情NPC编号 and 剧情进度 == 54 then
	-- -- 		第一页(1,"恭喜少侠通过考验,在入于化境之前我想问一下你的选择,你可以选择暂时不降低你的修炼等级直接入于化境,以后可以去找帮派的“修炼指导人”来降低1级修炼等级增加相应1级的修炼上限。或者直接降低相应的修炼等级入于化境,请少侠选择.",{"我暂时保留我的修炼等级，请带我入于化境吧","我已经考虑好了，请降低我的修炼等级入于化境","让我在考虑考虑"})
	-- -- 		角色信息.剧情.附加 = {}
	-- -- 		return{wb[1],wb[2],wb[3],xx,nil,nil,wb2,角色信息.剧情}
	--  	-- end
	end
end

function 取地图师傅(ID)
	local 师门 = nil
	if ID == 1043 then
		师门 = "空度禅师"
	elseif ID == 1054 then
		师门 = "程咬金"
	elseif ID == 1117  then
		师门 = "东海龙王"
	elseif ID == 1137  then
		师门 = "菩提祖师"
	elseif ID == 1124  then
		师门 = "地藏王"
	elseif ID == 1112 then
		师门 = "李靖"
	elseif ID == 1145 then
		师门 = "牛魔王"
	elseif ID == 1141 then
		师门 = "观音姐姐"
	elseif ID == 1147 then
		师门 = "镇元子"
	elseif ID == 1134 then
		师门 = "大大王"
	elseif ID == 1144 then
		师门 = "白晶晶"
	elseif ID == 1150 then
		师门 = "二郎神"
	elseif ID == 1154 then
		师门 = "巫奎虎"
	elseif ID == 1156 then
		师门 = "地涌夫人"
	elseif ID == 1143 then
		师门 = "孙婆婆"
	elseif ID == 1251 then
		师门 = "齐天大圣"
	elseif ID == 2580 then
        师门 = "蚩尤幻影"
	end
	return 师门
end

-- 领取任务模块
function 添加支线任务()
	local fsdf={}
	local 数据 =取支线描述(角色信息.支线任务.主线,角色信息.支线任务.进度,角色信息.支线任务.分支)
	--table.print(数据)
	if 数据 then
		fsdf[#fsdf+1]=数据
		if 角色信息["剧情"] and 取剧情是否追踪(角色信息["剧情"].主线) then
			local 数据 = 取剧情描述(角色信息["剧情"]["主线"], 角色信息["剧情"]["进度"], 角色信息["剧情"]["分支"])
			fsdf[#fsdf+1]=数据
			__主显["更新假人头顶图标"](__主显)
		end
		__UI界面["界面层"]["右上角"]["任务栏"]["追踪列表"]["重置"](__UI界面["界面层"]["右上角"]["任务栏"]["追踪列表"], fsdf)
	end
end
function 取支线描述(主线,进度,分支)
	if 主线 == 1 then
		if 进度 == 1 then
			return {"支线-雁塔试炼","升级到20级后，在大雁塔入口".."#Y/ht|雁塔地宫守卫*长安城*npc查询/雁塔地宫守卫".."#W处领取支线任务"}
		end
	end
end


function 取剧情描述(主线,进度,分支)
	if 主线 == 1 then
		if 进度 == 1 then
			return {"缘起东海湾","前往桃源村"}
		elseif 进度 == 2 then
			return {"缘起东海湾","找到".."#Y/ht|玄大夫*桃源村*npc查询/玄大夫#W，请他治疗自己的伤势。"}
		elseif 进度 == 3 then
			return {"缘起东海湾","去".."#Y/ht|谭村长*桃源村*npc查询/谭村长".."#W家看看,是怎么回事?"}
		elseif 进度 == 4 then
			  return {"缘起东海湾","去找".."#Y/ht|孙猎户*桃源村*npc查询/孙猎户#W挑一把趁手的兵器."}
		elseif 进度 == 5 then
			  return {"缘起东海湾","拿到了趁手的武器,攻击".."#Y/ht|野猪*桃源村*npc查询/野猪".."#W试试吧!"}
		elseif 进度 == 6 then
			   return {"缘起东海湾","第一次有了召唤兽,再攻击".."#Y/ht|野猪*桃源村*npc查询/野猪".."#W试试."}
		elseif 进度 == 7 then
			local 种族=角色信息.种族.."族使者"
		--	return {"缘起东海湾","前往建邺城找#Y/"..种族}
			return {"缘起东海湾","前往建邺城找".."#Y/ht|"..种族.."*建邺城*npc查询/"..种族.."#W问一下"}

		elseif 进度 == 8 then
			local 种族=角色信息.种族.."族使者"
			return {"拜师","在#Y/"..种族.."#W处开启拜师。"}
		elseif 进度 == 9 then
		  return {"缘起东海湾","找".."#Y/ht|雨画师*桃源村*npc查询/雨画师".."#W他好像又什么事情要告诉你。"}
		elseif 进度 == 10 then
		  return {"缘起东海湾","点按Tab键或点击界面上角放大镜图键地图左下方NPC".."#Y/ht|萍儿*桃源村*npc查询/萍儿".."#W就可以自动寻路。"}
		elseif 进度 == 11 then
		  return {"缘起东海湾","".."#Y/ht|桃源仙女*桃源村*npc查询/桃源仙女".."#W已经等候多时了，快点点击选项这就上船开启游湖之旅吧。"}
		elseif 进度 == 12 then
		  return {"缘起东海湾","和".."#Y/ht|夏大叔*桃源村*npc查询/夏大叔".."#W告个别吧！"}
		elseif 进度 == 13 then
		  return {"缘起东海湾","".."#Y/ht|夏大叔*桃源村*npc查询/夏大叔".."#W虽是对你多加鼓励，可是你的心中依然忐忑！"}
		end
	elseif 主线 == 2 then
		if 进度 == 1 then
			local 名字 = 取地图师傅(角色信息.剧情.地图)
			return {"拜师","快点和你师傅".."#R"..名字.."#W说说话，如果有缘的话就拜师吧。"}
		elseif 进度 == 2 then
			return {"商人的鬼魂","[新手主线]找".."#Y/ht|老孙头*建邺城*npc查询/老孙头".."#W问一下建邺城闹鬼的事！"}
		elseif 进度 == 3 then
			return {"商人的鬼魂","[新手主线]找".."#Y/ht|牛大胆*建邺城*npc查询/牛大胆".."#W去寻问做法事的事情。"}
		elseif 进度 == 4 then
			return {"商人的鬼魂","[新手主线]问问".."#Y/ht|王大嫂*建邺城*npc查询/王大嫂".."#W有没有美味可口的".."#Y/ht|获取途径*任务获取*bz提示/红烧鱼"}
		elseif 进度 == 5 then
			return {"商人的鬼魂","[新手主线]帮助".."#Y/ht|王大嫂*建邺城*npc查询/王大嫂".."#W寻找个#Y/ht|获取途径*建邺城药店购买*bz提示/熊胆"}
		elseif 进度 == 6 then
			return {"商人的鬼魂","[新手主线]将#R红烧鱼#W交给".."#Y/ht|牛大胆*建邺城*npc查询/牛大胆".."#W饱饱口福（ALT+G给予任务道具）"}
		elseif 进度 == 7 then
			return {"商人的鬼魂","[新手主线]询问".."#Y/ht|管家*建邺城*npc查询/管家".."#W关于李善人恶疾的事情"}
		elseif 进度 == 8 then
			return {"商人的鬼魂","[新手主线]找".."#Y/ht|马全有*建邺城*npc查询/马全友".."#W问问#R松风林芝"}
		elseif 进度 == 9 then
			return {"商人的鬼魂","[新手主线]将".."#Y/ht|获取途径*任务道具*bz提示/地狱灵芝".."#W交给".."#Y/ht|管家*建邺城*npc查询/管家#W（ALT+G给予任务道具）"}
		elseif 进度 == 10 then
			return {"商人的鬼魂","[新手主线]找".."#Y/ht|李善人*民居内室*npc查询/李善人".."#W打听一下恶鬼的来历"}
		elseif 进度 == 11 then
			return {"商人的鬼魂","[新手主线]去".."#Y/ht|行走路径*地图左面传送阵*bz提示/东海湾沉船底".."#W找到".."#Y/ht|商人的鬼魂*沉船内室*npc查询/商人的鬼魂"}
		elseif 进度 == 12 then
			return {"商人的鬼魂","[新手主线]告诉".."#Y/ht|李善人*民居内室*npc查询/李善人".."#W鬼魂的事情"}
		elseif 进度 == 13 then
			return {"商人的鬼魂","[新手主线]将事情告诉".."#Y/ht|老孙头*建邺城*npc查询/老孙头".."#W让他安心"}
		-- elseif 进度 == 14 then
		-- 	return {"商人的鬼魂","[新手主线]和".."#Y/ht|老孙头*建邺城*npc查询/老孙头".."#W告个别吧"}
		end
	elseif 主线 == 3 then
		if 进度 == 1 then
			return {"玄奘的身世","[主线剧情]在".."#Y/ht|南极仙翁*长寿村*npc查询/南极仙翁".."#W处领取“玄奘的身世”剧情任务。"}
		elseif 进度 == 2 then
			return {"玄奘的身世","[主线剧情]听说南极仙翁的白鹿幻化成".."#Y/ht|白鹿精*大唐境外*npc查询/白鹿精".."#W快去找找看吧！"}
		elseif 进度 == 3 then
			return {"玄奘的身世","[主线剧情]收服白鹿精后，将白鹿带回".."#Y/ht|南极仙翁*长寿村*npc查询/南极仙翁".."#W处。"}
		elseif 进度 == 4 then
			return {"玄奘的身世","[主线剧情]快去长安药店买一朵".."#Y/ht|获取途径*长安药店*bz提示/百色花".."#W来给".."#Y/ht|南极仙翁*长寿村*npc查询/南极仙翁".."#W吧。"}
		elseif 进度 == 5 then
			return {"玄奘的身世","[主线剧情]去长安的福寿店买一炷".."#Y/ht|获取途径*长安福寿店*bz提示/香".."#W再去大唐国境金山寺".."#Y/ht|酒肉和尚*大雄宝殿*npc查询/酒肉和尚".."#W处上香吧 。"}
		elseif 进度 == 6 then
			return {"玄奘的身世","[主线剧情]酒肉和尚出口伤人，去看看深受打击的".."#Y/ht|玄奘*大雄宝殿*npc查询/玄奘".."#W法师吧。"}
		elseif 进度 == 7 then
			return {"玄奘的身世","[主线剧情]去跟化生寺的小和尚".."#Y/ht|慧明*光华殿*npc查询/慧明".."#W打听".."#Y/ht|法明长老*光华殿*npc查询/法明长老".."#W的下落"}
		elseif 进度 == 8 and 分支 == 1 then
			return {"玄奘的身世","[主线剧情]去看看厢房内的".."#Y/ht|法明长老*光华殿*npc查询/法明长老".."#W是否安然无恙吧。"}
		elseif 进度 == 8 and 分支 == 2 then
			return {"玄奘的身世","[主线剧情]化生寺居然有劫难？快去".."#Y/ht|空慈方丈*化生寺*npc查询/空慈方丈".."#W处打探。"}
		elseif 进度 == 8 and 分支 == 3 then
			return {"玄奘的身世","[主线剧情]别让".."#Y/ht|白琉璃*大唐国境*npc查询/白琉璃".."#W逃走！快去夺回".."#Y/ht|获取途径*炼药*bz提示/佛光舍利子".."#W！"}
		elseif 进度 == 8 and 分支 == 4 then
			return {"玄奘的身世","[主线剧情]拿回了".."#Y/ht|获取途径*炼药*bz提示/佛光舍利子".."#W赶快回化生寺交给".."#Y/ht|空慈方丈*化生寺*npc查询/空慈方丈".."#W吧"}
		elseif 进度 == 9 then
			return {"玄奘的身世","[主线剧情]法明长老昏迷不醒，跟寺内照顾长老的".."#Y/ht|慧海*化生寺*npc查询/慧海".."#W和尚打听一下情况吧。"}
		elseif 进度 == 10 then
			return {"玄奘的身世","[主线剧情]人命关天，赶快请花果山的".."#Y/ht|侯医仙*花果山*npc查询/侯医仙".."#W来救长老一命！"}
		elseif 进度 == 11 then
			return {"玄奘的身世","[主线剧情]快去寻得".."#Y/ht|获取途径*野外或商会购买*bz提示/餐风饮露".."#W这味药材交给".."#Y/ht|侯医仙*花果山*npc查询/侯医仙".."#W！"}
		elseif 进度 == 12 then
			return {"玄奘的身世","[主线剧情]拿到灵药".."#Y/ht|获取途径*炼药*bz提示/定神香".."#W赶快回去化生寺厢房交给".."#Y/ht|法明长老*光华殿*npc查询/法明长老".."#W服用吧！"}
		elseif 进度 == 13 then
			return {"玄奘的身世","[主线剧情]把血书拿给".."#Y/ht|玄奘*大雄宝殿*npc查询/玄奘".."#W看看！"}
		elseif 进度 == 14 then
			return {"玄奘的身世","[主线剧情]".."#Y/ht|酒肉和尚*大雄宝殿*npc查询/酒肉和尚".."#W对玄奘法师做了什么！快去找他问问。"}
		elseif 进度 == 15 then
			return {"玄奘的身世","[主线剧情]快去普陀山请".."#Y/ht|青莲仙女*潮音洞*npc查询/青莲仙女".."#W炼制丹药吧！"}
		elseif 进度 == 16 then
			return {"玄奘的身世","[主线剧情]快去阴曹地府向".."#Y/ht|孟婆*阴曹地府*npc查询/孟婆".."#W讨一碗孟婆酒吧！（此环节需要等级 ≥40）"}
		elseif 进度 == 17 then
			return {"玄奘的身世","[主线剧情]去找地狱迷宫一层的".."#Y/ht|幽冥鬼*地狱迷宫一层*npc查询/幽冥鬼".."#W要一碗酒。"}
		elseif 进度 == 18 then
			return {"玄奘的身世","[主线剧情]去大唐国境江州民居内找他口中的".."#Y/ht|文秀*江州民居*npc查询/文秀".."#W要姑娘打听一下。"}
		elseif 进度 == 19 then
			return {"玄奘的身世","[主线剧情]把文秀的手镯交给".."#Y/ht|幽冥鬼*地狱迷宫一层*npc查询/幽冥鬼".."#W让他安心投胎吧！。"}
		elseif 进度 == 20 then
			return {"玄奘的身世","[主线剧情]跟".."#Y/ht|孟婆*阴曹地府*npc查询/孟婆".."#W讨了那碗孟婆酒走吧。"}
		elseif 进度 == 21 then
			return {"玄奘的身世","[主线剧情]把九转回魂丹和孟婆酒拿给".."#Y/ht|玄奘*大雄宝殿*npc查询/玄奘".."#法师服用吧。"}
		elseif 进度 == 22 then
			return {"玄奘的身世","[主线剧情]前往大唐国境江州衙门处找些".."#Y/ht|衙门守卫*大唐国境*npc查询/衙门守卫".."#打听打听情况。"}
		elseif 进度 == 23 then
			return {"玄奘的身世","[主线剧情]可以进衙门找".."#Y/ht|殷温娇*大唐国境*npc查询/殷温娇".."#W了。 "}
		elseif 进度 == 24 then
			return {"玄奘的身世","[主线剧情]既与龙宫相关，就找再江州衙门街头溜达的".."#Y/ht|虾兵*大唐国境*npc查询/虾兵".."#W问问！ "}
		elseif 进度 == 25 then
			return {"玄奘的身世","[主线剧情]快去向大唐国境的".."#Y/ht|渔翁*大唐国境*npc查询/渔翁".."#W求教入海方法吧！ "}
		elseif 进度 == 26 then
			return {"玄奘的身世","[主线剧情]听说大唐国境有".."#Y/ht|山神*大唐国境*npc查询/山神".."#出没过去看看吧！ "}
		elseif 进度 == 27 then
			return {"玄奘的身世","[主线剧情]找到".."#Y/ht|获取途径*烹饪或摊位购买*bz提示/臭豆腐".."#W贿赂".."#Y/ht|山神*大唐国境*npc查询/山神".."#W。（可选战斗） "}
		elseif 进度 == 28 then
			return {"玄奘的身世","[主线剧情]找到".."#Y/ht|获取途径*烹饪或摊位购买*bz提示/梅花酒".."#W重新贿赂".."#Y/ht|山神*大唐国境*npc查询/山神".."#W吧。 "}
		elseif 进度 == 29 then
			return {"玄奘的身世","[主线剧情]先去龙宫拜见守门大将军".."#Y/ht|蟹将军*龙宫*npc查询/蟹将军".."#大人吧！ "}
		elseif 进度 == 30 then
			return {"玄奘的身世","[主线剧情]终于可以拜见".."#Y/ht|龟千岁*龙宫*npc查询/龟千岁".."#交还宝物 ".."#Y/ht|获取途径*江州闲逛的虾兵*bz提示/定颜珠".."#！ "}
		elseif 进度 == 31 then
			return {"玄奘的身世","[主线剧情]将".."#Y/ht|获取途径*大唐国境山神*bz提示/辟水宝珠".."#W交换给".."#Y/ht|山神*大唐国境*npc查询/山神".."#W。 "}
		elseif 进度 == 32 then
			return {"玄奘的身世","[主线剧情]将陈光蕊的信物交给江州衙门的".."#Y/ht|殷温娇*大唐国境*npc查询/殷温娇".."#W吧！ "}
		elseif 进度 == 33 then
			return {"玄奘的身世","[主线剧情]要找寻找婆婆的下落先问问大唐国境万花店的".."#Y/ht|小二*大唐国境*npc查询/小二".."#W。 "}
		elseif 进度 == 34 then
			return {"玄奘的身世","[主线剧情]".."#Y/ht|婆婆*大唐国境*npc查询/婆婆".."#W在大唐国境洪州街头乞讨，快去那里找到她吧。（将萧给她） "}
		elseif 进度 == 35 then
			return {"玄奘的身世","[主线剧情]买一只".."#Y/ht|获取途径*烹饪技巧*bz提示/烤鸭".."#W给".."#Y/ht|婆婆*大唐国境*npc查询/婆婆".."#W吃。 "}
		elseif 进度 == 36 then
			return {"玄奘的身世","[主线剧情]去龙宫求丁香水".."#Y/ht|龟千岁*龙宫*npc查询/龟千岁".."#W应该会应允吧。 "}
		elseif 进度 == 37 then
			return {"玄奘的身世","[主线剧情]用".."#Y/ht|获取途径*野外或商会购买*bz提示/丁香水".."#W回去治好".."#Y/ht|婆婆*大唐国境*npc查询/婆婆".."#W的眼睛。"}
		elseif 进度 == 38 then
			return {"玄奘的身世","[主线剧情]替".."#Y/ht|殷温娇*大唐国境*npc查询/殷温娇".."#W找到了婆婆，可以回去江州衙门复命了。 "}
		elseif 进度 == 39 then
			return {"玄奘的身世","[主线剧情]前往长安到殷丞相府请".."#Y/ht|殷丞相*丞相府*npc查询/殷丞相".."#W派出救兵！ "}
		elseif 进度 == 40 then
			return {"玄奘的身世","[主线剧情]".."#Y/ht|殷夫人*丞相府*npc查询/殷夫人".."#W好像气坏了身体，快去丞相府内堂看看。 "}
		elseif 进度 == 41 then
			return {"玄奘的身世","[主线剧情]快去找金香玉来救殷夫人！听说江南野外的".."#Y/ht|江湖奸商*江南野外*npc查询/江湖奸商".."#W那里有售。 "}
		elseif 进度 == 42 then
			return {"玄奘的身世","[主线剧情]拿到金香玉，快给".."#Y/ht|殷夫人*丞相府*npc查询/殷夫人".."#W服用吧。 "}
		elseif 进度 == 43 then
			return {"玄奘的身世","[主线剧情]拿着前往皇宫拜见皇上".."#Y/ht|李世民*金銮殿*npc查询/李世民".."#W请求出兵！ "}
		elseif 进度 == 44 then
			return {"玄奘的身世","[主线剧情]去皇宫后花园找".."#Y/ht|赵美人*长安城*npc查询/赵美人".."#W问问。 "}
		elseif 进度 == 45 then
			return {"玄奘的身世","[主线剧情]将奏折递交给".."#Y/ht|魏征*金銮殿*npc查询/魏征".."#W大人吧。 "}
		elseif 进度 == 46 then
			return {"玄奘的身世","[主线剧情]终于请到救兵，快去大唐国境将恶贼".."#Y/ht|刘洪*江州衙门*npc查询/刘洪".."#W砍个片甲不留！ "}
		elseif 进度 == 47 then
			return {"玄奘的身世","[主线剧情]快去大唐境外追捕真正的".."#Y/ht|刘洪*大唐境外*npc查询/刘洪".."#W和帮凶".."#Y/ht|李彪*大唐境外*npc查询/李彪".."#W！"}
		elseif 进度 == 48 then
			return {"玄奘的身世","[主线剧情]恶人自有恶人磨，姑且看看".."#Y/ht|刘洪*大唐境外*npc查询/刘洪".."#W要怎么办！"}
		elseif 进度 == 49 then
			return {"玄奘的身世","[主线剧情]擒下李彪，且看".."#Y/ht|刘洪*大唐境外*npc查询/刘洪".."#W还有何话要说！"}
		elseif 进度 == 50 then
			return {"玄奘的身世","[主线剧情]将将州府大印交给".."#Y/ht|殷温娇*大唐国境*npc查询/殷温娇".."#W吧！"}
		elseif 进度 == 51 then
			return {"玄奘的身世","[主线剧情]去金山寺吧玄奘母亲手抄的".."#Y/ht|获取途径*玄奘的身世*bz提示/般若多罗蜜心经".."#W交给".."#Y/ht|玄奘*大雄宝殿*npc查询/玄奘".."#W法师吧。 "}
		end
	elseif 主线 == 4 then
		if 进度 == 1 then
			return {"沙僧剧情","[主线剧情]在".."#Y/ht|天兵飞剑*大唐境外*npc查询/天兵飞剑".."#W处领取沙僧剧情任务。"}
		elseif 进度 == 2 then
			return {"大战心魔","[主线剧情]搞定了天兵，看看".."#Y/ht|卷帘大将*大唐境外*npc查询/卷帘大将".."#W有什么苦衷。"}
		elseif 进度 == 3 then
			return {"大战心魔","[主线剧情]一场激战后".."#Y/ht|卷帘大将*大唐境外*npc查询/卷帘大将".."#W身上散落一串念珠，大将的神色忽然大变，赶紧问问他怎么回事？"}
		elseif 进度 == 4 then
			return {"大战心魔","[主线剧情]向长安城的".."#Y/ht|袁天罡*长安城*npc查询/袁天罡".."#W请教阵法之理。"}
		elseif 进度 == 5 then
			return {"大战心魔","[主线剧情]找到散落的天衡星听说江南野外的".."#Y/ht|樵夫*江南野外*npc查询/樵夫".."#W知道其下落。"}
		elseif 进度 == 6 then
			return {"大战心魔","[主线剧情]帮助袁守诚打败困扰他的".."#Y/ht|龙孙*长安城*npc查询/龙孙".."#W，然后询问天衡星的下落。"}
		elseif 进度 == 7 then
			return {"大战心魔","[主线剧情]这条小龙好生凶悍，问问".."#Y/ht|袁守诚*长安城*npc查询/袁守诚".."#W究竟是怎么回事。"}
		elseif 进度 == 8 then
			return {"大战心魔","[主线剧情]去龙宫找".."#Y/ht|小龙女*龙宫*npc查询/小龙女".."#W替袁守诚解释事情原由（可选战斗）"}
		elseif 进度 == 9 then
			return {"大战心魔","[主线剧情]回长安找".."#Y/ht|袁守诚*长安城*npc查询/袁守诚".."#W要天衡星。"}
		elseif 进度 == 10 then
			return {"大战心魔","[主线剧情]寻找散落的天英星，听说天宫的".."#Y/ht|水军统领*天宫*npc查询/水军统领".."#W知道其下落。"}
		elseif 进度 == 11 then
			return {"大战心魔","[主线剧情]去地府".."#Y/ht|转轮王*森罗殿*npc查询/转轮王".."#W处打听一下。"}
		elseif 进度 == 12 then
			return {"大战心魔","[主线剧情]难道天英也闯祸了？为何没被收去找天宫的托塔".."#Y/ht|李靖*凌霄宝殿*npc查询/李靖".."#W打听一下。"}
		elseif 进度 == 13 then
			return {"大战心魔","[主线剧情]给".."#Y/ht|守门天兵*天宫*npc查询/守门天兵".."#W买壶好酒".."#Y/ht|获取途径*烹饪技巧*bz提示/醉生梦死".."#W。 "}
		elseif 进度 == 14 then
			return {"大战心魔","[主线剧情]拿到了".."#Y/ht|获取途径*如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买*bz提示/火尖枪".."#W可以回去天宫和".."#Y/ht|李靖*凌霄宝殿*npc查询/李靖".."#W交换天英了。 "}
		elseif 进度 == 15 then
			return {"大战心魔","[主线剧情]天心妖犯了大错误将被处死，找负责看守的道童".."#Y/ht|清风*五庄观*npc查询/清风".."#W求下情。"}
		elseif 进度 == 16 then
			return {"大战心魔","[主线剧情]救活人参树的方法，长寿村的".."#Y/ht|太白金星*长寿村*npc查询/太白金星".."#W应该知晓！"}
		elseif 进度 == 17 then
			return {"大战心魔","[主线剧情]太白金星居然推卸给".."#Y/ht|太上老君*兜率宫*npc查询/太上老君".."#W，只好去问问了。"}
		elseif 进度 == 18 then
			return {"大战心魔","[主线剧情]太上老君又推卸给普陀山的".."#Y/ht|青莲仙女*潮音洞*npc查询/青莲仙女".."#W？！继续跑路吧。"}
		elseif 进度 == 19 then
			return {"大战心魔","[主线剧情]寻找".."#Y/ht|获取途径*野外或商会购买*bz提示/火凤之睛".."#W交给".."#Y/ht|青莲仙女*潮音洞*npc查询/青莲仙女".."#W快去吧。"}
		elseif 进度 == 20 then
			return {"大战心魔","[主线剧情]拿到仙露，回五庄观交给道童".."#Y/ht|清风*五庄观*npc查询/清风".."#W救治人参果树。"}
		elseif 进度 == 21 then
			return {"大战心魔","[主线剧情]听说是长寿郊外的".."#Y/ht|路人甲*长寿郊外*npc查询/路人甲".."#W拿了".."#Y/ht|获取途径*如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买*bz提示/金击子".."#W。（可选战斗））"}
		elseif 进度 == 22 then
			return {"大战心魔","[主线剧情]拿回".."#Y/ht|获取途径*如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买*bz提示/金击子".."#W回去交给五庄观的道童".."#Y/ht|清风*五庄观*npc查询/清风".."#W换取天心。"}
		elseif 进度 == 23 then
			return {"大战心魔","[主线剧情]集齐三星，复原九宫，可以去大唐境外找".."#Y/ht|卷帘大将*大唐境外*npc查询/卷帘大将".."#W的心魔算账了。"}
		elseif 进度 == 24 then
			return {"大战心魔","[主线剧情]打败心魔，劝服".."#Y/ht|卷帘大将*大唐境外*npc查询/卷帘大将".."#W成为天命取经人。"}
		end
	elseif 主线 == 5 then
		if 进度 == 1 then
			return {"含冤小白龙","[主线剧情]在".."#Y/ht|大力神灵*天宫*npc查询/大力神灵".."#W处领取“含冤的小白龙”剧情任务。"}
		elseif 进度 == 2 then
			return {"含冤小白龙","[主线剧情]白痴大力神灵的玄天铁鞭丢了，去长寿村找进来得到一条好鞭子的".."#Y/ht|毛驴张*长寿村*npc查询/毛驴张".."#W问问。"}
		elseif 进度 == 3 then
			return {"含冤小白龙","[主线剧情]帮".."#Y/ht|毛驴张*长寿村*npc查询/毛驴张".."#W买来".."#Y/ht|获取途径*长安杂货店*bz提示/高级宠物口粮".."#W。"}
		elseif 进度 == 4 then
			return {"含冤小白龙","[主线剧情]将".."#Y/ht|获取途径*如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买*bz提示/玄天铁鞭".."#W交给天宫的".."#Y/ht|大力神灵*天宫*npc查询/大力神灵".."#W。"}
		elseif 进度 == 5 then
			return {"含冤小白龙","[主线剧情]可怜的小白龙犯了死罪被关进天牢，去".."#Y/ht|天牢守卫*天宫*npc查询/天牢守卫".."#W疏通疏通看能不能探视下小白龙。"}
		elseif 进度 == 6 then
			return {"含冤小白龙","[主线剧情]".."#Y/ht|小白龙*海底迷宫五层*npc查询/小白龙".."#W越狱了？料想他定是逃去了海底迷宫五层了。"}
		elseif 进度 == 7 then
			return {"含冤小白龙","[主线剧情]把信物交给大雁塔顶层的".."#Y/ht|镇塔之神*大雁塔七层*npc查询/镇塔之神".."#W。"}
		elseif 进度 == 8 then
			return {"含冤小白龙","[主线剧情]帮忙在塔内巡逻，搞定二楼的".."#Y/ht|梦之魅*大雁塔二层*npc查询/梦之魅".."#W。"}
		elseif 进度 == 9 then
			return {"含冤小白龙","[主线剧情]帮忙在塔内巡逻，搞定四楼的".."#Y/ht|血之魅*大雁塔四层*npc查询/血之魅".."#W。"}
		elseif 进度 == 10 then
			return {"含冤小白龙","[主线剧情]帮忙在塔内巡逻，搞定五楼的".."#Y/ht|森之魅*大雁塔五层*npc查询/森之魅".."#W。"}
		elseif 进度 == 11 then
			return {"含冤小白龙","[主线剧情]回去找大雁塔顶层的".."#Y/ht|镇塔之神*大雁塔七层*npc查询/镇塔之神".."#W复命把。"}
		elseif 进度 == 12 then
			return {"含冤小白龙","[主线剧情]赶走入侵大雁塔四层的".."#Y/ht|奔波儿灞*大雁塔四层*npc查询/奔波儿灞".."#W。"}
		elseif 进度 == 13 then
			return {"含冤小白龙","[主线剧情]万圣公主竟做出这种事？！先带着".."#Y/ht|获取途径*如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买*bz提示/白剑".."#W去找".."#Y/ht|蟹将军*龙宫*npc查询/蟹将军".."#W问个明白把"}
		elseif 进度 == 14 then
			return {"含冤小白龙","[主线剧情]摆平了蟹将军，还有个".."#Y/ht|虾将军*龙宫*npc查询/虾将军".."#W做拦路虎！"}
		elseif 进度 == 15 then
			return {"含冤小白龙","[主线剧情]摆平了虾兵蟹将后，终于可以去找".."#Y/ht|万圣公主*龙宫*npc查询/万圣公主".."#W了。"}
		elseif 进度 == 16 then
			return {"含冤小白龙","[主线剧情]原来万圣公主受此冤屈，替他向天宫的".."#Y/ht|王母娘娘*凌霄宝殿*npc查询/王母娘娘".."#W索回龙须草。"}
		elseif 进度 == 17 then
			return {"含冤小白龙","[主线剧情]夺回了".."#Y/ht|获取途径*野外或商会购买*bz提示/龙须草".."#W可以交还给".."#Y/ht|万圣公主*龙宫*npc查询/万圣公主".."#W了。"}
		elseif 进度 == 18 then
			return {"含冤小白龙","[主线剧情]没想到被万圣公主骗了羞愧之余，还是把龙须草还给".."#Y/ht|王母娘娘*凌霄宝殿*npc查询/王母娘娘".."#W。"}
		elseif 进度 == 19 then
			return {"含冤小白龙","[主线剧情]了却万圣公主一事，是时候去魔王寨找元凶".."#Y/ht|九头精怪*魔王居*npc查询/九头精怪".."#W算账了。"}
		elseif 进度 == 20 then
			return {"含冤小白龙","[主线剧情]打发掉牛魔王的亲兵，这下该轮到".."#Y/ht|九头精怪*魔王居*npc查询/九头精怪".."#W了。"}
		elseif 进度 == 21 then
			return {"含冤小白龙","[主线剧情]九头精怪已经把镇塔之宝进贡给天庭了，赶紧找".."#Y/ht|玉皇大帝*凌霄宝殿*npc查询/玉皇大帝".."#W索要回来。"}
		elseif 进度 == 22 then
			return {"含冤小白龙","[主线剧情]昏庸的玉帝就知道喊观音如来，还是亲自去普陀山找".."#Y/ht|青莲仙女*潮音洞*npc查询/青莲仙女".."#W问个明白。"}
		elseif 进度 == 23 then
			return {"含冤小白龙","[主线剧情]拿着青莲仙女给予的餐风饮露，前往天宫跟".."#Y/ht|玉皇大帝*凌霄宝殿*npc查询/玉皇大帝".."#W问交换镇塔之宝吧。"}
		elseif 进度 == 24 then
			return {"含冤小白龙","[主线剧情]拿到镇塔之宝，返回大雁塔七层交还给".."#Y/ht|镇塔之神*大雁塔七层*npc查询/镇塔之神".."#W。"}
		elseif 进度 == 25 then
			return {"含冤小白龙","[主线剧情]将观音姐姐的信物碧水青龙带给小白龙，现在就去海底迷宫五层找".."#Y/ht|小白龙*海底迷宫五层*npc查询/小白龙".."#W吧。"}
		end
	elseif 主线 == 6 then
		if 进度 == 1 then
			return {"女娲神迹","[冥冥之中的天意]待你强大以后去找".."#Y/ht|女娲神迹传送人*北俱芦洲*npc查询/女娲神迹传送人".."#W问一下身世之谜"}
		elseif 进度 == 2 then
			return {"女娲神迹","[冥冥之中的天意]找".."#Y/ht|美猴王*水帘洞*npc查询/美猴王".."#W解开这个谜团"}
		elseif 进度 == 3 then
			return {"女娲神迹","[冥冥之中的天意]帮助".."#Y/ht|美猴王*水帘洞*npc查询/美猴王".."#W找来一坛#R女儿红"}
		elseif 进度 == 4 then
			return {"女娲神迹","[冥冥之中的天意]找".."#Y/ht|女娲神迹传送人*北俱芦洲*npc查询/女娲神迹传送人".."#W在问一下事情的始末"}
		elseif 进度 == 5 then
			return {"女娲神迹","[冥冥之中的天意]找".."#Y/ht|小紫*圣殿*npc查询/小紫".."#W了解下这件事情"}
		elseif 进度 == 6 then
			return {"女娲神迹","[冥冥之中的天意]将".."#Y/ht|小紫*圣殿*npc查询/小紫".."#W姑娘的情况告诉#R女娲神迹传送人"}
		elseif 进度 == 7 then
			return {"女娲神迹","[冥冥之中的天意]找到".."#Y/ht|获取途径*中药医理制作的道具*bz提示/千年保心丹".."#W给#Y/ht|小紫*圣殿*npc查询/小紫#W服下"}
		elseif 进度 == 8 then
			return {"女娲神迹","[冥冥之中的天意]将".."#Y/ht|小紫*圣殿*npc查询/小紫".."#W说的话告诉#Y/ht|美猴王*水帘洞*npc查询/美猴王"}
		elseif 进度 == 9 then
			return {"女娲神迹","[冥冥之中的天意]去盘丝岭打探下".."#Y/ht|春十三娘*盘丝洞*npc查询/春十三娘".."#W的底细"}
		elseif 进度 == 10 then
			return {"女娲神迹","[冥冥之中的天意]告诉".."#Y/ht|小紫*圣殿*npc查询/小紫".."#W敌人的情况"}
		end
	elseif 主线 == 7 then
		if 进度 == 1 then
			return {"八戒悟空","[主线剧情]".."#Y/ht|月香侍女*月宫*npc查询/月香侍女".."#W处领取“八戒悟空”剧情任务。"}
		elseif 进度 == 2 then
			return {"八戒悟空","[主线剧情]".."#Y/ht|康太尉*月宫*npc查询/康太尉".."#W这家伙竟来打扰嫦娥仙子清修，快打发他走！"}
		elseif 进度 == 3 then
			return {"八戒悟空","[主线剧情]打跑前来捣乱的康太尉，问问".."#Y/ht|月香侍女*月宫*npc查询/月香侍女".."#W究竟是为何事。"}
		elseif 进度 == 4 then
			return {"八戒悟空","[主线剧情]月香姑娘似乎难于开口，还是去问问".."#Y/ht|嫦娥*广寒宫*npc查询/嫦娥".."#W仙子吧。"}
		elseif 进度 == 5 then
			return {"八戒悟空","[主线剧情]原来如此，但听说他在凡间也惹了不少感情债，去找".."#Y/ht|卵二姐*江南野外*npc查询/卵二姐".."#W问问。"}
		elseif 进度 == 6 then
			return {"八戒悟空","[主线剧情]听说".."#Y/ht|罗纤纤*江南野外*npc查询/罗纤纤".."#W头上带的桃花特别娇艳，前去问问。"}
		elseif 进度 == 7 then
			return {"八戒悟空","[主线剧情]帮罗纤纤探探长安国子监内".."#Y/ht|吴举人*国子监书库*npc查询/吴举人".."#W的心意。"}
		elseif 进度 == 8 then
			return {"八戒悟空","[主线剧情]帮吴举人送花给".."#Y/ht|罗纤纤*江南野外*npc查询/罗纤纤".."#W。"}
		elseif 进度 == 9 then
			return {"八戒悟空","[主线剧情]撮合一对新人后，将"..剧情物品("桃花","长安城杂货店").."#W拿去给"..剧情npcooo("卵二姐",1193).."#W。"}
		elseif 进度 == 10 then
			return {"八戒悟空","[主线剧情]就帮卵二姐将"..剧情物品("桃花","长安城杂货店").."#W还给"..剧情npcooo("天蓬元帅",1173).."#W。"}
		elseif 进度 == 11 then
			return {"八戒悟空","[主线剧情]无奈还是帮天蓬元帅去高家庄找"..剧情npcooo("高翠兰",1171).."#W小姐退婚吧。"}
		elseif 进度 == 12 then
			return {"八戒悟空","[主线剧情]打退了高家抢亲的队伍，回去向"..剧情npcooo("天蓬元帅",1173).."#W交差。"}
		elseif 进度 == 13 then
			return {"八戒悟空","[主线剧情]得向境外的"..剧情npcooo("土地公公",1173).."#W打听盘丝岭的所在。"}
		elseif 进度 == 14 then
			return {"八戒悟空","[主线剧情]付给土地咨询费。"}
		elseif 进度 == 15 then
			return {"八戒悟空","[主线剧情]帮土地打退捣乱的大唐境外的"..剧情npcooo("冤魂",1173).."#W。"}
		elseif 进度 == 16 then
			return {"八戒悟空","[主线剧情]回去向"..剧情npcooo("土地公公",1173).."#W打听情魔蛛丝之毒的秘密。"}
		elseif 进度 == 17 then
			return {"八戒悟空","[主线剧情]先去找"..剧情npcooo("天蓬元帅",1173).."#W解除他和卵二姐之间的”疚“之情。"}
		elseif 进度 == 18 then
			return {"八戒悟空","[主线剧情]代替天蓬送"..剧情物品("桃花","长安城杂货店").."#W给"..剧情npcooo("卵二姐",1193).."#W请求她的原谅。"}
		elseif 进度 == 19 then
			return {"八戒悟空","[主线剧情]卵二姐原谅了天蓬，回去向"..剧情npcooo("天蓬元帅",1173).."#W说明。"}
		elseif 进度 == 20 then
			return {"八戒悟空","[主线剧情]前往高老庄找"..剧情npcooo("高翠兰",1171).."#W小姐，解除天蓬元帅的”怖“之情。"}
		elseif 进度 == 21 then
			return {"八戒悟空","[主线剧情]高翠兰小姐净是胡，还是找她爹爹"..剧情npcooo("高老先生",1170).."#W说个明白吧。"}
		elseif 进度 == 22 then
			return {"八戒悟空","[主线剧情]快去长安找"..剧情npcooo("红线童子",1001).."#W问问。"}
		elseif 进度 == 23 then
			return {"八戒悟空","[主线剧情]这红线童子好生胡闹去找"..剧情npcooo("绿儿",1142).."#W要回木偶。"}
		elseif 进度 == 24 then
			return {"八戒悟空","[主线剧情]终于换回张小二的木偶了，快拿去给"..剧情npcooo("红线童子",1001).."#W吧。"}
		elseif 进度 == 25 then
			return {"八戒悟空","[主线剧情]终于牵对了红线，回高家庄看看"..剧情npcooo("高翠兰",1171).."#W小姐现在怎样。"}
		elseif 进度 == 26 then
			return {"八戒悟空","[主线剧情]高小姐已不在迷恋天蓬元帅，拿回"..剧情物品("婚书","如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买").."给"..剧情npcooo("天蓬元帅",1173).."#W化解这”怖“之情吧。"}
		elseif 进度 == 27 then
			return {"八戒悟空","[主线剧情]上月宫广寒宫内找"..剧情npcooo("嫦娥",1531).."#W仙子，解除“嗔”之情。"}
		elseif 进度 == 28 then
			return {"八戒悟空","[主线剧情]前往天宫找可恶的"..剧情npcooo("杨戬",1112).."#W讨回两人定情之物绿烟如梦。"}
		elseif 进度 == 29 then
			return {"八戒悟空","[主线剧情]去长寿村的"..剧情npcooo("慧觉和尚",1070).."#W那里打探下绿烟如梦的情况。"}
		elseif 进度 == 30 then
			return {"八戒悟空","[主线剧情]"..剧情物品("绿烟如梦","如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买").."#W破碎了。拿回广寒宫给"..剧情npcooo("嫦娥",1531).."#W仙子想想办法吧。"}
		elseif 进度 == 31 then
			return {"八戒悟空","[主线剧情]嫦娥仙子的一滴泪让"..剧情物品("绿烟如梦","如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买").."#W恢复原状，快拿回给"..剧情npcooo("天蓬元帅",1173).."#W恢复记忆吧。"}
		elseif 进度 == 32 then
			return {"八戒悟空","[主线剧情]最后是找花十娘解除四情之“爱”盘丝岭的位置？还是得问问境外的"..剧情npcooo("土地公公",1173).."#W。"}
		elseif 进度 == 33 then
			return {"八戒悟空","[主线剧情]原来盘丝岭内讧了，快劝盘丝洞内的"..剧情npcooo("白晶晶",1144).."#W不要冲动犯事。"}
		elseif 进度 == 34 then
			return {"八戒悟空","[主线剧情]据说山寨的大帮主知晓孙悟空的下路，去境外找"..剧情npcooo("至尊宝",1173).."#W问个明白。"}
		elseif 进度 == 35 then
			return {"八戒悟空","[主线剧情]大帮主疯疯癫癫，去大唐境外找真正的"..剧情npcooo("二帮主",1173).."#W问个明白。"}
		elseif 进度 == 36 then
			return {"八戒悟空","[主线剧情]去北俱芦洲寻找蛇蝎美人，听说北俱芦洲的"..剧情npcooo("江湖奸商",1174).."#W那儿有。"}
		elseif 进度 == 37 then
			return {"八戒悟空","[主线剧情]去北俱芦洲凤巢的"..剧情npcooo("辛发明",1186).."#W那里寻找红雪散。"}
		elseif 进度 == 38 then
			return {"八戒悟空","[主线剧情]去北俱芦洲龙窟的"..剧情npcooo("龙神",1178).."#W那里寻找五龙丹。"}
		elseif 进度 == 39 then
			return {"八戒悟空","[主线剧情]集齐了三种药材，将蛇蝎美人交给"..剧情npcooo("至尊宝",1173).."#W吧。"}
		elseif 进度 == 40 then
			return {"八戒悟空","[主线剧情]集齐了三种药材，将红雪散交给"..剧情npcooo("至尊宝",1173).."#W吧。"}
		elseif 进度 == 41 then
			return {"八戒悟空","[主线剧情]集齐了三种药材，将五龙丹交给"..剧情npcooo("至尊宝",1173).."#W吧。"}
		elseif 进度 == 42 then
			return {"八戒悟空","[主线剧情]拿着孙悟空的信物"..剧情物品("玉佩","如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买").."#W回去盘丝洞交给"..剧情npcooo("白晶晶",1144).."#W。"}
		elseif 进度 == 43 then
			return {"八戒悟空","[主线剧情]晶晶姑娘杀去盘丝洞了，快去找境外的"..剧情npcooo("土地公公",1173).."#W阻止她。"}
		elseif 进度 == 44 then
			return {"八戒悟空","[主线剧情]现在四情终于破解，可以回去境外"..剧情npcooo("天蓬元帅",1173).."#W那解开他的心结了。"}
		elseif 进度 == 45 then
			return {"八戒悟空","[主线剧情]天蓬元帅已经大彻大悟，现在去境外告诉"..剧情npcooo("至尊宝",1173).."#W晶晶已经死这个消息吧。"}
		elseif 进度 == 46 then
			return {"八戒悟空","[主线剧情]去普陀山潮音洞找"..剧情npcooo("观音姐姐",1141).."#W借灵光宝匣一用看看过去到底发生了什么事。"}
		elseif 进度 == 47 then
			return {"八戒悟空","[主线剧情]灵光宝匣借不成。先听观音姐姐的话，回境外找"..剧情npcooo("至尊宝",1173).."#W。"}
		elseif 进度 == 48 then
			return {"八戒悟空","[主线剧情]到地府找"..剧情npcooo("地藏王",1124).."#W帮忙。"}
		elseif 进度 == 49 then
			return {"八戒悟空","[主线剧情]到地府迷宫二层找看守"..剧情npcooo("吊死鬼",1128).."#W求他把晶晶魂魄带来吧。"}
		elseif 进度 == 50 then
			return {"八戒悟空","[主线剧情]摆平了这啰嗦的看守，终于可以去地府四层找"..剧情npcooo("晶晶姑娘的鬼魂",1130).."#W了。"}
		elseif 进度 == 51 then
			return {"八戒悟空","[主线剧情]晶晶姑娘的鬼魂犹有怨气未消，帮她去普陀山潮音洞内找"..剧情npcooo("观音姐姐",1141).."#W问清事实。"}
		elseif 进度 == 52 then
			return {"八戒悟空","[主线剧情]只得去北俱芦洲的凤巢七层找叫"..剧情npcooo("无心",1192).."#W的和尚了。"}
		elseif 进度 == 53 then
			return {"八戒悟空","[主线剧情]只得去北俱芦洲的龙窟七层找"..剧情npcooo("青灵玄女",1183).."#W要回定海珠。"}
		elseif 进度 == 54 then
			return {"八戒悟空","[主线剧情]回去凤巢七层将"..剧情物品("定海珠","如果不慎遗失可到长安城“剧情道具NPC”（202，251）购买").."#W交给"..剧情npcooo("无心",1192).."#W和尚。"}
		elseif 进度 == 55 then
			return {"八戒悟空","[主线剧情]了解孙悟空和云霓的恩怨情仇，回去普陀山潮音洞拜见"..剧情npcooo("观音姐姐",1141).."#W。"}
		elseif 进度 == 56 then
			return {"八戒悟空","[主线剧情]拿着观音姐姐赐予的金箍回去境外交给"..剧情npcooo("至尊宝",1173).."#W点化孙悟空。"}
		elseif 进度 == 57 then
			return {"八戒悟空","[主线剧情]把孙悟空和云霓的爱恋之心带去地府四层给"..剧情npcooo("晶晶姑娘的鬼魂",1130).."#W看。"}
		elseif 进度 == 58 then
			return {"八戒悟空","[主线剧情]回去境外告知"..剧情npcooo("至尊宝",1173).."#W晶晶姑娘已大彻大悟。"}
		end
	elseif 主线 == 13 then
		if 进度 == 1 then
			return {"渡劫剧情","[渡劫重生]请回去找"..剧情npcooo("王母娘娘",1112).."#W入于化境。"}
		end
	elseif 主线 == 32 then
		if 进度 == 1 then
			return {"三界劫难","[天命之战]化圣后找".."#Y/ht|玉皇大帝*凌霄宝殿*npc查询/玉皇大帝".."#W询问封印魔神蚩尤的方法"}
		end
	end
end


function 剧情物品(物品, 详情)
    return "#Rht|获取途径*" .. 详情 .. "*bz提示/" .. 物品
end
function 剧情npcooo(npc,id)
	return "#Rht|"..npc.."*"..取地图名称(id).."*npc查询/"..npc
end

