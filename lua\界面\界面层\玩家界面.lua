--[[
LastEditTime: 2024-07-20 20:58:09
--]]
local 玩家界面 = 界面层:创建控件("玩家界面", 0, 0)
local SDL = require 'SDL'

-- 引入摇杆控件
local 摇杆控件类 = require("界面/控件层/摇杆控件")
function 玩家界面:初始化()
    self:置宽高(引擎.宽度,引擎.高度)
    self.可初始化=true
    
    -- 初始化移动端摇杆控件
    if __手机 then
        -- 摇杆位置回到原来位置
        local 摇杆x = 20
        local 摇杆y = 引擎.高度 - 120
        self.移动摇杆 = 摇杆控件类:new(self, 摇杆x, 摇杆y, 80)
        self.移动摇杆:初始化()
    end
end

-- 添加鼠标事件处理，优先处理摇杆事件
function 玩家界面:鼠标按下(x, y, 按钮)
    -- 优先检查摇杆事件
    if __手机 and self.移动摇杆 then
        if self.移动摇杆:鼠标按下(x, y) then
            return true -- 摇杆处理了事件，阻止穿透
        end
    end
    return false -- 允许其他控件处理事件
end

function 玩家界面:鼠标移动(x, y)
    -- 优先检查摇杆事件
    if __手机 and self.移动摇杆 then
        if self.移动摇杆:鼠标移动(x, y) then
            return true -- 摇杆处理了事件，阻止穿透
        end
    end
    return false -- 允许其他控件处理事件
end

function 玩家界面:鼠标释放(x, y, 按钮)
    -- 优先检查摇杆事件
    if __手机 and self.移动摇杆 then
        if self.移动摇杆:鼠标释放(x, y) then
            return true -- 摇杆处理了事件，阻止穿透
        end
    end
    return false -- 允许其他控件处理事件
end


function 玩家界面:重新初始化()
    for k, v in self:遍历控件() do
        v:初始化()
        if v.重新初始化 then
            v:重新初始化()
        end
    end
    
    -- 重新初始化摇杆控件
    if __手机 and self.移动摇杆 then
        local 摇杆x = 20
        local 摇杆y = 引擎.高度 - 120  -- 恢复原来位置
        self.移动摇杆:设置位置(摇杆x, 摇杆y)
        self.移动摇杆:初始化()
    end
end

-- 添加更新方法，确保摇杆更新
function 玩家界面:更新(dt)
    -- 更新摇杆控件
    if __手机 and self.移动摇杆 then
        self.移动摇杆:更新(dt)
    end
end

-- 添加显示方法，确保摇杆显示
function 玩家界面:显示(x, y)
    -- 最后显示摇杆控件，确保在最上层
    if __手机 and self.移动摇杆 then
        self.移动摇杆:显示(x or 0, y or 0)
    end
end





--------------------------------------------------------------------左上时辰

local 时辰控件=玩家界面:创建控件("时辰控件", 0, 0,300,300)
function 时辰控件:初始化()
   self.时辰 = 5
   self.偏移=0
   self.下载显示=文本字体:置颜色(__取颜色("绿色")):取描边精灵("游戏资源下载中......")
end




function 时辰控件:更新(dt)
  if  #__实时更新.updata>0 then
      local  显示内容 = ((#__实时更新.receivedata +  #__实时更新.tablereceivedata * 10024000)//1000) .. "KB".."/"..__实时更新.upsize // 1000 .. "KB"
      self.下载显示=文本字体:置颜色(__取颜色("绿色")):取描边精灵("资源下载:"..显示内容)
  end
end




function 时辰控件:显示(x,y)
    if __res.实时更新 and  #__实时更新.updata>0  then

        if self.偏移==0 then
            self.下载显示:显示(x, y + 130)
        else
            self.下载显示:显示(x+20,y)
        end
    end
end




function 时辰控件:重置(数据)
    if not 角色信息 or not 角色信息.地图数据 then return end
     self.背景.地图名称= 文本字体:置颜色(255,255,255,255):取精灵(取地图名称(角色信息.地图数据.编号))
    if 数据 then
        self.时辰 = 数据
    end
    if self.偏移==0 then
        self.昼夜:置可见(true)
        self.背景:置可见(true)
        self.灯笼1:置可见(true)
        self.灯笼2:置可见(true)
        self.灯笼3:置可见(true)
        self.日历:置可见(true)
        self.指引:置可见(true)
        self.商城:置可见(true)
        self.剑会:置可见(true)
        self.排行:置可见(true)
        if __会员显示 then
           self.会员:置可见(true)
        else
            self.会员:置可见(false)
        end
    else
        self.昼夜:置可见(false)
        self.背景:置可见(false)
        self.灯笼1:置可见(false)
        self.灯笼2:置可见(false)
        self.灯笼3:置可见(false)
        self.日历:置可见(false)
        self.指引:置可见(false)
        self.商城:置可见(false)
        self.剑会:置可见(false)
        self.排行:置可见(false)
        self.会员:置可见(false)
    end
end


local 昼夜 = 时辰控件:创建控件("昼夜", 23, 32, 80, 30)
function 昼夜:初始化()
   self.白昼 = __res:取资源动画("jszy/fwtb", 0x9DF6DEBC,"精灵")
   self.黑昼 = __res:取资源动画("jszy/fwtb", 0x99738F4C,"精灵")
   self.计时 = 1
   self.计次 = 250
end
function 昼夜:更新(dt)
    if __主显.主角 and __主显.主角.动作=="静立" then
        self.计时 = self.计时 + dt * 2
    else
        self.计时 = self.计时 + dt * 4
    end
    if self.计时 >= 1 then
        self.计次 = self.计次 - 1
        if self.计次 < 45 then
            self.计次 = 250
        end
        self.计时 = 0
    end
  
end
function 昼夜:显示(x, y)
    if 时辰控件.时辰 >= 11 or 时辰控件.时辰 <= 4 then
        self.黑昼:显示(x - self.计次, y)
    else
        self.白昼:显示(x - self.计次, y)
    end
   
end


local 背景 = 时辰控件:创建按钮("背景", 0, 0, 120, 100)
function 背景:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/fwtb", 0xDE3F48B7))
    self.小人跑步 = __res:取资源动画("jszy/fwtb", 0xAC307575,"动画")
    self.小人走路 = __res:取资源动画("jszy/fwtb", 0xC7BEBF45,"动画")
    local g = {0x361FA820,0xC0A66903,0xD1D11294,0xAA7DEB05,0x21274A87,0x09C4978D,0xC9E2F072,0x2ACB36B2,0xC26BF189,0x1AA170AE,0x7921D3A3,0xEA7CAB84}
	self.时辰={}
	for n=1,12 do
      self.时辰[n]=__res:取资源动画("jszy/fwtb", g[n],"精灵")
	end
    self.地图名称 = 文本字体:置颜色(255,255,255,255):取精灵("建业城")
 end

 function 背景:更新(dt)
     if __主显.主角 and __主显.主角.动作=="静立" then
          self.小人走路:更新(dt)
     else
          self.小人跑步:更新(dt)
     end
end
function 背景:显示(x, y)
    if __主显.主角 and __主显.主角.动作=="静立" then
        self.小人走路:显示(x+58,y+60)
    else
        self.小人跑步:显示(x+58,y+60)
    end
    self.时辰[时辰控件.时辰]:显示(x,y+15)
    if  self.地图名称 then
        self.地图名称:显示(x+(120-self.地图名称.宽度)//2,y+15)
    end
    文本字体:置颜色(255,255,255):显示(x+20+(80-文本字体:取宽度("X:"..(__主显.主角.xy.x//20).." ".."Y:"..(__主显.主角.xy.y//20)))//2, y + 64, "X:"..(__主显.主角.xy.x//20).." ".."Y:"..(__主显.主角.xy.y//20))
end
function 背景:左键弹起(x, y)
    if __手机 and not _tp.战斗中 then
        窗口层.小地图:打开(角色信息.地图数据.编号)
    end
end


local 缩放按钮 = 时辰控件:创建按钮( "缩放按钮",2,62)
function 缩放按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x6C566DEA),1)
end
function 缩放按钮:左键弹起(x, y)
    if 时辰控件.偏移==0 then
        时辰控件.昼夜:置可见(false)
        时辰控件.背景:置可见(false)
        时辰控件.灯笼1:置可见(false)
        时辰控件.灯笼2:置可见(false)
        时辰控件.灯笼3:置可见(false)
        时辰控件.日历:置可见(false)
        时辰控件.指引:置可见(false)
        时辰控件.商城:置可见(false)
        时辰控件.剑会:置可见(false)
        时辰控件.排行:置可见(false)
        时辰控件.会员:置可见(false)
        self:置坐标(2,0)
        时辰控件.偏移=1
    else
        时辰控件.昼夜:置可见(true)
        时辰控件.背景:置可见(true)
        时辰控件.灯笼1:置可见(true)
        时辰控件.灯笼2:置可见(true)
        时辰控件.灯笼3:置可见(true)
        时辰控件.日历:置可见(true)
        时辰控件.指引:置可见(true)
        时辰控件.商城:置可见(true)
        时辰控件.剑会:置可见(true)
        时辰控件.排行:置可见(true)
        if __会员显示 then
            时辰控件.会员:置可见(true)
        else
            时辰控件.会员:置可见(false)
        end
        self:置坐标(2,62)
        时辰控件.偏移=0
    end
end



local 灯笼1 = 时辰控件:创建按钮("灯笼1",95,22)
function 灯笼1:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0xBAF6A95D,"图像"),1)
end

function 灯笼1:左键弹起(x, y)
    if not _tp.战斗中 then
        窗口层.充值窗口:打开()
    end
end

local 灯笼2 = 时辰控件:创建按钮("灯笼2",95,40)
function 灯笼2:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0xBAF6A95D,"图像"),1)
end

function 灯笼2:左键弹起(x, y)
    if not _tp.战斗中 then
      
      请求服务(61,{文本="打开"})
    end
end
local 灯笼3 = 时辰控件:创建按钮("灯笼3",95,58)
function 灯笼3:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0xBAF6A95D,"图像"),1)
end
function 灯笼3:获得鼠标(x, y)
    __UI弹出.自定义:打开(x,y+42,"TAB打开小地图")
end
function 灯笼3:左键弹起(x, y)
    if not _tp.战斗中 then
        窗口层.小地图:打开(角色信息.地图数据.编号)
    end
end


local 指引 = 时辰控件:创建按钮("指引",1,80)
function 指引:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0xF05DB752),1)
end
function 指引:获得鼠标(x, y)
    __UI弹出.自定义:打开(x,y+42,"每日签到")
end
function 指引:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(46)
    end
end

local 日历 = 时辰控件:创建按钮("日历",59,80)
function 日历:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0xAF63A102),1)
end
function 日历:获得鼠标(x, y)
    __UI弹出.自定义:打开(x,y+42,"梦幻指引")
end
function 日历:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(40,{获取="打开"})
    end
end

local 剑会 = 时辰控件:创建按钮( "剑会",15,110)
function 剑会:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x1A23FA00),1)
  if __会员显示 then
      self:置坐标(10,110)
  end
end
function 剑会:获得鼠标(x, y)
    __UI弹出.自定义:打开(x,y+42,"剑会天下")
end
function 剑会:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(65)
    end
end

local 商城 = 时辰控件:创建按钮("商城",75,105)
function 商城:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x99998889),1)
  if __会员显示 then
     self:置坐标(50,105)
  end
end
function 商城:获得鼠标(x, y)
    __UI弹出.自定义:打开(x,y+42,"打开商城")
end
function 商城:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(29)
    end
end









local 会员 = 时辰控件:创建按钮("会员",83,105)
function 会员:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0x20000014),1)
end
function 会员:获得鼠标(x, y)
    __UI弹出.自定义:打开(x,y+42,"快捷功能")
end
function 会员:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(81)
    end
end



local 排行 = 时辰控件:创建按钮("排行",130,20)
function 排行:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0xFE277272),1)
end
function 排行:获得鼠标(x, y)
    __UI弹出.自定义:打开(x,y+42,"排行榜")
end
function 排行:左键弹起(x, y)
    if not _tp.战斗中 then
        if  窗口层.排行榜.是否可见 then
            窗口层.排行榜:置可见(false)
         else
           请求服务(39)
         end
    end
end


--------------------------------------------------------------------右下按钮组

local 按钮控件=玩家界面:创建控件("按钮控件")

function 按钮控件:初始化()
    if __手机 then
        self:置精灵()
        self:置宽高(545,110)
        self:置坐标(引擎.宽度-545, 引擎.高度-110)
        self.成就:置可见(false)
        self.任务:置可见(false)
    else
        self:置精灵(__res:取资源动画("dlzy", 0x3D1FA249,"精灵"))
        self:置宽高(350,63)
        self:置坐标(引擎.宽度-350, 引擎.高度-63)
        self.成就:置可见(true)
        self.任务:置可见(true)
    end
end
function 按钮控件:重新初始化()
    for k, v in self:遍历控件() do
        v:初始化()
        if v.重新初始化 then
            v:重新初始化()
        end
    end
end


local 攻击 = 按钮控件:创建按钮("攻击")
function 攻击:初始化()
    local tcp=__res:取资源动画("dlzy",0x6BBC42FA,"图像")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(52, 202, 44, 47)
        self:置坐标(0,55)
    else
        self:置坐标(3,33)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end
local 道具 = 按钮控件:创建按钮("道具")
function 道具:初始化()
    local tcp=__res:取资源动画("dlzy",0x0E53F705,"图像")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(469, 203, 45, 48)
        self:置坐标(55,55)
    else
        self:置坐标(30,33)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end
local 给予 = 按钮控件:创建按钮("给予")
function 给予:初始化()
    local tcp=__res:取资源动画("dlzy",0x7E4DE3DE,"图像")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(103, 201, 45, 50)
        self:置坐标(110,55)
    else
        self:置坐标(57,33)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end
local 交易 = 按钮控件:创建按钮("交易")
function 交易:初始化()
    local tcp=__res:取资源动画("dlzy",0xCAB0B8B4,"图像")
    if __手机 then
        --tcp=__res:取资源动画("dlzy",0xCAB0B8B4,"图像"):拉伸(45,45) --以前
        tcp=__res.UI素材[1]:复制区域(1068, 149, 45, 50)
        self:置坐标(165,55)
    else
        self:置坐标(84,34)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end
local 组队 = 按钮控件:创建按钮("组队")
function 组队:初始化()
    local tcp=__res:取资源动画("dlzy",0x0D3EA20B,"图像")
    self.消息动画=__res:取资源动画("dlzy",0x0D3EA20B,"动画")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(266, 201, 44, 49)
        self:置坐标(220,55)
    else
        self:置坐标(111,31)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
    
end

local 宠物 = 按钮控件:创建按钮("宠物")
function 宠物:初始化()
    local tcp=__res:取资源动画("dlzy",0x187ABFC8,"图像")
    if __手机 then
       -- tcp=__res:getPNGCC(2, 1150, 185, 40, 40):拉伸(45,45)
        tcp=__res.UI素材[1]:复制区域(1068, 200, 45, 50)
        self:置坐标(275,52)
    else
        self:置坐标(138,37)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end
local 任务 = 按钮控件:创建按钮("任务")
function 任务:初始化()
    local tcp=__res:取资源动画("dlzy",0xA15292B2,"图像")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(314, 201, 46, 50)
        --self:置坐标(245,55)
        self:置可见(false)
    else
        self:置坐标(165,37)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end

local 帮派 = 按钮控件:创建按钮("帮派")
function 帮派:初始化()
    local tcp=__res:取资源动画("dlzy",0xC35B2EC3,"图像")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(214, 200, 44, 51)
        self:置坐标(330,53)
    else
        self:置坐标(192,32)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end
local 快捷 = 按钮控件:创建按钮("快捷")
function 快捷:初始化()
    local tcp=__res:取资源动画("dlzy",0xBB6E607E,"图像")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(417, 203, 45, 48)
     
        self:置坐标(385,55)
    else
        self:置坐标(219,34)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end

local 好友 = 按钮控件:创建按钮("好友")
function 好友:初始化()
  local tcp=__res:取资源动画("dlzy",0x7C7A64D9,"图像")
  self.消息动画=__res:取资源动画("dlzy",0x7C7A64D9,"动画")
  if __手机 then
      tcp=__res.UI素材[1]:复制区域(516, 202, 46, 47) 
    
      self:置坐标(440,56)
  else
      self:置坐标(246,35)
  end
  self:置宽高(tcp.宽度,tcp.高度)
  self:创建按钮精灵(tcp,1)
end

local 成就 = 按钮控件:创建按钮("成就")
function 成就:初始化()
    local tcp=__res:取资源动画("dlzy",0x8B3AADDA,"图像")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(365, 202, 45, 47)
        self:置可见(false)
    else
        self:置坐标(273,34)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end

local 动作 = 按钮控件:创建按钮("动作")
function 动作:初始化()
    local tcp=__res:取资源动画("dlzy",0x548156A0,"图像")
    if __手机 then
        tcp=__res.UI素材[2]:复制区域(986, 122, 55, 55)  
        self:置坐标(485,0)
    else
        self:置坐标(300,34)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end



local 系统 = 按钮控件:创建按钮("系统")
function 系统:初始化()
    local tcp=__res:取资源动画("dlzy",0x5116F7DF,"图像")
    if __手机 then
        tcp=__res.UI素材[1]:复制区域(4, 201, 44, 50)
      
        self:置坐标(495,55)
    else
        self:置坐标(327,28)
    end
    self:置宽高(tcp.宽度,tcp.高度)
    self:创建按钮精灵(tcp,1)
end


  function 攻击:获得鼠标(x, y)
      __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+A")
  end
  function 攻击:左键弹起(x, y)
      if not _tp.战斗中 then
          界面层:重置("攻击")
          鼠标层:攻击形状()
      end
  end
  function 道具:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+E")
  end
  function 道具:左键弹起(x, y)
    if not _tp.战斗中 then
        if 窗口层.道具行囊.是否可见 then
            窗口层.道具行囊:关闭道具()
        elseif 窗口层.新行囊.是否可见 then
              窗口层.新行囊:关闭道具()
        else
            请求服务(3699)
        end
    end
  end
  function 给予:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+G")
  end
  function 给予:左键弹起(x, y)
    if not _tp.战斗中 then
        界面层:重置("给予")
        鼠标层:给予形状()
    end
  end

  function 交易:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+X")
  end
  function 交易:左键弹起(x, y)
    if not _tp.战斗中 then
        界面层:重置("交易")
        鼠标层:交易形状()
    end
  end


function 组队:获得鼠标(x, y)
        __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+T")
end

function 组队:更新(dt)
        if self.消息提醒 then
            self.消息动画:更新(dt)
        end
end



function 组队:显示(x, y)
    if self.消息提醒 then
        self.消息动画:显示(x-1, y-1)
    end
end



  function 组队:左键弹起(x, y)
        if not _tp.战斗中 then
            if self.消息提醒 and  __主显.主角 and  __主显.主角.是否队长 then
                请求服务(4003)
            else
                请求服务(4001)
            end
            
        end
  end


  function 宠物:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+O")
  end
  function 宠物:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(5006)
    end
  end
  function 任务:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+Q")
  end
  function 任务:左键弹起(x, y)
    if not _tp.战斗中 then
        if 窗口层.任务提示.是否可见 then
            窗口层.任务提示:打开()
        else
              请求服务(10)
        end
    end
  end

  function 帮派:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+B")
  end
  function 帮派:左键弹起(x, y)
    if not _tp.战斗中 then
        if 窗口层.帮派查看.可视 then
           窗口层.帮派查看:打开()
        else
            请求服务(6102)
        end
    end
  end

  function 快捷:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+C")
  end
  function 快捷:左键弹起(x, y)
    if not _tp.战斗中 then
        玩家界面.快捷控件:打开()
    end
  end
  function 好友:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+F")
  end
function 好友:更新(dt)
    if self.消息提醒 then
        self.消息动画:更新(dt)
    end
end



function 好友:显示(x, y)
    if self.消息提醒 then
        self.消息动画:显示(x-1, y-8)
    end
end



  function 好友:左键弹起(x, y)
    if not _tp.战斗中 then
        if 窗口层.好友列表.是否可见 and not  self.消息提醒 then
            窗口层.好友列表.关闭:左键弹起()
        else
            请求服务(16)
        end
    end
end

  function 成就:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"一键多开")
  end
  function 成就:左键弹起(x, y)
    if not _tp.战斗中 then
        if 窗口层.多开系统.是否可见 then
            窗口层.多开系统:置可见(false)
        else
           if  not _tp.战斗中  then
               请求服务(63,{参数=角色信息.数字id,文本="获取角色信息"})
           end
        end
    end
  end
  function 动作:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"藏宝阁")
  end
  function 动作:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(69,{文本="打开"})
    end
  end

  function 系统:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+S")
  end
  function 系统:左键弹起(x, y)
    if not _tp.战斗中 then
         窗口层.系统设置:打开()
    end
  end
---------------------------------------------------------------------人物召唤兽状态

function 玩家界面:重置人物()
    if 角色信息 then
        if not 角色信息.气血上限 then 角色信息.气血上限=角色信息.气血 end
        self.人物气血上限进度:置位置(math.floor(角色信息.气血上限/角色信息.最大气血 * 100))
        self.人物气血进度:置位置(math.floor(角色信息.气血/角色信息.最大气血 * 100))
        self.人物魔法进度:置位置(math.floor(角色信息.魔法/角色信息.最大魔法 * 100))
        self.人物愤怒进度:置位置(math.floor(角色信息.愤怒/150 * 100))
        self.人物经验进度:置位置(math.floor(角色信息.当前经验 / 角色信息.最大经验 * 100))
    else
        self.人物气血上限进度:置位置(0)
        self.人物气血进度:置位置(0)
        self.人物魔法进度:置位置(0)
        self.人物愤怒进度:置位置(0)
        self.人物经验进度:置位置(0)
    end
end



function 玩家界面:重置召唤兽()
    if 角色信息 and  角色信息.参战宝宝 and  角色信息.参战宝宝.名称 then
        self.宠物气血进度:置位置(math.floor(角色信息.参战宝宝.气血 / 角色信息.参战宝宝.最大气血 * 100))
        self.宠物魔法进度:置位置(math.floor(角色信息.参战宝宝.魔法 / 角色信息.参战宝宝.最大魔法 * 100))
        self.宠物经验进度:置位置(math.floor(角色信息.参战宝宝.当前经验 / 角色信息.参战宝宝.最大经验 * 100))
    else
        self.宠物气血进度:置位置(0)
        self.宠物魔法进度:置位置(0)
        self.宠物经验进度:置位置(0)
    end
end



local 人物头像=玩家界面:创建按钮("人物头像", 引擎.宽度-118,0, 50, 50)
function 人物头像:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy", 0x360B8373,"图像"))
    if __手机 then
        self:置坐标(引擎.宽度-123,0)
    else
        self:置坐标(引擎.宽度-118,0)
    end
end 

function 人物头像:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-30,y+50,"快捷键:ALT+W")
end

function 人物头像:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(7)
        窗口层.人物属性:打开()
    end
end

function 人物头像:置头像(数据)
    local lssj = __头像格子:创建()
    lssj:置头像(数据.模型,50,50)
    self.头像模型=lssj
    玩家界面:重置人物()

end

function 人物头像:显示(x,y)
    if  self.头像模型 then
        self.头像模型:显示(x,y)
    end

end

local 宠物头像=玩家界面:创建按钮("宠物头像", 引擎.宽度-230,0, 40, 40)
function 宠物头像:初始化()
    self:创建按钮精灵(__res:取资源动画("dlzy", 0x363AAF1B,"图像"))
    if __手机 then
        self:置坐标(引擎.宽度-235,0)
    else
        self:置坐标(引擎.宽度-230,0)
    end
end 
function 宠物头像:获得鼠标(x, y)
    __UI弹出.自定义:打开(x-40,y+30,"快捷键:ALT+R")
end

function 宠物头像:左键弹起(x, y)
    if not _tp.战斗中 then
        请求服务(5001)
    end
end

function 宠物头像:置头像(数据)
    local lssj = __头像格子:创建()
    lssj:置头像(数据.模型,40,40)
    self.头像模型=lssj
    玩家界面:重置召唤兽()
end

function 宠物头像:显示(x,y)
    if  self.头像模型 then
        self.头像模型:显示(x,y)
    end

end







local 人物状态背景={"人物血条","人物魔法","人物愤怒","人物经验"}
for i, v in ipairs(人物状态背景) do
        local 临时背景=玩家界面:创建按钮(v, 引擎.宽度-68,(i-1)*10)
        function 临时背景:初始化()
            self:创建按钮精灵(__res:取资源动画("dlzy", 0x2E8758EE,"图像"))
            if __手机 then
                self:置坐标(引擎.宽度-73,(i-1)*10)
            else
                self:置坐标(引擎.宽度-68,(i-1)*10)
            end
        end 
    
        function 临时背景:右键弹起(x, y)---电脑加血
            if 角色信息 and not _tp.战斗中 then
                if v=="人物血条" then
                    请求服务(3727,{类型=1})
                elseif v=="人物魔法" then
                    请求服务(3728,{类型=1})
                end
                
            end
     
        end
        function 临时背景:左键弹起(x, y)--手机加血
            if 角色信息 and not _tp.战斗中 and __手机 then
                if v=="人物血条" then
                  --  if  not 角色信息.气血上限 then 角色信息.气血上限=角色信息.气血 end
                    --__UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("气血："..角色信息.气血.."/"..角色信息.气血上限.."/"..角色信息.最大气血))
                    __UI弹出.气魔补充:打开("人物")
                elseif v=="人物魔法" then
                  --  __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("魔法:"..角色信息.魔法.."/"..角色信息.最大魔法))
                    __UI弹出.气魔补充:打开("人物")
                elseif v=="人物愤怒" then
                    __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("愤怒:"..角色信息.当前经验.."/150"))
                elseif v=="人物经验" then
                    __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("经验:"..角色信息.当前经验.."/"..角色信息.最大经验))
            
                end
                
            end
     
        end
    
        function 临时背景:获得鼠标(x, y)
            if 角色信息 then
                if v=="人物血条" then
                    if  not 角色信息.气血上限 then 角色信息.气血上限=角色信息.气血 end
                        __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("气血："..角色信息.气血.."/"..角色信息.气血上限.."/"..角色信息.最大气血))
                elseif v=="人物魔法" then
                    __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("魔法:"..角色信息.魔法.."/"..角色信息.最大魔法))
                   
                elseif v=="人物愤怒" then
                    __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("愤怒:"..角色信息.愤怒.."/150"))
                elseif v=="人物经验" then
                    __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("经验:"..角色信息.当前经验.."/"..角色信息.最大经验))
                end
            end
        end
    
    
    
    
        
    end
    
    
    



local 宠物状态背景={"宠物血条","宠物魔法","宠物经验"}
for i, v in ipairs(宠物状态背景) do
    local 临时宠物背景=玩家界面:创建按钮(v, 引擎.宽度-190,(i-1)*10)
    function 临时宠物背景:初始化()
        self:创建按钮精灵(__res:取资源动画("dlzy", 0x2E8758EE,"图像"))
        if __手机 then
            self:置坐标(引擎.宽度-195,(i-1)*10)
        else
            self:置坐标(引擎.宽度-190,(i-1)*10)
        end
    end 

    function 临时宠物背景:右键弹起(x, y)---电脑加血
        if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 and  not _tp.战斗中 then
            if v=="宠物血条" then
                请求服务(3727,{类型=2})
            elseif v=="宠物魔法" then
                请求服务(3728,{类型=2})
            end
            
        end
 
    end
    function 临时宠物背景:左键弹起(x, y)--手机加血
        if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 and not _tp.战斗中 and __手机 then
            if v=="宠物血条" then
              --  __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("气血："..角色信息.参战宝宝.气血.."/"..角色信息.参战宝宝.最大气血))
                __UI弹出.气魔补充:打开("召唤兽")

            elseif v=="宠物魔法" then
                --__UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("魔法："..角色信息.参战宝宝.魔法.."/"..角色信息.参战宝宝.最大魔法))
                __UI弹出.气魔补充:打开("召唤兽")
            elseif v=="宠物经验" then
                __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("经验："..角色信息.参战宝宝.当前经验.."/"..角色信息.参战宝宝.最大经验))
            end
            
        end
 
    end
    

    function 临时宠物背景:获得鼠标(x, y)
        if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 then
            if v=="宠物血条" then
                __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("气血："..角色信息.参战宝宝.气血.."/"..角色信息.参战宝宝.最大气血))
            elseif v=="宠物魔法" then
                __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("魔法："..角色信息.参战宝宝.魔法.."/"..角色信息.参战宝宝.最大魔法))
            elseif v=="宠物经验" then
                __UI弹出.自定义:打开(x-40,y+30,文本字体:置颜色(__取颜色("黄色")):取精灵("经验："..角色信息.参战宝宝.当前经验.."/"..角色信息.参战宝宝.最大经验))

            end
        end
    end
end



local 人物气血上限进度 = 玩家界面:创建进度("人物气血上限进度", 0, 0, 50, 8)
local 人物气血进度 = 玩家界面:创建进度("人物气血进度", 0, 0, 50, 8)


function 人物气血上限进度:初始化()---电脑加血
    self:置精灵(__res:取资源动画("dlzy", 0xAAD44583,"精灵"):置透明(80))
    if __手机 then
        self:置坐标(引擎.宽度-61, 2)
    else
        self:置坐标(引擎.宽度-56, 2)
    end
end
function 人物气血进度:初始化()---电脑加血
    self:置精灵(__res:取资源动画("dlzy", 0xAAD44583,"精灵"))
    if __手机 then
        self:置坐标(引擎.宽度-61, 2)
    else
        self:置坐标(引擎.宽度-56, 2)
    end
 end

function 人物气血上限进度:右键弹起(x, y)---电脑加血
    if 角色信息 and not _tp.战斗中 then
        玩家界面.人物血条:右键弹起(x, y)
    end
end
function 人物气血上限进度:左键弹起(x, y)--手机加血
    if 角色信息 and not _tp.战斗中 and __手机 then
        玩家界面.人物血条:左键弹起(x, y)
    end
end
function 人物气血上限进度:获得鼠标(x, y)
    if 角色信息 then
        玩家界面.人物血条:获得鼠标(x, y)
    end
end

function 人物气血进度:右键弹起(x, y)---电脑加血
    if 角色信息 and not _tp.战斗中 then
        玩家界面.人物血条:右键弹起(x, y)
    end
end
function 人物气血进度:左键弹起(x, y)--手机加血
    if 角色信息 and not _tp.战斗中 and __手机 then
        玩家界面.人物血条:左键弹起(x, y)
    end
end
function 人物气血进度:获得鼠标(x, y)
    if 角色信息 then
        玩家界面.人物血条:获得鼠标(x, y)
    end
end

local 人物魔法进度 = 玩家界面:创建进度("人物魔法进度", 引擎.宽度-56, 12, 50, 8)
function 人物魔法进度:初始化()---电脑加血
    self:置精灵(__res:取资源动画("dlzy", 0xCE4D3C2D,"精灵"))
    if __手机 then
        self:置坐标(引擎.宽度-61, 12)
    else
        self:置坐标(引擎.宽度-56, 12)
    end
end
function 人物魔法进度:右键弹起(x, y)---电脑加血
    if 角色信息 and not _tp.战斗中 then
        玩家界面.人物魔法:右键弹起(x, y)
    end
end
function 人物魔法进度:左键弹起(x, y)--手机加血
    if 角色信息 and not _tp.战斗中 and __手机 then
        玩家界面.人物魔法:左键弹起(x, y)
    end
end
function 人物魔法进度:获得鼠标(x, y)
    if 角色信息 then
        玩家界面.人物魔法:获得鼠标(x, y)
    end
end
local 人物愤怒进度 = 玩家界面:创建进度("人物愤怒进度", 引擎.宽度-56, 22, 50, 8)
function 人物愤怒进度:初始化()---电脑加血
    self:置精灵(__res:取资源动画("dlzy", 0xBAF8009F,"精灵"))
    if __手机 then
        self:置坐标(引擎.宽度-61, 22)
    else
        self:置坐标(引擎.宽度-56, 22)
    end
end
function 人物愤怒进度:左键弹起(x, y)--手机加血
    if 角色信息 and not _tp.战斗中 and __手机 then
        玩家界面.人物愤怒:左键弹起(x, y)
    end
end
function 人物愤怒进度:获得鼠标(x, y)
    if 角色信息 then
        玩家界面.人物愤怒:获得鼠标(x, y)
    end
end
local 人物经验进度 = 玩家界面:创建进度("人物经验进度", 引擎.宽度-56, 32, 50, 8)
function 人物经验进度:初始化()---电脑加血
    self:置精灵(__res:取资源动画("dlzy", 0x7B3C08E4,"精灵"))
    if __手机 then
        self:置坐标(引擎.宽度-61, 32)
    else
        self:置坐标(引擎.宽度-56, 32)
    end
end
function 人物经验进度:左键弹起(x, y)--手机加血
    if 角色信息 and not _tp.战斗中 and __手机 then
        玩家界面.人物经验:左键弹起(x, y)
    end
end
function 人物经验进度:获得鼠标(x, y)
    if 角色信息 then
        玩家界面.人物经验:获得鼠标(x, y)
    end
end

local 宠物气血进度 = 玩家界面:创建进度("宠物气血进度", 引擎.宽度-178, 2, 50, 8)
function 宠物气血进度:初始化()---电脑加血
    self:置精灵(__res:取资源动画("dlzy", 0xAAD44583,"精灵"))
    if __手机 then
        self:置坐标(引擎.宽度-183, 2)
    else
        self:置坐标(引擎.宽度-178, 2)
    end
end
function 宠物气血进度:右键弹起(x, y)---电脑加血
    if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 and  not _tp.战斗中 then
        玩家界面.宠物血条:右键弹起(x, y)
    end
end
function 宠物气血进度:左键弹起(x, y)--手机加血
    if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 and not _tp.战斗中 and __手机 then
        玩家界面.宠物血条:左键弹起(x, y)
    end
end
function 宠物气血进度:获得鼠标(x, y)
    if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 then
        玩家界面.宠物血条:获得鼠标(x, y)
    end
end



local 宠物魔法进度 = 玩家界面:创建进度("宠物魔法进度", 引擎.宽度-178, 12, 50, 8)
function 宠物魔法进度:初始化()---电脑加血
    self:置精灵(__res:取资源动画("dlzy", 0xCE4D3C2D,"精灵"))
    if __手机 then
        self:置坐标(引擎.宽度-183, 12)
    else
        self:置坐标(引擎.宽度-178, 12)
    end
end
function 宠物魔法进度:右键弹起(x, y)---电脑加血
    if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 and  not _tp.战斗中 then
        玩家界面.宠物魔法:右键弹起(x, y)
    end
end
function 宠物魔法进度:左键弹起(x, y)--手机加血
    if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 and not _tp.战斗中 and __手机 then
        玩家界面.宠物魔法:左键弹起(x, y)
    end
end
function 宠物魔法进度:获得鼠标(x, y)
    if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 then
        玩家界面.宠物魔法:获得鼠标(x, y)
    end
end
local 宠物经验进度 = 玩家界面:创建进度("宠物经验进度", 引擎.宽度-178, 22, 50, 8)
function 宠物经验进度:初始化()---电脑加血
    self:置精灵(__res:取资源动画("dlzy", 0x7B3C08E4,"精灵"))
    if __手机 then
        self:置坐标(引擎.宽度-183, 22)
    else
        self:置坐标(引擎.宽度-178, 22)
    end
end
function 宠物经验进度:左键弹起(x, y)--手机加血
    if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 and not _tp.战斗中 and __手机 then
        玩家界面.宠物经验:左键弹起(x, y)
    end
end
function 宠物经验进度:获得鼠标(x, y)
    if 角色信息 and 角色信息.参战宝宝 and 角色信息.参战宝宝.名称 then
        玩家界面.宠物经验:获得鼠标(x, y)
    end
end


---------------------------------------------------------------------快捷技能键
local 快捷控件 = 玩家界面:创建控件("快捷控件",0, 0,350,80)
function 快捷控件:初始化()
    self.锁定背景=__res:取资源动画("jszy/ui", 0X00000033,"精灵")
    self.上下格子 = __res:取资源动画("jszy/fwtb", 0X382A3975,"精灵") 
    self.锁住开关=false
    self.页数=1
    self.起始编号=0
    self.快捷显示={}
    for i=1,8 do
        self.快捷显示[i]=文本字体:置颜色(255,255,255,255):取描边精灵("F"..i)
    end
    if __手机 then
        self:置坐标(引擎.宽度-365, 引擎.高度-110)
    else
        self:置坐标(引擎.宽度-370, 引擎.高度-80)
    end
    



end
function 快捷控件:显示(x, y)
    self.锁定背景:显示(x, y+21)
    self.上下格子:显示(x+2+self.锁定背景.宽度, y+18)

    for i=1,8 do
        self.快捷显示[i]:显示(x+55+(i-1)*32,y)
    end

    
end
function 快捷控件:打开()
    self:置可见(not self.是否可见)
    if not self.是否可见 then return end
    self:置技能()
end


function 快捷控件:置技能()
    for i = 1, 8 do
        self["快捷F"..i]:置技能() 
    end
end




local 快捷锁=快捷控件:创建按钮("快捷锁",3,24,17,17)
function 快捷锁:初始化()
    self.锁住=__res:取资源动画("jszy/ui", 0X00000035,"图像"):拉伸(17, 17):到精灵()
    self.解锁=__res:取资源动画("jszy/ui", 0X00000034,"图像"):拉伸(17, 17):到精灵()
end
function 快捷锁:显示(x, y)
     if 快捷控件.锁住开关 then
        self.锁住:显示(x, y)
    else
        self.解锁:显示(x, y)
    end
end
function 快捷锁:左键弹起(x, y)
    快捷控件.锁住开关= not 快捷控件.锁住开关
end
function 快捷锁:获得鼠标(x, y)
    if 快捷控件.锁住开关 then
        __UI弹出.自定义:打开(x-70,y-40,"#Y技能框锁!点击解锁方可拖动技能到技能框哦!")
    end

end

local 上一页=快捷控件:创建按钮("上一页",28,20,15,15)
function 上一页:初始化()
    self.正常精灵=__res:取资源动画("jszy/fwtb", 0X72ECBE07,"图像"):拉伸(15, 15):到精灵()
    self.禁用精灵=__res:取资源动画("jszy/fwtb", 0X72ECBE07,"图像"):到灰度():拉伸(15, 15):到精灵()
end
function 上一页:显示(x, y)
    if 快捷控件.页数>1 then
        self.正常精灵:显示(x, y)
    else
        self.禁用精灵:显示(x, y)
    end
end 
 

function 上一页:左键弹起(x, y)
    if 快捷控件.页数>1 then
        快捷控件.页数= 快捷控件.页数-1
        快捷控件.起始编号=快捷控件.起始编号-8
        快捷控件:置技能()
    end
end
local 下一页=快捷控件:创建按钮("下一页",28,34,15,14)
function 下一页:初始化()
    self.正常精灵=__res:取资源动画("jszy/fwtb", 0X5B0263AB,"图像"):拉伸(15, 14):到精灵()
    self.禁用精灵=__res:取资源动画("jszy/fwtb", 0X5B0263AB,"图像"):到灰度():拉伸(15, 14):到精灵()
end
function 下一页:显示(x, y)
    if 快捷控件.页数<3 then
        self.正常精灵:显示(x, y)
    else
        self.禁用精灵:显示(x, y)
    end
end 
 

function 下一页:左键弹起(x, y)
    if 快捷控件.页数<3 then
        快捷控件.页数= 快捷控件.页数+1
        快捷控件.起始编号=快捷控件.起始编号+8
        快捷控件:置技能()
    end
end



function 快捷控件:取战斗可用(名称)
	if self:物攻技能(名称) then
		return true
	elseif self:封印技能(名称) then
		return true
	elseif self:增益技能(名称) then
		return true
	elseif self:减益技能(名称) then
		return true
	elseif self:法攻技能(名称) then
		return true
	elseif self:恢复技能(名称) then
		return true
	elseif 名称=="妙手空空" then
		return true
	end
end
function 快捷控件:恢复技能(名称)
	local 临时名称={"无穷妙道","地涌金莲","星月之惠","玉清诀","晶清诀","冰清诀","水清诀","四海升平","命归术","气归术","凝神诀","凝气诀","命疗术","心疗术","气疗术","归元咒","乾天罡气","我佛慈悲","杨柳甘露","推拿","推气过宫","解毒","百毒不侵","宁心","解封","清心","驱魔","驱尸","寡欲令","复苏"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 快捷控件:法攻技能(名称)
	local 临时名称={"夺命咒","落叶萧萧","荆棘舞","逍遥游","尘土刃","叱咤风云","天降灵葫","冰川怒","自爆","唧唧歪歪","五雷咒","落雷符","雨落寒沙","五雷轰顶","雷霆万钧","龙卷雨击","龙吟","二龙戏珠","龙腾","苍茫树","靛沧海","日光华","地裂火","巨岩破","三昧真火","飞砂走石","判官令","阎罗令","水攻","烈火","落岩","雷击","泰山压顶","水漫金山","地狱烈火","奔雷咒"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 快捷控件:物攻技能(名称)
	local 临时名称={"翻江搅海","惊涛怒","浪涌","天崩地裂","断岳势","裂石","满天花雨","破血狂攻","破碎无双","弱点击破","善恶有报","惊心一剑","壁垒击破","横扫千军","狮搏","象形","连环击","鹰击","烟雨剑法","飘渺式","天雷斩","裂石","断岳势","天崩地裂","浪涌","惊涛怒"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 快捷控件:封印技能(名称)
	local 临时名称={"摧心术","惊魂掌","煞气诀","夺魄令","反间之计","催眠符","失心符","落魄符","失忆符","追魂符","离魂符","失魂符","定身符","莲步轻舞","如花解语","似玉生香","娉婷嬝娜","镇妖","错乱","百万神兵","日月乾坤","威慑","含情脉脉","魔音摄魂","夺魄令","惊魂掌","煞气诀"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 快捷控件:减益技能(名称)
	local 临时名称={"尸腐毒","紧箍咒","勾魂","摄魄","雾杀","偷龙转凤"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 快捷控件:增益技能(名称)
	local 临时名称={"变身","碎星诀","不动如山","明光宝烛","移魂化骨","蜜润","炎护","后发制人","罗汉金钟","杀气诀","安神诀","分身术","达摩护体","金刚护法","金刚护体","韦陀护法","一苇渡江","佛法无边","楚楚可怜","天神护法","乘风破浪","神龙摆尾","生命之泉","炼气化神","天地同寿","乾坤妙法","普渡众生","灵动九天","幽冥鬼眼","修罗隐身","火甲术","魔王回首","定心术","极度疯狂","魔息术","天魔解体","盘丝阵","幻境术","不动如山","碎星诀","镇魂诀","明光宝烛","金身舍利","炎护","蜜润"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end

--角色信息.快捷技能

local 快捷按键={"快捷F1","快捷F2","快捷F3","快捷F4","快捷F5","快捷F6","快捷F7","快捷F8"}
for i, v in ipairs(快捷按键) do
    local 快捷技能键=快捷控件:创建按钮(v, 47+(i-1)*32,18)

    function 快捷技能键:初始化()
            self:创建按钮精灵(__res:取资源动画("jszy/ui", 0x00000032,"图像"))
            local lssj =  __技能格子:创建()
            lssj:置数据(nil,nil,true)
            self.技能信息=lssj
    end
    function 快捷技能键:显示(x,y)
        if self.技能信息 and self.技能信息.数据 then
            self.技能信息:显示(x+2,y+2)
        end
    end
    function 快捷技能键:获得鼠标(x, y)
        if self.技能信息 and self.技能信息.数据 then
                __UI弹出.技能详情:打开(self.技能信息.数据,x+20,y+20)
        end
    end
    function 快捷技能键:右键弹起(x, y)
            self:使用(i)
    end
  

    function 快捷技能键:左键弹起(x, y)
        if 鼠标层.附加 and (ggetype(鼠标层.附加) =="技能格子" or 窗口层.人物属性.是否可见 ) and  鼠标层.附加.技能数据  and 快捷控件.是否可见 and not 快捷控件.锁住开关  then
            请求服务(11,{位置=i+快捷控件.起始编号,名称=鼠标层.附加.技能数据.名称,类型=鼠标层.附加.技能数据.类型})
            鼠标层.附加=nil
            return
        end
        if self.技能信息.数据 and 角色信息 and 角色信息.快捷技能 and 角色信息.快捷技能[i+快捷控件.起始编号] then
            if __手机 then
                    __UI弹出.技能详情:打开(self.技能信息.数据,x+20,y+20,快捷技能键,"全部",i)
            else
                    self:拿起(i)
            end

        end
    end
    function 快捷技能键:使用(编号)
              if 编号 and 编号~=0 and self.技能信息 and self.技能信息.数据 then
                  if not  _tp.战斗中 then
                      请求服务(13,{序列=编号+快捷控件.起始编号})
                  else
                      if 快捷控件:取战斗可用(self.技能信息.数据.名称)  and __战斗主控.进程=="命令" then
                          界面层.战斗界面:设置法术参数(self.技能信息.数据)
                      end
                  end
              end

              
    end
    function 快捷技能键:拿起(编号)
            if 编号 and 编号~=0 and not 快捷控件.锁住开关 and not _tp.战斗中  then
                    鼠标层.附加=self.技能信息
                    鼠标层.附加.技能数据= 角色信息.快捷技能[i+快捷控件.起始编号]
                    鼠标层.附加.编号=编号
            end
    end


    
    function 快捷技能键:置技能()
        self.技能信息:置数据(nil,nil,true)
        if 角色信息 and 角色信息.快捷技能 and 角色信息.快捷技能[i+快捷控件.起始编号] then
            self.技能信息:置数据(角色信息.快捷技能[i+快捷控件.起始编号].名称,nil,true)
        end
    end


end










---------------------------------------------------------------------整体快捷键

function 玩家界面:键盘弹起(键码, 功能)
    if 功能 & SDL.KMOD_ALT ~= 0 and not  _tp.战斗中 then
        if 键码 == SDL.KEY_A then
            攻击:左键弹起()
        elseif 键码 == SDL.KEY_E then
              道具:左键弹起()
        elseif 键码 == SDL.KEY_G then
              给予:左键弹起()
        elseif 键码 == SDL.KEY_T then
              组队:左键弹起()
        elseif 键码 == SDL.KEY_S then
              系统:左键弹起()
   
        elseif 键码 == SDL.KEY_C  then
              快捷:左键弹起()
        elseif 键码 == SDL.KEY_B  then
              帮派:左键弹起()
        elseif 键码 == SDL.KEY_X then---交易
              交易:左键弹起()
        elseif 键码 == SDL.KEY_Q then
              任务:左键弹起()
        elseif 键码 == SDL.KEY_F then
              好友:左键弹起() 
        elseif 键码 == SDL.KEY_O then
              宠物:左键弹起()
        elseif 键码 == SDL.KEY_W then
              人物头像:左键弹起()
        elseif 键码 == SDL.KEY_R then
              宠物头像:左键弹起()
       

        end
    elseif 键码 == SDL.KEY_TAB and not _tp.战斗中 then
          if  not 窗口层.物品密码.是否可见 and not 窗口层.解锁修改.是否可见  then
              灯笼3:左键弹起()
          end
    elseif 键码 == SDL.KEY_F1 then
            快捷控件.快捷F1:右键弹起()
    elseif 键码 == SDL.KEY_F2 then
            快捷控件.快捷F2:右键弹起()
    elseif 键码 == SDL.KEY_F3 then
            快捷控件.快捷F3:右键弹起()
    elseif 键码 == SDL.KEY_F4 then
            快捷控件.快捷F4:右键弹起()
    elseif 键码 == SDL.KEY_F5 then
            快捷控件.快捷F5:右键弹起()
    elseif 键码 == SDL.KEY_F6 then
            快捷控件.快捷F6:右键弹起()
    elseif 键码 == SDL.KEY_F7 then
            快捷控件.快捷F7:右键弹起()
    elseif 键码 == SDL.KEY_F8 then
            快捷控件.快捷F8:右键弹起()
    elseif 键码 == SDL.KEY_F9 then
            if  __res.配置.显示玩家==1 then
                __res.配置.显示玩家=0
            else
                __res.配置.显示玩家=1
            end
            __res:写出文件( "config.txt", zdtostring(__res.配置))
    elseif 键码 == SDL.KEY_F10 and __手机 then
            -- 切换摇杆显示状态 (仅移动端)
            if __res.配置.显示摇杆==1 then
                __res.配置.显示摇杆=0
            else
                __res.配置.显示摇杆=1
            end
            -- 更新摇杆显示状态
            if self.移动摇杆 then
                self.移动摇杆:设置可见(__res.配置.显示摇杆==1)
            end
            __res:写出文件( "config.txt", zdtostring(__res.配置))
    elseif 键码 == SDL.KEY_F11 then
            if  __res.配置.屏蔽摊位==1 then
                __res.配置.屏蔽摊位=0
            else
                __res.配置.屏蔽摊位=1
            end
            __res:写出文件( "config.txt", zdtostring(__res.配置))

    end
  
  
  
  end
  



