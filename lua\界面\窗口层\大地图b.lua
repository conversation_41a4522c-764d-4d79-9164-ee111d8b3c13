
local  大地图b = 窗口层:创建窗口("大地图b",0, 0, 670, 510)
function  大地图b:初始化()
  self:创建纹理精灵(function()
            取黑色背景(0, 0, 670, 510, true):显示(0, 0)
            __res:取资源动画('dlzy',0x0B19A5DC,"图像"):显示(15, 15)
          end
        )

    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true

    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
end

function  大地图b:打开(传送)
    self:置可见(true)
    self.超级传送=nil
    if 传送 then
      self.超级传送=传送
    end
end



local 小西天 = 大地图b:创建按钮("小西天", 51+15, 70+15)
function 小西天:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xEA60ABA4),1)
end
function 小西天:左键弹起(x, y)
  窗口层.世界小地图:打开(1203,大地图b.超级传送)
  大地图b:置可见(false)
end

local 墨家村 = 大地图b:创建按钮( "墨家村", 155+15, 16+17)
function 墨家村:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xF617F5A9),1)
end
function 墨家村:左键弹起(x, y)
  窗口层.世界小地图:打开(1218,大地图b.超级传送)
  大地图b:置可见(false)
end

local 无名鬼蜮 = 大地图b:创建按钮( "无名鬼蜮", 274+17, 18+15)
function 无名鬼蜮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x5B96A2DD),1)
end
function 无名鬼蜮:左键弹起(x, y)
  窗口层.世界小地图:打开(1202,大地图b.超级传送)
  大地图b:置可见(false)
end

local 魔王寨 = 大地图b:创建按钮( "魔王寨", 189+15, 94+17)
function 魔王寨:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x150B5122),1)
end
function 魔王寨:左键弹起(x, y)
  窗口层.世界小地图:打开(1512,大地图b.超级传送)
  大地图b:置可见(false)
end

local 阴曹地府 = 大地图b:创建按钮("阴曹地府", 332+15, 34+16)
function 阴曹地府:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x9C992236),1)
end
function 阴曹地府:左键弹起(x, y)
  窗口层.世界小地图:打开(1122,大地图b.超级传送)
  大地图b:置可见(false)
end

local 江州 = 大地图b:创建按钮( "江州", 350+15, 114+15)
function 江州:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x991B588D),1)
end
function 江州:左键弹起(x, y)
  窗口层.世界小地图:打开(1110,大地图b.超级传送)
  大地图b:置可见(false)
end

local 洪州 = 大地图b:创建按钮("洪州", 296+20, 129+15)
function 洪州:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x6EA52AE1), 1)
end
function 洪州:左键弹起(x, y)
  窗口层.世界小地图:打开(1110,大地图b.超级传送)
  大地图b:置可见(false)
end

local 大唐官府 = 大地图b:创建按钮( "大唐官府", 451+15, 101+16)
function 大唐官府:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xE0C66A6F),1)
end
function 大唐官府:左键弹起(x, y)
  窗口层.世界小地图:打开(1198,大地图b.超级传送)
  大地图b:置可见(false)
end

local 狮驼岭 = 大地图b:创建按钮("狮驼岭", 4+15, 185+15)
function 狮驼岭:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xBF78C1EF),1)
end
function 狮驼岭:左键弹起(x, y)
  窗口层.世界小地图:打开(1131,大地图b.超级传送)
  大地图b:置可见(false)
end

local 高老庄 = 大地图b:创建按钮("高老庄", 106+15, 232+19)
function 高老庄:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xFF1D4A14),1)
end
function 高老庄:左键弹起(x, y)
  窗口层.世界小地图:打开(1173,大地图b.超级传送)
  大地图b:置可见(false)
end

local 盘丝洞 = 大地图b:创建按钮( "盘丝洞", 213+16, 194+15)
function 盘丝洞:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x301A4FE7),1)
end
function 盘丝洞:左键弹起(x, y)
  窗口层.世界小地图:打开(1513,大地图b.超级传送)
  大地图b:置可见(false)
end


local 长安城 = 大地图b:创建按钮( "长安城", 366+15, 140+15)
function 长安城:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x71DF41E1),1)
end
function 长安城:左键弹起(x, y)
  窗口层.世界小地图:打开(1001,大地图b.超级传送)
  大地图b:置可见(false)
end

local 化生寺 = 大地图b:创建按钮("化生寺", 478+20, 158+15)
function 化生寺:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x47FE6497), 1)
end
function 化生寺:左键弹起(x, y)
  窗口层.世界小地图:打开(1002,大地图b.超级传送)
  大地图b:置可见(false)
end

local 五庄观 = 大地图b:创建按钮("五庄观", 232+15, 270+15)
function 五庄观:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xCA26DE5D), 1)
end
function 五庄观:左键弹起(x, y)
  窗口层.世界小地图:打开(1146,大地图b.超级传送)
  大地图b:置可见(false)
end

local 神木林 = 大地图b:创建按钮("神木林", 371+15, 258+15)
function 神木林:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xCAF25E94), 1)
end
function 神木林:左键弹起(x, y)
  窗口层.世界小地图:打开(1138,大地图b.超级传送)
  大地图b:置可见(false)
end

local 建邺城 = 大地图b:创建按钮( "建邺城", 470+15, 231+15)
function 建邺城:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x6AB41A9A),1)
end
function 建邺城:左键弹起(x, y)
  窗口层.世界小地图:打开(1501,大地图b.超级传送)
  大地图b:置可见(false)
end

local 凌波城 = 大地图b:创建按钮( "凌波城", 372+15, 314+15)
function 凌波城:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xBDAEEA53),1)
end
function 凌波城:左键弹起(x, y)
  窗口层.世界小地图:打开(1150,大地图b.超级传送)
  大地图b:置可见(false)
end

local 普陀山 = 大地图b:创建按钮("普陀山", 332+15, 411+15)
function 普陀山:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xEC9EBCA8),1)
end
function 普陀山:左键弹起(x, y)
  窗口层.世界小地图:打开(1140,大地图b.超级传送)
  大地图b:置可见(false)
end

local 龙宫 = 大地图b:创建按钮( "龙宫", 470+15, 374+15)
function 龙宫:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xAD1DC28A),1)
end
function 龙宫:左键弹起(x, y)
  窗口层.世界小地图:打开(1116,大地图b.超级传送)
  大地图b:置可见(false)
end
local 关闭 = 大地图b:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  大地图b:置可见(false)
end






