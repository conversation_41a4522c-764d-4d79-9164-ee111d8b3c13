
local 召唤兽查看 = 窗口层:创建窗口("召唤兽查看", 0,0, 500, 470)
local 资质列表={"攻击资质","防御资质","体力资质","法力资质","速度资质","躲闪资质"}
local bd = {"等级","体质","魔力","力量","耐力","敏捷"}
local bd1 = {"气血","魔法","伤害","防御","速度","灵力"}
function 召唤兽查看:初始化()
  self:创建纹理精灵(function()
      置窗口背景("召唤兽", 0, 0, 500, 470, true):显示(0, 0)
      __res:取资源动画("dlzy",0x4FC09361,"图像"):复制区域(14,31,184,66):显示(280,33)
      __res:取资源动画("jszy/fwtb",0xabcd0204,"图像"):平铺(16,430):显示(245,30)
      取白色背景(0, 0, 215, 160, true):显示(20, 33)
       文本字体:置颜色(255,255,255,255)
      for i, v in ipairs(资质列表) do
          取输入背景(0, 0, 145, 23):显示(335,102+(i-1)*25)
          文本字体:取图像(v):显示(270, 105+(i-1)*25)
         
      end
      文本字体:取图像("名称"):显示(20, 204)
      取输入背景(0, 0, 185, 23):显示(50,200)
      for i, v in ipairs(bd1) do
          取输入背景(0, 0, 80, 23):显示(50,200+i*30)
          文本字体:取图像(v):显示(20, 203+i*30)
      end
      for i, v in ipairs(bd) do
          取输入背景(0, 0, 70, 23):显示(165,200+i*30)
          文本字体:取图像(v):显示(135, 203+i*30)
      end
      文本字体:置颜色(0,0,0):取图像("参战等级:"):显示(30, 45)
      文本字体:置颜色(__取颜色("橙色"))
      取输入背景(0, 0, 145, 23):显示(335,252)
      文本字体:取图像("寿    命"):显示(270, 255)
      取输入背景(0, 0, 145, 23):显示(335,277)
      文本字体:取图像("成    长"):显示(270, 280)
      取输入背景(0, 0, 145, 23):显示(335,302)
      文本字体:取图像("五    行"):显示(270, 305)
    



    end
  )





    self.可初始化=true
    self.模型格子 = __UI模型格子.创建()
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-27, 2)
    else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-18, 2)
    end
end






function 召唤兽查看:显示(x, y)
  if self.模型格子 then
        self.模型格子:显示(x, y)
  end
   if self.资质图片  then 
        self.资质图片:显示(x,y)
   end
   if self.类型图片 then
      self.类型图片:显示(x+55+ 文本字体:取宽度(self.属性数据.名称),y+204)
   end
end

function 召唤兽查看:更新(dt)
  if self.模型格子 then
      self.模型格子:更新(dt)
  end
  
end

function 召唤兽查看:打开(数据)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.类型图片=nil
    self.模型格子:清空()
    self:刷新(数据)
    
   
end
function 召唤兽查看:刷新(数据)
        self.属性数据=nil
        self.资质图片=nil
        if 数据 and 数据.名称 then
            self.属性数据=table.copy(数据)
        end
        self:显示设置() 
end


function 召唤兽查看:显示设置()
        self.技能控件:置数据()
        self.装备网格:置物品()
        if self.属性数据 then
            self.模型格子:置数据(self.属性数据, "召唤兽", 130,180)
            self.资质图片 =self:创建纹理精灵(function()
              文本字体:置颜色(0,0,0,255)
              local 资质={}
              for i, v in ipairs(资质列表) do
                      资质[v]=文本字体:取图像(self.属性数据[v])
                      资质[v]:显示(345, 105+(i-1)*25)
              end
              if self.属性数据.种类=="神兽" then
                  文本字体:取图像("★永生★"):显示(345, 255)
              else
                  文本字体:取图像(self.属性数据.寿命):显示(345, 255)
              end
              文本字体:取图像(self.属性数据.成长):显示(345, 280)
              文本字体:取图像(self.属性数据.五行):显示(345, 305)
              文本字体:取图像(self.属性数据.参战等级):显示(95, 45)
              文本字体:取图像(self.属性数据.名称):显示(55, 204)
              for i, v in ipairs(bd1) do
                  文本字体:取图像(self.属性数据[v]):显示(55, 204+i*30)
              end
              for i, v in ipairs(bd) do
                  文本字体:取图像(self.属性数据[v]):显示(170, 204+i*30)
              end
            end,1
          )
            self.技能控件:置数据(self.属性数据)
            self.装备网格:置物品(self.属性数据.装备)
        end
end

local  装备网格=召唤兽查看:创建网格("装备网格", 285, 38,170,52)

function 装备网格:初始化()
    self:创建格子(51, 51, 9, 9, 1, 3)
end



function 装备网格:置物品(数据)
    for i = 1, #self.子控件 do
            local lssj = __物品格子:创建()
            lssj:置物品(nil,50,50)
            if 数据 and 数据[i] then
                lssj:置物品(数据[i],50,50,nil,true)
            end
            self.子控件[i]:置精灵(lssj)   
      end
end

function 装备网格:获得鼠标(x,y,a)
        self.子控件[a]._spr.焦点=true
        if self.子控件[a]._spr and self.子控件[a]._spr.物品  then
                __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
        end
end
function 装备网格:失去鼠标(x,y)
        for i = 1, #self.子控件 do
            self.子控件[i]._spr.焦点=nil
        end
end
function 装备网格:左键弹起(x, y, a)
    if self.子控件[a]._spr and self.子控件[a]._spr.物品 and __手机 then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end




local  技能控件=召唤兽查看:创建技能内丹控件("技能控件", 270, 330)
技能控件.进阶:置禁止(true)
技能控件.内丹:置禁止(true)
技能控件.超级图标=nil


local 显示类型=召唤兽查看:创建红色按钮("显示类型","显示类型", 100, 425,100, 30,标题字体)  
function 显示类型:左键弹起(x, y)
      if 召唤兽查看.类型图片 then
          召唤兽查看.类型图片=nil
      else
          召唤兽查看.类型图片=文本字体:置颜色(0,0,0,255):取精灵("("..召唤兽查看.属性数据.种类..")")
      end
end


local 关闭 = 召唤兽查看:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  召唤兽查看:置可见(false)
end