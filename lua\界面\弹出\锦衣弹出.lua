__UI弹出["锦衣弹出"] = __UI界面["创建弹出窗口"](__UI界面, "锦衣弹出", 280-145 + abbr.py.x, 189-25 + abbr.py.y, 172, 162)
local 锦衣弹出 = __UI弹出["锦衣弹出"]
function 锦衣弹出:初始化()
  local nsf = require("SDL.图像")(172, 162)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 172, 162, true):置透明(230):显示(0, 0)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 20, 14)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 97, 14)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 20, 91)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 97, 91)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 锦衣弹出:打开(data)
  self:置可见(true)

  self.道具网格["置物品"](self.道具网格, data)
end
local 道具网格 = 锦衣弹出["创建网格"](锦衣弹出, "道具网格", 20, 14, 137, 214)
function 道具网格:初始化()
  self:创建格子(67, 67, 10, 10, 2, 2)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520-140, 86, w, h, "锦衣", a)
  end
end
function 道具网格:置物品(data)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], nil, "装备")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
