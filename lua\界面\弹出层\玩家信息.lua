--[[
LastEditTime: 2024-09-19 17:36:46
--]]
__UI弹出.玩家信息 = 界面:创建弹出窗口("玩家信息", 0, 0, 160, 190)
local 玩家信息 = __UI弹出.玩家信息
function 玩家信息:初始化()
  self.头像背景 = __res:取资源动画("dlzy", 0x360B8373,"精灵") 
  self.介绍背景 = __res:取资源动画("dlzy", 0x770F5F96,"精灵")
  self.按钮背景 = __res:取资源动画("dlzy", 0xA0D00989,"精灵")
end
function 玩家信息:显示(x, y)
    self.头像背景:显示(x, y)
    self.介绍背景:显示(x+50, y)
    self.按钮背景:显示(x+50, y+50)
    if  self.头像模型 then
        self.头像模型:显示(x,y)
    end
    if  self.玩家信息 then
      for i = 1, 3 do
          if self.玩家信息[i] then
              self.玩家信息[i]:显示(x+55,y+(i-1)*17)
          end
      end
  end
end


function 玩家信息:打开(信息)
  self:置可见(true)
  self.数据=nil
  self.头像模型=nil
  self.玩家信息={}

  if 信息 then
      self.数据=信息
      if 信息.模型 then
          local lssj = __头像格子:创建()
          lssj:置头像(信息.模型,50,50)
          self.头像模型=lssj
      end
      for i = 1, 3 do
          if 信息[i] then
              self.玩家信息[i]=文本字体:置颜色(__取颜色("黄色")):取描边精灵(信息[i],100,100,100,100)
          end
      end
  end
  


  if __手机 then
      self:置坐标(引擎.宽度-395,60)
  else
      self:置坐标(引擎.宽度-390,60)
  end
end

local 按钮组 = {'给予',"组队","好友","团队","观察","交易","装备","攻击","信息","临时"}

local xx=0
local yy=0
for i, v in ipairs(按钮组) do
    local 临时按钮=玩家信息:创建按钮(v)
    local tcp = __res:取资源动画("dlzy", 0x91D4E204) 
    临时按钮:置宽高(tcp.宽度,tcp.高度)
    临时按钮:置坐标(60+xx*(tcp.宽度+10),52+yy*(tcp.高度+5))
    function 临时按钮:初始化()
        self:创建按钮精灵(tcp,1,v,nil,nil,nil,"黑色")
    end
    function 临时按钮:左键弹起(x, y)
            if 玩家信息.数据  and 玩家信息.数据[2] and __主显.玩家[玩家信息.数据[2]] and 玩家信息.数据[2] < 99999 then
                if v=='给予' then
                    请求服务(3716,{id=玩家信息.数据[2]})
                elseif v=='组队' then
                      if __主显.玩家[玩家信息.数据[2]].是否队长  then --有队伍的情况
                            请求服务(4002,{id=玩家信息.数据[2]})
                      else
                            请求服务(4014,{id=玩家信息.数据[2]})
                      end
                elseif v=='好友' then      
                      请求服务(19,{名称=玩家信息.数据[1],id=玩家信息.数据[2],类型=1})
                elseif v=='观察' or v=='装备' or v=='信息' then
                      请求服务(9000,{id=玩家信息.数据[2]})
                elseif v=='交易' then
                      请求服务(3718,{id=玩家信息.数据[2]})
                elseif v=='攻击' then
                        请求服务(33,{序列=玩家信息.数据[2]})
                elseif v=='临时' then      
                        请求服务(18,{名称=玩家信息.数据[1],id=玩家信息.数据[2],类型=1})
                end

            else
                __UI弹出.提示框:打开("#Y对不起!对方不允许"..v.."!")
            end
    end


    xx=xx+1
    if xx>=2 then
        xx=0
        yy=yy+1
    end
  
end

-- for i=1,10 do
--   self.资源组[i+2] = 按钮.创建(tp.资源:载入('登陆资源.wdf',"网易WDF动画",0x91D4E204),0,0,4,true,true)
--   self.资源组[i+2]:置偏移(-4,0)
-- end
