local 人物修炼学习 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "人物修炼学习", 312 + abbr.py.x, 67 + abbr.py.y, 485, 365)
local 计算修炼等级经验 = function(等级, 上限)
  local 临时经验 = 110
  if 0 == 等级 then
    return 110
  end
  for n = 1, 上限 + 1 do
    临时经验 = 临时经验 + 20 + n * 20
    if n == 等级 then
      return 临时经验
    end
  end
end
function 人物修炼学习:初始化()
  local nsf = require("SDL.图像")(485, 365)
  if nsf["渲染开始"](nsf) then
    置窗口背景("人物修炼学习", 0, 12, 477, 356, true)["显示"](置窗口背景("人物修炼学习", 0, 12, 477, 356, true), 0, 0)
    取白色背景(0, 0, 450, 175, true)["显示"](取白色背景(0, 0, 450, 175, true), 16, 43)
    local lssj = 取输入背景(0, 0, 123, 23)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "帮派现有资金")["显示"](字体18["取图像"](字体18, "帮派现有资金"), 15, 237)
    lssj["显示"](lssj, 133, 235)
    字体18["取图像"](字体18, "现金")["显示"](字体18["取图像"](字体18, "现金"), 274, 237)
    lssj["显示"](lssj, 320, 235)
    字体18["取图像"](字体18, "修炼所需金钱")["显示"](字体18["取图像"](字体18, "修炼所需金钱"), 15, 273)
    lssj["显示"](lssj, 133, 271)
    字体18["取图像"](字体18, "帮贡")["显示"](字体18["取图像"](字体18, "帮贡"), 274, 273)
    lssj["显示"](lssj, 320, 271)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 人物修炼学习:打开(data)
  self:置可见(true)
  self:重置(data)
  self.选中 = nil
end
function 人物修炼学习:重置(data)
  self.数据 = data
  local nsf = require("SDL.图像")(477, 70)
  if nsf["渲染开始"](nsf) then
    local lssj = 取输入背景(0, 0, 123, 23)
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    -- 字体18["取图像"](字体18, self.数据["帮派资材"])["显示"](字体18["取图像"](字体18, self.数据["帮派资材"]), 143, 7)
    -- 字体18["取图像"](字体18, self.数据["银子"])["显示"](字体18["取图像"](字体18, self.数据["银子"]), 330, 7)
    -- 字体18["取图像"](字体18, "20000")["显示"](字体18["取图像"](字体18, "20000"), 143, 42)
    -- 字体18["取图像"](字体18, "5")["显示"](字体18["取图像"](字体18, "5"), 330, 42)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.图像["置中心"](self.图像, 0, -227)
  -- self.修炼列表["重置"](self.修炼列表, self.数据["人物修"])
end
local 关闭 = 人物修炼学习["创建我的按钮"](人物修炼学习, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 435, 0)
function 关闭:左键弹起(x, y, msg)
  人物修炼学习["置可见"](人物修炼学习, false)
end
local 修炼列表 = 人物修炼学习["创建列表"](人物修炼学习, "修炼列表", 22, 50, 436, 161)
function 修炼列表:初始化()
  self:置文字(字体20)
  self.行高度 = 32
  self.行间距 = 0
end
local lsb = {
  "攻击修炼",
  "防御修炼",
  "法术修炼",
  "抗法修炼",
  "猎术修炼"
}
function 修炼列表:重置(data)
  self.清空(self)
  for _, v in ipairs(lsb) do
    local nsf = require("SDL.图像")(436, 32)
    if nsf["渲染开始"](nsf) then
      if 1 == _ % 2 then
      end
      -- 字体16["置颜色"](字体16, __取颜色("浅黑"))
      -- 字体16["取图像"](字体16, v .. "      等级：" .. data[v][1] .. "/" .. data[v][3] .. "      修炼经验" .. data[v][2] .. "/" .. 计算修炼等级经验(data[v][1], data[v][3]))["显示"](字体16["取图像"](字体16, v .. "      等级：" .. data[v][1] .. "/" .. data[v][3] .. "      修炼经验" .. data[v][2] .. "/" .. 计算修炼等级经验(data[v][1], data[v][3])), 44, 7)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 修炼列表:左键弹起(x, y, i, item, msg)
  人物修炼学习["选中"] = i
end
local 修炼 = 人物修炼学习["创建我的按钮"](人物修炼学习, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "修炼", 200, 305, "修炼")
function 修炼:左键弹起(x, y, msg)
  if 人物修炼学习["选中"] then
    发送数据(44, {
      ["类别"] = lsb[人物修炼学习["选中"]]
    })
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请选择你要修炼的项目")
  end
end
