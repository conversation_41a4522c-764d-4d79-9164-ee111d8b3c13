local 创建角色 = 登录层:创建控件("创建角色", 0 , 0 , 引擎.宽度, 引擎.高度)

local  角色图片组 = {
    [1] = {
      模型="飞燕女", 种族="人", 兵器="可用兵器为：双剑、环圈", 门派="可选择门派：大唐官府、方寸山、女儿村、神木林",
      介绍="    深山有佳人,灼灼芙蓉姿,飞燕女轻盈飘逸,灵慧动人,自幼怜爱弱小,嫉恶如仇,一生自由自在,是大自然骄纵的宠儿",
      图片= __res:取资源动画("dlzy",0xAADDEE72,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE95,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE15,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE87,"图像"),
      立绘偏移X = 0, 立绘偏移Y = 0,
      },
    [2] = {
      模型="英女侠", 种族="人", 兵器="可用兵器为：双剑、长鞭", 门派="可选择门派：大唐官府、方寸山、女儿村、神木林",
      介绍="    兰心惠质出名门,英姿飒爽自芳华,英女侠天资聪颖,精通琴棋书画,心怀仁爱,行善不落人后,是位侠骨柔情的奇女子",
      图片= __res:取资源动画("dlzy",0xAADDEE77,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE96,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE71,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE02,"图像"),
      立绘偏移X = 10, 立绘偏移Y = -20,
      },
    [3] = {
      模型="巫蛮儿", 种族="人", 兵器="可用兵器为：宝珠、法杖", 门派="可选择门派：大唐官府、方寸山、女儿村、神木",
      介绍="    嫣然巧笑踏绿萝,一路银铃一路歌,巫蛮儿质朴单纯,灵动可人,生性善良,活泼可爱,花盈翠影出神木,环佩婉转披香来",
      图片= __res:取资源动画("dlzy",0xAADDEE74,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE97,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE17,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE00,"图像"),   
      立绘偏移X = -20, 立绘偏移Y = 10,
     },
    [4] = {
      模型="偃无师", 种族="人", 兵器="可用兵器为：巨剑、剑", 门派="可选择门派：大唐官府、化生寺、方寸山、神木林",
      介绍="    铁手隐机枢，巧夺天工，猛力执巨剑，志敌万均。偃无师性情冷厉，疏狂不羁，亦有奇谋满腹，铮铮傲骨。",
      图片= __res:取资源动画("dlzy",0xAADDEE76,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE98,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE19,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE03,"图像"),   
      立绘偏移X = 0, 立绘偏移Y = 0,
     },
    [5] = {
      模型="逍遥生", 种族="人", 兵器="可用兵器为：剑、扇", 门派="可选择门派：大唐官府、化生寺、方寸山、神木林",
      介绍="    快意恩仇事,把酒踏歌行,一袭白衫,一纸折扇,逍遥生风流倜傥,潇洒自如,行事光明磊落,是世人乐于结交的谦谦君子",   
      图片= __res:取资源动画("dlzy",0xAADDEE75,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE99,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE18,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE04,"图像"),   
      立绘偏移X = -40, 立绘偏移Y = 20,
     },
    [6] = {
      模型="剑侠客", 种族="人", 兵器="可用兵器为：刀、剑", 门派="可选择门派：大唐官府、化生寺、方寸山、神木林",
      介绍="    霜刃露锋芒,飒沓如流星,剑侠客率情任性,狂放不羁,一生淡泊名利,嗜武如痴,英雄意,儿女情,独闯江湖半生醉,举杯邀月最销魂",
      图片= __res:取资源动画("dlzy",0xAADDEE73,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDCC00,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE16,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE01,"图像"),   
      立绘偏移X = -105, 立绘偏移Y = 10,
    },
    [7] = {
      模型="狐美人", 种族="魔", 介绍="    修眉连娟,斜挑入眉,媚眼如丝,含娇含笑,狐美人柔情绰态,胜似海棠醉日,风情万种,颠倒众生",兵器="可用兵器为：爪刺、鞭", 门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞",
      图片= __res:取资源动画("dlzy",0xAADDEE07,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE35,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE60,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE05,"图像"),   
      立绘偏移X = 50, 立绘偏移Y = 30,
    },
    [8] = {
      模型="骨精灵", 种族="魔", 介绍="    眉黛春山秀,横波剪秋水,骨精灵娇妍俏皮,顾盼神飞,机敏聪慧,好打不平,对世间万物充满好奇",兵器="可用兵器为：爪刺、魔棒", 门派="可选择门派：盘丝洞、阴曹地府、魔王寨、无底洞",
      图片= __res:取资源动画("dlzy",0xAADDEE57,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE36,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE56,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE54,"图像"),   
      立绘偏移X = 60, 立绘偏移Y = 50,
    },
    [9] = {
      模型="鬼潇潇", 种族="魔", 介绍="    寒眸印秋水，魂隐三生途，素手执竹伞，影馀幽冥路。鬼潇潇青丝如墨，红杉如火，一对异色瞳里似乎藏着无尽的忧愁和神秘。", 兵器="可用兵器为：爪刺、伞", 门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",
      图片= __res:取资源动画("dlzy",0xAADDEE59,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE37,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE58,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE88,"图像"),   
      立绘偏移X = 50, 立绘偏移Y = -50,
    },
    [10] = {
      模型="杀破狼", 种族="魔", 介绍="    一啸生风雪,长歌动寒霜,杀破狼飘逸潇洒,气宇轩昂,能文能武,卓尔不群,身具的神秘天狼血统,纵横骄天下,傲立三界间.", 兵器="可用兵器为：弓弩、宝珠", 门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",
      图片= __res:取资源动画("dlzy",0xAADDEE61,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE38,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE12,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE51,"图像"),  
      立绘偏移X = 70, 立绘偏移Y = -50,
    },
    [11] = {
      模型="巨魔王", 种族="魔", 介绍="    一怒震乾坤,杀气凝如山,巨魔王力拔山气兮盖世,肩负魔族神秘使命,叱咤风云,威风凛凛", 兵器="可用兵器为：斧钺、刀", 门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",
      图片= __res:取资源动画("dlzy",0xAADDEE11,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE39,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE10,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE52,"图像"),  
      立绘偏移X = -10, 立绘偏移Y = 0,
    },
    [12] = {
      模型="虎头怪", 种族="魔", 介绍="    戏谑犹可爱,虽有神力不欺人,虎头怪弯弧五百步,长戟八十斤,勇武过人,生性耿直豁达,对朋友忠肝义胆,是顶天立地的大丈夫", 兵器="可用兵器为：斧钺、锤", 门派="可选择门派：狮驼岭、魔王寨、阴曹地府、无底洞",
      图片= __res:取资源动画("dlzy",0xAADDEE09,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDEE40,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE08,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE53,"图像"),  
      立绘偏移X = -30, 立绘偏移Y = -50,
    },
    [13] = {
      模型="舞天姬", 种族="仙", 介绍="    霓裳曳广带,飘拂升天行,舞天姬明眸珠辉,瑰姿艳逸,生性善解人意,令人如沐春风.一舞绡丝动四方,观之心魂俱醉",兵器="可用兵器为：飘带、环圈", 门派="可选择门派：天宫、普陀山、龙宫、凌波城",
      图片= __res:取资源动画("dlzy",0xAADDEE81,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDCC01,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE21,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE27,"图像"),  
      立绘偏移X = -30, 立绘偏移Y = -10,
    },
    [14] = {
      模型="玄彩娥", 种族="仙", 兵器="可用兵器为：飘带、魔棒", 门派="可选择门派：龙宫、普陀山、天宫、凌波城",
      介绍="    桂露对仙娥,星星下云逗,玄彩娥在花从中蝶翼翩翩,婀娜曼妙,犹聚晨露新聚,奇花初蕊,是集天地灵气于一身的百花仙子",
      图片= __res:取资源动画("dlzy",0xAADDEE82,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDCC02,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE22,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE50,"图像"), 
      立绘偏移X = 60, 立绘偏移Y = -20,
    },
    [15] = {
      模型="桃夭夭", 种族="仙", 兵器="飘带、灯笼", 门派="可选择门派：天宫、龙宫、普陀山、凌波城",
      介绍="    桃夭柳媚梦酣眠，笑语嫣然化春风。一朝春近晴光好，清波潋滟映芳菲，桃夭夭是蟠桃园含花吐蕊的花苞，历经三千毓秀钟灵，化身一个机灵爽朗，骄憨顽皮的少女。",
      图片= __res:取资源动画("dlzy",0xAADDEE26,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDCC06,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE80,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE34,"图像"), 
      立绘偏移X = 40, 立绘偏移Y = 0,
    },
    [16] = {
      模型="羽灵神", 种族="仙", 兵器="可用兵器为：弓弩、法杖", 门派="可选择门派：天宫、龙宫、普陀山、凌波城",
      介绍="    游侠红尘里,豪情动九天.羽灵神热情正直,率性豁达,游侠三界间,交友遍天下;乐见四海尽升平,愿引凤鸣遍九州",
      图片= __res:取资源动画("dlzy",0xAADDEE83,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDCC03,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE23,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE49,"图像"), 
      立绘偏移X = -10, 立绘偏移Y = -20,
    },
    [17] = {
      模型="神天兵", 种族="仙", 兵器="可用兵器为：枪矛、锤", 门派="可选择门派：龙宫、天宫、五庄观、凌波城",
      介绍="    金甲腾云受天命,神枪破逆卫灵霄,神天兵风采鹰扬,锋芒毕露,守护天庭立天威,所向披靡,妖魔皆闻风丧胆",
      图片= __res:取资源动画("dlzy",0xAADDEE25,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDCC04,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE79,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE41,"图像"), 
      立绘偏移X = -30, 立绘偏移Y = 0,
    },
    [18] = {
      模型="龙太子", 种族="仙", 兵器="可用兵器为：枪矛、扇", 门派="可选择门派：龙宫、天宫、五庄观、凌波城",
      介绍="    乘风破浪翔碧海,腾云架雾上青天,龙太子凭借天生的优势领悟仙法精髓,是当之无愧的龙族骄子,身经百战的天界战将",
      图片= __res:取资源动画("dlzy",0xAADDEE24,"精灵"), 头像圆图=__res:取资源动画("dlzy",0xAADDCC05,"精灵"), 图片介绍=__res:取资源动画("dlzy",0xAADDEE78,"精灵"), 头像=__res:取资源动画("dlzy",0xAADDEE48,"图像"), 
      立绘偏移X = -70, 立绘偏移Y = 30,
    },
    -- 如果希望“影精灵”成为魔族默认角色，可以把她挪到魔族列表的第一个位置（即放到7号“狐美人”前面）
    [19] = {
      模型="影精灵", 种族="魔", 兵器="可用兵器为：爪刺、魔棒", 门派="可选择门派：九黎城",
      介绍="    一骨生双魂,孤影绘玄青。冷若冰霜 卓尔不群。影精灵从过去的尘埃中走出，向未来飞翔。",
      图片= __res:取资源动画("jszy/yjlsc",0x10000807,"精灵"), 头像圆图=__res:取资源动画("jszy/yjlsc",0x10000356,"精灵"), 图片介绍=__res:取资源动画("jszy/yjlsc",0x10000306,"精灵"), 头像=__res:取资源动画("jszy/yjlsc",0x10000307,"图像"), 
      立绘偏移X = -30, 立绘偏移Y = 50,
    },
  }


function 创建角色:初始化()
    self.种族选中 = "人"
    self.选中人物 = 1
    self.方向 = 5
    self.动作 = "静立"
    self.人族背景 = __res:取资源动画("dlzy",0xAADDEE64,"图像"):拉伸(引擎.宽度,引擎.高度):到精灵()
    self.仙族背景 = __res:取资源动画("dlzy",0xAADDEE62,"图像"):拉伸(引擎.宽度,引擎.高度):到精灵()
    self.魔族背景 = __res:取资源动画("dlzy",0xAADDEE63,"图像"):拉伸(引擎.宽度,引擎.高度):到精灵()
    self.创建种族底框 =  __res:取资源动画("dlzy",0xAADDEE14,"精灵")
    self.创建人物背景 = __res:取资源动画("dlzy",0xAADDEE89,"精灵")
    self.站台 = __res:取资源动画("dlzy",0xAADDEE30,"精灵")
    self.头像底框 = __res:取资源动画("dlzy",0xAADDEE84,"精灵")
    self.创建名字 = __res:取资源动画("dlzy",0xAADDEE86,"精灵")
    self.创建选中光环 =  __res:取资源动画("dlzy",0xAADDEE32,"动画")
    self.人族选中=__res:取资源动画("dlzy",0xAADDEE70,"精灵")
    self.仙族选中 =__res:取资源动画("dlzy",0xAADDEE69,"精灵")
    self.魔族选中=__res:取资源动画("dlzy",0xAADDEE68,"精灵")
    self.说明框状态 = "人物介绍"
    
    self.创建人物精灵 = {}
    for n=1, #角色图片组 do
      local q = 取战斗模型(角色图片组[n].模型)
      local m = 取模型(角色图片组[n].模型)
      self.创建人物精灵[n] = {}
      self.创建人物精灵[n].静立 = __res:取资源动画(m[3], m[1],"置动画"):置循环(true)
      self.创建人物精灵[n].行走 = __res:取资源动画(m[3], m[2],"置动画"):置循环(true)
      self.创建人物精灵[n].攻击 = __res:取资源动画(q[10], q[1],"置动画"):置循环(true)
      self.创建人物精灵[n].施法 = __res:取资源动画(q[10], q[7],"置动画"):置循环(true)
    end
end


  function 创建角色:打开()
    self:置可见(true,true)
    self.说明框状态 = "人物介绍"
    self.种族选中 = "人"
    self:设置默认选中("人") -- 使用新函数初始化
    self.方向 = 5
    self.动作 = "静立"
    self:置方向()
    self:重置按钮()
  end

function 创建角色:更新(dt)
    if self.创建人物精灵[self.选中人物] and self.创建人物精灵[self.选中人物][self.动作] then
        self.创建人物精灵[self.选中人物][self.动作]:更新(dt)
    end
    self.创建选中光环:更新(dt)
end



function 创建角色:显示(x,y)
    if self.种族选中 =="人" then
        self.人族背景:显示(0,0)
        self.创建种族底框:显示(35,20)
        self.人族选中:显示(90,25)
    elseif self.种族选中 =="仙" then
        self.仙族背景:显示(0,0) 
        self.创建种族底框:显示(35,20)
        self.仙族选中:显示(90,25)
    elseif self.种族选中 =="魔" then
        self.魔族背景:显示(0,0)
        self.创建种族底框:显示(35,20)
        self.魔族选中:显示(90,25)
    end
    
    self.创建人物背景:显示(引擎.宽度-315,引擎.高度-440)
    self.站台:显示(引擎.宽度-177,引擎.高度-300)
    self.头像底框:显示(引擎.宽度-580,10)
    if self.创建人物精灵[self.选中人物] and self.创建人物精灵[self.选中人物][self.动作] then
        self.创建人物精灵[self.选中人物][self.动作]:显示(引擎.宽度-123,引擎.高度-290)
    end

    if self.选中人物 ~= 0 then
      self.创建选中光环:显示(self[角色图片组[self.选中人物].模型].x,self[角色图片组[self.选中人物].模型].y)

      local char_data = 角色图片组[self.选中人物]
      if char_data and char_data.图片 then
          local base_x = 150
          local base_y = 引擎.高度 - 460
          local offset_x = char_data.立绘偏移X or 0
          local offset_y = char_data.立绘偏移Y or 0
          char_data.图片:显示(base_x + offset_x, base_y + offset_y)
      end
      
      角色图片组[self.选中人物].图片介绍:显示(20,引擎.高度-380)
    end
    self.创建名字:显示(引擎.宽度/2-260,引擎.高度-60)
end

local 种族选择 ={
  { name="魔族未选中", tcp =__res:取资源动画("dlzy",0xAADDEE65,"图像") },
  { name="仙族未选中", tcp =__res:取资源动画("dlzy",0xAADDEE66,"图像") },
  { name="人族未选中", tcp =__res:取资源动画("dlzy",0xAADDEE67,"图像") },
  { name="左边旋转", tcp =__res:取资源动画("dlzy",0xAADDEE28,"图像") },
  { name="右边旋转", tcp =__res:取资源动画("dlzy",0xAADDEE29,"图像") },
  { name="站立按钮", tcp =__res:取资源动画("dlzy",0xAADDEE90,"图像") },
  { name="奔跑按钮", tcp =__res:取资源动画("dlzy",0xAADDEE33,"图像") },
  { name="攻击按钮", tcp =__res:取资源动画("dlzy",0xAADDEE91,"图像") },
  { name="施法按钮", tcp =__res:取资源动画("dlzy",0xAADDEE92,"图像") },
  { name="人物介绍", tcp =__res:取资源动画("dlzy",0xAADDEE31,"图像") },
  { name="可用兵器", tcp =__res:取资源动画("dlzy",0xAADDEE93,"图像") },
  { name="可选门派", tcp =__res:取资源动画("dlzy",0xAADDEE94,"图像") },
  { name="创建角色", tcp =__res:取资源动画("dlzy",0xDDFFCC02,"图像") },
  { name="上一步", tcp =__res:取资源动画("dlzy",0xAADDEE47,"图像") },
}

-- 【关键修改 1】 新增一个辅助函数，用于动态查找各种族的第一个角色
function 创建角色:设置默认选中(race)
    for i, v in ipairs(角色图片组) do
        if v.种族 == race then
            self.选中人物 = i
            return -- 找到第一个就退出
        end
    end
end

for i, v in ipairs(种族选择) do
  local 临时按钮 = 创建角色:创建按钮(v.name, 0, 0)
  function 临时按钮:初始化()
    self:创建按钮精灵(v.tcp,1)
  end
  function 临时按钮:左键弹起(x, y)
      -- 【关键修改 2】 将硬编码的数字替换为对新函数的调用
      if v.name == "魔族未选中" then
          创建角色.种族选中 = "魔"
          创建角色:设置默认选中("魔")
          创建角色.说明框状态 = "人物介绍"
      elseif v.name == "仙族未选中" then
          创建角色.种族选中 = "仙"
          创建角色:设置默认选中("仙")
          创建角色.说明框状态 = "人物介绍"
      elseif v.name == "人族未选中" then
          创建角色.种族选中 = "人"
          创建角色:设置默认选中("人")
          创建角色.说明框状态 = "人物介绍"
      elseif v.name == "左边旋转" then
          创建角色:前进方向()
      elseif v.name == "右边旋转" then
          创建角色:后退方向()
      elseif v.name == "站立按钮" then
          创建角色.动作 = "静立"
      elseif v.name == "奔跑按钮" then
          创建角色.动作 = "行走"
      elseif v.name == "攻击按钮" then
          创建角色.动作 = "攻击"
      elseif v.name == "施法按钮" then
          创建角色.动作 = "施法"
      elseif v.name == "人物介绍" then
          创建角色.说明框状态 = "人物介绍"
      elseif v.name == "可用兵器" then
          创建角色.说明框状态 = "可用兵器"
      elseif v.name == "可选门派" then
          创建角色.说明框状态 = "可选门派"
      elseif v.name == "创建角色" then
        if 创建角色.选中人物 ~=nil  and 创建角色.名称输入:取文本() ~= "" then
            请求服务(3, 角色图片组[创建角色.选中人物].模型 .. "1222*-*1222" .. 创建角色.名称输入:取文本() .."1222*-*1222".."0")
        end
      elseif v.name == "上一步" then
        创建角色:置可见(false)
        登录层.角色界面:置可见(true)
        登录层.角色界面.创建:置可见(true)
      end
      创建角色:重置按钮()
  end
end

function 创建角色:后退方向()
  if self.方向 ~= 7 then
    self.方向 = self.方向 + 1
  else
    self.方向 = 0
  end
  self:置方向()
end

function 创建角色:前进方向()
  if self.方向 ~= 0 then
    self.方向 = self.方向 - 1
  else
    self.方向 = 7
  end
  self:置方向()
end

function 创建角色:重置按钮()
    self.魔族未选中:置可见(false)
    self.仙族未选中:置可见(false) 
    self.人族未选中:置可见(false)
    local 头像个数 = 0
    for i, v in ipairs(角色图片组) do
       if self[v.模型] then -- 增加安全检查
           self[v.模型]:置可见(false)
           if self.种族选中 == v.种族 then
              self[v.模型]:置可见(true)
              self[v.模型]:置坐标(引擎.宽度-540+头像个数*85,20)
              头像个数 = 头像个数 + 1
          end
       end
    end
    self.左边旋转:置坐标(引擎.宽度-207,引擎.高度-280)
    self.右边旋转:置坐标(引擎.宽度-77,引擎.高度-280)
    self.站立按钮:置坐标(引擎.宽度-240,引擎.高度-420)
    self.奔跑按钮:置坐标(引擎.宽度-180,引擎.高度-420)
    self.攻击按钮:置坐标(引擎.宽度-120,引擎.高度-420)
    self.施法按钮:置坐标(引擎.宽度-60,引擎.高度-420)
    self.人物介绍:置坐标(引擎.宽度-240,引擎.高度-200)
    self.可用兵器:置坐标(引擎.宽度-160,引擎.高度-200)
    self.可选门派:置坐标(引擎.宽度-80,引擎.高度-200)
   
    self.上一步:置坐标(引擎.宽度-270,引擎.高度-60)
    self.创建角色:置坐标(引擎.宽度-130,引擎.高度-60)

    if self.种族选中 =="人" then
      self.魔族未选中:置坐标(25,70)
      self.仙族未选中:置坐标(25,0)
      self.魔族未选中:置可见(true)
      self.仙族未选中:置可见(true)
    elseif self.种族选中 =="仙" then
      self.人族未选中:置坐标(25,70)
      self.魔族未选中:置坐标(25,0)
      self.人族未选中:置可见(true)
      self.魔族未选中:置可见(true)
    elseif self.种族选中 =="魔" then
      self.仙族未选中:置坐标(25,70)
      self.人族未选中:置坐标(25,0)
      self.仙族未选中:置可见(true)
      self.人族未选中:置可见(true)
    end

    self.介绍文本:置文字(标题字体)
    self.介绍文本:清空()
    if self.说明框状态=="人物介绍" then
        self.介绍文本:置文本(角色图片组[self.选中人物].介绍)
    elseif self.说明框状态=="可用兵器" then
        self.介绍文本:置文本(角色图片组[self.选中人物].兵器)
    elseif self.说明框状态=="可选门派" then
        self.介绍文本:置文本(角色图片组[self.选中人物].门派)
    end
end

local 介绍文本 = 创建角色:创建文本("介绍文本", 引擎.宽度-240,引擎.高度-170, 230, 90)

for i, v in ipairs(角色图片组) do
    local 临时按钮1 = 创建角色:创建按钮(v.模型, 0, 0)
    function 临时按钮1:初始化()
      self:创建按钮精灵(v.头像,1)
    end
    function 临时按钮1:左键弹起(x, y)
      创建角色.选中人物=i
      创建角色.说明框状态 = "人物介绍"
      创建角色:重置按钮()
    end
end


local 名字骰子 = 创建角色:创建按钮("名字骰子", 引擎.宽度/2-25, 引擎.高度-60)
function 名字骰子:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0xAADDEE85),1)
end
function 名字骰子:左键弹起(x, y)
end


local 名称输入 = 创建角色:创建文本输入("名称输入", 引擎.宽度/2-150, 引擎.高度-47, 130, 18)
function 名称输入:初始化()
    self:取光标精灵()
    self:置限制字数(16)
    self:置颜色(255, 255, 255, 255)
end


function 创建角色:置方向()
    local xxx = {"静立","行走","攻击","施法"}
    for n=1, #角色图片组 do
        for i = 1, 4 do
            if self.创建人物精灵[n] and self.创建人物精灵[n][xxx[i]] then
                self.创建人物精灵[n][xxx[i]]:置方向(self.方向)
            end
        end
    end
end