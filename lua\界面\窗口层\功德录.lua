
local 功德录 = 窗口层:创建窗口("功德录", 0, 10, 295, 410)
local 基础={
    气血={a=98,b=600},
    伤害={a=14,b=180},
    防御={a=14,b=180},
    速度={a=20,b=60},
    穿刺等级={a=22,b=32},
    治疗能力={a=20,b=60},
    固定伤害={a=14,b=180},
    法术伤害={a=14,b=180},
    法术防御={a=14,b=180},
    气血回复效果={a=20,b=60},
    封印命中等级={a=20,b=60},
    抵抗封印等级={a=22,b=32},
    法术暴击等级={a=20,b=60},
    物理暴击等级={a=20,b=60},
    抗法术暴击等级={a=22,b=32},
    抗物理暴击等级={a=22,b=32},

  }
function 功德录:初始化()
        self:创建纹理精灵(function()
                置窗口背景("功德录", 0, 0, 295, 410, true):显示(0, 0)

                取白色背景(0, 0, 280, 260, true):显示(8, 140)
                 __res:取资源动画("jszy/fwtb",0X10000058,"图像"):显示(15,28)
                文本字体:置颜色(255, 255, 255)
                文本字体:取图像("草堂名刹岁丰深"):显示(145,39)
                文本字体:取图像("三藏谈经事莫寻"):显示(145,61)
                文本字体:取图像("唯有千章云木在"):显示(145,83)
                文本字体:取图像("风来犹作海潮音"):显示(145,105)
                for i = 1, 6 do
       
                    __res:取资源动画("pic","bossjyt.png","图片"):拉伸(122, 22):显示(110,176+(i-1)*30)
                end
        end)

        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        if __手机 then
            self.关闭:置大小(25,25)
            self.关闭:置坐标(self.宽度-27, 2)
        else
            self.关闭:置大小(16,16)
            self.关闭:置坐标(self.宽度-18, 2)
        end
end



function 功德录:显示(x, y)
    if self.图像 then
        self.图像:显示(x +15, y+176)
    end
    if self.需求残页 then
        self.需求残页:显示(x+15+(280-self.需求残页.宽度)//2,255)
    end
end

function 功德录:打开(内容)
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.锁定={}
    for i = 1, 6 do
        self.锁定[i]=0
    end
    self:刷新(内容)
end

function 功德录:刷新(内容)
  self.图像=self:创建纹理精灵(function()
        for i = 1, 6 do
            if self.锁定[i]==1 then
                self["锁定按钮"..i]:置选中(true)
            else
                self["锁定按钮"..i]:置选中(false)
            end
            if 内容.九珠副 and 内容.九珠副[i] then
                self["进度"..i].显示字体= 文本字体:置颜色(255, 255, 255, 255):取描边精灵(内容.九珠副[i].数值.."/"..基础[内容.九珠副[i].类型].b)
                self["进度"..i]:置位置(math.floor(内容.九珠副[i].数值 / 基础[内容.九珠副[i].类型].b * 100))
                文本字体:置颜色(0,0,0,255):取图像(内容.九珠副[i].类型):显示(90-文本字体:取宽度(内容.九珠副[i].类型),3+(i-1)*30)
            else
                self["进度"..i].显示字体= nil
                self["进度"..i]:置位置(0)
                文本字体:置颜色(0,0,0,255):取图像("未开启"):显示(95-文本字体:取宽度("未开启"),2+(i-1)*30)
            end
        end
    end,1,95,170)
    self:刷新残页()
end

function 功德录:刷新残页()


        local 锁定数量 = 0
        for i = 1, 6 do
          if self.锁定[i] == 1 then
              锁定数量=锁定数量+1
          end
        end
        local 需求数量 = 3
        if 锁定数量 == 1 then
              需求数量 = 6
        elseif 锁定数量 == 2 then
                需求数量 = 12
        elseif 锁定数量 == 3 then
                需求数量 = 24
        elseif 锁定数量 == 4 then
                需求数量 = 48
        elseif 锁定数量 == 5 then
                需求数量 = 96
        elseif 锁定数量 == 6 then
                需求数量 = 192
        end
        self.需求残页=文本字体:置颜色(__取颜色("蓝色")):取精灵("当前需求功德残卷:"..需求数量)

end


for i = 1, 6 do
local 临时进度 = 功德录:创建我的进度(__res:取资源动画("pic", "jindu.png","图片"):拉伸(120,20),"进度"..i,111,176+(i-1)*30, 120, 20)
function 临时进度:显示(x, y)
    if self.显示字体 then
        self.显示字体:显示(x + 43, y+2)
    end
end

local 临时按钮=功德录:创建多选按钮("锁定按钮"..i, 245, 174+(i-1)*30)
function 临时按钮:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000009))
end
function 临时按钮:左键弹起(x, y)
   if self.是否选中 then
        功德录.锁定[i]=0
   else
        功德录.锁定[i]=1
   end
   功德录:刷新残页()
end



end
local 洗练=功德录:创建红色按钮("洗练","洗练按钮", 120,365,50,22 )
function 洗练:左键弹起(x, y)
  请求服务(103,{锁定数据=功德录.锁定})

 end

local 关闭 = 功德录:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    功德录:置可见(false)
end



  


















