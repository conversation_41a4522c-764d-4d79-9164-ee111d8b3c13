
local 藏宝阁 = 窗口层:创建窗口("藏宝阁", 0,0, 660, 380)

function 藏宝阁:初始化()
    self:创建纹理精灵(function()
                      置窗口背景("藏宝阁", 0, 0, 660, 380,true):显示(0,0)
                      __res:取资源动画("dlzy",0xADC83326,"图像"):平铺(18,260):显示(5,80)
                      __res:取资源动画("dlzy",0xADC83326,"图像"):平铺(18,260):显示(637,80)
                      取白色背景(0, 0, 610, 260, true):显示(23, 80)
                      for i=1,4 do
                        __res:取资源动画("dlzy",0xB17505CF,"图像"):显示(28,110+(i-1)*55)
                      end
                      __res.UI素材[3]:复制区域(1125, 681, 2, 30):拉伸(2,250):显示(617,80)
                      __res.UI素材[3]:复制区域(1125, 681, 2, 30):拉伸(2,250):显示(631,80)
                    end
              )
    self.物品焦点 =__res:取资源动画("dlzy",0x10921CA7,"精灵")
    self.物品选中 =  __res:取资源动画("dlzy",0x6F88F494,"精灵")
    self.物品={}
    self.物品数据={}
    self.点卡=0
    self.分类 = "装备商城"
    self.开始=1
    self.结束=4
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end

end


function 藏宝阁:显示(x,y)


  if self.图像 then
      self.图像:显示(x, y)
  end

  if self.物品 then
    local yy = 0
    for i=self.开始,self.结束 do
        if self.物品[i] and self.物品[i].小动画 then
            --self.物品[i].小动画:显示(x+30,y+112+yy*55)
            self.物品[i].名称显示:显示(x+90,y+122+yy*55)
            self.物品[i].数量显示:显示(x+220,y+122+yy*55)
            self.物品[i].类型显示:显示(x+330,y+122+yy*55)
            self.物品[i].价格显示:显示(x+440,y+122+yy*55)
            self.物品[i].时间显示:显示(x+540,y+122+yy*55)
            if self.焦点编号 and self.焦点编号 ==i then
              self.物品焦点:显示(x+32,y+110+yy*55)
            end
            if self.选中编号 and self.选中编号 ==i then
              self.物品选中:显示(x+32,y+110+yy*55)
            end
          yy= yy+1
        end
    end
  end

end
function 藏宝阁:获得鼠标(x,y)
    self.焦点编号=nil
end

function 藏宝阁:鼠标滚轮(v)
    if v > 0 then
        if self.开始 <= 1 then
            __UI弹出.提示框:打开("#Y已经到最顶层了!")
        else
              self.开始 = self.开始 -1
              self.结束 = self.开始 + 3
              if self.开始<= 1 then
                  self.开始 = 1
                  self.结束 = self.开始 + 3
              end
              self.显示网格:置数据(self.物品)
        end
    elseif v < 0 then
          if self.结束 >= #self.物品 then
            __UI弹出.提示框:打开("#Y已经到最低层了")
          else
              self.开始 = self.开始 + 1
              self.结束 = self.开始 + 3
              self.显示网格:置数据(self.物品)
          end
    end

end



function 藏宝阁:打开(数据,点卡)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
     return
  end
  self:刷新(数据,"装备商城",点卡)

end
 
function 藏宝阁:物品数据刷新(数据,物品编号)
    self.物品数据[物品编号]= nil
    if self.分类~="宝宝商城" and self.分类~="角色商城"then
        local xxxj =__物品格子:创建()
        xxxj:置物品(数据)
        self.物品数据[物品编号]=xxxj.物品
    end
end
function 藏宝阁:刷新(数据,类型,点卡)
    self.焦点编号=nil
    self.选中编号=nil
    self.点卡 = 点卡
    if 类型 ~=self.分类 then
        self.开始=1
        self.结束=4
    end
    self.分类 = 类型
    self[self.分类]:置选中(true)
    self.物品数据={}
    self.物品={}
    for n=1,#数据 do
          self.物品[n] = {}
  
          local 资源=取物品(数据[n].名称)
          if self.分类~="宝宝商城" and self.分类~="角色商城" then
              资源=取物品(数据[n].名称)
              self.物品[n].小动画= __res:取资源动画(资源[11],资源[12],"精灵")
          else
              资源 = 取头像(数据[n].模型)
              self.物品[n].小动画 =__res:取资源动画(资源[7],资源[2],"精灵")
          end
          文本字体:置颜色(0,0,0,255)
          self.物品[n].名称=数据[n].名称
          self.物品[n].名称显示=文本字体:取精灵(数据[n].名称,0,0,0,150)
          self.物品[n].价格=数据[n].价格
          self.物品[n].价格显示=文本字体:取精灵(数据[n].价格,0,0,0,150)
          self.物品[n].时间=数据[n].时间
          self.物品[n].时间显示=文本字体:取精灵(math.ceil((数据[n].时间-os.time())/86400).."天",0,0,0,150)
          self.物品[n].数量 = 数据[n].数量 or 1
          if self.分类 == "银两商城" then
                local 转换数额 = 数额尾数转换(self.物品[n].数量)
                self.物品[n].数量显示 =文本字体:取精灵(self.物品[n].数量,0,0,0,150) 
          else
                self.物品[n].数量显示 = 文本字体:取精灵(self.物品[n].数量,0,0,0,150)  
          end
          self.物品[n].类型 =数据[n].类型
          self.物品[n].类型显示=文本字体:取精灵(数据[n].类型,0,0,0,150)
          self.物品[n].编号 =数据[n].编号
          self.物品[n].玩家id = 数据[n].玩家id
    end
    self.显示网格:置数据(self.物品)
    self:显示刷新()
    
end






function 藏宝阁:显示刷新()
    self.图像 = self:创建纹理精灵(function()
          排行标题:置颜色(0,0,0,255)
        if self.分类 =="装备商城" then 
          排行标题:取投影图像("装备名称",0,0,0,150):显示(100,80)
          排行标题:取投影图像("装备等级",0,0,0,150):显示(210,80)
          排行标题:取投影图像("装备类型",0,0,0,150):显示(320,80)
          
        elseif self.分类 =="灵饰商城" then
          排行标题:取投影图像("灵饰名称",0,0,0,150):显示(100,80)
          排行标题:取投影图像("灵饰等级",0,0,0,150):显示(210,80)
          排行标题:取投影图像("灵饰类型",0,0,0,150):显示(320,80)
        elseif self.分类 =="宝宝商城" then
          排行标题:取投影图像("宝宝名称",0,0,0,150):显示(100,80)
          排行标题:取投影图像("宝宝等级",0,0,0,150):显示(210,80)
          排行标题:取投影图像("宝宝类型",0,0,0,150):显示(320,80)
        elseif self.分类 =="银两商城" then
          排行标题:取投影图像("银两名称",0,0,0,150):显示(100,80)
          排行标题:取投影图像("银两数量",0,0,0,150):显示(210,80)
          排行标题:取投影图像("银两类型",0,0,0,150):显示(320,80)
        elseif self.分类 =="道具商城" then
          排行标题:取投影图像("物品名称",0,0,0,150):显示(100,80)
          排行标题:取投影图像("物品数量",0,0,0,150):显示(210,80)
          排行标题:取投影图像("物品单价",0,0,0,150):显示(320,80)
        elseif self.分类 =="角色商城" then
          排行标题:取投影图像("角色名称",0,0,0,150):显示(100,80)
          排行标题:取投影图像("角色等级",0,0,0,150):显示(210,80)
          排行标题:取投影图像("角色门派",0,0,0,150):显示(320,80)
        end
        排行标题:取投影图像("购买价格",0,0,0,150):显示(430,80)
        排行标题:取投影图像("剩余时间",0,0,0,150):显示(540,80)
        排行标题:置颜色(255,255,255,255)
        排行标题:取投影图像("剩余点卡: "..self.点卡,255,255,255,150):显示(315,350)
        end,1
      )
end

function 藏宝阁:商品价格排序()
        if self.物品~=nil and self.物品[1]~=nil then
              if  self.价格排序开关 == nil or self.价格排序开关 == 1 then
                table.sort(self.物品, function (a, b)
                  return a.价格 < b.价格
                end)
                self.价格排序开关 = 2
                __UI弹出.提示框:打开("#Y商品价格已从低到高排序!")
              elseif self.价格排序开关 == 2 then
                table.sort(self.物品, function (a, b)
                  return a.价格 > b.价格
                end)
                self.价格排序开关 = 1
                __UI弹出.提示框:打开("#Y商品价格已从高到低排序!")
              end
              self.显示网格:置数据(self.物品)
        end
end



local 显示网格 = 藏宝阁:创建网格("显示网格",28,112,55,220)
function 显示网格:初始化()
    self:创建格子(50, 50, 5, 5, 4, 1)
end

function 显示网格:置数据(数据)
    if 藏宝阁.分类=="宝宝商城" or 藏宝阁.分类=="角色商城"then
        self:置坐标(31,115)
    else
        self:置坐标(28,112)
    end 

      for i = 1, #self.子控件 do
        local 编号 = i+(藏宝阁.开始-1)
        if 数据 and 数据[编号] and 数据[编号].小动画  then
            self.子控件[i]:置精灵(数据[编号].小动画)  
        else
            self.子控件[i]:置精灵()
        end
      end
end

function 显示网格:获得鼠标(x,y,i)
      藏宝阁.焦点编号=nil
      local 编号 = i+(藏宝阁.开始-1)
      if 藏宝阁.物品 and 藏宝阁.物品[编号] and 藏宝阁.物品[编号].小动画 then
            local xx,yy=引擎:取鼠标坐标()
            if 藏宝阁.物品数据 and 藏宝阁.物品数据[编号] then
                  __UI弹出.道具提示:打开(藏宝阁.物品数据[编号],xx+20,yy+20)
            else
                  __UI弹出.自定义:打开(xx+20,yy+20,"#Y鼠标右键查看商品详情")
            end
              藏宝阁.焦点编号=编号
      end
  end
  function 显示网格:右键弹起(x,y,i)
      local 编号 = i+(藏宝阁.开始-1)
      if 藏宝阁.物品 and 藏宝阁.物品[编号] and 藏宝阁.物品[编号].小动画 and not 藏宝阁.物品数据[编号] then
          请求服务(69,{类型=藏宝阁.分类,文本="查看商品",编号=藏宝阁.物品[编号].编号,购买id=藏宝阁.物品[编号].玩家id,显示编号=编号})
      end
end

function 显示网格:左键弹起(x,y,i)
    藏宝阁.选中编号=nil 
    local 编号 = i+(藏宝阁.开始-1)
    if 藏宝阁.物品 and 藏宝阁.物品[编号] and 藏宝阁.物品[编号].小动画 then
          if __手机 then
              if 藏宝阁.物品数据 and 藏宝阁.物品数据[编号] then
                  __UI弹出.道具提示:打开(藏宝阁.物品数据[编号],x+40,y+40)
              else
                  __UI弹出.自定义:打开(x+40,y+40,"#Y再次点击查看商品详情")
                  请求服务(69,{类型=藏宝阁.分类,文本="查看商品",编号=藏宝阁.物品[编号].编号,购买id=藏宝阁.物品[编号].玩家id,显示编号=编号})
              end
          end
          藏宝阁.选中编号 = 编号
          
         
      end
end








local  类型配置={"装备商城","灵饰商城","宝宝商城","银两商城","道具商城","角色商城"} 
for i, v in ipairs(类型配置) do
  local 临时按钮 =藏宝阁:创建蓝色单选按钮(v,v,10+(i-1)*90,40,74,22)    
function 临时按钮:左键弹起(x, y)
  请求服务(69,{类型=v,文本="更新"})
  end
end

local  价格按钮= 藏宝阁:创建蓝色按钮("价格排序", "价格排序", 550, 40,74,22)
function 价格按钮:左键弹起(x, y)
     藏宝阁:商品价格排序()
end




local  上按钮= 藏宝阁:创建按钮( "上按钮", 616, 80)
function 上按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0xFD3D61F2),1)
end
function 上按钮:左键弹起(x, y)
     if 藏宝阁.开始 <= 1 then
            __UI弹出.提示框:打开("#Y已经到最顶层了!")
        else
          藏宝阁.开始 = 藏宝阁.开始 -1
          藏宝阁.结束 = 藏宝阁.开始 + 3
              if 藏宝阁.开始<= 1 then
                 藏宝阁.开始 = 1
                 藏宝阁.结束 = 藏宝阁.开始 + 3
              end
              藏宝阁.显示网格:置数据(藏宝阁.物品)
        end

end



local  下按钮= 藏宝阁:创建按钮( "下按钮", 616, 323)
function 下按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy",0x09217E13),1)
end
function 下按钮:左键弹起(x, y)
      if 藏宝阁.结束 >= #藏宝阁.物品 then
        __UI弹出.提示框:打开("#Y已经到最低层了")
      else
        藏宝阁.开始 = 藏宝阁.开始 + 1
        藏宝阁.结束 = 藏宝阁.开始 + 3
        藏宝阁.显示网格:置数据(藏宝阁.物品)
      end
end

  local  出售按钮= 藏宝阁:创建蓝色按钮("我要出售", "出售按钮", 10, 348,74,22)
  function 出售按钮:左键弹起(x, y)
      窗口层.对话栏:打开(角色信息.模型,角色信息.名称, "请选择上架商品类型",{"藏宝阁上架货币","藏宝阁上架物品","藏宝阁上架角色","藏宝阁上架召唤兽"})
  end

  local  物品按钮= 藏宝阁:创建蓝色按钮("我的物品", "物品按钮", 100, 348,74,22)
  function 物品按钮:左键弹起(x, y)
      窗口层.对话栏:打开(角色信息.模型,角色信息.名称,"请选择打开商品类型",{"打开上架商品","打开购买商品","打开上架召唤兽","打开购买召唤兽"})
  end


  local  购买按钮= 藏宝阁:创建蓝色按钮("购买商品", "购买按钮", 190, 348,74,22)
  function 购买按钮:左键弹起(x, y)
      if 藏宝阁.选中编号 and  藏宝阁.物品 and 藏宝阁.物品[藏宝阁.选中编号] then
           请求服务(69,{类型=藏宝阁.分类,文本="购买商品",编号=藏宝阁.物品[藏宝阁.选中编号].编号,购买id=藏宝阁.物品[藏宝阁.选中编号].玩家id})
      else
           __UI弹出.提示框:打开("#y你还未选择商品")
      end
  end
  local  提现按钮= 藏宝阁:创建蓝色按钮("提现按钮", "我要提现", 550, 348,74,22)
  function 提现按钮:左键弹起(x, y)
    __UI弹出.组合输入框:打开("藏宝阁提现",{"输入你要提现点卡数额","白色",藏宝阁.点卡,藏宝阁.分类})
  end


local 关闭 = 藏宝阁:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    藏宝阁:置可见(false)
    
  end





