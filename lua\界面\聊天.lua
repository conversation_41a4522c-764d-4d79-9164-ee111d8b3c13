

local SDL = require 'SDL'
local _ENV = setmetatable({}, { __index = _G })
引擎:注册事件(
    _ENV,
    {
        窗口事件 = function(_, 消息, ...)
            local t = select(select('#', ...), ...)

            if 外置窗口 then
                if 消息 == SDL.WINDOWEVENT_MOVED then --窗口移动
                    local x, y = 外置窗口:取坐标()
                    if x < t.data1 then
                        外置窗口:置坐标(t.data1 - 外置窗口.宽度 - 5, t.data2)
                    else
                        外置窗口:置坐标(t.data1 + 引擎.宽度 + 5, t.data2)
                    end
                elseif 消息 == SDL.WINDOWEVENT_SIZE_CHANGED then --窗口大小
                end
            end
        end
    }
)



function 打开(_, 主列表)
    -- table.print(_)
    外置窗口 =
    引擎:创建窗口 {
        标题 = '聊天窗口',
        宽度 = 270,
        帧率 = 60,
        鼠标 = false,
        可调整 = true,
        渲染器 = false
    }

    function 外置窗口:初始化()
        self:置最小宽高(270, 引擎.高度)
        self:置最大宽高(引擎.宽度, 引擎.高度)

        外置纹理 = 引擎:创建纹理(引擎.宽度, 引擎.高度)
        外置图像 = self:创建图像(引擎.宽度, 引擎.高度)
        local x, y = 引擎:取坐标()
       
        self:置坐标(x + 引擎.宽度 + 5, y)

        do
            外置界面 = require('GUI')(外置窗口, 文本字体)
            外置界面:置渲染窗口(引擎)
            
            local function 创建屏幕()
                local 屏幕 = 外置界面:创建界面('外置屏幕', 0, 0, 引擎.宽度, 引擎.高度)
                do
                    屏幕.背景 = __res:取资源动画('dlzy',0x1BDDA833,"图像"):平铺(引擎.宽度,引擎.高度):到精灵()
                    function 屏幕:显示()
                        self.背景:显示(0, 0)
                    end

                    function 屏幕:获得鼠标()
                        if 外置鼠标 then
                            外置鼠标:正常形状()
                        end
                    end
                end

                local 聊天列表 = 屏幕:创建列表('聊天列表', 5, 5, 外置窗口.宽度 - 10, 引擎.高度-30)
                function 聊天列表:初始化()
                        self.选中精灵 = nil
                        self.焦点精灵 = nil
                        self.行间距 = 5
                        self:自动滚动(true)
                        self:置宽高(外置窗口.宽度 - 10, 引擎.高度-30)
                end
                    
                function 聊天列表:检查消息()
                    return false
                end
                    
                -- function 聊天列表:鼠标滚轮(v)
                --     self:自动滚动(v)
                -- end
                    
                function 聊天列表:添加内容(文本,频道)
                        if #self.子控件>=500 then
                            self.子控件[1]:删除控件("文本")
                            self:删除(1)
                        end
                        if not 频道 or not __频道表[频道] then
                            频道 = "dq"
                        end
                        文本= 文本 or " "
                        local r = self:添加():置精灵()
                        local 文本内容 = r:丰富文本('文本', 0, 0, self.宽度, 500,true)
                       -- 文本.获得回调 = 聊天列表.文本获得回调
                                -- 文本.回调左键弹起 = 聊天列表.文本回调左键弹起
                                -- 文本.回调右键弹起 = 聊天列表.文本回调右键弹起
                        local 临时内容 = " #" .. __频道表[频道] .. 文本
                        if string.find(文本,"消耗了") or string.find(文本,"请截图给管理员") or  string.find(文本,"请求下载中")  then
                            临时内容 =  文本
                        end
                        local _, h = 文本内容:置文本(临时内容)
                        文本内容:置高度(h)
                        r:置高度(h)
                        r:置可见(true, true)
                 end
                    
                function 聊天列表:文本获得回调(x, y, v)
                                --鼠标层:手指形状()
                end
                    
                function 聊天列表:文本回调右键弹起(v)
                                -- local nid = v:match('0|(.*)')
                                -- if nid then
                                --     聊天列表:文本右键返回(
                                --         nid,
                                --         self:弹出右键 {
                                --             '加为好友',
                                --             '设为私聊',
                                --             '屏蔽发言',
                                --             '查看装备',
                                --             '禁止发言'
                                --         }
                                --     )
                                -- end
                end
                    
                    -- function 聊天列表:文本回调左键弹起(nid)
                            --     print('左键点击')
                            --     if nid:match('0|(.*)') == nil then
                            --         local t, d = __rpc:角色_查看对象(nid)
                            --         if t == 1 then --物品
                            --             local r = require('界面/数据/物品')(d)
                            --             窗口层:打开物品提示(r)
                            --         elseif t == 2 then --召唤
                            --             窗口层:打开召唤兽查看(d)
                            --         end
                            --     else
                            --         local v = nid
                            --         local newnid = v:match('0|(.*)')
                            --         if newnid then
                            --             聊天列表:文本右键返回(
                            --                 newnid,
                            --                 self:弹出右键 {
                            --                     '加为好友',
                            --                     '设为私聊',
                            --                     '屏蔽发言',
                            --                     '查看装备',
                            --                     '禁止发言'
                            --                 }
                            --             )
                            --         end
                            --     end
                    -- end
                    
                    -- function 聊天列表:文本右键返回(nid, v)
                            --     -- print(nid, v)
                            --     if v == '加为好友' then
                            --         __rpc:角色_申请添加好友(nid)
                            --     elseif v == '设为私聊' then
                    
                            --     elseif v == '屏蔽发言' then
                    
                            --     elseif v == '查看装备' then
                    
                            --     elseif v == '禁止发言' then
                            --         __rpc:快捷禁言(nid)
                            --     end
                     -- end
               
                function 聊天列表:置聊天列表宽度(w)
                    for _, v in self:遍历项目() do
                           v:置宽度(w)
                           local _, h = v.文本:置宽度(w)
                           v.文本:置高度(h)
                           v:置高度(h)
                    end
                    self:置宽度(w)
                
                end

                local 左拉按钮 =屏幕:创建按钮("左拉按钮", 50, 引擎.高度-25)
                function 左拉按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x55AB686A),1)
                end

                function 左拉按钮:左键按下(x, y)
                        外置纹理 = nil
                        外置图像 = nil
                        外置界面 = nil
                        for i, v in ipairs(聊天列表.子控件) do
                            v:删除控件("文本")
                        end
                        聊天列表:清空()
                        --test1()
                        collectgarbage()
                        外置窗口:关闭()
                        外置窗口 = nil
                        _ENV:关闭()
                end
                    
                local 上拉按钮 =屏幕:创建按钮("上拉按钮", 72, 引擎.高度-25)
                function 上拉按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x9C6CABF6),1)
                end

                function 上拉按钮:左键按下(x, y)
                        聊天列表:向上滚动()
                end
                
                local 下拉按钮 =屏幕:创建按钮("下拉按钮", 94, 引擎.高度-25)
                function 下拉按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x2BA6891C),1)
                end
                function 下拉按钮:左键按下(x, y)
                            聊天列表:向下滚动()
                end   
                local 移动按钮 =屏幕:创建按钮("移动按钮", 116, 引擎.高度-25)
                function 移动按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x32BE710D),1)
                end       
                local 查询按钮 =屏幕:创建按钮("查询按钮", 138, 引擎.高度-25)
                function 查询按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x2E6E37A3),1)
                end  
                local 禁止按钮 =屏幕:创建按钮("禁止按钮", 160, 引擎.高度-25)
                function 禁止按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x1122D737),1)
                end  
                local 锁定按钮 =屏幕:创建按钮("锁定按钮", 182, 引擎.高度-25)
                function 锁定按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x9B8428CC),1)
                end  
                function 锁定按钮:左键按下(x, y)
                        if self.已锁定 then
                            self.已锁定 =false
                        else
                            self.已锁定=true
                        end
                        聊天列表:自动滚动(self.已锁定)
                end
                  
                local 清屏按钮 =屏幕:创建按钮("清屏按钮", 204, 引擎.高度-25)
                function 清屏按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0xAEB2E8AC),1)
                end 
                function 清屏按钮:左键按下(x, y)
                          for i, v in ipairs(聊天列表.子控件) do
                              v:删除控件("文本")
                          end
                          聊天列表:清空()
                          __UI界面.界面层.聊天控件.消息缓存={}
                end
                



                local 左拉按钮1 =屏幕:创建按钮("左拉按钮1", 50, 引擎.高度2-25)
                function 左拉按钮1:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x55AB686A),1)
                end
                local 上拉按钮1 =屏幕:创建按钮("上拉按钮1", 72, 引擎.高度2-25)
                function 上拉按钮1:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x9C6CABF6),1)
                end

                local 下拉按钮1 =屏幕:创建按钮("下拉按钮1", 94, 引擎.高度2-25)
                function 下拉按钮1:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x2BA6891C),1)
                end
              
                local 移动按钮1 =屏幕:创建按钮("移动按钮1", 116, 引擎.高度2-25)
                function 移动按钮1:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x32BE710D),1)
                end       
                local 查询按钮1 =屏幕:创建按钮("查询按钮1", 138, 引擎.高度2-25)
                function 查询按钮1:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x2E6E37A3),1)
                end  
                local 禁止按钮1 =屏幕:创建按钮("禁止按钮1", 160, 引擎.高度2-25)
                function 禁止按钮1:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x1122D737),1)
                end  
                local 锁定按钮1 =屏幕:创建按钮("锁定按钮1", 182, 引擎.高度2-25)
                function 锁定按钮1:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0x9B8428CC),1)
                end  
                local 清屏按钮1 =屏幕:创建按钮("清屏按钮1", 204, 引擎.高度2-25)
                function 清屏按钮1:初始化()
                    self:创建按钮精灵(__res:取资源动画('dlzy',0xAEB2E8AC),1)
                end 



 

              local 自定义 =屏幕:创建提示控件("自定义", 0, 0)
              function 自定义:打开(xx,yy,数据,...)
                  if  not 数据 then
                      return
                  end
                  self.自定义文本:清空()
                  self.自定义文本:置文字(文本字体)
                  self.精灵文字=nil
                  self.关闭时间 = nil
                  local w, h = 0,0
                  if  type(数据)=="string" then
                      if select('#', ...) > 0 then
                          数据 = 数据:format(...)
                      end
                      w, h =self.自定义文本:置文本(数据)
                      self.精灵文字=nil
                      self.自定义文本:置可见(true)
                  elseif ggetype(数据)=="SDL图像" then
                      self.精灵文字=数据:到精灵()
                      w,h=self.精灵文字.宽度,self.精灵文字.高度
                      self.自定义文本:置可见(false)
                  elseif ggetype(数据)=="SDL精灵" then
                      self.精灵文字=数据
                      w,h=self.精灵文字.宽度,self.精灵文字.高度
                      self.自定义文本:置可见(false)
                  elseif ggetype(数据)=="tcp" then
                      self.精灵文字=数据:取精灵(1)
                      w,h=self.精灵文字.宽度,self.精灵文字.高度
                      self.自定义文本:置可见(false)
                  end
                  self.自定义文本:置高度(h)
                  self:置可见(true, true)
                  self:置精灵(require('SDL.精灵')(0, 0, 0, w+20, h+10):置颜色(0, 0, 0, 150))
                  self:置宽高(w + 20, h + 10)
                  local xxx = 0
                  local yyy = 0
                  if xx + w+25 <外置窗口.宽度 then
                      xxx = xx
                  else
                      xxx = 外置窗口.宽度-(w+25)
                  end
                  if yy + h +15 <引擎.高度 then
                      yyy = yy
                  else
                      yyy = 引擎.高度-(h+15)
                  end
                  if xxx<5 then
                     xxx = 5
                  end
                  if yyy <5 then
                     yyy = 5
                  end
                  self:置坐标(xxx,yyy)
              end
              
              function 自定义:显示(xx,yy)
                  if self.精灵文字 then
                      self.精灵文字:显示(xx+10,yy+5)
                  end
              end
              local 自定义文本 = 自定义:创建文本("自定义文本", 10, 5, 390, 40)
              
              local 战斗控件=屏幕:创建控件("战斗控件",0,引擎.高度2, 外置窗口.宽度, 引擎.高度2-30)
              function 战斗控件:初始化()
                    self.横条=__res:取资源动画("dlzy",0XE03A06B3,"精灵")
                    --self.回合背景=require('SDL.精灵')(0, 0, 0, 30, 15):置颜色(255,255,255,255)
                    self.回合背景=__res:取资源动画("pic","zdbtl.png","图片"):到精灵()
                    self.名称背景=__res:取资源动画("pic","zdmcl.png","图片"):到精灵()
              end
             


              function 战斗控件:显示(xx,yy)
                    self.横条:显示(xx+(self.宽度-self.横条.宽度)//2,yy)
                    self.回合背景:显示(xx+5,yy+20)
                   
                    if self.回合数 then
                        self.回合数:显示(xx+5+(self.名称背景.宽度-self.回合数.宽度)//2,yy+24)
                    end
                    if self.显示组 and self.显示组[1] then
                          for i=1,5 do
                             if self.显示组[i] and self.显示组[i].名称 then
                                  self.名称背景:显示(xx+5,yy+45+(i-1)*40)
                                  self.显示组[i].名称:显示(xx+15,yy+58+(i-1)*40)
                                  if self.显示组[i].状态 and self.显示组[i].状态[1] then
                                      for n,v in ipairs(self.显示组[i].状态) do
                                            
                                           if v.图标 then
                                               if self.焦点==n and self.焦点1==i then
                                                  v.选中:显示(xx+102+(n-1)*32,yy+55+(i-1)*40)
                                               else
                                                  v.图标:显示(xx+102+(n-1)*32,yy+55+(i-1)*40)
                                               end
                                           end
                                           if v.回合 then
                                              v.回合:显示(xx+102+(30-v.回合.宽度)//2+(n-1)*32,yy+67+(i-1)*40)
                                          end
                                      end
                                  end
                             end
                          end

                    end
                  
              end

              function 战斗控件:获得鼠标(xx,yy)
                      self.焦点=nil
                      self.焦点1=nil
                      if self.显示组 and self.显示组[1] then
                              for i=1,5 do
                                if self.显示组[i] and self.显示组[i].状态  then
                                    for n,v in ipairs(self.显示组[i].状态) do
                                        if v.图标 and v.说明 and v.图标:检查透明(xx,yy) then
                                              self.焦点=n
                                              self.焦点1=i
                                              自定义:打开(xx+20,yy+20,v.说明)
                                        end
                                    end
                                end
                              end
                        end

              end
              function 战斗控件:失去鼠标(xx,yy)
                    self.焦点=nil
                    self.焦点1=nil
           
              end

              function 战斗控件:添加内容(数据)
                    self.显示组={}
                    if 数据 and 数据.回合 then
                        self.回合数=文本字体:置颜色(255,255,255,255):取精灵("第"..数据.回合.."回")
                        for i=1,5 do
                            if 数据[i] then
                                self.显示组[i]={}
                                self.显示组[i].名称 = 文本字体:置颜色(0,0,0,255):取精灵(数据[i].名称)
                                if 数据[i].状态数据 and 数据[i].状态数据[1] and 数据[i].状态数据[1].图标 then
                                    self.显示组[i].状态=数据[i].状态数据
                                end
                            end
                        end
                    end
              end
             
                屏幕:置可见(true, true)
                return 屏幕
            end

            外置屏幕 = 创建屏幕()
            if not __UI界面.界面层.聊天控件.消息缓存 then __UI界面.界面层.聊天控件.消息缓存={} end
            for i, v in ipairs(__UI界面.界面层.聊天控件.消息缓存) do
                外置屏幕.聊天列表:添加内容(v.文本,v.频道)
            end
            if _tp.战斗中 then
                进入战斗()
            else
                退出战斗()
            end

           
        end
    end
    
    function 外置窗口:更新事件(dt, x, y)
        if 外置界面 then
            外置界面:更新(dt, x, y)
        end

        --显示到渲染区，然后截图到图像
        if 外置纹理 and 外置纹理:渲染开始() then
            if 外置界面 then
                外置界面:显示(x, y)
            end
            if 外置鼠标 then
                外置鼠标:显示(x, y)
            end
            if 外置提示 then
                外置提示:显示(x, y)
            end
            引擎:截图到图像(外置图像, 0, 0, self.宽度, self.高度)
            外置纹理:渲染结束()
        end
    end

    function 外置窗口:渲染事件(dt, x, y)
        --self:渲染开始(0x70,0x70,0x70)
        if  self:渲染开始()  then
            if 外置图像 then
                 外置图像:显示(0, 0)
            end
            self:渲染结束()
        end
    end

    function 外置窗口:窗口事件(消息, w,h)
        if 消息 == SDL.WINDOWEVENT_MOVED then
            if 外置窗口 then
              local x, y = 引擎:取坐标()
              if w < x then
                  外置窗口:置坐标(x - 外置窗口.宽度 - 5, y)
              else
                  外置窗口:置坐标(x + 引擎.宽度 + 5, y)
              end
            end
        elseif 消息 == SDL.WINDOWEVENT_ENTER then
            外置鼠标 =  __UI界面.鼠标层
        elseif 消息 == SDL.WINDOWEVENT_LEAVE then
            外置鼠标 = nil
        elseif 消息 == SDL.WINDOWEVENT_SIZE_CHANGED then
                if 外置屏幕 then
                    外置屏幕.聊天列表:置聊天列表宽度(外置窗口.宽度 - 10)
                    外置屏幕.战斗控件:置宽度(外置窗口.宽度)
                end
        end
    end

    return _ENV
end
function 进入战斗()
    if 外置屏幕 then
        外置屏幕.聊天列表:置高度(引擎.高度2-30)
        外置屏幕.左拉按钮1:置可见(true)
        外置屏幕.上拉按钮1:置可见(true)
        外置屏幕.下拉按钮1:置可见(true)
        外置屏幕.移动按钮1:置可见(true)
        外置屏幕.查询按钮1:置可见(true)
        外置屏幕.禁止按钮1:置可见(true)
        外置屏幕.锁定按钮1:置可见(true)
        外置屏幕.清屏按钮1:置可见(true)
        外置屏幕.战斗控件:置可见(true)
        __战斗主控:聊天外框战斗()
    end
end

function 退出战斗()
      if 外置屏幕 then
          外置屏幕.聊天列表:置高度(引擎.高度-30)
          外置屏幕.左拉按钮1:置可见(false)
          外置屏幕.上拉按钮1:置可见(false)
          外置屏幕.下拉按钮1:置可见(false)
          外置屏幕.移动按钮1:置可见(false)
          外置屏幕.查询按钮1:置可见(false)
          外置屏幕.禁止按钮1:置可见(false)
          外置屏幕.锁定按钮1:置可见(false)
          外置屏幕.清屏按钮1:置可见(false)
          外置屏幕.战斗控件:置可见(false)
      end
end

function 置状态数据(_, 数据)
          if 外置屏幕 then
              外置屏幕.战斗控件:添加内容(数据)
          end

    
end

function 添加内容(_, 文本,频道)
   if 外置屏幕 then
        外置屏幕.聊天列表:添加内容(文本,频道)
    end
end
function 重新初始化()
  if 外置屏幕 then
      外置窗口:初始化()
  end
end

return _ENV
