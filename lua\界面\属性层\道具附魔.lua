local 道具附魔 = 窗口层:创建窗口("道具附魔", 0, 0, 290, 280)  
function 道具附魔:初始化()
  self:置精灵(置窗口背景("附魔", 0, 0, 290, 280))
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end

end


function 道具附魔:打开(数据)
      self:置可见(not self.是否可见)
      if not self.是否可见 then
          return
      end
      self.编号= 0
      self.数据名称 = nil
      self.数据等级 = nil
      self.数据=数据
      self.道具列表={}
      if 数据.编号 then
          self.编号=tonumber(数据.编号)
      end
      self.道具网格:置数据()
      self:道具刷新(数据.道具.道具)
     
end

function 道具附魔:道具刷新(道具)
        local 道具数据 = table.copy(_tp.道具列表)
        if 道具 then
            道具数据 =table.copy(道具)
        end
       if self.编号~=0 and self.道具列表[self.编号] then
            if not 道具数据[self.编号] or self.道具列表[self.编号].识别码~=道具数据[self.编号].识别码 then
                  local 找到=0
                  for k, v in pairs(道具数据) do
                      if self.道具列表[self.编号].识别码==v.识别码 then
                          找到=k
                      end
                  end
                  self.编号=找到
            end
       end
      self.道具列表 =道具数据
      self.道具网格:置物品(self.道具列表)
      self.道具网格:置禁止({2})
end

local 道具网格=道具附魔:创建背包网格("道具网格",15,35)
function 道具网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
              __UI弹出.道具提示:打开(物品.物品,x+25,y+25)
          end
end



function 道具网格:左键弹起(x, y, a)
      local 物品=self:选中物品() 
      if 物品 and 物品.物品 then
            if __手机 then

                __UI弹出.道具提示:打开(物品.物品,x+25,y+25,道具网格,"选择",1)
            else
                  self:选择(1)
            end
      end

end

function 道具网格:选择(编号)
  if 编号 and 编号~=0 then
        道具附魔:物品附魔(self:选中物品(),self:选中())
  end
end


function 道具附魔:物品附魔(物品,编号)
      if not 物品.物品禁止  then
          local 物品数据= self.道具列表[self.编号]
          if 物品数据 then
              if (物品数据.类型=="嗜血" or 物品数据.类型=="莲华妙法") and 物品.物品.分类==2 then
                  请求服务(4512,{序列=物品.物品,序列1=self.编号,序列2=编号})
              elseif (物品数据.类型=="神兵护法" or 物品数据.类型=="尸气漫天") and 物品.物品.分类==1 then
                  请求服务(4512,{序列=物品.物品,序列1=self.编号,序列2=编号})
              elseif (物品数据.类型=="拈花妙指" or 物品数据.类型=="神木呓语") and 物品.物品.分类==6 then
                  请求服务(4512,{序列=物品.物品,序列1=self.编号,序列2=编号})
              elseif (物品数据.类型=="轻如鸿毛" or 物品数据.类型=="龙附" or 物品数据.类型=="魔王护持" or 物品数据.类型=="盘丝舞" or 物品数据.类型=="元阳护体" ) and 物品.物品.分类==3 then
                  请求服务(4512,{序列=物品.物品,序列1=self.编号,序列2=编号})
              elseif (物品数据.类型=="浩然正气" or 物品数据.类型=="一气化三清" ) and 物品.物品.分类==4 then
                  请求服务(4512,{序列=物品.物品,序列1=self.编号,序列2=编号})
              elseif (物品数据.类型=="神力无穷" or 物品数据.类型=="穿云破空" ) and 物品.物品.分类==5 then
                  请求服务(4512,{序列=物品.物品,序列1=self.编号,序列2=编号})
              else
                  __UI弹出.提示框:打开("#Y该道具无法附魔")
                  return
              end
          elseif self.数据 and self.数据.名称 and (self.数据.名称 == "嗜血" or self.数据.名称 == "莲华妙法") and 物品.物品.分类==2 then
                  请求服务(4513,{序列=物品.物品,数据=self.数据,序列2=编号})
          elseif self.数据 and self.数据.名称 and (self.数据.名称 == "神兵护法" or self.数据.名称 == "尸气漫天") and 物品.物品.分类==1 then
                  请求服务(4513,{序列=物品.物品,数据=self.数据,序列2=编号})
          elseif self.数据 and self.数据.名称 and (self.数据.名称 == "拈花妙指" or self.数据.名称 == "神木呓语") and 物品.物品.分类==6 then
                  请求服务(4513,{序列=物品.物品,数据=self.数据,序列2=编号})
          elseif self.数据 and self.数据.名称 and (self.数据.名称 == "轻如鸿毛" or self.数据.名称 == "龙附" or self.数据.名称 == "魔王护持"  or self.数据.名称 == "盘丝舞" or self.数据.名称 == "元阳护体" ) and 物品.物品.分类==3 then
                  请求服务(4513,{序列=物品.物品,数据=self.数据,序列2=编号}) 
          elseif self.数据 and self.数据.名称 and (self.数据.名称 == "浩然正气" or self.数据.名称 == "一气化三清") and 物品.物品.分类==4 then
                  请求服务(4513,{序列=物品.物品,数据=self.数据,序列2=编号})
          elseif self.数据 and self.数据.名称 and (self.数据.名称 == "神力无穷" or self.数据.名称 == "穿云破空") and 物品.物品.分类==5 then
                  请求服务(4513,{序列=物品.物品,数据=self.数据,序列2=编号})
          else
              __UI弹出.提示框:打开("#Y该道具无法附魔")
              return
          end
          self:置可见(false)
    else
      __UI弹出.提示框:打开("#Y该道具无法附魔")
    end

end


local 关闭 = 道具附魔:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  道具附魔:置可见(false)
end


