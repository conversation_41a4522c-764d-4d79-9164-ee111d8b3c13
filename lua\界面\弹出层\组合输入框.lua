
__UI弹出.组合输入框 = 界面:创建弹出窗口("组合输入框", 0, 0, 420, 145)
local 组合输入框 = __UI弹出.组合输入框


function 组合输入框:初始化()

end
function 组合输入框:打开(类型,附加)
  self:置可见(true)
  self:置坐标((引擎.宽度-self.宽度)//2, (引擎.高度-self.高度)//2)
  self.文本栏:清空()
  self.文本栏:置文字(标题字体)
  if 附加 and 附加[1] then
      local 颜色 = 取颜色字符(附加[2])
      self.文本栏:置文本(颜色..附加[1])
  end

  self.输入框:清空()
  self.输入框:置限制字数(15)
  if 类型 == "创建帮派" then
    self.输入框:置限制字数(14)
  elseif 类型 == "帮派宗旨" then
    self.输入框:置限制字数(150)
  elseif 类型=="藏宝阁提现" then
      self.输入框:置限制字数(9)
      self.输入框:置模式(self.输入框.数字模式)
  elseif 类型=="存钱" then
      self.输入框:置限制字数(15)
      self.输入框:置模式(self.输入框.数字模式)
  elseif 类型=="取钱" then
    self.输入框:置限制字数(15)
    self.输入框:置模式(self.输入框.数字模式)
  elseif 类型 == "帮费设置"  then
    self.输入框:置限制字数(6)
    self.输入框:置模式(self.输入框.数字模式)
  elseif 类型 == "拆分物品" then
      self.输入框:置限制字数(3)
      self.输入框:置模式(self.输入框.数字模式)
  elseif 类型=="修改分组名称" then
    self.输入框:置限制字数(20)
  end
  self.回调事件 = 附加
	self.类型事件 = 类型
  self:创建纹理精灵(function()
        置窗口背景(类型, 0, 0, 420, 145, true):显示( 0, 0)
        取输入背景(0, 0, 380, 23):显示(20, 83)
      end
    )
    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end

  
end

local 文本栏 = 组合输入框:创建文本("文本栏", 20, 33, 385, 45)




local  输入框 = 组合输入框:创建文本输入("输入框", 25, 86, 370, 18)
function 输入框:初始化()
  self:取光标精灵(0, 0, 0, 255)
  self:置颜色(0, 0, 0, 255)
end
local 取消=组合输入框:创建红色按钮("取消", "取消", 290, 115,60,22) 

function 取消:左键弹起(x, y, msg)
  组合输入框:置可见(false)
end

local 确定=组合输入框:创建红色按钮("确定", "确定", 60, 115,60,22) 
function 确定:左键弹起(x, y, msg)
      if 组合输入框.类型事件 == "创建帮派" then
          if 组合输入框.输入框:取文本() == "" then
              __UI弹出.提示框:打开("#Y请输入您要创建的帮派名称！")
          elseif string.len(组合输入框.输入框:取文本()) <= 4  then
              __UI弹出.提示框:打开("#Y最低帮派名称不能少于3个字符！")
          else
              请求服务(6101,{文本=组合输入框.输入框:取文本()})
          end
      elseif 组合输入框.类型事件 == "帮派宗旨" then
          if 组合输入框.输入框:取文本() == "" then
             __UI弹出.提示框:打开("#Y请输入您要修改的帮派宗旨！")
          elseif string.len(组合输入框.输入框:取文本()) <= 2  then
            __UI弹出.提示框:打开("#Y最低帮派名称不能少于1个字符！")
         else
            请求服务(6105,{文本=组合输入框.输入框:取文本()})
         end
      elseif 组合输入框.类型事件 == "改名" then
          if 组合输入框.输入框:取文本() == "" then
              __UI弹出.提示框:打开("#Y请输入您要修改的名字！")
          elseif string.len(组合输入框.输入框:取文本()) <= 2  then
              __UI弹出.提示框:打开("#Y最低名称不能少于1个字符！")
          else
              请求服务(3776,{文本=组合输入框.输入框:取文本()})
          end
      elseif 组合输入框.类型事件 == "输入安全码" then
          if 组合输入框.输入框:取文本() == "" then
            __UI弹出.提示框:打开("#Y请输入您的安全码！")
          else
            请求服务(3777,{文本=组合输入框.输入框:取文本()})
          end
      elseif 组合输入框.类型事件 == "帮费设置" then
          if 组合输入框.输入框:取文本() == "" then
               __UI弹出.提示框:打开("#Y请输入您的需要设置的帮费金额！")
          elseif string.len(组合输入框.输入框:取文本()) > 6  then
                __UI弹出.提示框:打开("#Y帮费金额不能大于99W哦")
          else
                请求服务(6115,{文本=组合输入框.输入框:取文本()})
          end
      elseif 组合输入框.类型事件 == "帮派竞赛报名" then
          if 组合输入框.输入框:取文本() == "" then
               __UI弹出.提示框:打开("#Y请输入您要报名的费用！")
          elseif string.len(组合输入框.输入框:取文本()) <= 5  then
                __UI弹出.提示框:打开("#Y最低报名费用不能低于50W或帮派名称错误")
          elseif 组合输入框.输入框:取文本()<"500000" then
                __UI弹出.提示框:打开("#Y最低报名费用不能低于50W或帮派名称错误")
          else
              请求服务(42,{文本=组合输入框.输入框:取文本()})
          end
      elseif 组合输入框.类型事件 == "藏宝阁提现" then
          if 组合输入框.输入框:取文本() == "" then
              __UI弹出.提示框:打开("#Y输入的数额错误")
          elseif tonumber(组合输入框.输入框:取文本()) < 1 then
              __UI弹出.提示框:打开("#Y输入的数额错误")
          elseif tonumber(组合输入框.输入框:取文本()) > tonumber(组合输入框.回调事件[3]) then
              __UI弹出.提示框:打开("#Y输入的数额错误")
          else
              请求服务(69,{类型=组合输入框.回调事件[4],文本="藏宝阁提现",数量=tonumber(组合输入框.输入框:取文本())})
          end
      elseif 组合输入框.类型事件 == "存钱" then
            if 组合输入框.输入框:取文本() == "" then
                __UI弹出.提示框:打开("#Y输入的数额错误")
            elseif tonumber(组合输入框.输入框:取文本()) < 1 then
                __UI弹出.提示框:打开("#Y输入的数额错误")
            elseif tonumber(组合输入框.输入框:取文本()) > tonumber(组合输入框.回调事件[3]) then
                __UI弹出.提示框:打开("#Y输入的数额错误")
            else
                请求服务(60,{文本="存钱",数额=tonumber(组合输入框.输入框:取文本())})
            end
      elseif 组合输入框.类型事件 == "取钱" then
            if 组合输入框.输入框:取文本() == "" then
                __UI弹出.提示框:打开("#Y输入的数额错误")
            elseif tonumber(组合输入框.输入框:取文本()) < 1 then
                __UI弹出.提示框:打开("#Y输入的数额错误")
            elseif tonumber(组合输入框.输入框:取文本()) > tonumber(组合输入框.回调事件[3]) then
                __UI弹出.提示框:打开("#Y输入的数额错误")
            else
                请求服务(60,{文本="取钱",数额=tonumber(组合输入框.输入框:取文本())})
            end
      elseif 组合输入框.类型事件 == "拆分物品" then
            if 组合输入框.输入框:取文本() == "" then
              __UI弹出.提示框:打开("#Y请输入需要拆分的数量！")
            elseif tonumber(组合输入框.输入框:取文本()) < 1 then
                __UI弹出.提示框:打开("#Y输入的数额错误")
            elseif tonumber(组合输入框.输入框:取文本()) >= tonumber(组合输入框.回调事件[3]) then
                __UI弹出.提示框:打开("#Y输入的数额错误")
            else
                请求服务(3741,{数量=tonumber(组合输入框.输入框:取文本())})
            end
      elseif 组合输入框.类型事件 == "修改分组名称" then
          if 组合输入框.输入框:取文本() == "" then
              __UI弹出.提示框:打开("#Y名称不能为空")
          else
              请求服务(17,{编号=组合输入框.回调事件[3],名称=组合输入框.输入框:取文本()})
          end


    end

    组合输入框:置可见(false)


end

local 关闭 = 组合输入框:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    组合输入框:置可见(false)
  end