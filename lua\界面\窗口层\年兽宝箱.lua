local 年兽宝箱 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "年兽宝箱", 178 + abbr.py.x, 75 + abbr.py.y, 512, 419)
function 年兽宝箱:初始化()
  local nsf = require("SDL.图像")(512, 419)
  if nsf["渲染开始"](nsf) then
    置窗口背景("无", 0, 14, 505, 406, true)["置颜色"](置窗口背景("无", 0, 14, 505, 406, true), 239, 56, 46, 255)["显示"](置窗口背景("无", 0, 14, 505, 406, true)["置颜色"](置窗口背景("无", 0, 14, 505, 406, true), 239, 56, 46, 255), 0, 0)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 年兽宝箱:打开(data)
  self:置可见(true)
  -- table.print(data)
  self.道具网格["置物品"](self.道具网格, data["道具"])
  self.宝箱进程 = 0
  self.宝箱位置 = 0
  self.宝箱间隔 = 0
  self.宝箱起始 = os.time()
  self.宝箱速度 = 5
  self.宝箱中奖 = data["道具"]["中奖"]
  self.宝箱类型 = data["类型"]
  self:重置(data)
end
function 年兽宝箱:重置(data)
  local nsf = require("SDL.图像")(512, 40)
  if nsf["渲染开始"](nsf) then
    local 宽度 = 字体20["取宽度"](字体20, data["类型"])
    字体20["置颜色"](字体20, 255, 255, 255)
    字体20["取图像"](字体20, data["类型"])["显示"](字体20["取图像"](字体20, data["类型"]), 252.5 - 宽度 / 2, 17)
  end
  self.图像 = nsf["到精灵"](nsf)
end
function 年兽宝箱:更新()
  if 0 == self.宝箱进程 then
    self.宝箱间隔 = self.宝箱间隔 + 1
    if self.宝箱间隔 >= self.宝箱速度 then
      self.宝箱间隔 = 0
      if self.道具网格["子控件"][self.宝箱位置] then
        self.道具网格["子控件"][self.宝箱位置]._spr["确定"] = nil
      end
      self.宝箱位置 = self.宝箱位置 + 1
      if 25 == self.宝箱位置 then
        self.宝箱位置 = 1
      end
      self.道具网格["子控件"][self.宝箱位置]._spr["确定"] = true
    end
  elseif 1 == self.宝箱进程 then
    self.宝箱间隔 = self.宝箱间隔 + 1
    if self.宝箱间隔 >= self.宝箱速度 then
      self.宝箱间隔 = 0
      if self.道具网格["子控件"][self.宝箱位置] then
        self.道具网格["子控件"][self.宝箱位置]._spr["确定"] = nil
      end
      self.宝箱位置 = self.宝箱位置 + 1
      if 25 == self.宝箱位置 then
        self.宝箱位置 = 1
      end
      self.道具网格["子控件"][self.宝箱位置]._spr["确定"] = true
    end
    if os.time() >= self.宝箱起始 + 3 then
      self.宝箱进程 = 2
    end
  elseif 2 == self.宝箱进程 then
    local 距离 = math.abs(self.宝箱位置 - self.宝箱中奖)
    if 距离 >= 15 then
      self.宝箱速度 = 10
    elseif 距离 >= 10 then
      self.宝箱速度 = 17
    elseif 距离 >= 5 then
      self.宝箱速度 = 25
    end
    self.宝箱间隔 = self.宝箱间隔 + 1
    if self.宝箱间隔 >= self.宝箱速度 then
      self.宝箱间隔 = 0
      if self.道具网格["子控件"][self.宝箱位置] then
        self.道具网格["子控件"][self.宝箱位置]._spr["确定"] = nil
      end
      self.宝箱位置 = self.宝箱位置 + 1
      if 25 == self.宝箱位置 then
        self.宝箱位置 = 1
      end
      self.道具网格["子控件"][self.宝箱位置]._spr["确定"] = true
    end
    if self.宝箱位置 == self.宝箱中奖 then
      self.宝箱进程 = 3
      self.宝箱起始 = os.time()
			发送数据(96.1,{序号=self.中奖})

    end
  elseif 3 == self.宝箱进程 and os.time() >= self.宝箱起始 + 1 then
    发送数据(1521)
    self.关闭["左键弹起"](self.关闭)
    return
  end
end
local 关闭 = 年兽宝箱["创建我的按钮"](年兽宝箱, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 462, 0)
function 关闭:左键弹起(x, y, msg)
  年兽宝箱["置可见"](年兽宝箱, false)
end
local 道具网格 = 年兽宝箱["创建网格"](年兽宝箱, "道具网格", 30, 60, 455, 280)
function 道具网格:初始化()
  self:创建格子(55, 55, 20, 23, 4, 6)
end
function 道具网格:置物品(data)
  for i = 1, #self.子控件 do
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, data[i], "NPC商店")
    self.子控件[i]["置精灵"](self.子控件[i], lssj)
  end
end
for i, v in ipairs({
  {
    name = "停止",
    x = 210,
    y = 359,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "停止"
  }
}) do
  local 临时函数 = 年兽宝箱["创建我的按钮"](年兽宝箱, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "停止" and 0 == 年兽宝箱["宝箱进程"] then
      年兽宝箱["宝箱进程"] = 1
      年兽宝箱["宝箱起始"] = os.time()
    end
  end
end
