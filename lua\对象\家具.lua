--[[
LastEditTime: 2025-05-20 06:22:42
--]]
--[[
LastEditTime: 2024-11-05 20:59:06
--]]
local SDL = require 'SDL'
local 家具 = class("家具")
function 家具:初始化(t)
    self.xy = require("GGE.坐标")(t.x * 20, t.y * 20):floor()
    self.精灵 = __res:取资源动画(t.资源,t.切换,"动画")
    self.id = t.编号
  
end

function 家具:更新(dt)
    if self.精灵 then
        self.精灵:更新(dt)
    end
end

function 家具:显示(pys)
    if self.精灵 then
        self.精灵:显示(self.xy + pys)
    end
end


function 家具:检查点(x, y)
      if self.精灵 and self.精灵:检查透明(x, y) then
          return true
      end
end

function 家具:检查透明(x, y)
  if self.精灵 and self.精灵:检查透明(x, y) then
        return true
  end
  return false
end



function 家具:消息事件(t)
  if  t.鼠标  and _tp.如意符 then
      for _, v in ipairs(t.鼠标) do
          if self.精灵 and self.精灵:检查透明(v.x, v.y)  then
              if v.button == SDL.BUTTON_LEFT then
                  if v.type == SDL.MOUSE_DOWN and __主显 and __主显.主角  then
                        __主显.主角.按下=false
                        __主显.主角.点击移动=nil
                  elseif v.type == SDL.MOUSE_UP then
                        请求服务(1006,{编号=self.id})
                        _tp.如意符=nil
                        鼠标层:正常形状()
                  end
              elseif not __手机 and  not v.button and  v.type == SDL.MOUSE_MOTION  then
                self.精灵:置高亮(true)
              end
          elseif not __手机 and  self.精灵 then
              self.精灵:置高亮(false)
          end
      end
  end
end


return 家具
