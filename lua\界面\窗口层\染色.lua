
local 染色 = 窗口层:创建窗口("染色",0,0,270,320)
function 染色:初始化()
        self:创建纹理精灵(function ()
            置窗口背景("服饰染色", 0, 0, 270, 320, true):显示(0, 0)
            取白色背景(0, 0, 250, 250, true):显示(10, 35)
            __res:取资源动画("jszy/xjjm",0X01AC0008,"图像"):显示(80,295)
            标题字体:置颜色(255,255,255,255):取图像("所需彩果"):显示(10,298)

        end)

        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        if __手机 then
            self.关闭:置大小(25,25)
            self.关闭:置坐标(self.宽度-27, 2)
        else
            self.关闭:置大小(16,16)
            self.关闭:置坐标(self.宽度-18, 2)
        end
end



function 染色:更新(dt)
  if self.人物模型 then
          self.人物模型:更新(dt)
  end
  if self.武器显示 then
          self.武器显示:更新(dt)
  end
  

end
function 染色:显示(x,y)


  if self.人物模型 then
      self.人物模型:显示(x+145,y+210)
  end
  if self.武器显示 then
      self.武器显示:显示(x+145,y+210)
  end
  if self.染色显示 then
      self.染色显示:显示(x+85,y+298)
  end

end




function 染色:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.彩果 = 10
    self.染色组 = {0,0,0}
    self.染色方案=角色信息.染色方案 or 0
    self.方向 = 5
    self.染色显示=文本字体:置颜色(0,0,0):取精灵(self.彩果)
    self:置模型()


end


function 染色:置模型()

  local 资源 = 取模型(角色信息.模型)
  local  m
  if 角色信息.装备 and 角色信息.装备[3] ~= nil then
          m= _tp:取武器子类(角色信息.装备[3].子类)
          if 角色信息.装备[3].名称 == "龙鸣寒水" or 角色信息.装备[3].名称 == "非攻" then
                  m = "弓弩1"
          end
          资源 = 取模型(角色信息.模型, m)
  end
  self.人物模型 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
  local 调色板  = __dewpal(self.染色方案)
  self.人物模型:调色(调色板,取调色数据(self.染色组))
  if 角色信息.装备 and 角色信息.装备[3] ~= nil then
          local ms = _tp:取武器附加名称(角色信息.装备[3].子类, 角色信息.装备[3].级别限制,角色信息.装备[3].名称)
          资源 = 取模型(ms .. "_" .. 角色信息.模型)
          self.武器显示 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
  end
  self:置方向(self.方向)
end


function 染色:置方向(v)
  if self.人物模型 then
      self.人物模型:置方向(v)
  end
  if self.武器显示 then
      self.武器显示:置方向(v)
  end


  
end



local 按钮设置={"壹","贰","叁"}
for i, v in ipairs(按钮设置) do
  local 临时函数 = 染色:创建红色按钮(i, v,17+(i-1)*30,258,25,25)

  function 临时函数:左键弹起(x, y)
      染色.染色组[i] = (染色.染色组[i] or 0) + 1
      if 染色.染色组[i] > 11 then
          染色.染色组[i] = 0
      end
      染色.彩果=10
      for n=1,3 do
          染色.彩果 = 染色.彩果 + 染色.染色组[n]
      end
      染色.染色显示=文本字体:置颜色(0,0,0):取精灵(染色.彩果)

      染色:置模型()
  end

end

local 还原染色 =染色:创建按钮("还原染色", 198, 262)
function 还原染色:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/ui",0x00000071,"图像"))
end
function 还原染色:左键弹起(x, y)
        染色.染色组 = {0,0,0}
        染色.染色方案=角色信息.染色方案 or 0
        染色:置模型()
        染色.彩果=10
        染色.染色显示=文本字体:置颜色(0,0,0):取精灵(染色.彩果)
end
local 转向按钮 =染色:创建按钮("转向按钮", 228, 262)
function 转向按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/ui",0x00000072,"图像"))
end
function 转向按钮:左键弹起(x, y)
          染色.方向 = 染色.方向 - 1
          if 染色.方向<1 then
              染色.方向=8
          end
          染色:置方向(染色.方向)
end
local 确定按钮 =染色:创建红色按钮("确定","确定按钮", 208, 295,40,22)

function 确定按钮:左键弹起(x, y)
      请求服务(3710,染色.染色组)
end


local 关闭 = 染色:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
      染色:置可见(false)
end



  


















