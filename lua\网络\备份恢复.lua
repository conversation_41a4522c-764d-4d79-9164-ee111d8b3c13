--[[
备份恢复器
功能: 提供安全的备份和恢复机制，支持增量备份、版本化备份等
作者: AI Assistant
日期: 2025-06-23
版本: v1.0
--]]

local SDLF = require("SDL.函数")
local ggf = require("GGE.函数")

local 备份恢复 = class("备份恢复器")

function 备份恢复:构造函数()
    self.备份目录 = "/backup"
    self.备份保留天数 = 7
    self.备份记录 = {}
    self.当前备份ID = nil
    
    self:初始化备份环境()
end

-- 初始化备份环境
function 备份恢复:初始化备份环境()
    local 备份路径 = SDLF.取内部存储路径() .. self.备份目录
    
    -- 创建备份目录
    if not self:目录存在(备份路径) then
        self:创建目录(备份路径)
    end
    
    -- 加载备份记录
    self:加载备份记录()
    
    -- 清理过期备份
    self:清理过期备份()
end

-- 创建备份
function 备份恢复:创建备份(备份类型)
    备份类型 = 备份类型 or "更新前备份"
    
    local 备份ID = self:生成备份ID()
    local 备份信息 = {
        ID = 备份ID,
        类型 = 备份类型,
        创建时间 = os.time(),
        文件列表 = {},
        状态 = "进行中"
    }
    
    print("备份恢复: 开始创建备份", 备份ID, 备份类型)
    
    -- 确定需要备份的文件
    local 备份文件列表 = self:获取备份文件列表()
    
    -- 执行备份
    local 备份成功 = true
    for _, 文件路径 in ipairs(备份文件列表) do
        if self:备份单个文件(备份ID, 文件路径) then
            table.insert(备份信息.文件列表, 文件路径)
        else
            print("备份恢复错误: 备份文件失败", 文件路径)
            备份成功 = false
        end
    end
    
    if 备份成功 then
        备份信息.状态 = "完成"
        self.当前备份ID = 备份ID
        print("备份恢复: 备份创建成功", 备份ID)
    else
        备份信息.状态 = "失败"
        print("备份恢复: 备份创建失败", 备份ID)
    end
    
    -- 保存备份记录
    self.备份记录[备份ID] = 备份信息
    self:保存备份记录()
    
    return 备份成功, 备份ID
end

-- 生成备份ID
function 备份恢复:生成备份ID()
    return "backup_" .. os.date("%Y%m%d_%H%M%S")
end

-- 获取需要备份的文件列表
function 备份恢复:获取备份文件列表()
    local 文件列表 = {}
    
    -- 主要文件
    local 主要文件 = {
        "/ggelua.com",
        "/config.txt",
        "/ggwb.txt"
    }
    
    for _, 文件 in ipairs(主要文件) do
        local 完整路径 = SDLF.取内部存储路径() .. 文件
        if self:文件存在(完整路径) then
            table.insert(文件列表, 文件)
        end
    end
    
    -- 如果存在主体名称，也备份
    if __主体名称 and __主体名称 ~= "ggelua.com" then
        local 主体文件 = "/" .. __主体名称
        local 完整路径 = SDLF.取内部存储路径() .. 主体文件
        if self:文件存在(完整路径) then
            table.insert(文件列表, 主体文件)
        end
    end
    
    return 文件列表
end

-- 备份单个文件
function 备份恢复:备份单个文件(备份ID, 文件路径)
    local 源文件 = SDLF.取内部存储路径() .. 文件路径
    local 备份文件 = SDLF.取内部存储路径() .. self.备份目录 .. "/" .. 备份ID .. 文件路径
    
    -- 确保备份目录存在
    local 备份目录 = string.match(备份文件, "(.+)/[^/]+$")
    if 备份目录 then
        self:创建目录(备份目录)
    end
    
    -- 复制文件
    return self:复制文件(源文件, 备份文件)
end

-- 恢复备份
function 备份恢复:恢复备份(备份ID)
    备份ID = 备份ID or self.当前备份ID
    
    if not 备份ID then
        print("备份恢复错误: 没有可恢复的备份")
        return false
    end
    
    local 备份信息 = self.备份记录[备份ID]
    if not 备份信息 then
        print("备份恢复错误: 备份记录不存在", 备份ID)
        return false
    end
    
    if 备份信息.状态 ~= "完成" then
        print("备份恢复错误: 备份状态异常", 备份ID, 备份信息.状态)
        return false
    end
    
    print("备份恢复: 开始恢复备份", 备份ID)
    
    -- 恢复文件
    local 恢复成功 = true
    for _, 文件路径 in ipairs(备份信息.文件列表) do
        if not self:恢复单个文件(备份ID, 文件路径) then
            print("备份恢复错误: 恢复文件失败", 文件路径)
            恢复成功 = false
        end
    end
    
    if 恢复成功 then
        print("备份恢复: 备份恢复成功", 备份ID)
    else
        print("备份恢复: 备份恢复失败", 备份ID)
    end
    
    return 恢复成功
end

-- 恢复单个文件
function 备份恢复:恢复单个文件(备份ID, 文件路径)
    local 备份文件 = SDLF.取内部存储路径() .. self.备份目录 .. "/" .. 备份ID .. 文件路径
    local 目标文件 = SDLF.取内部存储路径() .. 文件路径
    
    if not self:文件存在(备份文件) then
        print("备份恢复错误: 备份文件不存在", 备份文件)
        return false
    end
    
    return self:复制文件(备份文件, 目标文件)
end

-- 文件操作函数
function 备份恢复:文件存在(文件路径)
    return ggf.判断文件 and ggf.判断文件(文件路径) or false
end

function 备份恢复:目录存在(目录路径)
    -- 简单的目录存在检查
    return self:文件存在(目录路径)
end

function 备份恢复:创建目录(目录路径)
    -- 这里需要根据实际API实现目录创建
    if ggf.创建目录 then
        return ggf.创建目录(目录路径)
    end
    return true -- 假设创建成功
end

function 备份恢复:复制文件(源文件, 目标文件)
    if ggf.复制文件 then
        return ggf.复制文件(源文件, 目标文件)
    else
        -- 备用方法：读取然后写入
        local 内容 = self:读取文件(源文件)
        if 内容 then
            return self:写入文件(目标文件, 内容)
        end
    end
    return false
end

function 备份恢复:读取文件(文件路径)
    if ggf.读取文件 then
        return ggf.读取文件(文件路径)
    elseif __res and __res.读取文件 then
        return __res:读取文件(文件路径)
    end
    return nil
end

function 备份恢复:写入文件(文件路径, 内容)
    if ggf.写出文件 then
        return ggf.写出文件(文件路径, 内容)
    elseif __res and __res.写出文件 then
        return __res:写出文件(文件路径, 内容)
    end
    return false
end

-- 加载备份记录
function 备份恢复:加载备份记录()
    local 记录文件 = SDLF.取内部存储路径() .. self.备份目录 .. "/backup_records.txt"
    
    if self:文件存在(记录文件) then
        local 内容 = self:读取文件(记录文件)
        if 内容 then
            local 加载函数 = zdloadstring(内容)
            if 加载函数 then
                self.备份记录 = 加载函数() or {}
            end
        end
    end
end

-- 保存备份记录
function 备份恢复:保存备份记录()
    local 记录文件 = SDLF.取内部存储路径() .. self.备份目录 .. "/backup_records.txt"
    local 记录内容 = zdtostring(self.备份记录)
    
    self:写入文件(记录文件, 记录内容)
end

-- 清理过期备份
function 备份恢复:清理过期备份()
    local 当前时间 = os.time()
    local 过期时间 = self.备份保留天数 * 24 * 3600 -- 转换为秒
    
    local 需要删除 = {}
    
    for 备份ID, 备份信息 in pairs(self.备份记录) do
        if 当前时间 - 备份信息.创建时间 > 过期时间 then
            table.insert(需要删除, 备份ID)
        end
    end
    
    for _, 备份ID in ipairs(需要删除) do
        self:删除备份(备份ID)
    end
    
    if #需要删除 > 0 then
        print("备份恢复: 清理了", #需要删除, "个过期备份")
    end
end

-- 删除备份
function 备份恢复:删除备份(备份ID)
    local 备份信息 = self.备份记录[备份ID]
    if not 备份信息 then
        return false
    end
    
    -- 删除备份文件
    for _, 文件路径 in ipairs(备份信息.文件列表) do
        local 备份文件 = SDLF.取内部存储路径() .. self.备份目录 .. "/" .. 备份ID .. 文件路径
        if self:文件存在(备份文件) then
            self:删除文件(备份文件)
        end
    end
    
    -- 删除备份目录
    local 备份目录 = SDLF.取内部存储路径() .. self.备份目录 .. "/" .. 备份ID
    self:删除目录(备份目录)
    
    -- 从记录中移除
    self.备份记录[备份ID] = nil
    self:保存备份记录()
    
    print("备份恢复: 删除备份", 备份ID)
    return true
end

function 备份恢复:删除文件(文件路径)
    if ggf.删除文件 then
        return ggf.删除文件(文件路径)
    end
    return true -- 假设删除成功
end

function 备份恢复:删除目录(目录路径)
    if ggf.删除目录 then
        return ggf.删除目录(目录路径)
    end
    return true -- 假设删除成功
end

-- 获取备份列表
function 备份恢复:获取备份列表()
    local 备份列表 = {}
    
    for 备份ID, 备份信息 in pairs(self.备份记录) do
        table.insert(备份列表, {
            ID = 备份ID,
            类型 = 备份信息.类型,
            创建时间 = os.date("%Y-%m-%d %H:%M:%S", 备份信息.创建时间),
            状态 = 备份信息.状态,
            文件数量 = #备份信息.文件列表
        })
    end
    
    -- 按创建时间排序
    table.sort(备份列表, function(a, b)
        return a.创建时间 > b.创建时间
    end)
    
    return 备份列表
end

-- 获取最新备份
function 备份恢复:获取最新备份()
    local 最新备份 = nil
    local 最新时间 = 0
    
    for 备份ID, 备份信息 in pairs(self.备份记录) do
        if 备份信息.状态 == "完成" and 备份信息.创建时间 > 最新时间 then
            最新时间 = 备份信息.创建时间
            最新备份 = 备份ID
        end
    end
    
    return 最新备份
end

-- 设置备份保留天数
function 备份恢复:设置备份保留天数(天数)
    if 天数 and 天数 > 0 then
        self.备份保留天数 = 天数
        print("备份恢复: 备份保留天数设置为", 天数, "天")
    end
end

-- 获取备份统计信息
function 备份恢复:获取统计信息()
    local 统计 = {
        总备份数 = 0,
        完成备份数 = 0,
        失败备份数 = 0,
        总大小 = 0,
        最早备份 = nil,
        最新备份 = nil
    }
    
    for 备份ID, 备份信息 in pairs(self.备份记录) do
        统计.总备份数 = 统计.总备份数 + 1
        
        if 备份信息.状态 == "完成" then
            统计.完成备份数 = 统计.完成备份数 + 1
        else
            统计.失败备份数 = 统计.失败备份数 + 1
        end
        
        if not 统计.最早备份 or 备份信息.创建时间 < 统计.最早备份 then
            统计.最早备份 = 备份信息.创建时间
        end
        
        if not 统计.最新备份 or 备份信息.创建时间 > 统计.最新备份 then
            统计.最新备份 = 备份信息.创建时间
        end
    end
    
    return 统计
end

return 备份恢复 