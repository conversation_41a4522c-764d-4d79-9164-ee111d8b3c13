
__UI弹出.提示框 = 界面:创建弹出控件("提示框",0,0,引擎.宽度,引擎.高度)
local 提示框=__UI弹出.提示框 
function 提示框:初始化()

end
function 提示框:打开(数据)
    self:置可见(true)
    self.显示区域:添加内容(数据)
  
end


local 移动按钮 = 提示框:创建按钮('移动按钮')
function 移动按钮:初始化()
      self:创建按钮精灵(__res:取资源动画('jszy/fwtb',0x4CFB6A98),1)
end
function 移动按钮:左键按下(x, y)
    self.按下x = y
    self.按下y = y
end

function 移动按钮:左键按住(x, y)
    if self.按下y and self.按下x then
        local mx, my = 引擎:取鼠标坐标()
        local px,py = self.按下x-mx,self.按下y-my
        self.按下x,self.按下y =mx, my 
        提示框.显示区域:移动坐标(-px,-py)
    end
end
function 移动按钮:左键弹起(x, y)
    self.按下y =nil
    self.按下x =nil
end

local 显示区域 = 提示框:创建控件("显示区域")
function 显示区域:初始化()
      self:置坐标(引擎.宽度2-170,引擎.高度2-20)
end
function 显示区域:检查消息()
    return false
end
function 显示区域:移动坐标(px,py)
    
    self.开始移动 =true
    if self.x + self.宽度+px <=引擎.宽度 and  self.x+px >=0 then
        self.x = self.x + px
    end
    if self.y + self.高度+py <=引擎.高度-20 or self.y+py >=0 then
        self.y = self.y + py
    end
    if self.x<=0 then
        self.x=0
    end
    if self.x+self.宽度>=引擎.宽度 then
        self.x = 引擎.宽度-self.宽度
    end
    if self.y <=0 then
        self.y =0
    end
    if self.y+self.高度 >=引擎.高度-20 then
        self.y = 引擎.高度-self.高度-20
    end
    local yy = 0
    if #self.文本数据 > 0 then
        yy = yy +self.文本数据[#self.文本数据].y
    end
    提示框.移动按钮:置坐标(self.x,self.y+yy+2)
    self.开始移动=false
end

function 显示区域:更新(dt)
        if self.开始移动 then 
            return 
        end
        if #self.文本数据 > 0  then
            for i=1,#self.文本数据 do
                if self.文本数据[i]~=nil and os.clock()-self.文本数据[i].tiem>=2.5 then
                    self:删除控件("文本"..self.文本数据[i].name)
                    table.remove(self.文本数据, i)
                    self:适应坐标()
                end
            end
        end
end


function 显示区域:显示(x,y)
    if #self.文本数据 > 0  then
        for i=1,#self.文本数据 do
            self.文本数据[i].背景:显示(x+20,y+self.文本数据[i].y)
        end
    end

end

function 显示区域:添加内容(文本)
        if not self.创建计数 then self.创建计数=1 end
        if not self.文本数据 then self.文本数据={} end
        if #self.文本数据 > 6 then
            self:删除控件("文本"..self.文本数据[1].name)
            table.remove(self.文本数据, 1)
        end
        local txts = {
            tiem = os.clock(),
            name=self.创建计数,
            ffs =  self:创建文本("文本"..self.创建计数,0,0,320,20)
        }
        self.创建计数=self.创建计数+1
        txts.ffs:置文字(文本字体)
        local w,h = txts.ffs:置文本(文本)
        txts.ffs:置高度(h)
        txts.背景 = require('SDL.精灵')(0, 0, 0,340,h+10):置颜色(0, 0, 0, 150)
        table.insert(self.文本数据,txts)
        self:适应坐标()
end
function 显示区域:适应坐标()
    if #self.文本数据==0 then
        提示框.移动按钮.按下x=nil
        提示框.移动按钮.按下y=nil
        提示框:置可见(false)
        return
    end
    local hh=0
    if #self.文本数据 > 0 then
            for i=1,#self.文本数据 do
                self.文本数据[i].y=hh
                hh = hh + self.文本数据[i].背景.高度+5
            end
    end
    local py = self.高度-hh
    self:置宽高(360,hh)
    self:移动坐标(0,py)
    local yy = 0
    if #self.文本数据 > 0 then
        for i=1,#self.文本数据 do
            self.文本数据[i].ffs:置坐标(30,self.文本数据[i].y+5)
            self.文本数据[i].ffs:置可见(true,true)
        end
        yy = yy +self.文本数据[#self.文本数据].y
    end
    提示框.移动按钮:置坐标(self.x,self.y+yy+2)

   
end
-------------------
-------------------------------------------------



  
if __手机 then
    __UI弹出.自定义 = 界面:创建弹出控件("自定义", 0, 0)
 else
    __UI弹出.自定义 = 界面:创建提示控件("自定义", 0, 0)
 end
local 自定义 = __UI弹出.自定义
function 自定义:初始化()
end
function 自定义:更新(dt)
    if self.关闭时间 and self.关闭时间 <= os.time() then
        self.关闭时间 = nil
        self:置可见(false)
    end
end
function 自定义:左键弹起(x, y, msg)
    self.关闭时间 = nil
    self:置可见(false)
end
function 自定义:打开(x,y,数据,...)
    if  not 数据 then
        return
    end
    self.自定义文本:清空()
    self.自定义文本:置文字(文本字体)
    self.精灵文字=nil
    self.关闭时间 = nil
    local w, h = 0,0
    if  type(数据)=="string" then
        if select('#', ...) > 0 then
            数据 = 数据:format(...)
        end
        w, h =self.自定义文本:置文本(数据)
        self.精灵文字=nil
        self.自定义文本:置可见(true)
    elseif ggetype(数据)=="SDL图像" then
        self.精灵文字=数据:到精灵()
        w,h=self.精灵文字.宽度,self.精灵文字.高度
        self.自定义文本:置可见(false)
    elseif ggetype(数据)=="SDL精灵" then
        self.精灵文字=数据
        w,h=self.精灵文字.宽度,self.精灵文字.高度
        self.自定义文本:置可见(false)
    elseif ggetype(数据)=="tcp" then
        self.精灵文字=数据:取精灵(1)
        w,h=self.精灵文字.宽度,self.精灵文字.高度
        self.自定义文本:置可见(false)
    end

    if __手机 then
        self.关闭时间 = os.time() + 3  
    end
    self.自定义文本:置高度(h)
    self:置可见(true, true)
    self:置精灵(require('SDL.精灵')(0, 0, 0, w+20, h+10):置颜色(0, 0, 0, 150))
    self:置宽高(w + 20, h + 10)
    local xx = 0
    local yy = 0
    if x + w+25 <引擎.宽度 then
        xx = x
    else
        xx = 引擎.宽度-(w+25)
    end
    if y + h +15 <引擎.高度 then
        yy = y
    else
        yy = 引擎.高度-(h+15)
    end
    if xx<5 then
       xx = 5
    end
    if yy <5 then
       yy = 5
    end
    self:置坐标(xx,yy)
end

function 自定义:显示(x,y)
    if self.精灵文字 then
        self.精灵文字:显示(x+10,y+5)
    end

end
local 自定义文本 = 自定义:创建文本("自定义文本", 10, 5, 390, 40)
function 自定义文本:初始化()
   
end
