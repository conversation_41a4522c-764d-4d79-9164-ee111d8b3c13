{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        //ggebuild Windows
        {
            "label": "ggebuild_Windows",
            "type": "shell",
            "args": [
                "-GB",
                "BUILD",
                "Windows",
                "${workspaceFolder}"
            ],
            "windows": {
                "command": "${env:GGELUA}/lua.exe",
            },
            "osx": {
                "command": "/Applications/GGELUA2.app/Contents/Resources/lua",
            },
            "linux": {
                "command": "lua",
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "group": {
                "kind": "build",
                "isDefault": true
            },
            "problemMatcher": {
                "owner": "lua",
                "fileLocation": [
                    "autoDetect",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "([^\"]+)\"\\]:(\\d+):\\s*(.*)",
                    "file": 1,
                    "line": 2,
                    "message": 3
                }
            }
        },
        //ggebuild Android
        {
            "label": "ggebuild_Android",
            "type": "shell",
            "args": [
                "-GB",
                "BUILD",
                "Android",
                "${workspaceFolder}"
            ],
            "windows": {
                "command": "${env:GGELUA}/lua.exe",
            },
            "osx": {
                "command": "/Applications/GGELUA2.app/Contents/Resources/lua",
            },
            "linux": {
                "command": "lua",
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": {
                "owner": "lua",
                "fileLocation": [
                    "autoDetect",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "([^\"]+)\"\\]:(\\d+):\\s*(.*)",
                    "file": 1,
                    "line": 2,
                    "message": 3
                }
            }
        },
        //ggebuild MacOS
        {
            "label": "ggebuild_MacOS",
            "type": "shell",
            "args": [
                "-GB",
                "BUILD",
                "MacOS",
                "${workspaceFolder}"
            ],
            "windows": {
                "command": "${env:GGELUA}/lua.exe",
            },
            "osx": {
                "command": "/Applications/GGELUA2.app/Contents/Resources/lua",
            },
            "linux": {
                "command": "lua",
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": {
                "owner": "lua",
                "fileLocation": [
                    "autoDetect",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "([^\"]+)\"\\]:(\\d+):\\s*(.*)",
                    "file": 1,
                    "line": 2,
                    "message": 3
                }
            }
        },
        //ggebuild iOS
        {
            "label": "ggebuild_iOS",
            "type": "shell",
            "args": [
                "-GB",
                "BUILD",
                "iOS",
                "${workspaceFolder}"
            ],
            "windows": {
                "command": "${env:GGELUA}/lua.exe",
            },
            "osx": {
                "command": "/Applications/GGELUA2.app/Contents/Resources/lua",
            },
            "linux": {
                "command": "lua",
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": {
                "owner": "lua",
                "fileLocation": [
                    "autoDetect",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "([^\"]+)\"\\]:(\\d+):\\s*(.*)",
                    "file": 1,
                    "line": 2,
                    "message": 3
                }
            }
        },
        //build-ggeliua.com
        // {
        //     "label": "generate_ggelua_com",
        //     "type": "shell",
        //     "args": ["-GB", "BUILD", "HOTUPDATE", "${workspaceFolder}"],
        //     "windows": {
        //         "command": "${env:GGELUA}/lua.exe"
        //     },
        //     "osx": {
        //         "command": "/Applications/GGELUA2.app/Contents/Resources/lua"
        //     },
        //     "linux": {
        //         "command": "lua"
        //     },
        //     "presentation": {
        //         "echo": true,
        //         "reveal": "always",
        //         "focus": true,
        //         "panel": "shared",
        //         "showReuseMessage": false,
        //         "clear": true
        //     },
        //     "group": "build"
        // },
        //ggerun
        {
            "label": "ggerun",
            "type": "shell",
            "windows": {
                "args": [
                    "-GR",
                    "RUN",
                    "Windows",
                    "${workspaceFolder}"
                ],
                "command": "${env:GGELUA}/lua.exe",
            },
            "osx": {
                "args": [
                    "-GR",
                    "RUN",
                    "MacOS",
                    "${workspaceFolder}"
                ],
                "command": "/Applications/GGELUA2.app/Contents/Resources/lua",
            },
            "linux": {
                "args": [
                    "-GR",
                    "RUN",
                    "Linux",
                    "${workspaceFolder}"
                ],
                "command": "lua",
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true,
                "group": "ggelua"
            },
            "problemMatcher": {
                "owner": "lua",
                "fileLocation": [
                    "autoDetect",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "([^\"]+)\"\\]:(\\d+):\\s*(.*)",
                    "file": 1,
                    "line": 2,
                    "message": 3
                }
            }
        },
        {
            "label": "ggerun_android",
            "type": "shell",
            "args": [
                "-GR",
                "RUN",
                "Android",
                "${workspaceFolder}"
            ],
            "windows": {
                "command": "${env:GGELUA}/lua.exe",
            },
            "osx": {
                "command": "/Applications/GGELUA2.app/Contents/Resources/lua",
            },
            "linux": {
                "command": "lua",
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true,
                "group": "ggelua"
            },
            "problemMatcher": {
                "owner": "lua",
                "fileLocation": [
                    "autoDetect",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "([^\"]+)\"\\]:(\\d+):\\s*(.*)",
                    "file": 1,
                    "line": 2,
                    "message": 3
                }
            }
        },
        //lua
        {
            "label": "lua",
            "type": "shell",
            "args": [
                "${file}",
                "${workspaceFolder}"
            ],
            "windows": {
                "command": "${env:GGELUA}/lua.exe",
                "options": {
                    "shell": {
                        "executable": "cmd.exe",
                        "args": [
                            "/c \"\" ",
                            "chcp 65001 &&"
                        ]
                    }
                }
            },
            "osx": {
                "command": "/Applications/GGELUA2.app/Contents/MacOS/lua",
            },
            "linux": {
                "command": "lua",
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "problemMatcher": {
                "owner": "lua",
                "fileLocation": [
                    "autoDetect",
                    "${workspaceFolder}"
                ],
                "pattern": {
                    "regexp": "([^\"]+)\"\\]:(\\d+):\\s*(.*)",
                    "file": 1,
                    "line": 2,
                    "message": 3
                }
            }
        },
        //生成ggelua热更新包（无扩展名）
        {
            "label": "generate_ggelua",
            "type": "shell",
            "args": ["-GB", "BUILD", "GGELUA", "${workspaceFolder}"],
            "windows": {
                "command": "${env:GGELUA}/lua.exe"
            },
            "osx": {
                "command": "/Applications/GGELUA2.app/Contents/Resources/lua"
            },
            "linux": {
                "command": "lua"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "group": "build"
        },
        //生成ggelua.com热更新包（带扩展名）
        {
            "label": "generate_ggelua_dot_com",
            "type": "shell",
            "args": ["-GB", "BUILD", "GGELUA_COM", "${workspaceFolder}"],
            "windows": {
                "command": "${env:GGELUA}/lua.exe"
            },
            "osx": {
                "command": "/Applications/GGELUA2.app/Contents/Resources/lua"
            },
            "linux": {
                "command": "lua"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "shared",
                "showReuseMessage": false,
                "clear": true
            },
            "group": "build"
        }
    ]
}