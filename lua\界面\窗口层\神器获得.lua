
local 神器获得 = 窗口层:创建窗口("神器获得")
local 渲染背景 = {
    神木林 = 0x92A0D697,
    凌波城 = 0x087BEF28,
    盘丝洞 = 0xD9EC5E82,
    女儿村 = 0x5E688B50,
    无底洞 = 0x8CBAED1B,
    天宫   = 0x6918E30A,
    狮驼岭 = 0xFAF8A22D,
    阴曹地府 = 0x97E88B79,
    方寸山 = 0x69C5B0D1,
    魔王寨 = 0xFD1F847B,
    普陀山 = 0x119883BC,
    大唐官府 = 0x754D40E2,  
    化生寺 = 0x5587BEF3,
    龙宫   = 0xF9A0A6B9,
    五庄观 = 0x5E4F6929,
}

local 光源底图 = {
    神木林 = 0x4902C991,
    凌波城 = 0x37364FA2,
    女儿村 = 0x350639BB,
    无底洞 = 0xA40E6707,
    天宫   = 0xB7F12AC8,
    大唐官府 = 0x593B5349,
    方寸山 = 0xC8326D89,
    魔王寨 = 0xF244CEF3,
    五庄观 = 0x5E4F6929,
}

local 神器模型 = {
    神木林 = {0x018013AE,0x48A22679,0x27504873}, 
    天宫 = {0x290ACCB0,0xB2A9682E,0xC68841B3}, 
    盘丝洞 = {0x131B5F07,0x507F5020,0xD2EAC261}, 
    凌波城 = {0x9B10DFD8,0x65BB498B,0x3AAA5BED}, 
    女儿村 = {0x909F588F,0x14488877,0xF57FB4C6}, 
    无底洞 = {0xF991023F,0x16E503E3,0x4428166E}, 
    狮驼岭 = {0x7A43A176,0x8167254F,0x3BECCD5C}, 
    阴曹地府 = {0x64DDB00C,0x523D76AB,0xD02AC1C7}, 
    大唐官府 = {0x828B7E27,0x780B0000,0x52E6A411}, 
    化生寺 = {0xE2F71BD1,0xDA956848,0x5CC81540}, 
    龙宫 = {0x6EDC07C1,0xC7A953A8,0xF288E253}, 
    五庄观 = {0xA00DD3E7,0xE1CF43ED,0xDE964585}, 
    九黎城 = {0x10000079,0x10000080,0x10000081},
}
local 神器模型1={
    普陀山={[1]={"pt1.png","pt2.png","pt3.png"},[2]={"pt11.png","pt22.png","pt33.png"}},
    魔王寨={[1]={"mw1.png","mw2.png","mw3.png"},[2]={"mw11.png","mw22.png","mw33.png"}},
    方寸山={[1]={"fc1.png","fc2.png","fc3.png"},[2]={"fc11.png","fc22.png","fc33.png"}},
    花果山={[1]={"hg1.png","hg2.png","hg3.png"},[2]={"hg11.png","hg22.png","hg33.png"}},

}


function 神器获得:初始化()
        self:置宽高(引擎.宽度,引擎.高度)
        self:置精灵(require("SDL.精灵")(0, 0, 0,引擎.宽度,引擎.高度):置颜色(30,30,70,150))
        self:置坐标(0, 0)
end


function 神器获得:更新(dt)
    if self.打开时间 and os.time()>self.打开时间 then
        self:置可见(false)
        self.打开时间=nil
    end
end








function 神器获得:打开(门派)
        self:置可见(true)
    
        self.打开时间=os.time()+5
        self.主体:显示设置(门派)
end





local 主体=神器获得:创建控件("主体",0,0,640,480)

function 主体:显示设置(门派)
    self:置宽高(640,480)
    self:置坐标((引擎.宽度 - self.宽度) // 2+20, (引擎.高度 - self.高度) // 2-10)
    self.神器光源=nil
    self.神器渲染=nil
    self.棋子=nil
    self.门派=门派
    if 渲染背景[self.门派] then
        self.神器渲染=__res:取资源动画("jszy/xjjm",渲染背景[self.门派],"精灵")
    end
    if 光源底图[self.门派] then
        self.神器光源=__res:取资源动画("jszy/xjjm",光源底图[self.门派],"精灵")
    end
    for i = 1, 3 do
        self["按钮"..i]:显示设置(self.门派)
    end
    if self.门派=="神木林" then
        self.按钮1:置坐标(260,67)
        self.按钮2:置坐标(234,137)
        self.按钮3:置坐标(203,134)  
    elseif self.门派=="大唐官府" then
        self.按钮1:置坐标(260,114)
        self.按钮2:置坐标(39,191)
        self.按钮3:置坐标(119,240) 
    elseif self.门派=="化生寺" then
        self.按钮1:置坐标(336,136)
        self.按钮2:置坐标(201,70)
        self.按钮3:置坐标(86,216) 
    elseif self.门派=="女儿村" then
        self.按钮1:置坐标(146,63)
        self.按钮2:置坐标(193,185)
        self.按钮3:置坐标(108,155) 
    elseif self.门派=="方寸山" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(96,65)
            self["按钮"..i]:置宽高(350,350)
        end
      
    elseif self.门派=="龙宫" then
        self.按钮1:置坐标(127,66)
        self.按钮2:置坐标(232,66)
        self.按钮3:置坐标(76,71) 
    elseif self.门派=="普陀山" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(126,33)
            self["按钮"..i]:置宽高(380,380)
        end
    elseif self.门派=="五庄观" then
        self.按钮1:置坐标(156,86)
        self.按钮2:置坐标(193,186)
        self.按钮3:置坐标(153,159) 
        self.棋子=__res:取资源动画("jszy/xjjm",0x013253E7,"精灵")
    elseif self.门派=="天宫" then
        self.按钮1:置坐标(277,66)
        self.按钮2:置坐标(121,122)
        self.按钮3:置坐标(177,232) 
    elseif self.门派=="凌波城" then
        self.按钮1:置坐标(183,66)
        self.按钮2:置坐标(148,91)
        self.按钮3:置坐标(159,201) 
    elseif self.门派=="狮驼岭" then
        self.按钮1:置坐标(135,60)
        self.按钮2:置坐标(150,143)
        self.按钮3:置坐标(213,239) 
    elseif self.门派=="魔王寨" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(96,33)
            self["按钮"..i]:置宽高(350,350)
        end
    elseif self.门派=="阴曹地府" then
        self.按钮1:置坐标(115,126)
        self.按钮2:置坐标(165,173)
        self.按钮3:置坐标(151,261) 
    elseif self.门派=="无底洞" then
        self.按钮1:置坐标(323,60)
        self.按钮2:置坐标(234,151)
        self.按钮3:置坐标(137,238) 
    elseif self.门派=="盘丝洞" then
        self.按钮1:置坐标(165,120)
        self.按钮2:置坐标(125,53)
        self.按钮3:置坐标(138,189) 
	elseif self.门派=="九黎城" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(100,70)
            self["按钮"..i]:置宽高(380,300)
        end
    elseif self.门派=="花果山" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(96,37)
            self["按钮"..i]:置宽高(350,350)
        end
    end
    
   

end


function 主体:显示(x,y)
        if self.门派 then
            if self.神器渲染 then
                if self.门派=="神木林" then
                    self.神器渲染:显示(x + 110,y + 29) 
                elseif self.门派=="大唐官府" then
                    self.神器渲染:显示(x + 73,y + 50)
                elseif self.门派=="化生寺" then
                    self.神器渲染:显示(x + 53,y + 32)
                elseif self.门派=="女儿村" then
                    self.神器渲染:显示(x + 78,y + 12)
                elseif self.门派=="方寸山" then
                    self.神器渲染:显示(x + 111,y + 18)
                elseif self.门派=="龙宫" then
                    self.神器渲染:显示(x + 108,y + 18)
                elseif self.门派=="普陀山" then
                    self.神器渲染:显示(x + 138,y + 25)
                elseif self.门派=="五庄观" then
                    self.神器渲染:显示(x + 88,y + 44)
                elseif self.门派=="天宫" then
                    self.神器渲染:显示(x + 108,y + 46)
                elseif self.门派=="凌波城" then
                    self.神器渲染:显示(x + 85,y + 48)
                elseif self.门派=="狮驼岭" then
                    self.神器渲染:显示(x + 83,y + 47)
                elseif self.门派=="阴曹地府" then
                    self.神器渲染:显示(x + 88,y + 29)
                elseif self.门派=="无底洞" then
                    self.神器渲染:显示(x + 118,y + 28)
                elseif self.门派=="盘丝洞" then
                    self.神器渲染:显示(x + 97,y + 23)
                end
            end
            if self.神器光源 then
                if self.门派=="神木林" then
                    self.神器光源:显示(x + 109,y + 30) 
                elseif self.门派=="大唐官府" then
                    self.神器光源:显示(x + 75,y + 45) 
                elseif self.门派=="女儿村" then
                    self.神器光源:显示(x + 80,y + 10)
                elseif self.门派=="方寸山" then
                    self.神器光源:显示(x + 114,y + 13)
                elseif self.门派=="五庄观" then
                    self.神器光源:显示(x + 88,y + 41)
                elseif self.门派=="天宫" then
                    self.神器光源:显示(x + 114,y + 43)  
                elseif self.门派=="凌波城" then
                    self.神器光源:显示(x + 88,y + 45)
                elseif self.门派=="魔王寨" then
                    self.神器光源:显示(x + 88,y + 45)
                elseif self.门派=="无底洞" then
                    self.神器光源:显示(x + 116,y + 33) 
                end
            end
        end
  if self.棋子 then 
    self.棋子:显示(x + 89,y + 42)
  end 
end


for n = 1, 3 do
    local 临时函数=主体:创建按钮("按钮"..n)
    function 临时函数:显示设置(门派)
            local tpc = __res:取资源动画("jszy/jmxf",0x00000407,"图像"):拉伸(10,10)
            local tpc1 = __res:取资源动画("jszy/jmxf",0x00000407):取图像(2):拉伸(10,10)
            if 门派=="九黎城" then
                tpc = __res:取资源动画("jszy/jmtb",神器模型[门派][n],"图像")
                tpc1 = __res:取资源动画("jszy/jmtb",神器模型[门派][n]):取图像(2)
            elseif 神器模型1[门派] and 神器模型1[门派][1] then
                tpc = __res:取资源动画("pic/sqsc",神器模型1[门派][1][n],"图片")
                tpc1 =__res:取资源动画("pic/sqsc",神器模型1[门派][2][n],"图片")
            elseif 神器模型[门派] and 神器模型[门派][n] then
                tpc = __res:取资源动画("jszy/xjjm",神器模型[门派][n],"图像")
                tpc1 = __res:取资源动画("jszy/xjjm",神器模型[门派][n]):取图像(3)
            end
            self:置宽高(tpc.宽度,tpc.高度)
            self:置正常精灵(tpc:到精灵())
            self:置按下精灵(tpc:到精灵())
            if not __手机 then
                self:置经过精灵(tpc1:到精灵())
            end
    end

end


  


















