local 召唤兽合宠 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "召唤兽合宠", 179 + abbr.py.x, 5 + abbr.py.y, 738, 520)
local lsb = {
  "攻击资质",
  "体力资质",
  "速度资质",
  "寿  命",
  "五  行",
  "防御资质",
  "法术资质",
  "闪躲资质",
  "成  长"
}
local lsb2 = {
  "攻击资质",
  "体力资质",
  "速度资质",
  "寿命",
  "五行",
  "防御资质",
  "法力资质",
  "躲闪资质",
  "成长"
}
function 召唤兽合宠:初始化()
  local nsf = require("SDL.图像")(730, 520)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("合宠", 0, 12, 417, 506, true)["显示"](xiao置窗口背景("合宠", 0, 12, 417, 506, true), 0, 0)
    local lssc = 取白色背景(0, 0, 308, 188, true)
    取灰色背景(0, 0, 307, 217, true)["显示"](取灰色背景(0, 0, 307, 217, true), 17, 291)
    取灰色背景(0, 0, 307, 217, true)["显示"](取灰色背景(0, 0, 307, 217, true), 400, 291)
    lssc["显示"](lssc, 18, 50)
    lssc["显示"](lssc, 399, 50)
    字体18["置颜色"](字体18, 39, 53, 81)
    local pyx = 0
    local pyy = 0
    for i = 1, #lsb do
      if i > 5 then
        pyx = 142
        pyy = -120
      end
      字体18["取图像"](字体18, lsb[i])["显示"](字体18["取图像"](字体18, lsb[i]), 39 + pyx, 116 + (i - 1) * 24 + pyy)
    end
    local pyx = 0
    local pyy = 0
    for i = 1, #lsb do
      if i > 5 then
        pyx = 142
        pyy = -120
      end
      字体18["取图像"](字体18, lsb[i])["显示"](字体18["取图像"](字体18, lsb[i]), 420 + pyx, 116 + (i - 1) * 24 + pyy)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.选中bb1 = nil
  self.选中bb2 = nil
end


function 召唤兽合宠:打开(数据)
  self:置可见(true)
  self.刷新(self)
end
function 召唤兽合宠:显示(x, y)
  if self.图像 then
    self.图像["显示"](self.图像, x, y)
  end
  if self.图像2 then
    self.图像2["显示"](self.图像2, x, y)
  end
  -- self.模型格子["显示"](self.模型格子, x, y)
  -- if self.btt then
  --   self.btt["显示"](self.btt, x, y)
  -- end
  
end

function 召唤兽合宠:刷新()
  self.选中bb1 = nil
  self.选中bb2 = nil
  self.图像 = nil
  self.图像2 = nil
  self.头像网格["置头像"](self.头像网格, nil)
  self.头像网格2["置头像"](self.头像网格2, nil)
  self.材料网格["置物品"](self.材料网格, nil)
  self.技能控件["置可见"](self.技能控件, false)
  self.技能控件2["置可见"](self.技能控件2, false)
end


function 召唤兽合宠:重置(数据, lx)
  if "合宠" == lx then
    self.选中bb1 = 数据
    local nsf = require("SDL.图像")(730, 240)
    if nsf["渲染开始"](nsf) then
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      local pyx = 0
      local pyy = 0
      for i = 1, #lsb2 do
        if i > 5 then
          pyx = 142
          pyy = -120
        end
        字体18:取图像(角色信息["宝宝列表"][self.选中bb1][lsb2[i]]):显示(126 + pyx, 116 + (i - 1) * 24 + pyy)
        -- print(角色信息["宝宝列表"][self.选中bb1][lsb2[i]])
      end
      字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb1]["名称"])["显示"](字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb1]["名称"]), 100, 60)
      字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb1]["等级"] .. " 级")["显示"](字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb1]["等级"] .. " 级"), 100, 80)
    end
    self.图像 = nsf["到精灵"](nsf)
    self.头像网格["置头像"](self.头像网格, 角色信息["宝宝列表"][self.选中bb1])
    self.技能控件["置可见"](self.技能控件, true)
    self.技能控件["技能按钮"]["置选中"](self.技能控件["技能按钮"], true)
    self.技能控件["技能"]["重置"](self.技能控件["技能"])
  elseif "合宠2" == lx then
    self.选中bb2 = 数据
    local nsf = require("SDL.图像")(730, 240)
    if nsf["渲染开始"](nsf) then
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      local pyx = 0
      local pyy = 0
      for i = 1, #lsb2 do
        if i > 5 then
          pyx = 142
          pyy = -120
        end
        字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb2][lsb2[i]])["显示"](字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb2][lsb2[i]]), 508 + pyx, 116 + (i - 1) * 24 + pyy)
      end
      字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb2]["名称"])["显示"](字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb2]["名称"]), 480, 60)
      字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb2]["等级"] .. " 级")["显示"](字体18["取图像"](字体18, 角色信息["宝宝列表"][self.选中bb2]["等级"] .. " 级"), 480, 80)
    end
    self.图像2 = nsf["到精灵"](nsf)
    self.头像网格2["置头像"](self.头像网格2, 角色信息["宝宝列表"][self.选中bb2])
    self.技能控件2["置可见"](self.技能控件2, true)
    self.技能控件2["技能按钮"]["置选中"](self.技能控件2["技能按钮"], true)
    self.技能控件2["技能"]["重置"](self.技能控件2["技能"])
  end
end


local 关闭 = 召唤兽合宠["创建我的按钮"](召唤兽合宠, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 688, 0)
function 关闭:左键弹起(x, y, msg)
  召唤兽合宠["置可见"](召唤兽合宠, false)
end
local 头像网格 = 召唤兽合宠["创建网格"](召唤兽合宠, "头像网格", 39, 55, 50, 50)
function 头像网格:初始化()
  self:创建格子(50, 50, 0, 0, 1, 1)
end
function 头像网格:左键弹起(x, y, a, b, msg)
  __UI弹出["召唤兽选择"]["打开"](__UI弹出["召唤兽选择"], 151+130, 133, "合宠")
end
function 头像网格:置头像(数据)
  local lssj = __头像选择格子["创建"]()
  lssj["置头像"](lssj, 数据)
  头像网格["子控件"][1]["置精灵"](头像网格["子控件"][1], lssj)
end
local 头像网格2 = 召唤兽合宠["创建网格"](召唤兽合宠, "头像网格2", 420, 55, 50, 50)
function 头像网格2:初始化()
  self:创建格子(50, 50, 0, 0, 1, 1)
end
function 头像网格2:左键弹起(x, y, a, b, msg)
  __UI弹出["召唤兽选择"]["打开"](__UI弹出["召唤兽选择"], 510+130, 133, "合宠2")
end
function 头像网格2:置头像(数据)
  local lssj = __头像选择格子["创建"]()
  lssj["置头像"](lssj, 数据)
  头像网格2["子控件"][1]["置精灵"](头像网格2["子控件"][1], lssj)
end
local 技能控件 = 召唤兽合宠["创建控件"](召唤兽合宠, "技能控件", 16, 245, 310, 265)
local jcx = 0
local jcy = 0
local lsb3 = {
  "技能",
  "内丹",
  "进阶"
}
for i = 1, #lsb3 do
  local 临时函数 = 技能控件["创建我的单选按钮"](技能控件, __res:getPNGCC(3, 880, 331, 86, 37, true), __res:getPNGCC(3, 876, 289, 85, 36, true), lsb3[i] .. "按钮", jcx + (i - 1) * 102, jcy, lsb3[i])
 function  临时函数:左键按下(消息, x, y)
    技能控件[lsb3[i]]["重置"](技能控件[lsb3[i]])
  end
  local 临时函数2 = 技能控件["创建控件"](技能控件, lsb3[i], jcx, jcy + 45, 310, 220)
 function  临时函数2:初始化()
    if "技能" == lsb3[i] then
      -- local nsf = require("SDL.图像")(310, 220)
      -- if nsf["渲染开始"](nsf) then
      --   取灰色背景(0, 0, 308, 220, true)["显示"](取灰色背景(0, 0, 308, 220, true), 0, 0)
      --   nsf["渲染结束"](nsf)
      -- end
      -- self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 22, 12, 273, 202)
     function  临时函数3:初始化()
        self:创建格子(55, 55, 18, 18, 9, 4, true)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(数据, 认证)
        for i = 1, #临时函数3["子控件"] do
          local lssj = __召唤兽技能格子["创建"]()
          if 认证 and i == 认证 then
            认证 = 数据[i]
          end
          
          if 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]] and 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]].赐福技能_生效 then
            是否赐福=判断是否赐福技能(角色信息["宝宝列表"][召唤兽合宠["选中bb1"]].赐福技能_生效  ,  数据[i])
          end
          lssj:置数据(数据[i], 55, 55, nil, 认证,是否赐福)
         -- lssj["置数据"](lssj, 数据[i], 55, 55, nil, 认证)
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
        end
      end
    elseif "内丹" == lsb3[i] then
      local nsf = require("SDL.图像")(310, 220)
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 653, 931, 315, 218)["显示"](__res:getPNGCC(3, 653, 931, 315, 218), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 0, 0, 315, 218)
     function  临时函数3:初始化()
        self:创建格子(55, 55, 8, 8, 1, 6)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(zjcz)
      local 坐标 = {
        {129, 7}, --1
        {129-67, 7+41}, --2
        {129+65, 7+41}, --3
        {129+65, 7+80+33}, --4
        {129, 7+150}, --5
        {129-67, 7+80+33}, --6
      }
        for i = 1, #临时函数3["子控件"] do
          local lssj = __内丹格子["创建"]()
          lssj:置数据(zjcz,角色信息["宝宝列表"][召唤兽合宠["选中bb1"]],i, 55, 55) 
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
          临时函数3["子控件"][i]["置坐标"](临时函数3["子控件"][i], 坐标[i][1], 坐标[i][2])
        end
      end
    elseif "进阶" == lsb3[i] then
      local nsf = require("SDL.图像")(310, 220)
      if nsf["渲染开始"](nsf) then
        取灰色背景(0, 0, 308, 220, true)["显示"](取灰色背景(0, 0, 308, 220, true), 0, 0)
        __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094)), 196, 210)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094)), 196, 210), 55, 7)
        __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212), 55, 7)
        nsf["渲染结束"](nsf)
      end
      self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 76, 52, 126, 102)
     function  临时函数3:初始化()
        self:创建格子(126, 102, 0, 0, 1, 1)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(数据, zjcz, bb, nsgz)
        for i = 1, #临时函数3["子控件"] do
          local lssj = __特性格子["创建"]()
          lssj["置数据"](lssj, 数据, 126, 102)
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
        end
      end
    end
    self:置可见(false)
  end
 function  临时函数2:显示(x, y)
    if self.数据 then
      self.数据["显示"](self.数据, x, y)
    end
  end
 function  临时函数2:重置()
    if "技能" == lsb3[i] then
      技能控件["技能"]["置可见"](技能控件["技能"], true)
      技能控件["内丹"]["置可见"](技能控件["内丹"], false)
      技能控件["进阶"]["置可见"](技能控件["进阶"], false)
      self.数据 = nil
      if 召唤兽合宠["选中bb1"] then
        临时函数2["技能网格"]["置数据"](临时函数2["技能网格"], 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]]["技能"], 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]]["法术认证"])
      else
        临时函数2["技能网格"]["置数据"](临时函数2["技能网格"], {})
      end
    elseif "内丹" == lsb3[i] then
      技能控件["技能"]["置可见"](技能控件["技能"], false)
      技能控件["内丹"]["置可见"](技能控件["内丹"], true)
      技能控件["进阶"]["置可见"](技能控件["进阶"], false)
      self.数据 = nil
      local zjcz = 0
      if 召唤兽合宠["选中bb1"] and 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]].内丹 and 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]].内丹.格子 and 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]].内丹.内丹上限 then
        zjcz=0
        for n = 1,#角色信息.坐骑列表 do
          if 角色信息.坐骑列表[n].统御召唤兽[1] == 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]].认证码 or 角色信息.坐骑列表[n].统御召唤兽[2] == 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]].认证码 then
            zjcz = 角色信息.坐骑列表[n].初始成长
            break
          end
        end
        临时函数2["内丹网格"]["置数据"](临时函数2["内丹网格"], zjcz)
      else
        临时函数2["内丹网格"]["置数据"](临时函数2["内丹网格"])
      end
    elseif "进阶" == lsb3[i] then
      技能控件["技能"]["置可见"](技能控件["技能"], false)
      技能控件["内丹"]["置可见"](技能控件["内丹"], false)
      技能控件["进阶"]["置可见"](技能控件["进阶"], true)
      self.数据 = nil
      if 召唤兽合宠["选中bb1"] and 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]]["进阶"] then
        local nsf = require("SDL.图像")(310, 220)
        local jinjie=角色信息["宝宝列表"][召唤兽合宠["选中bb1"]]["进阶"]
        local lx=jinjie.灵性
        if nsf["渲染开始"](nsf) then
          if lx > 0 and lx <= 10 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869)), 200, 212), 55, 7)
          elseif lx > 10 and lx <= 20 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230)), 200, 212), 55, 7)
          elseif lx > 20 and lx <= 30 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528)), 200, 212), 55, 7)
          elseif lx > 30 and lx <= 40 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212), 55, 7)
          elseif lx > 40 and lx <= 50 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520)), 200, 212), 55, 7)
          elseif lx > 50 and lx <= 60 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105)), 200, 212), 55, 7)
          elseif lx > 60 and lx <= 70 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892)), 200, 212), 55, 7)
          elseif lx > 70 and lx <= 80 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450)), 200, 212), 55, 7)
          elseif lx > 80 and lx <= 90 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895)), 200, 212), 155, 7)
          elseif lx > 90 and lx <= 91 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119)), 200, 212), 55, 7)
          elseif lx > 91 and lx <= 93 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218)), 200, 212), 55, 7)
          elseif lx > 93 and lx <= 97 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179)), 200, 212), 155, 7)
          elseif lx >= 98 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070)), 200, 212), 55, 7)
          end
          字体18["置颜色"](字体18, 255, 255, 255)
          字体18["取图像"](字体18, "灵性:" .. lx):置混合(0):显示(15, 7)
          if jinjie.特性 and jinjie.特性~="无" then
            if jinjie.开启 then
              字体18["置颜色"](字体18, __取颜色("黄色"))
            else
              字体18["置颜色"](字体18, __取颜色("白色"))
            end
            字体18["取图像"](字体18, jinjie.特性):置混合(0):显示(15+117, 7+89)
          end
          nsf["渲染结束"](nsf)
        end
        self.数据 = nsf["到精灵"](nsf)
        临时函数2["进阶网格"]["置数据"](临时函数2["进阶网格"], 角色信息["宝宝列表"][召唤兽合宠["选中bb1"]])
      else
        临时函数2["进阶网格"]["置数据"](临时函数2["进阶网格"], {})
      end
    end
  end
end
local 技能控件2 = 召唤兽合宠["创建控件"](召唤兽合宠, "技能控件2", 399, 245, 310, 265)
local jcx = 0
local jcy = 0
local lsb3 = {
  "技能",
  "内丹",
  "进阶"
}
for i = 1, #lsb3 do
  local 临时函数 = 技能控件2["创建我的单选按钮"](技能控件2, __res:getPNGCC(3, 880, 331, 86, 37, true), __res:getPNGCC(3, 876, 289, 85, 36, true), lsb3[i] .. "按钮", 17+jcx + (i - 1) * 102, jcy, lsb3[i])
 function  临时函数:左键按下(消息, x, y)
    技能控件2[lsb3[i]]["重置"](技能控件2[lsb3[i]])
  end
  local 临时函数2 = 技能控件2["创建控件"](技能控件2, lsb3[i], jcx, jcy + 45, 310, 220)
 function  临时函数2:初始化()
    if "技能" == lsb3[i] then
      -- local nsf = require("SDL.图像")(310, 220)
      -- if nsf["渲染开始"](nsf) then
      --   取灰色背景(0, 0, 308, 220, true)["显示"](取灰色背景(0, 0, 308, 220, true), 0, 0)
      --   nsf["渲染结束"](nsf)
      -- end
      -- self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 22, 12, 273, 202)
     function  临时函数3:初始化()
        self:创建格子(55, 55, 18, 18, 9, 4, true)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(数据, 认证)
        for i = 1, #临时函数3["子控件"] do
          local lssj = __召唤兽技能格子["创建"]()
          if 认证 and i == 认证 then
            认证 = 数据[i]
          end
          if 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]] and 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]].赐福技能_生效 then
            是否赐福=判断是否赐福技能(角色信息["宝宝列表"][召唤兽合宠["选中bb2"]].赐福技能_生效  ,  数据[i])
          end
          lssj:置数据(数据[i], 55, 55, nil, 认证,是否赐福)
          --lssj["置数据"](lssj, 数据[i], 55, 55, nil, 认证)
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
        end
      end
    elseif "内丹" == lsb3[i] then
      local nsf = require("SDL.图像")(310, 220)
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 653, 931, 315, 218)["显示"](__res:getPNGCC(3, 653, 931, 315, 218), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 0, 0, 315, 218)
     function  临时函数3:初始化()
        self:创建格子(55, 55, 8, 8, 1, 6)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(zjcz)
      local 坐标 = {
        {129, 7}, --1
        {129-67, 7+41}, --2
        {129+65, 7+41}, --3
        {129+65, 7+80+33}, --4
        {129, 7+150}, --5
        {129-67, 7+80+33}, --6
      }
        for i = 1, #临时函数3["子控件"] do
          local lssj = __内丹格子["创建"]()
          lssj:置数据(zjcz,角色信息["宝宝列表"][召唤兽合宠["选中bb2"]],i, 55, 55) 
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
          临时函数3["子控件"][i]["置坐标"](临时函数3["子控件"][i], 坐标[i][1], 坐标[i][2])
        end
      end
    elseif "进阶" == lsb3[i] then
      local nsf = require("SDL.图像")(310, 220)
      if nsf["渲染开始"](nsf) then
        取灰色背景(0, 0, 308, 220, true)["显示"](取灰色背景(0, 0, 308, 220, true), 0, 0)
        __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094)), 196, 210)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 278179094)), 196, 210), 55, 7)
        __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212), 55, 7)
        nsf["渲染结束"](nsf)
      end
      self:置精灵(nsf["到精灵"](nsf))
      local 临时函数3 = 临时函数2["创建网格"](临时函数2, lsb3[i] .. "网格", 76, 52, 126, 102)
     function  临时函数3:初始化()
        self:创建格子(126, 102, 0, 0, 1, 1)
      end
     function  临时函数3:左键弹起(x, y, a, b, msg)
        if self.子控件[a]._spr["数据"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
        end
      end
     function  临时函数3:置数据(数据, zjcz, bb, nsgz)
        for i = 1, #临时函数3["子控件"] do
          local lssj = __特性格子["创建"]()
          lssj["置数据"](lssj, 数据, 126, 102)
          临时函数3["子控件"][i]["置精灵"](临时函数3["子控件"][i], lssj)
        end
      end
    end
    self:置可见(false)
  end
 function  临时函数2:显示(x, y)
    if self.数据 then
      self.数据["显示"](self.数据, x, y)
    end
  end
 function  临时函数2:重置()
    if "技能" == lsb3[i] then
      技能控件2["技能"]["置可见"](技能控件2["技能"], true)
      技能控件2["内丹"]["置可见"](技能控件2["内丹"], false)
      技能控件2["进阶"]["置可见"](技能控件2["进阶"], false)
      self.数据 = nil
      if 召唤兽合宠["选中bb2"] then
        临时函数2["技能网格"]["置数据"](临时函数2["技能网格"], 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]]["技能"], 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]]["法术认证"])
      else
        临时函数2["技能网格"]["置数据"](临时函数2["技能网格"], {})
      end
    elseif "内丹" == lsb3[i] then
      技能控件2["技能"]["置可见"](技能控件2["技能"], false)
      技能控件2["内丹"]["置可见"](技能控件2["内丹"], true)
      技能控件2["进阶"]["置可见"](技能控件2["进阶"], false)
      self.数据 = nil
      local zjcz = 0
      if 召唤兽合宠["选中bb2"] and 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]].内丹 and 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]].内丹.格子 and 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]].内丹.内丹上限 then
        zjcz=0
        for n = 1,#角色信息.坐骑列表 do
          if 角色信息.坐骑列表[n].统御召唤兽[1] == 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]].认证码 or 角色信息.坐骑列表[n].统御召唤兽[2] == 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]].认证码 then
            zjcz = 角色信息.坐骑列表[n].初始成长
            break
          end
        end
        临时函数2["内丹网格"]["置数据"](临时函数2["内丹网格"], zjcz)
      else
        临时函数2["内丹网格"]["置数据"](临时函数2["内丹网格"])
      end
    elseif "进阶" == lsb3[i] then
      技能控件2["技能"]["置可见"](技能控件2["技能"], false)
      技能控件2["内丹"]["置可见"](技能控件2["内丹"], false)
      技能控件2["进阶"]["置可见"](技能控件2["进阶"], true)
      self.数据 = nil
      if 召唤兽合宠["选中bb2"] and 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]]["进阶"] then
        local nsf = require("SDL.图像")(310, 220)
        local jinjie=角色信息["宝宝列表"][召唤兽合宠["选中bb2"]]["进阶"]
        local lx=jinjie.灵性
        if nsf["渲染开始"](nsf) then
          if lx > 0 and lx <= 10 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1161207869)), 200, 212), 55, 7)
          elseif lx > 10 and lx <= 20 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 1900820230)), 200, 212), 55, 7)
          elseif lx > 20 and lx <= 30 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3590329528)), 200, 212), 55, 7)
          elseif lx > 30 and lx <= 40 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 4159437191)), 200, 212), 55, 7)
          elseif lx > 40 and lx <= 50 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 295056520)), 200, 212), 55, 7)
          elseif lx > 50 and lx <= 60 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2588875105)), 200, 212), 55, 7)
          elseif lx > 60 and lx <= 70 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 511359892)), 200, 212), 55, 7)
          elseif lx > 70 and lx <= 80 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2798233450)), 200, 212), 55, 7)
          elseif lx > 80 and lx <= 90 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 696443895)), 200, 212), 155, 7)
          elseif lx > 90 and lx <= 91 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 487004119)), 200, 212), 55, 7)
          elseif lx > 91 and lx <= 93 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 3293513218)), 200, 212), 55, 7)
          elseif lx > 93 and lx <= 97 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 2540032179)), 200, 212), 155, 7)
          elseif lx >= 98 then
            __res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070)), 200, 212)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/zhs/", 916636070)), 200, 212), 55, 7)
          end
          字体18["置颜色"](字体18, 255, 255, 255)
          字体18["取图像"](字体18, "灵性:" .. lx):置混合(0):显示(15, 7)
          if jinjie.特性 and jinjie.特性~="无" then
            if jinjie.开启 then
              字体18["置颜色"](字体18, __取颜色("黄色"))
            else
              字体18["置颜色"](字体18, __取颜色("白色"))
            end
            字体18["取图像"](字体18, jinjie.特性):置混合(0):显示(15+117, 7+89)
          end
          nsf["渲染结束"](nsf)
        end
        self.数据 = nsf["到精灵"](nsf)
        临时函数2["进阶网格"]["置数据"](临时函数2["进阶网格"], 角色信息["宝宝列表"][召唤兽合宠["选中bb2"]])
      else
        临时函数2["进阶网格"]["置数据"](临时函数2["进阶网格"], {})
      end
    end
  end
end
local 合成按钮 = 召唤兽合宠["创建我的按钮"](召唤兽合宠, __res:getPNGCC(3, 1075, 257, 107, 107), "合成按钮", 308, 199)
function 合成按钮:左键弹起(x, y, msg)
  if 召唤兽合宠["选中bb1"] and 召唤兽合宠["选中bb2"] then
    发送数据(5009, {
      ["序列"] = 召唤兽合宠["选中bb1"],
      ["序列1"] = 召唤兽合宠["选中bb2"]
    })
  end
end
local 材料网格 = 召唤兽合宠["创建网格"](召唤兽合宠, "材料网格", 335, 386, 55, 55)
function 材料网格:初始化()
  self:创建格子(55, 55, 0, 0, 1, 1)
end
function 材料网格:左键弹起(x, y, a, b, msg)
end
function 材料网格:置物品(数据)
  local lssj = __材料格子["创建"]()
  lssj["置物品"](lssj, 数据)
  材料网格["子控件"][1]["置精灵"](材料网格["子控件"][1], lssj)
end
