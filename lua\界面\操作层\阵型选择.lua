--[[
LastEditTime: 2024-09-27 03:19:24
--]]
local 阵型选择 = 窗口层:创建窗口("阵型选择", 0, 0, 490, 350)
local 阵型位置={}
  阵型位置.鸟翔阵={
		[1]={x=283,y=165},
		[2]={x=349,y=172},
		[3]={x=288,y=208},
		[4]={x=349,y=133},
		[5]={x=218,y=205},
		说明="全速型阵法，所有队员的速度大幅度提高。",
	}
	阵型位置.雷绝阵={
		[1]={x=346,y=222},
		[3]={x=353,y=174},
		[2]={x=285,y=209},
		[5]={x=324,y=156},
		[4]={x=253,y=194},
		说明="攻击型阵法，阵后三人固定攻击效果提升，阵前两人攻击效果提升，全队携带的召唤兽攻击效果提升。",
	}


	阵型位置.鹰啸阵={
		[4]={x=319,y=191},
		[5]={x=364,y=239},
		[3]={x=353,y=176},
		[2]={x=284,y=208},
		[1]={x=339,y=216},
		说明="攻击型阵法，两翼速度提高，阵前阵尾攻击效果提高，阵中物理防御效果提高。",
	}
	阵型位置.蛇蟠阵={
		[1]={x=320,y=197},
		[2]={x=287,y=212},
		[3]={x=350,y=184},
		[4]={x=251,y=186},
		[5]={x=353,y=227},
		说明="灵力型阵法，阵中及两侧抵御敌人法术的几率增加，阵前及阵尾攻击效果提高。",
	}
	阵型位置.龙飞阵={
		[1]={x=297,y=203},
		[2]={x=328,y=227},
		[3]={x=339,y=138},
		[4]={x=226,y=202},
		[5]={x=266,y=181},
		说明="全能型阵法，队长法术防御效果提高，阵尾物理防御效果提高，阵前中间队员攻击效果提高，左翼速度提高，右翼法术对敌伤害提高、速度降低。",
	}
	阵型位置.云垂阵={
		[1]={x=345,y=215},
		[2]={x=307,y=152},
		[3]={x=242,y=190},
		[4]={x=344,y=129},
		[5]={x=199,y=211},
		说明="防御型阵法，两翼队员速度提高，右肋防御效果提高，左肋攻击效果大幅度提高，阵尾防御效果大幅度提高，速度大幅度降低。",
	}
	阵型位置.风扬阵={
		[1]={x=319,y=192},
		[2]={x=353,y=174},
		[3]={x=285,y=209},
		[4]={x=324,y=156},
		[5]={x=253,y=194},
		说明="攻速型阵法，两翼速度效果提高，其余位置攻击效果提高。",
	}
	阵型位置.普通={
		[1]={x=277,y=165},
		[3]={x=244,y=183},
		[2]={x=309,y=150},
		[5]={x=211,y=202},
		[4]={x=342,y=136},
		说明="普通的一字阵形，没有特别的站位效果。",
	}
	阵型位置.天覆阵={
		[1]={x=299,y=188},
		[3]={x=244,y=183},
		[2]={x=309,y=150},
		[5]={x=211,y=202},
		[4]={x=342,y=136},
		说明="攻击型阵法，所有队员物理、法术攻击效果提高，速度下降。",
	}
	阵型位置.虎翼阵={
		[1]={x=346,y=209},
		[2]={x=349,y=172},
		[3]={x=288,y=208},
		[4]={x=349,y=133},
		[5]={x=217,y=205},
		说明="攻击型阵法，两翼攻击效果提高，中间防御效果提高，阵尾攻击效果大幅度提高。",
	}
	阵型位置.地载阵={
		[1]={x=319,y=191},
		[2]={x=293,y=166},
		[3]={x=353,y=176},
		[4]={x=284,y=208},
		[5]={x=339,y=216},
		说明="防御型阵法，阵中及两侧物理、法术防御效果提高，阵前攻击效果提高，阵后速度提高。",
	}



function 阵型选择:初始化()
        self:置精灵(置窗口背景("阵型选择",0,0,490,350))
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.人物图标=__res:取资源动画('dlzy',0x0E43E0DF,"精灵")
        self.底图背景=__res:取资源动画('dlzy',0x57AD4C4E,"精灵")
        self.字体图标={}
        for i=1,5 do
            self.字体图标[i]=文本字体:置颜色(255,255,255,255):取描边精灵(i)
        end
        -- [15] = 资源:载入('登陆资源.wdf',"网易WDF动画",0x0E43E0DF),
        -- [16] = 资源:载入('登陆资源.wdf',"网易WDF动画",0x57AD4C4E),
        self.可初始化=true
        if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-30, 5)
        else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-21, 5)
        end
end
function 阵型选择:显示(x,y)
  --
       self.底图背景:显示(x+15,y+85)
       for i=1,5 do
          self.人物图标:显示(x+阵型位置[self.状态][i].x,y+阵型位置[self.状态][i].y)--- 80 90
          self.字体图标[i]:显示(x+阵型位置[self.状态][i].x+18,y+阵型位置[self.状态][i].y-10)
       end


end



function 阵型选择:打开(内容)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
        return
  end
  self.状态="普通"
  self.阵型=内容
  self:刷新显示()
end


function 阵型选择:刷新显示()
        self[self.状态]:置选中(true)
        self.介绍文本:置文字(文本字体):清空()
        local _,h = 0,0
        if self.阵型[self.状态] then
          _,h =self.介绍文本:置文本("#G"..阵型位置[self.状态].说明)
        else
          _,h =self.介绍文本:置文本("#G"..阵型位置[self.状态].说明.."#R(你还不会这个阵法)")
        end
        self.介绍文本:置高度(h)


end



local 介绍文本 = 阵型选择:创建文本("介绍文本", 260, 35, 210, 0)

local 阵法名称={"普通","风扬阵","虎翼阵","天覆阵","云垂阵","鸟翔阵","地载阵","龙飞阵","蛇蟠阵","鹰啸阵","雷绝阵"}
local xx=0
local yy=0
for i, v in ipairs(阵法名称) do
      local 临时按钮=阵型选择:创建红色单选按钮(v,v,15+xx*80,35+yy*30,74,22) 
      function 临时按钮:左键弹起(x,y)
          if 阵型选择.状态~=v then
              阵型选择.状态=v
              阵型选择:刷新显示()
          end
      end


      xx=xx+1
      if xx>=3 then
        xx=0
        yy=yy+1
      end
end



local 选定阵型=阵型选择:创建红色单选按钮("选定阵型","选定阵型",80,320,74,22) 



 function 选定阵型:左键弹起(x,y)
      请求服务(4009,{名称=阵型选择.状态})
end






local 关闭 = 阵型选择:创建关闭按钮("关闭")
function 关闭:左键弹起(x,y)
    阵型选择:置可见(false)
end