--[[
LastEditTime: 2025-03-19 03:13:15
--]]
--[[
LastEditTime: 2024-10-30 17:16:43
--]]


function 加载层:初始化()
    self:置坐标(0,0)
    self:置宽高(引擎.宽度,引擎.高度)
    self.背景= __res:取资源动画('pic',"jzym.png","图片")
    self:创建纹理精灵(function()
        self.背景:显示((引擎.宽度-self.背景.宽度)//2,(引擎.高度-self.背景.高度)//2)
        __res:取资源动画("ui",0x01000158,"图像"):显示(引擎.宽度2-110,引擎.高度2-85)
    end)
    self.精灵位置=30
end




local 进度 = 加载层:创建进度("进度", 284, 343, 232,17)
function 进度:初始化()  
      self:置坐标(引擎.宽度2-116,引擎.高度2+43)
      self:置精灵(取横排图像(__res:取资源动画('pic',"jzym.png","图片"):复制区域(284,343,27,17),232,5)) 
end



function 加载层:更新(dt)
      if self.进度.位置<100 then
          self.精灵位置=self.精灵位置+2
          self.进度:置位置(self.精灵位置/232*100)
      else
            __UI界面.登录层:置可见(true)
            __UI界面.战斗层:置可见(true)
            __UI界面.窗口层:置可见(true)
            _tp:播放音乐(1070)
            __Http:效验版本号()
            self:置可见(false)
      end
end



