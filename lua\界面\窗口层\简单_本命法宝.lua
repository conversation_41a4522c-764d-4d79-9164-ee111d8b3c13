


local 简单_本命法宝 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "简单_本命法宝", 138 + abbr.py.x, 17 + abbr.py.y, 695, 496)
function 简单_本命法宝:初始化()
  local nsf = require("SDL.图像")(695, 500)
  if nsf["渲染开始"](nsf) then
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

  
function 简单_本命法宝:技能效果(名称,等级)
    if 名称 == "凝海珠" then
    return "携带凝海珠的龙宫弟子\n在使用龙卷雨击时，将有"..(等级).."%的几率\n触发法术连击"
    elseif  名称 == "万鬼木鱼" then
    return "携带万鬼木鱼的神木林弟子\n在使用落叶萧萧时，将有"..(等级).."%的几率\n使受击单位不受分灵影响"
    elseif  名称 == "烈焰杖" then
    return "携带烈焰杖的魔王寨弟子\n在使用飞砂走石时，将有额外增加100~"..(100+等级).."%的波动效果"
    elseif  名称 == "无尘雪" then
    return "携带无尘雪的凌波城弟子\n在使用翻江搅海时，将有"..(等级).."%的几率\n免除战意消耗"
    elseif  名称 == "沁香花藤" then
    return "携带沁香花藤的女儿村弟子\n在进入战斗的时候，将有"..(等级).."%的几率\n直接增加1000点速度"
    elseif  名称 == "天师符" then
    return "携带天师符的方寸山弟子\n在使用失心符时，将有"..(等级).."%的几率\n对随机目标再次施放"
    elseif  名称 == "弥音禅钟" then
    return "携带弥音禅钟的化生寺弟子\n在使用推气过宫时，将有"..(等级).."%的几率\n对目标附带金刚护法效果"
    elseif  名称 == "万兽圣令" then
    return "携带万兽圣令的狮驼岭弟子\n在使用鹰击时，将有"..(等级).."%的几率\n不需要进行休息"
    elseif  名称 == "鸣鸿刀" then
    return "携带鸣鸿刀的大唐官府弟子\n在使用横扫千军时，将有"..(等级).."%的几率\n额外增加一次横扫次数"
    elseif  名称 == "灵明仙桃" then
    return "携带灵明仙桃的天宫弟子\n在使用雷霆万钧时，将有"..(等级).."%的几率\n额外增加"..(等级).."%的法伤效果"
    elseif  名称 == "祭魄印" then
    return "携带祭魄印的阴曹地府弟子\n在使用幽冥鬼眼时，将有"..(等级).."%的几率\n额外增加目标15%的输出效果"
    elseif  名称 == "琼华玉露" then
    return "携带琼华玉露的普陀山弟子\n在使用灵动九天时，将有"..(等级).."%的几率\n额外增加目标20%的防御与速度"
    elseif  名称 == "崆峒印" then
    return "携带崆峒印的五庄观弟子\n在使用受到攻击时，将有"..(等级).."%的几率\n将伤害转化为治疗效果"
    elseif  名称 == "炼妖壶" then
    return "携带炼妖壶的无底洞弟子\n在使用明光宝烛时，将额外增加\n"..(等级).."%的防御效果"
    elseif  名称 == "迷情镜" then
    return "携带迷情镜的盘丝洞弟子\n在使用含情脉脉时，将额外增加\n"..(等级).."%的封印命中几率"
    else
    return "无效果"
    end
end

local data 
local bmzzbh 
function 简单_本命法宝:刷新(数据)
    data=数据.本命数据
  
    if 数据.助战id then
      bmzzbh  =数据.助战id
    end
  self:重置数据()
end
function 简单_本命法宝:打开(数据)
    -- self:置可见(true)

    data=数据.本命数据
  if 数据.助战id then
    bmzzbh  =数据.助战id
  end
  简单_本命法宝:置可见(true)
  self:重置数据()
end

function 简单_本命法宝:显示(x, y)
    if self.数据 then 
    self.数据["显示"](self.数据, x, y)
    end
end


local 技能文本 = 简单_本命法宝["创建文本"](简单_本命法宝, "技能文本", 468+11-180, 106, 219, 270)
function 技能文本:初始化()
end



function 简单_本命法宝:取技能图标(名称)
    local 图标数据={} ---1小 2 大
    if 名称=="祭魄印" then 
        图标数据[1]=0xABC164A0
        图标数据[2]=0xABC164A1
    elseif 名称=="崆峒印" then--蓝
        图标数据[1]=0xABC164A2
        图标数据[2]=0xABC164A3
    elseif 名称=="昆仑镜" then
        图标数据[1]=0xABC164A4
        图标数据[2]=0xABC164A5
    elseif 名称=="炼妖壶" then--红
        图标数据[1]=0xABC164A6
        图标数据[2]=0xABC164A7
    elseif 名称=="烈焰杖" then --白
        图标数据[1]=0xABC164A8
        图标数据[2]=0xABC164A9
    elseif 名称=="灵明仙桃" then--蓝
        图标数据[1]=0xABC16A10
        图标数据[2]=0xABC16A11
    elseif 名称=="弥音禅钟" then--青
        图标数据[1]=0xABC16A12
        图标数据[2]=0xABC16A13
    elseif 名称=="鸣鸿刀" then--红
        图标数据[1]=0xABC16A14
        图标数据[2]=0xABC16A15
    elseif 名称=="凝海珠" then--白
        图标数据[1]=0xABC16A16
        图标数据[2]=0xABC16A17
    elseif 名称=="沁香花藤" then--蓝
        图标数据[1]=0xABC16A18
        图标数据[2]=0xABC16A19
    elseif 名称=="琼花玉露" then--青
        图标数据[1]=0xABC16A20
        图标数据[2]=0xABC16A21
    elseif 名称=="天师符" then--红
        图标数据[1]=0xABC16A22
        图标数据[2]=0xABC16A23
    elseif 名称=="万鬼木鱼" then--白
        图标数据[1]=0xABC16A24
        图标数据[2]=0xABC16A25
    elseif 名称=="万兽圣令" then--蓝
        图标数据[1]=0xABC16A26
        图标数据[2]=0xABC16A27
    elseif 名称=="无尘雪" then--青
        图标数据[1]=0xABC16A28
        图标数据[2]=0xABC16A29
    else 
        图标数据[1]=0xABC16A28
        图标数据[2]=0xABC16A29
    end
    return 图标数据
  end

function 简单_本命法宝:重置数据()
  local nsf = require("SDL.图像")(695, 500)
  if nsf["渲染开始"](nsf) then
    
    xiao置窗口背景("本命法宝", 0, 12, 650, 500, true)["显示"](xiao置窗口背景("本命法宝", 0, 12, 650, 500, true), 0, 0)
    __res:getPNGCC(1, 682, 386, 219, 126, true):拉伸(380, 75):显示(275, 90)
    __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", 0xABC16A32)):显示(30,35+8+18) 
    __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", 0xABC16A33)):显示(30,35+8+18) 
    __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", 0xABC16A34)):显示(23,35+8+18) 
    -- __res["取图像"](__res, "shape/jn/ABC16A32.tcp"):显示(30,35+8+18)--圆圈背景
    -- __res["取图像"](__res, "shape/jn/ABC16A33.tcp"):显示(30,35+8+18)--圆圈边框
    -- __res["取图像"](__res, "shape/jn/ABC16A34.tcp"):显示(23,35+8+18)--四方边框
    字体16["置颜色"](字体16, __取颜色("白色"))
    简单_本命法宝["技能文本"]["清空"](简单_本命法宝["技能文本"])
    简单_本命法宝["技能文本"]["置文本"](简单_本命法宝["技能文本"], "#W" ..  self:技能效果(data.名称,data.等级))
  字体25["置颜色"](字体25, __取颜色("黄色"))
  字体25["取图像"](字体25, "门派特性")["显示"](字体25["取图像"](字体25,"门派特性"), 135+130, 110-65)
  字体25["取图像"](字体25, "两仪属性")["显示"](字体25["取图像"](字体25,"两仪属性"), 135+130, 240-65)
  字体25["取图像"](字体25, "四象属性")["显示"](字体25["取图像"](字体25,"四象属性"), 135+130, 400-65)
  字体16["置颜色"](字体16, __取颜色("蓝色"))
  字体16["取图像"](字体16, "当前操作需要消耗法宝精华50点")["显示"](字体16["取图像"](字体16,"当前操作需要消耗法宝精华50点"), 135+130, 205)
  字体16["置颜色"](字体16, __取颜色("黄色"))
  字体16["取图像"](字体16, "两仪增加：")["显示"](字体16["取图像"](字体16,"两仪增加："), 135+130, 235)
  字体16["取图像"](字体16, "两仪增加：")["显示"](字体16["取图像"](字体16,"两仪增加："), 135+130, 265)
  字体16["取图像"](字体16, "两仪增加：")["显示"](字体16["取图像"](字体16,"两仪增加："), 135+130, 295)
 
  
  字体16["取图像"](字体16, "四象增加：")["显示"](字体16["取图像"](字体16,"四象增加："), 135+130, 215+160)
  字体16["取图像"](字体16, "四象增加：")["显示"](字体16["取图像"](字体16,"四象增加："), 135+130, 245+160)
  字体16["取图像"](字体16, "四象增加：")["显示"](字体16["取图像"](字体16,"四象增加："), 135+130, 275+160)
  字体16["取图像"](字体16, "四象增加：")["显示"](字体16["取图像"](字体16,"四象增加："), 135+130, 305+160)
 
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(23,235+8+18)
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(23+80,235+8+18)
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(23+160,235+8+18)
  if data.名称~="无" then 
    local lssj = self:取技能图标(data.名称)
    __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", lssj[2])):显示(66,80) 
    __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", lssj[1])):显示(29,241+18) 
    __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", lssj[1])):显示(29+80,241+18) 
    __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", lssj[1])):显示(29+160,241+18) 
  end 
  __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", 0xABC16A30)):显示(50,235+90+18) 
  __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", 0xABC16A31)):显示(50,300+90+18) 

  __res:getPNGCC(1, 400, 109, 185, 17)["显示"](__res:getPNGCC(1, 400, 109, 185, 17), 350, 236)
  __res:getPNGCC(1, 400, 109, 185, 17)["显示"](__res:getPNGCC(1, 400, 109, 185, 17), 350, 266)
  __res:getPNGCC(1, 400, 109, 185, 17)["显示"](__res:getPNGCC(1, 400, 109, 185, 17), 350, 296)
  
  __res:getPNGCC(1, 400, 109, 185, 17)["显示"](__res:getPNGCC(1, 400, 109, 185, 17), 350,138+ 236)
  __res:getPNGCC(1, 400, 109, 185, 17)["显示"](__res:getPNGCC(1, 400, 109, 185, 17), 350,138+ 266)
  __res:getPNGCC(1, 400, 109, 185, 17)["显示"](__res:getPNGCC(1, 400, 109, 185, 17), 350,138+ 296)
  __res:getPNGCC(1, 400, 109, 185, 17)["显示"](__res:getPNGCC(1, 400, 109, 185, 17), 350,138+ 326)
  
  if data.两仪[1]~=nil then
  简单_本命法宝.技能["经验条"]["置位置"](简单_本命法宝.技能["经验条"], math.floor(data.两仪[1].数值 / 100 * 100))
  简单_本命法宝.技能["经验条"]["置可见"](简单_本命法宝.技能["经验条"], true)
  end
  if data.两仪[2]~=nil then
  简单_本命法宝.技能["经验条2"]["置位置"](简单_本命法宝.技能["经验条2"], math.floor(data.两仪[2].数值 / 100 * 100))
  简单_本命法宝.技能["经验条2"]["置可见"](简单_本命法宝.技能["经验条2"], true)
  end
  if data.两仪[3]~=nil then
  简单_本命法宝.技能["经验条3"]["置位置"](简单_本命法宝.技能["经验条3"], math.floor(data.两仪[3].数值 / 100 * 100))
  简单_本命法宝.技能["经验条3"]["置可见"](简单_本命法宝.技能["经验条3"], true)
  end
  if data.四象[1]~=nil then
  简单_本命法宝.技能["经验条4"]["置位置"](简单_本命法宝.技能["经验条4"], math.floor(data.四象[1].数值 / 100 * 100))
  简单_本命法宝.技能["经验条4"]["置可见"](简单_本命法宝.技能["经验条4"], true)
  end
  if data.四象[2]~=nil then
  简单_本命法宝.技能["经验条5"]["置位置"](简单_本命法宝.技能["经验条5"], math.floor(data.四象[2].数值 / 100 * 100))
  简单_本命法宝.技能["经验条5"]["置可见"](简单_本命法宝.技能["经验条5"], true)
  end
  if data.四象[3]~=nil then
  简单_本命法宝.技能["经验条6"]["置位置"](简单_本命法宝.技能["经验条6"], math.floor(data.四象[3].数值 / 100 * 100))
  简单_本命法宝.技能["经验条6"]["置可见"](简单_本命法宝.技能["经验条6"], true)
  end
  if data.四象[4]~=nil then
  简单_本命法宝.技能["经验条7"]["置位置"](简单_本命法宝.技能["经验条7"], math.floor(data.四象[4].数值 / 100 * 100))
  简单_本命法宝.技能["经验条7"]["置可见"](简单_本命法宝.技能["经验条7"], true)
  end

    nsf["渲染结束"](nsf)
  end
  self.数据 = nsf["到精灵"](nsf)
end 


local 技能 = 简单_本命法宝:创建控件("技能", 0, 0, 630, 600)
function 技能:初始化()
  local nsf = require("SDL.图像")(630, 600)
  if nsf:渲染开始() then
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
end
local 经验条 = 技能["创建进度"](技能, "经验条", 350, 236, 183, 16)
function 经验条:初始化()
self:置精灵(__res:getPNGCC(1, 587, 108, 183, 16)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 16))))
end
function 经验条:显示(x, y)
    if data.两仪[1]~=nil then
        if data.两仪[1].阴阳=="阴" then
            字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + %s    %s", data.两仪[1].属性, data.两仪[1].数值,data.两仪[1].阴阳))
        else 
            字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + 百分%s    %s", data.两仪[1].属性, math.floor(((data.两仪[1].数值*100)/3000)),data.两仪[1].阴阳))
        end 
    end
end
local 经验条2 = 技能["创建进度"](技能, "经验条2", 350, 266, 183, 16)
function 经验条2:初始化()
self:置精灵(__res:getPNGCC(1, 587, 108, 183, 16)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 16))))
end
function 经验条2:显示(x, y)
    if data.两仪[2]~=nil then
        if data.两仪[2].阴阳=="阴" then
            字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + %s    %s", data.两仪[2].属性, data.两仪[2].数值,data.两仪[2].阴阳))
        else 
            字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + 百分%s    %s", data.两仪[2].属性, math.floor(((data.两仪[2].数值*100)/3000)),data.两仪[2].阴阳))
        end 
    end
end
local 经验条3 = 技能["创建进度"](技能, "经验条3", 350, 296, 183, 16)
function 经验条3:初始化()
self:置精灵(__res:getPNGCC(1, 587, 108, 183, 16)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 16))))
end
function 经验条3:显示(x, y)
    if data.两仪[3]~=nil then
        if data.两仪[3].阴阳=="阴" then
            字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + %s    %s", data.两仪[3].属性, data.两仪[3].数值,data.两仪[3].阴阳))
        else 
            字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + 百分%s    %s", data.两仪[3].属性, math.floor(((data.两仪[3].数值*100)/3000)),data.两仪[3].阴阳))
        end 
    end
end



local 经验条4 = 技能["创建进度"](技能, "经验条4", 350, 236+138, 183, 16)
function 经验条4:初始化()
self:置精灵(__res:getPNGCC(1, 587, 108, 183, 16)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 16))))
end
function 经验条4:显示(x, y)
    if data.四象[1]~=nil then
        字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + %s", data.四象[1].属性, data.四象[1].数值))
    end
end

local 经验条5 = 技能["创建进度"](技能, "经验条5", 350, 266+138, 183, 16)
function 经验条5:初始化()
self:置精灵(__res:getPNGCC(1, 587, 108, 183, 16)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 16))))
end
function 经验条5:显示(x, y)
    if data.四象[2]~=nil then
        字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + %s", data.四象[2].属性, data.四象[2].数值))
    end
end

local 经验条6 = 技能["创建进度"](技能, "经验条6", 350, 296+138, 183, 16)
function 经验条6:初始化()
self:置精灵(__res:getPNGCC(1, 587, 108, 183, 16)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 16))))
end
function 经验条6:显示(x, y)
    if data.四象[3]~=nil then
        字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + %s", data.四象[3].属性, data.四象[3].数值))
    end
end
local 经验条7 = 技能["创建进度"](技能, "经验条7", 350, 326+138, 183, 16)
function 经验条7:初始化()
self:置精灵(__res:getPNGCC(1, 587, 108, 183, 16)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 16))))
end
function 经验条7:显示(x, y)
    if data.四象[4]~=nil then
        字体18:置颜色(__取颜色("黄色"))
        字体18["显示"](字体18, x+30 , y , string.format("%s + %s", data.四象[4].属性, data.四象[4].数值))
    end
end

local 关闭 = 简单_本命法宝["创建我的按钮"](简单_本命法宝, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 620, 0)
function 关闭:左键弹起(x, y, msg)
  简单_本命法宝["置可见"](简单_本命法宝, false)
end




for i, v in ipairs({
    {
      name = "升级法宝",
      x =380+66,
      y = 50,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(90, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(90, 20),
      font = "升级法宝"
    },
    {
      name = "修复法宝",
      x =480+66,
      y = 50,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(90, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(90, 20),
      font = "修复法宝"
    },
    {
      name = "选择1",
      x =25,
      y = 325,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(45, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(45, 20),
      font = "选择"
    },
    {
      name = "选择2",
      x =25+80,
      y = 325,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(45, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(45, 20),
      font = "选择"
    },
    {
      name = "选择3",
      x =25+160,
      y = 325,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(45, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(45, 20),
      font = "选择"
    },
    
    {
        name = "两仪属性1",
        x =540,
        y = 240-6,
        tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
        tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
        font = "属性"
      },
      {
          name = "两仪属性2",
          x =540,
          y = 270-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "属性"
        },
      {
            name = "两仪属性3",
            x =540,
            y = 300-6,
            tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
            tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
            font = "属性"
      },
      {
          name = "两仪数值1",
          x =590,
          y = 240-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "数值"
      },
      {
          name = "两仪数值2",
          x =590,
          y = 270-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "数值"
      },
      {
          name = "两仪数值3",
          x =590,
          y = 300-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "数值"
      },


      {
        name = "四象属性1",
        x =540,
        y =138+ 240-6,
        tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
        tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
        font = "属性"
      },
      {
          name = "四象属性2",
          x =540,
          y =138+ 270-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "属性"
        },
      {
            name = "四象属性3",
            x =540,
            y =138+ 300-6,
            tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
            tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
            font = "属性"
      },
      {
            name = "四象属性4",
            x =540,
            y =138+ 330-6,
            tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
            tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
            font = "属性"
      },
      {
          name = "四象数值1",
          x =590,
          y =138+ 240-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "数值"
      },
      {
          name = "四象数值2",
          x =590,
          y =138+ 270-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "数值"
      },
      {
          name = "四象数值3",
          x =590,
          y =138+ 300-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "数值"
      },
      {
          name = "四象数值4",
          x =590,
          y =138+ 330-6,
          tcp =  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          tcp2 = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(45, 22),
          font = "数值"
      },



  }) do
    local 临时函数 = 简单_本命法宝["创建我的按钮"](简单_本命法宝, v.tcp, v.name, v.x, v.y, v.font)
   function  临时函数:左键弹起(x, y)
      if v.name == "升级法宝"  then 
        if bmzzbh then 
            发送数据(193,{序号1=3,助战id=bmzzbh})
        else 
            发送数据(193,{序号1=3})
        end
       elseif   v.name == "修复法宝"  then 
        if bmzzbh then 
            发送数据(193,{序号1=4,助战id=bmzzbh})
        else 
            发送数据(193,{序号1=4})
        end
         elseif   v.name == "两仪属性1"  then 
            if bmzzbh then 
                发送数据(193,{序号1=13,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=13})
            end
         elseif   v.name == "两仪属性2"  then 
            if bmzzbh then 
                发送数据(193,{序号1=15,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=15})
            end
         elseif   v.name == "两仪属性3"  then 
            if bmzzbh then 
                发送数据(193,{序号1=17,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=17})
            end
         elseif   v.name == "两仪数值1"  then 
            if bmzzbh then 
                发送数据(193,{序号1=14,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=14})
            end
         elseif   v.name == "两仪数值2"  then 
            if bmzzbh then 
                发送数据(193,{序号1=16,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=16})
            end
         elseif   v.name == "两仪数值3"  then 
            if bmzzbh then 
                发送数据(193,{序号1=18,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=18})
            end
         elseif   v.name == "四象属性1"  then 
            if bmzzbh then 
                发送数据(193,{序号1=19,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=19})
            end
         elseif   v.name == "四象属性2"  then 
            if bmzzbh then 
                发送数据(193,{序号1=21,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=21})
            end
         elseif   v.name == "四象属性3"  then 
            if bmzzbh then 
                发送数据(193,{序号1=23,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=23})
            end
         elseif   v.name == "四象属性4"  then 
            if bmzzbh then 
                发送数据(193,{序号1=25,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=25})
            end
         elseif   v.name == "四象数值1"  then 
            if bmzzbh then 
                发送数据(193,{序号1=20,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=20})
            end
         elseif   v.name == "四象数值2"  then 
            if bmzzbh then 
                发送数据(193,{序号1=22,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=22})
            end
         elseif   v.name == "四象数值3"  then 
            if bmzzbh then 
                发送数据(193,{序号1=24,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=24})
            end
         elseif   v.name == "四象数值4"  then 
            if bmzzbh then 
                发送数据(193,{序号1=26,助战id=bmzzbh})
            else 
                发送数据(193,{序号1=26})
            end
        elseif   v.name == "选择1"  then 
            if bmzzbh then
                发送数据(193,{序号1=10,助战id=bmzzbh})
            else
                发送数据(193,{序号1=10})
            end
        elseif   v.name == "选择2"  then 
            if bmzzbh then
                发送数据(193,{序号1=11,助战id=bmzzbh})
            else
                发送数据(193,{序号1=11})
            end
        elseif   v.name == "选择3"  then 
            if bmzzbh then
                发送数据(193,{序号1=12,助战id=bmzzbh})
            else
                发送数据(193,{序号1=12})
            end
      end 
    end
  end