
local 坐骑属性 = 窗口层:创建窗口("坐骑属性", 0,0, 370, 480)
local bd = {"体质","魔力","力量","耐力","敏捷"}
local qb = {"成  长","环境度","好感度","饱食度"}

function 坐骑属性:初始化()
  self:创建纹理精灵(function()
      置窗口背景("坐骑属性", 0, 0, 370, 480, true):显示(0, 0)
        取白色背景(0, 0, 180, 160,true):显示(10,35)
        取白色背景(0, 0, 160, 160,true):显示(200,35)
        for i = 1, 7 do
            取输入背景(0, 0, 100, 23):显示(50, 230+(i-1)*25)
        end
        标题字体:置颜色(255,255,255,255):取图像("统筹召唤兽"):显示(260, 208)
        文本字体:置颜色(255,255,255,255)
        文本字体:取图像("名称"):显示(15, 234)
        文本字体:取图像("等级"):显示(15, 259)
        for i, v in ipairs(bd) do
            文本字体:取图像(v):显示(15, 284+(i-1)*25)
        end
        for i, v in ipairs(qb) do
            文本字体:取图像(v):显示(210, 284+(i-1)*25)
            if v=="好感度" or v=="饱食度" then 
                取输入背景(0, 0, 65, 23):显示(255, 281+(i-1)*25)
            else
                取输入背景(0, 0, 100, 23):显示(255, 281+(i-1)*25)
            end 
        end
        文本字体:置颜色(__取颜色("橙色"))
        文本字体:取图像("潜能"):显示(15, 410)   
        文本字体:取图像("获得经验"):显示(210, 384) 
        文本字体:取图像("升级经验"):显示(210, 409) 
        取输入背景(0, 0, 95, 23):显示(270, 381)
        取输入背景(0, 0, 95, 23):显示(270, 406)
        取输入背景(0, 0, 45, 23):显示(50,408)


    end
  )
    self.选中 = nil
    self.模型格子 = __UI模型格子.创建()
    self.可初始化=true
    self.临时加点={体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
    self:置坐标(0, 30)
    if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-27, 2)
    else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-18, 2)
    end
end







function 坐骑属性:更新(dt)
    if self.模型格子 then
        self.模型格子:更新(dt)
    end
    
end
function 坐骑属性:显示(x, y)
      if self.模型格子 then
          self.模型格子:显示(x, y)
      end
      if self.属性背景 then
          self.属性背景:显示(x, y)
      end
      if self.加点属性 then
        self.加点属性:显示(x, y)
      end
end



function 坐骑属性:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.选中 = nil
  self.名称选择:置数据()
  self:显示设置()
end

function 坐骑属性:显示设置()
      
        self.选中潜力=0
        self.属性背景=nil
        self.装备网格:置物品()
        self.统御网格:置数据()
        self.名称输入:清空()
        self.模型格子:清空()
        self.骑乘:重置文字()
        self.祥瑞:置禁止(true)
        self.观看:置禁止(true)
        self.改名:置禁止(true)
        self.重置:置禁止(true)
        self.放生:置禁止(true)
        self.确认加点:置禁止(true)
        self.推荐加点:置禁止(true)
        self.查看资质:置禁止(true)
        self.临时加点={体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
      
        if self.选中 and 角色信息 and 角色信息.坐骑列表[self.选中] then
              self.模型格子:置数据(角色信息.坐骑列表[self.选中], "坐骑", 280,175,角色信息.模型)
              self.名称输入:置文本(角色信息.坐骑列表[self.选中].名称)
              self.装备网格:置物品(角色信息.坐骑列表[self.选中].装备)
              self.统御网格:置数据(角色信息.坐骑列表[self.选中].统御召唤兽)
              self.选中潜力=角色信息.坐骑列表[self.选中].潜力
              if self.名称选择.子控件[self.选中]~=nil then
                  self.名称选择:置选中(self.选中)
              end
              self.祥瑞:置禁止(false)
              self.观看:置禁止(false)
              self.改名:置禁止(false)
              self.重置:置禁止(false)
              self.放生:置禁止(false)
              self.确认加点:置禁止(false)
              self.推荐加点:置禁止(false)
              self.查看资质:置禁止(false)
              if 窗口层.坐骑技能.是否可见 then
                 窗口层.坐骑技能:刷新(角色信息.坐骑列表[self.选中])
              end
        elseif 窗口层.坐骑技能.是否可见 then
              窗口层.坐骑技能:置可见(false)
        end
        self.属性背景 = self:创建纹理精灵(function()
                if self.选中 and 角色信息 and  角色信息.坐骑列表[self.选中] then
                  文本字体:置颜色(0,0,0,255):取图像(角色信息.坐骑列表[self.选中].等级):显示(55, 259)
                      for i, v in ipairs(bd) do
                          文本字体:置颜色(0,0,0,255):取图像(角色信息.坐骑列表[self.选中][v]):显示(55, 284+(i-1)*25)
                      end
                      文本字体:置颜色(0,0,0,255):取图像(角色信息.坐骑列表[self.选中].成长):显示(260, 285)
                      文本字体:置颜色(0,0,0,255):取图像(角色信息.坐骑列表[self.选中].忠诚):显示(260, 360)
                      文本字体:置颜色(0,0,0,255):取图像(角色信息.坐骑列表[self.选中].当前经验.."("..math.floor((角色信息.坐骑列表[self.选中].当前经验/角色信息.坐骑列表[self.选中].最大经验)*100)..")".."%"):显示(280, 385)
                      文本字体:置颜色(0,0,0,255):取图像(角色信息.坐骑列表[self.选中].最大经验):显示(280, 410)
                      文本字体:置颜色(0,0,0,255):取图像("98"):显示(260, 310)
                      文本字体:置颜色(0,0,0,255):取图像("100"):显示(260, 335)
                end
            end,1
          )

        self:属性显示()
        if __手机 then
            self.重置:置文字(40,22,"加点")
        else
            self.重置:置文字(40,22,"重置")
        end

end

function 坐骑属性:属性显示()
            for i=1,5 do
                  self[bd[i].."加"]:置禁止(true)
                  self[bd[i].."减"]:置禁止(true)
                  if self.选中 and  角色信息 and 角色信息.坐骑列表[self.选中] and self.选中潜力>0 then
                      self[bd[i].."加"]:置禁止(false)
                  end
                  if self.选中 and 角色信息 and 角色信息.坐骑列表[self.选中] and self.临时加点[bd[i]]>0 then
                      self[bd[i].."减"]:置禁止(false)
                  end
            end
           
              self.加点属性 = self:创建纹理精灵(function()
                    if self.选中 and 角色信息 and 角色信息.坐骑列表[self.选中] then
                      文本字体:置颜色(__取颜色("红色"))
                      for i, v in ipairs(bd) do
                          if self.临时加点[v]>0 then
                              文本字体:取图像("+"..self.临时加点[v]):显示(55+(90-文本字体:取宽度("+"..self.临时加点[v])), 256+i*25)
                          end
                      end
                      文本字体:置颜色(0,0,0,255):取图像(self.选中潜力):显示(55, 413)
                  end
              end,1
            )


end        


local 装备网格 = 坐骑属性:创建网格("装备网格", 210, 225, 50, 50)
  function 装备网格:初始化()
       self:创建格子(50, 50, 0,5, 1, 1)

  end
  function 装备网格:左键弹起(x, y, a, b, msg)
          for i, v in ipairs(self.子控件) do
              if v._spr and v._spr.确定 then
                  v._spr.确定=nil
              end
          end
          if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
              self.子控件[a]._spr.确定 =true
              if __手机 then
                 __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,装备网格,"卸下",a)
              else
                  self:卸下(a)
              end
          end
  end


  function 装备网格:卸下(编号)
          if 编号 and 编号~=0 then
              请求服务(3756,{编号=编号 })
          end
  end



    function 装备网格:置物品(数据)
          for i = 1, #self.子控件 do
              local lssj = __物品格子:创建()
              lssj:置物品(nil,50,50, nil,true)
              if 数据 and 数据[i] then
                lssj:置物品(数据[i], 50,50, nil,true)
              end
              self.子控件[i]:置精灵(lssj)
          end
    end


  local 统御网格 = 坐骑属性:创建网格("统御网格", 268, 230, 90, 45)
  function 统御网格:初始化()
          self:创建格子(45, 45, 0,2, 2, 2)
  end

  function 统御网格:右键弹起(x, y, a)
          if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] and 角色信息.坐骑列表[坐骑属性.选中].统御召唤兽  and 角色信息.坐骑列表[坐骑属性.选中].统御召唤兽[a] then
                  窗口层.召唤兽查看:打开(角色信息.宝宝列表[角色信息.坐骑列表[坐骑属性.选中].统御召唤兽[a]])
          end
  end

  function 统御网格:获得鼠标(x, y, a)
          if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
                local 坐骑  = 角色信息.坐骑列表[坐骑属性.选中]
                if 坐骑.统御召唤兽 and  坐骑.统御召唤兽[a] then
                      __UI弹出.自定义:打开(x+25,y+25,"#Y左键点击结束统御,右键点击查看宝宝")
                else
                    __UI弹出.自定义:打开(x+25,y+25,"#Y左键点击统御宝宝")
                end
          end
  end

  function 统御网格:左键弹起(x, y, a)
          if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
              local 坐骑  = 角色信息.坐骑列表[坐骑属性.选中]
              if 坐骑.统御召唤兽 and  坐骑.统御召唤兽[a] then
                          if __手机 then
                                  local 事件 =function (z)
                                        if z ==1 then
                                            窗口层.召唤兽查看:打开(角色信息.宝宝列表[坐骑.统御召唤兽[a]])
                                        else
                                            请求服务(91,{序列=坐骑属性.选中,编号=a})
                                        end
                                  end
                                  __UI弹出.临时按钮:打开({"查看属性","结束统御"},事件,x,y)
                            else
                                  请求服务(91,{序列=坐骑属性.选中,编号=a})
                            end
              elseif 角色信息.宝宝列表 and #角色信息.宝宝列表>0  then
                        local 列表={}
                        for k, z in ipairs(角色信息.宝宝列表) do
                            列表[k] = self:创建纹理精灵(function()
                                  __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
                                  local lssj = 取头像(z.模型)
                                  __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
                                  文本字体:置颜色(0,0,0,255)
                                  文本字体:取图像(z.名称):显示(40,4)
                                  文本字体:取图像(z.等级.."级"):显示(40,20) 
                                end,1,110,37
                              )    
                        end     
                          local 事件 =function (z)
                              请求服务(90,{序列=坐骑属性.选中,召唤兽编号=z})
                          end
                          local xx,yy=self:取坐标()
                        __UI弹出.弹出列表:打开(列表,取白色背景(0,0,130,#列表*46),事件,xx-40,yy+45)
              else
                  __UI弹出.提示框:打开("#Y你没有可以统御的召唤兽!")
              end

          end
end




  function 统御网格:置数据(数据)
      for i = 1, #self.子控件 do
            self.子控件[i]:创建纹理精灵(function()
                  __res:取资源动画("dlzy", 0x363AAF1B,"图像"):拉伸(45, 45):显示(0,0)
                  if 数据 and 数据[i] and 角色信息.宝宝列表 and 角色信息.宝宝列表[数据[i]] then
                      local lssj = 取头像(角色信息.宝宝列表[数据[i]].模型)
                      __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(37, 37):显示(4,4)
                  else
                      __res:取资源动画("dlzy",0xCEC838D7,"图像"):拉伸(37, 37):显示(4,4)
                  end
            end
          )
      end
end


local 名称输入 = 坐骑属性:创建文本输入("名称输入", 55, 235, 90, 18)
function 名称输入:初始化()
  self:取光标精灵()
  self:置限制字数(16)
  self:置颜色(0,0,0,255)
end



local 名称选择 = 坐骑属性:创建列表("名称选择", 20, 45, 160, 150)
function 名称选择:初始化()
    self.行高度= 37
    self.行间距 = 3
end
local 滑块=坐骑属性:创建竖向滑块("名称滑块",179,36,10,160,true)
名称选择:绑定滑块(滑块.滑块)
--local 滑块=名称选择:创建竖向滑块("名称滑块",180,35,10,190)

function 名称选择:置数据()
      self:清空()
      for i, v in ipairs(角色信息.坐骑列表) do

          self:添加():创建纹理精灵(function()
            __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
            local lssj = 取头像(v.模型)
            __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
            文本字体:置颜色(0,0,0,255)
            文本字体:取图像(v.名称):显示(40,4)
            文本字体:取图像(v.等级.."级"):显示(40,20)
          end)
      end
  
end

function 名称选择:单独重置(编号)
      if 角色信息.坐骑列表[编号] and self.子控件[编号] then
          self.子控件[编号]:创建纹理精灵(function()
            __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
            local lssj = 取头像(角色信息.坐骑列表[编号].模型)
            __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
            文本字体:置颜色(0,0,0,255)
            文本字体:取图像(角色信息.坐骑列表[编号].名称):显示(40,4)
            文本字体:取图像(角色信息.坐骑列表[编号].等级.."级"):显示(40,20)
          end
        )
      end

end
  
function 名称选择:左键弹起(x, y, i)
    if 角色信息.坐骑列表[i] then
      坐骑属性.选中 = i
      坐骑属性:显示设置()
        if 窗口层.坐骑技能.是否可见 then
            窗口层.坐骑技能:刷新(角色信息.坐骑列表[i],i)
        end
    end
end

local 骑乘 = 坐骑属性:创建红色按钮("骑乘", "骑乘", 5, 205,60,22) 
local 观看 = 坐骑属性:创建红色按钮("观看", "观看", 70, 205,60,22) 
local 祥瑞 = 坐骑属性:创建红色按钮("祥瑞", "祥瑞", 135, 205,60,22) 

function 祥瑞:左键弹起(x, y)
        if not  角色信息.坐骑列表[坐骑属性.选中] then
          __UI弹出.提示框:打开("#请选中一个坐骑！")
          return
        end
        if 角色信息.坐骑列表[坐骑属性.选中] and 角色信息.坐骑列表[坐骑属性.选中].祥瑞 then
            请求服务(116)
        else
            __UI弹出.提示框:打开("#该坐骑无法设置飞行！")
        end
end 
function 骑乘:重置文字()
      if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then 
          if  角色信息.坐骑~=nil  and ((角色信息.坐骑.认证码 and 角色信息.坐骑.认证码==角色信息.坐骑列表[坐骑属性.选中].认证码) or ( 角色信息.坐骑[1]~=nil and 角色信息.坐骑列表[坐骑属性.选中].认证码==角色信息.坐骑[1].认证码)) then
              self:置文字(60,22,"下骑")   
          else
              self:置文字(60,22,"乘骑")
          end
          self:置禁止(false)
      else    
          self:置文字(60,22,"乘骑")
          self:置禁止(true)
      end
end
function 骑乘:左键弹起(x, y)
    if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
        if  角色信息.坐骑~=nil  and ((角色信息.坐骑.认证码 and 角色信息.坐骑.认证码==角色信息.坐骑列表[坐骑属性.选中].认证码) or (角色信息.坐骑[1]~=nil and 角色信息.坐骑列表[坐骑属性.选中].认证码==角色信息.坐骑[1].认证码)) then
            请求服务(27,{序列=0})
        else
            请求服务(26,{序列=坐骑属性.选中})
        end
  end
end
local 改名 = 坐骑属性:创建红色按钮("改名", "改名", 155, 230,40,22) 
function 改名:左键弹起(x, y)
      if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
          if 坐骑属性.名称输入:取文本() then
              请求服务(89,{序列=坐骑属性.选中,名称=坐骑属性.名称输入:取文本()})
              角色信息.坐骑列表[坐骑属性.选中].名称 = 坐骑属性.名称输入:取文本()
              __UI弹出.提示框:打开("改名成功!")
          else
            __UI弹出.提示框:打开("输入名称不能是空")
          end
      end

end
local 重置 = 坐骑属性:创建红色按钮("重置", "重置", 140, 444,40,22) 
function 重置:左键弹起(x, y)
      if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
          if __手机 then
              窗口层.属性加点:打开(角色信息.坐骑列表[坐骑属性.选中],"坐骑")
          else
              坐骑属性:显示设置()
          end
      end

end
local 驯养 = 坐骑属性:创建红色按钮("驯养", "驯养", 230, 444,40,22) 
function 驯养:左键弹起(x, y)
  if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
      窗口层.对话栏:打开("天兵","坐骑驯养","请选择您要进行的操作,单次驯养消耗10W经验、10W银子",{"驯养一次","驯养十次","驯养五十次","驯养百次","驯养千次","算了算了"})
  end
end
local 放生 = 坐骑属性:创建红色按钮("放生", "放生", 15, 444,40,22) 
function 放生:左键弹起(x, y)
      if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
         窗口层.对话栏:打开("天兵","坐骑放生","您确认要放生该坐骑么,一旦放生无法找回",{"坐骑放生","算了算了"})
     
      end
end
local 喂养 = 坐骑属性:创建红色按钮("喂养", "喂养", 322, 332,40,22) 
function 喂养:左键弹起(x, y)
     if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
        请求服务(97,{编号=坐骑属性.选中})
    end
end
local 洗点 = 坐骑属性:创建红色按钮("洗点", "洗点", 322, 358,40,22) 
function 洗点:左键弹起(x, y)
  if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
      窗口层.对话栏:打开("天兵","坐骑洗点","您确认要为该坐骑进行洗点操作么,每次洗点将会扣除10W银子",{"坐骑洗点","算了算了"})
  end
end


local 确认加点 = 坐骑属性:创建红色按钮("确认加点", "确认加点", 100, 410,74,22) --红色  (215, 385)
function 确认加点:左键弹起(x, y)
        if  坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
              请求服务(93,{编号=坐骑属性.选中,加点=坐骑属性.临时加点})
        end
end

local 推荐加点 = 坐骑属性:创建红色按钮("推荐加点", "推荐加点", 60, 444,74,22) --红色
function 推荐加点:左键弹起(x, y)
        -- if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
        --       local 是否推荐 =true
        --       local 记录加点 = {}
        --       if  角色信息.坐骑列表[坐骑属性.选中].灵性==nil then 角色信息.坐骑列表[坐骑属性.选中].灵性=0 end
        --       local 已加属性 = 角色信息.坐骑列表[坐骑属性.选中].等级*5+角色信息.坐骑列表[坐骑属性.选中].灵性*2-坐骑属性.选中潜力
        --       for i=1,5 do
        --         记录加点[bd[i]]=0
        --         if 角色信息.坐骑列表[坐骑属性.选中].加点记录[bd[i]]~=0 and 已加属性>=5 then
        --             记录加点[bd[i]]=math.floor(角色信息.坐骑列表[坐骑属性.选中].加点记录[bd[i]]/已加属性*5)
        --             是否推荐 = false
        --         end
        --       end

        --       if 是否推荐 then
        --         坐骑属性.临时加点.力量 = 坐骑属性.临时加点.力量 + 坐骑属性.选中潜力
        --         坐骑属性.选中潜力 = 0
        --       else
        --           local 循环次数 = 坐骑属性.选中潜力
        --           for i=1,循环次数 do
        --             if 坐骑属性.选中潜力>0 then
        --                 for k,v in pairs(记录加点) do
        --                       if 坐骑属性.选中潜力>= v then
        --                         坐骑属性.选中潜力= 坐骑属性.选中潜力-v
        --                         坐骑属性.临时加点[k]=坐骑属性.临时加点[k]+v
        --                       end
        --                   end
        --               end
        --           end
        --       end
        --       坐骑属性:属性显示()
        --   end


end

for i, v in ipairs(bd) do
  local 加号 = 坐骑属性:创建按钮(v.."加",155,282+(i-1)*25)
  function 加号:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/dd",0x10000004),1)
  end
  local 减号 = 坐骑属性:创建按钮(v.."减",177,282+(i-1)*25)
  function 减号:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/dd",0x10000003),1)
  end
  function 加号:左键弹起(x, y)
          if 坐骑属性.选中 and  角色信息 and 角色信息.坐骑列表[坐骑属性.选中] and 坐骑属性.选中潜力>0  then
            坐骑属性.临时加点[v]=坐骑属性.临时加点[v]+1
            坐骑属性.选中潜力=坐骑属性.选中潜力-1
            坐骑属性:属性显示()
          end
  end
  function 减号:左键弹起(x, y)
          if  坐骑属性.选中 and  角色信息 and 角色信息.坐骑列表[坐骑属性.选中] and 坐骑属性.临时加点[v]>0  then
            坐骑属性.临时加点[v]=坐骑属性.临时加点[v]-1
            坐骑属性.选中潜力=坐骑属性.选中潜力+1
            坐骑属性:属性显示()
          end
  end


end
local 查看资质 = 坐骑属性:创建按钮("查看资质",280,440)
function 查看资质:初始化()
    self:创建按钮精灵(__res:取资源动画("dlzy",0xB15C5678),1,"查看资质")
end

function 查看资质:左键弹起(x, y)
      if 坐骑属性.选中 and 角色信息.坐骑列表[坐骑属性.选中] then
          窗口层.坐骑技能:打开(角色信息.坐骑列表[坐骑属性.选中],坐骑属性.选中)
      end
end





local 关闭 = 坐骑属性:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  坐骑属性:置可见(false)
    窗口层.召唤资质:置可见(false)
end