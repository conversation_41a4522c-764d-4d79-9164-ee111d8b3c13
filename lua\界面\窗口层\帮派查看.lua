local 帮派查看 = 窗口层:创建窗口("帮派查看", 0, 0, 725, 520)
local 帮派建筑升级经验 = {
  [0]={经验=1600,繁荣=5,人气=3},
  [1]={经验=1600,繁荣=5,人气=3},
  [2]={经验=3200,繁荣=10,人气=6},
  [3]={经验=6400,繁荣=20,人气=12},
  [4]={经验=12800,繁荣=40,人气=24},
  [5]={经验=25600,繁荣=80,人气=48},
  [6]={经验=51200,繁荣=160,人气=96},
  [7]={经验=102400,繁荣=320,人气=192},
  [8]={经验=204800,繁荣=640,人气=384},
  [9]={经验=409600,繁荣=1280,人气=768},
  [10]={经验=819200,繁荣=2560,人气=1536},
  [11]={经验=1638400,繁荣=5120,人气=3072},
  [12]={经验=3276800,繁荣=10240,人气=6144},
  [13]={经验=6553600,繁荣=20480,人气=12288},
  [14]={经验=13107200,繁荣=40960,人气=24576},
  [15]={经验=15728640,繁荣=49150,人气=29491},
  [16]={经验=18874368,繁荣=58980,人气=35398},
  [17]={经验=22649241,繁荣=70776,人气=42467},
  [18]={经验=27179089,繁荣=84931,人气=50960},
  [19]={经验=35332816,繁荣=110410,人气=66248},
  [20]={经验=45932661,繁荣=143533,人气=86123},
  [21]={经验=59712460,繁荣=186593,人气=111960},
  [22]={经验=77626198,繁荣=242573,人气=145548},
  [23]={经验=100914057,繁荣=315345,人气=189212},
  [24]={经验=141279680,繁荣=441482,人气=264899},
  [25]={经验=197791552,繁荣=618073,人气=370587},
  [26]={经验=276908173,繁荣=865302,人气=519199},
  [27]={经验=387671443,繁荣=1211424,人气=726880},
  [28]={经验=542740018,繁荣=1695993,人气=1017630},
}




local 技能经验={
  [1]=16,
  [2]=32,
  [3]=52,
  [4]=75,
  [5]=103,
  [6]=136,
  [7]=179,
  [8]=231,
  [9]=295,
  [10]=372,
  [11]=466,
  [12]=578,
  [13]=711,
  [14]=867,
  [15]=1049,
  [16]=1280,
  [17]=1503,
  [18]=1780,
  [19]=2096,
  [20]=2452,
  [21]=2854,
  [22]=3304,
  [23]=3807,
  [24]=4364,
  [25]=4983,
  [26]=5664,
  [27]=6415,
  [28]=7238,
  [29]=8138,
  [30]=9120,
  [31]=10188,
  [32]=11347,
  [33]=12602,
  [34]=13959,
  [35]=15423,
  [36]=16998,
  [37]=18692,
  [38]=20508,
  [39]=22452,
  [40]=24532,
  [41]=26753,
  [42]=29121,
  [43]=31642,
  [44]=34323,
  [45]=37169,
  [46]=40186,
  [47]=43388,
  [48]=46773,
  [49]=50352,
  [50]=54132,
  [51]=58120,
  [52]=62324,
  [53]=66750,
  [54]=71407,
  [55]=76303,
  [56]=81444,
  [57]=86840,
  [58]=92500,
  [59]=104640,
  [60]=111136,
  [61]=117931,
  [62]=125031,
  [63]=132444,
  [64]=140183,
  [65]=148253,
  [66]=156666,
  [67]=156666,
  [68]=165430,
  [69]=174556,
  [70]=184052,
  [71]=193930,
  [72]=204198,
  [73]=214868,
  [74]=225948,
  [75]=237449,
  [76]=249383,
  [77]=261760,
  [78]=274589,
  [79]=287884,
  [80]=301652,
  [81]=315908,
  [82]=330662,
  [83]=345924,
  [84]=361708,
  [85]=378023,
  [86]=394882,
  [87]=412297,
  [88]=430280,
  [89]=448844,
  [90]=468000,
  [91]=487760,
  [92]=508137,
  [93]=529145,
  [94]=550796,
  [95]=573103,
  [96]=596078,
  [97]=619735,
  [98]=644088,
  [99]=669149,
  [100]=721452,
  [101]=748722,
  [102]=776755,
  [103]=805566,
  [104]=835169,
  [105]=865579,
  [106]=896809,
  [107]=928876,
  [108]=961792,
  [109]=995572,
  [110]=1030234,
  [111]=1065190,
  [112]=1102256,
  [113]=1139649,
  [114]=1177983,
  [115]=1217273,
  [116]=1256104,
  [117]=1298787,
  [118]=1341043,
  [119]=1384320,
  [120]=1428632,
  [121]=1473999,
  [122]=1520435,
  [123]=1567957,
  [124]=1616583,
  [125]=1666328,
  [126]=1717211,
  [127]=1769248,
  [128]=1822456,
  [129]=1876852,
  [130]=1932456,
  [131]=1989284,
  [132]=2047353,
  [133]=2106682,
  [134]=2167289,
  [135]=2229192,
  [136]=2292410,
  [137]=2356960,
  [138]=2422861,
  [139]=2490132,
  [140]=2558792,
  [141]=2628860,
  [142]=2700356,
  [143]=2773296,
  [144]=2847703,
  [145]=2923593,
  [146]=3000989,
  [147]=3079908,
  [148]=3160372,
  [149]=3242400,
  [150]=6652022,
  [151]=6822452,
  [152]=6996132,
  [153]=7173104,
  [154]=7353406,
  [155]=11305620,
  [156]=15305620,
  [157]=22305620,
  [158]=27305620,
  [159]=37305620,
  [160]=45305620,
  [161]=54305620
}





function 帮派查看:初始化()
  --self:置精灵(置窗口背景("帮派查看", 0, 12, 720, 520))
  self.帮派数据 = {}
  self.帮众数据 = {}
  self.申请数据 = {}
  self.帮派权限 = 0
  self.帮派技能 = {}
  self.修炼技能 = {}
  self.帮派建筑 = {}
  self.类型="基础"
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
end


local 单选设置={"基础","成员","统计","技能","建筑"}

for i, v in ipairs(单选设置) do
  local 临时函数 = 帮派查看:创建红色单选按钮(v, v, 14+(i-1)*90, 35, 80,30)
  function 临时函数:左键弹起(x, y)
        帮派查看.类型=v
        帮派查看:显示设置()
  end
end
--,"申请"


local 帮派缴纳 = 帮派查看:创建红色按钮("缴纳帮费", "帮派缴纳", 582, 330, 100, 30)
function 帮派缴纳:左键弹起(x, y)
  if 帮派查看.帮派数据 and 帮派查看.帮派数据.每周帮费 then
      窗口层.文本栏:打开("每周需要缴纳帮费一次,本帮帮费为:"..帮派查看.帮派数据.每周帮费.帮费,6114,{})
  end
end
local 关闭按钮 = 帮派查看:创建红色按钮("关 闭","关闭按钮", 530, 370, 60, 30)
function 关闭按钮:左键弹起(x, y)
  帮派查看:置可见(false)
end



local 修改宗旨 = 帮派查看:创建红色按钮("修改宗旨","修改宗旨", 600, 370, 100, 30)
function 修改宗旨:左键弹起(x, y)

  __UI弹出.组合输入框:打开("帮派宗旨",{"请输入你要修改的帮派宗旨 ","白色"})
end


local 退出帮派= 帮派查看:创建蓝色按钮("退出帮派","退出帮派", 590, 35, 100, 30)
function 退出帮派:左键弹起(x, y)
  if 角色信息.帮派数据.权限+0 ~= 0 then
    __UI弹出.提示框:打开('#Y你身兼帮派要职无法主动离开帮派!')
  else
    请求服务(6107)
    return 0
  end

end



local 申请按钮= 帮派查看:创建红色按钮("申  请","申请按钮", 470, 35, 80, 30)
function 申请按钮:左键弹起(x, y)
 if 帮派查看.帮派权限>=1 and 帮派查看.申请数据 and 帮派查看.申请数据[1]  then
      窗口层.帮派申请:打开(帮派查看.申请数据)
      帮派查看:置可见(false)
  else
    __UI弹出.提示框:打开('#Y你的权限不够或没有玩家申请!')
 end
  
end



local 逐出按钮 = 帮派查看:创建蓝色按钮("逐出帮派","逐出按钮", 485, 35, 100, 30)
function 逐出按钮:左键弹起(x, y)
  if 帮派查看.选中成员 and 帮派查看.帮众数据[帮派查看.选中成员] and 帮派查看.帮派权限>=2  then
        if  帮派查看.帮众数据[帮派查看.选中成员].职务 ~= "帮众" then
           __UI弹出.提示框:打开('#Y请解除该玩家的职务后再次尝试!')
        else
            请求服务(6106,{玩家id=帮派查看.帮众数据[帮派查看.选中成员].id})
            帮派查看.选中成员=nil
            帮派查看.成员列表:置数据()
        end
    end

end

local 修改职务 = 帮派查看:创建红色按钮("修改职务","修改职务", 595, 35, 100, 30)
function 修改职务:左键弹起(x, y)
      if 帮派查看.选中成员 and 帮派查看.帮众数据[帮派查看.选中成员] and 帮派查看:权限判断() then
        local 选项信息 = {}
        if 帮派查看.帮派权限 >= 5 then
          选项信息 = {"帮主","副帮主","左护法","右护法","长老","堂主","帮众","商人"}
        elseif 帮派查看.帮派权限 >= 4 then
          选项信息 = {"左护法","右护法","长老","堂主","帮众","商人"}
        elseif 帮派查看.帮派权限 >=3 then
          选项信息 = {"长老","堂主","帮众","商人"}
        elseif 帮派查看.帮派权限 >=2 then
          选项信息 = {"堂主","帮众","商人"}
        else
            选项信息 = {"帮众"}
        end
        窗口层.对话栏:打开("男人_兰虎","帮派总管","请选中您要任命的职位",选项信息)

      end
  end


local 购买经验 = 帮派查看:创建红色按钮("购买经验","购买经验", 390, 480, 100, 30)
function 购买经验:左键弹起(x, y)
  if 帮派查看.选中技能 and 帮派查看.帮派技能[帮派查看.选中技能] and 帮派查看.帮派权限>=4  then
      窗口层.文本栏:打开("是否消耗1000万帮派资金购买10万点技能经验或10点修炼经验",6117,{名称=帮派查看.帮派技能[帮派查看.选中技能].名称})
  end
end

local 研究技能 = 帮派查看:创建红色按钮("研究技能","研究技能", 500, 480, 100, 30)
function 研究技能:左键弹起(x, y)
  if 帮派查看.选中技能 and 帮派查看.帮派技能[帮派查看.选中技能] and 帮派查看.帮派权限>=4  then
       请求服务(6108,{名称=帮派查看.帮派技能[帮派查看.选中技能].名称})

  end

end


local 提升等级 = 帮派查看:创建红色按钮("提升等级","提升等级", 610, 480, 100, 30)
function 提升等级:左键弹起(x, y)
    if 帮派查看.选中技能 and 帮派查看.帮派技能[帮派查看.选中技能] and 帮派查看.帮派权限>=4  then
  
      请求服务(6110,{名称=帮派查看.帮派技能[帮派查看.选中技能].名称})
     


      
    end

end


local 瞬间研究 = 帮派查看:创建红色按钮("瞬间研究","瞬间研究", 390, 480, 100, 30)
function 瞬间研究:左键弹起(x, y)
  if 帮派查看.选中建筑 and 帮派查看.帮派建筑[帮派查看.选中建筑] and 帮派查看.帮派权限>=4 then
        窗口层.文本栏:打开("是否消耗十倍损耗资金瞬间研究#G"..帮派查看.帮派建筑[帮派查看.选中建筑].名称.."内政，瞬间研究会同样扣除人气和繁荣",6116,{名称=帮派查看.帮派建筑[帮派查看.选中建筑].名称})
  end

end

local 研究建筑 = 帮派查看:创建红色按钮("研    究","研究建筑", 500, 480, 100, 30)
function 研究建筑:左键弹起(x, y)
  if 帮派查看.选中建筑 and 帮派查看.帮派建筑[帮派查看.选中建筑] and 帮派查看.帮派权限>=4 then
      请求服务(6112,{名称=帮派查看.帮派建筑[帮派查看.选中建筑].名称})
  end


end


local 提升规模 = 帮派查看:创建红色按钮("提升规模","提升规模", 610, 480, 100, 30)
function 提升规模:左键弹起(x, y)
  if 帮派查看.选中建筑 and 帮派查看.帮派建筑[帮派查看.选中建筑] and 帮派查看.帮派权限>=4 then
    请求服务(6113)
  end


end



  local 宗旨文本 = 帮派查看:创建文本("宗旨文本", 24, 405, 630, 100)
  local 技能文本 = 帮派查看:创建文本("技能文本", 420, 90, 270, 170)

function 帮派查看:打开(数据)
  self:置可见(true)
  self.图像=nil
  self.帮派数据 = {}
  self.帮众数据 = {}
  self.申请数据 = {}
  self.帮派权限 = 0
  self.帮派技能 = {}
  self.修炼技能 = {}
  self.帮派建筑 = {}
  self.类型="基础"
  self.选中成员=nil
  self.选中技能=nil
  self.选中建筑=nil
  self:刷新(数据)
end 

function 帮派查看:刷新(数据)
  self.帮派数据 = 数据[1]
  self.帮众数据 = {}
  self.申请数据 = self.帮派数据.申请数据
  for i ,v in pairs(self.帮派数据.成员数据) do
    if self.帮派数据.成员数据[i].职务 == "帮主" then
        table.insert(self.帮众数据, 1, self.帮派数据.成员数据[i])
      elseif self.帮派数据.成员数据[i].职务 == "副帮主" then
        for n=1,2 do
         if self.帮众数据[n] == nil or (self.帮众数据[n].职务 ~= "帮主") then
              table.insert(self.帮众数据, n, self.帮派数据.成员数据[i])
              break
            end
        end
      elseif self.帮派数据.成员数据[i].职务 == "左护法" then
        for n=1,3 do
         if self.帮众数据[n] == nil or (self.帮众数据[n].职务 ~= "帮主" and self.帮众数据[n].职务 ~= "副帮主" ) then
              table.insert(self.帮众数据, n, self.帮派数据.成员数据[i])
              break
            end
        end
      elseif self.帮派数据.成员数据[i].职务 == "右护法" then
        for n=1,4 do
         if self.帮众数据[n] == nil or (self.帮众数据[n].职务 ~= "帮主" and self.帮众数据[n].职务 ~= "副帮主" and self.帮众数据[n].职务 ~= "左护法" ) then
              table.insert(self.帮众数据, n, self.帮派数据.成员数据[i])
              break
            end
        end
      elseif self.帮派数据.成员数据[i].职务 == "长老" then
        for n=1,5 do
            if self.帮众数据[n] == nil or (self.帮众数据[n].职务 ~= "帮主" and self.帮众数据[n].职务 ~= "副帮主" and self.帮众数据[n].职务 ~= "左护法" and self.帮众数据[n].职务 ~= "右护法") then
              table.insert(self.帮众数据, n, self.帮派数据.成员数据[i])
              break
            end
        end
      elseif self.帮派数据.成员数据[i].职务 == "堂主" then
        for n=1,8 do
            if self.帮众数据[n] == nil or (self.帮众数据[n].职务 ~= "帮主" and self.帮众数据[n].职务 ~= "副帮主" and self.帮众数据[n].职务 ~= "左护法" and self.帮众数据[n].职务 ~= "右护法" and self.帮众数据[n].职务 ~= "长老")then
              table.insert(self.帮众数据, n, self.帮派数据.成员数据[i])
              break
            end
        end
      else
        table.insert(self.帮众数据, self.帮派数据.成员数据[i])
      end
  end
  self.帮派权限 = 角色信息.帮派数据.权限
  self.帮派技能 = {}
  for n,v in pairs(self.帮派数据.技能数据) do
    self.帮派技能[#self.帮派技能+1] = self.帮派数据.技能数据[n]
    self.帮派技能[#self.帮派技能].名称 = n
  end
  self.修炼技能 = {}
  for n,v in pairs(self.帮派数据.修炼数据) do
    if n =="攻击修炼" then
      self.修炼技能[1] = self.帮派数据.修炼数据[n]
      self.修炼技能[1].名称 = n
    elseif n == "防御修炼" then
      self.修炼技能[2] = self.帮派数据.修炼数据[n]
      self.修炼技能[2].名称 = n
    elseif n == "法术修炼" then
      self.修炼技能[3] = self.帮派数据.修炼数据[n]
      self.修炼技能[3].名称 = n
    elseif n == "法抗修炼" then
      self.修炼技能[4] = self.帮派数据.修炼数据[n]
      self.修炼技能[4].名称 = n
    end
  end
  self.帮派建筑 = {}
  for n,v in pairs(self.帮派数据.帮派建筑) do
    self.帮派建筑[#self.帮派建筑+1] = self.帮派数据.帮派建筑[n]
    self.帮派建筑[#self.帮派建筑].名称 = n
  end
  self.宗旨文本:清空()
  self.宗旨文本:置文本("    #H"..self.帮派数据.帮派宗旨)


  self.成员列表:置数据()
  if not self.选中技能 or (self.选中技能 and not self.帮派技能[self.选中技能]) then
     self.技能列表:置数据()
  end
  if not self.选中建筑 or (self.选中建筑 and not self.帮派建筑[self.选中建筑]) then
     self.建筑列表:置数据()
  end
  self:显示设置()

end

function 帮派查看:显示设置()
  self[self.类型]:置选中(true)
  self.图像=nil
  self.帮派缴纳:置可见(false)
  self.关闭按钮:置可见(false)
  self.修改宗旨:置可见(false)
  self.退出帮派:置可见(false)
  self.宗旨文本:置可见(false)
  self.成员列表:置可见(false)
  self.逐出按钮:置可见(false)
  self.修改职务:置可见(false)
  self.技能列表:置可见(false)
  self.技能文本:置可见(false)
  self.申请按钮:置可见(true)
  self.技能文本:清空()
  self.购买经验:置可见(false):置禁止(true)
  self.研究技能:置可见(false):置禁止(true)
  self.提升等级:置可见(false):置禁止(true)
  self.瞬间研究:置可见(false):置禁止(true)
  self.研究建筑:置可见(false):置禁止(true)
  self.提升规模:置可见(false):置禁止(true)
  self.建筑列表:置可见(false)
  if self.选中成员 and self.帮众数据[self.选中成员] then
      if  self.帮派权限>=2 then
          self.逐出按钮:置禁止(false)
      end
      if self:权限判断() then
        self.修改职务:置禁止(false)
      end
  else
      self.选中成员=nil
      self.逐出按钮:置禁止(true)
      self.修改职务:置禁止(true)
  end
  if self.选中技能 and self.帮派技能[self.选中技能] then
      self:额外显示()
      self.技能列表:置子数据()
      if 取技能(self.帮派技能[self.选中技能].名称)[1] then
         self.技能文本:置文本("#R"..self.帮派技能[self.选中技能].名称.."\n#B"..取技能(self.帮派技能[self.选中技能].名称)[1])
      end
  end
  if self.选中建筑 and self.帮派建筑[self.选中建筑] then
      self:额外显示()
      self.建筑列表:置子数据()
  end
  self:创建纹理精灵(function()
                    if self.类型=="基础" then
                          self.选中成员=nil
                          self.选中技能=nil
                          self.选中建筑=nil
                          __res:取资源动画("dlzy",0x10000084,"图像"):拉伸(720,520):显示(0, 0)
                          说明字体:置颜色(0, 0, 0)
                          说明字体:取图像(self.帮派数据.帮派名称):显示(102, 75)
                          说明字体:取图像(self.帮派数据.帮派资金.当前):显示(340, 75)
                          说明字体:取图像(self.帮派数据.敌对帮派):显示(580, 75)
                          说明字体:取图像(self.帮派数据.帮派规模):显示(102, 112)
                          说明字体:取图像(self.帮派数据.储备金):显示(340, 112)
                          说明字体:取图像(self.帮派数据.同盟帮派):显示(580, 112)
                          说明字体:取图像(self.帮派数据.帮派编号):显示(102, 149)
                          说明字体:取图像(self.帮派数据.修理指数):显示(340, 149)
                          说明字体:取图像(self.帮派数据.安定度):显示(580, 149)
                          说明字体:取图像(self.帮派数据.创始人.名称):显示(102, 184)
                          说明字体:取图像(self.帮派数据.维护时间):显示(340, 184)
                          说明字体:取图像(self.帮派数据.人气度):显示(580, 184)
                          说明字体:取图像(self.帮派数据.现任帮主.名称):显示(102, 221)
                          说明字体:取图像(self.帮派数据.帮派资材.当前):显示(340, 221)
                          说明字体:取图像(self.帮派数据.行动力):显示(580, 221)
                          说明字体:取图像(self.帮派数据.成员数量.当前.."/"..self.帮派数据.成员数量.上限):显示(102, 258)
                          说明字体:取图像(self.帮派数据.帮派资材.上限):显示(340, 258)
                          说明字体:取图像(self.帮派数据.研究力):显示(580, 258)
                          说明字体:取图像(self.帮派数据.繁荣度/100):显示(102, 293)
                          说明字体:取图像(self.帮派数据.掌控区域):显示(340, 293)
                          说明字体:取图像(self.帮派数据.训练力):显示(580, 293)
                          说明字体:取图像(self.帮派数据.繁荣度):显示(102, 331)
                          说明字体:取图像(self.帮派数据.管辖社区):显示(340, 331)
                          self.帮派缴纳:置可见(true)
                          self.关闭按钮:置可见(true)
                          self.修改宗旨:置可见(true)
                          self.退出帮派:置可见(true)
                          self.宗旨文本:置可见(true)
                    elseif self.类型=="成员" then    
                            self.选中技能=nil
                            self.选中建筑=nil
                            __res:取资源动画("dlzy",0x10000083,"图像"):拉伸(720,520):显示(0, 0)
                            self.成员列表:置可见(true)
                            self.逐出按钮:置可见(true)
                            self.修改职务:置可见(true)
                            self.申请按钮:置可见(false)
                    elseif self.类型=="统计" then  
                              __res:取资源动画("dlzy",0x10000092,"图像"):拉伸(720,520):显示(0, 0)
                    elseif self.类型=="技能" then  
                              self.选中成员=nil
                              self.选中建筑=nil
                              __res:取资源动画("dlzy",0x10000091,"图像"):拉伸(720,520):显示(0, 0)
                              说明字体:置颜色(0, 0, 0)
                              说明字体:取图像("攻击修炼        0             20"):显示(44, 330)
                              说明字体:取图像("防御修炼        0             20"):显示(44, 365)
                              说明字体:取图像("法术修炼        0             20"):显示(44, 400)
                              说明字体:取图像("法抗修炼        0             20"):显示(44, 435)
                              说明字体:置颜色(255,255,255,255)
                              说明字体:取图像("当前研究技能:"):显示(400, 280)
                              if self.帮派数据.研究技能~=nil then
                                  说明字体:取图像(self.帮派数据.研究技能):显示(405+说明字体:取宽度("当前研究技能:"), 280)
                              end
                              self.技能列表:置可见(true)
                              self.技能文本:置可见(true)
                              self.购买经验:置可见(true)
                              self.研究技能:置可见(true)
                              self.提升等级:置可见(true)
                    elseif self.类型=="建筑" then  
                              self.选中成员=nil
                              self.选中技能=nil
                              __res:取资源动画("dlzy",0x10000085,"图像"):拉伸(720,520):显示(0, 0)
                              说明字体:置颜色(0, 0, 0)
                              说明字体:取图像(self.帮派数据.帮派建筑.金库.数量):显示(102, 90)
                              说明字体:取图像(self.帮派数据.帮派建筑.书院.数量):显示(102, 140)
                              说明字体:取图像(self.帮派数据.帮派建筑.兽室.数量):显示(102, 190)
                              说明字体:取图像(self.帮派数据.帮派建筑.厢房.数量):显示(102, 239)
                              说明字体:取图像(self.帮派数据.帮派建筑.药房.数量):显示(102, 290)
                              说明字体:取图像(self.帮派数据.帮派建筑.仓库.数量):显示(102, 338)
                              说明字体:取图像(self.帮派数据.药品增加量):显示(102, 390)
                              说明字体:取图像(self.帮派数据.物价指数):显示(297, 90)
                              说明字体:取图像(self.帮派数据.修理指数):显示(297, 140)
                              说明字体:取图像(self.帮派数据.守护兽等级):显示(297, 190)
                              说明字体:取图像(self.帮派数据.帮派迷宫):显示(297, 239)
                              说明字体:取图像(self.帮派数据.当前内政):显示(297, 288)
                              说明字体:取图像(self.帮派数据.当前维护费):显示(102, 457)
                              说明字体:置颜色(255,255,255,255)
                              说明字体:取图像("当前内政:         进度:"):显示(20, 420)
                              if self.帮派数据.当前内政 and self.帮派数据.帮派建筑[self.帮派数据.当前内政] then
                                说明字体:取图像(self.帮派数据.当前内政.."          "..self.帮派数据.帮派建筑[self.帮派数据.当前内政].当前经验):显示(110, 420)
                              end
                              self.瞬间研究:置可见(true)
                              self.研究建筑:置可见(true)
                              self.提升规模:置可见(true)
                              self.建筑列表:置可见(true)
                    end
                end)
end

function 帮派查看:额外显示()
  self.图像 = self:创建纹理精灵(function()
                    说明字体:置颜色(0, 0, 0)
                    if self.类型=="技能" and self.选中技能 and self.帮派技能[self.选中技能]  then  
                          说明字体:取图像(self.帮派技能[self.选中技能].名称):显示(532, 330)
                          说明字体:取图像(技能经验[self.帮派技能[self.选中技能].当前+1]):显示(532, 381)
                          说明字体:取图像(self.帮派技能[self.选中技能].当前经验):显示(532, 432)
                          if self.帮派权限>=4 then
                            self.购买经验:置禁止(false)
                            self.研究技能:置禁止(false)
                            self.提升等级:置禁止(false)
                          end      
                    elseif self.类型=="建筑" and self.选中建筑 and self.帮派建筑[self.选中建筑]   then 
                          说明字体:取图像(帮派建筑升级经验[self.帮派数据.帮派建筑[self.帮派建筑[self.选中建筑].名称].数量].经验):显示(525, 320)
                          说明字体:取图像(帮派建筑升级经验[self.帮派数据.帮派建筑[self.帮派建筑[self.选中建筑].名称].数量].经验):显示(525, 361)
                          说明字体:取图像(帮派建筑升级经验[self.帮派数据.帮派建筑[self.帮派建筑[self.选中建筑].名称].数量].繁荣):显示(525, 402)
                          说明字体:取图像(帮派建筑升级经验[self.帮派数据.帮派建筑[self.帮派建筑[self.选中建筑].名称].数量].人气):显示(525, 443)
                          if self.帮派权限>=4 then
                            self.瞬间研究:置禁止(false)
                            self.研究建筑:置禁止(false)
                            self.提升规模:置禁止(false)
                          end 
                    end
                end,1
              )
end


function 帮派查看:显示(x,y)
  if self.图像 then
    self.图像:显示(x,y)
  end

end



local 成员列表 = 帮派查看:创建列表("成员列表",13, 140, 680, 336)
function 成员列表:初始化()
  self.行高度 = 42
end



function 成员列表:置数据()
    self:清空()
   for _, v in ipairs(帮派查看.帮众数据) do
      local r = self:添加()
      r:创建纹理精灵(function()
                        if v.在线 == nil or v.在线 then
                          说明字体:置颜色(__取颜色("红色"))
                        else
                            说明字体:置颜色(0,0,0,255)
                        end
                        说明字体:取图像(v.名称):显示(0, 10)
                        说明字体:取图像(v.等级):显示(120+(47-说明字体:取宽度(v.等级))//2, 10)
                        说明字体:取图像(v.id):显示(167+(130-说明字体:取宽度(v.id))//2, 10)
                        说明字体:取图像(v.门派):显示(295+(90-说明字体:取宽度(v.门派))//2, 10)
                        说明字体:取图像(v.职务):显示(382+(83-说明字体:取宽度(v.职务))//2, 10)
                        说明字体:取图像(v.帮贡.当前.."/"..v.帮贡.上限):显示(460+(120-说明字体:取宽度(v.帮贡.当前.."/"..v.帮贡.上限))//2, 10)
                        说明字体:取图像(os.date("%m", v.离线时间).. "月" .. os.date("%d", v.离线时间).."日"):显示(678-说明字体:取宽度(os.date("%m", v.离线时间).. "月" .. os.date("%d", v.离线时间).."日"), 10)
                      
                    end
              )
    end
end

function 成员列表:左键弹起(x, y, i)
  if 帮派查看.帮众数据[i] then
      帮派查看.选中成员= i
      if  帮派查看.帮派权限>=2 then
          帮派查看.逐出按钮:置禁止(false)
      end
      if 帮派查看:权限判断() then
         帮派查看.修改职务:置禁止(false)
      end
  else
      帮派查看.选中成员= nil
      帮派查看.修改职务:置禁止(true)
      帮派查看.逐出按钮:置禁止(true)
  end
end

local 技能列表 = 帮派查看:创建列表("技能列表",40, 120, 330, 140)
function 技能列表:初始化()
  self.行高度 = 35
end



function 技能列表:置数据()
    self:清空()
   for _, v in ipairs(帮派查看.帮派技能) do
      local r = self:添加()
      r:创建纹理精灵(function()
                      if 帮派查看.帮派数据.研究技能==v.名称 then
                          说明字体:置颜色(__取颜色("红色"))
                      else
                          说明字体:置颜色(0,0,0,255)
                      end
                      说明字体:取图像(v.名称):显示(0, 8)
                      说明字体:取图像(v.当前):显示(130, 8)
                      说明字体:取图像(v.上限):显示(230, 8)
                    end
              )
    end
end

function 技能列表:置子数据()
  if 帮派查看.选中技能 and 帮派查看.帮派技能[帮派查看.选中技能] and self.子控件[帮派查看.选中技能] then
        self.子控件[帮派查看.选中技能]:创建纹理精灵(function()
                      if 帮派查看.帮派数据.研究技能==帮派查看.帮派技能[帮派查看.选中技能].名称 then
                        说明字体:置颜色(__取颜色("红色"))
                      else
                          说明字体:置颜色(0,0,0,255)
                      end
                      说明字体:取图像(帮派查看.帮派技能[帮派查看.选中技能].名称):显示(0, 8)
                      说明字体:取图像(帮派查看.帮派技能[帮派查看.选中技能].当前):显示(130, 8)
                      说明字体:取图像(帮派查看.帮派技能[帮派查看.选中技能].上限):显示(230, 8)
                    end
              )
    end
end

function 技能列表:左键弹起(x, y, i)
      帮派查看.技能文本:清空()
      if 帮派查看.帮派技能[i] then 
        帮派查看.选中技能 = i
        if 取技能(帮派查看.帮派技能[i].名称)[1]  then
            帮派查看.技能文本:置文本("#R"..帮派查看.帮派技能[i].名称.."\n#B"..取技能(帮派查看.帮派技能[i].名称)[1])
        end

        帮派查看:额外显示()
      end
end



local 建筑列表 = 帮派查看:创建列表("建筑列表",410, 75, 280, 228)
function 建筑列表:初始化()
     self.行高度 = 38
end

function 建筑列表:置数据()
    self:清空()
   for _, v in ipairs(帮派查看.帮派建筑) do
      local r = self:添加()
      r:创建纹理精灵(function()
                if 帮派查看.帮派数据.当前内政 ==v.名称 then
                    说明字体:置颜色(__取颜色("红色"))
                else
                    说明字体:置颜色(0,0,0,255)
                end
                说明字体:取图像(v.名称):显示(18, 10)
              end
        )
     
    end
end

function 建筑列表:置子数据()
      if 帮派查看.选中建筑 and 帮派查看.帮派建筑[帮派查看.选中建筑] and self.子控件[帮派查看.选中建筑] then
            self.子控件[帮派查看.选中建筑]:创建纹理精灵(function()
                          if 帮派查看.帮派数据.当前内政 ==帮派查看.帮派建筑[帮派查看.选中建筑].名称 then
                              说明字体:置颜色(__取颜色("红色"))
                          else
                              说明字体:置颜色(0,0,0,255)
                          end
                          说明字体:取图像(帮派查看.帮派建筑[帮派查看.选中建筑].名称):显示(18, 10)   
                          end
                  )
        end
end

function 建筑列表:左键弹起(x, y, i)
      if 帮派查看.帮派建筑[i] then 
          帮派查看.选中建筑 = i
        帮派查看:额外显示()
      end
end







function 帮派查看:权限判断()
	if not self.选中成员 then
		return false
	end
	if self.帮派权限 == 5 then
		if self.帮众数据[self.选中成员].职务 ~= "帮主"  then
			return true
		else
			return false
		end
	elseif self.帮派权限 == 4 then
		if self.帮众数据[self.选中成员].职务 ~= "帮主"  then
			return true
		else
			return false
		end
	elseif self.帮派权限 == 3 then
		if self.帮众数据[self.选中成员].职务 ~= "帮主" and self.帮众数据[self.选中成员].职务 ~= "副帮主" then
			return true
		else
			return false
		end
	elseif self.帮派权限 == 2 then
		if self.帮众数据[self.选中成员].职务 ~= "帮主" and self.帮众数据[self.选中成员].职务 ~= "副帮主"  and self.帮众数据[self.选中成员].职务 ~= "左护法" and self.帮众数据[self.选中成员].职务 ~= "右护法" then
			return true
		else
			return false
		end
	elseif self.帮派权限 == 1 then
		if self.帮众数据[self.选中].职务 ~= "堂主" or self.帮众数据[self.选中].职务 == "帮众"   then
			return true
		else
			return false
		end
	else
		return false
	end
end


local 关闭 = 帮派查看:创建关闭按钮("关闭",690,3,20,20)
function 关闭:左键弹起(x, y)
      帮派查看:置可见(false)
end







