--[[
LastEditTime: 2024-10-09 20:44:12
--]]

local 飞行符 = 窗口层:创建窗口("飞行符", 0,0, 660, 310)
function 飞行符:初始化()
  self:置精灵(__res:取资源动画('ui',1365723636,"精灵"))
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true

  self.关闭:置大小(16,16)
  self.关闭:置坐标(self.宽度-18, 2)

end
function 飞行符:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
end



for i, v in ipairs({
  {
    name = "长安城",
    x = 378,
    y = 83,
    tcp = 3680611625
  },
  {
    name = "建邺城",
    x = 425,
    y = 191,
    tcp = 1029051796
  },
  {
    name = "傲来国",
    x = 574,
    y = 244,
    tcp = 1809379784
  },
  {
    name = "长寿村",
    x = 208,
    y = 17,
    tcp = 3318087238
  },
  {
    name = "西凉女国",
    x = 203,
    y = 85,
    tcp = 694300123
  },
  {
    name = "宝象国",
    x = 186,
    y = 161,
    tcp = 1226730316
  },
  {
    name = "朱紫国",
    x = 249,
    y = 224,
    tcp = 1419455363
  }
}) do
  local 临时函数 = 飞行符:创建按钮(v.name, v.x, v.y)
  function 临时函数:初始化()
    self:创建按钮精灵(__res:取资源动画("ui", v.tcp), 1)
  end
 function  临时函数:左键弹起(x, y)
    请求服务(3706, {
      ["序列"] = i + 1
    })
    飞行符:置可见(false)
  end
end

local 关闭 = 飞行符:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  飞行符:置可见(false)
end

