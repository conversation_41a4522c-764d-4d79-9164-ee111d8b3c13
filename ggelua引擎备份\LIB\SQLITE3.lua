-- <AUTHOR> GGELUA
-- @Last Modified by    : baidwwy
-- @Date                : 2022-03-07 18:52:00
-- @Last Modified time  : 2023-03-16 13:15:01

--http://lua.sqlite.org/

---@diagnostic disable
local sqlite3 = require('lsqlite3')
--print(sqlite3.version(),sqlite3.lversion())

local SQLITE3 = class('SQLITE3')

function SQLITE3:初始化(file, psd, flag)
    local db, code, err = sqlite3.open(file, flag)
    self._db = assert(db, err)
    if psd then
        local psd = string.format("PRAGMA key = '%s';", psd)
        self._db:exec(psd)
    end
    assert(self._db:exec('select count(*) from sqlite_master') == 0, '密码或文件错误') --self._db:errmsg()
    collectgarbage() --防止密码保存在内存
end

function SQLITE3:取对象()
    return self._db
end

function SQLITE3:检查语句(sql, ...)
    if select('#', ...) > 0 then
        sql = sql:format(...)
    end
    return sqlite3.complete(sql)
end

function SQLITE3:备份(db)
    local bu = sqlite3.backup_init(db:取对象(), 'main', self._db, 'main')
    if bu then
        bu:step(-1)
        bu:finish()
    end
end

function SQLITE3:执行(sql, ...)
    if select('#', ...) > 0 then
        sql = sql:format(...)
    end

    if self._db:exec(sql) == sqlite3.OK then
        return self._db:changes()
    else
        return false, self._db:errmsg()
    end
end
--insert into test(id) values(?)
function SQLITE3:增加(sql, ...)
    if sql and select('#', ...) > 0 then
        sql = sql:format(...)
    end

    local vm
    if sql == nil then
        vm = self.vm --上次的VM
    else
        vm = self._db:prepare(sql)
    end

    if vm then
        self.vm = vm
        local n = vm:bind_parameter_count()
        if n > 0 then
            for i = 1, n do
                vm:bind(i, select(i - 1 - n, ...))
            end
        end
        if vm:step() == sqlite3.DONE then
            vm:reset()
            return self._db:changes()
        end
    end
    return 0
end
--delete from test where id=1
SQLITE3.删除 = SQLITE3.执行

function SQLITE3:查询(sql, ...)
    if select('#', ...) > 0 then
        sql = sql:format(...)
    end
    local r = {}
    local n = 1
    for t in self._db:nrows(sql) do
        r[n] = t
        n = n + 1
    end
    return r
end
--update test set data=? where id=1
SQLITE3.修改 = SQLITE3.增加
--update test set data=? where id=1
function SQLITE3:blob(sql, ...)
    if sql and select('#', ...) > 0 then
        sql = sql:format(...)
    end

    local vm
    if sql == nil then
        vm = self.vm --上次的VM
    else
        vm = self._db:prepare(sql)
    end

    if vm then
        self.vm = vm
        local n = vm:bind_parameter_count()
        if n > 0 then
            for i = 1, n do
                vm:bind_blob(i, select(i - 1 - n, ...))
            end
        end
        if vm:step() == sqlite3.DONE then
            vm:reset()
            return self._db:changes()
        end
    end
    return 0
end

function SQLITE3:查询一行(sql, ...)
    if select('#', ...) > 0 then
        sql = sql:format(...)
    end
    local fun, vm = self._db:nrows(sql)
    return fun(vm)
end

function SQLITE3:遍历(sql, ...)
    if select('#', ...) > 0 then
        sql = sql:format(...)
    end
    return self._db:nrows(sql)
end
--select count(*) from 表
function SQLITE3:取值(sql, ...)
    if select('#', ...) > 0 then
        sql = sql:format(...)
    end
    local vm = self._db:prepare(sql)
    if vm and vm:step() == sqlite3.ROW then
        return vm:get_value(0)
    end
    return 0
end

--自动递增
function SQLITE3:取递增ID()
    return self._db:last_insert_rowid()
end

function SQLITE3:修改密码(v)
    return self._db:exec(string.format("PRAGMA rekey = '%s';", v)) == sqlite3.OK, self._db:errmsg()
end

function SQLITE3:开始事务()
    return self._db:exec('BEGIN') == sqlite3.OK, self._db:errmsg()
end

function SQLITE3:提交事务()
    return self._db:exec('COMMIT') == sqlite3.OK, self._db:errmsg()
end

function SQLITE3:回滚事务()
    return self._db:exec('ROLLBACK') == sqlite3.OK, self._db:errmsg()
end

function SQLITE3:清理()
    return self._db:exec('VACUUM') == sqlite3.OK, self._db:errmsg()
end

function SQLITE3:取错误()
    return self._db:errmsg()
end
return SQLITE3
