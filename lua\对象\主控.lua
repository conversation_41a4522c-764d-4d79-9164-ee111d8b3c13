local SDL = require("SDL")
local 主控 = class("主控")
-- local 生成假人信息 = function()
--     local fhsj = {}
--     local 地图编号 ={1001,1002,1003,1004,1005,1006,1007,1008,1009,1012,1013,1014,1015,1016,1017,1018,1019,1020,1021,1022,1023,1024,1025,1026,1028,1029,1030,1031,1032,1033,1034,1035,1036,1037,1038,1040,1041,1042,1043,1044,1046,1049,1050,1051,1052,1054,1056,1057,1070,1072,1075,1077,1078,1079,1080,1081,1082,1083,1085,1087,1090,1091,1092,1093,1094,1095,1098,1099,1100,1101,1103,1104,1105,1106,1107,1110,1111,1112,1113,1114,1115,1116,1117,1118,1119,1120,1121,1122,1123,1124,1125,1126,1127,1128,1129,1130,1131,1132,1133,1134,1135,1137,1138,1139,1140,1141,1142,1143,1144,1145,1146,1147,1149,1150,1152,1153,1154,1155,1156,1165,1167,1168,1170,1171,1173,1174,1175,1177,1178,1179,1180,1181,1182,1183,1186,1187,1188,1189,1190,1191,1192,1193,1197,1198,1201,1202,1203,1204,1205,1206,1207,1208,1209,1210,1211,1212,1213,1214,1215,1216,1217,1218,1219,1220,1221,1222,1223,1224,1225,1226,1227,1228,1229,1230,1231,1232,1233,1234,1235,1236,1237,1238,1239,1241,1242,1243,1245,1246,1248,1249,1250,1251,1252,1253,1256,1257,1258,1259,1272,1273,1306,1310,1311,1312,1313,1314,1315,1316,1317,1318,1319,1320,1321,1322,1323,1324,1325,1326,1327,1328,1329,1330,1331,1332,1333,1334,1335,1336,1337,1338,1339,1340,1341,1342,1343,1380,1382,1400,1401,1402,1403,1404,1405,1406,1407,1408,1409,1410,1411,1412,1413,1414,1415,1416,1417,1418,1420,1421,1422,1424,1425,1426,1427,1428,1429,1430,1446,1447,1501,1502,1503,1504,1505,1506,1507,1508,1509,1511,1512,1513,1514,1523,1524,1525,1526,1527,1528,1529,1531,1532,1533,1534,1535,1536,1537,1605,1606,1607,1608,1810,1811,1812,1813,1814,1815,1820,1821,1822,1823,1824,1825,1830,1831,1832,1833,1834,1835,1840,1841,1842,1843,1844,1845,1850,1851,1852,1853,1854,1855,1860,1861,1862,1863,1864,1865,1870,1871,1872,1873,1874,1875,1876,1885,1886,1887,1888,1890,1891,1892,1910,1911,1912,1913,1914,1915,1916,1920,1930,1931,1932,1933,1934,1935,1936,1937,1938,1939,1940,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968,1969,1970,1971,2000,4001,5000}
--     for i = 1, #地图编号 do
--         local fhsj1 = 场景取假人表(地图编号[i])
--         if #fhsj1 > 0 then
--             fhsj[取地图名称(地图编号[i])] = {}
--         end
--         for k, v in pairs(fhsj1) do
--             fhsj[取地图名称(地图编号[i])][fhsj1[k].名称] = {
--                 地图id = 地图编号[i],
--                 x = fhsj1[k].X,
--                 y = fhsj1[k].Y
--             }
--         end
--     end
--     return fhsj
-- end
function 主控:初始化()
    self.战斗中 = false
    self.观战中 = false
    self.道具列表 = {}
    self.行囊列表 = {}
    self.战斗道具 = {}
    self.房屋数据 = {}
    self.多角色={}
    self.角色选择背景 = __res:取资源动画('ui',"A0D00989.was","精灵")
    self.角色选择背景选中 = __res:取资源动画('ui',"A0D00989.was","精灵"):置颜色(246, 14, 57, 255)
    self.给予操作 = false
    local bqs = {0x4D3D1188,0x6E7A8E8F,0x403105F2,0xD3C23894,0xEDD63AB1,0xC8AA7848,0xA5D718B1,
    0xE0C6F0D3,0x572F2A4D,0xA1E13E27,0xB2F4A198,0xEDEBCFCF,0x3B3D19C0,0x9EEC6DE4,
    0x1B1B8326,0x525FCCF9,0xAD9E8BAD,0xE9A1E271,0x1C7C95C4,0x1500E768,0x30615DBC,
    0x3694C64F,0xFD438646,0x4FAD347C,0x743AF90F,0x853F3BC9,0xD6436048,0x74E0F5FA,
    0x8E0063E2,0x5BA9CF5E,0xE8E08FA9,0x888536BF,0xBEDE7D41,0xF06B6B9E,0x58FAA400,
    0x270D5C71,0xACE9C474,0xBE3150EE,0x11C5EA40,0x73F3BF9D,0xCCD6B7E8,0x66D0E07C,
    0x9A8BFB91,0xCA47B474,0x590CAA9B,0x4E20C2E6,0x44B657A9,0x978F8F8A,0x522BC68F,
    0xA8A9B15D,0xE53DE56A,0xE88B5354,0x0417C932,0xC699AB3E,0x19CA9706,0xFCD58523,
    0xCD8F0AD6,0x978B9123,0x0E658C4C,0x12BE1C3E,0x85AC8CCB,0x707ABF50,0x58C9FAB0,
    0xAA7B3B42,0xF2FBDA6E,0xFC4215EC,0xD086F684,0xFCCAA9B5,0xE5FF2DE2,0x87621B9F,
    0xCDC95381,0x396C4E03,0xB06B70C0,0xADE1576E,0xFB472367,0xEDA67286,0x15CA26D9,
    0xDC9C1E87,0xB5786848,0xC2A7A47D,0x7EEB3422,0x8F20BE2E,0xA1E7B566,0x11729962,
    0xEF498C25,0xF95512DC,0xF5509B1C,0x7F869E1E,0x107CF5F3,0xF45DCF6A,0x99AFED62,
    0xC8BBEEA3,0x225ECF82,0xD5C14B62,0xA8BC861D,0x7229A70C,0x4FF6E07A,0xDF1F56AC,
    0x488EBBD6,0x4806AE3B,0x09574327,0x7A9F28C7,0xB7E060C1,0x5887677B,0x1C0BCE22,
    0xAAFBD630,0xE4994B6A,0xACA32B8F,0xED5B5996,0x65D48DBF,0x91EAD158,0x50BF3749,
    0x383F3815,0x445A8BA0,0xD6252D94,0x247121AF,0x64A8BD13,0x79C2D9F2,0x57648A83,
    0x2DF12D10,0xD753949E}
    self.表情 = {}
    for i = 0, 120 do
        self.表情[i] = __res:取资源动画("dlzy", bqs[i],"动画")
    end
    self.靓号 = {}
    for i=121,190 do
        local 编号=i-120
        self.靓号[i] = __res:取资源动画("jszy/lianghao",""..编号,"动画"):置中心(0,16)
    end
    local pdsc = {
        0x43700E25,--系统
        0x1B1DCE56,--世界
        0x65C5B7EE,--当前
        0xF9ADC3DA,--私聊
        0xE8897A81,--GM
        0xCD23D726,--传闻
        0xAD9D6490,--帮派
        0xF9858C95,--队伍
        0x285527E7,--活动
        0x29F78369,--夫妻
------------------------------------下面门派
        0xFC41B9B4, --大唐
        0x0902DAF6, --化生
        0x212E3A85, --龙宫
        0x2BF201B4, --天宫
        0x4100814A, --普陀
        0x81B9B7F2, --方寸
        0xA5F25FB3, --狮驼
        0xADE6DAA4, --魔王
        0xB3296C64, --女儿
        0xEDE8EA9A, --盘丝
        0x2535CF6D, --五庄
        0x46C2DDA3, --地府
        0x000C253D, --凌波
        0x4E6A12A9, --无底
        0x114ABFFE, --神木

    }
    self.频道 = {}
    for i = 1, 25 do
        self.频道[i] = __res:取资源动画("dlzy", pdsc[i],"精灵")
    end
end

function 主控:取武器子类(子类)
    local n = {
        "枪矛",
        "斧钺",
        "剑",
        "双短剑",
        "飘带",
        "爪刺",
        "扇",
        "魔棒",
        "锤",
        "鞭",
        "环圈",
        "刀",
        "法杖",
        "弓弩",
        "宝珠",
        "巨剑",
        "伞",
        "灯笼",
        "头盔",
        "发钗",
        "项链",
        "女衣",
        "男衣",
        "腰带",
        "鞋子",
        "双斧",
    }
    return n[子类]
end

function 主控:取武器附加名称(子类, 级别限制, 名称)
    if 1 == 子类 then
        if 级别限制 < 21 then
            return "红缨枪"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "乌金三叉戟"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "玄铁矛"
        end
    elseif 2 == 子类 then
        if 级别限制 < 21 then
            return "青铜斧"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "双弦钺"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "乌金鬼头镰"
        end
    elseif 3 == 子类 then
        if 级别限制 < 21 then
            return "青铜短剑"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "青锋剑"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "游龙剑"
        end
    elseif 4 == 子类 then
        if 级别限制 < 21 then
            return "双短剑"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "竹节双剑"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "赤焰双剑"
        end
    elseif 5 == 子类 then
        if 级别限制 < 21 then
            return "五色缎带"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "无极丝"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "七彩罗刹"
        end
    elseif 6 == 子类 then
        if 级别限制 < 21 then
            return "铁爪"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "青龙牙"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "青刚刺"
        end
    elseif 7 == 子类 then
        if 级别限制 < 21 then
            return "折扇"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "铁面扇"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "神火扇"
        end
    elseif 8 == 子类 then
        if 级别限制 < 21 then
            return "细木棒"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "点金棒"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "满天星"
        end
    elseif 9 == 子类 then
        if 级别限制 < 21 then
            return "松木锤"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "狼牙锤"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "震天锤"
        end
    elseif 10 == 子类 then
        if 级别限制 < 21 then
            return "牛皮鞭"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "钢结鞭"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "青藤柳叶鞭"
        end
    elseif 11 == 子类 then
        if 级别限制 < 21 then
            return "黄铜圈"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "金刺轮"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "蛇形月"
        end
    elseif 12 == 子类 then
        if 级别限制 < 21 then
            return "柳叶刀"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "金背大砍刀"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "狼牙刀"
        end
    elseif 13 == 子类 then
        if 级别限制 < 21 then
            return "曲柳杖"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "墨铁拐"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "腾云杖"
        end
    elseif 14 == 子类 then
        if 级别限制 < 21 then
            return "硬木弓"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "宝雕长弓"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "连珠神弓"
        end
    elseif 15 == 子类 then
        if 级别限制 < 21 then
            return "琉璃珠"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "翡翠珠"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "如意宝珠"
        end
    elseif 16 == 子类 then
        if 级别限制 < 21 then
            return "钝铁重剑"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "壁玉长铗"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "惊涛雪"
        end
    elseif 17 == 子类 then
        if 级别限制 < 21 then
            return "红罗伞"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "琳琅盖"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "金刚伞"
        end
    elseif 18 == 子类 then
        if 级别限制 < 21 then
            return "素纸灯"
        elseif 级别限制 > 20 and 级别限制 < 51 then
            return "如意宫灯"
        elseif 级别限制 > 59 and 级别限制 < 90 then
            return "玉兔盏"
        end
    end
    return 名称
end

function 主控:取y偏移(头像)
    if "杀破狼" == 头像 then
        return 35
    elseif "巫蛮儿" == 头像 then
        return 19
    elseif "羽灵神" == 头像 then
        return 25
    elseif "吸血鬼" == 头像 then
        return 25
    elseif "鬼潇潇" == 头像 then
        return 1
    elseif "桃夭夭" == 头像 then
        return 58
    elseif "偃无师" == 头像 then
        return 33
    elseif "曼珠沙华" == 头像 or "狂豹人形" == 头像 then
        return 303
    elseif "鲛人" == 头像 then
        return 27
    elseif "犀牛将军_人" == 头像 then
        return 32
    elseif "野猪精" == 头像 then
        return 25
    elseif "修罗傀儡妖" == 头像 or "金身罗汉" == 头像 then
        return 235
    elseif "猫灵人形" == 头像 then
        return 275
    elseif "藤蔓妖花" == 头像 or "修罗傀儡鬼" == 头像 then
        return 260
    elseif "混沌兽" == 头像 then
        return 350
    elseif "蜃气妖" == 头像 then
        return 215
    elseif "泡泡" == 头像 then
        return 20
    elseif "福星" == 头像 or "长眉灵猴" == 头像 or "巨力神猿" == 头像 then
        return 28
    elseif "葫芦宝贝" == 头像 then
        return 336
    elseif "蝎子精" == 头像 then
        return 236
    elseif "猫灵兽形" == 头像 or "猫灵" == 头像 then
        return 377
    elseif "狂豹兽形" == 头像 then
        return 363
    elseif "物件_打铁炉" == 头像 then
        return -63
    elseif "银两" == 头像 or "食物" == 头像 or "口粮" == 头像 or "摄妖香" == 头像 or "药品" == 头像 then
        return -20
    end
    return 0
end


function 主控:取门派()
    if 角色信息.门派 == "方寸山" then
        return "方\n寸\n山"
    elseif 角色信息.门派 == "女儿村" then
        return "女\n儿\n村"
    elseif 角色信息.门派 == "神木林" then
        return "神\n木\n林"
    elseif 角色信息.门派 == "化生寺" then
        return "化\n生\n寺"
    elseif 角色信息.门派 == "大唐官府" then
        return "大\n唐\n官\n府"
    elseif 角色信息.门派 == "盘丝洞" then
        return "盘\n丝\n洞"
    elseif 角色信息.门派 == "阴曹地府" then
        return "阴\n曹\n地\n府"
    elseif 角色信息.门派 == "无底洞" then
        return "无\n底\n洞"
    elseif 角色信息.门派 == "魔王寨" then
        return "魔\n王\n寨"
    elseif 角色信息.门派 == "狮驼岭" then
        return "狮\n驼\n岭"
    elseif 角色信息.门派 == "天宫" then
        return "天\n宫"
    elseif 角色信息.门派 == "普陀山" then
        return "普\n陀\n山"
    elseif 角色信息.门派 == "凌波城" then
        return "凌\n波\n城"
    elseif 角色信息.门派 == "五庄观" then
        return "五\n庄\n观"
    elseif 角色信息.门派 == "龙宫" then
        return "龙\n宫"
    else
        return "无\n门\n派"
    end
end







function 主控:计算修炼经验(等级)
    等级= tonumber(等级) or 0
    if 等级==0 then return 110 end
    local 经验=110
    for n=1,等级 do
        经验=经验+20+n*20
    end
    return math.floor(经验)
end


function 主控:载入特效(特效)
    local a = 特效库(特效)
    if nil == a[1] then
        a = 特效库("无特效")
    elseif nil == a[2] and nil == a[1] then
        a = 特效库("无特效")
    end
  
    return __res:取资源动画(a[2], a[1],"置动画")
    
end



function 主控:播放音乐(map)
      self.地图音乐=map
      if __res.配置.音乐 and __res.配置.音乐>0 then
          self.音乐 = __res:取资源动画('music',取Bgm(self.地图音乐)..".mp3","音乐"):播放(true):置音量(__res.配置.音乐)
      elseif self.音乐 and self.音乐:是否播放() then
            self.音乐:停止()
      end
end

function 主控:停止音乐()
    if self.音乐 and self.音乐:是否播放() then
         self.音乐:停止()
    end
end


function 主控:恢复音乐()
         if self.地图音乐 then
              self.音乐 = __res:取资源动画('music',取Bgm(self.地图音乐)..".mp3","音乐"):播放(true):置音量(__res.配置.音乐)
         elseif 角色信息 and 角色信息.地图数据.编号 then
              self.地图音乐=取地图id(角色信息.地图数据.编号)
              self.音乐 = __res:取资源动画('music',取Bgm(self.地图音乐)..".mp3","音乐"):播放(true):置音量(__res.配置.音乐)
         end
end


function 主控:置音乐音量(声音)
    if 声音 and type(声音)=="number" then
      if not _tp.战斗中 then
          if self.音乐 and self.音乐:是否播放() then
              self.音乐:置音量(声音)
          else
              self:恢复音乐()
          end
      else
            if self.战斗音乐 and self.战斗音乐:是否播放()then
                self.战斗音乐:置音量(声音)
            else
              self:恢复战斗音乐()
            end
      end
    end
end


function 主控:播放战斗音乐()
      if __res.配置.音乐 and __res.配置.音乐>0 then
        local id =math.random(1,6)
        self.战斗id=id 
        self.战斗音乐 = __res:取资源动画('music',"zd"..self.战斗id..".mp3","音乐"):播放(true):置音量(__res.配置.音乐) 
      elseif self.战斗音乐 and self.战斗音乐:是否播放() then
          self.战斗音乐:停止()
      end
end

function 主控:停止战斗音乐()
    if self.战斗音乐 and self.战斗音乐:是否播放() then
        self.战斗音乐:停止()
    end
    if self.战斗动作 and self.战斗动作:是否播放() then
        self.战斗动作:停止()
    end
    if self.战斗特效 and self.战斗特效:是否播放() then
        self.战斗特效:停止()
    end
    
end
function 主控:恢复战斗音乐()
          if not self.战斗id then
              local id =math.random(1,6)
              self.战斗id=id 
          end
          self.战斗音乐 = __res:取资源动画('music',"zd"..self.战斗id..".mp3","音乐"):播放(true):置音量(__res.配置.音乐) 
end

function 主控:播放特效音乐(obj)
    if __res.配置.音效 and __res.配置.音效>0 and obj and  obj.文件 then
        self.战斗特效= __res:取资源动画(obj.资源,obj.文件,"音效"):播放():置音量(__res.配置.音效)
    end
end

function 主控:播放动作音乐(route, path)
    if __res.配置.音效 and __res.配置.音效>0 then
        self.战斗动作 = __res:取资源动画(route,path,"音效"):播放():置音量(__res.配置.音效)
    end
end




function 主控:队伍角色(模型)
    local 角色信息 = {
        飞燕女 = {
            模型 = "飞燕女",
            ID = 1,
            染色方案 = 3,
            性别 = "女",
            种族 = "人",
            门派 = {
                "大唐官府",
                "女儿村",
                "方寸山",
                "神木林"
            },
            门派师傅 = {
                "程咬金",
                "孙婆婆",
                "菩提祖师",
                "巫奎虎"
            },
            门派模型 = {
                "男人_程咬金",
                "孙婆婆",
                "菩提老祖",
                "巫奎虎"
            },
            武器 = { "双剑", "环圈" }
        },
        英女侠 = {
            模型 = "英女侠",
            ID = 2,
            染色方案 = 4,
            性别= "女",
            种族 = "人",
            门派 = {
                "大唐官府",
                "女儿村",
                "方寸山",
                "神木林"
            },
            门派师傅 = {
                "程咬金",
                "孙婆婆",
                "菩提祖师",
                "巫奎虎"
            },
            门派模型 = {
                "男人_程咬金",
                "孙婆婆",
                "菩提老祖",
                "巫奎虎"
            },
            武器= { "双剑", "鞭" }
        },
        巫蛮儿 = {
            模型 = "巫蛮儿",
            ID = 3,
            染色方案 = 1,
            性别= "女",
            种族 = "人",
            门派 = {
                "大唐官府",
                "女儿村",
                "方寸山",
                "神木林"
            },
            门派师傅 = {
                "程咬金",
                "孙婆婆",
                "菩提祖师",
                "巫奎虎"
            },
            门派模型 = {
                "男人_程咬金",
                "孙婆婆",
                "菩提老祖",
                "巫奎虎"
            },
            武器= { "宝珠", "法杖" }
        },
        逍遥生 = {
            模型 = "逍遥生",
            ID = 4,
            染色方案 = 1,
            性别= "男",
            种族 = "人",
            门派 = {
                "大唐官府",
                "化生寺",
                "方寸山",
                "神木林"
            },
            门派师傅 = {
                "程咬金",
                "空度禅师",
                "菩提祖师",
                "巫奎虎"
            },
            门派模型 = {
                "男人_程咬金",
                "男人_老和尚",
                "菩提老祖",
                "巫奎虎"
            },
            武器= { "扇", "剑" }
        },
        剑侠客 = {
            模型 = "剑侠客",
            ID = 5,
            染色方案 = 2,
            性别= "男",
            种族 = "人",
            门派 = {
                "大唐官府",
                "化生寺",
                "方寸山",
                "神木林"
            },
            门派师傅 = {
                "程咬金",
                "空度禅师",
                "菩提祖师",
                "巫奎虎"
            },
            门派模型 = {
                "男人_程咬金",
                "男人_老和尚",
                "菩提老祖",
                "巫奎虎"
            },
            武器= { "刀", "剑" }
        },
        狐美人 = {
            模型 = "狐美人",
            ID = 6,
            染色方案 = 7,
            性别= "女",
            种族 = "魔",
            门派 = {
                "盘丝洞",
                "阴曹地府",
                "魔王寨",
                "无底洞"
            },
            门派师傅 = {
                "白晶晶",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            门派模型 = {
                "白晶晶",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            武器= { "爪刺", "鞭" }
        },
        骨精灵 = {
            模型 = "骨精灵",
            ID = 7,
            染色方案 = 8,
            性别= "女",
            种族 = "魔",
            门派 = {
                "盘丝洞",
                "阴曹地府",
                "魔王寨",
                "无底洞"
            },
            门派师傅 = {
                "白晶晶",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            门派模型 = {
                "白晶晶",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            武器= { "魔棒", "爪刺" }
        },
        影精灵 = {
            模型 = "影精灵",
            ID = 7,
            染色方案 = 8,
            性别= "女",
            种族 = "魔",
            门派 = {
                "九黎城"

            },
            门派师傅 = {
                "刑天"

            },
            门派模型 = {
                "刑天"

            },
            武器= { "魔棒", "爪刺" ,"双斧"}
        },



        杀破狼 = {
            模型 = "杀破狼",
            ID = 8,
            染色方案 = 1,
            性别= "男",
            种族 = "魔",
            门派 = {
                "狮驼岭",
                "阴曹地府",
                "魔王寨",
                "无底洞"
            },
            门派师傅 = {
                "大大王",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            门派模型 = {
                "大大王",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            武器= { "宝珠", "弓弩" }
        },
        巨魔王 = {
            模型 = "巨魔王",
            ID = 9,
            染色方案 = 5,
            性别= "男",
            种族 = "魔",
            门派 = {
                "狮驼岭",
                "阴曹地府",
                "魔王寨",
                "无底洞"
            },
            门派师傅 = {
                "大大王",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            门派模型 = {
                "大大王",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            武器= { "刀", "斧钺" }
        },
        虎头怪 = {
            模型 = "虎头怪",
            ID = 10,
            染色方案 = 6,
            性别= "男",
            种族 = "魔",
            门派 = {
                "狮驼岭",
                "阴曹地府",
                "魔王寨",
                "无底洞"
            },
            门派师傅 = {
                "大大王",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            门派模型 = {
                "大大王",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            武器= { "斧钺", "锤子" }
        },
        舞天姬 = {
            模型 = "舞天姬",
            ID = 11,
            染色方案 = 11,
            性别= "女",
            种族 = "仙",
            门派 = {
                "天宫",
                "普陀山",
                "龙宫",
                "凌波城"
            },
            门派师傅 = {
                "李靖",
                "观音姐姐",
                "东海龙王",
                "二郎神"
            },
            门派模型 = {
                "李靖",
                "观音姐姐",
                "男人_东海龙王",
                "二郎神"
            },
            武器= { "飘带", "环圈" }
        },
        玄彩娥 = {
            模型 = "玄彩娥",
            ID = 12,
            染色方案 = 12,
            性别= "女",
            种族 = "仙",
            门派 = {
                "天宫",
                "普陀山",
                "龙宫",
                "凌波城"
            },
            门派师傅 = {
                "李靖",
                "观音姐姐",
                "东海龙王",
                "二郎神"
            },
            门派模型 = {
                "李靖",
                "观音姐姐",
                "男人_东海龙王",
                "二郎神"
            },
            武器= { "飘带", "魔棒" }
        },
        羽灵神 = {
            模型 = "羽灵神",
            ID = 13,
            染色方案 = 1,
            性别= "男",
            种族 = "仙",
            门派 = {
                "天宫",
                "五庄观",
                "龙宫",
                "凌波城"
            },
            门派师傅 = {
                "李靖",
                "镇元子",
                "东海龙王",
                "二郎神"
            },
            门派模型 = {
                "李靖",
                "镇元子",
                "男人_东海龙王",
                "二郎神"
            },
            武器= { "法杖", "弓弩" }
        },
        神天兵 = {
            模型 = "神天兵",
            ID = 14,
            染色方案 = 9,
            性别= "男",
            种族 = "仙",
            门派 = {
                "天宫",
                "五庄观",
                "龙宫",
                "凌波城"
            },
            门派师傅 = {
                "李靖",
                "镇元子",
                "东海龙王",
                "二郎神"
            },
            门派模型 = {
                "李靖",
                "镇元子",
                "男人_东海龙王",
                "二郎神"
            },
            武器= { "锤", "枪矛" }
        },
        龙太子 = {
            模型 = "龙太子",
            ID = 15,
            染色方案 = 10,
            性别= "男",
            种族 = "仙",
            门派 = {
                "天宫",
                "五庄观",
                "龙宫",
                "凌波城"
            },
            门派师傅 = {
                "李靖",
                "镇元子",
                "东海龙王",
                "二郎神"
            },
            门派模型 = {
                "李靖",
                "镇元子",
                "男人_东海龙王",
                "二郎神"
            },
            武器= { "扇", "枪矛" }
        },
        桃夭夭 = {
            模型 = "桃夭夭",
            ID = 16,
            染色方案 = 1,
            性别= "女",
            种族 = "仙",
            门派 = {
                "天宫",
                "普陀山",
                "龙宫",
                "凌波城"
            },
            门派师傅 = {
                "李靖",
                "观音姐姐",
                "东海龙王",
                "二郎神"
            },
            门派模型 = {
                "李靖",
                "观音姐姐",
                "男人_东海龙王",
                "二郎神"
            },
            武器= { "灯笼" }
        },
        偃无师 = {
            模型 = "偃无师",
            ID = 17,
            染色方案 = 1,
            性别= "男",
            种族 = "人",
            门派 = {
                "大唐官府",
                "化生寺",
                "方寸山",
                "神木林"
            },
            门派师傅 = {
                "程咬金",
                "空度禅师",
                "菩提祖师",
                "巫奎虎"
            },
            门派模型 = {
                "男人_程咬金",
                "男人_老和尚",
                "菩提老祖",
                "巫奎虎"
            },
            武器= { "剑", "巨剑" }
        },
        鬼潇潇 = {
            模型 = "鬼潇潇",
            ID = 18,
            染色方案 = 2,
            性别= "女",
            种族 = "魔",
            门派 = {
                "盘丝洞",
                "阴曹地府",
                "魔王寨",
                "无底洞"
            },
            门派师傅 = {
                "白晶晶",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            门派模型 = {
                "白晶晶",
                "地藏王",
                "牛魔王",
                "地涌夫人"
            },
            武器= { "爪刺", "伞" }
        }
    }
    return 角色信息[模型]
end

return 主控
