local 潜能果 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "潜能果", 80+67 + abbr.py.x, 20+42 + abbr.py.y, 555, 350)

function 潜能果:初始化()
  local nsf = require("SDL.图像")(555+29, 430)
  if nsf["渲染开始"](nsf) then
    -- __res:getPNGCC(5, 0, 0, 683, 450):显示(0,5)
    -- 字体20:置颜色(__取颜色("浅黑"))
    -- 字体20:取图像("伙 伴"):显示(683/2-20,9)
    xiao置窗口背景("潜能果", 0, 12, 512,332, true):显示(0, 0)
    取白色背景(0, 0, 472, 170-18-16, true):显示(18, 67-15)
    -- 字体18:置颜色(__取颜色("浅黑"))
    -- 字体18:取图像("潜能果是一种能把经验转化为潜力的果子。\n90级＞人物等级≥60级，可食用50个；\n渡劫155级＞人物等级≥90级，可食用100个；\n渡劫170级＞人物等级≥155级，可食用150个；\n人物等级≥渡劫170级，可食用200个。"):显示(20+20,79-10)
    -- 字体18:置颜色(__取颜色("白色"))
    -- 字体18:取图像("当前已食用潜能果："):显示(40,201)
    -- 字体18:取图像("还能再食用："):显示(40+259,201)
    -- 字体18:取图像("当前可用经验点数："):显示(40,79+50*3)
    -- 字体18:取图像("下一个果所需经验："):显示(40,79+50*3+28)
    --取白色背景(0, 0, 440, 300, true)["显示"](取白色背景(0, 0, 440, 300, true), 20, 56)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 潜能果:打开(内容)
  self:置可见(true)
  self.是否气血果=内容.气血果
  self:更新数据(内容)
end
function 潜能果:更新数据(内容)
	self.可用经验 = 内容.可用经验 or 0
	self.已食用潜能果 = 内容.已食用潜能果 or 0
	self.剩余潜能果 = 内容.剩余潜能果 or 0
	self.下个经验 = 内容.下个经验 or 0
  self:chongz()
end
function 潜能果:chongz()
  local nsf = require("SDL.图像")(555, 368)
  if nsf["渲染开始"](nsf) then
    字体18:置颜色(__取颜色("黄色"))
    字体18:取图像(self.已食用潜能果):显示(145+56,201)
    字体18:取图像(self.剩余潜能果):显示(145+259,201)
    字体18:取图像(self.可用经验):显示(145+56,79+50*3)
    字体18:取图像(self.下个经验):显示(145+56,79+50*3+28)
    if self.是否气血果 then
      self:我的按钮置文字(self.食用潜能果,__res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(143, 38), "食用气血果")
      字体18:置颜色(__取颜色("红色"))
      字体18:取图像("气血果是一种能把经验转化为气血的果子，1=10点气血。\n90级＞人物等级≥60级，可食用50个；\n渡劫155级＞人物等级≥90级，可食用100个；\n渡劫170级＞人物等级≥155级，可食用150个；\n人物等级≥渡劫170级，可食用200个。"):显示(20+20,79-10)
      字体18:置颜色(__取颜色("白色"))
      字体18:取图像("当前已食用气血果："):显示(40,201)
      字体18:取图像("还能再食用："):显示(40+259,201)
      字体18:取图像("当前可用经验点数："):显示(40,79+50*3)
      字体18:取图像("下一个果所需经验："):显示(40,79+50*3+28)
    else
      self:我的按钮置文字(self.食用潜能果,__res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(143, 38), "食用潜能果")
      字体18:置颜色(__取颜色("浅黑"))
      字体18:取图像("潜能果是一种能把经验转化为潜力的果子。\n90级＞人物等级≥60级，可食用50个；\n渡劫155级＞人物等级≥90级，可食用100个；\n渡劫170级＞人物等级≥155级，可食用150个；\n人物等级≥渡劫170级，可食用200个。"):显示(20+20,79-10)
      字体18:置颜色(__取颜色("白色"))
      字体18:取图像("当前已食用潜能果："):显示(40,201)
      字体18:取图像("还能再食用："):显示(40+259,201)
      字体18:取图像("当前可用经验点数："):显示(40,79+50*3)
      字体18:取图像("下一个果所需经验："):显示(40,79+50*3+28)
    end
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
local 食用潜能果 = 潜能果["创建我的按钮"](潜能果, __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(143, 38), "食用潜能果", 40,79+50*3+28+34, "食用潜能果")
function 食用潜能果:左键弹起(x, y, msg)
  if 潜能果.是否气血果 then
    local  mc="老猕猴"
    __UI界面["窗口层"]["对话栏"]:打开("长眉灵猴",mc,"你当前已食用"..潜能果.已食用潜能果.."个气血果，需要消耗"..潜能果.下个经验.."点经验才能食用下一个，你是否需要继续食用？",{"是  ","否"})
  else
    __UI界面["窗口层"]["对话栏"]:打开("长眉灵猴",mc,"你当前已食用"..潜能果.已食用潜能果.."个潜能果，需要消耗"..潜能果.下个经验.."点经验才能食用下一个，你是否需要继续食用？",{"是 ","否"})
  end
  -- __UI界面["窗口层"]["对话栏"]:打开("长眉灵猴","老猕猴","你当前已食用"..潜能果.已食用潜能果.."个潜能果，需要消耗"..潜能果.下个经验.."点经验才能食用下一个，你是否需要继续食用？",{"是 ","否"})
end

local 取消 = 潜能果["创建我的按钮"](潜能果, __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(143, 38), "取消", 40+259,79+50*3+28+34, "取消")
function 取消:左键弹起(x, y, msg)
  潜能果["置可见"](潜能果, false)
end

local 关闭 = 潜能果["创建我的按钮"](潜能果, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 500+29-50, 0)
function 关闭:左键弹起(x, y, msg)
  潜能果["置可见"](潜能果, false)
end