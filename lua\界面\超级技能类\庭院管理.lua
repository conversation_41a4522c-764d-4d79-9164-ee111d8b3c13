--[[
Author: GGELUA
Date: 2024-11-24 00:19:29
Last Modified by: GGELUA
Last Modified time: 2024-11-24 00:34:39
--]]
local 庭院管理 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "庭院管理", 138 + abbr.py.x, 17 + abbr.py.y, 710, 550)
function 庭院管理:初始化()
  local nsf = require("SDL.图像")(695, 550)
  if nsf["渲染开始"](nsf) then
    置窗口背景("庭院管理", 0, 12, 710, 500, true)["显示"](置窗口背景("庭院管理", 0, 12, 710, 500, true), 0, 0)
   -- 取白色背景(0, 0, 400, 230, true)["显示"](取白色背景(0, 0, 400, 230, true), 17, 150)
  --  字体18["置颜色"](字体18, __取颜色("黑色"))
   -- 字体18["取图像"](字体18, "充值记录：")["显示"](字体18["取图像"](字体18, "充值记录："), 25, 160)


    __res:getPNGCC(2, 820, 885, 650, 370)["拉伸"](__res:getPNGCC(2, 820, 885, 272, 115), 650, 370)["显示"](__res:getPNGCC(2, 820, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 820, 885, 272, 115), 650, 370), 20, 110)
   字体18["置颜色"](字体18, __取颜色("黑色"))
   字体18["取图像"](字体18, "农业等级：")["显示"](字体18["取图像"](字体18, "农业等级："), 50, 150)
   字体18["取图像"](字体18, "升级经验：")["显示"](字体18["取图像"](字体18, "升级经验："), 50, 175)
   字体18["取图像"](字体18, "护卫概率")["显示"](字体18["取图像"](字体18, "护卫概率"), 50, 205)
   字体18["取图像"](字体18, "当前概率：")["显示"](字体18["取图像"](字体18, "当前概率："), 50, 235)
   字体18["取图像"](字体18, "庭院积分")["显示"](字体18["取图像"](字体18, "庭院积分"), 50, 265)
   字体18["取图像"](字体18, "当前积分：")["显示"](字体18["取图像"](字体18, "当前积分："), 50, 295)
   字体18["取图像"](字体18, "累计积分：")["显示"](字体18["取图像"](字体18, "累计积分："), 50, 325)
   字体18["取图像"](字体18, "植物数量：")["显示"](字体18["取图像"](字体18, "植物数量："), 50, 355)
   字体18["取图像"](字体18, "动物数量：")["显示"](字体18["取图像"](字体18, "动物数量："), 50, 385)
   字体18["取图像"](字体18, "家具请前往商城其他类使用积分购买")["显示"](字体18["取图像"](字体18, "家具请前往商城其他类使用积分购买"), 50, 415)

    if nsf["渲染结束"](nsf) then
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
end

local data
function 庭院管理:打开(数据)
  data= 数据
  self:置可见(true)


end




local 赞助界面 = 庭院管理["创建我的控件"](庭院管理, "赞助界面", 0, 0, 650, 486)
function 赞助界面:初始化()

  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, data.农业等级.等级)["显示"](字体18["取图像"](字体18,data.农业等级.等级), 130, 150)
    字体18["取图像"](字体18, data.农业等级.经验.."/")["显示"](字体18["取图像"](字体18,data.农业等级.经验.."/"), 130, 175)
    字体18["取图像"](字体18, data.农业等级.最大经验)["显示"](字体18["取图像"](字体18,data.农业等级.最大经验), 150, 175)
    字体18["取图像"](字体18, data.护卫概率.."%抓到偷盗庭院物品的小偷")["显示"](字体18["取图像"](字体18,data.护卫概率.."%抓到偷盗庭院物品的小偷"), 130, 235)
    字体18["取图像"](字体18, data.庭院积分.当前)["显示"](字体18["取图像"](字体18,data.庭院积分.当前), 130, 295)
    字体18["取图像"](字体18, data.庭院积分.累计)["显示"](字体18["取图像"](字体18,data.庭院积分.累计), 130, 325)
    字体18["取图像"](字体18, data.植物位置)["显示"](字体18["取图像"](字体18,data.植物位置), 130, 355)
    字体18["取图像"](字体18, data.动物位置)["显示"](字体18["取图像"](字体18,data.动物位置), 130, 385)


    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end



local 自动抓鬼 = 庭院管理["创建我的控件"](庭院管理, "自动抓鬼", 0, 0, 650, 486)
function 自动抓鬼:初始化()
  local nsf = require("SDL.图像")(650, 550)
  self:置精灵(nsf["到精灵"](nsf))
end


function 自动抓鬼:显示(x,y)
    if self.图像 then
    self.图像["显示"](self.图像, x, y)
    end
end



local 关闭 = 庭院管理["创建我的按钮"](庭院管理, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 645, 0)
function 关闭:左键弹起(x, y, msg)
  庭院管理["置可见"](庭院管理, false)
end

for i, v in ipairs({
  {
    name = "庭院管理",
    x = 22-5,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 100, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 100, 43),
    font = "庭院管理"
  },

 
}) do
  local 临时函数 = 庭院管理["创建我的单选按钮"](庭院管理, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "确认充值"  then


    elseif    v.name == "锁定信息"  then
      __UI弹出.提示框:打开("#Y/锁定确认成功！")

      end
  end
end

local  收获动物 = 庭院管理["创建我的按钮"](庭院管理, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 35), "收获动物", 50, 440, "收获动物")
function 收获动物:左键弹起(x, y, msg)

    if os.time()  then
      发送数据(116.1,{类型="庭院动物"})

    end

end

local  收获植物 = 庭院管理["创建我的按钮"](庭院管理, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 35), "收获植物", 170, 440, "收获植物")
function 收获植物:左键弹起(x, y, msg)

    if os.time()  then
      发送数据(116.1,{类型="庭院植物"})

    end

end

local  扩充种植 = 庭院管理["创建我的按钮"](庭院管理, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 35), "扩充种植", 290, 440, "扩充种植")
function 扩充种植:左键弹起(x, y, msg)

    if os.time()  then
      发送数据(135.1)

    end

end

local  提升护卫 = 庭院管理["创建我的按钮"](庭院管理, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 35), "提升护卫", 410, 440, "提升护卫")
function 提升护卫:左键弹起(x, y, msg)

    if os.time()  then
      发送数据(136.1)

    end

end