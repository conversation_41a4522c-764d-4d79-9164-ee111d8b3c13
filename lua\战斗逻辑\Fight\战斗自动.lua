local 战斗自动 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "战斗自动", 334+50, 368, 310, 170)
function 战斗自动:初始化()
  local nsf = require("SDL.图像")(310, 170)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 310, 170, true):显示(0, 0)
    字体18["置颜色"](字体18, 255, 255, 255)
    字体18["取图像"](字体18, "剩余自动回合数:"):显示(15, 18)
    字体18["取图像"](字体18, "人   物   技   能:"):显示(15, 18+35)
    字体18["取图像"](字体18, "宠   物   技   能:"):显示(15, 18+35*2)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 战斗自动:取自动语句(类型)
  if 类型==1 then --人物
		local 编号=0
		for n=1,#__战斗主控.战斗单位 do
			if __战斗主控.战斗单位[n].数据.类型=="角色" and __战斗主控.战斗单位[n].数据.id==角色信息.数字id then
				编号=n
			end
		end
    local 语句="攻击"
		if 编号==0 then return 语句 end
		if __战斗主控.战斗单位[编号].数据.自动指令~=nil then
			if __战斗主控.战斗单位[编号].数据.自动指令.类型=="法术" then
        if type(__战斗主控.战斗单位[编号].数据.自动指令.参数) == 'table' then
          语句=__战斗主控.战斗单位[编号].数据.自动指令.参数[1][1]
        else
          语句=__战斗主控.战斗单位[编号].数据.自动指令.参数
        end

			else
				语句=__战斗主控.战斗单位[编号].数据.自动指令.类型
			end
		end
		return 语句 or "攻击"
	elseif 类型==2 then --人物
		local 编号=0
		for n=1,#__战斗主控.战斗单位 do
			if __战斗主控.战斗单位[n].数据.类型=="bb" and __战斗主控.战斗单位[n].数据.id==角色信息.数字id then
				编号=n
			end
		end
		if 编号==0 then return "无" end
		local 语句="攻击"
		if __战斗主控.战斗单位[编号].数据.自动指令~=nil then
			if __战斗主控.战斗单位[编号].数据.自动指令.类型=="法术" then
				语句=__战斗主控.战斗单位[编号].数据.自动指令.参数
			else
				语句=__战斗主控.战斗单位[编号].数据.自动指令.类型
			 end
		end
		return 语句 or "攻击"
	end
end
function 战斗自动:取自动状态()
	local 状态=true
	 -- 控制状态 = "开启"
	for n=1,#__战斗主控.战斗单位 do
		if __战斗主控.战斗单位[n].数据.类型=="角色" and __战斗主控.战斗单位[n].数据.id==角色信息.数字id then
      return __战斗主控.战斗单位[n].数据.自动战斗
			-- if __战斗主控.战斗单位[n].数据.自动战斗 then
			-- 	状态=false
      --   break
			-- end
		end
	end
	return 状态
 end
function 战斗自动:重置()
  local nsf = require("SDL.图像")(310, 170)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18["取图像"](字体18, "∞"):显示(195, 18)
    字体18["取图像"](字体18, self:取自动语句(1)):显示(190, 18+35)
    字体18["取图像"](字体18, self:取自动语句(2)):显示(190, 18+35*2)
    -- local font = ""
    -- if self.自动kaiguan then
    --   font = "开"
    -- else
    --   font = "关"
    -- end
    -- 字体18["取图像"](字体18, font)["显示"](字体18["取图像"](字体18, font), 115, 18)
    -- if self.队长控制 then
    --   font = "开"
    -- else
    --   font = "关"
    -- end
    -- 字体18["取图像"](字体18, font)["显示"](字体18["取图像"](字体18, font), 260, 18)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  战斗自动.自动kaiguan=self:取自动状态()
  if 战斗自动.自动kaiguan then
    战斗自动.自动开关按钮:我的按钮置文字(战斗自动.自动开关按钮, __res:getPNGCC(2, 510, 919, 92, 35, true), "取 消")
  else
    战斗自动.自动开关按钮:我的按钮置文字(战斗自动.自动开关按钮, __res:getPNGCC(2, 510, 919, 92, 35, true), "自 动")
  end
end

function 战斗自动:取主动技能(lx)
  for n=1,#__战斗主控.战斗单位 do
    if __战斗主控.战斗单位[n].数据.类型==lx and __战斗主控.战斗单位[n].数据.id==角色信息.数字id then
      return __战斗主控.战斗单位[n].主动技能
    end
  end
  return {}
end

local 人物技能 = 战斗自动["创建我的按钮"](战斗自动, __res:getPNGCC(2, 510, 919, 92, 35, true), "暂 离", 14, 119, "人 物")
function 人物技能:左键弹起(x, y)
  __UI弹出["自动法术选择"]:打开(战斗自动:取主动技能("角色"),"角色")
end
local 宝宝技能 = 战斗自动["创建我的按钮"](战斗自动, __res:getPNGCC(2, 510, 919, 92, 35, true), "重 置", 110, 119, "宝 宝")
function 宝宝技能:左键弹起(x, y)
  __UI弹出["自动法术选择"]:打开(战斗自动:取主动技能("bb"),"bb")
end
local 自动开关按钮 = 战斗自动["创建我的按钮"](战斗自动, __res:getPNGCC(2, 510, 919, 92, 35, true), "自动开关按钮", 208, 119, "自 动")
function 自动开关按钮:左键弹起(x, y)
  -- 战斗自动["置可见"](战斗自动, false)
  发送数据(5507)
  if 战斗自动.自动kaiguan then
    战斗自动["置可见"](战斗自动, false)
  end
end


-- local 自动开关按钮 = 战斗自动["创建我的按钮"](战斗自动, __res:getPNGCC(2, 510, 919, 92, 35, true), "自动开关按钮", 16, 119, "自动")
-- function 自动开关按钮:左键弹起(x, y)
--   发送数据(5507)
-- end
-- local 队控开关按钮 = 战斗自动["创建我的按钮"](战斗自动, __res:getPNGCC(2, 510, 919, 92, 35, true), "队控开关按钮", 111, 119, "队长控制")
-- function 队控开关按钮:左键弹起(x, y)
--   发送数据(5521)
-- end
-- local 自动取消按钮 = 战斗自动["创建我的按钮"](战斗自动, __res:getPNGCC(2, 510, 919, 92, 35, true), "自动取消按钮", 208, 119, "关闭")
-- function 自动取消按钮:左键弹起(x, y)
--   战斗自动["置可见"](战斗自动, false)
-- end
