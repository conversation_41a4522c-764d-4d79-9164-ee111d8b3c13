local 法宝 = 窗口层:创建窗口("法宝", 0, 0, 560, 340)

local 神器大图与坐标 = {
  大唐官府 = {0x01AC0090,320,55,0x01AC0046}, 
  化生寺 = {0x01AC0092,330,75,0x01AC0052},
  方寸山 = {0x01AC0091,320,55,0x01AC0057},
  女儿村 = {0x01AC0093,300,25,0x01AC0055},
  天宫 = {0x01AC0099,310,45,0x01AC0059},
  普陀山 = {0x01AC0104,305,40,0x01AC0058},
  龙宫 = {0x01AC0102,310,45,0x01AC0051},
  五庄观 = {0x01AC0100,320,55,0x01AC0047},
  魔王寨 = {0x01AC0096,310,55,0x01AC0053},
  狮驼岭 = {0x01AC0097,325,60,0x01AC0050},
  盘丝洞 = {0x01AC0098,310,50,0x01AC0056},
  阴曹地府 = {0x01AC0095,310,45,0x01AC0049},
  神木林 = {0x01AC0094,300,30,0x01AC0045},----神木林 = {0x01AC0094,300,30,0x01AC0045}, -130  -65

  凌波城 = {0x01AC0101,300,50,0x01AC0048},
  无底洞 = {0x01AC0103,320,55,0x01AC0054},
  花果山 = {0x0C000015,320,55,0x01AC0054},
  九黎城 = {"mxj.png",320,100,"mxj.png"},
}

local 神器门派属性 = {
  大唐官府 = {"伤　　害","物理暴击"},化生寺 = {"防　　御","治疗能力"},方寸山 = {"封印命中","法术伤害"},女儿村 = {"封印命中","固定伤害"},天宫 = {"法术伤害","封印命中"},
  普陀山 = {"固定伤害","治疗能力"},龙宫 = {"法术伤害","法术暴击"},五庄观 = {"伤　　害","封印命中"},魔王寨 = {"法术伤害","法术暴击"},狮驼岭 = {"伤　　害","物理暴击"},
  盘丝洞 = {"封印命中","法术防御"},阴曹地府 = {"伤　　害","法术防御"},神木林 = {"法术伤害","法术暴击"},凌波城 = {"伤　　害","物理暴击"},无底洞 = {"封印命中","治疗能力"},
  花果山 = {"伤害","物理暴击"},九黎城 = {"伤害","物理暴击"},
}

local 神器技能 = {
  藏锋敛锐 = {[1]= "横扫千军消耗的气血有50%的几率转化为等量护盾。",[2]="横扫千军消耗的气血有100%的几率转化为等量护盾。"},                 ---大唐
  惊锋     = {[1]= "每次攻击提升自身10点伤害，最多叠加12层，死亡后清零。",[2]="每次攻击提升自身20点伤害，最多叠加12层，死亡后清零。"},     ---大唐
  披坚执锐 = {[1]= "遭受攻击时，有4%的几率免受90%的伤害。",[2]="遭受攻击时，有8%的几率免受90%的伤害。"},
  金汤之固 = {[1]= "气血小于30%时，提升240点抗封等级。",[2]="气血小于30%时，提升480点抗封等级。"},
  风起云墨 = {[1]= "受到你治疗的首目标本回合内受到的所有伤害降低4%。",[2]="受到你治疗的首目标本回合内受到的所有伤害降低8%。"},
  挥毫     = {[1]= "受到你的治疗时，目标每带有一个增益状态，额外恢复25点气血。",[2]="受到你的治疗时，目标每带有一个增益状态，额外恢复50点气血。"},
  盏中晴雪 = {[1]= "若你的速度高于施法者，提升速度差×0.5的抗封等级。",[2]="若你的速度高于施法者，提升速度差×1的抗封等级。"},
  泪光盈盈 = {[1]= "笑里藏刀额外减少目标6点愤怒。",[2]="笑里藏刀额外减少目标12点愤怒。"},
  凭虚御风 = {[1]= "每点被消耗的风灵增加40点法术伤害结果，最多叠加三层，死亡后清零。",[2]="每点被消耗的风灵增加80点法术伤害结果，最多叠加三层，死亡后清零。"},
  钟灵     = {[1]= "被使用3级药是有一定几率获得1层风灵。",[2]="被使用3级药是有较大几率获得1层风灵。"},
  亡灵泣语 = {[1]= "你的锢魂术会使得目标额外受到5%的物法伤害。",[2]="你的锢魂术会使得目标额外受到10%的物法伤害。"},
  魂魇     = {[1]= "被你的物理伤害攻击的单位在当回合内的法术伤害结果减少100点。",[2]="被你的物理伤害攻击的单位在当回合内的法术伤害结果减少200点。"},
  业焰明光 = {[1]= "你的法术有25%的几率造成额外25%的伤害。",[2]="你的法术有25%的几率造成额外50%的伤害。"},
  流火     = {[1]= "攻击气血百分比小于你的单位时，增加8%的伤害。",[2]="攻击气血百分比小于你的单位时，增加16%的伤害。"},
  蛮血     = {[1]= "增加（1-自身气血/气血上限）×8%的狂暴几率。",[2]="增加（1-自身气血/气血上限）×16%的狂暴几率。"},
  狂战     = {[1]= "每有一个己方召唤兽被击飞，增加30点伤害力，可叠加4层，死亡后消失。",[2]="每有一个己方召唤兽被击飞，增加60点伤害力，可叠加4层，死亡后消失。"},
  镜花水月 = {[1]= "受到治疗时，有8%的几率获得一个等额度的护盾。",[2]="受到治疗时，有16%的几率获得一个等额度的护盾。"},
  澄明     = {[1]= "每回合结束时，增加3点抵抗封印等级。",[2]="每回合结束时，增加6点抵抗封印等级。"},
  情思悠悠 = {[1]= "地涌金莲的目标获得治疗量10%的护盾。",[2]="地涌金莲的目标获得治疗量20%的护盾。"},
  相思     = {[1]= "偶数回合结束时，增加3点速度。",[2]="每个回合结束时，增加3点速度。"},
  弦外之音 = {[1]= "回合结束时，每个主动法宝效果会增加你3点愤怒。",[2]="回合结束时，每个主动法宝效果会增加你6点愤怒。"},
  裂帛     = {[1]= "伤害性法术首目标伤害增加8%。",[2]="伤害性法术首目标伤害增加16%。"},
  定风波   = {[1]= "受到的法术暴击伤害降低30%。",[2]="受到的法术暴击伤害降低60%。"},
  沧浪赋   = {[1]= "攻击气血小于30%的目标时，额外提升120点的法术伤害。",[2]="攻击气血小于30%的目标时，额外提升240点的法术伤害。"},
  斗转参横 = {[1]= "带有状态生命之泉时，日月乾坤命中率增加3%。",[2]="带有状态生命之泉时，日月乾坤命中率增加6%。"},
  静笃     = {[1]= "每次击杀敌方单位，增加60点伤害。",[2]= "每次击杀敌方单位，增加120点伤害。"},
  玉魄     = {[1]= "消耗愤怒的100%转化为下一次释放恢复性技能时的治疗能力。",[2]="消耗愤怒的200%转化为下一次释放恢复性技能时的治疗能力。"},
  璇华     = {[1]= "使用五行法术时，增加10%的伤害。",[2]="使用五行法术时，增加20%的伤害。"},
  威服天下 = {[1]= "暴击伤害增加12%。",[2]="暴击伤害增加24%。"},
  酣战     = {[1]= "每点消耗的战意，会提升30点物理暴击等级，可叠加6次，死亡后清零。",[2]="每点消耗的战意，会提升60点物理暴击等级，可叠加6次，死亡后清零。"},
  万物滋长 = {[1]= "使用特技时将会获得（消耗愤怒值×等级×5%）的护盾和气血回复。",[2]="使用特技时将会获得（消耗愤怒值×等级×10%）的护盾和气血回复。"},
  开辟     = {[1]= "每次使用如意神通，提升20点自身伤害，最多叠加6层，死亡后清零。",[2]="每次使用如意神通，提升40点自身伤害，最多叠加6层，死亡后清零。"},
  鸣空     = {[1]= "每当令目标浮空时，你获得12点狂暴等级并且造成的物理伤害结果提高2%，最多叠加6层，阵亡后清零",[2]="每当令目标浮空时，你获得24点狂暴等级并且造成的物理伤害结果提高2%，最多叠加6层，阵亡后清零"},
  骇神     = {[1]= "受到物理伤害时，若攻击者物理伤害低于你，伤害结果降低10%",[2]="受到物理伤害时，若攻击者物理伤害低于你，伤害结果降低20%"},
}
local 门派神器名称 = {
    大唐官府 = "轩辕剑",化生寺 = "墨魂笔",方寸山 = "黄金甲",女儿村 = "泪痕碗",天宫 = "独弦琴",
    普陀山 = "华光玉",龙宫 = "清泽谱",五庄观 = "星斗盘",魔王寨 = "明火珠",狮驼岭 = "噬魂齿",
    盘丝洞 = "昆仑镜",阴曹地府 = "四神鼎",神木林 = "月光草",凌波城 = "天罡印",无底洞 = "玲珑结",
    花果山 = "鸿蒙石",九黎城 = "魔息角",
}





function 法宝:初始化()
   self:置精灵(__res:取资源动画("jszy/jmtb", 0x1F73D091,"精灵")) 
   self.选中灵宝 = nil
   self.选中法宝 = nil
   self.选中道具 = nil
   self.移动 = nil 
   self.窗口类型 ="法宝"
  self.miaoshu={}
	self.miaoshu["速度"]={"速　　度",177}
	self.miaoshu["气血"]={"气　　血",202}
	self.miaoshu["伤害"]={"伤　　害",227}
	self.miaoshu["防御"]={"防    御",227}
	self.miaoshu["封印命中"]={"封印命中",227}
	self.miaoshu["法术伤害"]={"法术伤害",227}
	self.miaoshu["固定伤害"]={"固定伤害",227}
	self.miaoshu["物理暴击"]={"物理暴击",252}
	self.miaoshu["治疗能力"]={"治疗能力",252}
	self.miaoshu["法术暴击"]={"法术暴击",252}
	self.miaoshu["法术防御"]={"法术防御",252}
	self.miaoshu["抵抗封印"]={"抵抗封印",277}
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true

  

end







function 法宝:打开()
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
    self.选中灵宝 = nil
    self.选中法宝 = nil
    self.选中道具 = nil
    self.移动 = nil 
    self.神器属性 =nil
    self.标题=nil
    self.属性数值 = {速度=0,气血=0,抵抗封印=0}
    self.窗口类型 ="法宝"
    self.法宝显示:置可见(true)
    self.法宝:置选中(true)
    self:按钮重置()
end

function 法宝:显示(x,y)
    if self.标题 then
        self.标题:显示(x+255,y+14)
    end
end

function 法宝:按钮重置()
      self.使用:置可见(false)
      self.修炼:置可见(false)
      self.道具:置可见(false)
      self.行囊:置可见(false)
      self.装备:置可见(false)
      
      self.修复:置可见(false)
      self.补充灵气:置可见(false)
      if self.窗口类型=="神器" then
          self.装备:置坐标(290,310)
          self.修复:置坐标(375,310)
          self.补充灵气:置坐标(460,310)
          self.装备:置可见(true)
          self.修复:置可见(true)
          self.补充灵气:置可见(true)
    else
          self.使用:置坐标(20,300)
          self.修炼:置坐标(110,300)
          self.补充灵气:置坐标(200,300)
          self.道具:置坐标(370,305)
          self.行囊:置坐标(450,305)
          self.使用:置可见(true)
          self.修炼:置可见(true)
          self.道具:置可见(true)
          self.行囊:置可见(true)
          self.补充灵气:置可见(true)
    end
    if 角色信息.神器数据 then
        self:更新神器(角色信息.神器数据)
    end
    self.法宝显示:重置显示()
end



function 法宝:更新修炼(数据)
  if self.窗口类型=="法宝" then
        if 角色信息.法宝 and 角色信息.法宝[数据.id] then
            角色信息.法宝[数据.id].当前经验=数据.当前经验
            角色信息.法宝[数据.id].升级经验=数据.升级经验
            角色信息.法宝[数据.id].气血=数据.境界
            角色信息.法宝[数据.id].魔法=数据.灵气
        end
    elseif self.窗口类型=="灵宝" then
        if 角色信息.灵宝 and 角色信息.灵宝[数据.id] then
            角色信息.灵宝[数据.id].当前经验=数据.当前经验
            角色信息.灵宝[数据.id].升级经验=数据.升级经验
            角色信息.灵宝[数据.id].气血=数据.境界
            角色信息.灵宝[数据.id].魔法=数据.灵气
        end
    end
    
    self.法宝显示:重置显示()
end

function 法宝:更新神器(数据)
  self.装备:置文字(74,22,"装备")
  if not 数据 or not 数据.神器技能 then return end
  if 角色信息.神器佩戴 then
    self.装备:置文字(74,22,"卸下")
  end
	self.神器属性 = 数据
	self.属性数值 = {速度=0,气血=0,抵抗封印=0}
  self.法宝显示.神器说明:清空()
  self.法宝显示.神器说明:置文字(文本字体)
  self.法宝显示.神器网格:置物品(门派神器名称[角色信息.门派])
	for n=1,2 do
	    if 神器门派属性[角色信息.门派][n] == "伤　　害" then
	    	self.属性数值.伤害 = 0
	    elseif 神器门派属性[角色信息.门派][n] == "防　　御" then
	    	self.属性数值.防御 = 0
	    else
	        self.属性数值[神器门派属性[角色信息.门派][n]] = 0
	    end
	end
	if self.神器属性 then
      if not self.神器属性.神器技能.名称 then 
          self.神器属性.神器技能.名称= self.神器属性.神器技能.name 
      end
      if not self.神器属性.神器技能.等级 then 
          self.神器属性.神器技能.等级 = self.神器属性.神器技能.lv
      end
      self.法宝显示.神器说明:置文本("#Y"..神器技能[self.神器属性.神器技能.名称][self.神器属性.神器技能.等级])
      for k,v in pairs(self.神器属性.神器解锁) do
          for n=1,#v.神器五行属性 do
              self.属性数值[v.神器五行属性[n]] = self.属性数值[v.神器五行属性[n]] + v.神器五行数值[n]
          end
      end
  end
 

end



function 法宝:重置抓取() 
    self.选中灵宝 = nil
    self.选中法宝 = nil
    self.选中道具 = nil
    self.选中神器 = nil
    self.移动 = nil 
    for i, v in ipairs(self.法宝显示.法宝网格.子控件) do
        if v._spr and v._spr.确定 then
            v._spr.确定=nil
        end
    end
    for i, v in ipairs(self.法宝显示.灵宝网格.子控件) do
      if v._spr and v._spr.确定 then
          v._spr.确定=nil
      end
    end
    self.法宝显示.道具网格.选中编号=nil
    self.法宝显示.道具网格.焦点编号=nil
    if 鼠标层.附加 and ggetype(鼠标层.附加)=="物品格子"  then
       鼠标层.附加=nil
    end
end

local  法宝显示 = 法宝:创建控件("法宝显示", 20,50, 550,350)

function 法宝显示:重置显示()
  self.法宝网格:置可见(false)
  self.灵宝网格:置可见(false)
  self.神器网格:置可见(false)
  self.道具网格:置可见(false)
  self.神器说明:置可见(false)
  法宝.标题 = 标题字体:置颜色(255,255,255,255):取精灵(法宝.窗口类型)
  self:创建纹理精灵(function ()
              __res:取资源动画("jszy/jmtb",0x1F73D093,"图像"):显示(85,-5)
              __res:取资源动画("jszy/jmtb",0x1F73D093,"图像"):显示(125,-5)
              __res:取资源动画("jszy/jmtb",0x1F73D093,"图像"):显示(165,-5)
              标题字体:置颜色(255,255,255,255)
              标题字体:取图像("壹"):显示(97, 2)
              标题字体:取图像("贰"):显示(137, 2)
              标题字体:取图像("叁"):显示(177, 2)
              if  法宝.窗口类型=="法宝"  then
                  标题字体:取图像("已装备法宝"):显示(0, 0)
                  取输入背景(0, 0, 165, 23):显示(80, 127)
                  取输入背景(0, 0, 165, 23):显示(80, 162)
                  取输入背景(0, 0, 165, 23):显示(80, 197)
                  标题字体:取图像("法宝名称:"):显示(0, 130)
                  标题字体:取图像("升级经验:"):显示(0, 165)
                  标题字体:取图像("修炼经验:"):显示(0, 200)
                  标题字体:取图像("法宝移动至"):显示(360, 376)
                  self.法宝网格:置可见(true)
                  self.道具网格:置可见(true)
                  self.法宝网格:置物品(角色信息.法宝佩戴)
                  self.道具网格:置物品(角色信息.法宝)
                  if 法宝.选中道具 then
                      self.道具网格.选中编号=法宝.选中道具
                  end
                  说明字体:置颜色(0,0,0,255)
                  local 物品 = self.道具网格:选中物品()
                  if 物品 and 物品.物品 then
                        说明字体:取图像(物品.物品.名称):显示(90, 130)
                        说明字体:取图像(物品.物品.当前经验):显示(90, 165)
                        说明字体:取图像(物品.物品.升级经验):显示(90, 200)
                  elseif 法宝.选中法宝 and self.法宝网格.子控件[法宝.选中法宝]._spr and self.法宝网格.子控件[法宝.选中法宝]._spr.物品 then
                        说明字体:取图像(self.法宝网格.子控件[法宝.选中法宝]._spr.物品.名称):显示(90, 130)
                        说明字体:取图像(self.法宝网格.子控件[法宝.选中法宝]._spr.物品.当前经验):显示(90, 165)
                        说明字体:取图像(self.法宝网格.子控件[法宝.选中法宝]._spr.物品.升级经验):显示(90, 200)
                        self.法宝网格.子控件[法宝.选中法宝]._spr.确定=true
                  end
              elseif 法宝.窗口类型=="神器"  then
                    标题字体:取图像("已装备法宝"):显示(0, 0)
                    self.神器网格:置可见(true)
                    self.神器网格:置物品(门派神器名称[角色信息.门派])
                    self.神器说明:置可见(true)
                    __res:取资源动画("jszy/xjjm",0x01AC0041,"图像"):拉伸(270,215):显示(255,30)
                    __res:取资源动画("jszy/xjjm",0x01AC0042,"图像"):显示(15,100)
                    __res:取资源动画("jszy/xjjm",0x01AC0043,"图像"):显示(145,100)
                    __res:取资源动画("jszy/xjjm",0x01AC0044,"图像"):拉伸(80,3):显示(142,130)
                    说明字体:置颜色(255,255,255,255)
                    if 法宝.神器属性 and 法宝.神器属性.神器技能 then
                      if 角色信息.门派=="九黎城" then
                        __res:取资源动画("pic/sqsc","mxj.png","图片"):显示(285,40)

                      else
                        __res:取资源动画("jszy/xjjm",神器大图与坐标[角色信息.门派][1],"图像"):显示(神器大图与坐标[角色信息.门派][2],神器大图与坐标[角色信息.门派][3])
                        __res:取资源动画("jszy/xjjm",神器大图与坐标[角色信息.门派][4],"图像"):显示(270,35)
                      end
                      
                        标题字体:取图像(法宝.神器属性.神器技能.名称):显示(180 - 标题字体:取宽度(法宝.神器属性.神器技能.名称)//2, 140)
                        标题字体:取图像("剩余灵气："..法宝.神器属性.灵气):显示(660-标题字体:取宽度("剩余灵气："..法宝.神器属性.灵气),292)
                        local yy = 0
                        for k,v in pairs(法宝.属性数值) do
                            标题字体:取图像(法宝.miaoshu[k][1]):显示(10, 135+yy*22)
                            标题字体:取描边图像("+"..v):显示(15+标题字体:取宽度(法宝.miaoshu[k][1]), 134+yy*22)
                          yy = yy+1
                        end
                    end

                    if 法宝.选中法宝 and self.法宝网格.子控件[法宝.选中法宝]._spr and self.法宝网格.子控件[法宝.选中法宝]._spr.物品 then
                        self.法宝网格.子控件[法宝.选中法宝]._spr.确定=true
                  end
                
              elseif 法宝.窗口类型=="灵宝"  then
                  标题字体:取图像("已装备灵宝"):显示(0, 0)
                  取输入背景(0, 0, 165, 23):显示(80, 127)
                  取输入背景(0, 0, 165, 23):显示(80, 162)
                  取输入背景(0, 0, 165, 23):显示(80, 197)
                  标题字体:取图像("灵宝名称:"):显示(0, 130)
                  标题字体:取图像("升级经验:"):显示(0, 165)
                  标题字体:取图像("修炼经验:"):显示(0, 200)
                  标题字体:取图像("灵宝移动至"):显示(360, 376)
                  self.灵宝网格:置可见(true)
                  self.道具网格:置可见(true)
                  self.灵宝网格:置物品(角色信息.灵宝佩戴)
                  self.道具网格:置物品(角色信息.灵宝)
                  if 法宝.选中道具 then
                      self.道具网格.选中编号=法宝.选中道具
                  end
                    说明字体:置颜色(0,0,0,255)
                    local 物品 = self.道具网格:选中物品()
                    if 物品 and 物品.物品 then
                          说明字体:取图像(物品.物品.名称):显示(90, 130)
                          说明字体:取图像(物品.物品.当前经验):显示(90, 165)
                          说明字体:取图像(物品.物品.升级经验):显示(90, 200)
                        
                    elseif 法宝.选中灵宝 and self.灵宝网格.子控件[法宝.选中灵宝]._spr and self.灵宝网格.子控件[法宝.选中灵宝]._spr.物品 then
                          说明字体:取图像(self.灵宝网格.子控件[法宝.选中灵宝]._spr.物品.名称):显示(90, 130)
                          说明字体:取图像(self.灵宝网格.子控件[法宝.选中灵宝]._spr.物品.当前经验):显示(90, 165)
                          说明字体:取图像(self.灵宝网格.子控件[法宝.选中灵宝]._spr.物品.升级经验):显示(90, 200)
                          
                          self.灵宝网格.子控件[法宝.选中灵宝]._spr.确定=true
                    end

              end
      end)
end


local 神器说明 = 法宝显示:创建文本("神器说明", 137, 160, 110, 120)

local 法宝网格 = 法宝显示:创建网格("法宝网格", 10, 25, 315, 70)
function 法宝网格:初始化()
    self:创建格子(57, 57, 0, 4, 1, 4)
end


function 法宝网格:获得鼠标(x, y,a,b)
    for i = 1, #self.子控件 do
      self.子控件[i]._spr.焦点=nil
    end
    self.子控件[a]._spr.焦点=true
    if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not 鼠标层.附加  then
        local xx,yy=引擎:取鼠标坐标()
        __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,xx+20,yy+20)
    end
end
function 法宝网格:失去鼠标(x, y)
    for i = 1, #self.子控件 do
      self.子控件[i]._spr.焦点=nil
    end
end

function 法宝网格:左键弹起(x, y, a, b)
        if 鼠标层.附加 and  鼠标层.附加.窗口类型==法宝.窗口类型 then
                if  法宝.选中道具 then
                        请求服务(3735,{序列=法宝.选中道具,序列1=a})
                elseif 法宝.选中法宝 and 法宝.选中法宝~=a then
                        请求服务(3729,{序列=法宝.选中法宝,序列1=a})
                elseif 法宝.选中法宝 and  法宝.选中法宝==a and self.子控件[a]._spr and self.子控件[a]._spr.物品  then
                      法宝.选中法宝=a
                      鼠标层.附加=nil
                      法宝显示:重置显示()
                    return
                end
                法宝:重置抓取()
        else
              if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
                    if __手机 then
                        __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
                    end
                    鼠标层.附加= self.子控件[a]._spr
                    鼠标层.附加.窗口类型=法宝.窗口类型
                    self.子控件[a]._spr.确定 = true
                    法宝.选中法宝 = a
              else
                  法宝:重置抓取()
              end
        end
        法宝显示:重置显示()
end



function 法宝网格:置物品(数据)
    for i = 1, #self.子控件 do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,55,55, nil,true)
        if 数据 and 数据[i] then
            lssj:置物品(数据[i],55,55, nil,true)
        end
        self.子控件[i]:置精灵(lssj)
    end
   
end

local 灵宝网格 = 法宝显示:创建网格("灵宝网格", 50, 25, 315, 70)
function 灵宝网格:初始化()
    self:创建格子(57, 57, 0, 22, 1, 2)
end






function 灵宝网格:获得鼠标(x, y,a,b)
  self.子控件[a]._spr.焦点=true
  if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not 鼠标层.附加  then
      local xx,yy=引擎:取鼠标坐标()
      __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,xx+20,yy+20)
  end
end
function 灵宝网格:失去鼠标(x, y)
  for i = 1, #self.子控件 do
    self.子控件[i]._spr.焦点=nil
  end
end




function 灵宝网格:左键弹起(x, y, a, b)
  if 鼠标层.附加 and  鼠标层.附加.窗口类型==法宝.窗口类型 then
          if  法宝.选中道具 then
                请求服务(3735.1,{序列=法宝.选中道具,序列1=a})
          elseif 法宝.选中灵宝 and 法宝.选中灵宝~=a then
                请求服务(3729.1,{序列=法宝.选中灵宝,序列1=a})
          elseif 法宝.选中灵宝 and  法宝.选中灵宝==a and self.子控件[a]._spr and self.子控件[a]._spr.物品  then
                法宝.选中灵宝=a
                鼠标层.附加=nil
                法宝显示:重置显示()
              return
          end
          法宝:重置抓取()
  else
        if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
              if __手机 then
                __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
              end
              鼠标层.附加= self.子控件[a]._spr
              鼠标层.附加.窗口类型=法宝.窗口类型
              self.子控件[a]._spr.确定 = true
              法宝.选中灵宝 = a
        else
            法宝:重置抓取()
        end
  end
  法宝显示:重置显示()
end





function 灵宝网格:置物品(数据)
    for i = 1, #self.子控件 do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,55,55, nil,true)
        if 数据 and 数据[i] then
            lssj:置物品(数据[i],55,55, nil,true)
        end
        self.子控件[i]:置精灵(lssj)
    end
end



local 神器网格 = 法宝显示:创建网格("神器网格", 10, 25, 70, 70)
function 神器网格:初始化()
    self:创建格子(57,57,0,0,1,1)
end
function 神器网格:获得鼠标(x, y,a,b)
  self.子控件[1]._spr.焦点=true
  if self.子控件[1]._spr and self.子控件[1]._spr.物品 and not 鼠标层.附加  then
      local xx,yy=引擎:取鼠标坐标()
      __UI弹出.道具提示:打开(self.子控件[1]._spr.物品,xx+20,yy+20)
  end
end
function 神器网格:失去鼠标(x, y)
    self.子控件[1]._spr.焦点=nil
end

function 神器网格:左键弹起(x, y,a)
  if self.子控件[1]._spr and self.子控件[1]._spr.物品 and not 鼠标层.附加 and __手机 then
      __UI弹出.道具提示:打开(self.子控件[1]._spr.物品,x+20,y+20)
  end
end


function 神器网格:置物品(数据)
        local lssj = __物品格子:创建()
        lssj:置物品(nil,55,55, nil,true)
        if 数据 and 角色信息.神器佩戴 then
            lssj:置物品(数据,55,55, nil,true)
        end
        self.子控件[1]:置精灵(lssj)

end




local 道具网格 = 法宝显示:创建道具网格("道具网格", 260, 30)

function 道具网格:获得鼠标(x, y,a)
      local 物品 = self:焦点物品()
      if 物品 and 物品.物品 and not 鼠标层.附加 then
          local xx,yy=引擎:取鼠标坐标()
          __UI弹出.道具提示:打开(物品.物品,xx+20,yy+20)
      end
end



function 道具网格:右键弹起(x, y, a)
      local 物品 = self:焦点物品()
      if 物品 and 物品.物品 and self:焦点()~=0 then
          法宝.选中道具 = a
          self.选中编号 = a
          法宝显示:重置显示()
      end

end
function 道具网格:左键弹起(x, y, a)
      if type(a)~="number" then return end
      if 鼠标层.附加  and  鼠标层.附加.窗口类型==法宝.窗口类型  then
              local 物品 = self:焦点物品()
              if 物品 and 物品.物品 and self:焦点()~=0 then
                    if 法宝.选中法宝 and 法宝.窗口类型=="法宝" then
                            请求服务(3730,{序列=法宝.选中法宝,序列1=a})
                    elseif 法宝.选中灵宝 and 法宝.窗口类型=="灵宝" then
                            请求服务(3730.1,{序列=法宝.选中灵宝,序列1=a})
                    elseif 法宝.选中道具 and 法宝.选中道具~=a then
                          if 法宝.法宝.是否选中 then
                              请求服务(3731,{序列=法宝.选中道具,序列1=a})
                          else
                              请求服务(3731.1,{序列=法宝.选中道具,序列1=a})
                          end
                    elseif 法宝.选中道具 and 法宝.选中道具==a then
                          法宝.选中道具=a
                          鼠标层.附加=nil
                          法宝显示:重置显示()
                          return
                    end
              else
                    if 法宝.选中法宝 then
                          请求服务(3734,{序列=法宝.选中法宝,序列1=a})
                    elseif 法宝.选中灵宝 then
                          请求服务(3734.1,{序列=法宝.选中灵宝,序列1=a})
                    elseif 法宝.选中道具 then
                          if 法宝.法宝.是否选中 then
                                请求服务(3731,{序列=法宝.选中道具,序列1=a})
                          else
                                请求服务(3731.1,{序列=法宝.选中道具,序列1=a})
                          end        
                    end
              end
              法宝:重置抓取()
      else
            local 物品 = self:选中物品()
            if 物品 and 物品.物品 and self:选中()~=0 then
                  if __手机 then
                      __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
                  end
                  鼠标层.附加= 物品
                  鼠标层.附加.窗口类型=法宝.窗口类型
                  法宝.选中道具=a
            else
                法宝:重置抓取()
            end
      end

    法宝显示:重置显示()

end




local 背包设置 = {"法宝","灵宝", "神器"}
for i, v in ipairs(背包设置) do
        local 临时函数 =法宝:创建蓝色单选按钮( v,v, 290+(i-1)*80,45,64,22)
        function  临时函数:左键弹起(x, y)
          法宝.窗口类型=v
          法宝:按钮重置()
          法宝:重置抓取()
    end
end


  local 修炼设置 = {"使用","修炼","补充灵气","装备","修复","道具","行囊"}
for i, v in ipairs(修炼设置) do
      local 临时函数 =法宝:创建红色按钮( v,v, 0,0,74,22)
      function  临时函数:左键弹起(x, y)
            if 法宝.窗口类型~="神器" then
                if 法宝.选中道具 then
                      if v =="使用" and 法宝.窗口类型=="法宝" then
                            请求服务(3736,{序列=法宝.选中道具})
                      elseif v =="修炼" then
                          if 法宝.窗口类型=="法宝" then
                              请求服务(3733,{序列=法宝.选中道具})
                          else
                              请求服务(3733.1,{序列=法宝.选中道具})
                          end
                      elseif v =="补充灵气" then
                          if 法宝.窗口类型=="法宝" then
                            请求服务(3739,{序列=法宝.选中道具})
                          else
                              请求服务(3739.1,{序列=法宝.选中道具})
                          end
                      elseif (v =="道具" or v =="行囊") and 鼠标层.附加 and  鼠标层.附加.窗口类型==法宝.窗口类型 then    
                            请求服务(3701.1,{抓取id=法宝.选中道具,放置id=0,放置类型=v,抓取类型=法宝.窗口类型})
                            法宝:置可见(false)
                      end
                  else
                      __UI弹出.提示框:打开("#Y请先选中法宝后使用")
                end
            elseif v =="装备" and 法宝.窗口类型=="神器" then
                  请求服务(3748)
            elseif v =="修复" and 法宝.窗口类型=="神器" then
                  请求服务(6208)
                  法宝:置可见(false)
                return
            end
      end
end
local 关闭 = 法宝:创建按钮("关闭",525,15)
function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/jmtb", 0x81DD40D3))
end
function 关闭:左键弹起(x, y)
  法宝:置可见(false)
end






