--[[
Author: GGELUA
Date: 2024-11-24 23:33:58
Last Modified by: GGELUA
Last Modified time: 2024-11-24 23:35:17
--]]
--[[
Author: GGELUA
Date: 2024-11-06 21:48:13
Last Modified by: GGELUA
Last Modified time: 2024-11-17 15:15:55
--]]
local 家园访问 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "家园访问", 258 + abbr.py.x, 60 + abbr.py.y, 367+10, 157+10+87)
function 家园访问:初始化()
  local nsf = require("SDL.图像")(445, 410)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("家园访问", 0, 12, 367, 157+17+58-39, true):显示( 0, 0)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35), 72-24, 60+60-29)
    字体18:置颜色(__取颜色("白色"))
    字体18:取图像("请输入要拜访家园玩家的ID"):显示(32,33+8+16)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 家园访问:打开()
  self.发送时间=os.time()
  self:置可见(true)
  self.shuru:置文本("")
end



local 关闭 = 家园访问["创建我的按钮"](家园访问, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 403-75, 0)
function 关闭:左键弹起(x, y, msg)
  家园访问["置可见"](家园访问, false)
  家园访问["shuru"]["清空"](家园访问["shuru"])
end
local shuru = 家园访问["创建我的输入"](家园访问, "shuru", 81-24, 67+60-28, 254, 24, nil, 88, "黑色", 字体20)

local 确定 = 家园访问["创建我的按钮"](家园访问, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 130, 41, true), 130, 41), "确定", 153-26, 337-189-25+18, "拜访")
function 确定:左键弹起(x, y, msg)
  if 家园访问.shuru:取文本()~= ""  then
--    if os.time()-家园访问.发送时间>=3 then
      发送数据(1506,{房屋ID=家园访问.shuru:取文本()})
      __UI界面.窗口层.家园访问:置可见(false)
    --  家园访问.发送时间=os.time()
      家园访问.shuru:置文本("")
    end
 -- else
    __UI弹出["提示框"]:打开("#Y/已进入该玩家家园内")
  --end
end
