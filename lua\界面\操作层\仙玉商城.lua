
local 仙玉商城 = 窗口层:创建窗口("仙玉商城", 0, 0, 800, 540) 
function 仙玉商城:初始化()
    self:置精灵(__res:取资源动画("pic","sc1.png","图片"):到精灵())
    self.模型格子= __UI模型格子:创建()
    self.起点 = 0
    self.转向 = 5
    self.缓存数据={}
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true



end
function 仙玉商城:更新(dt)
  if self.模型格子 then
      self.模型格子:更新(dt)
  end
end
local 大类设置 = {"银子商城","仙玉商城","积分商城","锦衣商城","神兽商城","点卡商城"}
local 分类配置={
  银子商城={"杂货商品","锻造商品","宝石商品","房屋装饰","庭院装饰"},
  仙玉商城={"仙玉商品","锻造商品","宝宝商品","灵宝商品","一级法宝","二级法宝","三级法宝"},
  积分商城={"妖魔积分","活跃积分","镇妖积分","师门积分","成就积分","副本积分"},
  锦衣商城={"锦衣商品","祥瑞商品","足迹商品","足印商品"},
  神兽商城={"商品详情"},
  点卡商城={"商品详情"},
}


function 仙玉商城:显示(x, y)
  if self.图像 then
      self.图像:显示(x, y)
  end
  if self.图像2 then
      self.图像2:显示(x, y)
  end
  if self.模型格子 then
      self.模型格子:显示(x, y)
  end
end

function 仙玉商城:打开(数据)
  self:置可见(true,true)
  self.分类 = "杂货商品"
  self.商品 = {}
  self.银子 =0
  self.仙玉 = 0
  self.点卡 = 0
  self.大分类 =  "银子商城"
  self.选中商品=nil
  self.起点 = 0
  self.副本积分 = 0
  self.镇妖积分 = 0
  self.成就积分 = 0
  self.活跃积分 = 0
  self.师门积分 = 0
  self.妖魔积分 = 0
  self.选中商品=nil
  self.选中数量=nil
  self:刷新(数据)

end


function 仙玉商城:刷新(内容)
  self.选中商品=nil
  self.选中数量=nil
  self.起点 = 0
  self.分类 = 内容.分类
  self.商品 = {}
  self.银子 = 内容.银子
  self.仙玉 = 内容.仙玉
  self.点卡 = 内容.点卡
  self.大分类 =  内容.大分类
  self.副本积分 = 内容.副本积分
  self.镇妖积分 = 内容.镇妖积分
  self.成就积分 = 内容.成就积分
  self.活跃积分 = 内容.活跃积分
  self.师门积分 = 内容.师门积分
  self.妖魔积分 = 内容.妖魔积分
  if not self.缓存数据 then
    self.缓存数据={}
  end

  if not self.缓存数据[self.大分类] then
      self.缓存数据[self.大分类]={}
  end
  if not self.缓存数据[self.大分类][self.分类] then
      self.缓存数据[self.大分类][self.分类] ={}
  end
  for i = 1, #self.商品网格.子控件 do
      if self.商品网格.子控件[i] and self.商品网格.子控件[i]._spr then
        self.商品网格.子控件[i]._spr.确定 =nil 
      end
  end
  for n=1,#内容.商品 do 
      self.商品[n]={}
      if self.缓存数据[self.大分类][self.分类][内容.商品[n][1]] then
              if self.缓存数据[self.大分类][self.分类][内容.商品[n][1]].价格~=内容.商品[n][4] then
                  self.缓存数据[self.大分类][self.分类][内容.商品[n][1]].价格=内容.商品[n][4]
              end
              if 内容.神兽介绍~=nil and 内容.神兽介绍[n]~=nil and self.缓存数据[self.大分类][self.分类][内容.商品[n][1]].介绍~=nil then
                  self.缓存数据[self.大分类][self.分类][内容.商品[n][1]].介绍="攻资: "..内容.神兽介绍[n].攻击资质..
                                                                          "\n防资: "..内容.神兽介绍[n].防御资质..
                                                                          "\n体资: "..内容.神兽介绍[n].体力资质..
                                                                          "\n法资: "..内容.神兽介绍[n].法力资质..
                                                                          "\n速资: "..内容.神兽介绍[n].速度资质..
                                                                          "\n躲资: "..内容.神兽介绍[n].躲闪资质..
                                                                          "\n成长: "..内容.神兽介绍[n].成长..
                                                                          "\n#Y物理技能:\n"..
                                                                          内容.神兽介绍[n].物理技能..
                                                                          "\n#Y法术技能:\n"..
                                                                          内容.神兽介绍[n].法术技能..
                                                                          "\n#Y物理固定技能:\n"..
                                                                          内容.神兽介绍[n].物理天生..
                                                                          "\n#Y法术固定技能:\n"..
                                                                          内容.神兽介绍[n].法术天生
              end
              self.商品[n] = self.缓存数据[self.大分类][self.分类][内容.商品[n][1]]
            
      else
                if 内容.商品[n][3]=="坐骑" then
                        local lssj = 取物品(内容.商品[n][1])
                        self.商品[n].格子=内容.商品[n][1]
                        self.商品[n].大动画 = lssj[13]
                        self.商品[n].资源 = lssj[11]
                        self.商品[n].名称=内容.商品[n][1]
                        self.商品[n].介绍=内容.商品[n][2]
                        self.商品[n].价格=内容.商品[n][4]
                elseif 内容.商品[n][3]=="神兽" then
                        self.商品[n].格子=内容.商品[n][1]
                        local 资源1 = 取战斗模型(内容.商品[n][1])
                        self.商品[n].大模型=资源1[6]
                        self.商品[n].资源 =资源1[10]
                        self.商品[n].名称=内容.商品[n][1]
                        self.商品[n].介绍=内容.商品[n][2]
                        self.商品[n].造型=内容.商品[n][1]
                        if 内容.神兽介绍~=nil and 内容.神兽介绍[n]~=nil then
                          self.商品[n].介绍="攻资: "..内容.神兽介绍[n].攻击资质..
                                          "\n防资: "..内容.神兽介绍[n].防御资质..
                                          "\n体资: "..内容.神兽介绍[n].体力资质..
                                          "\n法资: "..内容.神兽介绍[n].法力资质..
                                          "\n速资: "..内容.神兽介绍[n].速度资质..
                                          "\n躲资: "..内容.神兽介绍[n].躲闪资质..
                                          "\n成长: "..内容.神兽介绍[n].成长..
                                          "\n#Y物理技能:\n"..
                                          内容.神兽介绍[n].物理技能..
                                          "\n#Y法术技能:\n"..
                                          内容.神兽介绍[n].法术技能..
                                          "\n#Y物理固定技能:\n"..
                                          内容.神兽介绍[n].物理天生..
                                          "\n#Y法术固定技能:\n"..
                                          内容.神兽介绍[n].法术天生
                        end
                        self.商品[n].类型 ="神兽"
                        self.商品[n].价格=内容.商品[n][4]
              else
                
                       local lssj = 取物品(内容.商品[n][3])
                        self.商品[n].格子=内容.商品[n][3]
                        self.商品[n].大动画=lssj[13]
                        self.商品[n].资源 =lssj[11]
                        self.商品[n].名称=内容.商品[n][1]
                        self.商品[n].介绍=内容.商品[n][2]
                        if self.商品[n].介绍=="法宝" or self.商品[n].介绍=="锦衣" or self.商品[n].介绍=="普通" or self.商品[n].介绍=="仙玉" or self.商品[n].介绍=="灵宝" then
                          self.商品[n].介绍=lssj[1] or "暂无说明!"
                        end
                        self.商品[n].价格=内容.商品[n][4]
                        if lssj[2]==2 and lssj[5]==60 and lssj[3]~=nil then
                            self.商品[n].锦衣=true
                          if lssj[3] == 15 then
                              self.商品[n].介绍 = self.商品[n].介绍.."\n#S气血 +10%\n#S魔法 +10%\n#S伤害 +5%\n#S法伤 +5%"
                            elseif lssj[3] == 16 then
                              self.商品[n].介绍 = self.商品[n].介绍.."\n#S防御 +5%\n#S法防 +5%"
                            elseif lssj[3] == 17 then
                              self.商品[n].介绍 = self.商品[n].介绍.."\n#S速度 +8%"
                            end
                        end


                      if 内容.商品[n][2]=="灵宝" and lssj[15] and lssj[6] then
                              self.商品[n].介绍 =self.商品[n].介绍..
                                  "\n#W【使用条件】\n".."#G"..lssj[6].."专用，消耗#G1\\3\\7点灵元"..
                                  "\n#F【战斗效果】"..
                                  "\n#P"..lssj[15]
                      end
              end
          self.缓存数据[self.大分类][self.分类][内容.商品[n][1]]  = self.商品[n]
      end 
  end

      self:显示设置()
      self:货币设置()
      self.分类网格:置显示()
      self.商品网格:置显示()
end



function 仙玉商城:显示设置()
  self.模型格子:清空()
  self.数量输入:置数值(0)
  for i, v in ipairs(大类设置) do
    if v == self.大分类 then
        self[v]:置选中(true)
    else
        self[v]:置选中(false)
    end
  end
  if self[self.大分类] then
      self[self.大分类]:置选中(true)
  end
  if self.大分类=="锦衣商城" or self.大分类=="神兽商城" then
      self.数量输入:置坐标(482, 473)
      self.购买:置坐标(570, 502)
      self.左转:置可见(true)
      self.右转:置可见(true)
      if self.大分类=="锦衣商城" and self.分类~="祥瑞商品"  then
          self.转向 = 5
      else
          self.转向 = 1
      end
  else
      self.数量输入:置坐标(210, 470)
      self.购买:置坐标(440, 510)
      self.左转:置可见(false)
      self.右转:置可见(false)
  end

  self.图像=nil
  if self.大分类=="锦衣商城" or self.大分类=="神兽商城" then
      self.图像 = self:创建纹理精灵(function()
        __res:取资源动画("pic","sc2.png","图片"):显示(128,150)
      end,1
    )
  end
  
end


function 仙玉商城:刷新货币(内容)
  self.仙玉=内容.仙玉
  self.银子=内容.银子
  self.点卡 = 内容.点卡
  self.活跃积分 = 内容.活跃积分
  self.妖魔积分 = 内容.妖魔积分
  self.镇妖积分 = 内容.镇妖积分
  self.师门积分 = 内容.师门积分
  self.成就积分 = 内容.成就积分
  self.副本积分 = 内容.副本积分
  self:货币设置()
end



function 仙玉商城:货币设置()
   self.图像2 =self:创建纹理精灵(function()
    排行字体:置颜色(255,255,255,255):取投影图像(self.大分类.." > "..self.分类,255,255,255,255):显示(138,121)
    排行标题:置颜色(__取商城银子颜色(self.银子))
      排行标题:取描边图像(self.银子):显示(342,19)
    __res:取资源动画("jszy/fwtb", 0x00000302,"图像"):显示(128,64)
    if self.大分类== "点卡商城" then
          排行标题:置颜色(__取商城银子颜色(self.点卡))
          排行标题:取描边图像(self.点卡):显示(167,19)

    elseif self.大分类== "积分商城" then
            if self[self.分类] and type(self[self.分类])=="number" then
                排行标题:置颜色(__取商城银子颜色(self[self.分类]))
                排行标题:取描边图像(self[self.分类]):显示(167,19)
            end
    else
          排行标题 :置颜色(__取商城银子颜色(self.仙玉))
          排行标题:取描边图像(self.仙玉):显示(167,19)
    end
    if self.选中商品 and  self.商品[self.选中商品] then
      if self.大分类=="锦衣商城" or self.大分类=="神兽商城" then
          文本字体:置颜色(__取颜色("黄色")):取图像(self.商品[self.选中商品].名称):显示(482,410)
          文本字体:置颜色(__取商城银子颜色(self.商品[self.选中商品].价格))
          文本字体:取描边图像(self.商品[self.选中商品].价格):显示(482,438)
          if self.选中数量 then
              文本字体:置颜色(__取商城银子颜色(self.选中数量*self.商品[self.选中商品].价格))
              文本字体:取描边图像(self.选中数量*self.商品[self.选中商品].价格):显示(663,473)
           
          end
          if self.大分类=="锦衣商城" then
              local 传入数据 = {造型=角色信息.造型,锦衣={},装备={[3]=角色信息.装备[3],[4]=角色信息.装备[4]}}
                if self.分类=="锦衣商品" then
                    传入数据.锦衣[1] = {名称=self.商品[self.选中商品].名称}
                    self.模型格子:置数据(传入数据, "角色",255,450)
                elseif self.分类=="祥瑞商品" then
                    self.模型格子:置数据({模型=self.商品[self.选中商品].名称}, "坐骑",255,450,角色信息.造型)
                elseif self.分类=="足迹商品" then
                    传入数据.锦衣[3] = {名称=self.商品[self.选中商品].名称}
                    self.模型格子:置数据(传入数据, "角色",255,450)
                elseif self.分类=="足印商品" then
                    传入数据.锦衣[2] = {名称=self.商品[self.选中商品].名称}
                    self.模型格子:置数据(传入数据, "角色",255,450)
                end
          else
              self.模型格子:置数据({模型=self.商品[self.选中商品].名称}, "召唤兽",255,450)
          end
         -- self.模型格子:置方向(self.转向)
      else
          文本字体:置颜色(__取颜色("黄色")):取图像(self.商品[self.选中商品].名称):显示(210,435)
          文本字体:置颜色(__取商城银子颜色(self.商品[self.选中商品].价格))
          文本字体:取描边图像(self.商品[self.选中商品].价格):显示(538,435)
          if self.选中数量 then
              文本字体:置颜色(__取商城银子颜色(self.选中数量*self.商品[self.选中商品].价格))
              文本字体:取描边图像(self.选中数量*self.商品[self.选中商品].价格):显示(538,470)
          end
      end
    end
   end,1
 )
end



for i, v in ipairs(大类设置) do
  local 临时函数 = 仙玉商城:创建单选按钮(v, 2, 70+(i-1)*35)
  function 临时函数:初始化()
      self:创建按钮精灵(__res:取资源动画("jszy/fwtb", 0x10000052),nil,v,nil,nil,排行标题,nil,true)
  end
  function 临时函数:左键按下(x, y)
     仙玉商城.大分类=v
     if v  == "银子商城" then
          仙玉商城.分类="杂货商品"
      elseif v  == "仙玉商城" then
           仙玉商城.分类="仙玉商品"
      elseif v  == "积分商城" then
          仙玉商城.分类="妖魔积分"
      elseif v  == "锦衣商城" then
          仙玉商城.分类="锦衣商品"
      elseif v  == "神兽商城" then
          仙玉商城.分类="商品详情"
      elseif v  == "点卡商城" then
          仙玉商城.分类="商品详情"
     end
     请求服务(30,{序列=仙玉商城.分类,大类=仙玉商城.大分类})
  end
end

local 分类网格 = 仙玉商城:创建网格("分类网格", 130, 63, 600, 40)


function 分类网格:置显示()
  self:创建格子(76, 40, 0, 0, 1, #分类配置[仙玉商城.大分类])
    for i = 1, #self.子控件 do
        if 分类配置[仙玉商城.大分类][i] then
          self.子控件[i]:创建纹理精灵(function()
            if 分类配置[仙玉商城.大分类][i]==仙玉商城.分类 then
              __res:取资源动画("jszy/fwtb", 0x10000047):取图像(2):显示(0,0)
           else
              __res:取资源动画("jszy/fwtb", 0x10000047):取图像(1):显示(0,0)
           end
           __res:取资源动画("jszy/fwtb", 0x00000303,"图像"):显示(75,9)
       
           文本字体:置颜色(255,255,255,255)
           文本字体:取投影图像(分类配置[仙玉商城.大分类][i]):显示(10,12)
          end
        )
        else
          self.子控件[i]:置精灵()
        end
    end
end

function 分类网格:左键按下(x, y,a)
    if self.子控件[a] and self.子控件[a]._spr and 分类配置[仙玉商城.大分类][a] then
        请求服务(30,{序列=分类配置[仙玉商城.大分类][a],大类=仙玉商城.大分类})
    end
end


local 商品网格 = 仙玉商城:创建网格("商品网格", 0, 0, 0, 0)

function 商品网格:置显示()
    if 仙玉商城.大分类=="锦衣商城" or 仙玉商城.大分类=="神兽商城" then
        self:创建格子(55, 55, 9, 10,4 ,6,true)
        self:置宽高(380,250)
        self:置坐标(395,150)
    else
        self:创建格子(55, 55, 15, 10,4,10,true)
        self:置宽高(645,275)
        self:置坐标(140, 154)
    end
    for i = 1, #self.子控件 do
        if 仙玉商城.商品[i+仙玉商城.起点] and 仙玉商城.商品[i+仙玉商城.起点].格子 then
           if 仙玉商城.大分类=="神兽商城" then
              local lssj = __头像格子:创建()
              lssj:置头像(仙玉商城.商品[i+仙玉商城.起点].格子,55,55)
              self.子控件[i]:置精灵(lssj)
           else
             local lssj = __物品格子:创建()
             lssj:置物品(仙玉商城.商品[i+仙玉商城.起点].格子, 55,55)
             self.子控件[i]:置精灵(lssj)
          end
        else
             self.子控件[i]:置精灵()
        end
    end
    if 仙玉商城.选中商品 and 仙玉商城.商品[仙玉商城.选中商品] and  self.子控件[仙玉商城.选中商品-仙玉商城.起点] and self.子控件[仙玉商城.选中商品-仙玉商城.起点]._spr then
        self.子控件[仙玉商城.选中商品-仙玉商城.起点]._spr.确定 =true    
    end
end


function 商品网格:获得鼠标(x, y,a)
  if self.子控件[a] and self.子控件[a]._spr and 仙玉商城.商品[a+仙玉商城.起点] and not 鼠标层.附加  then
       __UI弹出.自定义提示:打开(仙玉商城.商品[a+仙玉商城.起点],x+20,y+20)
  end
end


function 商品网格:右键按下(x, y,a)
  for i, v in ipairs(self.子控件) do
        if v._spr and v._spr.确定 then
          v._spr.确定 = nil
        end
  end
  if  仙玉商城.选中数量 and 仙玉商城.选中商品 and 仙玉商城.商品[仙玉商城.选中商品]  and  a ==仙玉商城.选中商品-仙玉商城.起点 then
        if 仙玉商城.选中数量~=仙玉商城.数量输入:取数值() then
            仙玉商城.选中数量 = 仙玉商城.数量输入:取数值()
        end
        仙玉商城.选中商品 = a+仙玉商城.起点
        仙玉商城.选中数量 =仙玉商城.选中数量 - 1
        if 仙玉商城.选中数量<=1 then
          仙玉商城.选中数量=1
        end
        仙玉商城.数量输入:置数值(仙玉商城.选中数量)
        self.子控件[a]._spr.确定=true
  else
      if self.子控件[a] and self.子控件[a]._spr and 仙玉商城.商品[a+仙玉商城.起点] then
          仙玉商城.选中商品 = a+仙玉商城.起点
          仙玉商城.选中数量 = 1
          仙玉商城.数量输入:置数值(1)
          self.子控件[a]._spr.确定=true
      end
  end
  仙玉商城:货币设置()
end

function 商品网格:左键按下(x, y,a)
    for i, v in ipairs(self.子控件) do
          if v._spr and v._spr.确定 then
            v._spr.确定 = nil
          end
    end
    if  仙玉商城.选中数量 and 仙玉商城.选中商品 and 仙玉商城.商品[仙玉商城.选中商品]  and  a ==仙玉商城.选中商品-仙玉商城.起点 then
          if 仙玉商城.选中数量~=仙玉商城.数量输入:取数值() then
              仙玉商城.选中数量 = 仙玉商城.数量输入:取数值()
          end
          仙玉商城.选中商品 = a+仙玉商城.起点
          仙玉商城.选中数量 =仙玉商城.选中数量 +1
          if 仙玉商城.选中数量>999 then
              仙玉商城.选中数量=999
          end
          仙玉商城.数量输入:置数值(仙玉商城.选中数量)
          self.子控件[a]._spr.确定=true
    else
        if self.子控件[a] and self.子控件[a]._spr and 仙玉商城.商品[a+仙玉商城.起点] then
            仙玉商城.选中商品 = a+仙玉商城.起点
            仙玉商城.选中数量 = 1
            仙玉商城.数量输入:置数值(1)
            if __手机 then
              __UI弹出.自定义提示:打开(仙玉商城.商品[a+仙玉商城.起点],x,y)
            end
            self.子控件[a]._spr.确定=true
        end
    end
    仙玉商城:货币设置()
end










local 图标控件=仙玉商城:创建控件("图标控件", 0, 74, 135, 215)
function 图标控件:初始化()
  self:创建纹理精灵(function()
    __res:取资源动画("pic", "0-1.png","图片"):显示(8,0)
    __res:取资源动画("pic", "0-2.png","图片"):显示(5,37)
    __res:取资源动画("pic", "0-3.png","图片"):显示(5,70)
    __res:取资源动画("pic", "0-4.png","图片"):显示(5,106)
    __res:取资源动画("pic", "0-5.png","图片"):显示(5,142)
    __res:取资源动画("pic", "0-6.png","图片"):显示(8,182)
  end
)
end


local 数量输入 = 仙玉商城:创建文本输入("数量输入", 210, 470, 80, 18)
function 数量输入:初始化()
  self:取光标精灵(255, 255, 255, 255)
  self:置限制字数(3)
  self:置颜色(255, 255, 255, 255)
  self:置模式(2)
end

function 数量输入:输入事件()
  if 仙玉商城.选中商品 and 仙玉商城.商品[仙玉商城.选中商品] and 仙玉商城.商品[仙玉商城.选中商品].名称 and  self:取数值()>0 then
      仙玉商城.选中数量 = self:取数值()
  else
      仙玉商城.选中数量 =1
  end
  仙玉商城:货币设置()
end


 
local 左转 = 仙玉商城:创建按钮("左转", 155, 435)
function 左转:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/xjiem", 0x83000000),1)
end
function 左转:左键弹起(x, y, msg)
  if  仙玉商城.模型格子 then
    仙玉商城.转向 = 仙玉商城.转向 - 1
    if 仙玉商城.大分类=="锦衣商城" and 仙玉商城.分类~="祥瑞商品"  then
          if 仙玉商城.转向<0 then
              仙玉商城.转向 = 7
          end
    else
        if 仙玉商城.转向<0 then
            仙玉商城.转向 = 3
        end
    end
    仙玉商城.模型格子:置方向(仙玉商城.转向)
end

end





local 右转 = 仙玉商城:创建按钮("右转", 305, 435)
function 右转:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/xjiem", 0x84000000),1)
end
function 右转:左键弹起(x, y, msg)
  if  仙玉商城.模型格子 then
    仙玉商城.转向 = 仙玉商城.转向 + 1
    if 仙玉商城.大分类=="锦衣商城" and 仙玉商城.分类~="祥瑞商品"  then
          if 仙玉商城.转向>=8 then
              仙玉商城.转向 = 0
          end
    else
        if 仙玉商城.转向>=4 then
            仙玉商城.转向 = 0
        end
    end
    仙玉商城.模型格子:置方向(仙玉商城.转向)
end

end

local 上一页 = 仙玉商城:创建蓝色按钮("上一页", "上一页", 620, 115,74,22)
function 上一页:左键弹起(x, y, msg)
  if 仙玉商城.起点>0 then
    if 仙玉商城.大分类=="锦衣商城" or 仙玉商城.大分类=="神兽商城" then
       仙玉商城.起点 = 仙玉商城.起点 - 24
    else
       仙玉商城.起点 = 仙玉商城.起点 - 40
    end
    商品网格:置显示()
  end
end

local 下一页 = 仙玉商城:创建蓝色按钮("下一页", "下一页", 700, 115,74,22)
function 下一页:左键弹起(x, y, msg)
  if 仙玉商城.起点<#仙玉商城.商品 then
    if 仙玉商城.大分类=="锦衣商城" or 仙玉商城.大分类=="神兽商城"  and #仙玉商城.商品>24 then
       仙玉商城.起点 = 仙玉商城.起点 + 24
    elseif #仙玉商城.商品>40 then
       仙玉商城.起点 = 仙玉商城.起点 + 40
    end
    商品网格:置显示()
  end

end


local 购买 = 仙玉商城:创建蓝色按钮("购 买", "购买", 440, 522,74,22)
function 购买:左键弹起(x, y)
  if 仙玉商城.选中商品 and 仙玉商城.选中数量 and 仙玉商城.选中数量>0 then
     if 仙玉商城.选中数量~=仙玉商城.数量输入:取数值() then
        仙玉商城.选中数量 = 仙玉商城.数量输入:取数值()
     end
     请求服务(30.1,{序列=仙玉商城.分类,序列1=仙玉商城.选中商品,数量=仙玉商城.选中数量,大类=仙玉商城.大分类})
  end
end

local 关闭 = 仙玉商城:创建按钮("关闭",765,10)

function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0x00300101))
end
function 关闭:左键弹起(x, y)
  仙玉商城:置可见(false)
end


