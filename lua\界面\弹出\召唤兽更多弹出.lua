--[[
Author: GGELUA
Date: 2024-09-04 16:18:55
Last Modified by: GGELUA
Last Modified time: 2024-11-02 00:08:46
--]]
--[[
Author: GGELUA
Date: 2024-09-04 16:18:55
Last Modified by: GGELUA
Last Modified time: 2024-10-30 00:07:19
--]]
--[[
Author: GGELUA
Date: 2024-08-10 00:03:19
Last Modified by: GGELUA
Last Modified time: 2024-08-17 00:18:04
--]]
__UI弹出["召唤兽更多弹出"] = __UI界面["创建弹出窗口"](__UI界面, "召唤兽更多弹出", 339+45 + abbr.py.x, 284+30 + abbr.py.y, 150, 192)
local 召唤兽更多弹出 = __UI弹出["召唤兽更多弹出"]
function 召唤兽更多弹出:初始化()
  self:置精灵(取黑色背景(0, 0, 150, 135))
end
function 召唤兽更多弹出:打开()
  if self.是否可见 then
    self:置可见(false)
  else
    self:置可见(true)
  end
end
local lsan = {
  "鉴 定",
  "放 生",
 -- "兽 魂"
}
for i = 1, #lsan do
  local 临时函数 = 召唤兽更多弹出["创建我的按钮"](召唤兽更多弹出, __res:getPNGCC(2, 368, 906, 126, 52, true), lsan[i] .. "按钮", 12, 11 + (i - 1) * 60, lsan[i])
  function  临时函数:左键弹起(x, y, msg)
    if "鉴 定" == lsan[i] then
      if 角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽]~=nil then
        local 内丹开孔进度=0
    if 角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].内丹 and 角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].内丹.开孔进度 then
      内丹开孔进度=角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].内丹.开孔进度
    end
        __UI弹出.提示框:打开("#Y/这只召唤兽还可以用喂食#G"..角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].元宵.可用.."#Y个元宵，#G"..角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].元宵.炼兽真经.."#Y本炼兽珍经，#G"..角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].元宵.如意丹.."#Y个如意丹，#G"..角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].元宵.千金露.."#Y个千金露，#G"..角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].元宵.水晶糕.."#Y个水晶糕。内丹开孔进度：#G"..内丹开孔进度)
      end
    elseif "兽 魂" == lsan[i] then
     -- 发送数据(200,{序号1=1})
    elseif "放 生" == lsan[i] then
      if __UI界面["窗口层"].召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽] then
        local 事件 = function()
          发送数据(5005, {
            序列 = 角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].认证码
          })
        end
        local wb = "真的要放生#Y/" .. 角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].等级 .. "级的#R/" .. 角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽].名称 .. "#W/吗?"
        __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, wb, 285, 155, 390, 200, 事件)
      end
    end
    召唤兽更多弹出["置可见"](召唤兽更多弹出, false)
  end
end