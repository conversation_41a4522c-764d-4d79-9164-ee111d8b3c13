local 打造 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "打造", 96 + abbr.py.x, 19 + abbr.py.y, 777, 491)
local qufenlei = function(标识)
  local ls = {}
  if "打造" == 标识 then
    ls = {
      "强化人物装备",
      "普通人物装备",
      "召唤兽装备",
      "灵饰淬灵"
    }
  elseif "镶嵌" == 标识 then
    ls = {
      "宝石",
      "灵石",
      "萃灵晶石",
      "珍珠",
      "点化石",
      "星辉石"
    }
  elseif "合成" == 标识 then
    ls = {
      "宝石",
      "灵石",
      "变身卡",
      "碎石锤",
      "精铁",
      "暗器",
      "星辉石",
      "钟灵石"
    }
  elseif "修理" == 标识 then
    ls = {
      "人物装备",
      "召唤兽装备",
      "召唤兽装饰",
      "坐骑装饰"
    }
  elseif "熔炼" == 标识 then
    ls = {
      "熔炼装备",
      "还原装备"
    }
  elseif "分解" == 标识 then
    ls = {
      "分解装备",
      "分解灵犀玉",
      "宝石降级"
    }
  elseif "神器" == 标识 then
    ls = {
      "人物装备",
      "人物装备重铸",
      "人物装备炼化",
      "灵饰打造",
      "灵饰重铸",
      "灵饰炼化"
    }
  end
  return ls
end
local 打造说明 = {
  ["打造"] = {
    ["强化人物装备"] = "百炼精铁，制造指南书",
    ["普通人物装备"] = "百炼精铁，制造指南书",
    ["召唤兽装备"] = "上古锻造图策，天眼珠",
    ["灵饰淬灵"] = "元灵晶石，灵饰指南书"
  },
  ["镶嵌"] = {
    ["宝石"] = "人物装备，宝石",
    ["灵石"] = "召唤兽装备，灵石",
    ["萃灵晶石"] = "人物装备，萃灵晶石",
    ["珍珠"] = "人物装备/召唤兽装备，珍珠",
    ["点化石"] = "召唤兽装备，点化石",
    ["星辉石"] = "人物灵饰，星辉石"
  },
  ["合成"] = {
    ["宝石"] = "宝石",
    ["灵石"] = "灵石",
    ["变身卡"] = "变身卡",
    ["碎石锤"] = "碎石锤",
    ["精铁"] = "精铁",
    ["暗器"] = "暗器",
    ["星辉石"] = "星辉石",
    ["钟灵石"] = "钟灵石"
  },
  ["修理"] = {
    ["人物装备"] = "人物装备",
    ["召唤兽装备"] = "召唤兽装备，天眼珠/珍珠",
    ["召唤兽装饰"] = "召唤兽装饰",
    ["坐骑装饰"] = "坐骑装饰"
  },
  ["熔炼"] = {
    ["熔炼装备"] = "人物装备，钨金",
    ["还原装备"] = "人物装备"
  },
  ["分解"] = {
    ["分解装备"] = "人物装备/召唤兽装备",
    ["分解灵犀玉"] = "灵犀玉",
    ["宝石降级"] = "人物装备，碎石锤"
  },
  ["神器"] = {
    ["人物装备"] = "人物装备，破天石",
    ["人物装备重铸"] = "人物装备，冰火玄晶",
    ["人物装备炼化"] = "人物装备，云龙珠",
    ["灵饰打造"] = "人物灵饰，神魔石",
    ["灵饰重铸"] = "人物灵饰，悔梦石",
    ["灵饰炼化"] = "人物灵饰，地心火，天淬火，三昧火，真元火"
  }
}
function 打造:初始化()
  local nsf = require("SDL.图像")(777, 491)
  if nsf["渲染开始"](nsf) then
    置窗口背景("制造", 0, 12, 769, 479, true)["显示"](置窗口背景("制造", 0, 12, 769, 479, true), 0, 0)
    取灰色背景(0, 0, 359, 315, true)["显示"](取灰色背景(0, 0, 359, 315, true), 20, 156)
    取灰色背景(0, 0, 359, 103, true)["显示"](取灰色背景(0, 0, 359, 103, true), 392, 156)
    __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 31, 192)
    local lssc = 取输入背景(0, 0, 262, 23)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "需要材料")["显示"](字体18["取图像"](字体18, "需要材料"), 28, 164)
    字体18["取图像"](字体18, "所需资金")["显示"](字体18["取图像"](字体18, "所需资金"), 390, 279)
    lssc["显示"](lssc, 487, 277)
    字体18["取图像"](字体18, "现有资金")["显示"](字体18["取图像"](字体18, "现有资金"), 390, 318)
    lssc["显示"](lssc, 487, 316)
    字体18["取图像"](字体18, "所需体力")["显示"](字体18["取图像"](字体18, "所需体力"), 390, 358)
    lssc["显示"](lssc, 487, 356)
    字体18["取图像"](字体18, "现有体力")["显示"](字体18["取图像"](字体18, "现有体力"), 390, 398)
    lssc["显示"](lssc, 487, 396)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 打造:打开(data)
  self:置可见(true)
  self.数据 = data
  self.打造["置选中"](self.打造, true)
  self.标识 = "打造"
  self.分类 = nil
  self.xinghuishiwangge:置可见(false)
  self:刷新(data)
  self.请选择["我的按钮置文字"](self.请选择, self.请选择, __res:getPNGCC(3, 378, 347, 143, 37, true)["拉伸"](__res:getPNGCC(3, 378, 347, 143, 37, true), 233, 37), "请选择")
end
function 打造:重置()
  self.图像2 = nil
  local nsf = require("SDL.图像")(266, 147)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取银子颜色(角色信息["银子"]))
    字体18["取图像"](字体18, 角色信息["银子"])["显示"](字体18["取图像"](字体18, 角色信息["银子"]), 10, 46)
    字体18["置颜色"](字体18, __取银子颜色(角色信息["体力"]))
    字体18["取图像"](字体18, 角色信息["体力"])["显示"](字体18["取图像"](字体18, 角色信息["体力"]), 10, 126)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.图像["置中心"](self.图像, -487, -275)
  if self.标识 and self.分类 then
    字体18["置颜色"](字体18, __取颜色("白色"))
    self.图像2 = 字体18["取精灵"](字体18, "需要材料：" .. 打造说明[self.标识][self.分类])
    self.图像2["置中心"](self.图像2, -375, -120)
  end
end
function 打造:刷新(data)
  self.物品限制 = {}
  if self.标识 == "打造" then
    if self.分类 == "强化人物装备" or self.分类 == "普通人物装备" then
      self.物品限制 = {5, 204}
    elseif self.分类 == "召唤兽装备" then
      self.物品限制 = {3, 3}
    elseif self.分类 == "灵饰淬灵" then
      self.物品限制 = {5, 5}
    elseif self.分类 == "元身幻化" then
      self.物品限制 = {204, 5}
    elseif self.分类 == "元身装备" then
      self.物品限制 = {204, 5}
    end
  elseif self.标识 == "镶嵌" then
    if self.分类 == "宝石" then
      self.物品限制 = {2, 5}
    elseif self.分类 == "灵石" then
      self.物品限制 = {2, "召唤兽镶嵌"}
    elseif self.分类 == "萃灵晶石" then
      self.物品限制 = {2, "萃灵晶石"}
    elseif self.分类 == "珍珠" then
      self.物品限制 = {2, 5}
    elseif self.分类 == "点化石" then
      self.物品限制 = {2, 5}
    elseif self.分类 == "星辉石" then
      self.物品限制 = {2, 5}
    end
  elseif self.标识 == "合成" then
    if self.分类 == "宝石" then
      self.物品限制 = {5, 5}
    elseif self.分类 == "灵石" then
      self.物品限制 = {"召唤兽镶嵌", "召唤兽镶嵌"}
    elseif self.分类 == "变身卡" then
      self.物品限制 = {5, 5}
    elseif self.分类 == "星辉石" then
      self.物品限制 = {5, 5}
    elseif self.分类 == "钟灵石" then
      self.物品限制 = {5, 5}
    end
  elseif self.标识 == "修理" then
    if self.分类 == "人物装备" then
      self.物品限制 = {2,5}
    elseif self.分类 == "召唤兽装备" then
      self.物品限制 = {2}
    elseif self.分类 == "召唤兽装饰" then
      self.物品限制 = {2, 5}
    elseif self.分类 == "坐骑装饰" then
      self.物品限制 = {2, 5}
    end
  elseif self.标识 == "熔炼" then
    if self.分类 == "熔炼装备" then
      self.物品限制 = {2, 5}
    elseif self.分类 == "还原装备" then
      self.物品限制 = {2, 5}
    end
  elseif self.标识 == "分解" then
    if self.分类 == "分解装备" then
      self.物品限制 = {2, 5}
    elseif self.分类 == "分解灵犀玉" then
      self.物品限制 = {2, 5}
    elseif self.分类 == "宝石降级" then
      self.物品限制 = {2, 5}
    end
  elseif self.标识 == "神器" then
    if self.分类 == "人物装备" then
      self.物品限制 = {2, 300}
    elseif self.分类 == "人物装备重铸" then
      self.物品限制 = {2, 300}
    elseif self.分类 == "人物装备炼化" then
      self.物品限制 = {2, 300}
    elseif self.分类 == "灵饰打造" then
      self.物品限制 = {2, 300}
    elseif self.分类 == "灵饰重铸" then
      self.物品限制 = {2, 300}
    elseif self.分类 == "灵饰炼化" then
      self.物品限制 = {2, 300}
    end
  end
  -- print(打造.分类 , 打造.标识)
  if 打造.分类 == "星辉石" and 打造.标识 == "合成" then
    打造["材料网格"]:置可见(false)
    打造["xinghuishiwangge"]:置可见(true)
  else 
    打造["材料网格"]:置可见(true)
    打造["xinghuishiwangge"]:置可见(false)
  end
  self.道具网格["置物品"](self.道具网格, data)
  self.材料网格["置物品"](self.材料网格, {})
  self.xinghuishiwangge["置物品"](self.xinghuishiwangge, {})
  self.重置(self)
end
local 关闭 = 打造["创建我的按钮"](打造, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 727, 0)
function 关闭:左键弹起(x, y, msg)
  打造["置可见"](打造, false)
end
local 请选择 = 打造["创建我的按钮"](打造, __res:getPNGCC(3, 378, 347, 143, 37, true):拉伸(233, 37), "请选择", 93, 111, "请选择")
function 请选择:左键弹起(x, y, msg)
  __UI弹出["打造选择"]["打开"](__UI弹出["打造选择"], qufenlei(打造["标识"]))
end
local 道具网格 = 打造["创建网格"](打造, "道具网格", 31, 192, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] and not self.子控件[a]._spr["物品禁止"] then
    if 打造.分类 == "星辉石" and 打造.标识 == "合成" then
      for i = 1, #打造["xinghuishiwangge"]["子控件"] do
        if not 打造["xinghuishiwangge"]["子控件"][i]._spr["物品"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
          打造["xinghuishiwangge"]["置物品"](打造["xinghuishiwangge"], self.子控件[a]._spr["物品"], i)
          打造["xinghuishiwangge"]["子控件"][i]._spr["物品"]["原始编号"] = a
          self:置物品(nil, a)
          break
        end
      end
    else
      for i = 1, #打造["材料网格"]["子控件"] do
        if not 打造["材料网格"]["子控件"][i]._spr["物品"] then
          self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
          打造["材料网格"]["置物品"](打造["材料网格"], self.子控件[a]._spr["物品"], i)
          打造["材料网格"]["子控件"][i]._spr["物品"]["原始编号"] = a
          self:置物品(nil, a)
          break
        end
      end
    end
  end
end
function 道具网格:置物品(data, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], nil, "战斗道具")
      lssj["置偏移"](lssj, 10, 10)
      lssj["置禁止"](lssj, 打造["物品限制"][1])
      if lssj["物品禁止"] and 打造["物品限制"][2] then
        lssj["置禁止"](lssj, 打造["物品限制"][2])
      end
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data, nil, "战斗道具")
    lssj["置偏移"](lssj, 10, 10)
    lssj["置禁止"](lssj, 打造["物品限制"][1])
    if lssj["物品禁止"] and 打造["物品限制"][2] then
      lssj["置禁止"](lssj, 打造["物品限制"][2])
    end
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end
local 材料网格 = 打造["创建网格"](打造, "材料网格", 481, 182, 180, 55)
function 材料网格:初始化()
  self:创建格子(55, 55, 0, 70, 1, 2)
end
function 材料网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
    打造["道具网格"]["置物品"](打造["道具网格"], self.子控件[a]._spr["物品"], self.子控件[a]._spr["物品"]["原始编号"])
    self:置物品(nil, a)
  end
end
function 材料网格:置物品(数据, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __商店格子["创建"]()
      lssj["置物品"](lssj, 数据[i], "制造")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, 数据, "制造")
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end

local xinghuishiwangge = 打造["创建网格"](打造, "xinghuishiwangge", 481-68, 182, 326, 55)
function xinghuishiwangge:初始化()
  self:创建格子(55, 55, 0, 70, 1, 3)
end
function xinghuishiwangge:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
    打造["道具网格"]["置物品"](打造["道具网格"], self.子控件[a]._spr["物品"], self.子控件[a]._spr["物品"]["原始编号"])
    self:置物品(nil, a)
  end
end
function xinghuishiwangge:置物品(数据, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __商店格子["创建"]()
      lssj["置物品"](lssj, 数据[i], "制造")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, 数据, "制造")
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end



for i, v in ipairs({
  {
    name = "打造",
    x = 20,
    y = 61,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 95, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 95, 43),
    font = "打造"
  },
  {
    name = "镶嵌",
    x = 127,
    y = 61,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 95, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 95, 43),
    font = "镶嵌"
  },
  {
    name = "合成",
    x = 234,
    y = 61,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 95, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 95, 43),
    font = "合成"
  },
  {
    name = "修理",
    x = 341,
    y = 61,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 95, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 95, 43),
    font = "修理"
  },
  {
    name = "熔炼",
    x = 448,
    y = 61,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 95, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 95, 43),
    font = "熔炼"
  },
  {
    name = "分解",
    x = 555,
    y = 61,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 95, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 95, 43),
    font = "分解"
  },
  {
    name = "神器",
    x = 662,
    y = 61,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 95, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 95, 43),
    font = "神器"
  }
}) do
  local 临时函数 = 打造["创建我的单选按钮"](打造, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    qufenlei(v.name)
    打造["标识"] = v.name
    打造["分类"] = nil
    打造["刷新"](打造, 打造["数据"])
    打造["请选择"]["我的按钮置文字"](打造["请选择"], 打造["请选择"], __res:getPNGCC(3, 378, 347, 143, 37, true)["拉伸"](__res:getPNGCC(3, 378, 347, 143, 37, true), 233, 37), "请选择")
  end
end
local 打造按钮 = 打造["创建我的按钮"](打造, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "打造按钮", 625, 435, "打造")
function 打造按钮:左键弹起(x, y, msg)
  if 打造["标识"] and 打造["分类"] then
    local 允许 = false
    if 1 == #打造["物品限制"] and 打造["材料网格"]["子控件"][1] and 打造["材料网格"]["子控件"][1]._spr["物品"] then
      允许 = true
      发送数据(4501, {
        ["序列"] = 打造["材料网格"]["子控件"][1]._spr["物品"]["原始编号"],
        ["序列1"] = nil,
        ["分类标识"] = 打造["标识"],
        ["功能标识"] = 打造["分类"]
      })
    elseif 2 == #打造["物品限制"] and 打造.分类 == "星辉石" and 打造.标识 == "合成" and 打造["xinghuishiwangge"]["子控件"] and 打造["xinghuishiwangge"]["子控件"][1]._spr["物品"] and 打造["xinghuishiwangge"]["子控件"][2] and 打造["xinghuishiwangge"]["子控件"][2]._spr["物品"] and 打造["xinghuishiwangge"]["子控件"][3] and 打造["xinghuishiwangge"]["子控件"][3]._spr["物品"] then
      发送数据(4501, {
        ["序列"] = 打造["xinghuishiwangge"]["子控件"][1]._spr["物品"]["原始编号"],
        ["序列1"] = 打造["xinghuishiwangge"]["子控件"][2]._spr["物品"]["原始编号"],
        ["序列3"]=打造["xinghuishiwangge"]["子控件"][3]._spr["物品"]["原始编号"],
        ["分类标识"] = 打造["标识"],
        ["功能标识"] = 打造["分类"]
      })
    elseif 2 == #打造["物品限制"] and 打造["材料网格"]["子控件"][1] and 打造["材料网格"]["子控件"][1]._spr["物品"] and 打造["材料网格"]["子控件"][2] and 打造["材料网格"]["子控件"][2]._spr["物品"] then
      允许 = true
      if 打造.分类 == "熔炼" and 打造.标识 == "还原装备" then 
        local 事件 = function()
            发送数据(4501, {
              ["序列"] = 打造["材料网格"]["子控件"][1]._spr["物品"]["原始编号"],
              ["序列1"] = 打造["材料网格"]["子控件"][2]._spr["物品"]["原始编号"],
              -- ["序列3"]=打造["材料网格"]["子控件"][3]._spr["物品"]["原始编号"],
              ["分类标识"] = 打造["标识"],
              ["功能标识"] = 打造["分类"]
            })
        end
        __UI界面.窗口层.文本栏:打开("你确定要还原这个装备的#R熔炼效果#W吗？", 285, 155, 390, 200, 事件)
      else
        发送数据(4501, {
          ["序列"] = 打造["材料网格"]["子控件"][1]._spr["物品"]["原始编号"],
          ["序列1"] = 打造["材料网格"]["子控件"][2]._spr["物品"]["原始编号"],
          -- ["序列3"]=打造["材料网格"]["子控件"][3]._spr["物品"]["原始编号"],
          ["分类标识"] = 打造["标识"],
          ["功能标识"] = 打造["分类"]
        })
      end
    end
    if not 允许 then
      __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "请放置需要材料！")
    end
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "请选择的需要打造的项目！")
  end
end
