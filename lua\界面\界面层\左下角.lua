--[[
Author: GGELUA
Date: 2023-02-12 19:59:09
Last Modified by: GGELUA
Last Modified time: 2023-02-16 12:58:29
--]]
-- <AUTHOR> GGELUA
-- Last Modified by: GGELUA
-- @Date                : 2022-11-02 01:27:45
-- Last Modified time: 2023-02-16 13:00:48
local 左下角 = __UI界面["界面层"]["创建控件"](__UI界面["界面层"], "左下角", 1, 320-167 + abbr.py.y, 400, 220+167) --（就像取矩形框一样的范围）
function 左下角:初始化() 
    local nsf = require("SDL.图像")(482, 378+10)
    if nsf["渲染开始"](nsf) then
        local tuxiang=__res["UI素材"][1]["复制区域"](__res["UI素材"][1], 305, 371, 366, 169) --SDL图像:复制区域(x, y, w, h) 图片中 的像素区域
        tuxiang["显示"](tuxiang, -10, 54+167) --显示格式
        nsf["渲染结束"](nsf) --删除
    end
    self.图像 = nsf["到精灵"](nsf)
    self.消息缓存 = {
        xt = {},
        sj = {},
        dq = {},
        sl = {},
        -- gm = {},
        cw = {},
        bp = {},
        cy = {},
        dw = {}
    }
end

function 左下角:显示(x, y)
    if self.聊天控件.是否可见 then
        self.图像["显示"](self.图像, x, y)
    end
end






local 拓展按钮 = 左下角["创建我的按钮"](左下角, __res:getPNGCC(1, 617, 203, 55, 55), "拓展按钮", 65, 170)
function 拓展按钮:左键弹起(x, y, msg)
    左下角.拓展界面:置可见(not 左下角.拓展界面.是否可见)
    左下角.聊天控件:置可见(not 左下角.拓展界面.是否可见)
end

local 拓展界面 = 左下角:创建控件("拓展界面", 1, 63, 245, 325)

function 拓展界面:初始化()
    
end

local 隐藏玩家 = 拓展界面:创建我的多选按钮(__res.UI素材[4]:复制区域(562, 378, 69, 67), __res.UI素材[4]:复制区域(562, 454, 69, 67), "隐藏玩家", 8, 3)
左下角显示玩家=true
function 隐藏玩家:左键弹起(x, y, msg)
    左下角显示玩家=隐藏玩家["是否选中"]
end
local 隐藏摊位 = 拓展界面:创建我的多选按钮(__res.UI素材[4]:复制区域(636, 378, 69, 67), __res.UI素材[4]:复制区域(636, 454, 69, 67), "隐藏摊位", 8, 3+77*1)
左下角显示摊位=true
function 隐藏摊位:左键弹起(x, y, msg)
    左下角显示摊位=隐藏摊位["是否选中"]
end
local 隐藏界面 =拓展界面:创建我的多选按钮(__res.UI素材[4]:复制区域(712, 378, 69, 67), __res.UI素材[4]:复制区域(712, 454, 69, 67), "隐藏界面", 8, 3+77*2)
左下角显示界面=true
function 隐藏界面:左键弹起(x, y, msg)
    左下角显示界面=隐藏界面["是否选中"]
    __UI界面["界面层"]["左上角"]:置可见(左下角显示界面)
    __UI界面["界面层"]["右下角"]:置可见(左下角显示界面)
    __UI界面["界面层"]["右上角"]:置可见(左下角显示界面)
end
local 聊天控件 = 左下角:创建控件("聊天控件", 6, 1+167, 361, 218)
function 拓展界面:初始化()
end
local 频道 = 聊天控件["创建我的按钮"](聊天控件, __res:getPNGCC(1, 185, 272, 53, 53), "频道", 6, 0)
function 频道:左键弹起(x, y, msg)
    __UI界面["窗口层"]["消息管理"]["打开"](__UI界面["窗口层"]["消息管理"], 左下角["消息缓存"])
end

local 聊天文本 = 聊天控件["创建我的文本"](聊天控件, "聊天文本", 10, 70, 340, 130, true)

function 聊天文本:初始化()
    -- self._文字表.默认=字体14
    self:置文字(字体15)
end

function 聊天文本:添加文本(文本, 频道)
    if self._max > 1500 then
        self.清空(self)
        左下角["消息缓存"] = {
            xt = {},
            sj = {},
            dq = {},
            sl = {},
            -- gm = {},
            cw = {},
            bp = {},
            cy = {},
            dw = {}
        }
    end
    if not 频道 or not __频道表[频道] then
        频道 = "dq"
    end
    table.insert(左下角["消息缓存"][频道], 文本)
    文本 = 文本 or " "
    self:置文本("#" .. __频道表[频道] .. 文本)
    if __UI界面["窗口层"]["消息管理"][频道] and __UI界面["窗口层"]["消息管理"][频道]["是否选中"] then
        __UI界面["窗口层"]["消息管理"]["聊天文本"]["添加文本"](__UI界面["窗口层"]["消息管理"]["聊天文本"], 文本, 频道)
    end
    self._py = -self._max
end

function 聊天文本:回调左键弹起(cb, msg)
    -- print(cb, msg)
    if cb then
        local lssj = 分割文本(cb, "*")
        if "玩家信息" == lssj[3] then
            __UI弹出["玩家信息弹出"]["打开"](__UI弹出["玩家信息弹出"], {名称=lssj[1],ID = lssj[4]})
            -- __UI界面.界面层.右上角.玩家头像.头像网格:置头像({
            --     模型 = lssj[2],
            --     名称 = lssj[1],
            --     ID = lssj[4],
            --     pid = nil
            -- })
        elseif  lssj[3] == "召唤兽" then
            for i,v in ipairs(chaolianjieshuju) do
                if  lssj[3] == v.索引类型 and lssj[1] == v.名称 and lssj[2] == v.认证码 then
                    __UI界面["窗口层"]["召唤兽查看"]:打开(v)
                    break
                end
            end
            -- table.print(lssj)
        elseif  lssj[3] == "道具" then
            for i,v in ipairs(chaolianjieshuju) do
                if lssj[3] == v.索引类型 and lssj[1] == v.名称 and lssj[2] == v.识别码 then
                    local wwewq = __物品格子["创建"]()
                    -- wwewq["置物品"](wwewq, v, nil, "临时背包")
                    wwewq:取数据(v)
                    wwewq["详情打开"](wwewq, 170+254-232, 86, w, h, "选择", a)
                    break
                end
            end
        end
    end
end
