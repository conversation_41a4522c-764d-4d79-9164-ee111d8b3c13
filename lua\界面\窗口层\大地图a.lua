--[[
LastEditTime: 2024-05-06 20:34:28
--]]

local  大地图a = 窗口层:创建窗口("大地图a",0, 0, 670, 510)
function  大地图a:初始化()
  self:创建纹理精灵(function()
                        取黑色背景(0, 0, 670, 510, true):显示(0, 0)
                       __res:取资源动画('dlzy',0xBF90A4C2,"图像"):显示(15, 15)
                    end
              )
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true

    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)


end

function  大地图a:打开(传送)
    self:置可见(true)
    self.超级传送=nil
    if 传送 then
      self.超级传送=传送
    end
end



local 方寸山 = 大地图a:创建按钮("方寸山", 542+15, 7+15)
function 方寸山:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x6A154DDE),1)
end
function 方寸山:左键弹起(x, y)
  窗口层.世界小地图:打开(1135,大地图a.超级传送)
  大地图a:置可见(false)
end
local 长寿村 = 大地图a:创建按钮( "长寿村", 414+15, 115+15)
function 长寿村:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x637B3C48),1)
end
function 长寿村:左键弹起(x, y)
    窗口层.世界小地图:打开(1070,大地图a.超级传送)
  大地图a:置可见(false)
end
local 宝象国 = 大地图a:创建按钮("宝象国", 361+15, 190+15)
function 宝象国:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x0D906C8D),1)
end
function 宝象国:左键弹起(x, y)
  窗口层.世界小地图:打开(1226,大地图a.超级传送)
  大地图a:置可见(false)
end


local 朱紫国 = 大地图a:创建按钮("朱紫国", 264+15, 166+15)
function 朱紫国:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x689EB676),1)
end
function 朱紫国:左键弹起(x, y)
   窗口层.世界小地图:打开(1208,大地图a.超级传送)
  大地图a:置可见(false)
end

local 西凉女国 = 大地图a:创建按钮("西凉女国", 488+15, 335+15)
function 西凉女国:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xE329AD6A),1)
end
function 西凉女国:左键弹起(x, y)
  窗口层.世界小地图:打开(1040,大地图a.超级传送)
  大地图a:置可见(false)
end


local 麒麟山 = 大地图a:创建按钮( "麒麟山", 298+15, 269+15)
function 麒麟山:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x048DA00D),1)
end
function 麒麟山:左键弹起(x, y)
  窗口层.世界小地图:打开(1210,大地图a.超级传送)
  大地图a:置可见(false)
end
local 无底洞 = 大地图a:创建按钮( "无底洞", 386+15, 220+24)
function 无底洞:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xBFF5620B),1)
end
function 无底洞:左键弹起(x, y)
  窗口层.世界小地图:打开(1139,大地图a.超级传送)
  大地图a:置可见(false)
end
local 比丘国 = 大地图a:创建按钮( "比丘国", 362+15+15, 303+15+24)
function 比丘国:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x63566DA9),1)
end
function 比丘国:左键弹起(x, y)
  窗口层.世界小地图:打开(1232,大地图a.超级传送)
  大地图a:置可见(false)
end
local 关闭 = 大地图a:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  大地图a:置可见(false)
end









