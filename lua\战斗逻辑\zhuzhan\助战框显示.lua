
local 助战框显示 = __UI界面["界面层"]["创建我的控件"](__UI界面["界面层"], "助战框显示",0, 81, 440, 450)
function 助战框显示:初始化()
    self.hbjnpng= __res:getPNGCC(5, 904, 0, 151, 52)
    self.ziy4= __res:getPNGCC(5, 1031, 110, 46, 48)
	self.hbk= __res:getPNGCC(5, 904, 215, 198, 57):到精灵()
	self.quanquan= __res:getPNGCC(5, 1031, 163, 50, 50)--:到精灵()
    self.伙伴头像={}
end


-- local 关闭 = 助战框显示["创建我的按钮"](助战框显示, __res:getPNGCC(5, 903, 60, 37, 37), "关闭", 2, 60)


function 助战框显示:进入战斗()
	self.伙伴头像={}
	__UI界面["界面层"]["助战操作界面"]:进入战斗()
end
function 助战框显示:退出战斗()
	self.伙伴头像={}
	self:置可见(false)
	__UI界面["界面层"]["助战操作界面"]:退出战斗()
end

local 门派缩写={}
门派缩写["大唐官府"]="大\n唐"
门派缩写["化生寺"]="化\n生"
门派缩写["龙宫"]="龙\n宫"
门派缩写["魔王寨"]="魔\n王"
门派缩写["神木林"]="神\n木"
门派缩写["方寸山"]="方\n寸"
门派缩写["女儿村"]="女\n儿"
门派缩写["天宫"]="天\n宫"
门派缩写["普陀山"]="普\n陀"
门派缩写["盘丝洞"]="盘\n丝"
门派缩写["阴曹地府"]="地\n府"
门派缩写["狮驼岭"]="狮\n驼"
门派缩写["五庄观"]="五\n庄"
门派缩写["无底洞"]="无\n底"
门派缩写["凌波城"]="凌\n波"
门派缩写["花果山"]="花\n果"
门派缩写["九黎城"]="九\n黎"
门派缩写["无门派"]="无"
function 助战框显示:加载伙伴头像(单位,编号)
    -- local x = 引擎.取头像(单位.模型)
	if not self.是否可见 then
		self:置可见(true,true)
		-- self.ycangkongjian:置可见(true)
	end
    -- print(self.伙伴头像)
	self.伙伴头像[#self.伙伴头像+1]={}--tp.资源:载入(x[7],"网易WDF动画",x[2]) --资源:载入('wzife.wd3',"网易WDF动画",4254597813)
    self.伙伴头像[#self.伙伴头像].头像=单位.模型
	self.伙伴头像[#self.伙伴头像].num=编号
	self.伙伴头像[#self.伙伴头像].助战id=单位.助战小号.id
	-- self.伙伴头像[#self.伙伴头像].追加法术=单位.追加法术
	self.伙伴头像[#self.伙伴头像].名称=单位.数据.名称
	self.伙伴头像[#self.伙伴头像].门派=门派缩写[单位.数据.门派] or "无"
	self.伙伴头像[#self.伙伴头像].宝宝技能=nil
    -- print(单位.模型)
end

local 隐藏按钮 = 助战框显示["创建我的按钮"](助战框显示, __res:getPNGCC(5, 0, 764, 14, 265):拉伸(14, 226), "隐藏按钮", 0, 1)
function 隐藏按钮:左键弹起(x, y)
	if 助战框显示.右侧技能控件.是否可见 then
		助战框显示.右侧技能控件:置可见(false)
		助战框显示.图像=nil
		__UI界面["界面层"]["助战操作界面"]["置可见"](__UI界面["界面层"]["助战操作界面"], false)
	else
		助战框显示.右侧技能控件:置可见(true)
		if not 助战框显示.图像 then
			助战框显示:载入头像显示()
		end
		-- 助战框显示.图像=助战框显示.tuxianghuancun
	end
end

function 助战框显示:载入头像显示()
    -- print("助战框显示:载入头像显示()")
    if self.伙伴头像 then
        nsf = require("SDL.图像")(246, 246)
        if nsf["渲染开始"](nsf) then
            for i=1,#self.伙伴头像 do
                self.hbjnpng:显示(54+3,(i-1)*58)
                __res["取图像"](__res, "shape/ui/360B8373.tcp"):显示(13+3,(i-1)*58+1)
                local lssj = 取头像(self.伙伴头像[i].头像)
                __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])):显示(13+3+3,(i-1)*58+3+1)
				字体18:置颜色(__取颜色("白色"))
				print(self.伙伴头像[i].门派)
				字体18:取图像(self.伙伴头像[i].门派 or "无"):显示(58+9+3,(i-1)*58+14-7)
            end
        end
        self.图像 = nsf["到精灵"](nsf)
		-- self:置精灵(nsf["到精灵"](nsf))
    end
end

function 助战框显示:伙伴设置命令(数据) --	self.战斗指令:更新类型(数据)
	--table.print(数据)
    -- self.jinengshuju={}
	for i=1,#self.伙伴头像 do
		if self.伙伴头像[i] and self.伙伴头像[i].助战id==数据.助战id then
            -- self.jinengshuju[#self.jinengshuju+1]={}
			local 自动="攻击"
			if 数据.zdzl and 数据.zdzl[1] then
				if 数据.zdzl[1].类型~="法术" then
					自动=数据.zdzl[1].类型
				else
					if type(数据.zdzl[1].参数)=="table" then
						自动=数据.zdzl[1].参数[1][1]
					else
						自动=数据.zdzl[1].参数
					end
				end
			end
			if 自动=="道具" then
				自动="攻击"
			end
			-- 自动="龙卷雨击"
			local x,是否技能 = 取技能展示(自动) --数据.zdzl1
            local mx
            if 是否技能 then
                mx="shape/jn/"
            else
                mx="shape/dj/"
                -- __res:取图像(__res:取地址("shape/jn/", lssc[7])):拉伸(45, 45):显示(0,0)
            end
			local py= {0,0}
			if x[10] then
				py=x[10]
			end
            -- print(mx)
			self.伙伴头像[i].伙伴技能={名称=自动,技能模型={mx,x[7]},偏移=py,主动技能组=数据.zdjn1,自动技能=自动}
			self.伙伴头像[i].操作数据=数据
			if #数据.id>=2 then
				自动="攻击"
				if 数据.zdzl and 数据.zdzl[2] then
					if 数据.zdzl[2].类型~="法术" then
						自动=数据.zdzl[2].类型
					else
						自动=数据.zdzl[2].参数
					end
				end
				if 自动=="道具" then
					自动="攻击"
				end
				local x,是否技能 = 取技能展示(自动) --数据.zdzl1
				local mx
				if 是否技能 then
					mx="shape/jn/"
				else
					mx="shape/dj/"
				end
				py= {0,0}
				if x[10] then
					py=x[10]
				end
				self.伙伴头像[i].宝宝技能={名称=自动,技能模型={mx,x[7]},偏移=py,主动技能组=数据.zdjn2,自动技能=自动}
			else
				self.伙伴头像[i].宝宝技能=nil
			end
			break
		end
	end
	for i=1,4 do
		if self.伙伴头像[i] then
			助战框显示.右侧技能控件["jinengwangge__"..i]:置可见(true)
			助战框显示.右侧技能控件["jinengwangge__"..i]:置数据1(self.伙伴头像[i].伙伴技能,self.伙伴头像[i].宝宝技能)
		else
			助战框显示.右侧技能控件["jinengwangge__"..i]:置可见(false)
		end
	end
end

function 助战框显示:更新战斗图标(编号,类型,指令)
	if self.伙伴头像[编号] then
		if 类型==1 then
			if self.伙伴头像[编号].伙伴技能 then
				local x,是否技能 = 取技能展示(指令) --数据.zdzl1
				local mx
				if 是否技能 then
					mx="shape/jn/"
				else
					mx="shape/dj/"
					-- __res:取图像(__res:取地址("shape/jn/", lssc[7])):拉伸(45, 45):显示(0,0)
				end
				local py= {0,0}
				if x[10] then
					py=x[10]
				end
				self.伙伴头像[编号].伙伴技能.名称=指令
				self.伙伴头像[编号].伙伴技能.技能模型={mx,x[7]}
				self.伙伴头像[编号].伙伴技能.偏移=py
				self.伙伴头像[编号].伙伴技能.自动技能=指令
			end
		else
			if self.伙伴头像[编号].宝宝技能 then
				local x,是否技能 = 取技能展示(指令) --数据.zdzl1
				local mx
				if 是否技能 then
					mx="shape/jn/"
				else
					mx="shape/dj/"
				end
				py= {0,0}
				if x[10] then
					py=x[10]
				end
				self.伙伴头像[编号].宝宝技能.名称=指令
				self.伙伴头像[编号].宝宝技能.技能模型={mx,x[7]}
				self.伙伴头像[编号].宝宝技能.偏移=py
				self.伙伴头像[编号].宝宝技能.自动技能=指令
			end
		end
		助战框显示.右侧技能控件["jinengwangge__"..编号]:置数据1(self.伙伴头像[编号].伙伴技能,self.伙伴头像[编号].宝宝技能)
	end
end

local 右侧技能控件= 助战框显示["创建控件"](助战框显示, "右侧技能控件", 90+3, 3, 110, 246)
local jinengwangge__1=右侧技能控件["创建网格"](右侧技能控件, "jinengwangge__1", 0, 0, 110, 246)
function jinengwangge__1:初始化()
end
function jinengwangge__1:左键弹起(x, y, a, b, msg)
	-- print(x, y, a, b)
	-- 助战框显示.=1
	if  __战斗主控["进程"]=="命令" then
		if a==1 then
			__UI界面["界面层"]["助战操作界面"]:更新角色类型(助战框显示.伙伴头像[1].操作数据,1)
		elseif a==2 then
			__UI界面["界面层"]["助战操作界面"]:更新bb类型(助战框显示.伙伴头像[1].操作数据,1)
		end
	end
	-- __UI界面["窗口层"].助战法术:打开({}, lx, silllx,1,a)
	-- self:置数据1(助战框显示.伙伴头像[1].伙伴技能,助战框显示.伙伴头像[1].宝宝技能,a)
end
function jinengwangge__1:置数据1(juese,bb,xuanzhong)
	local jinengshuju={juese,bb}
    self:创建格子(50, 50, 0, 5, 1, #jinengshuju)
	for i = 1, #self["子控件"] do
		if jinengshuju[i] then
		  local nsf = require("SDL.图像")(110, 50)
		  if nsf["渲染开始"](nsf) then
			-- nsf["渲染清除"](nsf, 0, 0, 0, 255)
			if jinengshuju[i]["剩余冷却回合"] then
				__res:取图像(__res:取地址(jinengshuju[i].技能模型[1], jinengshuju[i].技能模型[2])):拉伸(40, 40):显示(3,3)
			else
				__res:取图像(__res:取地址(jinengshuju[i].技能模型[1], jinengshuju[i].技能模型[2])):拉伸(40, 40):显示(3,3)
			end
			助战框显示.ziy4:显示(0,0)
			if xuanzhong then
				-- print(xuanzhong)
				助战框显示.quanquan:显示((xuanzhong-1)*60,0)
			end
			nsf["渲染结束"](nsf)
		  end
		  local sc = nsf["到精灵"](nsf)
		  -- sc["置混合"](sc, 2)
		  self["子控件"][i]["置精灵"](self["子控件"][i], sc)
		  self["子控件"][i]["技能信息"] = jinengshuju[i]
		else
		  self["子控件"][i]["置精灵"](self["子控件"][i])
		end
	end
end






local jinengwangge__2=右侧技能控件["创建网格"](右侧技能控件, "jinengwangge__2", 0, 0+58*1, 110, 246)
function jinengwangge__2:初始化()
end
function jinengwangge__2:左键弹起(x, y, a, b, msg)
	if  __战斗主控["进程"]=="命令" then
		if a==1 then
			__UI界面["界面层"]["助战操作界面"]:更新角色类型(助战框显示.伙伴头像[2].操作数据,2)
		elseif a==2 then
			__UI界面["界面层"]["助战操作界面"]:更新bb类型(助战框显示.伙伴头像[2].操作数据,2)
		end
	end
end
function jinengwangge__2:置数据1(juese,bb)
	local jinengshuju={juese,bb}
	self:创建格子(50, 50, 0, 5, 1, #jinengshuju)
	for i = 1, #self["子控件"] do
		if jinengshuju[i] then
		  local nsf = require("SDL.图像")(110, 50)
		  if nsf["渲染开始"](nsf) then
			-- nsf["渲染清除"](nsf, 0, 0, 0, 255)
			if jinengshuju[i]["剩余冷却回合"] then
				__res:取图像(__res:取地址(jinengshuju[i].技能模型[1], jinengshuju[i].技能模型[2])):拉伸(40, 40):显示(3,3)
			else
				__res:取图像(__res:取地址(jinengshuju[i].技能模型[1], jinengshuju[i].技能模型[2])):拉伸(40, 40):显示(3,3)
			end
			助战框显示.ziy4:显示(0,0)
			if xuanzhong then
				-- print(xuanzhong)
				助战框显示.quanquan:显示((xuanzhong-1)*60,0)
			end
			nsf["渲染结束"](nsf)
		  end
		  local sc = nsf["到精灵"](nsf)
		  -- sc["置混合"](sc, 2)
		  self["子控件"][i]["置精灵"](self["子控件"][i], sc)
		  self["子控件"][i]["技能信息"] = jinengshuju[i]
		else
		  self["子控件"][i]["置精灵"](self["子控件"][i])
		end
	end
end




local jinengwangge__3=右侧技能控件["创建网格"](右侧技能控件, "jinengwangge__3", 0, 0+58*2, 110, 246)
function jinengwangge__3:初始化()
end
function jinengwangge__3:左键弹起(x, y, a, b, msg)
	if  __战斗主控["进程"]=="命令" then
		if a==1 then
			__UI界面["界面层"]["助战操作界面"]:更新角色类型(助战框显示.伙伴头像[3].操作数据,3)
		elseif a==2 then
			__UI界面["界面层"]["助战操作界面"]:更新bb类型(助战框显示.伙伴头像[3].操作数据,3)
		end
	end
end
function jinengwangge__3:置数据1(juese,bb)
	local jinengshuju={juese,bb}
	self:创建格子(50, 50, 0, 5, 1, #jinengshuju)
	for i = 1, #self["子控件"] do
		if jinengshuju[i] then
		  local nsf = require("SDL.图像")(110, 50)
		  if nsf["渲染开始"](nsf) then
			-- nsf["渲染清除"](nsf, 0, 0, 0, 255)
			if jinengshuju[i]["剩余冷却回合"] then
				__res:取图像(__res:取地址(jinengshuju[i].技能模型[1], jinengshuju[i].技能模型[2])):拉伸(40, 40):显示(3,3)
			else
				__res:取图像(__res:取地址(jinengshuju[i].技能模型[1], jinengshuju[i].技能模型[2])):拉伸(40, 40):显示(3,3)
			end
			助战框显示.ziy4:显示(0,0)
			if xuanzhong then
				-- print(xuanzhong)
				助战框显示.quanquan:显示((xuanzhong-1)*60,0)
			end
			nsf["渲染结束"](nsf)
		  end
		  local sc = nsf["到精灵"](nsf)
		  -- sc["置混合"](sc, 2)
		  self["子控件"][i]["置精灵"](self["子控件"][i], sc)
		  self["子控件"][i]["技能信息"] = jinengshuju[i]
		else
		  self["子控件"][i]["置精灵"](self["子控件"][i])
		end
	end
end





local jinengwangge__4=右侧技能控件["创建网格"](右侧技能控件, "jinengwangge__4", 0, 0+58*3, 110, 246)
function jinengwangge__4:初始化()
end
function jinengwangge__4:左键弹起(x, y, a, b, msg)
	if  __战斗主控["进程"]=="命令" then
		if a==1 then
			__UI界面["界面层"]["助战操作界面"]:更新角色类型(助战框显示.伙伴头像[4].操作数据,4)
		elseif a==2 then
			__UI界面["界面层"]["助战操作界面"]:更新bb类型(助战框显示.伙伴头像[4].操作数据,4)
		end
	end
end
function jinengwangge__4:置数据1(juese,bb)
    local jinengshuju={juese,bb}
	self:创建格子(50, 50, 0, 5, 1, #jinengshuju)
	for i = 1, #self["子控件"] do
		if jinengshuju[i] then
		  local nsf = require("SDL.图像")(110, 50)
		  if nsf["渲染开始"](nsf) then
			-- nsf["渲染清除"](nsf, 0, 0, 0, 255)
			if jinengshuju[i]["剩余冷却回合"] then
				__res:取图像(__res:取地址(jinengshuju[i].技能模型[1], jinengshuju[i].技能模型[2])):拉伸(40, 40):显示(3,3)
			else
				__res:取图像(__res:取地址(jinengshuju[i].技能模型[1], jinengshuju[i].技能模型[2])):拉伸(40, 40):显示(3,3)
			end
			助战框显示.ziy4:显示(0,0)
			if xuanzhong then
				-- print(xuanzhong)
				助战框显示.quanquan:显示((xuanzhong-1)*60,0)
			end
			nsf["渲染结束"](nsf)
		  end
		  local sc = nsf["到精灵"](nsf)
		  -- sc["置混合"](sc, 2)
		  self["子控件"][i]["置精灵"](self["子控件"][i], sc)
		  self["子控件"][i]["技能信息"] = jinengshuju[i]
		else
		  self["子控件"][i]["置精灵"](self["子控件"][i])
		end
	end
end