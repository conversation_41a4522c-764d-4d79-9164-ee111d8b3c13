

local  大地图c = 窗口层:创建窗口("大地图c",0, 0, 670, 510)
function  大地图c:初始化()
  self:创建纹理精灵(function()
              取黑色背景(0, 0, 670, 510, true):显示(0, 0)
              __res:取资源动画('dlzy',0x26E89B1F,"图像"):显示(15, 15)
            end
          )

    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true

    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
end

function  大地图c:打开(传送)
    self:置可见(true)
    self.超级传送=nil
    if 传送 then
      self.超级传送=传送
    end
    
end

local 花果山 = 大地图c:创建按钮("花果山", 263+15, 10+15)
function 花果山:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x6B27F57F), 1)
end
function 花果山:左键弹起(x, y)
  窗口层.世界小地图:打开(1514,大地图c.超级传送)
  大地图c:置可见(false)
end

local 女儿村 = 大地图c:创建按钮( "女儿村", 211+15, 160+15)
function 女儿村:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x6BF13E64), 1)
end
function 女儿村:左键弹起(x, y)
  窗口层.世界小地图:打开(1142,大地图c.超级传送)
  大地图c:置可见(false)
end
local 傲来国 = 大地图c:创建按钮( "傲来国", 344+15, 272+15)
function 傲来国:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xCCB7A7C3),1)
end
function 傲来国:左键弹起(x, y)
  窗口层.世界小地图:打开(1092,大地图c.超级传送)
  大地图c:置可见(false)
end
local 蓬莱仙岛 = 大地图c:创建按钮( "蓬莱仙岛", 149+15, 312+15)
function 蓬莱仙岛:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xA9BC7D93),1)
end
function 蓬莱仙岛:左键弹起(x, y)
  窗口层.世界小地图:打开(1207,大地图c.超级传送)
  大地图c:置可见(false)
end

local 关闭 = 大地图c:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  大地图c:置可见(false)
end

