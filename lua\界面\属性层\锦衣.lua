
local 锦衣 = 窗口层:创建窗口("锦衣",0, 0, 120, 150)

function 锦衣:初始化()
        self:创建纹理精灵(function()
          置窗口背景("锦衣", 0, 0, 120, 150, true):显示(0, 0)
          __res:取资源动画("dlzy", 0x841CBD61,"图像"):显示(5, 33)
          __res:取资源动画("dlzy", 0x9912E8B9,"图像"):显示(60, 33)
          __res:取资源动画("dlzy", 0x5795605E,"图像"):显示(5, 88)
          __res:取资源动画("dlzy", 0xEBD8985D,"图像"):显示(60, 88)
 

      
        end)
        self.可初始化=true
        if __手机 then
            self.关闭:置大小(25,25)
            self.关闭:置坐标(self.宽度-27, 2)
        else
            self.关闭:置大小(16,16)
            self.关闭:置坐标(self.宽度-18, 2)
        end
end




function 锦衣:更新(dt)
  if self.开始x and self.开始y then
        local xx,yy=窗口层.新行囊:取坐标()
        if self.开始x~=xx or self.开始y~=yy then
            if xx + 窗口层.新行囊.宽度+self.宽度<引擎.宽度 then
                  self:置坐标(xx + 窗口层.新行囊.宽度, yy)
            else
                self:置坐标(xx - self.宽度, yy)
            end
            self.开始x,self.开始y=窗口层.新行囊:取坐标()
        end

  end
  if  not 窗口层.新行囊.是否可见 and self.是否可见 then
      self:置可见(false)
  end
end


function 锦衣:打开()
        窗口层.灵饰:置可见(false)
        self:置可见(not self.是否可见)
        if not self.是否可见 then
            return
        end
        self.锦衣网格:置数据(角色信息.锦衣)
        self.开始x,self.开始y=窗口层.新行囊:取坐标()
        if self.开始x + 窗口层.新行囊.宽度+self.宽度<引擎.宽度 then
              self:置坐标(self.开始x + 窗口层.新行囊.宽度, self.开始y)
        else
            self:置坐标(self.开始x - self.宽度, self.开始y)
        end
        


end

function 锦衣:刷新()
        窗口层.灵饰:置可见(false)
        self.锦衣网格:置数据(角色信息.锦衣)
end

local 锦衣网格 = 锦衣:创建网格("锦衣网格", 5, 33, 110, 110)
function 锦衣网格:初始化()
   self:创建格子(52, 52, 3, 3, 2, 2)

end

function 锦衣网格:获得鼠标(x, y,a)
    窗口层.新行囊.装备网格:失去鼠标()
    if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr  and self.子控件[self.焦点]._spr.焦点 then
        self.子控件[self.焦点]._spr.焦点=nil
    end
    self.子控件[a]._spr.焦点=true
    self.焦点=a
    if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not 鼠标层.附加  then
        __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end
function 锦衣网格:失去鼠标(x, y)
  for i = 1, #self.子控件 do
    self.子控件[i]._spr.焦点=nil
  end
  self.焦点=nil

end

function 锦衣网格:右键弹起(x, y, a)
      if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
          self.子控件[a]._spr.确定=nil
          请求服务(3704,{类型=窗口层.新行囊.包裹类型,锦衣=true,角色="主角",道具=a})
      end
end


function 锦衣网格:左键弹起(x, y, a)
        local 物品=窗口层.新行囊.道具网格:选中物品()

        if 窗口层.新行囊.抓取物品ID and 窗口层.新行囊.抓取类型 and 鼠标层.附加 and 物品 and 物品.物品 and 物品.物品.分类>=15 and 物品.物品.分类<=17  then
                  请求服务(3703,{类型=窗口层.新行囊.抓取类型,角色="主角",锦衣=true,道具=窗口层.新行囊.抓取物品ID})
        elseif self.子控件[a]._spr and self.子控件[a]._spr.物品 then
              if __手机 then
                  __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,锦衣网格,"卸下",a)
              else
                  self:卸下(a)
              end
        end
end


function 锦衣网格:卸下(编号)
        if 编号 and 编号~=0 then
            请求服务(3704,{类型=窗口层.新行囊.包裹类型,锦衣=true,角色="主角",道具=编号})
        end

end


function 锦衣网格:置数据(数据)
  for i = 1, #self.子控件 do
      local lssj = __物品格子:创建()
      lssj:置物品(nil,52,52)
      if 数据 and 数据[i] then
          lssj:置物品(数据[i],52,52, nil,true)
      end
      self.子控件[i]:置精灵(lssj)
  end
end
local 关闭 = 锦衣:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    锦衣:置可见(false)
end
