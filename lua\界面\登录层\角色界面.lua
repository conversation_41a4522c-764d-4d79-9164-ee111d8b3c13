--[[
LastEditTime: 2024-10-30 18:36:54
--]]
--[[
LastEditTime: 2024-10-09 20:40:40
--]]
local 选中
local 角色界面 = 登录层:创建控件("角色界面",0, 0, 引擎.宽度, 引擎.高度)
function 角色界面:初始化()
    self.信息背景 =  __res:取资源动画('ui',0x89C23A51,"精灵")
    self.选择角色 =  __res:取资源动画('ui',0xCA43E749,"精灵")
    self.角色列表 = {}
end
function 角色界面:显示(x, y)
   self.信息背景:显示(引擎.宽度2-170, 引擎.高度-105)
    self.选择角色:显示(引擎.宽度2-75, 64)
    if self.图像 then
      self.图像:显示(引擎.宽度2-170, 引擎.高度-105)
      
    end
end
function 角色界面:打开(sj)
    选中 = nil
    self:置可见(true, true)
    self.角色列表 = sj
    self.角色网格:置数据(self.角色列表)
    self.下一步:置可见(false)
    self.图像=nil
    
end
function 角色界面:名称显示()
  self.图像=nil
  if 选中 then
    self.图像=self:创建纹理精灵(function()
        标题字体:置颜色(255,255,255,255)
        标题字体:取图像(self.角色列表[选中].名称):显示(75,8)
        标题字体:取图像(self.角色列表[选中].id):显示(250,8)
        标题字体:取图像(self.角色列表[选中].等级):显示(75, 51)
        标题字体:取图像(self.角色列表[选中].门派):显示(250, 51)
    
    end,1,self.信息背景.宽度,self.信息背景.高度)

  end
end
local 角色网格 = 角色界面:创建网格("角色网格", 引擎.宽度2-158, 130, 316, 265)
function 角色网格:初始化()
    self:创建格子(104, 132, 3, 2, 2, 3)
end
function 角色网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a]._spr.人物 then
        if 选中 and 选中==a then
                请求服务(4, a..fgc.._版本号)
                __连接信息.选中玩家id = a
        else
            if 选中 and self.子控件[选中]._spr.确定 then
                self.子控件[选中]._spr.确定 = nil
            end
            选中 = a
            角色界面:名称显示()
            self.子控件[选中]._spr.确定 = true
            角色界面.创建:置可见(false)
            角色界面.下一步:置可见(true)
        end
        
    end
end

function 角色网格:置数据(数据)
    for i = 1, #self.子控件 do
        local lssj = __角色选择格子()
        lssj:置数据(数据[i], 55, 110)
        self.子控件[i]:置精灵(lssj)
    end
end
local 上一步 = 角色界面:创建按钮("上一步", 引擎.宽度-150, 引擎.高度-135)
function 上一步:初始化()
  self:创建按钮精灵( __res:取资源动画('ui',0x611107AA),1)
end
function 上一步:左键弹起(x, y, msg)
    选中 = nil
    角色界面:置可见(false)
    登录层.登录游戏:置可见(true)
end
local 下一步 = 角色界面:创建按钮( "下一步", 引擎.宽度-150, 引擎.高度-70) 
function 下一步:初始化()
  self:创建按钮精灵(__res:取资源动画('ui',0x51A45362),1)
end
function 下一步:左键弹起(x, y, msg)
    if nil ~= 选中 then
        请求服务(4, 选中..fgc.._版本号)
        __连接信息.选中玩家id = 选中
        
    end
end
local 创建 = 角色界面:创建按钮("创建", 引擎.宽度-150, 引擎.高度-70)
function 创建:初始化()
  self:创建按钮精灵(__res:取资源动画('ui',0x75D9CC0E), 1)
end
function 创建:左键弹起(x, y, msg)
      请求服务(2)
end
