
local 队伍格子 = class("队伍格子")
local ggf = require("GGE.函数")
function 队伍格子:初始化()
        self.py = {x = 55, y = 110}
        self.影子=__res:取资源动画('dlzy',0xDCE4B562,"精灵")
        self.队伍选中 =取九宫图像(__res:取资源动画("dlzy",0xC540D7A7,"图像"),116,141,15)
        self.队伍焦点 =取九宫图像(__res:取资源动画("dlzy",0x1ADE7867,"图像"),116,141,15)
end
function 队伍格子:置数据(数据,编号,类型)
        self.数据 = nil
        self.动画 = {}
        local nsf = require('SDL.图像')(120,145)
        if nsf:渲染开始() then
            取白色背景(0, 0, 116, 140, true):显示(0, 0)
            文本字体:置颜色(255,255,255,255):取投影图像(编号):显示(95,115)
            if 数据 then
                self.数据 = 数据
                if  数据.已参战 and 类型=="多开系统" then
                    __res:取资源动画('jszy/xjiem',0x00000197,"图像"):显示(80, 0)
                end
            else
                if 类型=="队伍列表" then
                    __res:getPNGCC(3, 777, 550, 45, 46):拉伸(50,50):显示(33, 45)
                end
            end
            nsf:渲染结束()
        end
        self.图像 = nsf:到精灵()
        local 加入数据 = ggf.insert(self.动画)
        if 数据 then
            local 方向 = 5
            if 数据.变身数据 and  __res.配置.变身造型~=1 then
                    local 资源 = 取模型(数据.变身数据)
                    local 显示动画=__res:取资源动画(资源[3], 资源[1])
                    if 数据.变异 and __染色信息[数据.变身数据]~=nil then
                        local 调色板  = __dewpal(__染色信息[数据.变身数据].id)
                        显示动画:调色(调色板,取调色数据(__染色信息[数据.变身数据].方案))
                    end
                    加入数据(显示动画:取动画(方向):置当前帧(1):播放(true))
                    if 数据.变身数据=="进阶古代瑞兽" or 数据.变身数据=="进阶雷鸟人" or 数据.变身数据=="进阶蝴蝶仙子" or 数据.变身数据=="进阶白熊"
                    or 数据.变身数据=="进阶黑山老妖" or 数据.变身数据=="进阶天兵" or 数据.变身数据=="进阶天将" or 数据.变身数据=="进阶地狱战神"
                    or 数据.变身数据=="进阶风伯" or 数据.变身数据=="进阶凤凰" or 数据.变身数据=="进阶碧水夜叉" or 数据.变身数据=="进阶雨师" then
                        local zl = 取战斗模型(数据.变身数据.."_饰品")  ---这里显示宠物饰品的
                        加入数据(__res:取资源动画(zl[10],zl[6]):取动画(方向):置当前帧(1):播放(true))
                    end
            elseif  数据.坐骑 and 数据.坐骑.模型 and (not 数据.锦衣 or not 数据.锦衣[1] or not 数据.锦衣[1].名称 or __res.配置.锦衣效果==1) then
                        方向 = 2
                        local 资源组 = {}
                        if 新增坐骑(数据.模型,数据.坐骑.模型,"站立") ~= nil and 新增坐骑(数据.模型,数据.坐骑.模型,"站立") ~= ""  then
                            资源组.人物资源 = "jszy/xzzq"
                            资源组.人物站立 = 新增坐骑(数据.模型,数据.坐骑.模型,"站立")
                            资源组.人物行走 = 新增坐骑(数据.模型,数据.坐骑.模型,"奔跑")
                            资源组.坐骑资源 = "jszy/xzzq"
                            资源组.坐骑行走 = 新增坐骑(数据.模型,数据.坐骑.模型,"奔跑")
                            资源组.坐骑站立 = 新增坐骑(数据.模型,数据.坐骑.模型,"站立")
                        else
                            资源组 = 坐骑库(数据.模型,数据.坐骑.模型,数据.坐骑.饰品 or "空")
                        end
                        if 资源组 then
                            加入数据(__res:取资源动画(资源组.坐骑资源, 资源组.坐骑站立):取动画(方向):置当前帧(1):播放(true))
                            if 资源组.坐骑饰品站立 ~= nil then
                                加入数据(__res:取资源动画(资源组.坐骑饰品资源, 资源组.坐骑饰品站立):取动画(方向):置当前帧(1):播放(true))
                            end
                            加入数据(__res:取资源动画(资源组.人物资源, 资源组.人物站立):取动画(方向):置当前帧(1):播放(true))
                        else
                            local 资源 = 取模型(数据.模型)
                            加入数据(__res:取资源动画(资源[3], 资源[1]):取动画(5):置当前帧(1):播放(true))
                        end
            else
                local 资源 = 取模型(数据.模型)
                local  m
                local 武器=nil
                local 副武器=nil
                local 显示动画 =nil
                local 是否显示武器 = true
                if 数据.装备 and 数据.装备[3] then
                    m= _tp:取武器子类(数据.装备[3].子类)
                    if 数据.装备[3].名称 == "龙鸣寒水" or 数据.装备[3].名称 == "非攻" then
                         m = "弓弩1"
                    end
                    资源 = 取模型(数据.模型, m)
                end
                if 数据.锦衣[1] and 数据.锦衣[1].名称 and __res.配置.锦衣效果~=1 then
                    local 锦衣名称 = 数据.锦衣[1].名称
                    if 锦衣名称=="青春" or 锦衣名称=="素颜" or 锦衣名称=="绝色" or 锦衣名称=="春秋" or  锦衣名称=="夏蚕"
                        or 锦衣名称=="星河" or 锦衣名称=="白峨" or 锦衣名称=="糖果" or 锦衣名称=="青涩" or 锦衣名称=="傲然"
                        or 锦衣名称=="牛仔" or  锦衣名称=="试剑" or 锦衣名称=="骨龙战骑" or 锦衣名称=="水嘟嘟·钻白"or 锦衣名称=="斗战神"
                        or 锦衣名称=="斗战胜佛" or  锦衣名称=="八部天龙马·玄" or  锦衣名称=="龙凰·桃" or  锦衣名称=="龙凰·皑"  then
                            资源 = 取战斗锦衣素材(数据.锦衣[1].名称,数据.模型)
                            显示动画 =  __res:取资源动画(资源[5],资源[3])
                            是否显示武器 = false
                    elseif 新加战斗锦衣[锦衣名称] then
                            资源 = 取武器锦衣素材(数据.锦衣[1].名称,数据.模型,m)
                            显示动画 =  __res:取资源动画(资源[5],资源[3])
                            是否显示武器 = true
                    end
                        数据.染色方案=nil
                        数据.染色组=nil
                else
                    显示动画= __res:取资源动画(资源[3], 资源[1])
                end
                if 数据.染色方案 and 数据.染色方案~=0 and 数据.染色组 and 数据.染色组~=0 and #数据.染色组>0 and 显示动画 then
                    local 调色板  = __dewpal(数据.染色方案)
                    显示动画:调色(调色板,取调色数据(数据.染色组))
                end
                if 数据.装备 and 数据.装备[3] and 数据.装备[3].名称 then
                    local ms = _tp:取武器附加名称(数据.装备[3].子类, 数据.装备[3].级别限制,数据.装备[3].名称)
                    资源 = 取模型(ms .. "_" .. 数据.模型)
                    武器 = __res:取资源动画(资源[3], 资源[1])
                    if 数据.装备[3].染色方案 and 数据.装备[3].染色方案~=0 and 数据.装备[3].染色组 and 数据.装备[3].染色组~=0  and #数据.装备[3].染色组>0 then
                        local 调色板  = __dewpal(数据.装备[3].染色方案)
                        武器:调色(调色板,取调色数据(数据.装备[3].染色组))
                    end
                end
                if 数据.装备 and 数据.装备[4] and 数据.模型=="影精灵" and string.find(数据.装备[4].名称,"(坤)") and ( not 数据.装备[3] or string.find(数据.装备[3].名称,"(乾)")) then
                    资源 = 取模型(数据.装备[4].名称 .. "_" .. 数据.模型)
                    副武器 = __res:取资源动画(资源[3], 资源[1])
                    if 数据.装备[4].染色方案 and 数据.装备[4].染色方案~=0 and 数据.装备[4].染色组 and 数据.装备[4].染色组~=0 and #数据.装备[4].染色组>0 then
                        local 调色板  = __dewpal(数据.装备[4].染色方案)
                        副武器:调色(调色板,取调色数据(数据.装备[4].染色组))
                    end
                end
                if 数据.锦衣[2] and 数据.锦衣[2].名称 and __res.配置.光环足迹~=1  then
                    local n = 取光环(数据.锦衣[2].名称)
                    加入数据(__res:取资源动画(n[4],n[1]):取动画(方向):置当前帧(1):播放(true))
                end
                if 显示动画 then
                    加入数据(显示动画:取动画(方向):置当前帧(1):播放(true))
                end
                if 是否显示武器 or __res.配置.锦衣效果==1 then
                    if 武器 then
                        加入数据(武器:取动画(方向):置当前帧(1):播放(true))
                    end
                    if 副武器 then
                        加入数据(副武器:取动画(方向):置当前帧(1):播放(true))
                     end
                end
            end  
        end
end
function 队伍格子:更新(dt)
        for k, v in pairs(self.动画) do
            v:更新(dt)
        end
end



function 队伍格子:显示(x, y)
        self.图像:显示(x, y)
        if self.数据 then
            self.影子:显示(x + self.py.x, y + self.py.y)
        end
        for k, v in pairs(self.动画) do
            v:显示(x + self.py.x, y + self.py.y)
        end
        if self.确定 then
            self.队伍选中:显示(x, y)
        end
        if self.焦点 then
            self.队伍焦点:显示(x, y)
        end
end
return 队伍格子
