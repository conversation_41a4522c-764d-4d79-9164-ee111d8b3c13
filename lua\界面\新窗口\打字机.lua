local 打字机 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "打字机", 0, 0, 引擎.宽度, 引擎.高度)
local wnzi=require("SDl.文字")("assets/hkyt.ttf", 50, true)
function 打字机:初始化()
  -- local nsf = require("SDL.图像")(引擎.宽度, 引擎.高度)
  self.ssds=require("SDL.精灵")(0, 0, 0, 引擎["宽度"], 引擎["高度"]):置颜色(0,0,0, 255)
  -- if nsf["渲染开始"](nsf) then
    -- ssds:显示(0,222)
  -- end
  -- self:置精灵(nsf["到精灵"](nsf))
end

function 打字机:打开(类型,动画调用,回调)
  local nsf = require("SDL.图像")(引擎.宽度, 引擎.高度)
  if nsf["渲染开始"](nsf) then
    wnzi:置颜色(白色)
    wnzi:取图像("动画中……"):显示(236+95,222)
    nsf["渲染结束"](nsf)
  end
  self.图像2 = nsf["到精灵"](nsf)
  self.djishi=70
  self.类型=类型
  self.回调=回调
  self.动画调用=动画调用
  self.发送=true
  self:置可见(true)
end

function 打字机:显示(x,y)
  self.ssds:显示(0,0)
  if self.图像2 then
    self.图像2:显示(x,y)
  end
  self.djishi=self.djishi-1
  if self.djishi<=0 then
    if self.发送 then
      self.发送=false
      self:回调发送()
    end
    self:置可见(false)
  end
end

function 打字机:回调发送()
  -- print( self.类型,self.回调)
  if self.类型 then
    发送数据(97,{类型=self.类型})
  end
  if self.回调 then
    发送数据(98,{类型=self.回调})
  end
  if self.动画调用 then
    发送数据(98,{类型=self.动画调用})
  end
end