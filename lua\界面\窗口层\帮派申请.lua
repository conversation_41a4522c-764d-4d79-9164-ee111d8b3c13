
local 帮派申请 = 窗口层:创建窗口("帮派申请", 0, 0, 572, 468)



function 帮派申请:初始化()

    self:置精灵(__res:取资源动画("dlzy", 0x10000093,"图像"):拉伸(572, 468):到精灵())--440 360
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
    if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
    else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
    end

end


function 帮派申请:打开(申请数据)
  self:置可见(true)
  self.申请数据={}
  if 申请数据 then
    self.申请数据=申请数据
  end
  self.选中=nil
  self.申请列表:置数据()
  self.申请按钮:置禁止(true)
end


local 申请按钮 = 帮派申请:创建红色按钮("批准申请", "申请按钮", 236, 425,100,30) 
function 申请按钮:左键弹起(x, y)
  if 帮派申请.选中 and 帮派申请.申请数据[帮派申请.选中] then
    请求服务(6104,{玩家序号=帮派申请.选中})
		table.remove(帮派申请.申请数据,帮派申请.选中)
    帮派申请.选中=nil
    帮派申请.申请列表:置数据()
    帮派申请:置可见(false)
  end
 
end



local 申请列表 = 帮派申请:创建列表("申请列表",20, 74, 520, 344)
function 申请列表:初始化()
  self.行高度 = 43
end



function 申请列表:置数据()
    self:清空()
    for _, v in ipairs(帮派申请.申请数据) do
        local r = self:添加()
        r:创建纹理精灵(function()
          说明字体:置颜色(0,0,0,255)
          说明字体:取图像(v.名称):显示(0, 10)
          说明字体:取图像(v.等级):显示(120+(47-说明字体:取宽度(v.等级))//2, 10)
          说明字体:取图像(v.id):显示(166+(135-说明字体:取宽度(v.id))//2, 10)
          说明字体:取图像(v.门派):显示(298+(89-说明字体:取宽度(v.门派))//2, 10)
        end
      )
    end

end

function 申请列表:左键弹起(x, y, i)
      帮派申请.选中=i
      帮派申请.申请按钮:置禁止(false)
end



local 关闭 = 帮派申请:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  帮派申请:置可见(false)
end








