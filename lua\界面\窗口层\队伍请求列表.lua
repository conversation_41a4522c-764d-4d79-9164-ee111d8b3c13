local 队伍请求列表 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "队伍请求列表", 97 + abbr.py.x, 23 + abbr.py.y, 774, 484)
function 队伍请求列表:初始化()
  local nsf = require("SDL.图像")(774, 484)
  if nsf["渲染开始"](nsf) then
    置窗口背景("请求列表", 0, 12, 766, 473, true)["显示"](置窗口背景("请求列表", 0, 12, 766, 473, true), 0, 0)
    取灰色背景(0, 0, 753, 350, true)["显示"](取灰色背景(0, 0, 753, 350, true), 7, 80)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 队伍请求列表:打开(数据)
  self:置可见(true)
  self:重置(数据)
end
function 队伍请求列表:重置(数据)
  self.选中 = nil
  self.队员网格["置数据"](self.队员网格, 数据)
end
local 关闭 = 队伍请求列表["创建我的按钮"](队伍请求列表, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 724, 0)
function 关闭:左键弹起(x, y, msg)
  队伍请求列表["置可见"](队伍请求列表, false)
end
local 队员网格 = 队伍请求列表["创建网格"](队伍请求列表, "队员网格", 17, 85, 740, 290)
function 队员网格:初始化()
  self:创建格子(143, 290, 0, 5, 1, 5)
end
function 队员网格:左键弹起(x, y, a, b, msg)
  if 队伍请求列表["选中"] then
    self.子控件[队伍列表["选中"]]._spr["确定"] = nil
  end
  if self.子控件[a]._spr["数据"] then
    队伍请求列表["选中"] = a
    self.子控件[a]._spr["确定"] = true
  end
end
function 队员网格:置数据(数据)
  for i = 1, #队员网格["子控件"] do
    local lssj = __队伍格子["创建"]()
    lssj["置数据"](lssj, 数据[i], i, "请求列表")
    队员网格["子控件"][i]["置精灵"](队员网格["子控件"][i], lssj)
  end
end
for i, v in ipairs({
  {
    name = "同意按钮",
    font = "同意",
    x = 366,
    y = 435,
    tcp = __res:getPNGCC(2, 487, 802, 150, 43, true)["拉伸"](__res:getPNGCC(2, 487, 802, 150, 43, true), 116, 37)
  },
  {
    name = "拒绝按钮",
    font = "拒绝",
    x = 496,
    y = 435,
    tcp = __res:getPNGCC(2, 487, 802, 150, 43, true)["拉伸"](__res:getPNGCC(2, 487, 802, 150, 43, true), 116, 37)
  },
  {
    name = "清空按钮",
    font = "清空",
    x = 626,
    y = 435,
    tcp = __res:getPNGCC(2, 487, 802, 150, 43, true)["拉伸"](__res:getPNGCC(2, 487, 802, 150, 43, true), 116, 37)
  }
}) do
  local 临时函数 = 队伍请求列表["创建我的按钮"](队伍请求列表, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "同意按钮" then
      if 队伍请求列表["选中"] and 队员网格["子控件"][队伍请求列表["选中"]]._spr["数据"] then
        发送数据(4004, {
          ["序列"] = 队伍请求列表["选中"]
        })
      end
    elseif v.name == "拒绝按钮" then
      if 队伍请求列表["选中"] and 队员网格["子控件"][队伍请求列表["选中"]]._spr["数据"] then
        发送数据(4005, {
          ["序列"] = 队伍请求列表["选中"]
        })
      end
    elseif v.name == "清空按钮" then
    end
  end
end
