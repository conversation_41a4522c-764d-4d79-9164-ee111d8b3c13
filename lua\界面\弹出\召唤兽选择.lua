__UI弹出["召唤兽选择"] = __UI界面["创建弹出窗口"](__UI界面, "创建弹出控件", 0, 0, 221, 353)
local 召唤兽选择 = __UI弹出["召唤兽选择"]
function 召唤兽选择:初始化()
  local nsf = require("SDL.图像")(221, 353)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 221, 353, true)["显示"](取黑透明背景(0, 0, 221, 353, true), 0, 0)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 召唤兽选择:打开(x, y, lx)
  self:置可见(true)

  self.头像网格["置头像"](self.头像网格, 角色信息["宝宝列表"])
  self:置坐标(x + abbr.py2.x, y + abbr.py2.y)
  self.类型 = lx
end
local 头像网格 = 召唤兽选择["创建网格"](召唤兽选择, "头像网格", 12, 12, 198, 331)
function 头像网格:初始化()
end
function 头像网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["数据"] then
    if 召唤兽选择["类型"] == "洗炼" then
      __UI界面["窗口层"]["召唤兽洗炼"]["重置"](__UI界面["窗口层"]["召唤兽洗炼"], a)
      召唤兽选择["置可见"](召唤兽选择, false)
    elseif 召唤兽选择["类型"] == "合宠" then
      if __UI界面["窗口层"]["召唤兽合宠"]["选中召唤兽"] ~= a and __UI界面["窗口层"]["召唤兽合宠"]["选中召唤兽2"] ~= a then
        __UI界面["窗口层"]["召唤兽合宠"]["重置"](__UI界面["窗口层"]["召唤兽合宠"], a, 召唤兽选择["类型"])
        召唤兽选择["置可见"](召唤兽选择, false)
      end
    elseif 召唤兽选择["类型"] == "合宠2" and __UI界面["窗口层"]["召唤兽合宠"]["选中召唤兽"] ~= a and __UI界面["窗口层"]["召唤兽合宠"]["选中召唤兽2"] ~= a then
      __UI界面["窗口层"]["召唤兽合宠"]["重置"](__UI界面["窗口层"]["召唤兽合宠"], a, 召唤兽选择["类型"])
      召唤兽选择["置可见"](召唤兽选择, false)
    elseif 召唤兽选择["类型"] == "坐骑统御1" then
      __UI界面["窗口层"].坐骑属性栏:统御(1,a)
      召唤兽选择["置可见"](召唤兽选择, false)
    elseif 召唤兽选择["类型"] == "坐骑统御2" then
      __UI界面["窗口层"].坐骑属性栏:统御(2,a)
      召唤兽选择["置可见"](召唤兽选择, false)
    end
  end
end
function 头像网格:置头像(数据)
  self:创建格子(198, 57, 10, 0, #数据, 1, true)
  for i = 1, #头像网格["子控件"] do
    local lssj = __头像格子2["创建"]()
    lssj["置头像"](lssj, 数据[i])
    头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i], lssj)
  end
end
