--[[
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2024-09-27 20:15:16
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2024-10-08 11:25:57
FilePath: \XYQCStiaoshi\lua\界面\弹出\小地图寻路.lua
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
--]]
-- <AUTHOR> GGELUA
--Last Modified by: GGELUA
-- @Date                : 2024-09-27 20:15:16
--Last Modified time: 2024-11-15 21:37:40

__UI弹出["小地图寻路"] = __UI界面["创建弹出窗口"](__UI界面, "小地图寻路", 480-56-119 + abbr.py2.x, -63 + abbr.py2.y, 155, 385)
local 小地图寻路 = __UI弹出["小地图寻路"]
function 小地图寻路:初始化()
  self:置精灵(取黑色背景(0, 0, 155, 385))
end
function 小地图寻路:打开(id, x, y)
  self:置可见(true)
  self:置中心(x - 480 - 155, 270 - y - 385)
  if self.smap and self.smap ~= id then
    self:重置(id)
  elseif not self.smap then
    self:重置(id)
  end
  self.选中 = nil
end
function 小地图寻路:重置(id)
  self.smap = id
  self.NPC列表["重置"](self.NPC列表, id)
end


local NPC列表 = 小地图寻路["创建列表"](小地图寻路, "NPC列表", 9, 15, 137, 315)
function NPC列表:初始化()
  self:置文字(字体18)
  self.行间距 = 10
end
function NPC列表:重置(id)
  self.清空(self)
  self:置颜色(255, 255, 255)

  小地图寻路.假人表={}
  for k, v in pairs(场景取假人表(id) or {}) do
    table.insert(小地图寻路["假人表"], v)
    self:添加(v["名称"])
  end
end
function NPC列表:左键弹起(x, y, i, item, msg)
  小地图寻路["选中"] = i
end
local 寻路按钮 = 小地图寻路["创建我的按钮"](小地图寻路, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 118, 41), "寻路按钮", 19, 335, "寻路")
function 寻路按钮:左键弹起(x, y, msg)
  if 小地图寻路["选中"] then
    if 小地图寻路["假人表"][小地图寻路["选中"]].X and 小地图寻路["假人表"][小地图寻路["选中"]].Y then
      __UI界面["窗口层"]["小地图"]["终点"] = require("GGE.坐标")(小地图寻路["假人表"][小地图寻路["选中"]].X * 20, 小地图寻路["假人表"][小地图寻路["选中"]].Y * 20):floor()

    elseif 小地图寻路["假人表"][小地图寻路["选中"]].x and 小地图寻路["假人表"][小地图寻路["选中"]].y then
      __UI界面["窗口层"]["小地图"]["终点"] = require("GGE.坐标")(小地图寻路["假人表"][小地图寻路["选中"]].x * 20, 小地图寻路["假人表"][小地图寻路["选中"]].y * 20):floor()
    end

    __主显["主角"]["设置路径"](__主显["主角"], __UI界面["窗口层"]["小地图"]["终点"])
  end
end
