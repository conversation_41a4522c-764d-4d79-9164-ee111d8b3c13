

__UI弹出.弹出列表 = 界面:创建弹出窗口("弹出列表", 0, 0, 0, 0)
local 弹出列表 = __UI弹出.弹出列表
function 弹出列表:初始化()

end


function 弹出列表:打开(列表,背景,事件,x,y,w,h,...) 
        if not 背景 and (not 列表 or not 列表[1] ) then self:置可见(false) return end
        self:置可见(true)
        self.附加={...}
        self.事件=nil
        if 事件 then
            self.事件=事件
        end
        local 宽度,高度=0,0
        if 背景 then
                宽度=背景.宽度
                高度=背景.高度
        else
            if type(列表[1])~="string" then
                宽度 =列表[1].宽度+10
                if self.附加[2] then
                    高度 =#列表*(列表[1].高度+self.附加[2])+(10-self.附加[2])
                else
                    高度 =#列表*(列表[1].高度+2)+8
                end
            else
                local 最大宽度=0
                for i, v in ipairs(列表) do
                    if self.附加[1] then
                        if self.附加[1]:取宽度(v)>最大宽度 then
                            最大宽度=self.附加[1]:取宽度(v)
                        end
                    else
                        if 文本字体:取宽度(v)>最大宽度 then
                            最大宽度=文本字体:取宽度(v)
                        end
                    end
                end
                宽度=最大宽度+10
                local 行高度 =0
                if self.附加[1] then
                    行高度=self.附加[1]:取高度("高")+4
                else
                    行高度=文本字体:取高度("高")+4
                end
                if self.附加[2] then
                    高度 =#列表*(行高度+self.附加[2])+(10-self.附加[2])
                else
                    高度 =#列表*(行高度+3)+7
                end
            end
        end
        if  not w or w==0 then
            w=宽度
        end
        if  not h or h==0  then
            h=高度
        end
        if h>=250 then
            h=240
        end
        self.列表显示:置宽高(w-10,h-10)
        self.列表显示:显示列表(列表,self.附加[1],self.附加[2],self.附加[3])
        if 背景 then
            if ggetype(背景)=="SDL图像" then
                self:置精灵(背景:到精灵())
            elseif ggetype(背景)=="SDL精灵" then
                self:置精灵(背景)
            elseif ggetype(背景)=="tcp" then
                self:置精灵(背景:取精灵(1))
            end
        else
            self:置精灵(require('SDL.精灵')(0, 0, 0, w, h):置颜色(20, 20, 60, 170), true)
        end

        if x+w >引擎.宽度 then
            x = 引擎.宽度 - w- 5
        elseif x<0 then
            x = 0
        end
        if y+h >引擎.高度 then
            y = 引擎.高度 - h- 5
        elseif y<0 then
            y = 0
        end
        self:置坐标(x,y)
        self:置宽高(w,h)

end





local 列表显示 = 弹出列表:创建列表("列表显示", 5, 5, 0, 0)
function 列表显示:初始化()
    self:置颜色(255,255,255,255)
   self:置文字(文本字体)
   self.行高度 = 16
   self.行间距 = 3
end

function 列表显示:显示列表(列表,zt,行间距,ys)
  self:清空()
  if not 列表 or not 列表[1] then return end
  local zts =文本字体
  if zt then
    zts=zt
  end
  self:置文字(zts)
  self.行高度=zts:取高度("高")+4
  if 行间距 then
      self.行间距 = 行间距
  else
      self.行间距 = 3
  end
  local R,G,B,A=255,255,255,255
  if ys then
    if type(ys)=="string" then
        R,G,B,A=__取颜色(ys)
    elseif type(ys)=="table" then
        R,G,B,A=ys[1],ys[2],ys[3],ys[4]
    end
  end
  self:置颜色(R,G,B,A)

   if type(列表[1])~="string" then
          self.行高度 =列表[1].高度
          self.行间距 = 2
    end
 
for i, v in ipairs(列表) do
        if type(v) =="string" or type(v)=="number"  then
            self:添加(v)
        else
            local r = self:添加()
            if ggetype(v)=="SDL图像"then
                r:置精灵(v:到精灵())
            elseif ggetype(v)=="SDL精灵" then
                r:置精灵(v)
            elseif ggetype(v)=="tcp" then
                r:置精灵(v:取精灵(1))
            else
                r:置精灵(v)
            end
        end
  end
end

function 列表显示:左键弹起(x, y, i)

    if 弹出列表.事件 then
        弹出列表.事件(i)
    end
    弹出列表:置可见(false)
end
