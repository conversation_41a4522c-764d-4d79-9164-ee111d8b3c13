if gge.isdebug and os.getenv('LOCAL_LUA_DEBUGGER_VSCODE') == '1' then
  package.loaded['lldebugger'] = assert(loadfile(os.getenv('LOCAL_LUA_DEBUGGER_FILEPATH')))()
  require('lldebugger').start()
end


local GGEF = require("GGE.函数")
local SDL = require("SDL")
local 窗口配置 = {
    标题= "梦幻西游 ONLINE",
    版本 = '0.0.1',
    鼠标 = false,
    --可调整 = true,

}
--  __手机=false
if gge.platform == "Android" or gge.platform == "iOS"  then
-- 窗口配置.鼠标 = true
--if gge.platform == 'Windows' then
   -- 窗口配置.渲染器 = 'opengl'
    窗口配置.宽度 = 800
    窗口配置.高度 = 600
    窗口配置.帧率 = 60
   __手机=true
 else
    if gge.platform == "Windows" and gge.isdebug then
        窗口配置.宽度 = 960
        窗口配置.高度 = 540
        窗口配置.帧率 = 60
       __手机=true
    else
    窗口配置.宽度 = 960
    窗口配置.高度 = 540
    窗口配置.帧率 = 35

   -- 窗口配置.渲染器 = 'opengles2'
    --窗口配置.全屏=true
    __手机=false
    end
end



引擎 = require 'SDL.窗口' (窗口配置)


__全局数据 = {}
__上次时间 = os.time()
__重连时间 = os.time()
__时间记录 = os.time()
__随机记录 = 0
__随机1记录 = 0
__多开操作 = false
__全局自动走路开关=false

function 引擎:初始化()
    引擎.逻辑宽度=引擎.宽度
    引擎.逻辑高度=引擎.高度
    
    require("custom")
    require("UIlayer")
    
    -- collectgarbage("generational")
    collectgarbage("incremental") 
end


--__清理时间 = os.time()
function 引擎:更新事件(dt, x, y)
    if type(dt) == "number" then
        -- if os.time()-__清理时间>=300 then
        --     test1()
        --     __清理时间 = os.time()
        -- end
        if __主显 then
            __主显:更新(dt)
        end
        if __UI界面 then
            __UI界面:更新(dt, x, y)

            if __UI界面.界面层.聊天控件.聊天窗口 and (__UI界面.界面层.聊天控件.内容区域.是否可见 or __UI界面.界面层.聊天控件.拉伸按钮.是否可见 or __UI界面.界面层.聊天控件.喇叭按钮.是否可见) then
                __UI界面.界面层.聊天控件.内容区域:置可见(false)
                __UI界面.界面层.聊天控件.拉伸按钮:置可见(false)
                __UI界面.界面层.聊天控件.喇叭按钮:置可见(false)
            end
            -- if __UI界面.窗口层.寄存 then
            --   for i, v in ipairs(__UI界面.窗口层.寄存) do
            --         if i~=1 and v.更新 then
            --             v:更新(dt)
            --         end
            --     end
            -- end



        end
        --  if __收到信息 and __全局数据 and #__全局数据~=0 then
        --     __CLT:数据到达()
        -- end

        if  __连接信息.连接断开 and not __连接信息.重连进入 and os.time()-__重连时间>=10 then
            __CLT:重连()  
            __重连时间 = os.time()
        -- elseif not __连接信息.连接断开  and __失去焦点 and os.time()-__失去焦点>=1200 then
        --      __CLT:断开()
        --      引擎:关闭()
             
        end
       
    else
        print(dt, x, y)
    end
    if __上次时间 ~= os.time() then
        __上次时间 = os.time()
        if __全局自动走路开关 then
            __全局自动走路秒 = __全局自动走路秒 + 1
        end
    end
    if __res.实时更新 and __Http.更新==99  and __手机 and os.time()<__res.实时更新 then
        __实时更新:检查更新(__Http.ip,__Http.dk)
    end

   if 引擎.逻辑宽度~=引擎.宽度 or 引擎.逻辑高度 ~=引擎.高度 then
        __UI界面.界面层:重新初始化()
        __UI界面.窗口层:重新初始化()
        __UI界面.战斗层:重新初始化()
        __UI弹出.提示框:置宽高(引擎.宽度,引擎.高度)
        __UI界面.界面层.队伍栏.队员网格:置头像(_tp.队伍数据 or {})
        if  not _tp.战斗中 then
            __战斗主控 = require("战斗主控")()
            __战斗单位 = require("战斗单位")
            __战斗动画 = require("战斗动画")
        end
        引擎.逻辑宽度=引擎.宽度
        引擎.逻辑高度=引擎.高度
    end
 
     
  
end

function 引擎:渲染事件(dt, x, y)
    if self:渲染开始(0, 0, 0) then
        if __主显 then
            __主显:显示(x, y)
        end
        if __UI界面 then
            __UI界面:显示(x, y)
            -- if __UI界面.窗口层.寄存 then
            --   for i, v in ipairs(__UI界面.窗口层.寄存) do
            --         if i==1 then
            --               v.精灵:显示(v.x, v.y)
            --         else
            --             v:显示(__UI界面.窗口层.寄存[1].x+v.x,__UI界面.窗口层.寄存[1].y+v.y)
            --         end
            --     end
            -- end
        end
        


        self:渲染结束()
    end
end

local 上一个消息 = 0
function 引擎:窗口事件(ev,w,h)
    if ev == SDL.WINDOWEVENT_CLOSE then
        if __主显 and __主显.主角  then
            if not __UI界面.窗口层.系统设置.是否可见 then
                __UI界面.窗口层.系统设置:打开()
            end
        else
            if __UI界面.登录层.更新界面.是否可见 then
                引擎:关闭()
            else
                if not __UI界面.窗口层.退出提示.是否可见 then
                    __UI界面.窗口层.退出提示:置可见(true)
                end
            end
        end
    elseif ev == SDL.WINDOWEVENT_FOCUS_LOST and __手机 then--失去焦点
  
    
           __失去焦点 = os.time()
   
    elseif ev == SDL.WINDOWEVENT_FOCUS_GAINED then--获取焦点
          __失去焦点 =nil

    -- elseif ev == SDL.WINDOWEVENT_MOVED then



    end
end

function 引擎:键盘事件(键码, 功能)
      if 键码==13 and 功能==0 and gge.platform == 'iOS' then
          SDL.StopTextInput()
      end
end


-- function 引擎:鼠标事件(KEY, x, y, 按住)
 
    
--    -- for k, v in pairs(属性层) do
    


--     --     if  type(v)=="table" and (v.是否可见 or not v.是否可见) then
--     --         print(v.获得鼠标)
--     --         if v.获得鼠标 then
--     --             print(1111111111111111111)
--     --         end
--     --     end
--     -- end



-- end

-- function 引擎:触摸事件(KEY, x, y, xx, yy, 按住)
--     print(KEY, x, y, xx, yy, 按住)

-- end
-- function 引擎:输入事件()
-- end

-- function 引擎:销毁事件()
-- end
