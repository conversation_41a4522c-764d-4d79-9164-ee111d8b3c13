local 宝箱抽奖 = 窗口层:创建窗口("宝箱抽奖", 0, 0, 512, 419)
function 宝箱抽奖:初始化()
  self:置精灵(置窗口背景("无", 0, 14, 505, 406):置颜色(239, 56, 46, 255))
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
end
function 宝箱抽奖:打开(data)
  self:置可见(true)

  self.道具网格["置物品"](self.道具网格, data["道具"])
  self.宝箱进程 = 0
  self.宝箱位置 = 0
  self.宝箱间隔 = 0
  self.宝箱起始 = os.time()
  self.宝箱速度 = 5
  self.宝箱中奖 = data["道具"]["中奖"]
  self.宝箱类型 = data["类型"]
  self:重置(data)
end
function 宝箱抽奖:重置(data)
  self.图像 = self:创建纹理精灵(function()
    道具字体:置颜色(255, 255, 255)
    道具字体:取图像(data["类型"]):显示(252.5 - 道具字体["取宽度"](道具字体, data["类型"]) // 2, 17)
  end,1,512,40
)
end
function 宝箱抽奖:更新()
  if 0 == self.宝箱进程 then
    self.宝箱间隔 = self.宝箱间隔 + 1
    if self.宝箱间隔 >= self.宝箱速度 then
      self.宝箱间隔 = 0
      if self.道具网格.子控件[self.宝箱位置] then
        self.道具网格.子控件[self.宝箱位置]._spr["确定"] = nil
      end
      self.宝箱位置 = self.宝箱位置 + 1
      if 25 == self.宝箱位置 then
        self.宝箱位置 = 1
      end
      self.道具网格.子控件[self.宝箱位置]._spr["确定"] = true
    end
  elseif 1 == self.宝箱进程 then
    self.宝箱间隔 = self.宝箱间隔 + 1
    if self.宝箱间隔 >= self.宝箱速度 then
      self.宝箱间隔 = 0
      if self.道具网格.子控件[self.宝箱位置] then
        self.道具网格.子控件[self.宝箱位置]._spr["确定"] = nil
      end
      self.宝箱位置 = self.宝箱位置 + 1
      if 25 == self.宝箱位置 then
        self.宝箱位置 = 1
      end
      self.道具网格.子控件[self.宝箱位置]._spr["确定"] = true
    end
    if os.time() >= self.宝箱起始 + 3 then
      self.宝箱进程 = 2
    end
  elseif 2 == self.宝箱进程 then
    local 距离 = math.abs(self.宝箱位置 - self.宝箱中奖)
    if 距离 >= 15 then
      self.宝箱速度 = 10
    elseif 距离 >= 10 then
      self.宝箱速度 = 17
    elseif 距离 >= 5 then
      self.宝箱速度 = 25
    end
    self.宝箱间隔 = self.宝箱间隔 + 1
    if self.宝箱间隔 >= self.宝箱速度 then
      self.宝箱间隔 = 0
      if self.道具网格.子控件[self.宝箱位置] then
        self.道具网格.子控件[self.宝箱位置]._spr["确定"] = nil
      end
      self.宝箱位置 = self.宝箱位置 + 1
      if 25 == self.宝箱位置 then
        self.宝箱位置 = 1
      end
      self.道具网格.子控件[self.宝箱位置]._spr["确定"] = true
    end
    if self.宝箱位置 == self.宝箱中奖 then
      self.宝箱进程 = 3
      self.宝箱起始 = os.time()
      请求服务(1520, {
        ["序号"] = self.宝箱中奖,
        ["类型"] = self.宝箱类型
      })
    end
  elseif 3 == self.宝箱进程 and os.time() >= self.宝箱起始 + 1 then
    请求服务(1521)
    self.关闭["左键弹起"](self.关闭)
    return
  end
end
local 关闭 = 宝箱抽奖:创建按钮("关闭", 462, 0) 
function 关闭:初始化()
  self:创建按钮精灵(__res:getPNGCC(1, 401, 0, 46, 46),1)
end
function 关闭:左键弹起(x, y, msg)
  宝箱抽奖:置可见(false)
end
local 道具网格 = 宝箱抽奖:创建网格("道具网格", 30, 60, 455, 280)
function 道具网格:初始化()
  self:创建格子(55, 55, 20, 23, 4, 6)
end
function 道具网格:置物品(data)
  for i = 1, #self.子控件 do
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, data[i], "NPC商店")
    self.子控件[i]:置精灵(lssj)
  end
end
for i, v in ipairs({
  {
    name = "停止",
    x = 210,
    y = 359,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "停止"
  }
}) do
  local 临时函数 = 宝箱抽奖:创建红色按钮(v.font, v.name, v.x, v.y,114,41,说明字体) 
 function  临时函数:左键弹起(x, y)
    if v.name == "停止" and 0 == 宝箱抽奖["宝箱进程"] then
      宝箱抽奖["宝箱进程"] = 1
      宝箱抽奖["宝箱起始"] = os.time()
    end
  end
end
