


local 超级兽决 = 窗口层:创建窗口("超级兽决", 0,0, 440, 270)
function 超级兽决:初始化()
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
    --self.禁止右键=true
end

function 超级兽决:更新()
      if self.打开时间 and self.是否可见 and os.time()-self.打开时间>=5 then
        
          self:置可见(false)
      end


end

function 超级兽决:打开(技能)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self:刷新(技能)
end


function 超级兽决:刷新(技能)
      self.打开时间 = os.time()
      self:创建纹理精灵(function()
              __res:取资源动画('pic',"cjsjbj.png","图片"):显示(0,0)
              if 技能 then
                  local  临时名称 = 分割字符(技能)
                  if #临时名称>4 then
                      __res:取资源动画('pic',"cjsjmc.png","图片"):显示(300,93)
                  end
                  local 显示名称 = ""
                  for i=1,#临时名称 do
                    显示名称=显示名称..临时名称[i].."\n"
                  end
                  文本字体:置颜色(__取颜色("黄色")):取图像(显示名称):显示(306,107)
              end
            
        end)
end



local 关闭 = 超级兽决:创建按钮("关闭",405,28)
function 关闭:初始化()
    self:创建按钮精灵(__res:取资源动画('pic',"cjsjgb.png","图片"))
end
function 关闭:左键弹起(x, y)
      超级兽决:置可见(false)
end

