--[[
Author: GGELUA
Date: 2024-11-15 19:30:32
Last Modified by: GGELUA
Last Modified time: 2024-11-18 15:57:35
--]]
--[[
Author: GGELUA
Date: 2024-11-15 19:30:32
Last Modified by: GGELUA
Last Modified time: 2024-11-15 19:37:05
--]]
local 电量过低提示 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "电量过低提示", 0, 0, 960 + abbr.py.x, 540 + abbr.py.y)
function 电量过低提示:初始化()
end
function 电量过低提示:打开(数据, x, y, w, h, sj)
  电量过低开始=true
  self:置可见(true, true)
  self.电量过低提示文本["清空"](self.电量过低提示文本)
  self:置坐标(x + abbr.py.x, y + abbr.py.y)
  if sj then
    self.事件 = sj
  else
    self.事件 = nil
  end
  local nsf = require("SDL.图像")(w, h)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 213, 927, 30, 30)["显示"](__res:getPNGCC(3, 213, 927, 30, 30), 0, 0)
    __res:getPNGCC(3, 213, 957, 30, 141)["平铺"](__res:getPNGCC(3, 213, 957, 30, 141), 30, h - 60)["显示"](__res:getPNGCC(3, 213, 957, 30, 141)["平铺"](__res:getPNGCC(3, 213, 957, 30, 141), 30, h - 60), 0, 30)
    __res:getPNGCC(3, 213, 1098, 30, 31)["显示"](__res:getPNGCC(3, 213, 1098, 30, 31), 0, h - 30)
    __res:getPNGCC(3, 243, 927, 140, 30)["平铺"](__res:getPNGCC(3, 243, 927, 140, 30), w - 60, 30)["显示"](__res:getPNGCC(3, 243, 927, 140, 30)["平铺"](__res:getPNGCC(3, 243, 927, 140, 30), w - 60, 30), 30, 0)
    __res:getPNGCC(3, 243, 957, 155, 141)["平铺"](__res:getPNGCC(3, 243, 957, 155, 141), w - 60, h - 60)["显示"](__res:getPNGCC(3, 243, 957, 155, 141)["平铺"](__res:getPNGCC(3, 243, 957, 155, 141), w - 60, h - 60), 30, 30)
    __res:getPNGCC(3, 243, 1098, 155, 31)["平铺"](__res:getPNGCC(3, 243, 1098, 155, 31), w - 60, 30)["显示"](__res:getPNGCC(3, 243, 1098, 155, 31)["平铺"](__res:getPNGCC(3, 243, 1098, 155, 31), w - 60, 30), 30, h - 30)
    __res:getPNGCC(3, 398, 927, 30, 30)["显示"](__res:getPNGCC(3, 398, 927, 30, 30), w - 30, 0)
    __res:getPNGCC(3, 398, 957, 30, 141)["平铺"](__res:getPNGCC(3, 398, 957, 30, 141), 30, h - 60)["显示"](__res:getPNGCC(3, 398, 957, 30, 141)["平铺"](__res:getPNGCC(3, 398, 957, 30, 141), 30, h - 60), w - 30, 30)
    __res:getPNGCC(3, 398, 1098, 30, 31)["显示"](__res:getPNGCC(3, 398, 1098, 30, 31), w - 30, h - 30)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "温馨提示：")["显示"](字体18["取图像"](字体18, "温馨提示："), 30, 30)

    

    nsf["渲染结束"](nsf)
  end
  电量过低提示["置精灵"](电量过低提示, nsf["到精灵"](nsf))
  self.电量过低提示文本["置文本"](self.电量过低提示文本, 数据)
end
local 确定 = 电量过低提示["创建我的按钮"](电量过低提示, __res:getPNGCC(1, 401, 65, 175, 43, true)["拉伸"](__res:getPNGCC(1, 401, 65, 175, 43, true), 130, 43), "谢谢提醒", 30+101, 130, "谢谢提醒")
function 确定:左键弹起(x, y, msg)
  电量过低提示["置可见"](电量过低提示, false)
  --__CLT["断开"](__CLT)
  --引擎["关闭"](引擎)
end

local 电量过低提示文本 = 电量过低提示["创建我的文本"](电量过低提示, "电量过低提示文本", 40, 80, 359, 150)
function 电量过低提示文本:初始化()
end
