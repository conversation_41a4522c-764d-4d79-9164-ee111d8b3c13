local 经脉格子 = class("经脉格子")
function 经脉格子:初始化()
end
-- function 经脉格子:置数据(数据, sj)
function 经脉格子:置数据(流派,技能,灰色,特色法术,助战门派,助战编号)
  local nsf = require("SDL.图像")(145, 57)
  local tux = require("SDL.图像")
  if 技能 then
    if nsf["渲染开始"](nsf) then
      local 门派=角色信息.门派
      if 助战门派 then
          门派=助战门派
          self.助战编号=助战编号
      end
      self.门派=门派
      if 特色法术 then
        self.数据 = 取技能(技能)
        if self.数据 then
          self.数据.名称=技能
          if 灰色 then
            __res:取图像(__res:取地址("shape/jn/", self.数据[7])):到灰度():拉伸(45, 45):显示(3,3)
          else
            -- if self.数据[10] then
            --   tux("assets/shape/jingmai/jmk/"..__jingma<PERSON>iaoshu[角色信息.门派].."/"..__jingmaimiaoshu[技能]..".jpg"):拉伸(45, 45):显示(0,0)
            -- else
            __res:取图像(__res:取地址("shape/jn/", self.数据[7])):拉伸(45, 45):显示(3,3)
            -- end
          end
          字体16:置颜色(__取颜色("浅黑"))
          字体16:取图像(技能):显示(60, 16)
        end
      else
        -- print(门派,技能,__jingmaimiaoshu[门派],__jingmaimiaoshu[技能])
        -- print(门派,"assets/wpal/20230/jingmai/jmk/"..__jingmaimiaoshu[门派].."/"..__jingmaimiaoshu[技能]..".jpg")
        self.数据 = 取经脉(流派,技能)
        if self.数据 then
          self.数据.流派=流派
          self.数据.名称=技能
          self.数据.经脉详情=true
          if 灰色 then
            if self.数据[10] then
              tux("assets/wpal/20230/jingmai/jmk/"..__jingmaimiaoshu[门派].."/"..__jingmaimiaoshu[技能]..".jpg"):到灰度():拉伸(45, 45):显示(3,3)
            else
              __res:取图像(__res:取地址("shape/jn/", self.数据[7])):到灰度():拉伸(45, 45):显示(3,3)
            end
          else
            if self.数据[10] then
              -- print(技能)
              tux("assets/wpal/20230/jingmai/jmk/"..__jingmaimiaoshu[门派].."/"..__jingmaimiaoshu[技能]..".jpg"):拉伸(45, 45):显示(3,3)
            else
              __res:取图像(__res:取地址("shape/jn/", self.数据[7])):拉伸(45, 45):显示(3,3)
            end
          end
        end
      end
      nsf["渲染结束"](nsf)
    end
  -- if 数据 then
  --   if nsf["渲染开始"](nsf) then
  --     self.数据 = 取技能(数据, 角色信息["门派"])
  --     if sj then
  --       __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7])), 45, 45)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7])), 45, 45), 0, 0)
  --     else
  --       __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7])), 45, 45)["到灰度"]((__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7])), 45, 45)))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7])), 45, 45)["到灰度"]((__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self.数据[7])), 45, 45))), 0, 0)
  --     end
  --     nsf["渲染结束"](nsf)
  --   end
    self.精灵 = nsf["到精灵"](nsf)
  end
end
function 经脉格子:详情打开(x, y,可选)
  __UI弹出["经脉详情"]["置可见"](__UI弹出["经脉详情"], true, true)
  __UI弹出["经脉详情"]["技能文本"]["清空"](__UI弹出["经脉详情"]["技能文本"])
  __UI弹出["经脉详情"]["打开"](__UI弹出["经脉详情"], self.数据, x - 240, y - 125, 280, 300, self.精灵,可选,self.门派,self.助战编号)
end
function 经脉格子:更新(dt)
end
function 经脉格子:显示(x, y)
  if self.精灵 then
    self.精灵["显示"](self.精灵, x, y)
  end
  if self.可选 then
    __主控["经脉可选"]["显示"](__主控["经脉可选"], x-3, y-3)
  end
end
return 经脉格子
