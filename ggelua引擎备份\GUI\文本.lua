-- <AUTHOR> GGELUA
-- @Date                : 2022-03-07 18:52:00
-- @Last Modified by    : baidwwy
-- @Last Modified time  : 2024-06-27 23:23:05

local SDL = require 'SDL'
local GUI控件 = require('GUI.控件')
local GGE文本 = require('GGE.文本')

local GUI文本 = class('GUI文本', GGE文本, GUI控件)

function GUI文本:初始化()
    self._py = 0
    self._max = 0
    GGE文本.GGE文本(self, self.宽度, self.高度)

    self:置文字(self:取根控件()._文字:复制())
end

function GUI文本:_更新(...)
    GUI控件._更新(self, ...)
    GGE文本.更新(self, ...)
end

function GUI文本:_显示(...)
    local _x, _y = self:取坐标()
    self._win:置区域(_x, _y, self.宽度, self.高度)
    GGE文本.显示(self, _x, _y + self._py)
    self._win:置区域()
    GUI控件._显示(self, ...)
end

function GUI文本:置文本(s, ...)
    if not s then
        return 0, 0
    end

    self._py = 0
    local w, h = GGE文本.置文本(self, s, ...)
    self._max = h - self.高度
    if self._max < 0 then
        self._max = 0
    end
    return w, h
end

function GUI文本:置宽度(...)
    local w, h = GGE文本.置宽度(self, ...)
    self._max = h - self.高度
    if self._max < 0 then
        self._max = 0
    end
    GUI控件.置宽度(self, ...)
    return w, h
end

function GUI文本:绑定滑块(obj)
    self.滑块 = obj
    if obj then
        local 置位置 = obj.置位置
        obj.置位置 = function(this, v)
            置位置(this, v)
            self._py = -math.floor(this.位置 / this.最大值 * self._max)
            if self._py == 0 then
                置位置(this, 0)
            end
            return self._py ~= 0
        end
    end
    return obj
end

function GUI文本:创建滑块(name, x, y, w, h)
    local 滑块 = self.父控件:创建滑块(name, x, y, w, h)
    self:绑定滑块(滑块)
    return 滑块
end

-- local _滚动 = function(self, y)
--     local py = self._py + y * (self.高度 / 2)

--     if py > 0 then
--         py = 0
--     elseif math.abs(py) > self._max then
--         py = -self._max
--     end

--     if self.滑块 then
--         self.滑块:置位置(math.floor(math.abs(py) / self._max * self.滑块.最大值))
--     else
--         self._py = math.floor(py)
--     end
-- end

local function _滚动(self,y)

  local py =self._py + y
  if py > 0 then
      py = 0
  elseif math.abs(py) > self._max then
      py = -self._max
  end
  if self.滑块 then
      self.滑块:置位置(math.floor(math.abs(py) / self._max * self.滑块.最大值))
  else
      self._py = math.floor(py)
  end
end

-- function GUI文本:向上滚动()
--     _滚动(self, 100)
-- end

-- function GUI文本:向下滚动()
--     _滚动(self, -100)
-- end

function GUI文本:向上滚动()
  _滚动(self, 40)
end

function GUI文本:向下滚动()
  _滚动(self, -40)
end

function GUI文本:_消息事件(msg)
    if not self.是否可见 or self.是否禁止 or not msg.鼠标 then
        return
    end

    if self:发送消息('鼠标消息') ~= false then
        for _, v in ipairs(msg.鼠标) do
            if v.type == SDL.MOUSE_DOWN then
                if self:检查点(v.x, v.y) then
                    self._DOWN = { x = v.x, y = v.y, py = self._py }
                    local cb = self:检查回调(v.x, v.y)

                    if cb and not self.是否滑动 then
                        if v.button == SDL.BUTTON_LEFT then
                            self._lcb = cb.cb
                            v.typed, v.type = v.type, nil
                            v.control = self
                            self:发送消息('回调左键按下', cb.cb, v.x, v.y)
                        elseif v.button == SDL.BUTTON_RIGHT then
                            self._rcb = cb.cb

                            v.typed, v.type = v.type, nil
                            v.control = self
                            self:发送消息('回调右键按下', cb.cb, v.x, v.y)
                        end
                    end
                end
            elseif v.type == SDL.MOUSE_UP then
                self._DOWN = false
                if self:检查点(v.x, v.y) and not self.是否滑动 then
                    local cb = self:检查回调(v.x, v.y)
                    if cb then
                        if v.button == SDL.BUTTON_LEFT then
                            if cb.cb == self._lcb then
                                v.typed, v.type = v.type, nil
                                v.control = self
                                self:发送消息('回调左键弹起', cb.cb, v.x, v.y, msg)
                            end
                        elseif v.button == SDL.BUTTON_RIGHT then
                            if cb.cb == self._rcb then
                                v.typed, v.type = v.type, nil
                                v.control = self
                                self:发送消息('回调右键弹起', cb.cb, v.x, v.y, msg)
                            end
                        end
                    end
                    self._lcb = nil
                    self._rcb = nil
                    self.是否滑动 = nil
                end
            elseif v.type == SDL.MOUSE_MOTION then
                if gge.platform == 'Windows' then
                    if self:检查点(v.x, v.y) and v.state == 0 then
                        local x, y = self:取坐标()
                        self:发送消息('获得鼠标', x, y, msg)
                        self._mf = true
                        local cb = self:检查回调(v.x, v.y)
                        if cb then
                            v.typed, v.type = v.type, nil
                            v.control = self
                            self._focus = true
                            self:发送消息('获得回调', v.x, v.y, cb.cb, msg)
                        elseif self._focus then
                            self._focus = nil
                            self:发送消息('失去回调', v.x, v.y, msg)
                        end
                    elseif self._mf then
                        self._mf = nil
                        self:发送消息('失去鼠标', v.x, v.y, msg)
                    end
                elseif self._DOWN and v.state & SDL.BUTTON_LMASK == SDL.BUTTON_LMASK then
                        if self:检查点(v.x, v.y) and self._max > 0  then
                            self.是否滑动 = true
                            local py = self._DOWN.py + (v.y - self._DOWN.y)
                            if py > 0 then
                                py = 0
                            elseif math.abs(py) > self._max then
                                py = -self._max
                            end

                            if self.滑块 then
                                self.滑块:置位置(math.floor(math.abs(py) / self._max * self.滑块.最大值))
                            else
                                self._py = math.floor(py)
                            end
                            self:发送消息('鼠标滚轮', py == -self._max)
                        end
                end
            -- elseif v.type == SDL.MOUSE_WHEEL then
            --     local x, y = SDL._wins[v.windowID]:取鼠标坐标()
            --     if self:检查点(x, y) and self._max > 0 then
            --         v.typed, v.type = v.type, nil
            --         v.control = self
            --         _滚动(self, v.y)

            --         self:发送消息('鼠标滚轮', py == -self._max)
            --     end
            elseif v.type == SDL.MOUSE_WHEEL  then
                if self:检查点(v.x, v.y) and self._max > 0  then
                    v.typed, v.type = v.type, nil
                    v.control = self
                    if v.wy >0 then
                        self:向上滚动()
                    else
                      self:向下滚动()
                    end
                    --self:发送消息('鼠标滚轮',v.x, v.y, v.wx, v.wy, msg)
                 --  self:发送消息('鼠标滚轮', py == -self._max)
                end
            end
        end
    end
end

function GUI控件:创建文本(name, x, y, w, h)
    assert(not self[name], name .. ':此文本已存在，不能重复创建.')
    self[name] = GUI文本(name, x, y, w, h, self)
    table.insert(self.子控件, self[name])
    return self[name]
end

return GUI文本
