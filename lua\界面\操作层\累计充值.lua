
local 累计充值 = 窗口层:创建窗口("累计充值", 0, 0, 660, 450)



function 累计充值:初始化()
      self:创建纹理精灵(function()
      置窗口背景("累计充值", 0, 0, 660, 450,true):显示(0,0)
      取白色背景(0, 0, 550, 400, true):显示(90, 40)
     end
   )
     self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
     self.可初始化=true
     if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end
  
end


function 累计充值:打开(data)
      self:置可见(true)

      self:刷新(data)
end



function 累计充值:刷新(数据)
      self.累充数据={}
      self.累充物品={}
      self.累充召唤兽={}
      self.显示档次=1
      self.已领充值 = 0
      self.累计充值 = 0
      if 数据.已领充值~=nil then
          self.已领充值 = 数据.已领充值
      end
      if 数据.累计充值~=nil then
        self.累计充值 = 数据.累计充值
    end
      if 数据.累充数据~=nil then
          self.累充数据=数据.累充数据
          table.sort(self.累充数据,function(a,b) return a.需求充值<b.需求充值 end )
          if self.已领充值>0 then
              local  获取最小 = {}
              for i=1,#self.累充数据 do
                  if self.已领充值<self.累充数据[i].需求充值  then
                    获取最小[#获取最小+1]={数额=self.累充数据[i].需求充值,编号=i}
                  end
              end
              table.sort(获取最小,function(a,b) return a.数额<b.数额 end )
              if  获取最小[1]==nil then
                    self.显示档次 = #self.累充数据
              else
                    self.显示档次 = 获取最小[1].编号
              end
          end
   
         for i = 1,#self.累充数据 do
             self.累充物品[i]={}
             self.累充召唤兽[i]={}
             if self.累充数据[i].召唤兽~="不给"  then
                  local 临时资源 = 取头像(self.累充数据[i].召唤兽)
                  self.累充召唤兽[i].小动画 = 临时资源[2]
                  self.累充召唤兽[i].小资源 = 临时资源[7]
                  local n = 取战斗模型(self.累充数据[i].召唤兽)
                  self.累充召唤兽[i].大模型= n[6]
                  self.累充召唤兽[i].资源 =n[10]
                  self.累充召唤兽[i].名称=self.累充数据[i].召唤兽
                  self.累充召唤兽[i].类型=self.累充数据[i].召唤兽类型
                  self.累充召唤兽[i].数量=1
                  self.累充召唤兽[i].介绍="#G领取"..self.累充数据[i].召唤兽类型..":"..self.累充数据[i].召唤兽
              end
              if self.累充数据[i].物品~=nil and self.累充数据[i].物品数量~=nil  and self.累充数据[i].物品数量+0>0 then
                  for n=1,self.累充数据[i].物品数量+0 do
                      self.累充物品[i][n] = {}
                      local 资源=取物品(self.累充数据[i].物品[n].名称)
                      self.累充物品[i][n].资源 =资源[11]
                      self.累充物品[i][n].小动画=资源[12]
                      self.累充物品[i][n].大动画=资源[13]
                      self.累充物品[i][n].名称=self.累充数据[i].物品[n].名称
                      self.累充物品[i][n].数量=self.累充数据[i].物品[n].数量
                      self.累充物品[i][n].介绍=资源[1]
                  end
              end
         end
      end

      self.累充选择:重置(self.累充数据)
      if self.累充选择.子控件[self.显示档次] then
         self.累充选择:置选中(self.显示档次)
      end
      self:显示刷新()
end

function 累计充值:显示刷新()
  self.图像 = self:创建纹理精灵(function()
    说明字体:置颜色(__取颜色("紫色")):取描边图像("赞助"..self.累充数据[self.显示档次].需求充值.."$礼包"):显示(345-说明字体:取宽度("赞助"..self.累充数据[self.显示档次].需求充值.."$礼包")//2,55)
    说明字体:置颜色(__取颜色("蓝色")):取图像("获得"..self.累充数据[self.显示档次].货币类型.."奖励:"..数额尾数转换(self.累充数据[self.显示档次].货币数量)):显示(100,90)
    说明字体:置颜色(0,0,0,255):取图像("当前充值:"..self.累计充值):显示(100,410)
  end,1
)
  if  self.累充物品[self.显示档次] then
      self.道具网格:置物品(self.累充召唤兽[self.显示档次],self.累充物品[self.显示档次])
  end

  if self.累充数据[self.显示档次].需求充值<=self.已领充值 then
        self.领取奖励:重置文字("已领奖励",true)
  elseif  self.累充数据[self.显示档次].需求充值>self.累计充值 then
        self.领取奖励:重置文字("领取奖励",true)
  else
        self.领取奖励:置禁止(false)
        self.领取奖励:重置文字("领取奖励")  
  end
  
end




local 累充选择 = 累计充值:创建列表("累充选择", 15, 45, 60, 395)
  function 累充选择:初始化()
    self:置文字(说明字体)
    self:置颜色(255,255,255,255)
    self.行间距 = 20
  end
  function 累充选择:重置(data)
        self:清空()
        for i, v in ipairs(data) do
            self:添加("VIP"..i)
        end
  end

  function 累充选择:左键弹起(x, y, i)
        if 累计充值.累充数据[i] then
            累计充值.显示档次 = i
            累计充值:显示刷新()
        end
end










local 道具网格 = 累计充值:创建网格("道具网格", 110, 115, 530, 310)
function 道具网格:初始化()
   
end

function 道具网格:获得鼠标(x, y,a)
      if 累计充值.累充召唤兽[累计充值.显示档次] and 累计充值.累充召唤兽[累计充值.显示档次].小资源 then
            if a == 1 then
                __UI弹出.自定义提示:打开(累计充值.累充召唤兽[累计充值.显示档次],x+20,y+20)
            else
                __UI弹出.自定义提示:打开(累计充值.累充物品[累计充值.显示档次][a-1],x+20,y+20)
            end
      else
          if 累计充值.累充物品[累计充值.显示档次][a] then
              __UI弹出.自定义提示:打开(累计充值.累充物品[累计充值.显示档次][a],x+20,y+20)
          end
      end

end




function 道具网格:左键弹起(x, y, a)
  if __手机 then
      if 累计充值.累充召唤兽[累计充值.显示档次] and 累计充值.累充召唤兽[累计充值.显示档次].小资源 then
            if a == 1 then
                __UI弹出.自定义提示:打开(累计充值.累充召唤兽[累计充值.显示档次],x+20,y+20)
            else
                __UI弹出.自定义提示:打开(累计充值.累充物品[累计充值.显示档次][a-1],x+20,y+20)
            end
      else
          if 累计充值.累充物品[累计充值.显示档次][a] then
              __UI弹出.自定义提示:打开(累计充值.累充物品[累计充值.显示档次][a],x+20,y+20)
          end
      end
  end
end




function 道具网格:置物品(召唤兽,数据)
  self:创建格子(60, 60, 35, 30, math.ceil((#数据+1)/6), 6,math.ceil((#数据+1)/6)>3)
  for i = 1, #self.子控件 do
        self.子控件[i]:创建纹理精灵(function()
          __res:getPNGCC(3, 442, 931, 200, 200):拉伸(60, 60):显示(0, 0)
          文本字体:置颜色(__取颜色("紫色"))
          if 召唤兽 and 召唤兽.小资源 then
              if i == 1 then
                  __res:取资源动画(召唤兽.小资源, 召唤兽.小动画,"图像"):拉伸(50,50):显示(5, 5)
                  文本字体:取描边图像(召唤兽.类型):显示((58-文本字体:取宽度(召唤兽.类型))//2,45)
              else
                  if 数据[i-1] then
                    __res:取资源动画(数据[i-1].资源, 数据[i-1].小动画,"图像"):显示(5, 5)
                    文本字体:取描边图像("x"..数据[i-1].数量):显示((58-文本字体:取宽度("x"..数据[i-1].数量))//2,45)
                  end
              end
          else
              if 数据[i] then
                __res:取资源动画(数据[i].资源, 数据[i].小动画,"图像"):显示(5, 5)
                文本字体:取描边图像("x"..数据[i].数量):显示((58-文本字体:取宽度("x"..数据[i].数量))//2,45)
              end
          end
        end
      )
    end
end




function 累计充值:显示(x,y)
   if self.图像 then
      self.图像:显示(x,y)
   end
end
local 关闭 = 累计充值:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  累计充值:置可见(false)
end


local 领取奖励 = 累计充值:创建蓝色按钮("领取奖励", "领取奖励", 500, 390,120,40,道具字体)    
function 领取奖励:重置文字(txt,jz)
      self:置文字(120,40,txt,道具字体)
      self:置禁止(false)
      if jz then
          self:置禁止(jz)
      end
end


function 领取奖励:左键按下(x, y)
        if 累计充值.显示档次 <= 1 then
              请求服务(36,{领取= "领取累充",文本=累计充值.累充数据[累计充值.显示档次].文本,编号=累计充值.累充数据[累计充值.显示档次].编号})
        else
              if 累计充值.已领充值>=累计充值.累充数据[累计充值.显示档次-1].需求充值 then
                  请求服务(36,{领取= "领取累充",文本=累计充值.累充数据[累计充值.显示档次].文本,编号=累计充值.累充数据[累计充值.显示档次].编号})
              else
                  local xxxx ="您是否领取#R"..累计充值.累充数据[累计充值.显示档次].需求充值.."#H/的赞助奖励,#R注意:你的#Y"..累计充值.累充数据[累计充值.显示档次-1].需求充值.."#R赞助奖励还未领取，领取#Y"..累计充值.累充数据[累计充值.显示档次].需求充值.."#R的赞助奖励后#Y"..累计充值.累充数据[累计充值.显示档次-1].需求充值.."#R的赞助奖励将无法领取"
                  窗口层.文本栏:打开(xxxx,36,{领取= "领取累充",文本=累计充值.累充数据[累计充值.显示档次].文本,编号=累计充值.累充数据[累计充值.显示档次].编号})
              end
        end
end



