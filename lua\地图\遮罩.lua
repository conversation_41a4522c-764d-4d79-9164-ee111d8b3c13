--[[
LastEditTime: 2024-10-14 15:30:07
--]]
--[[
LastEditTime: 2024-10-14 14:56:25
--]]
--[[
LastEditTime: 2024-10-14 06:09:16
--]]

-- local 遮罩 = class("遮罩")
-- function 遮罩:初始化(id, sf, x, y, width, height)
--     self.xy = require("GGE.坐标")(x + 0, y + height):floor()
--     self.pys = require("GGE.坐标")(0, height):floor()
--     self.id = id
--     self.遮罩 = true
--     self.精灵 = require("SDL.精灵")(require("SDL.纹理")(sf))
-- end

-- function 遮罩:更新(dt)
-- end

-- function 遮罩:显示(xy)
--     self.精灵:显示(self.xy + xy - self.pys)
-- end

-- return 遮罩


local 遮罩 = class('遮罩')

function 遮罩:初始化(v,id,sid,ss)

    -- if id and sid and ss then
    --     self.存储路径="map/maplj/"..ss.."/"..id.."/"..sid
    -- end
    self.xy=require("GGE.坐标")(v.x+0,v.y+v.h):floor()
    self.pys=require("GGE.坐标")(0,v.h):floor()
    -- self.id=v.id
end

function 遮罩:更新(dt)

end

function 遮罩:置图像(sf)
    self.精灵 = require("SDL.精灵")(require("SDL.纹理")(sf))
    self.精灵:置透明(170)
    -- if self.存储路径 and not __res:是否存在(self.存储路径) then
    --     local tcp=require("SDL.纹理")(sf):到图像()

    --     tcp:保存文件(self.存储路径, 'PNG')
    --    -- __res:写出文件(self.存储路径,sf)
    -- end
end

function 遮罩:显示(xy)
    if self.精灵 then
        self.精灵:显示(self.xy+xy-self.pys)
    end
end

return 遮罩
