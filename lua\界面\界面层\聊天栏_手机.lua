
local 聊天栏 = 界面层:创建窗口("聊天控件")
function 聊天栏:初始化()
    self:置宽高(400, 220)
    self:置坐标(5, 引擎.高度-225)
    self:创建纹理精灵(function()
      __res.UI素材[1]:复制区域(298, 369, 373, 167):显示(-60, 54) 
    end
  )
    self.消息缓存 = {
        xt = {},
        sj = {},
        dq = {},
        sl = {},
        gm = {},
        cw = {},
        bp = {},
        cy = {},
        dw = {}
    }
    self.禁止右键=true
end


function 聊天栏:左键按下(x,y)
      -- 检查是否点击在摇杆区域内，如果是则忽略
      if 界面层.玩家界面 and 界面层.玩家界面.移动摇杆 then
          if 界面层.玩家界面.移动摇杆:是否在摇杆区域内(x, y) then
              return false -- 忽略摇杆区域的点击
          end
      end
      
      if self.聊天文本:检查点(x,y) and self.聊天文本.是否滑动 then
          self.禁止移动= true
      else
          -- 手机端聊天界面固定位置，禁止移动
          self.禁止移动= true
      end
end

-- 重写移动方法，手机端固定位置
function 聊天栏:移动(x, y)
    -- 手机端聊天界面不允许移动，直接返回
    return
end

-- 添加消息事件处理，检查摇杆区域
function 聊天栏:消息事件(消息)
    if 消息.鼠标 then
        for _, v in ipairs(消息.鼠标) do
            -- 检查是否在摇杆区域内，如果是则不处理
            if 界面层.玩家界面 and 界面层.玩家界面.移动摇杆 then
                if 界面层.玩家界面.移动摇杆:是否在摇杆区域内(v.x, v.y) then
                    return -- 不处理摇杆区域的事件
                end
            end
        end
    end
end





function 聊天栏:添加文本(文本, 频道)
    self.聊天文本:添加文本(文本, 频道)
end
local 频道 = 聊天栏:创建按钮("频道", 6, 1)
function 频道:初始化()
  self:创建按钮精灵(__res:getPNGCC(2, 1096, 181, 53, 52),1)
end
function 频道:左键弹起(x, y, msg)
    窗口层.消息管理:打开(聊天栏.消息缓存)
end

local 多开 = 聊天栏:创建按钮( "多开", 65, 1) 
function 多开:初始化()
  self:创建按钮精灵(__res:getPNGCC(1,719, 200, 60, 74):拉伸(45,52),1)
end
function 多开:左键弹起(x, y)
    if 窗口层.多开系统.是否可见 then
         窗口层.多开系统:置可见(false)
    else
        if  not _tp.战斗中  then
            请求服务(63,{参数=角色信息.数字id,文本="获取角色信息"})
        end
    end
end

-- 添加聊天栏隐藏按钮
local 隐藏按钮 = 聊天栏:创建按钮( "隐藏按钮", 122, 5) 
function 隐藏按钮:初始化()
    self:创建按钮精灵(__res:getPNGCC(1, 677, 318, 30, 30):拉伸(25,25),1)
end
function 隐藏按钮:左键弹起(x, y)
    if 聊天栏.聊天文本.是否可见 then
        -- 隐藏聊天内容
        聊天栏.聊天文本:置可见(false)
        聊天栏:置宽高(150, 55)  -- 缩小聊天栏
    else
        -- 显示聊天内容
        聊天栏.聊天文本:置可见(true)
        聊天栏:置宽高(400, 220)  -- 恢复聊天栏大小
    end
end
-- local 宠物 = 聊天栏:创建按钮( "宠物", 124, 1) 
-- function 宠物:初始化()
--   self:创建按钮精灵(__res:getPNGCC(2, 1150, 185, 40, 40):拉伸(50,50),1)
-- end
-- function 宠物:获得鼠标(x, y)
--     __UI弹出.自定义:打开(x-40,y-20,"快捷键:ALT+O")
-- end
-- function 宠物:左键弹起(x, y)
--     if not _tp.战斗中 then
--         请求服务(5006)
--     end
-- end



local 聊天文本 = 聊天栏:丰富文本("聊天文本", 0, 0, 295, 130, true)
function 聊天文本:初始化()
    self:置坐标(10,70)
   -- self:置文字(文本字体)
end


function 聊天文本:添加文本(文本, 频道)
    if self._max > 1000 then
        self:清空()
        聊天栏["消息缓存"] = {
            xt = {},
            sj = {},
            dq = {},
            sl = {},
            gm = {},
            cw = {},
            bp = {},
            cy = {},
            dw = {}
        }
    end
    if not 频道 or not __频道表[频道] then
        频道 = "dq"
    end
    if not 聊天栏.消息缓存[频道] then 聊天栏.消息缓存[频道]={} end
    table.insert(聊天栏.消息缓存[频道], 文本)
    文本 = 文本 or " "
    local 临时内容 = " #" .. __频道表[频道] .. 文本
    if string.find(文本,"消耗了") or string.find(文本,"请截图给管理员")  or  string.find(文本,"请求下载中")  then
        临时内容 =  文本
    end
    self:置文本(临时内容)
    if 窗口层.消息管理.是否可见 then
        窗口层.消息管理.聊天文本:添加文本(文本, 频道)
    end
    self._py = -self._max
end

function 聊天文本:回调左键弹起(cb, msg)
        if cb then
            -- local lssj = 分割文本(cb, "*")
            -- if "玩家信息" == lssj[3] then
            --     __UI弹出["玩家信息弹出"]["打开"](__UI弹出["玩家信息弹出"], {名称=lssj[1],ID = lssj[4]})
            --     -- 界面层.右上角.玩家头像.头像网格:置头像({
            --     --     模型 = lssj[2],
            --     --     名称 = lssj[1],
            --     --     ID = lssj[4],
            --     --     pid = nil
            --     -- })
            -- elseif  lssj[3] == "召唤兽" then
            --     for i,v in ipairs(chaolianjieshuju) do
            --         if  lssj[3] == v.索引类型 and lssj[1] == v.名称 and lssj[2] == v.认证码 then
            --             窗口层.召唤兽查看:打开(v)
            --             break
            --         end
            --     end
            --     -- table.print(lssj)
            -- elseif  lssj[3] == "道具" then
            --     for i,v in ipairs(chaolianjieshuju) do
            --         if lssj[3] == v.索引类型 and lssj[1] == v.名称 and lssj[2] == v.识别码 then
            --             local wwewq = __物品格子["创建"]()
            --             -- wwewq["置物品"](wwewq, v, nil, "临时背包")
            --             wwewq:取数据(v)
            --          --   wwewq["详情打开"](wwewq, 170+254-232, 86, w, h, "选择", a)
            --             break
            --         end
            --     end
            -- end
        end
end
