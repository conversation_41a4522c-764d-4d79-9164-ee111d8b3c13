local 镇妖拘魂铃 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "镇妖拘魂铃", 80+67+178 + abbr.py.x, 20+42 +129+ abbr.py.y, 410, 215)

function 镇妖拘魂铃:初始化()
  local nsf = require("SDL.图像")(410, 215)
  if nsf["渲染开始"](nsf) then
    -- __res:getPNGCC(5, 0, 0, 683, 450):显示(0,5)
    -- 字体20:置颜色(__取颜色("浅黑"))
    -- 字体20:取图像("伙 伴"):显示(683/2-20,9)
    xiao置窗口背景("炼化镇妖拘魂铃", 0, 12, 363+23,199, true):显示(0, 0)
    字体18:置颜色(__取颜色("白色"))
    字体18:取图像("选择个数"):显示(8,42+16)
    字体18:取图像("请选择你最想要的一个奖励"):显示(8,138+37)
    local 白格子背景=__res:getPNGCC(3, 132, 506, 55, 55)
    for n=1,5 do
      白格子背景:显示(15 + (n-1) * 75, 71+28)
    end
    --取白色背景(0, 0, 440, 300, true)["显示"](取白色背景(0, 0, 440, 300, true), 20, 56)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 镇妖拘魂铃:打开(内容)
  -- self.物品wangge:置数据()
  self:置可见(true)
  self.选择个数 = 1
  self.按钮1:置选中(true)
  self.确定:置禁止(true)
end
function 镇妖拘魂铃:清空数据()
  self.物品组={}
  self.滚动1 = 0
  self.奖励选择 = 0
end
function 镇妖拘魂铃:播放滚动动画(数据)
  self:清空数据()
  self.动画开关 = true
  --获取数据
  self.选择个数 =  数据.选择个数
  local 物品数据 = 数据.物品数据
  -- table.print(物品数据)
  self.物品组={}
  for k,v in pairs(物品数据) do
    -- if self.物品组[k]==nil then
    --    self.物品组[k]={}
    -- end
    for i,n in pairs(v) do
        -- if self.物品组[k][i]== nil then
        --     self.物品组[k][i]={}
        -- end
        -- local 资源=引擎.取物品(n.名称)
        -- self.物品组[k][i].小动画=tp.资源:载入(资源[11],"网易WDF动画",资源[12])
        -- self.物品组[k][i].大动画=tp.资源:载入(资源[11],"网易WDF动画",资源[13])
        -- self.物品组[k][i].名称=n.名称
        -- self.物品组[k][i].备注=n.备注
        -- self.物品组[k][i].说明=资源[1]
        if i==5 then
          self.物品组[k]={名称=n.名称}
        end
    end
  end
  -- for k,v in pairs(self.物品组) do
  --   print(k)
  --   table.print(v)
  -- end
  -- table.print(self.物品组)
  self.物品wangge:置数据()
end

local 物品wangge = 镇妖拘魂铃["创建网格"](镇妖拘魂铃, "物品wangge", 94-25-54, 69+16-25+41, 400, 350)
function 物品wangge:初始化()
  self:创建格子(75, 75, 18, 0, 1, 5)
end
function 物品wangge:左键弹起(x, y, a, b, msg)
  if 镇妖拘魂铃["奖励选择"]~=0 and self.子控件[镇妖拘魂铃["奖励选择"]] and self.子控件[镇妖拘魂铃["奖励选择"]]._spr then
    self.子控件[镇妖拘魂铃["奖励选择"]]._spr["确定"] = nil
  end
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    镇妖拘魂铃["奖励选择"] = a
    镇妖拘魂铃.确定:置禁止(false)
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, -100, 86, w, h, "选择", a)
  end
end
function 物品wangge:置数据()
  for i = 1, #self.子控件 do
    if 镇妖拘魂铃.物品组[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 镇妖拘魂铃.物品组[i], nil,"无需显示数量11")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
      self.子控件[i]._spr["确定"] = nil
    else
      self.子控件[i]["置精灵"](self.子控件[i], nil)
    end
  end
end

for i, v in ipairs({
  {
    name = "一",
    x = 93+(1-1)*33,
    y = 39+16,
    tcp = __res:getPNGCC(2, 1122, 125, 27, 27, true),
    tcp2 = __res:getPNGCC(2, 1163, 145, 27, 27, true),
    font = "1"
  },
  {
    name = "二",
    x = 93+(2-1)*33,
    y = 39+16,
    tcp = __res:getPNGCC(2, 1122, 125, 27, 27, true),
    tcp2 = __res:getPNGCC(2, 1163, 145, 27, 27, true),
    font = "2"
  },
  {
    name = "三",
    x = 93+(3-1)*33,
    y = 39+16,
    tcp = __res:getPNGCC(2, 1122, 125, 27, 27, true),
    tcp2 = __res:getPNGCC(2, 1163, 145, 27, 27, true),
    font = "3"
  },
  {
    name = "四",
    x = 93+(4-1)*33,
    y = 39+16,
    tcp = __res:getPNGCC(2, 1122, 125, 27, 27, true),
    tcp2 = __res:getPNGCC(2, 1163, 145, 27, 27, true),
    font = "4"
  },
  {
    name = "五",
    x = 93+(5-1)*33,
    y = 39+16,
    tcp = __res:getPNGCC(2, 1122, 125, 27, 27, true),
    tcp2 = __res:getPNGCC(2, 1163, 145, 27, 27, true),
    font = "5"
  },
}) do
  local 临时函数 = 镇妖拘魂铃["创建我的单选按钮"](镇妖拘魂铃, v.tcp, v.tcp2, "按钮"..v.font, v.x, v.y, v.font)
  function  临时函数:左键弹起(x, y)
    镇妖拘魂铃.选择个数 = v.font+0
  end
end

local 炼化 = 镇妖拘魂铃["创建我的按钮"](镇妖拘魂铃, __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(100,33), "炼化", 275, 53, "炼化")
function 炼化:左键弹起(x, y, msg)
  发送数据(3760,{选择个数=镇妖拘魂铃.选择个数})
end
local 确定 = 镇妖拘魂铃["创建我的按钮"](镇妖拘魂铃, __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(100,33), "确定", 275, 53+117, "确定")
function 确定:左键弹起(x, y, msg)
  if 镇妖拘魂铃.奖励选择 and 镇妖拘魂铃.奖励选择~=0 then 
    发送数据(3761,{奖励选择=镇妖拘魂铃.奖励选择})
  end
  镇妖拘魂铃.奖励选择=0
  self:置禁止(true)
  镇妖拘魂铃.物品组={}
  镇妖拘魂铃.物品wangge:置数据()

end
local 关闭 = 镇妖拘魂铃["创建我的按钮"](镇妖拘魂铃, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 363+29-47, 0)
function 关闭:左键弹起(x, y, msg)
  镇妖拘魂铃["置可见"](镇妖拘魂铃, false)
end