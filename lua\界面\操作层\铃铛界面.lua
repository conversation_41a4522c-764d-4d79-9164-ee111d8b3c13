

local 铃铛界面 = 窗口层:创建窗口("铃铛界面", 0, 0, 690, 410)
function 铃铛界面:初始化()
  self:创建纹理精灵(function()

  end
)
  self.输入文本 = nil
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
end
  
end



function 铃铛界面:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end


end

local 关闭 = 铃铛界面:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  铃铛界面:置可见(false)
end


