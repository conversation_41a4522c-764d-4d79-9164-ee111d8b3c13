__UI弹出["道具选择"] = __UI界面["创建弹出窗口"](__UI界面, "道具选择", 0, 0, 370, 350)
local 道具选择 = __UI弹出["道具选择"]
function 道具选择:初始化()
  local nsf = require("SDL.图像")(370, 350)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 370, 350, true)["显示"](取黑透明背景(0, 0, 370, 350, true), 0, 0)
    __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 20-5, 14)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.选中道具 = nil
end
function 道具选择:打开(数据, x, y, lx, 总类, 分类, bh)
  self:置可见(true)
  if "洗炼" == lx then
    self.道具网格["置物品"](self.道具网格, 数据, 3, 2)
  elseif "炼化" == lx then
    self.道具网格["置物品"](self.道具网格, 数据, 3)
  elseif "打书" == lx then
    self.道具网格["置物品"](self.道具网格, 数据, 3, 1)
  elseif "内丹" == lx then
    self.道具网格["置物品"](self.道具网格, 数据, 203, 1)
  elseif "法宝合成" == lx then
    self.道具网格["置物品"](self.道具网格, 数据, 总类, 分类)
  elseif "神器1" == lx or "神器2" == lx  or "神器3" == lx  or "神器4" == lx  then
    self.道具网格["置物品"](self.道具网格, 数据, "灵犀玉", 1) 
    
  else
    self.道具网格["置物品"](self.道具网格, 数据)
  end
  self:置坐标(x + abbr.py2.x, y + abbr.py2.y)
  self.类型 = lx
  self.选中道具 = nil
  self.返回编号 = bh
  self.道具 = 数据
end
local 道具网格 = 道具选择["创建网格"](道具选择, "道具网格", 20-5, 14, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] and not self.子控件[a]._spr["物品禁止"] then
    local xx, yy = 道具选择["取坐标减宽"](道具选择)
    if 道具选择["选中道具"] and self.子控件[道具选择["选中道具"]]._spr["物品"] then
      self.子控件[道具选择["选中道具"]]._spr["确定"] = nil
    end
    道具选择["选中道具"] = a
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, xx, yy, nil, nil, "选择", a)
  end
end
function 道具网格:置物品(data, zl, fl)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], nil, "道具选择")
      lssj["置禁止"](lssj, zl, fl)
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
local 取消材料 = 道具选择["创建我的按钮"](道具选择, __res:getPNGCC(3, 126, 563, 111, 36, true), "取消材料", 15, 300, "取消材料")
function 取消材料:左键弹起(x, y, msg)
  道具选择["置可见"](道具选择, false)
end
local 添加材料 = 道具选择["创建我的按钮"](道具选择, __res:getPNGCC(3, 126, 563, 111, 36, true), "添加材料", 228, 300, "添加材料")
function 添加材料:左键弹起(x, y, msg)
  if 道具选择["选中道具"] and 道具选择["道具"][道具选择["选中道具"]] then
    if 道具选择["类型"] == "法宝合成" then
      __UI界面["窗口层"]["法宝锻造"]["材料网格"]["置物品"](__UI界面["窗口层"]["法宝锻造"]["材料网格"], 道具选择["道具"][道具选择["选中道具"]], 道具选择["返回编号"], 道具选择["选中道具"])
      道具选择["置可见"](道具选择, false)
    elseif 道具选择["类型"] == "神器1" then
      __UI界面["窗口层"]["修复神器"]["材料网格1"]["置物品"](__UI界面["窗口层"]["修复神器"]["材料网格1"], 道具选择["道具"][道具选择["选中道具"]],道具选择["返回编号"], 道具选择["选中道具"])
      道具选择["置可见"](道具选择, false)
    elseif 道具选择["类型"] == "神器2" then
      __UI界面["窗口层"]["修复神器"]["材料网格2"]["置物品"](__UI界面["窗口层"]["修复神器"]["材料网格2"], 道具选择["道具"][道具选择["选中道具"]], 道具选择["返回编号"],道具选择["选中道具"])
      道具选择["置可见"](道具选择, false)
    elseif 道具选择["类型"] == "神器3" then
      __UI界面["窗口层"]["修复神器"]["材料网格3"]["置物品"](__UI界面["窗口层"]["修复神器"]["材料网格3"], 道具选择["道具"][道具选择["选中道具"]],道具选择["返回编号"],  道具选择["选中道具"])
      道具选择["置可见"](道具选择, false)
    elseif 道具选择["类型"] == "神器4" then
      __UI界面["窗口层"]["修复神器"]["材料网格4"]["置物品"](__UI界面["窗口层"]["修复神器"]["材料网格4"], 道具选择["道具"][道具选择["选中道具"]],道具选择["返回编号"], 道具选择["选中道具"])
      道具选择["置可见"](道具选择, false)
    
    else
      __UI界面["窗口层"]["召唤兽洗炼"]["材料网格"]["置物品"](__UI界面["窗口层"]["召唤兽洗炼"]["材料网格"], 道具选择["道具"][道具选择["选中道具"]], 道具选择["选中道具"])
      道具选择["置可见"](道具选择, false)
    end
  end
end
