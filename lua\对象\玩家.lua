--[[
Author: GGELUA
Date: 2024-03-31 01:31:44
Last Modified by: GGELUA
Last Modified time: 2024-04-01 11:53:41
--]]

local 动作 = require("对象/基类/角色动作")
local 控制 = require("对象/基类/控制")
local 状态 = require("对象/基类/状态")
local 玩家 = class("玩家", 动作, 控制, 状态)
local SDL = require("SDL")
function 玩家:初始化(数据)
    self.id = 数据.id
    self.玩家ID = 数据.id
    self.组合 = 数据.组合
    self.等级 = 数据.等级
    self.性别 = 数据.性别
    self.种族 = 数据.种族
    self.帮派 = 数据.帮派
    self.门派 = 数据.门派
end

function 玩家:更新(dt)
    self[动作]:更新(dt)
    self[控制]:更新(dt)
    self[状态]:更新(dt)
end

function 玩家:显示(pys)
    local p = self.xy + pys
    self[状态]:显示底层(p)
    self[动作]:显示(p)
    self[状态]:显示(p)
    self[状态]:显示顶层(p)
end

function 玩家:设置路径(数据)
    if not _tp.战斗中 then
        if self.dest and self.dest.x == 数据.x and self.dest.y == 数据.y then return end
        self.dest = 数据
        local xy = require('GGE.坐标')(数据.x * 20, 数据.y * 20)
        local route = __主显.地图:寻路(self.xy, xy)
        if self.飞行 then
            self.移动速度 = 220
            route =__主显.地图:飞行寻路(self.xy, xy)
        else
            self.移动速度 = 150
        end
        if #route > 0 then
            self:路径移动(route)
        end
    else
        self.xy:pack(数据.x * 20, 数据.y * 20)
    end
end


function 玩家:查找重叠(x,y)
          local 重叠目标={[1]={编号=self.门派,序列=self.模型,标识=self.玩家ID,名称=self.名称,类型=ggetype(self)}}
          for k, n in pairs(__主显.场景人物) do
              if n~=self and n.检查透明 and n:检查透明(x,y) then
                  if ggetype(n)=="NPC" then
                      table.insert(重叠目标,{编号=n.编号,序列=n.编号,标识=n.id,名称=n.名称,类型=ggetype(n)})
                  elseif ggetype(n)=="玩家" then
                      table.insert(重叠目标,{编号=n.门派,序列=n.模型,标识=n.玩家ID,名称=n.名称,类型=ggetype(n)})
                  end
              end
          end
          if #重叠目标>1 then
              local 列表={}
              for z, j in ipairs(重叠目标) do
                  列表[z] = j.名称
              end
              local 事件 =function (a)
                  if 重叠目标[a] then
                    if 重叠目标[a].类型=="NPC" then
                      if __主显 and __主显.地图id >100000 then
                            if 重叠目标[a].名称 == "管家" then
                                窗口层.对话栏:打开("男人_店小二","管家","主人，有什么可以为你效劳的。",{"使用厨房","使用丹房","使用卧室","我还要逛逛"})
                            elseif 重叠目标[a].名称 == "佣人" then
                                  窗口层.对话栏:打开("女人_丫鬟","佣人","主人，有什么可以为你效劳的。",{"侍奉睡觉","打开仓库","养儿育女","我还要逛逛"})
                            elseif 重叠目标[a].名称 == "牧场看守" then
                                  窗口层.对话栏:打开("男人_兰虎","牧场看守","主人，有什么可以为你效劳的。",{"打开牧场界面","我还要逛逛"})
                            elseif 重叠目标[a].名称 == "牧场管理员" then
                                  窗口层.对话栏:打开("男人_兰虎","牧场管理员","主人，有什么可以为你效劳的。",{"进入牧场","我还要逛逛"})
                            end
                      else
                            请求服务(1501, { 地图 = 角色信息.地图数据.编号, 编号 = 重叠目标[a].编号, 序列 = 重叠目标[a].序列,标识 = 重叠目标[a].标识 })
                      end
                    elseif 重叠目标[a].类型=="玩家" then
                        __UI弹出.玩家信息:打开({模型 = 重叠目标[a].序列,[1] = 重叠目标[a].名称,[2]= 重叠目标[a].标识,[3]=重叠目标[a].编号 or "无门派"})
                    end
                  end
              end
              __UI弹出.弹出列表:打开(列表,nil,事件,x+30, y+25,130)
              return true
          end




end

function 玩家:消息事件(t)
    self[状态]:消息事件(t)
    self[动作]:消息事件(t)
    if t.摆摊 == self then
        请求服务(3725, { id = self.玩家ID })
        if __主显 and __主显.主角 then
            __主显.主角.按下=false
            __主显.主角.点击移动=nil
        end
        return
    end
    if not t.鼠标 then return end
    for _, v in ipairs(t.鼠标) do
        if  self:检查透明(v.x, v.y) then
            if v.type == SDL.MOUSE_UP and v.button == SDL.BUTTON_LEFT then
                    v.type = nil
                    if 界面层.类型 then
                        if 界面层.类型 == '组队' then
                            if self.是否队长 then --有队伍的情况
                                        请求服务(4002,{id=self.玩家ID})
                            else
                                        请求服务(4014,{id=self.玩家ID})
                            end
                        elseif 界面层.类型 == '好友' then
                                    请求服务(18,{id=self.玩家ID})
                        elseif 界面层.类型 == '交易' then
                                请求服务(3718,{id=self.玩家ID})
                        elseif 界面层.类型 == '给予' then
                                请求服务(3716, { id = self.玩家ID })
                        elseif 界面层.类型 == '攻击' then
                                请求服务(33, {
                                    序列 = self.玩家ID
                                })
                                
                        end
                        界面层:重置()
                        鼠标层:正常形状()
                    elseif __手机 and self.模型 and self.名称 and self.玩家ID then
                            if not self:查找重叠(v.x,v.y) then
                                __UI弹出.玩家信息:打开({模型 = self.模型,[1] = self.名称,[2]= self.玩家ID,[3]=self.门派 or "无门派"})
                            end 
                    end
            elseif v.button == SDL.BUTTON_RIGHT and v.type == SDL.MOUSE_UP and  not 界面层.类型 and self.模型 and self.名称 and self.玩家ID then--鼠标右键
                    if not self:查找重叠(v.x,v.y) then
                        __UI弹出.玩家信息:打开({模型 = self.模型,[1] = self.名称,[2]= self.玩家ID,[3]=self.门派 or "无门派"})
                    end
                     v.type = nil
            end
        end
    end





end

return 玩家
