
local 帮派点修 = 窗口层:创建窗口("帮派点修", 0, 0, 360, 220)
local sts = {"攻击修炼","法术修炼","猎术修炼","防御修炼","抗法修炼"}
function 帮派点修:初始化()
  self:创建纹理精灵(function()
              置窗口背景("选择修炼", 0, 0, 360, 220, true):显示(0, 0)
              取白色背景(0, 0, 340, 140, true):显示(10, 35)
            end
    )
  self.选中修炼=nil
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end

function 帮派点修:打开()
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.选中修炼=nil
    self.项目选择:重置()
end
local 项目选择 = 帮派点修:创建列表("项目选择", 15, 40, 330, 195)
function 项目选择:初始化()
  self:置文字(标题字体)
  self:置颜色(0,0,0,255)
  self.行间距 = 10
end
function 项目选择:重置()
    self:清空()
    for i=1, #sts do
        if i<=2 then
            self:添加(sts[i].."       3万两5点帮贡5点帮派资材/次")
        else
            self:添加(sts[i].."       2万两5点帮贡5点帮派资材/次")
        end

        
    end
end
function 项目选择:左键弹起(x, y, i)
      帮派点修.选中修炼=i
end

local 修炼 = 帮派点修:创建红色按钮("修炼", "修炼", 125, 185,80, 22)
function 修炼:左键弹起(x, y, msg)
  if 帮派点修.选中修炼 then
      请求服务(3743,{修炼项目=sts[帮派点修.选中修炼]})
  else
       __UI弹出.提示框:打开("#Y请选择你要修炼的项目")
  end
end

local 关闭 = 帮派点修:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  帮派点修:置可见(false)
end



