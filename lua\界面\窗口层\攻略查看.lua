--[[
LastEditTime: 2025-01-19 17:35:07
--]]


local 攻略查看 = 窗口层:创建窗口("攻略查看", 0, 0, 320, 438)
function 攻略查看:初始化()
  self:创建纹理精灵(function()
      置窗口背景("查看攻略", 0, 0, 320, 438, true):显示(0, 0)
      取白色背景(0, 0, 300, 390, true):显示(10, 35)
  end)
    self.表情动画 = __res:取资源动画("dlzy", 0x4FAD347C,"动画")
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
    if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end

end

function 攻略查看:更新(dt)
  self.表情动画:更新(dt)
end

function 攻略查看:显示(x,y)
  for i = 1, 9 do
    self.表情动画:显示(x+70+(i-1)*20,y+65)
  end
end


function 攻略查看:打开(数据)
  self:置可见(true)
  self.文本:清空()
  self.文本:置文本(数据.文本 or "")
end


local 文本 = 攻略查看:丰富文本("文本", 15, 75, 290, 340)


local 关闭 = 攻略查看:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    攻略查看:置可见(false)
  end