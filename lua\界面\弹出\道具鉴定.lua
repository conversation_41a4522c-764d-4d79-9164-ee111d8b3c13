__UI弹出["道具鉴定"] = __UI界面["创建弹出窗口"](__UI界面, "道具鉴定", 190 + abbr.py2.x, 90 + abbr.py2.y, 370, 300)
local 道具鉴定 = __UI弹出["道具鉴定"]
function 道具鉴定:初始化()
  local nsf = require("SDL.图像")(370, 300)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 370, 300, true)["显示"](取黑透明背景(0, 0, 370, 300, true), 0, 0)
    __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 17, 14)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.选中道具 = nil
end
function 道具鉴定:打开(sj,内丹,吸附石)
  self:置可见(true)
  self.符纸格子 = nil
  self.符纸总类 = nil
  self.符纸分类 = nil
  self.符纸子类 = nil
  self.宝宝 = nil
  self.选中兽决 = nil
  if not 内丹 and not 吸附石 then
    self.符纸格子 = sj[1]
    self.符纸总类 = sj[2]
    self.符纸分类 = sj[3]
    self.符纸子类 = sj[4]
    self.功能分类 = sj[5]
    -- self.助战=sj[6]
  elseif 内丹 then
    self.宝宝 = 内丹
    self.功能分类 = "打内丹"
  elseif 吸附石 then
    self.选中兽决 = 0
    self.功能分类 = "吸附石"
  end
  if sj and sj[6] then
    if sj[6]=="灵饰洗练石" then
      self.功能分类 = "灵饰洗练石"
      self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 2)
    elseif  sj[6]=="双加洗练石" then
      self.功能分类 = "双加洗练石"
      self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 2)
    elseif sj[6]=="和田白玉" then
      self.功能分类 = "和田白玉"
      self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 2)
    end
  end
  if self.功能分类 == "打内丹" then
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 203)
  elseif self.功能分类 == "吸附石" then
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 3)
  elseif self.功能分类 == "光武拓印" then
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 2)
  elseif self.功能分类 == "获取法宝" then
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 1000)
  -- elseif self.功能分类 == "特性宝珠" then
  -- 	self.物品[is]:显示(dt,x,y,self.鼠标,{2,2})
  else
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 2)
  end
  
end

local 道具网格 = 道具鉴定["创建网格"](道具鉴定, "道具网格", 17, 14, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end

function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and not self.子控件[a]._spr["物品禁止"] and self.子控件[a]._spr["物品"] then
    local wuping=self.子控件[a]._spr["物品"]
    if 道具鉴定.功能分类 == "鉴定" and wuping.总类 == 2 then
      if wuping.鉴定 ~= nil and  wuping.鉴定 == false then
        if 道具鉴定.符纸总类 == 111 then
          if 道具鉴定.符纸子类 >= wuping.级别限制 then
            if 道具鉴定.符纸分类 == 1 and (wuping.分类 == 3 or wuping.分类 == 4) then
              __主控:播放音效类("鉴定" )
              if wuping.专用提示 then
                  道具鉴定["置可见"](道具鉴定, false)
                  local 事件 = function()
                    发送数据(3758,{类型="装备鉴定",道具格子=a,符纸格子=道具鉴定.符纸格子})
                  end
                  local wb = "#W恭喜！这物品可能变为特有装备，#Y特有装备只能由你本人使用#W，你需要将它鉴定成特有装备吗？"
                  __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, wb, 285, 155, 390, 200, 事件)
                  return
              end
              发送数据(3738,{类型="装备鉴定",道具格子=a,符纸格子=道具鉴定.符纸格子})
              道具鉴定["置可见"](道具鉴定, false)
            elseif 道具鉴定.符纸分类 == 2 and (wuping.分类 == 1 or  wuping.分类 == 2 or wuping.分类 == 5 or wuping.分类 == 6) then
              __主控:播放音效类("鉴定" )
              if wuping.专用提示 then
                  道具鉴定["置可见"](道具鉴定, false)
                  local 事件 = function()
                    发送数据(3758,{类型="装备鉴定",道具格子=a,符纸格子=道具鉴定.符纸格子})
                  end
                  local wb = "#W恭喜！这物品可能变为特有装备，#Y特有装备只能由你本人使用#W，你需要将它鉴定成特有装备吗？"
                  __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, wb, 285, 155, 390, 200, 事件)
                  return
              end
              发送数据(3738,{类型="装备鉴定",道具格子=a,符纸格子=道具鉴定.符纸格子})
              道具鉴定["置可见"](道具鉴定, false)
            elseif 道具鉴定.符纸分类 == 3 and (wuping.分类 == 10 or wuping.分类 == 11 or wuping.分类 == 12 or wuping.分类 == 13) then
              __主控:播放音效类("鉴定" )
              发送数据(3738,{类型="灵饰鉴定",道具格子=a,符纸格子=道具鉴定.符纸格子}) 
              道具鉴定["置可见"](道具鉴定, false)
            else
              __UI弹出.提示框:打开("#Y/此图鉴类型无法鉴定该装备")
            end
          else
            __UI弹出.提示框:打开("#Y/此图鉴的等级过低无法鉴定该装备")
          end
        else
          __UI弹出.提示框:打开("#Y/这个物品不能用于鉴定！")
        end
      else
     --   print(道具鉴定.符纸总类)
        if 道具鉴定.符纸总类 == 101 then
          __主控:播放音效类("鉴定" )
          发送数据(3738,{类型="强化符",道具格子=a,符纸格子=道具鉴定.符纸格子})
          道具鉴定["置可见"](道具鉴定, false)
        elseif 道具鉴定.符纸总类 == 180 then
          __主控:播放音效类("鉴定" )
          发送数据(3738,{类型="特技书",道具格子=a,符纸格子=道具鉴定.符纸格子})
          道具鉴定["置可见"](道具鉴定, false)
        elseif 道具鉴定.符纸总类 == 181 then
          __主控:播放音效类("鉴定" )
          发送数据(3738,{类型="和氏璧",道具格子=a,符纸格子=道具鉴定.符纸格子})
          道具鉴定["置可见"](道具鉴定, false)
        elseif 道具鉴定.符纸总类 == 182 then
          __主控:播放音效类("鉴定" )
          发送数据(3738,{类型="和田玉",道具格子=a,符纸格子=道具鉴定.符纸格子})
          道具鉴定["置可见"](道具鉴定, false)
        elseif 道具鉴定.符纸总类 == 183 then
          __主控:播放音效类("鉴定" )
          发送数据(3738,{类型="愤怒符",道具格子=a,符纸格子=道具鉴定.符纸格子})
          道具鉴定["置可见"](道具鉴定, false)
        elseif 道具鉴定.符纸总类 == 184 then
          __主控:播放音效类("鉴定" )
          发送数据(3738,{类型="特性宝珠",道具格子=a,符纸格子=道具鉴定.符纸格子})
          道具鉴定["置可见"](道具鉴定, false)
        else
          __UI弹出.提示框:打开("#Y/这件装备已经鉴定过了")
        end
      end
    elseif 道具鉴定.功能分类 == "附魔" and wuping.总类 == 2 then
      if wuping.鉴定 ~= nil and  wuping.鉴定 then
        if wuping.分类 <= 6 then
          发送数据(3738,{类型="装备附魔",道具格子=a,符纸格子=道具鉴定.符纸格子})
          道具鉴定["置可见"](道具鉴定, false)
        end
      end
    elseif (道具鉴定.功能分类 == "灵饰洗练石" or 道具鉴定.功能分类 == "双加洗练石" or 道具鉴定.功能分类 == "和田白玉") and wuping.总类 == 2 then
      发送数据(3738,{类型=道具鉴定.功能分类,道具格子=a,符纸格子=道具鉴定.符纸格子})
      __主控:播放音效类("鉴定" )
      道具鉴定["置可见"](道具鉴定, false)
    elseif 道具鉴定.功能分类 == "打内丹" and wuping.总类 == 203 then
      发送数据(5014,{类型="宝宝打内丹",道具格子=a,bb=道具鉴定.宝宝})
      道具鉴定["置可见"](道具鉴定, false)
    elseif 道具鉴定.功能分类 == "吸附石" then
      if wuping.名称 == "魔兽要诀" or wuping.名称 == "高级魔兽要诀" then
        local 事件 = function()
          发送数据(3759,{兽诀格子=a})
        end
        local wb = "吸附石可直接吸附消耗魔兽要诀来获得对应的点化石。有一定概率失败，失败后会损失吸附石。（魔兽要诀不会损失）"
        __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, wb, 285, 155, 390, 200, 事件)
        道具鉴定["置可见"](道具鉴定, false)
      else
        __UI弹出.提示框:打开("#Y/我不认识这个技能哦")
      end
    elseif 道具鉴定.功能分类 == "光武拓印" then
      发送数据(3781,{道具格子=a})
      道具鉴定["置可见"](道具鉴定, false)
    end
  end
end
function 道具网格:置物品(data, zl, fl)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], nil, "道具选择",huise,true) --置物品(数据, 背景, 类型,huise,子类描述)
      lssj["置禁止"](lssj, zl, fl)
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
