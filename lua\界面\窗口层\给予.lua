local 给予 = 窗口层:创建窗口("给予", 0, 0, 490, 290)
function 给予:初始化()
  self:创建纹理精灵(function()
          置窗口背景("给予", 0, 0, 490, 290,true):显示(0,0)
          文本字体:置颜色(255,255,255,255):取图像("现有银两:"):显示(15, 180)
          文本字体:置颜色(__取颜色("黄色")):取图像("给予银两:"):显示(15, 210)
          for i = 1, 3 do
            取输入背景(0, 0, 55, 22):显示(15+(i-1)*70,140)
          end
          取输入背景(0, 0, 120, 22):显示(82,177) 
          取输入背景(0, 0, 120, 22):显示(82,207)
  end)
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end

end



  
  local 给予按钮 = 给予:创建红色按钮("给予", "给予按钮", 58,242, 44, 22)
  function 给予按钮:左键弹起(x, y)
      if 给予.银两:取文本()~=nil and 给予.银两:取文本()~="" then
         if tonumber(给予.银两:取文本())==nil or tonumber(给予.银两:取文本())==0 then
              __UI弹出.提示框:打开("#Y请输入正确的银子数额")
            return
         end 
      end
      local 格子 = {}
      for i, v in ipairs(给予.材料物品) do
          if v.原始编号 then
            格子[i] = v.原始编号
          end
      end
      请求服务(3715,{格子=格子,银子=tonumber(给予.银两:取文本())})
      给予:置可见(false)
  end

function 给予:打开(名称,类型,等级)
  self:置可见(true,true)
  self.名称=名称
  self.类型=类型
  self.等级=等级
  self.银两:置文本()
  self.道具网格:置数据()
  self:道具刷新()
end



function 给予:显示(x, y)
    if self.图像 then
        self.图像:显示(x, y)
    end
end

function 给予:道具刷新()
        self.材料物品={}
        self.道具网格:置物品(_tp.道具列表)
        self.材料网格:置物品(self.材料物品)
        self:显示刷新()

end
function 给予:显示刷新()
  
  self.图像 = self:创建纹理精灵(function()
                    文本字体:置颜色(255,255,255,255):取图像(self.类型..": "..self.名称):显示(15, 40)
                    文本字体:置颜色(__取银子颜色(角色信息.银子)):取图像(角色信息.银子):显示(87, 180)
                  for i=1,3 do
                      if self.材料物品[i] then
                        if self.材料物品[i].数量 then
                          说明字体:置颜色(0,0,0,255):取图像(self.材料物品[i].数量):显示((95-说明字体:取宽度(self.材料物品[i].数量))//2+(i-1)*70,143)
                        else
                          说明字体:置颜色(0,0,0,255):取图像("1"):显示((95-说明字体:取宽度("1"))//2+(i-1)*70,143)
                        end
                      end
                  end
              end,1,250,280)
end




local 银两 = 给予:创建文本输入("银两",87,210, 120, 18)
function 银两:初始化()
  self:取光标精灵()
  self:置限制字数(11)
  self:置颜色(0, 0, 0, 255)
  self:置模式(2)
end




local 道具网格 = 给予:创建背包网格("道具网格", 220, 33)

function 道具网格:获得鼠标(x, y,a)
    local 物品 = self:焦点物品()
    if 物品 and 物品.物品 then
        __UI弹出.道具提示:打开(物品.物品,x+25,y+25)
    end
end



function 道具网格:左键弹起(x, y, a)
        local 物品 = self:选中物品()
        if 物品 and 物品.物品 and self:选中()~=0 then
              if __手机 then
                  __UI弹出.道具提示:打开(物品.物品,x+25,y+25,道具网格,"选择",1)
              else
                  self:选择(1)
              end
        end

end

function 道具网格:选择(编号)
    if 编号 and 编号~=0 then
        给予:设置物品(self:选中())
    end
end




function 给予:设置物品(id)
  local 编号=0
  for n=1,3 do
    if not self.材料物品[n] and 编号==0 then 编号=n end
  end
  if 编号 == 0 then
      if self.材料物品[1] and self.材料物品[1].原始编号 then
          _tp.道具列表[self.材料物品[1].原始编号]=self.材料物品[1]
          self.材料物品[1] = nil
      end
      self.材料物品[1]= _tp.道具列表[id]
      self.材料物品[1].原始编号=id
      _tp.道具列表[id]=nil
  else
      if self.材料物品[编号] and self.材料物品[编号].原始编号 then
        _tp.道具列表[self.材料物品[编号].原始编号]=self.材料物品[编号]
          self.材料物品[编号] = nil
      end
      self.材料物品[编号]= _tp.道具列表[id]
      self.材料物品[编号].原始编号=id
      _tp.道具列表[id]=nil
  end
  self.道具网格:置物品(_tp.道具列表)
  self.材料网格:置物品(self.材料物品)
  self:显示刷新()
end



local 材料网格 = 给予:创建网格("材料网格", 15, 80, 210, 55)  
function 材料网格:初始化()
   self:创建格子(55, 55, 10, 15, 1, 3)
end

function 材料网格:获得鼠标(x, y, a)
      if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点=nil
      end
      self.焦点=nil
      if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
              self.焦点=a
              self.子控件[a]._spr.焦点=true
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25)
      end
end
function 材料网格:失去鼠标(x, y, a)
    for i, v in ipairs(self.子控件) do
        if v._spr then
            v._spr.焦点=nil
        end
    end
    self.焦点=nil
end


function 材料网格:左键弹起(x, y, a)
  if 给予.材料物品[a] and 给予.材料物品[a].原始编号 then
        if __手机 then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25,材料网格,"取消",a)
        else
              self:取消(a)
        end
  end
end

function 材料网格:取消(编号)
        if 编号 and 编号~=0 then
              _tp.道具列表[给予.材料物品[编号].原始编号]=给予.材料物品[编号]
              给予.道具网格:置物品(_tp.道具列表)
              给予.材料物品[编号] = nil
              self:置物品(给予.材料物品)
        end
end




function 材料网格:置物品(数据)
    for i, v in ipairs(self.子控件) do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,55,55,nil,true)
        if 数据 and 数据[i] then
          lssj:置物品(数据[i],55,55,"数量",true,true)
        end
        self.子控件[i]:置精灵(lssj)
    end 
end






local 取消按钮 = 给予:创建红色按钮("取消", "取消按钮", 145,242, 44, 22)
function 取消按钮:左键弹起(x, y)
     给予:置可见(false)
end
local 关闭 = 给予:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  给予:置可见(false)
end





