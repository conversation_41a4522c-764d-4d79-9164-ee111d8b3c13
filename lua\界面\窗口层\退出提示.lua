

--创建提示控件
local 退出提示 = 窗口层:创建窗口("退出提示", 0, 0, 360, 120)
function 退出提示:初始化()
  self:置精灵(置窗口背景("退出游戏",0,0,360,120)) 
  self:置坐标((引擎.宽度-self.宽度)// 2,(引擎.高度-self.高度) // 2)
  self.可初始化=true
  self.说明=文本字体:置颜色(__取颜色("黄色")):取精灵("游戏素材来源于网络,请勿商用")
  if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
  else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
  end
end

function 退出提示:显示(x,y)
        self.说明:显示(x+(self.宽度-self.说明.宽度)//2,y+35)
end




local 按钮设置={"退出游戏","到主界面","继续游戏","游戏团队"}

for i,v in ipairs(按钮设置) do
      local 临时按钮=退出提示:创建红色按钮(v, v, 25+(i-1)*80, 75,74,20) 
      function 临时按钮:左键弹起(x, y)
              if v=="退出游戏" then
                  引擎:关闭()
              elseif v=="继续游戏" then
                    退出提示:置可见(false)
              elseif v=="到主界面" then
                  登录层:置可见(true)
                  __CLT:断开()
                  登录层.更新界面:置可见(false)
                  登录层.登录游戏:置可见(false)
                  登录层.选择大区:置可见(false)
                  登录层.角色界面:置可见(false)
                  登录层.创建角色:置可见(false)
                  登录层.注册账号:置可见(false)
                  登录层.公告界面:置可见(true, true)
                  退出提示:置可见(false)

              end
  
      end

end











local 关闭 = 退出提示:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    退出提示:置可见(false)
end











