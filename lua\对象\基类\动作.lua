local 动作 = class("动作")
local ggf = require("GGE.函数")
动作["模型表"] = { "静立", "行走" }
function 动作:初始化(t)
    self.模型 = t["模型"]
    -- self.锦衣 = t["锦衣"]
    --print(t.pid)
--    if t.ID and 角色信息.数字id==t.ID then
--     print(t.模型)
--    end
    -- table.print(self.锦衣)
    self.变身数据 = t["变身数据"]
    self.坐骑 = t["坐骑"]
    self.武器 = {}
	self.锦衣 = {}
    if t["装备"] then
        self.武器 = t["装备"][3]
    elseif t["武器"] then
        self.武器 = t["武器"]
        if t["武器子类"] then
            self.武器子类 = t["武器子类"]
        end
    end

    if t.装备 and t.装备[4] and t.装备[4].子类 == 911 then
        self.副武器={名称=t.装备[4].名称,子类=t.装备[4].子类,级别限制 = t.装备[4].级别限制}
    elseif t.副武器 then
        if ItemData[t.副武器] then
            self.副武器={名称=t.副武器,子类=__quwuqizilei(t.副武器)}
        elseif t.副武器.名称 then
            self.副武器={名称=t.副武器.名称,子类=(t.副武器.子类 or __quwuqizilei(t.副武器.名称)),级别限制=t.副武器["级别限制"]}
        end
    end
    -- if t["装备"] then
    --     self.武器 = t["装备"][3]
    -- elseif t["武器"] then
    --     --self.武器 = t["武器"]
    --     if t.武器 then
    --         if ItemData[t.武器] then --只是单纯的武器名称
    --             self.武器={名称=t.武器,子类=__quwuqizilei(t.武器)}
    --         elseif t.武器.名称 then
    --             self.武器={名称=t.武器.名称,子类=(t.武器.子类 or __quwuqizilei(t.武器.名称)),级别限制=t.武器["级别限制"]}
    --         end
    --     end
    -- end
    if t.锦衣 then
		if ItemData[t.锦衣] then --只是单纯的武器名称
			self.锦衣[1]=t.锦衣
		elseif t.锦衣[1] then
			self.锦衣 = t["锦衣"]
		end
	end
    self.饰品 = t["显示饰品"]
    self.染色组 = t["染色组"]
    self.染色方案 = t["染色方案"]
    self.炫彩组 = t["炫彩组"]
    self.炫彩 = t["炫彩"]
    if t["地图数据"] and t["地图数据"]["方向"] then
        self.方向 = t["地图数据"]["方向"] + 1
    elseif t["方向"] then
        self.方向 = t["方向"] + 1
    else
        self.方向 = 1
    end
    self.置wpal(self)
    self.动作 = self.模型表[1]
    self.模型编号 = {}
    self.显示1 = false
    self:清空资源()
    if t.ID and 角色信息.数字id==t.ID then
        self.zhujue=true
        self:置模型()
    end
    self.飞行高度 = 0
end
function 动作:判断一体坐骑(卸下)
    if not 卸下 then
		if  self.变身数据 == nil and self.坐骑 and self.坐骑.模型 and 一体坐骑表[self.坐骑.模型] then
			self:yitizuoqi(self.模型,self.坐骑.模型)
			return true
		end
	end
end
local 不显示锦衣=false
function 动作:置模型()
    self:清空资源()
    local lssj
    local 显示染色=true
    if not self.变身数据 then
        -- if 1 == __res["配置"]["锦衣"] then--and __附加资源 and 不显示锦衣 then
        --     self.置光环(self)
        --     self.置足迹(self)
        -- end
        if 锦衣文件完整 then
            self.置光环(self)
            self.置足迹(self)
        end
        if 坐骑文件完整 and self:判断一体坐骑(卸下) then
            return
        end
        if 锦衣文件完整 and self.锦衣 and self.锦衣[1] then
            local wpmc
            if self.武器 and self.武器["子类"] then
                wpmc = __主控["取武器子类"](__主控, self.武器["子类"])
               -- print(self.武器["子类"],self.名称)
            end
            local ziyuan=取普通锦衣模型(self.锦衣[1].名称 or self.锦衣[1],self.模型,wpmc) --取普通锦衣模型(jy,js,wpmc)
            -- table.print(ziyuan)
            self.actions["静立"] = __res:取动画2(__res:取地址("shape/sys/", ziyuan[1])):置循环(true)
            self.actions["行走"] = __res:取动画2(__res:取地址("shape/sys/", ziyuan[2])):置循环(true)
            if wpmc then
               -- print(self.武器["子类"], self.武器["级别限制"],self.武器["名称"])
                local m = __主控["取武器附加名称"](__主控, self.武器["子类"], self.武器["级别限制"],self.武器["名称"]) or self.武器
                lssj = 取模型(m .. "_" .. self.模型)
                self.arms["静立"] = __res:取动画2(__res:取地址("shape/mx/", lssj[1])):置循环(true)
                self.arms["行走"] = __res:取动画2(__res:取地址("shape/mx/", lssj[2])):置循环(true)
            end
            显示染色=false
        else
            
            if self.坐骑 and self.坐骑["模型"] and not 一体坐骑表[self.坐骑.模型] and 可骑乘坐骑(self.模型,self.坐骑["模型"]) then--not __未加载坐骑[self.坐骑["模型"]] then
                lssj = 坐骑库(self.模型, self.坐骑["模型"], self.坐骑["饰品"] or "空",self.坐骑["饰品2"] or "空")
                self.Mount["静立"] = __res:取动画2(__res:取地址("shape/mx/", lssj["坐骑站立"])):置循环(true)
                self.Mount["行走"] = __res:取动画2(__res:取地址("shape/mx/", lssj["坐骑行走"])):置循环(true)
                -- self.Mount["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj["坐骑站立"]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj["坐骑站立"])), true)
                -- self.Mount["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj["坐骑行走"]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj["坐骑行走"])), true)
                if lssj["坐骑饰品站立"] and lssj["坐骑饰品行走"] then
                    self.MountAcc["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/",lssj["坐骑饰品站立"]))["置循环"](__res["取动画2"](__res,__res["取地址"](__res, "shape/mx/", lssj["坐骑饰品站立"])), true)
                    self.MountAcc["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/",lssj["坐骑饰品行走"]))["置循环"](__res["取动画2"](__res,__res["取地址"](__res, "shape/mx/", lssj["坐骑饰品行走"])), true)
                end
                self.actions["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj["人物站立"]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj["人物站立"])), true)
                self.actions["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj["人物行走"]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj["人物行走"])), true)
                local zqx, zqy = 坐骑补差(self.坐骑["模型"], self.模型)
                self.actions["静立"].pyx, self.actions["静立"].pyy = zqx, zqy
                self.actions["行走"].pyx, self.actions["行走"].pyy = zqx, zqy
            elseif self.武器 and self.武器["子类"] then
                local m = __主控["取武器子类"](__主控, self.武器["子类"])
                lssj = 取模型(self.模型, m)
                self.actions["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
                self.actions["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), true)
                m = __主控["取武器附加名称"](__主控, self.武器["子类"], self.武器["级别限制"],self.武器["名称"]) or self.武器
                lssj = 取模型(m .. "_" .. self.模型)
                self.arms["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
                self.arms["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), true)
                if self.副武器 and self.副武器["子类"] then
                    m = __主控["取武器附加名称"](__主控, self.副武器["子类"], self.副武器["级别限制"],self.副武器["名称"]) or self.副武器
                    lssj = 取模型(m .. "_" .. self.模型)
                    self.Secondary["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
                    self.Secondary["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), true)
                end
            else
                lssj = 取模型(self.模型)
                if self.副武器 and self.副武器["子类"] then
                    local m = __主控["取武器子类"](__主控, self.副武器["子类"])
                    lssj = 取模型(self.模型, m)
                end
                self.actions["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
                self.actions["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), true)
                lssj = 取模型(self.模型 .. "_身体")
                if lssj[2] and lssj[1] then
                    self.body["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
                    self.body["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), true)
                end
                if self.饰品 then
                    lssj = 取模型(self.模型 .. "_饰品")
                    if lssj[2] and lssj[1] then
                        self.arms["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])),true)
                        self.arms["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])),true)
                    end
                else
                    lssj = 取模型(self.模型 .. "_装饰")
                    if lssj[2] and lssj[1] then
                        self.arms["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])),true)
                        self.arms["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])),true)
                    end
                end
            end
            if 显示染色 then
             --   if self.炫彩 and self.炫彩组 then
                 --   self.置炫彩调色(self)
                if self.染色方案 then
                    self.置调色(self)
                end
            end
            if self.坐骑 and self.坐骑["炫彩"] then
                self.置坐骑炫彩调色(self)
            end
        end
    else
        lssj = 取模型(self.变身数据)
        self.actions["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
        self.actions["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), true)
        lssj = 取模型(self.变身数据 .. "_身体")
        if lssj[2] and lssj[1] then
            self.body["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
            self.body["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), true)
        end
        lssj = 取模型(self.变身数据 .. "_装饰")
        if lssj[2] and lssj[1] then
            self.arms["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
            self.arms["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), true)
        end
    end
    self:置动作(self.动作)
    self:置方向(self.方向)
end

function 动作:yitizuoqi()
    self.actions["静立"] = __res:取动画2(__res:取地址("shape/zuoqi/", 一体坐骑表[self.坐骑.模型][self.模型].站)):置循环(true)
    self.actions["行走"] = __res:取动画2(__res:取地址("shape/zuoqi/", 一体坐骑表[self.坐骑.模型][self.模型].跑)):置循环(true)
    self:置动作(self.动作)
    self:置方向(self.方向)
end

function 动作:置wpal()
    if self.wpal then
        return
    end
    if self.炫彩 then
        self.wpal = __dewpal(self.炫彩)
    elseif self.染色方案 then
        self.wpal = __dewpal(self.染色方案)
        if self.染色组 then
            for i = 1, 3 do
                if not self.染色组[i] then
                    self.染色组[i] = 0
                else
                    self.染色组[i] = self.染色组[i] + 1
                end
            end
        else
            self.染色方案 = nil
        end
    end
end

function 动作:置光环()
    if self.锦衣 and self.锦衣[3] then
        local lssj = 取光环(self.锦衣[3]["名称"] or self.锦衣[3])
        self.halo["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/sys/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/sys/", lssj[1])), true)
        self.halo["行走"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/sys/", lssj[2]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/sys/", lssj[2])), true)
        self.guanghuan=true
    end
end

function 动作:置足迹()
    if self.锦衣 and self.锦衣[4] then
        local lssj = 取足迹(self.锦衣[4]["名称"] or self.锦衣[4])
         self.footprint["静立"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/sys/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/sys/", lssj[1])), true)
         self.footprint["行走"] = self.footprint["静立"]
    end
end
function 动作:置调色()
    self.置wpal(self)
    if self.变身数据 or self.锦衣 and self.锦衣[1] then
        return
    end
    local dz = { "静立", "行走" }
    for i, v in ipairs(dz) do
        if self.actions[v] then
            self.actions[v]["调色"](self.actions[v], self.wpal,
                tonumber("0x0" .. self.染色组[1] .. "0" .. self.染色组[2] .. "0" .. self.染色组[3] .. "00"))
        end
        if self.body[v] then
            self.body[v]["调色"](self.body[v], self.wpal,
                tonumber("0x0" .. self.染色组[1] .. "0" .. self.染色组[2] .. "0" .. self.染色组[3] .. "00"))
        end
    end
end

function 动作:置炫彩调色()
    self.置wpal(self)
    if self.变身数据 or self.锦衣 and self.锦衣[1] then
        return
    end
    local dz = { "静立", "行走" }
    local rsid = self.炫彩组[1][1].R ..
        self.炫彩组[1][1].G ..
        self.炫彩组[1][1].B ..
        self.炫彩组[1][2].R ..
        self.炫彩组[1][2].G .. self.炫彩组[1][2].B .. self.炫彩组[1][3].R ..
        self.炫彩组[1][3].G .. self.炫彩组[1][3].B
    local rsid = tonumber(self.炫彩组[1][1].R .. self.炫彩组[1][1].G .. self.炫彩组[1][1].B) +
        tonumber(self.炫彩组[1][2].R .. self.炫彩组[1][2].G .. self.炫彩组[1][2].B) +
        tonumber(self.炫彩组[1][3].R .. self.炫彩组[1][3].G .. self.炫彩组[1][3].B)
    --for i, v in ipairs(dz) do
        --if self.actions[v] then
            --self.actions[v]["炫彩调色"](self.actions[v], self.wpal, 16843008, self.炫彩组, rsid)
      --  end
       -- if self.body[v] then
         --   self.body[v]["炫彩调色"](self.body[v], self.wpal, 16843008, self.炫彩组, rsid)
       -- end
   -- end
end

function 动作:置坐骑炫彩调色()
    self.置wpal(self)
    if self.变身数据 or self.锦衣 and self.锦衣[1] then
        return
    end
    local dz = { "静立", "行走" }
    local wpal = __dewpal(self.染色方案)
    local rsid = tonumber(self.坐骑["炫彩组"][1][1].R .. self.坐骑["炫彩组"][1][1].G .. self.坐骑["炫彩组"
        ][1][1].B) + tonumber(self.坐骑["炫彩组"][1][2].R .. self.坐骑["炫彩组"][1][2].G ..
        self.坐骑["炫彩组"][1][2].B) +
        tonumber(self.坐骑["炫彩组"][1][3].R .. self.坐骑["炫彩组"][1][3].G .. self.坐骑["炫彩组"][1][3].B)
    for i, v in ipairs(dz) do
        if self.Mount[v] then
            self.Mount[v]["炫彩调色"](self.Mount[v], wpal, 16843008, self.坐骑["炫彩组"], rsid)
        end
    end
end

function 动作:置动作(v)
    self.cur_action = {}
    self.动作 = v
    local 加入数据 = ggf.insert(self.cur_action)
    if self.halo[v] then
        加入数据(self.halo[v])
    end
    if self.footprint[v] then
        加入数据(self.footprint[v])
    end
    if self.wing[v] then
        加入数据(self.wing[v])
    end
    if self.Mount[v] then
        加入数据(self.Mount[v])
    end
    if self.MountAcc[v] then
        加入数据(self.MountAcc[v])
    end
    if self.actions[v] then
        加入数据(self.actions[v])
    end
    if self.body[v] then
        加入数据(self.body[v])
    end
    if self.arms[v] then
        加入数据(self.arms[v])
    end
    if self.Secondary[v] then
        加入数据(self.Secondary[v])
    end
end
local 置当前 = function(self, k, ...)
    if self.cur_action then
        self.cur_action[k](self.cur_action, ...)
    end
end
local 置所有 = function(self, k, ...)
    for _, v in pairs(self.halo) do
        v[k](v, ...)
    end
    for _, v in pairs(self.footprint) do
        v[k](v, ...)
    end
    for _, v in pairs(self.wing) do
        v[k](v, ...)
    end
    for _, v in pairs(self.Mount) do
        v[k](v, ...)
    end
    for _, v in pairs(self.MountAcc) do
        v[k](v, ...)
    end
    for _, v in pairs(self.actions) do
        v[k](v, ...)
    end
    for _, v in pairs(self.body) do
        v[k](v, ...)
    end
    for _, v in pairs(self.arms) do
        v[k](v, ...)
    end
    for _, v in pairs(self.Secondary) do
        v[k](v, ...)
    end
    return self
end
function 动作:置方向(v)
    self.方向 = v
    置所有(self, "置方向", v)
end

function 动作:取方向()
    return self.方向
end

function 动作:取高亮()
    return self.cur_action and self.cur_action["取高亮"](self.cur_action)
end

function 动作:置高亮(...)
    置所有(self, "置高亮", ...)
    return self
end

function 动作:置颜色(...)
    置所有(self, "置颜色", ...)
    return self
end

function 动作:帧同步()
    置当前(self, "置首帧")
    置当前(self, "播放")
    return self
end

function 动作:播放()
    置当前(self, "播放")
    return self
end

function 动作:置首帧()
    置当前(self, "置首帧")
    return self
end

function 动作:置尾帧()
    置当前(self, "置尾帧")
    return self
end

function 动作:置循环(...)
    置当前(self, "置循环", ...)
    return self
end
function 动作:清空资源()
    self.cur_action = {}
    self.actions = {}
    self.body = {}
    self.arms = {}
    self.Mount = {}
    self.MountAcc = {}
    self.halo = {}
    self.footprint = {}
    self.wing = {}
    self.Secondary = {}
    self.guanghuan=nil
end
function 动作:更新(dt,juli)
    if self.zhujue then --如果是主角自己走，可能有问题
        for _, v in ipairs(self.cur_action) do
            v["更新"](v, dt, x, y)
        end
    else
        if juli then 
            if not self.显示1 then
                --print(111111)
                self:置模型()
                self.显示1 = true
            end
            for _, v in ipairs(self.cur_action) do
                v["更新"](v, dt, x, y)
            end
        else
            if self.显示1 then
               --print(1111222)
                self.显示1 = false
                self:清空资源()
            end
        end
    end
end

function 动作:显示(xy,feixing)
    if self.guanghuan then
        for _, v in ipairs(self.cur_action) do
            v:显示(xy)
        end
        if not feixing then
            __主显["影子"]:显示(xy)
        end
    else
        if not feixing then
            __主显["影子"]:显示(xy)
        end
        -- if self.飞行中 then
        --     if self.飞行高度>-110 then
        --         self.飞行高度=self.飞行高度-2
        --     end
        -- else
        --     if self.飞行高度~=0 then
        --        self.飞行高度=self.飞行高度+2
        --     end
        --     if self.飞行高度>0 then
        --        self.飞行高度=0
        --     end
        -- end
        -- --table.print(xy)
        -- xy.y=xy.y+self.飞行高度
        for _, v in ipairs(self.cur_action) do
            v:显示(xy)
        end
    end
end

function 动作:显示影子(x, y)
    __主显["影子"]["显示"](__主显["影子"], x, y)
end

function 动作:检查点(x, y)
    for _, v in ipairs(self.cur_action) do
        if v["检查点"](v, x, y) then
            return true
        end
    end
end

function 动作:检查透明(x, y)
    local r = false
    for _, v in ipairs(self.cur_action) do
        r = r or v["检查透明"](v, x, y)
    end
    return r
end

return 动作
