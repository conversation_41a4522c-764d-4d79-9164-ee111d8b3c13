--[[
LastEditTime: 2024-10-23 18:17:44
--]]

local 坐骑技能 = 窗口层:创建窗口("坐骑技能", 0,0, 300, 350)

function 坐骑技能:初始化()
  self:创建纹理精灵(function()
      置窗口背景("坐骑技能", 0, 0, 300, 350, true):显示(0, 0)
        __res:取资源动画("dlzy",0x2BA5AE64,"图像"):置区域(0,0,280,2):显示(10,55)
        __res:取资源动画("dlzy",0x2BA5AE64,"图像"):置区域(0,0,280,2):显示(10,185)
        取白色背景(0, 0, 280, 140,true):显示(10,200)
        文本字体:置颜色(255,255,255,255)
        文本字体:取图像("可用技能点"):显示(15, 36)   
        取输入背景(0, 0, 100, 23):显示(95,32)
       

    end
  )
    self:置坐标(370, 30)
    self.可初始化=true

    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end
end



function 坐骑技能:更新(dt)
  if self.开始x and self.开始y then
        local xx,yy=窗口层.坐骑属性:取坐标()
        if self.开始x~=xx or self.开始y~=yy then
            if xx - self.宽度>=5 then
                  self:置坐标(xx - self.宽度, yy)
            else
                  self:置坐标(xx + 窗口层.坐骑属性.宽度, yy)
            end
            self.开始x,self.开始y=窗口层.坐骑属性:取坐标()
        end

  end
  if not 窗口层.坐骑属性.是否可见 and self.是否可见 then
      self:置可见(false)
  end
end

function 坐骑技能:显示(x, y)
  if self.图像 then
      self.图像:显示(x+100, y+35)
  end
end








function 坐骑技能:打开(坐骑,编号)
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.坐骑=table.copy(坐骑)
  self.编号=编号
  self:刷新()
  self.开始x,self.开始y=窗口层.坐骑属性:取坐标()
  if self.开始x - self.宽度>=5 then
        self:置坐标(self.开始x - self.宽度, self.开始y)
  else
        self:置坐标(self.开始x + 窗口层.坐骑属性.宽度, self.开始y)
  end
 
end

function 坐骑技能:刷新(坐骑,编号)
         if 坐骑 then
            self.坐骑=table.copy(坐骑)
         end
         if 编号 then
              self.编号=编号
         end
        self.介绍文本:清空()
        if self.坐骑  then
            self.技能网格:置技能(self.坐骑.技能)
            self.图像 = 文本字体:置颜色(0,0,0,255):取精灵(self.坐骑.技能点)
        end
end

function 坐骑技能:添加文本(技能,编号)
  self.介绍文本:清空()
  if self.坐骑 and 技能 then
      local 等级 = 0
      if self.坐骑.技能等级 and self.坐骑.技能等级[编号] then
          等级=self.坐骑.技能等级[编号]
      end
      self.介绍文本:置文本("#R"..技能.名称.."#L".." 等级: "..等级.."\\3#Y(右键升级)")
      self.介绍文本:置文本("#L".."实际效果(饱食度影响): "..self.坐骑.忠诚.."%")
      self.介绍文本:置文本("#L".."当前等级: ".."#H"..技能.介绍)
  end
end

local 技能网格 = 坐骑技能:创建网格("技能网格", 25, 80, 250, 110)
function 技能网格:初始化()
    self:创建格子(40, 40, 19, 30, 2, 4)
end

function 技能网格:置技能(数据)
  for i, v in ipairs(self.子控件) do
      local lssj =  __技能格子:创建()
      lssj:置数据(数据[i],true)
 
      v:置精灵(lssj)
  end
end

function 技能网格:获得鼠标(x,y,a)
  if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
      self.子控件[self.焦点]._spr.焦点=nil
  end
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
          self.焦点=a
          self.子控件[a]._spr.焦点=true
          坐骑技能:添加文本(self.子控件[a]._spr.数据,a)
    end
end

function 技能网格:失去鼠标(x,y)
  for i, v in ipairs(self.子控件) do
      if v._spr.焦点 then
          v._spr.焦点=nil
      end
  end
  self.焦点=nil
end

function 技能网格:右键弹起(x, y,a)
      if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
            if 坐骑技能.坐骑 and  坐骑技能.坐骑.技能点 > 0 then
                  请求服务(98,{编号=坐骑技能.编号,技能编号=a,名称=self.子控件[a]._spr.数据.名称})
            else
                __UI弹出.提示框:打开("#Y你没有足够的技能点！")
            end
      end
end

function 技能网格:左键弹起(x, y,a)
       if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
            if __手机 then
                local 事件 =function (编号)
                    if 编号==1 then
                        if 坐骑技能.坐骑 and  坐骑技能.坐骑.技能点 > 0 then
                              请求服务(98,{编号=坐骑技能.编号,技能编号=a,名称=self.子控件[a]._spr.数据.名称})
                        else
                            __UI弹出.提示框:打开("#Y你没有足够的技能点！")
                        end
                    end
                end
              __UI弹出.临时按钮:打开({"升级"},事件,x,y)
            end
            坐骑技能:添加文本(self.子控件[a]._spr.数据,a)
      end
end


			
		

local 介绍文本 = 坐骑技能:创建文本("介绍文本", 15, 205, 270,130)


local 关闭 = 坐骑技能:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
      坐骑技能:置可见(false)
    
end