--[[
Author: GGELUA
Date: 2024-03-31 01:31:44
Last Modified by: GGELUA
Last Modified time: 2024-04-01 13:52:37
--]]


local 动作 = require('对象/基类/假人动作')
local 控制 = require('对象/基类/控制')
local 状态 = require('对象/基类/状态')
local NPC = class('NPC', 动作, 控制, 状态)
local SDL = require 'SDL'
function NPC:初始化(t)
    self.id = t.id  
    self.编号 = t.编号
    self.类型 = true
    self.执行事件 = t.执行事件
    self.事件 = t.事件
    self.行走开关 = t.行走开关
  
    if self.事件 == "明雷" or self.行走开关 then
        self.行走时间 = 20+math.random(-10,10)
        local xy
        if t.X and t.Y then
            xy = require("GGE.坐标")(t.X * 20, t.Y * 20):floor()
        elseif t.x and t.y then
            xy = require("GGE.坐标")(t.x * 20, t.y * 20):floor()
        end
        self.矩形 = require('SDL.矩形')(xy.x, xy.y, 60, 50):置中心(30, 40)
        self.已发送 = true

    end



end

function NPC:更新(dt)
    self[动作]:更新(dt)
    self[控制]:更新(dt)
    self[状态]:更新(dt)
   -- 


   if self.xy:取距离(__主显.主角.xy) < 800  then
		if self.行走时间 ~= nil then
			self.行走时间 = self.行走时间 - 1
            self.矩形 = require('SDL.矩形')(self.xy.x, self.xy.y,  60, 50):置中心(30, 40)
			if self.行走时间 <= 0 then
				local 临时目标 = require('GGE.坐标')(self.xy.x/20 + math.random(-6,6) ,self.xy.y/20 + math.random(-6,6)):floor()
                self:设置路径(临时目标)
				self.行走时间 = 250+math.random(-20,10)
                if self.事件 == "明雷"  then
                    local xy
                    if 临时目标.x and 临时目标.y then
                        xy = require("GGE.坐标")(临时目标.x * 20, 临时目标.y * 20):floor()
                        self.矩形 = require('SDL.矩形')(xy.x, xy.y,  60, 50):置中心(30, 40)
                    end
                end
			end
        end
	end
    if self.事件 == "明雷" then
        if self.矩形:检查点(__主显.主角.xy) then
            if not self.已发送 and (not 角色信息.队伍 or 角色信息.队伍==0 or (角色信息.队伍~=0 and __主显.主角.是否队长)) then
                __主显.主角:停止移动()
                self.已发送 = true
                请求服务(1004, { 地图 = 角色信息.地图数据.编号, 编号 = self.编号, 标识 = self.id })
                
            end
        
        elseif self.已发送 then
            self.已发送 = false
        end
    end
end

function NPC:显示(pys)
    local p = self.xy + pys
    self[状态]:显示底层(p)
    self[动作]:显示(p)
    self[状态]:显示(p)
    self[状态]:显示顶层(p)
end

function NPC:设置路径(数据)
    if not _tp.战斗中 then
        local  xy =require('GGE.坐标')(0,0)
        if 数据.坐标x and 数据.坐标y then
            xy = require('GGE.坐标')(数据.坐标x * 20, 数据.坐标y * 20)
        elseif 数据.x and 数据.y then
            xy = require('GGE.坐标')(数据.x * 20, 数据.y * 20)
        end
        if xy.x and xy.y then
            local route = __主显.地图:寻路(self.xy, xy)
            if #route > 0 then
                self:路径移动(route)
            end
        end
    else
        if 数据.坐标x and 数据.坐标y then
            self.xy:pack(数据.坐标x * 20, 数据.坐标y * 20)
        elseif 数据.x and 数据.y then
            self.xy:pack(数据.x * 20, 数据.y * 20)
        end
    end
end



function NPC:对话事件(名称,地图,编号,标识)
    if __主显 and __主显.地图id >100000 then
        if 名称 == "管家" then
            窗口层.对话栏:打开("男人_店小二","管家","主人，有什么可以为你效劳的。",{"使用厨房","使用丹房","使用卧室","我还要逛逛"})
        elseif 名称 == "佣人" then
              窗口层.对话栏:打开("女人_丫鬟","佣人","主人，有什么可以为你效劳的。",{"侍奉睡觉","打开仓库","养儿育女","我还要逛逛"})
        elseif 名称 == "牧场看守" then
              窗口层.对话栏:打开("男人_兰虎","牧场看守","主人，有什么可以为你效劳的。",{"打开牧场界面","我还要逛逛"})
        elseif 名称 == "牧场管理员" then
              窗口层.对话栏:打开("男人_兰虎","牧场管理员","主人，有什么可以为你效劳的。",{"进入牧场","我还要逛逛"})
        end
    elseif self.执行事件 or self.事件=="单位" then
        请求服务(1501, {地图 = 地图, 编号 = 编号, 标识 = 标识})

    end
end


function NPC:查找重叠(x,y)
    local 重叠目标={[1]={编号=self.编号,序列=self.编号,标识=self.id,名称=self.名称,类型=ggetype(self)}}
    for k, n in pairs(__主显.场景人物) do
        if n~=self and n.检查透明 and n:检查透明(x,y) then
            if ggetype(n)=="NPC" then
                table.insert(重叠目标,{编号=n.编号,序列=n.编号,标识=n.id,名称=n.名称,类型=ggetype(n)})
            elseif ggetype(n)=="玩家" then
                table.insert(重叠目标,{编号=n.门派,序列=n.模型,标识=n.玩家ID,名称=n.名称,类型=ggetype(n)})
            end
        end
    end

    if #重叠目标>1 then
        local 列表={}
        for z, j in ipairs(重叠目标) do
            列表[z] = j.名称
        end
        local 事件 =function (a)
            if 重叠目标[a] then
              if 重叠目标[a].类型=="NPC" then
                    if __主显 and __主显.地图id >100000 then
                          if 重叠目标[a].名称 == "管家" then
                              窗口层.对话栏:打开("男人_店小二","管家","主人，有什么可以为你效劳的。",{"使用厨房","使用丹房","使用卧室","我还要逛逛"})
                          elseif 重叠目标[a].名称 == "佣人" then
                                窗口层.对话栏:打开("女人_丫鬟","佣人","主人，有什么可以为你效劳的。",{"侍奉睡觉","打开仓库","养儿育女","我还要逛逛"})
                          elseif 重叠目标[a].名称 == "牧场看守" then
                                窗口层.对话栏:打开("男人_兰虎","牧场看守","主人，有什么可以为你效劳的。",{"打开牧场界面","我还要逛逛"})
                          elseif 重叠目标[a].名称 == "牧场管理员" then
                                窗口层.对话栏:打开("男人_兰虎","牧场管理员","主人，有什么可以为你效劳的。",{"进入牧场","我还要逛逛"})
                          end
                    else
                          请求服务(1501, { 地图 = 角色信息.地图数据.编号, 编号 = 重叠目标[a].编号,标识 = 重叠目标[a].标识 })
                    end
              elseif 重叠目标[a].类型=="玩家" then
                  __UI弹出.玩家信息:打开({模型 = 重叠目标[a].序列,[1] = 重叠目标[a].名称,[2]= 重叠目标[a].标识,[3]=重叠目标[a].编号 or "无门派"})
              end
            end
        end
        __UI弹出.弹出列表:打开(列表,nil,事件,x+30, y+25,130)
        return true
    end

end


function NPC:消息事件(t)
    self[状态]:消息事件(t)
    self[动作]:消息事件(t)
    if not t.鼠标 then return end
    for _, v in ipairs(t.鼠标) do
        if v.type == SDL.MOUSE_UP and self:检查透明(v.x,v.y) and not _tp.战斗中  then
            v.type = nil
            if  v.button == SDL.BUTTON_LEFT then
                if 界面层.类型 then
                    if 界面层.类型 == '攻击' then
                        self:对话事件(self.名称,角色信息.地图数据.编号,self.编号,self.id)
                    end
                    界面层:重置()
                    鼠标层:正常形状()
                else
                    if __手机 and self:查找重叠(v.x,v.y) then
                    else
                        self:对话事件(self.名称,角色信息.地图数据.编号,self.编号,self.id)
                    end  
                end
            elseif  v.button == SDL.BUTTON_RIGHT then 
                       self:查找重叠(v.x,v.y)
            end
        end
    end
end

return NPC





-- local 主显层 = class("主显层")




-- function 主显层:初始化()
--     self.屏幕坐标 = 生成XY()
--     self.地图id = 0
--     self.玩家 = {}
--     self.假人 = {}
--     self.场景人物 = setmetatable({}, { __mode = "v" })

-- end

-- function 主显层:加载(id)
--     self.地图id =id
--     if not self.地图 then
--       self.地图=require("地图/地图")(取地图id(self.地图id))
--     else
--       self.地图:加载(取地图id(self.地图id))
--     end
--     _tp:播放音乐(取地图id(self.地图id))
--     if not self.主角 then
--         self.主角 = require("主角")(角色信息)
--     end
--     self.屏幕坐标 = 角色信息.坐标:取地图偏移(self.地图.宽度,self.地图.高度)
--     self.玩家 = {}
--     self.场景人物 = setmetatable({}, { __mode = "v" })
--     table.insert(self.场景人物, self.主角)
--     local 地图等级 = {}
--     地图等级[1], 地图等级[2] = 取场景等级(角色信息.地图数据.编号)
--     if 地图等级[1] and 地图等级[2] then
--         self.场景最低等级 = 地图等级[1]
--         self.场景最高等级 = 地图等级[2]
--         __UI弹出.提示框:打开("#Y本场景等级为" ..地图等级[1] .. "-" .. 地图等级[2] .. "级", "xt")
--     else
--         self.场景最低等级 = nil
--         self.场景最高等级 = nil
--     end
--     test1()
-- end


-- function 主显层:添加玩家(内容)
--       if not 内容 or not 内容.id then return end
--       self.玩家[内容.id]= __玩家类.创建(内容)
--       table.insert(self.场景人物, self.玩家[内容.id])
-- end


-- function 主显层:删除玩家(角色ID)
--         if not 角色ID then return end
--         self.玩家[角色ID]=nil
--         for i=#self.场景人物,1,-1 do
--             if self.场景人物[i] and ggetype(self.场景人物[i])=="玩家" and  self.场景人物[i].玩家ID == 角色ID then
--               table.remove(self.场景人物, i)
--               break
--             end
--         end
-- end


--  function 主显层:设置传送(内容)
--         if not 内容 then return end
--         for k,v in pairs(内容) do
--             if not v.编号 then
--                 v.编号 = k
--             end
--           self.地图:添加传送圈(v)
--         end
--         if self.地图id > 100000 then
--           self:加载房屋特效()
--         end
--         self:加载场景特效()
--         if not __传送数据[self.地图id] then
--             __传送数据[self.地图id] =内容
--         end
-- end





-- function 主显层:清除场景传送()
--           self.地图.传送圈={}
-- end


-- function 主显层:设置假人(内容)
--         if not 内容 then return end
--         self.假人 = {}
--         for i, v in pairs(内容) do
--             if not v.编号 then
--                 v.编号 = i
--             end
--             self.假人[v.编号] = __假人类.创建(v)
--             table.insert(self.场景人物, self.假人[v.编号])
--         end
--         self:更新假人头顶图标()
--         if not __NPC列表[self.地图id] then
--             __NPC列表[self.地图id] =内容
--         end
-- end

-- function 主显层:添加单位(内容)
--         if 内容 and 内容.id and 内容.编号 then
--             self.假人[内容.编号] = __假人类.创建(内容)
--             table.insert(self.场景人物, self.假人[内容.编号])
--         end
-- end


-- function 主显层:删除单位(编号,序列)
--           if self.假人[编号] and self.假人[编号].id == 序列 then
--               self.假人[编号] =nil
--               for i=#self.场景人物,1,-1 do
--                   if self.场景人物[i].编号 == 编号 and self.场景人物[i].id == 序列 then
--                     table.remove(self.场景人物, i)
--                     break
--                   end
--               end
--           end
-- end

-- function 主显层:更改单位(数据)
--         if self.假人[数据.编号] and self.假人[数据.编号].id == 序列 then
--             for i=#self.场景人物,1,-1 do
--                 if self.场景人物[i].编号 == 数据.编号 and self.场景人物[i].名称 == 数据.名称 then
--                     table.remove(self.场景人物, i)
--                     break
--                 end
--             end
--             if 数据.变异 and 数据.变异 and __染色信息[数据.模型] then
--                 数据.染色方案 = __染色信息[数据.模型].id
--                 数据.染色组 = __染色信息[数据.模型].方案
--             end
--             self.假人[数据.编号] = __假人类.创建(数据)
--             table.insert(self.场景人物, self.假人[数据.编号])
--         end
-- end



-- function 主显层:加载场景特效()
--         local txb = 取传特效表(self.地图id)
--         if self.地图id > 100000 then
--             if _tp.房屋数据.庭院ID == self.地图id then
--                   txb = 取传特效表(_tp.房屋数据.庭院地图)
--             elseif _tp.房屋数据.房屋ID == self.地图id then
--                   txb = 取传特效表(_tp.房屋数据.房屋地图)
--             elseif _tp.房屋数据.阁楼ID == self.地图id then
--                 txb = 取传特效表(_tp.房屋数据.阁楼地图)
--             elseif _tp.房屋数据.牧场ID == self.地图id then
--                 txb = 取传特效表(_tp.房屋数据.牧场地图)
--             end
--         end
--         if txb then
--             if self.地图id > 100000 or __res.配置.地图特效==1  then
--                 for i, v in pairs(txb) do
--                     v.id = i
--                     self.地图:添加特效(v)
--                 end
--             end
--         end
        
        
-- end

-- function 主显层:清除场景特效()
--          if self.地图id > 100000 then return end 
--           self.地图.特效={}
-- end

-- function 主显层:加载房屋特效()
--       if self.地图id > 100000 then
--           local jj
--           if _tp.房屋数据.庭院ID == self.地图id then
--               jj =_tp.房屋数据.庭院装饰
--           elseif _tp.房屋数据.房屋ID == self.地图id then
--               jj = _tp.房屋数据.室内装饰
--           elseif _tp.房屋数据.阁楼ID == self.地图id then
--               jj = _tp.房屋数据.阁楼装饰
--           elseif _tp.房屋数据.牧场ID == self.地图id then
--               jj = _tp.房屋数据.牧场装饰
--           end
--           if jj then
--               for i, v in pairs(jj) do
--                   if not v.编号 then
--                      v.编号 = i
--                   end
--                   local zx = 取房屋特效(v.名称..v.方向)
--                   if zx.切换 and zx.资源 then
--                       v.切换=zx.切换 
--                       v.资源=zx.资源
--                       self.地图:添加家具(v)
--                   end
--               end
--           end
--       end
-- end

-- function 主显层:家具旋转特效(数据)
--        self.地图:删除家具(数据.编号)
--         local zx = 取房屋特效(数据.名称..数据.方向)
--         if zx.切换 and zx.资源 then
--             数据.切换=zx.切换 
--             数据.资源=zx.资源
--             self.地图:添加家具(数据)
--         end
-- end



-- function 主显层:清除房屋特效()
--         self.地图.家具={}
      
-- end




-- function 主显层:房屋特效(数据)
--       if self.地图id > 100000 and 数据 then
--           if _tp.房屋数据.庭院ID == self.地图id then
--               if not 数据.编号 then
--                 数据.编号=#_tp.房屋数据.庭院装饰+1
--               end
--               _tp.房屋数据.庭院装饰[数据.编号] = 数据
--           elseif _tp.房屋数据.房屋ID == self.地图id then
--                   if not 数据.编号 then
--                       数据.编号=#_tp.房屋数据.室内装饰+1
--                   end
--                   _tp.房屋数据.室内装饰[数据.编号] = 数据
--           elseif _tp.房屋数据.阁楼ID == self.地图id then
--               if not 数据.编号 then
--                   数据.编号=#_tp.房屋数据.阁楼装饰+1
--               end
--               _tp.房屋数据.阁楼装饰[数据.编号] = 数据
--           elseif _tp.房屋数据.牧场ID == self.地图id then
--                   if not 数据.编号 then
--                       数据.编号=#_tp.房屋数据.牧场装饰+1
--                   end
--                   _tp.房屋数据.牧场装饰[数据.编号] = 数据
--           end
--           if 数据.编号 then
--                 local xxxj =__家具格子.创建() 
--                 xxxj:置数据(数据.名称,数据.方向,数据.编号)
--                 __UI界面.鼠标层.附加=xxxj
--           end
--       end
-- end







-- function 主显层:更新(dt, x, y)
--     if not _tp.战斗中 then
--         self.地图:更新(dt,self.主角.xy:取地图位置(self.地图.块宽度, self.地图.块高度))
--     else
--         __战斗主控:更新( dt * 1.2, x, y)
--     end
-- end


-- function 主显层:消息事件(msg)
--           self.地图:消息事件(msg)
--           for k, v in pairs(self.场景人物) do
--               if v.消息事件 then
--                   v:消息事件(msg)
--               end
--           end
-- end


-- function 主显层:显示(x, y)
--     self.地图:显示(self.屏幕坐标)
--     if _tp.战斗中 then
--         __战斗主控:显示(x, y)
--     end

-- end


-- function 主显层:更新假人头顶图标()
--     for k, v in pairs(self.场景人物) do
--         if v.名称 == "商人的鬼魂" or v.名称 == "白鹿精"
--             or v.名称 == "酒肉和尚" or v.名称 == "执法天兵"
--             or v.名称 == "白琉璃"or v.名称 == "幽冥鬼"
--             or v.名称 == "刘洪" or v.名称 == "野猪" or v.名称 == "蟹将军" then
--                 v:置任战(true)
--         elseif   v.名称 == "新手接待师"  or v.名称 == "杜少海" or v.名称 == "皇宫护卫" or v.名称 == "钟馗"  or v.名称 == "赵捕头" then
--             v:置任务(true)
-- 		    end
--     end
-- end

-- return 主显层


