

local 状态图标 = 界面层:创建控件("状态图标", 0,0, 120,60)
 
function 状态图标:初始化()
    self:置坐标(引擎.宽度-120, 55)
    self.可初始化=true
end


local 状态网格 = 状态图标:创建网格("状态网格", 0, 0, 120, 60)
function 状态网格:初始化()
    self:创建格子(26, 26, 2, 2, 2, 4)
end


 function 状态网格:左键弹起(x, y, a)
     if self.子控件[a].数据 then
         __UI弹出.状态提示:打开(self.子控件[a].数据,x,y)
         if __主显 and __主显.主角 then
            __主显.主角.按下=false
            __主显.主角.点击移动=nil
         end
     end
 end
 function 状态网格:获得鼠标(x, y,a)
    if self.子控件[a].数据 then
        __UI弹出.状态提示:打开(self.子控件[a].数据,x,y)
    end
end
 
 function 状态网格:置数据(data)
     for i = 1, #self.子控件 do
         if data[i] then
             self.子控件[i]:创建纹理精灵(function()
              local lssj = __取界面小图标外框(data[i].外框)
              __res:取资源动画(lssj[3],lssj[1],"图像"):显示(0,0)
              lssj = __取界面小图标(data[i].图标)
              __res:取资源动画(lssj[3],lssj[1],"图像"):显示(4,4)
             end
           )
             self.子控件[i].数据= {名称=data[i].名称,介绍=data[i].介绍}
         else
             self.子控件[i]:置精灵()
             self.子控件[i].数据=nil
         end
     end
 end
