 
__UI弹出.频道表情 = 界面:创建弹出控件("频道表情",0, 0, 640, 500)

local 频道表情 =__UI弹出.频道表情 --聊天控件:创建控件("频道表情", 0, 0, 640, 500)
function 频道表情:初始化()
    self:置精灵(require('SDL.精灵')(0, 0, 0, 640, 500):置颜色(0, 0, 0, 200), true)

end

function 频道表情:打开(插入控件)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        self.插入控件=nil
        return
    end
    if 插入控件 then
        self.插入控件=插入控件
        self:置坐标((引擎.宽度 - self.宽度) // 2,(引擎.高度 - self.高度) // 2 )
    else
        self:置坐标((引擎.宽度 - self.宽度) // 2, 引擎.高度-530)
    end
end

local 表情网格 = 频道表情:创建网格("表情网格", 10, 10, 620, 480)
function 表情网格:初始化()
  self:创建格子(45, 45, 3, 3, 10, 13, true)
  for i = 1, #self.子控件 do
    if _tp.表情[i] then
        local sssj = _tp.表情[i]:复制()
        sssj:置中心(-13,-13-_tp.表情[i].高度)
        if i==3 then
            sssj:置中心(-11,-11-_tp.表情[i].高度)
        elseif i==4 then
            sssj:置中心(-13,-7-_tp.表情[i].高度)
        elseif i==5 then
            sssj:置中心(-12,-12-_tp.表情[i].高度)
        elseif i==6 then
            sssj:置中心(-13,-17-_tp.表情[i].高度)
        elseif i==8 then
            sssj:置中心(-8,-9-_tp.表情[i].高度)
        elseif i==9 then
            sssj:置中心(-14,-15-_tp.表情[i].高度)
        elseif i==10 then
            sssj:置中心(-11,-4-_tp.表情[i].高度)
        elseif i==11 or i==21 or i==20 or i==24  then
            sssj:置中心(-10,-8-_tp.表情[i].高度)
        elseif i==12 then
            sssj:置中心(-10,-18-_tp.表情[i].高度)
        elseif i==13 or i==16  then
            sssj:置中心(-10,-13-_tp.表情[i].高度)
        elseif i==14 then
            sssj:置中心(-10,-1-_tp.表情[i].高度)
        elseif i==15 or i==17 then
            sssj:置中心(-10,-10-_tp.表情[i].高度)
        elseif i==19 or i==20  then
            sssj:置中心(-13,-8-_tp.表情[i].高度)
        elseif i==23  then
            sssj:置中心(-14,-8-_tp.表情[i].高度)
        elseif i==27  then
            sssj:置中心(-12,-16-_tp.表情[i].高度)
        elseif i==28  or i==29  then
            sssj:置中心(-14,-10-_tp.表情[i].高度)
        elseif i==30  then
            sssj:置中心(-11,-8-_tp.表情[i].高度)
        elseif i==31  then
            sssj:置中心(-10,-10-_tp.表情[i].高度)
        elseif i==32  then
            sssj:置中心(-6,-8-_tp.表情[i].高度)
        elseif i==33  then
            sssj:置中心(-14,-10-_tp.表情[i].高度)
        elseif i==34  then
            sssj:置中心(-8,17-_tp.表情[i].高度)
        elseif i==35 or i==46 or i==48  then
            sssj:置中心(-10,-5-_tp.表情[i].高度) 
        elseif i==36 then
            sssj:置中心(-14,-13-_tp.表情[i].高度)
        elseif i==37 then
            sssj:置中心(-8,-13-_tp.表情[i].高度)
        elseif i==38 or i==39  then
            sssj:置中心(-10,-8-_tp.表情[i].高度)
        elseif i==40 then
            sssj:置中心(-10,-14-_tp.表情[i].高度)
        elseif i==41 then
            sssj:置中心(-12,-14-_tp.表情[i].高度)
        elseif i==42 then
            sssj:置中心(-10,-18-_tp.表情[i].高度)
        elseif i==43  then
            sssj:置中心(-12,5-_tp.表情[i].高度)
         elseif i==47  then
           sssj:置中心(-10,-17-_tp.表情[i].高度)
        elseif i==56  then
            sssj:置中心(-10,-8-_tp.表情[i].高度)
        elseif i==57  then
            sssj:置中心(-10,-17-_tp.表情[i].高度)
        elseif i==58  then
            sssj:置中心(-10,-8-_tp.表情[i].高度)
        elseif i==59 or i==62 or i==63 or i==64 or i==65   then
            sssj:置中心(-10,-2-_tp.表情[i].高度)
        elseif i==67  then
            sssj:置中心(-7,2-_tp.表情[i].高度)
        elseif i==69  then
            sssj:置中心(-10,-2-_tp.表情[i].高度)
        elseif i==70  then
            sssj:置中心(-6,-10-_tp.表情[i].高度)
        elseif i==71 or i==72 or i==73 or i==74 or i==75 or i==76 then
            sssj:置中心(-7,-10-_tp.表情[i].高度)  
        elseif i==77 or i==78 then
            sssj:置中心(-5,-10-_tp.表情[i].高度)
        elseif i==79 then
            sssj:置中心(-8,-16-_tp.表情[i].高度)
        elseif i==80 or i==91 then
            sssj:置中心(-8,-10-_tp.表情[i].高度)
        elseif i==83 then
            sssj:置中心(-3,-16-_tp.表情[i].高度)
        elseif i==84 then
            sssj:置中心(-8,-10-_tp.表情[i].高度)
        elseif i==85 then
            sssj:置中心(-2,-5-_tp.表情[i].高度)
        elseif i==86 or i==87 then
            sssj:置中心(-8,-9-_tp.表情[i].高度)
        elseif i==88 then
            sssj:置中心(-8,-7-_tp.表情[i].高度)
        elseif i==90 then
            sssj:置中心(-1,-7-_tp.表情[i].高度)
        elseif i==92   then
            sssj:置中心(-4,-4-_tp.表情[i].高度)
        elseif i==94 or i==97 then
            sssj:置中心(-12,2-_tp.表情[i].高度)
        elseif i==98 then
            sssj:置中心(-10,-2-_tp.表情[i].高度)
        elseif i==99 then
            sssj:置中心(-8,-14-_tp.表情[i].高度)
        elseif i==100 then
            sssj:置中心(-6,-10-_tp.表情[i].高度)  
        elseif  i==101 or i==102 then
            sssj:置中心(-8,12-_tp.表情[i].高度)
        elseif  i==103 or i==104 then
            sssj:置中心(-8,-10-_tp.表情[i].高度)
        elseif  i==105 or i==106 or i==107 then
            sssj:置中心(-8,-5-_tp.表情[i].高度)
        elseif  i==109 or i==112 then
            sssj:置中心(-8,-5-_tp.表情[i].高度)
        elseif  i==113 then
            sssj:置中心(-2,-14-_tp.表情[i].高度)
        elseif  i==117 then
            sssj:置中心(-2,-10-_tp.表情[i].高度)
        elseif  i==118  then
            sssj:置中心(-2,-5-_tp.表情[i].高度)
        elseif   i==119 or i==120 then
            sssj:置中心(-8,-5-_tp.表情[i].高度)
        end    
        self.子控件[i]:置精灵(sssj)
    else
        self.子控件[i]:置精灵()
    end
  end
end

function 表情网格:获得鼠标(x, y, a)
      if self.子控件[a]._spr and _tp.表情[a] then
          __UI弹出.自定义:打开(x+20, y+20,文本字体:置颜色(255,255,255):取图像("快捷输入#".. a))
      end
end


function 表情网格:左键弹起(x, y, a)
      if self.子控件[a]._spr and _tp.表情[a] then
          if 频道表情.插入控件 then
                频道表情.插入控件:插入文本("#" .. a)
          elseif not __手机 then
                界面层.聊天控件.输入区域.内容输入:插入文本("#" .. a)
          end
      end
      频道表情:置可见(false)
end

