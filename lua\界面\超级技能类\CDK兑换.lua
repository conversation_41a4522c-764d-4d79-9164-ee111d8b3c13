local CDK兑换 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "CDK兑换", 258 + abbr.py.x, 60 + abbr.py.y, 367+10, 157+10+87)
function CDK兑换:初始化()
  local nsf = require("SDL.图像")(445, 410)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("CDK兑换", 0, 12, 367, 157+17+58-39, true):显示( 0, 0)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35), 72-24, 60+60-29)
    字体18:置颜色(__取颜色("白色"))
    字体18:取图像("兑换前请先预留足够的道具格子！"):显示(32,33+8+16)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function CDK兑换:打开()
  self.发送时间=os.time()
  self:置可见(true)
  self.shuru:置文本("")
end



local 关闭 = CDK兑换["创建我的按钮"](CDK兑换, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 403-75, 0)
function 关闭:左键弹起(x, y, msg)
  CDK兑换["置可见"](CDK兑换, false)
  CDK兑换["shuru"]["清空"](CDK兑换["shuru"])
end
local shuru = CDK兑换["创建我的输入"](CDK兑换, "shuru", 81-24, 67+60-28, 254, 24, nil, 88, "黑色", 字体20)

local 确定 = CDK兑换["创建我的按钮"](CDK兑换, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "确定", 153-26, 337-189-25+18, "确定兑换")
function 确定:左键弹起(x, y, msg)
  if CDK兑换.shuru:取文本()~= ""  then
    if os.time()-CDK兑换.发送时间>=3 then
      发送数据(6971,{码=CDK兑换.shuru:取文本()})
      CDK兑换.发送时间=os.time()
      CDK兑换.shuru:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end
