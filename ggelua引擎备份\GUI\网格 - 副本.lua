-- <AUTHOR> GGELUA
-- @Date                : 2022-03-07 18:52:00
-- @Last Modified by    : baidwwy
-- @Last Modified time  : 2022-08-23 12:09:36

local SDL = require 'SDL'
local GUI控件 = require('GUI.控件')

local GUI网格 = class('GUI网格', GUI控件)
local _滚动 = function(self)
  local py = self._py
  for i, v in ipairs(self.子控件) do
      local yy = py + v.y
      v.是否可见 = yy + v.高度 > 0 and yy < self.高度
      v:置中心(0, -py)
      -- for _, x in v:遍历控件() do
      --     x.是否可见=v.是否可见
      -- end
  end
end
local _刷新列表 = function(self)
  local hy, py = 0, self._py
  for i, v in ipairs(self.子控件) do
      hy = hy + v.高度 + self.行间距
  end
  hy = hy / self.列数量 - self.行间距
  if hy > self.高度 then
      self._max = hy - self.高度
  else
      self._max = 0
  end
end


function GUI网格:初始化()
    self._id = 0
    self._py = 0
    self.行间距 = 0
    self.列间距 = 0
    self.行数量 = 0
    self.列数量 = 0
    self.行高度 = 0
end

local _格子 = class('GUI格子', GUI控件) --继承一下，防止控件接收掉消息
function GUI网格:添加格子(x, y, w, h)
    self._id = self._id + 1
    local id = self._id

    local 格子 = _格子(id, x, y, w, h, self)
    self[id] = 格子
    格子._id = id
    if type(self.子初始化) == 'function' then
        格子.初始化 = function()
            self:子初始化(id)
        end
    end
    if type(self.子更新) == 'function' then
        格子.更新 = function(_, dt)
            self:子更新(dt, id)
        end
    end
    if type(self.子显示) == 'function' then
        格子.显示 = function(_, x, y)
            self:子显示(x, y, id)
        end
    end
    if type(self.子前显示) == 'function' then
        格子.前显示 = function(_, x, y)
            self:子前显示(x, y, id)
        end
    end
    格子:置可见(true)

    table.insert(self.子控件, 格子)
    return 格子
end

function GUI网格:删除格子(id)
    for _, v in self:遍历控件() do
        if v._id and (not id or v._id == id) then
            self:删除控件(v._id)
        end
    end
    if not id then
        self._id = 0
    end
end

function GUI网格:创建格子(宽度, 高度, 行间距, 列间距, 行数量, 列数量)
    self:删除格子()
    for h = 1, 行数量 do
        for l = 1, 列数量 do
            local r = self:添加格子((l - 1) * (宽度 + 列间距), (h - 1) * (高度 + 行间距), 宽度, 高度)
        end
    end
    self._py = 0
    self._max = 0
    self.行间距 = 行间距
    self.列间距 = 列间距
    self.行数量 = 行数量
    self.列数量 = 列数量
    self.行高度 = 高度 + 行间距
    _刷新列表(self)
    return self
end

function GUI网格:置格子检查区域(x, y, w, h)
    for i, v in ipairs(self.子控件) do
        v:置检查区域(x, y, w, h)
    end
    return self
end

function GUI网格:检查格子(x, y)
    if self:检查点(x, y) then
        for i, v in ipairs(self.子控件) do
            if v.是否可见 and v:检查点(x, y) then
                return i, v
            end
        end
    end
end

-- function GUI网格:绑定滑块(obj)
--     self.滑块 = obj
--     if obj then
--         local 置位置 = obj.置位置
--         obj.置位置 = function(this, v)
--             if self.高度 > self:取父控件().高度 then
--                 local max = self.高度 - self:取父控件().高度
--                 self:置中心(0, math.floor(max * (this.位置 / this.最大值)))
--                 置位置(this, v)
--             else
--                 置位置(this, 0)
--             end
--         end
--     end
--     return obj
-- end

function GUI网格:绑定滑块(obj)
  self.滑块 = obj
  if obj then
      local 置位置 = obj.置位置
      obj.置位置 = function(this, v)
          置位置(this, v)
          self._py = -math.floor(this.位置 / this.最大值 * self._max)
          if self._py == 0 then
              置位置(this, 0)
          end
          _滚动(self)
          return self._py ~= 0
      end
  end
  return obj
end


function GUI网格:_消息事件(msg)
    if not self.是否可见 then
        return
    end

    GUI控件._消息事件(self, msg)

    if not msg.鼠标 then
        return
    end

    if self:发送消息('鼠标消息') ~= false then
        for _, v in ipairs(msg.鼠标) do
            if v.type == SDL.MOUSE_DOWN then
         
                local a, b = self:检查格子(v.x, v.y)
                self._DOWN = { x = v.x, y = v.y, py = self._py }
                if a and not self.是否滑动 then
                    v.typed, v.type = v.type, nil
                    v.control = self
                    
                    if not self.是否禁止 then
                        local x, y, r = b:取坐标()
                        if v.button == SDL.BUTTON_LEFT then
                            self._ldown = a
                            r = self:发送消息('左键按下', x, y, a, b, msg)
                        elseif v.button == SDL.BUTTON_RIGHT then
                            self._rdown = a
                            r = self:发送消息('右键按下', x, y, a, b, msg)
                        end
                        if not msg.win and not r then
                            v.type = v.typed
                        end
                    end
                end
            elseif v.type == SDL.MOUSE_UP then
                self._DOWN = false
                local a, b = self:检查格子(v.x, v.y)
                if a then
                    v.typed, v.type = v.type, nil
                    v.control = self

                    if not self.是否禁止 and not  self.是否滑动 then
                        local x, y, r = b:取坐标()
                        if v.button == SDL.BUTTON_LEFT then
                            if self._ldown == a then
                                r = self:发送消息('左键弹起', x, y, a, b, msg)
                            end
                            if v.clicks == 2 then
                                r = r or self:发送消息('左键双击', x, y, a, b, msg)
                            end
                        elseif v.button == SDL.BUTTON_RIGHT then
                            if self._rdown == a then
                                r = self:发送消息('右键弹起', x, y, a, b, msg)
                            end
                            if v.clicks == 2 then
                                r = r or self:发送消息('右键双击', x, y, a, b, msg)
                            end
                        end
                        if not msg.win and not r then
                            v.type = v.typed
                        end
                    end
                end
                self._ldown = nil
                self._rdown = nil
                self.是否滑动=nil
            -- elseif v.type == SDL.MOUSE_MOTION then
            --     if v.state == 0 then
            --         local a, b = self:检查格子(v.x, v.y)
            --         if a then
            --             v.typed, v.type = v.type, nil
            --             v.control = self
            --             self._focus = true
            --             local x, y = b:取坐标()
            --             self:发送消息('获得鼠标', x, y, a, b, msg)
            --         elseif self._focus then
            --             self._focus = nil
            --             self:发送消息('失去鼠标', v.x, v.y, msg)
            --         end
            --     end

            elseif v.type == SDL.MOUSE_MOTION then
             
                --if gge.platform == 'Windows' then
                    if self:检查点(v.x, v.y) then
                        if v.state == 0 and gge.platform == 'Windows' then
                            local a, b = self:检查格子(v.x, v.y)
                            if a then
                                v.typed, v.type = v.type, nil
                                v.control = self
                                self._focus = true
                                local x, y = b:取坐标()
                                self:发送消息('获得鼠标', x, y, a, b, msg)
                            elseif self._focus then
                                self._focus = nil
                                self:发送消息('失去鼠标', v.x, v.y, msg)
                            end
                        elseif self._DOWN and v.state & SDL.BUTTON_LMASK == SDL.BUTTON_LMASK and self._max > 0 and self._max>self.高度  then 
                                v.typed, v.type = nil, nil
                                self._curdown = self
                                self.是否滑动=true
                                local py = self._DOWN.py + (v.y - self._DOWN.y)
                                if py > 0 then
                                    py = 0
                                elseif math.abs(py) > self._max then
                                    py = -self._max
                                end
                                if self.滑块 then
                                    self.滑块:置位置(math.floor(math.abs(py) / self._max * self.滑块.最大值))
                                else
                                    self._py = math.floor(py)
                                    _滚动(self)
                                end
                                self:发送消息('鼠标滚轮', py == -self._max)
                        end
                    else
                      self.是否滑动=nil
                    end
                -- elseif self._DOWN and v.state & SDL.BUTTON_LMASK == SDL.BUTTON_LMASK  then
                --   print(v.state)
                --       if self:检查点(v.x, v.y) and self._max > 0 and self._max>self.高度 then
                --             v.typed, v.type = v.type, nil
                --             self._curdown = false
                --             local py = self._DOWN.py + (v.y - self._DOWN.y)
                --             if py > 0 then
                --                 py = 0
                --             elseif math.abs(py) > self._max then
                --                 py = -self._max
                --             end
                --             if self.滑块 then
                --                 self.滑块:置位置(math.floor(math.abs(py) / self._max * self.滑块.最大值))
                --             else
                --                 self._py = math.floor(py)
                --                 _滚动(self)
                --             end
                --             self:发送消息('鼠标滚轮', py == -self._max)
                --       end
                -- end
            elseif v.type == SDL.MOUSE_WHEEL  then
                if self:检查点(v.x, v.y) and self._max > 0 and self._max>self.高度 then
                    v.typed, v.type = v.type, nil
                    v.control = self
                    local py =self._py
                    if v.wy >0 then
                        py =self._py + self.行高度
                    else
                        py =self._py - self.行高度
                    end
                    if py > 0 then
                        py = 0
                    elseif math.abs(py) > self._max then
                        py = -self._max
                    end
                    if self.滑块 then
                        self.滑块:置位置(math.floor(math.abs(self._py) / self._max * self.滑块.最大值))
                    else
                        self._py = math.floor(py)
                        _滚动(self)
                    end
                  -- self:发送消息('鼠标滚轮',v.x, v.y, v.wx, v.wy, msg)
                   self:发送消息('鼠标滚轮', py == -self._max)
                end





            end
        end
    end
end

function GUI控件:创建网格(name, x, y, w, h)
    assert(not self[name], name .. ':此网格已存在，不能重复创建.')
    self[name] = GUI网格(name, x, y, w, h, self)
    table.insert(self.子控件, self[name])
    return self[name]
end



return GUI网格
