local 消息管理 = 窗口层:创建窗口("消息管理", 0, 0, 590, 530)
local 字符串判定 = function(str, item)
  local t = {}
  local l = {}
  local index = 0
  for i = 1, string.len(str) do
    table.insert(t, string.byte(string.sub(str, i, i)))
  end
  for i = 1, string.len(item) do
    
    table.insert(l, string.byte(string.sub(item, i, i)))
  end
  if #l > #t then
    return false
  end
  for k, v1 in pairs(t) do
    index = index + 1
    if v1 == l[1] then
      local iscontens = true
      for i = 1, #l do
        if t[index + i - 1] ~= l[i] then
          iscontens = false
        end
      end
      if iscontens then
        return iscontens
      end
    end
  end
  return false
end
function 消息管理:初始化()
  self:创建纹理精灵(function()
    __res:getPNGCC(4, 0, 0, 535, 529):显示(0, 0)
     取输入背景(0, 0, 200, 34):显示(123, 483)
  end)
  self:置坐标(0, 引擎.高度 - self.高度)
  self.可初始化=true
end
function 消息管理:打开(data)
  self:置可见(true)
  --self.关闭:置可见(true)
  self.关闭:置坐标(535,self.高度//2)
 
  for _, k in ipairs(界面层.聊天控件.消息缓存.dq) do
      消息管理.聊天文本:添加文本(k, "dq")
  end
  self.dq:置选中(true)
  self.发送方式 = 1
  self.超链接 = {}
  self.选中 = "dq"
end

local 关闭 = 消息管理:创建按钮("关闭", 535, 210)
function 关闭:初始化()
  self:创建按钮精灵(__res:getPNGCC(2, 1138, 475, 31, 64),1)
end
function 关闭:左键弹起(x, y, msg)
  消息管理:置可见(false)
  if 窗口层.消息综合.是否可见 then
    窗口层.消息综合:打开(消息管理)
  end
end
function 消息管理:左键按下(x,y)
  if self.聊天文本:检查点(x,y) and self.聊天文本.是否滑动 then
      self.禁止移动= true
  else
      self.禁止移动= false
  end
end

for i, v in ipairs({
    {"dq","当前"},
    {"sj","世界"},
    {"bp","帮派"},
    {"xt","系统"},
    {"dw","队伍"},
    {"cw","传闻"}
}) do
  local 临时函数 = 消息管理:创建蓝色单选按钮(v[2], v[1], 40, 60+(i-1)*45,80, 40,说明字体)
  function  临时函数:左键弹起(x, y)
    消息管理.聊天文本:清空()
    for _, k in ipairs(界面层.聊天控件.消息缓存[v[1]]) do
       消息管理.聊天文本:添加文本(k, v[1])
    end
    消息管理.选中 = v[1]
  end
end
local 聊天文本 = 消息管理["丰富文本"](消息管理, "聊天文本", 140, 77, 343, 376, true)
function 聊天文本:添加文本(文本, 频道)
  if self._max > 1500 then
    self.清空(self)
  end
  if not 频道 or not __频道表[频道] then
    频道 = "dq"
  end

  local 临时内容 = " #" .. __频道表[频道] .. 文本
  if string.find(文本,"消耗了") or string.find(文本,"请截图给管理员")  or  string.find(文本,"请求下载中")  then
      临时内容 =  文本
  end
  self:置文本(临时内容)
  self._py = -self._max
end
-- function 聊天文本:回调左键弹起(cb, msg)
--   print(cb, msg)
-- end
local 消息输入 = 消息管理:创建文本输入("消息输入", 130, 479+8, 180, 18)
function 消息输入:初始化()
  self.取光标精灵(self)
  self:置限制字数(30)
  self:置颜色(0,0,0,255)
end

local 表情 = 消息管理:创建按钮( "表情", 330, 473)
function 表情:初始化()
  self:创建按钮精灵(__res:getPNGCC(4, 539, 2, 45, 45, true),1)
end
function 表情:左键弹起(x, y, msg)
  窗口层.消息综合:打开(消息管理)
end
local 序列表 = {
  sj = 3,
  bp = 6,
  dq = 1,
  cw = 5,
  dw = 2,
  -- gm = 9
  cy = 7
}
local 发送 = 消息管理:创建蓝色按钮("发送", "发送", 383,476, 120, 40,说明字体) 
function 发送:左键弹起(x, y, msg)
  if 消息管理["消息输入"]["取文本"](消息管理["消息输入"]) then
    if 序列表[消息管理["选中"]] then
      if 1 == self.发送方式 then
        请求服务(6001, {
          ["频道"] = 序列表[消息管理["选中"]],
          ["文本"] = 消息管理["消息输入"]["取文本"](消息管理["消息输入"]),
          ["发送方式"] = 消息管理["发送方式"]
        })
        消息管理["消息输入"]["清空"](消息管理["消息输入"])
      else
        local pd = false
        for i = 1, #消息管理["超链接"] do
         -- print(字符串判定(消息管理["消息输入"]["取文本"](消息管理["消息输入"]), 消息管理["超链接"][i]["名称"]))
          if 字符串判定(消息管理["消息输入"]["取文本"](消息管理["消息输入"]), 消息管理["超链接"][i]["名称"]) and 消息管理["超链接"][i]["类型"] ~= "" and "" ~= 消息管理["超链接"][i]["点击类型"] and 0 ~= 消息管理["超链接"][i]["编号"] then
            pd = true
          else
            pd = false
            break
          end
        end
        if pd then 
          if (序列表[消息管理["选中"]] == 3 or 序列表[消息管理["选中"]] == 2 or 序列表[消息管理["选中"]] == 7) then
            请求服务(6001, {
              ["频道"] = 序列表[消息管理["选中"]],
              ["文本"] = 消息管理["消息输入"]["取文本"](消息管理["消息输入"]),
              ["发送方式"] = 2,
              ["超链接"] = 消息管理["超链接"]
            })
            消息管理["消息输入"]["清空"](消息管理["消息输入"])
            消息管理["发送方式"] = 1
            消息管理["超链接"] = {}
          end
        else
          消息管理["发送方式"] = 1
          消息管理["超链接"] = {}
          请求服务(6001, {
            ["频道"] = 序列表[消息管理["选中"]],
            ["文本"] = 消息管理["消息输入"]["取文本"](消息管理["消息输入"]),
            ["发送方式"] = 1
          })
          消息管理["消息输入"]["清空"](消息管理["消息输入"])
        end
      end
    end
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y温馨提示：你还未输入内容")
  end
end
