--[[
LastEditTime: 2024-10-22 17:34:49
--]]


local 解锁修改 = 窗口层:创建窗口("解锁修改", 0, 0, 250, 320)
function 解锁修改:初始化()
  self:创建纹理精灵(function()
    置窗口背景("密码修改", 0, 0, 250, 320, true):显示(0, 0)
    取输入背景(0, 0, 140, 22):显示(92, 40)
    取输入背景(0, 0, 140, 22):显示(92, 75)
    取输入背景(0, 0, 140, 22):显示(92, 110)
    文本字体:置颜色( 255, 255, 255,255)
    文本字体:取图像("原密码"):显示(24, 42)
    文本字体:取图像("新密码"):显示( 24, 77)
    文本字体:取图像("确认密码"):显示( 24, 112)
    文本字体:取图像("请用右边的软键盘输入您的物品锁\n密码的前3位，剩余的位数请用键\n盘输入。\n物品或召唤兽加锁时，密码由"):显示(24, 140)
    文本字体:取图像("，长度不得少于4个或超过12"):显示(40,212)
    文本字体:取图像("个字符。加锁的召唤兽不允许使\n用系统赋予的名字。例如狼、蛤\n蟆精、地狱战神之类。修改密码\n一次扣除20点体力。"):显示(24,228)
    文本字体:置颜色(__取颜色("黄色")):取图像("a-"):显示(208,182)
    文本字体:置颜色(__取颜色("黄色")):取图像("z的小写英文字母、0-9的数字组\n成"):显示(24, 197)

end
)
 
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
  else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
  end

  
end


function 解锁修改:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.原密码:置文本("")
    self.新密码:置文本("")
    self.确认密码:置文本("")

end

local 原密码 = 解锁修改:创建文本输入( "原密码", 97, 42,130, 16)
function 原密码:初始化()
    self:取光标精灵()
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(0,0,0,255)
end

local 新密码 = 解锁修改:创建文本输入( "新密码", 97, 77,130, 16)
function 新密码:初始化()
    self:取光标精灵()
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(0,0,0,255)
end

local 确认密码 = 解锁修改:创建文本输入( "确认密码", 97,112,130, 16)
function 确认密码:初始化()
    self:取光标精灵()
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(0,0,0,255)
end


function 解锁修改:键盘弹起(键码, 功能)
  if 键码 == 9 then
      if self.原密码._输入焦点 then
          self.新密码:置焦点(true)
      elseif self.新密码._输入焦点 then
            self.确认密码:置焦点(true)
      else
          self.原密码:置焦点(true)
      end
  elseif 键码==13 then
          self.确认:左键弹起()
  end
end


local 确认=解锁修改:创建红色按钮("确认","确认", 50, 290,50, 22)  
function 确认:左键弹起(x, y)
          if not 解锁修改.原密码:取文本() or 解锁修改.原密码:取文本()=="" then
                __UI弹出.提示框:打开("#Y你还没有填写密码呢！")
          elseif string.len(解锁修改.原密码:取文本())>12 then
                 __UI弹出.提示框:打开("#Y密码过长，请重新填写")
          elseif not 解锁修改.新密码:取文本() or 解锁修改.新密码:取文本()=="" then
                  __UI弹出.提示框:打开("#Y你还没有填写密码呢！")
          elseif string.len(解锁修改.新密码:取文本())>12 then
                   __UI弹出.提示框:打开("#Y密码过长，请重新填写")
          elseif not 解锁修改.确认密码:取文本() or 解锁修改.确认密码:取文本()=="" then
                 __UI弹出.提示框:打开("#Y你还没有填写密码呢！")
          elseif 解锁修改.新密码:取文本()~=解锁修改.确认密码:取文本() then
                  __UI弹出.提示框:打开("#Y俩次密码不一样?")
          else
                请求服务(3754,{密码=解锁修改.原密码:取文本(),新密码=解锁修改.新密码:取文本()})
                解锁修改:置可见(false)
          end
end

local 取消=解锁修改:创建红色按钮("取消","取消", 150, 290,50, 22)  
function 取消:左键弹起(x, y)
  解锁修改:置可见(false)
end





local 关闭 = 解锁修改:创建关闭按钮("关闭")

function 关闭:左键弹起(x, y)
  解锁修改:置可见(false)
end

