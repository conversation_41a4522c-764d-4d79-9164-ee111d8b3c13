-- @Author: baidwwy
-- @Date:   2024-05-17 10:39:43
-- @Last Modified by:   baidwwy
-- @Last Modified time: 2024-05-17 10:41:43
local 角色改名 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "角色改名", 258 + abbr.py.x, 60 + abbr.py.y, 453, 410)
function 角色改名:初始化()
  local nsf = require("SDL.图像")(445, 410)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("角色改名", 0, 12, 445, 400, true)["显示"](xiao置窗口背景("角色改名", 0, 12, 445, 400, true), 0, 0)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35), 72, 60)
    取白色背景(0, 0, 410, 172, true)["显示"](取白色背景(0, 0, 410, 172, true), 18, 141)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "名称")["显示"](字体18["取图像"](字体18, "名称"), 18, 66)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 角色改名:打开()
  self:置可见(true)
end
local 关闭 = 角色改名["创建我的按钮"](角色改名, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 403, 0)
function 关闭:左键弹起(x, y, msg)
  角色改名["置可见"](角色改名, false)
  角色改名["名称输入"]["清空"](角色改名["名称输入"])
end
local 名称输入 = 角色改名["创建我的输入"](角色改名, "名称输入", 81, 67, 254, 24, nil, 8, "黑色", 字体20)
local 创建 = 角色改名["创建我的按钮"](角色改名, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "改名", 153, 337, "改名")
function 创建:左键弹起(x, y, msg)
  if 名称输入["取文本"](名称输入) then
    发送数据(71, {
      ["名称"] = 名称输入["取文本"](名称输入),
    })
    角色改名["置可见"](角色改名, false)
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请输入名字")
  end
end
