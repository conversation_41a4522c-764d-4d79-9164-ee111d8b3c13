__UI弹出["技能详情"] = __UI界面["创建弹出窗口"](__UI界面, "技能详情", 0, 0, 240, 300)
local 技能详情 = __UI弹出["技能详情"]
function 技能详情:初始化()
end
function 技能详情:左键弹起()
  self:置可见(false)
end
function 技能详情:打开(数据, x, y, w, h, mx,学习)
  self.数据 = 数据
  self:置坐标(x, y)
  self:置宽高(w, h)
  self.升级按钮:置可见(学习)
  if self.数据 then
    local nsf = require("SDL.图像")(w, h)
    if nsf["渲染开始"](nsf) then
      取黑色背景(0, 0, w, h, true):置透明(200):显示(0, 0)
      -- __res:getPNGCC(2, 230, 964, 10, 10)["显示"](__res:getPNGCC(2, 230, 964, 10, 10), 0, 0)
      -- __res:getPNGCC(2, 230, 974, 10, 31)["平铺"](__res:getPNGCC(2, 230, 974, 10, 31), 10, h - 20)["显示"](__res:getPNGCC(2, 230, 974, 10, 31)["平铺"](__res:getPNGCC(2, 230, 974, 10, 31), 10, h - 20), 0, 10)
      -- __res:getPNGCC(2, 230, 1005, 10, 10)["显示"](__res:getPNGCC(2, 230, 1005, 10, 10), 0, h - 10)
      -- __res:getPNGCC(2, 240, 964, 382, 10)["平铺"](__res:getPNGCC(2, 240, 964, 382, 10), w - 20, 10)["显示"](__res:getPNGCC(2, 240, 964, 382, 10)["平铺"](__res:getPNGCC(2, 240, 964, 382, 10), w - 20, 10), 10, 0)
      -- __res:getPNGCC(2, 240, 974, 382, 31)["平铺"](__res:getPNGCC(2, 240, 974, 382, 31), w - 20, h - 20)["显示"](__res:getPNGCC(2, 240, 974, 382, 31)["平铺"](__res:getPNGCC(2, 240, 974, 382, 31), w - 20, h - 20), 10, 10)
      -- __res:getPNGCC(2, 240, 1005, 382, 10)["平铺"](__res:getPNGCC(2, 240, 1005, 382, 10), w - 20, 10)["显示"](__res:getPNGCC(2, 240, 1005, 382, 10)["平铺"](__res:getPNGCC(2, 240, 1005, 382, 10), w - 20, 10), 10, h - 10)
      -- __res:getPNGCC(2, 622, 964, 10, 10)["显示"](__res:getPNGCC(2, 622, 964, 10, 10), w - 10, 0)
      -- __res:getPNGCC(2, 622, 974, 10, 31)["平铺"](__res:getPNGCC(2, 622, 974, 10, 31), 10, h - 20)["显示"](__res:getPNGCC(2, 622, 974, 10, 31)["平铺"](__res:getPNGCC(2, 622, 974, 10, 31), 10, h - 20), w - 10, 10)
      -- __res:getPNGCC(2, 622, 1005, 10, 10)["显示"](__res:getPNGCC(2, 622, 1005, 10, 10), w - 10, h - 10)
      if mx then
        mx["显示"](mx, 20, 15)
      end
      if 数据["名称"] then
        if self.数据.是否赐福 and skill取技能[self.数据.是否赐福] then 
          字体18:置颜色(__取颜色("紫色"))
          字体18:取图像(self.数据.是否赐福.."\n（进化后获得）"):显示(90, 15)
        elseif 取所有超级技能[self.数据.名称] then
          字体18:置颜色(__取颜色("紫色"))
          字体18:取图像(self.数据.名称):显示(90, 15)
        else
          字体18["置颜色"](字体18, 252, 252, 8)
          字体18["取图像"](字体18, 数据["名称"])["显示"](字体18["取图像"](字体18, 数据["名称"]), 90, 15)
        end
      end
      nsf["渲染结束"](nsf) --1
    end
    self:置精灵(nsf["到精灵"](nsf))
    if self.数据["类型"] == "召唤兽技能" then
      if self.数据[1] then
        技能详情["技能文本"]["置文本"](技能详情["技能文本"], "#G【功效】" .. self.数据[1])
      end
      if self.数据[4] then
        技能详情["技能文本"]["置文本"](技能详情["技能文本"], "#Y【消耗】" .. self.数据[4])
      end
      if self.数据[12] then
        技能详情["技能文本"]["置文本"](技能详情["技能文本"], "#Y【冷却】" .. self.数据[12])
      end
    elseif self.数据["类型"] == "召唤兽内丹" then
      if self.数据["等级"] then
        技能详情["技能文本"]["置文本"](技能详情["技能文本"], "#G" .. self.数据["等级"] .. "层/5层")
      end
      if self.数据["说明"] then
        技能详情["技能文本"]["置文本"](技能详情["技能文本"], "#Y" .. self.数据["说明"])
      end
      if self.数据["效果"] then
        技能详情["技能文本"]["置文本"](技能详情["技能文本"], "#G" .. self.数据["效果"])
      end
    elseif self.数据["类型"] == "召唤兽特性" then
      if self.数据["说明"] then
        技能详情["技能文本"]["置文本"](技能详情["技能文本"], "#G" .. self.数据["说明"])
      end
    elseif self.数据[1] then
      技能详情["技能文本"]["置文本"](技能详情["技能文本"], self.数据[1])
    end
  else
    local nsf = require("SDL.图像")(w, h)
    if nsf["渲染开始"](nsf) then
      取黑色背景(0, 0, w, h, true):置透明(200):显示(0, 0)
      nsf["渲染结束"](nsf)
    end
    self:置精灵(nsf["到精灵"](nsf))
  end
end
local 升级按钮 = 技能详情:创建我的按钮(__res:getPNGCC(3, 126, 563, 111, 36, true), "升级按钮", 55, 255, "内丹升级"):置可见(false)
function 升级按钮:左键弹起(x, y, msg)
  if __UI界面["窗口层"].召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[__UI界面["窗口层"].召唤兽属性.选中召唤兽] then
    __UI弹出["道具鉴定"]:打开(nil,__UI界面["窗口层"].召唤兽属性.选中召唤兽)
  end
  技能详情["置可见"](技能详情, false)
end
local 技能文本 = 技能详情["创建文本"](技能详情, "技能文本", 20, 80, 200, 200)
function 技能文本:初始化()
end
