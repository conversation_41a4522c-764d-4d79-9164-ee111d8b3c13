local 长安保卫战 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "长安保卫战", 80+67 + abbr.py.x, 20+42 + abbr.py.y, 561, 337)

function 长安保卫战:初始化()
  local nsf = require("SDL.图像")(561, 337)
  if nsf["渲染开始"](nsf) then
    -- __res:getPNGCC(5, 0, 0, 683, 450):显示(0,5)
    -- 字体20:置颜色(__取颜色("浅黑"))
    -- 字体20:取图像("伙 伴"):显示(683/2-20,9)
    置窗口背景("长安保卫战", 0, 12, 517+16,322, true):显示(0, 0)
    self.状态="最新战况"
    取白色背景(0, 0, 375, 154, true):显示(148, 4+48)
    取白色背景(0, 0, 375, 112, true):显示(148, 154+59)
    -- 字体18:置颜色(__取颜色("浅黑"))
    -- 字体18:取图像("当前剩余怪物最多的场景：\n先锋：\n头目：\n大王："):显示(148+18, 154+59+18)
    -- 字体18:取图像("30级"):显示(20,79+73)
    -- 字体18:取图像("40级"):显示(20,79+73*2)
    -- 字体18:取图像("50级"):显示(20,79+73*3)
    -- 字体18:取图像("60级"):显示(20,79+73*4)
    --取白色背景(0, 0, 440, 300, true)["显示"](取白色背景(0, 0, 440, 300, true), 20, 56)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 长安保卫战:打开(数据)
  self:置可见(true)
  self.刷怪记录=数据.刷怪记录
  self.积分=数据.积分
  self["jieshaowenben"]["清空"](self["jieshaowenben"])
  for k,v in pairs(self.刷怪记录) do
    self["jieshaowenben"]["置文本"](self["jieshaowenben"], v or "")
  end
  self.先锋=""
  self.头目=""
  self.大王=""
  for k,v in pairs(数据.先锋) do
    -- print(k,v) --地图 ， 数量
    if v>10 then
        self.先锋=self.先锋..取地图名称(k).."、"
    end
  end
  for k,v in pairs(数据.头目) do
    -- print(k,v) --地图 ， 数量
    if v>5 then
        self.头目=self.头目..取地图名称(k).."、"
    end
  end
  for k,v in pairs(数据.大王) do
    -- print(k,v) --地图 ， 数量
    if v>=2 then
        self.大王=self.大王..取地图名称(k).."、"
    end
  end
  self:cz()
  self[self.状态]:置选中(true)
end
function 长安保卫战:cz()
  self.图像=nil
  local nsf = require("SDL.图像")(317+47, 66+17)
  if nsf["渲染开始"](nsf) then
    if  self.状态=="个人积分" then
      字体18:置颜色(__取颜色("浅黑"))
      字体18:取图像("当前你的积分为："):显示(47, 17+11)
      字体18:置颜色(__取颜色("紫色"))
      字体18:取图像(self.积分):显示(130+52, 17+11)
    else
      字体18:置颜色(__取颜色("浅黑"))
      字体18:取图像("当前剩余怪物最多的场景：\n先锋：\n头目：\n大王："):显示(0, 0)
      字体18:置颜色(__取颜色("紫色"))
      字体18:取图像(self.先锋):显示(47, 17)
      字体18:取图像(self.头目):显示(47, 17+19)
      字体18:取图像(self.大王):显示(47, 17+19+19)
    end
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.图像:置中心(-213+47,-248+17)
end

local jieshaowenben = 长安保卫战["创建我的文本"](长安保卫战, "jieshaowenben", 148+18, 4+48+18, 348 , 130)
function jieshaowenben:初始化()
  
end




for i, v in ipairs({
  {
    name = "最新战况",
    x = 19,
    y = 52,
    tcp = __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(107, 36),
    tcp2 = __res:getPNGCC(1, 963, 495, 175, 43, true):拉伸(107, 36),
    font = "最新战况"
  },
  {
    name = "个人积分",
    x = 19,
    y = 52+45*1,
    tcp = __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(107, 36),
    tcp2 = __res:getPNGCC(1, 963, 495, 175, 43, true):拉伸(107, 36),
    font = "个人积分"
  },
  {
    name = "长安功臣",
    x = 19,
    y = 52+45*2,
    tcp = __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(107, 36),
    tcp2 = __res:getPNGCC(1, 963, 495, 175, 43, true):拉伸(107, 36),
    font = "长安功臣"
  },
  {
    name = "活动说明",
    x = 19,
    y = 52+45*3,
    tcp = __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(107, 36),
    tcp2 = __res:getPNGCC(1, 963, 495, 175, 43, true):拉伸(107, 36),
    font = "活动说明"
  },
}) do
  local 临时函数 = 长安保卫战["创建我的单选按钮"](长安保卫战, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
  function  临时函数:左键弹起(x, y)
    长安保卫战.状态=v.name
    长安保卫战:cz()
    -- if v.name == "最新战况" then
    -- end
  end
end








local 关闭 = 长安保卫战["创建我的按钮"](长安保卫战, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 500+29-35, 0)
function 关闭:左键弹起(x, y, msg)
  长安保卫战["置可见"](长安保卫战, false)
end