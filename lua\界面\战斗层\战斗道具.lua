--[[
LastEditTime: 2025-03-15 07:07:55
--]]
local 战斗道具 = 战斗层:创建窗口("战斗道具", 0, 0, 280, 280)
function 战斗道具:初始化()
      self:置精灵(置窗口背景("道具", 0, 0, 280, 280))
      self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
      self.可初始化=true
      if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
      else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
      end
end



function 战斗道具:打开(人物,无法使用)
    self:置可见(true)
    界面层.战斗界面:置可见(false)
		self.无法使用 = {}
    self.获取道具 = {}
    self.显示名称="道具"
		if 无法使用~=nil then
			 self.无法使用 = 无法使用
		end
		if 人物==角色信息.数字id then
			  self.获取道具 = table.copy(_tp.战斗道具)
        self.显示名称 = 角色信息.名称
	  else
        if not _tp.多角色[人物].战斗道具 then
            _tp.多角色[人物].战斗道具={}
        end
	      self.获取道具 = table.copy(_tp.多角色[人物].战斗道具)
        self.显示名称 = _tp.多角色[人物].名称
	  end
    self.道具网格:置数据(self.获取道具)
    self:禁止物品()
end

local 道具网格 = 战斗道具:创建背包网格("道具网格", 10,35)

function 道具网格:获得鼠标(x,y,i)
  local 物品 = self:焦点物品()
  if 物品 and 物品.物品  then
      __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
  end
end

function 道具网格:左键弹起(x,y,i)

  
        local 物品=self:选中物品() 
        if  物品 and 物品.物品 and self:选中()~=0 then
            if __手机 then
                __UI弹出.道具提示:打开(物品.物品,x+20,y+20,道具网格,"战斗道具",1)
            else
                 self:使用(1)
            end
        end
end


function 道具网格:使用(编号)
        if 编号 and 编号~=0 then
            local 物品=self:选中物品() 
            if 物品 and not 物品.禁止物品 then
                  界面层.战斗界面:设置道具参数(self:选中(), 战斗道具:取可用道具(物品.物品.名称,物品.物品.总类))
            end
        end
end



function 战斗道具:取可用道具(名称,总类)
  local 加血道具={"金创药","小还丹","千年保心丹","金香玉","五龙丹","翡翠豆腐","佛跳墙","蛇蝎美人","风水混元丹","定神香","十香返生丸","佛光舍利子","九转回魂丹","珍露酒","虎骨酒","女儿红","蛇胆酒","醉生梦死","梅花酒","百味酒","天不老","紫石英","血色茶花","熊胆","鹿茸","六道轮回","凤凰尾","硫磺草","龙之心屑","火凤之睛","四叶花","天青地白","七叶莲","丁香水","月星子","仙狐涎","地狱灵芝","麝香","血珊瑚","餐风饮露","白露为霜","天龙水","孔雀红","紫丹罗","佛手","旋复花","龙须草","百色花","香叶","白玉骨头","鬼切草","灵脂","曼陀罗花"
                  ,"草果","九香虫","水黄莲","山药","八角莲叶","人参"}
  for n=1,#加血道具 do
      if 加血道具[n]==名称 then return 5 end
  end
  if 名称=="乾坤袋" then
      return 4
  end
  if 总类~=nil and 总类==2000 then
    return 4
  end
   if 名称=="清心咒" or 名称=="乾坤玄火塔" or 名称=="混元伞"  or 名称=="五彩娃娃" or 名称=="苍白纸人" or 名称=="干将莫邪" or 名称=="聚妖铃" then
       return 5
    elseif 名称=="惊魂铃" or 名称=="发瘟匣" or 名称=="断线木偶"  or 名称=="摄魂" or 名称=="无魂傀儡" or 名称=="无尘扇" or 名称=="鬼泣"  or 名称=="缚妖索" or 名称=="捆仙绳" or 名称=="摄魂" then
       return 4
   end
   return 0
end


function 战斗道具:禁止物品()
  local 编号={}
  for k,v in pairs(self.获取道具) do
       if self.无法使用 and self.无法使用[k] then
            table.insert(编号,k)
       end
       if self:取可用道具(v.名称,v.总类)==0 then
            table.insert(编号,k)
       end
   end
   self.道具网格:置禁止(true,nil,编号)
end

local 关闭 = 战斗道具:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
      战斗层.战斗道具:置可见(false)
      界面层.战斗界面:重置()
      界面层.战斗界面:置可见(true)
end
