

function 取九宫图像(tcp,w,h,l,lx,t,zt,ys)
  if not l then
      l=10
  end
  local nsf = require('SDL.图像')(w,h)
  if nsf:渲染开始() then
        tcp:复制区域(0, 0, l, l):显示(0,0)
        tcp:复制区域(l, 0, tcp.宽度-l*2, l):平铺(w-l*2,l):显示(l,0)
        tcp:复制区域(tcp.宽度-l,0,l,l):显示(w-l,0)
        tcp:复制区域(0, l, l, tcp.高度-l*2):平铺(l,h-l*2):显示(0,l)
        tcp:复制区域(l, l,tcp.宽度-l*2, tcp.高度-l*2):平铺(w-l*2,h-l*2):显示(l,l)
        tcp:复制区域(tcp.宽度-l, l, l, tcp.高度-l*2):平铺(l,h-l*2):显示(w-l,l)
        tcp:复制区域(0, tcp.高度-l, l, l):显示(0,h-l)
        tcp:复制区域(l, tcp.高度-l, tcp.宽度-l*2, l):平铺(w-l*2,l):显示(l,h-l)
        tcp:复制区域(tcp.宽度-l,tcp.高度-l,l,l):显示(w-l,(h-l))
        if t then
            if type(t)=="string" or type(t)=="number" then
                local zts =文本字体
                if zt then
                    zts=zt
                end
                zts:置颜色(255,255,255,255)
                if ys then
                    if type(ys)=="string" then
                        zts:置颜色(__取颜色(ys))
                    elseif type(ys)=="table" then
                          zts:置颜色(ys[1],ys[2],ys[3],ys[4])
                    end
                end
                zts:取图像(t):显示((w- zts:取宽度(t)) // 2, (h - zts:取高度(t)) // 2 - 1)
            elseif ggetype(t)=="SDL图像" then
                  t:显示((w- t.宽度) // 2, (h - t.高度) // 2 - 1)
            elseif ggetype(t)=="tcp" then
                t:取图像(1):显示((w- t.宽度) // 2, (h - t.高度) // 2 - 1)
            end
      end
      nsf:渲染结束()
  end
  if lx then
      return nsf
  else
      return nsf:到精灵()
      
  end
end



--local n,_x,_y,x,y = 0,40,40,0,0
--         repeat
--             x = math.random(地图数据[IDX].宽)-20
--             y = math.random(地图数据[IDX].高)-20
--             if x<=0 then
--               x=  math.random(20)
--             end
--             if y<=0 then
--               y=  math.random(20)
--             end
--             n=n+1
--         until (self.地图数据[IDX].坐标[x] and self.地图数据[IDX].坐标[x][y]) or n>250
--     if n>200 then
--         return {x=_x,y=_y}
--     end
--     return {x=x,y=y}


function 取横排图像(tcp,w,l,lx,t,zt,ys)
        if not l then
            l=10
        end
        local ww,hh=w,tcp.高度
        local nsf = require('SDL.图像')(ww,hh)
        if nsf:渲染开始() then
            tcp:复制区域(l,0,tcp.宽度-2*l,hh):拉伸(ww-l,hh):显示(l//2,0)
            tcp:复制区域(0,0,l,hh):显示(0,0)
            tcp:复制区域(tcp.宽度-l,0,l,hh):显示(ww-l,0)
            if t then
                  if type(t)=="string" or type(t)=="number" then
                      local zts =文本字体
                      if zt then
                          zts=zt
                      end
                      zts:置颜色(255,255,255,255)
                      if ys then
                          if type(ys)=="string" then
                              zts:置颜色(__取颜色(ys))
                          elseif type(ys)=="table" then
                              zts:置颜色(ys[1],ys[2],ys[3],ys[4])
                          end
                      end
                      zts:取图像(t):显示((ww- zts:取宽度(t)) // 2, (hh - zts:取高度(t)) // 2 - 1)
                  elseif ggetype(t)=="SDL图像" then
                        t:显示((ww- t.宽度) // 2, (hh - t.高度) // 2 - 1)
                  elseif ggetype(t)=="tcp" then
                      t:取图像(1):显示((ww- t.宽度) // 2, (hh - t.高度) // 2 - 1)
                  end
            end
            nsf:渲染结束()
        end
      if lx then
          return nsf
      else
          return nsf:到精灵()
      end
end

function 置窗口背景(name,x,y,w,h,lx)
  local nsf = require('SDL.图像')(w + x,h + y)
  if nsf:渲染开始() then
    if __手机 then
        __res:getPNGCC(1,0,0,30,30):显示(x,y)
        __res:getPNGCC(1,0,30,30,140):平铺(30,h-60):显示(x,30+y)
        __res:getPNGCC(1,0,170,30,30):显示(x,h-30+y)

        __res:getPNGCC(1,30,0,140,30):平铺(w-60,30):显示(x + 30,y)
        __res:getPNGCC(1,30,30,140,140):平铺(w-60,h-60):显示(x + 30,30+y)
        __res:getPNGCC(1,30,170,140,30):平铺(w-60,30):显示(x + 30,h-30+y)

        __res:getPNGCC(1,170,0,30,30):显示(x + w-30,0+y)
        __res:getPNGCC(1,170,30,30,140):平铺(30,h-60):显示(x + w-30,30+y)
        __res:getPNGCC(1,170,170,30,30):显示(x + w-30,h-30+y)
        if name then
            __res:getPNGCC(1,448,0,20,28):显示(x+math.floor(w/3),y)
            __res:getPNGCC(1,20+448,0,45,28):平铺(math.floor(w/3)-40,28):显示(x + 20+math.floor(w/3),0+y)
            __res:getPNGCC(1,81+448,0,20,28):显示(x + math.floor(w/3)-20+math.floor(w/3),y)
            if name ~= "无" then
                道具字体:置颜色(255,255,255)
                道具字体:取图像(name):显示(x + math.floor(w/2- 道具字体:取宽度(name)/2),y+3)
            end
        end
    else
        取九宫图像(__res:取资源动画("jszy/dd",0x00000074,"图像"),w,h,20,true):显示(x,y)
        local tcp1 = __res:取资源动画("dlzy",0x12989E68,"图像")
        tcp1:复制区域(0, 0, 19, 19):显示(x + 2,2+y)
        tcp1:复制区域(19, 0, tcp1.宽度-38, 19):平铺(w-42,19):显示(x + 21,2+y)
        tcp1:复制区域(tcp1.宽度-19,0,19,19):显示(x + w-21,2+y)

        if name then 
              local 初始坐标 =  math.floor(w/2- 文本字体:取宽度(name)/2)
              local tcp2 = __res:取资源动画("jszy/dd",0x00000084,"图像")
              tcp2:复制区域(0, 0, 60, tcp2.高度):显示(x + 初始坐标-60,y)
              tcp2:复制区域(60, 0, tcp2.宽度-120, tcp2.高度):平铺(文本字体:取宽度(name),tcp2.高度):显示(x + 初始坐标,y)
              tcp2:复制区域(tcp2.宽度-60, 0, 60, tcp2.高度):显示(x + 初始坐标+文本字体:取宽度(name),y)
              if  name ~= "无" then 
                文本字体:置颜色(255,255,255)
                文本字体:取图像(name):显示(x + 初始坐标,y+3)
              end
        end
    end
         
      nsf:渲染结束()
  end
 
  if lx then
      return nsf
  else
      return nsf:到精灵()
  end
end


function 白色文字栏背景(w,h,lx)
  return 取九宫图像(__res:取资源动画("dlzy",0x550F8EF4,"图像"),w,h,55,lx)
end
function 蓝白标题背景(w,h,lx)
        return  取九宫图像(__res:取资源动画("jszy/dd",0x00000024,"图像"),w,h,55,lx)
end

取属性背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(x + w, h + y)
  if nsf:渲染开始() then
    __res:getPNGCC(1, 200, 0, 20, 20):显示(x,y)
    __res:getPNGCC(1, 200, 20, 20, 160):拉伸(20, h - 40):显示(x, 20 + y)
    __res:getPNGCC(1, 200, 180, 20, 20):显示(x, h - 20 + y)
    __res:getPNGCC(1, 220, 0, 160, 20):拉伸(w - 40, 20):显示(x + 20, y)
    __res:getPNGCC(1, 220, 20, 60, 160):拉伸(w - 40, h - 40):显示(x + 20, 20 + y)
    __res:getPNGCC(1, 230, 170, 140, 30):拉伸(w - 40, 20):显示(x + 20, h - 20 + y)
    __res:getPNGCC(1, 380, 0, 20, 20):显示(x + w - 20,0 + y)
    __res:getPNGCC(1, 380, 20, 20, 160):拉伸(20, h - 40):显示(x + w - 20, 20 + y)
    __res:getPNGCC(1, 380, 180, 20, 20):显示(x + w - 20, h - 20 + y)
    nsf:渲染结束()
  end
  if lx then
    return nsf
  else
    return nsf:到精灵()
  end
end


取输入背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(x + w, h + y)
  if nsf:渲染开始() then
    __res:getPNGCC(1, 552, 0, 10, 23):显示(x, y)
    __res:getPNGCC(1, 562, 0, 81, 23):平铺(w - 20, 23):显示(x + 10, y)
    __res:getPNGCC(1, 643, 0, 10, 23):显示(x + w - 10, y)
    nsf:渲染结束()
  end
  if lx then
    return nsf:到精灵()
  else
    return nsf
  end
end


取灰黑背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(x + w, h + y)
  if nsf:渲染开始() then
    __res:getPNGCC(1, 905, 385, 10, 72):显示(x, y)
    __res:getPNGCC(1, 915, 385, 210, 72):平铺(w - 20, 72):显示(x + 10, y)
    __res:getPNGCC(1, 1125, 385, 10, 72):显示(x + w - 10, y)
    nsf:渲染结束()
  end

  if lx then
    return nsf:到精灵()
  else
    return nsf
  end
end



取白色背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(x + w, h + y)
  if nsf:渲染开始() then
      取九宫图像(__res:取资源动画("jszy/dd",0x00000026,"图像"),w,h,20,true):显示(x,y)
    nsf:渲染结束()
  end
  if lx then
    return nsf
  else
    return nsf:到精灵()
  end
end
取灰色背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(x + w, h + y)
  if nsf:渲染开始() then
    __res:getPNGCC(3, 5, 923, 20, 20):显示(x,y)
    __res:getPNGCC(3, 5, 943, 20, 161):拉伸(20, h - 40):显示(x, 20 + y)
    __res:getPNGCC(3, 5, 1103, 20, 20):显示(x, h - 20 + y)
    __res:getPNGCC(3, 25, 923, 160, 20):拉伸(w - 40, 20):显示(x + 20,y)
    __res:getPNGCC(3, 25, 943, 160, 161):拉伸( w - 40, h - 40):显示(x + 20, 20 + y)
    __res:getPNGCC(3, 35, 1103, 160, 20):拉伸(w - 40, 20):显示(x + 20, h - 20 + y)
    __res:getPNGCC(3, 185, 923, 20, 20):显示(x + w - 20,y)
    __res:getPNGCC(3, 185, 943, 20, 161):拉伸(20, h - 40):显示(x + w - 20, 20 + y)
    __res:getPNGCC(3, 185, 1103, 20, 20):显示(x + w - 20, h - 20 + y)
    nsf:渲染结束()
  end
  if lx then
    return nsf
  else
    return nsf:到精灵()
  end
end
取黑透明背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(x + w, h + y)
  if nsf:渲染开始() then
    __res:getPNGCC(3, 213, 927, 20, 20):显示(x,y)
    __res:getPNGCC(3, 213, 947, 20, 161):拉伸(20, h - 40):显示(x, 20 + y)
    __res:getPNGCC(3, 213, 1108, 20, 20):显示(x, h - 20 + y)
    __res:getPNGCC(3, 233, 927, 160, 20):拉伸(w - 40, 20):显示(x + 20,y)
    __res:getPNGCC(3, 233, 947, 160, 161):拉伸(w - 40, h - 40):显示(x + 20, 20 + y)
    __res:getPNGCC(3, 243, 1108, 160, 20):拉伸(w - 40, 20):显示(x + 20, h - 20 + y)
    __res:getPNGCC(3, 407, 927, 20, 20):显示(x + w - 20, 0 + y)
    __res:getPNGCC(3, 407, 947, 20, 161):拉伸(20, h - 40):显示(x + w - 20, 20 + y)
    __res:getPNGCC(3, 407, 1108, 20, 20):显示(x + w - 20, h - 20 + y)
    nsf:渲染结束()
  end
  if lx then
    return nsf
  else
    return nsf:到精灵()
  end
end
取蓝黑色背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(x + w, h + y)
  if nsf:渲染开始() then
    __res:getPNGCC(2, 258, 572, 20, 20):显示(x,y)
    __res:getPNGCC(2, 258, 592, 20, 281):拉伸(20, h - 40):显示(x, 20 + y)
    __res:getPNGCC(2, 258, 873, 20, 20):显示(x, h - 20 + y)
    __res:getPNGCC(2, 278, 572, 189, 20):拉伸(w - 40, 20):显示(x + 20,y)
    __res:getPNGCC(2, 278, 592, 189, 281):拉伸(w - 40, h - 40):显示(x + 20, 20 + y)
    __res:getPNGCC(2, 288, 873, 189, 20):拉伸(w - 40, 20):显示(x + 20, h - 20 + y)
    __res:getPNGCC(2, 467, 572, 20, 20):显示(x + w - 20,y)
    __res:getPNGCC(2, 467, 592, 20, 281):拉伸(20, h - 40):显示(x + w - 20, 20 + y)
    __res:getPNGCC(2, 467, 873, 20, 20):显示(x + w - 20, h - 20 + y)
    nsf:渲染结束()
  end
  if lx then
    return nsf
  else
    return nsf:到精灵()
  end
end
取黑色背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(x + w, h + y)
  if nsf:渲染开始() then
    __res:getPNGCC(2, 487, 571, 20, 20):显示(x,y)
    __res:getPNGCC(2, 487, 591, 20, 153):拉伸(20, h - 40):显示(x, 20 + y)
    __res:getPNGCC(2, 487, 744, 20, 20):显示(x, h - 20 + y)
    __res:getPNGCC(2, 507, 571, 108, 20):拉伸( w - 40, 20):显示(x + 20,y)
    __res:getPNGCC(2, 507, 591, 108, 153):拉伸(w - 40, h - 40):显示(x + 20, 20 + y)
    __res:getPNGCC(2, 517, 744, 108, 20):拉伸(w - 40, 20):显示(x + 20, h - 20 + y)
    __res:getPNGCC(2, 615, 571, 20, 20):显示(x + w - 20,y)
    __res:getPNGCC(2, 615, 591, 20, 153):拉伸(20, h - 40):显示(x + w - 20, 20 + y)
    __res:getPNGCC(2, 615, 744, 20, 20):显示(x + w - 20, h - 20 + y)
    nsf:渲染结束()
  end
  if lx then
    return nsf
  else
    return nsf:到精灵()
  end
end



function 取钟灵石套装(套装,等级)
	if  套装=="心源" then
		if 等级<=3 then
			return "降低受到的物理伤害5%。"
		 else
		 	return "降低受到的物理伤害10%。"
		end
     elseif 套装=="气血方刚" then
		if 等级<=3 then
			return "气血上限提升6%"
		 else
		 	return "气血上限提升12%"
		end
    elseif 套装=="通真达灵" then
  	   if 等级 <=3 then
			return "降低受到的法术伤害3.5%"
		else
			return "降低受到的法术伤害4%"
		end
 elseif 套装=="心无旁骛" then
  	   if 等级 <=3 then
			return "增加100点抵抗封印等级"
		else
			return "增加300点抵抗封印等级"
		end
 elseif 套装=="健步如飞" then
  	   if 等级 <=3 then
			return "增加50点速度"
		else
			return "增加150点速度"
		end
 elseif 套装=="回春之术" then
  	   if 等级 <=3 then
			return "增加100点治疗能力"
		else
			return "增加350点治疗能力"
		end
 elseif 套装=="风雨不动" then
  	   if 等级 <=3 then
			return "增加100点抗法术暴击等级"
		else
			return "增加300点抗法术暴击等级"
		end
 elseif 套装=="固若金汤" then
  	   if 等级 <=3 then
			return "增加100点抗物理暴击等级"
		else
			return "增加300点抗物理暴击等级"
		end
 elseif 套装=="气壮山河" then
  	   if 等级 <=3 then
			return "增加80点气血回复效果"
		else
			return "增加240点气血回复效果"
		end
 elseif 套装=="锐不可当" then
  	   if 等级 <=3 then
			return "增加100点固定伤害"
		else
			return "增加350点固定伤害"
		end

	end
 return "未知错误"
end






