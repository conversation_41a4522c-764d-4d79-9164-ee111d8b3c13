
local 神器查看 = 窗口层:创建窗口("神器查看")
local 属性字段={速度="速　　度", 气血="气　　血",伤害="伤　　害",防御="防    御",封印命中="封印命中",法术伤害="法术伤害",固定伤害="固定伤害",物理暴击="物理暴击",治疗能力="治疗能力",法术暴击="法术暴击",法术防御="法术防御",抵抗封印="抵抗封印"}
local 技能介绍 = {
    藏锋敛锐 = {[1]= "横扫千军消耗的气血有50%的\n几率转化为等量护盾。",[2]="横扫千军消耗的气血有100%的\n几率转化为等量护盾。"},
    惊锋     = {[1]= "每次攻击提升自身10点伤害，\n最多叠加12层，死亡后清零。",[2]="每次攻击提升自身20点伤害\n，最多叠加12层，死亡后清\n零。"},
    披坚执锐 = {[1]= "遭受攻击时，有4%的几率免受\n90%的伤害。",[2]="遭受攻击时，有8%的几率免受\n90%的伤害。"},
    金汤之固 = {[1]= "气血小于30%时，提升240点抗\n封等级。",[2]="气血小于30%时，提升480点抗\n封等级。"},
    风起云墨 = {[1]= "受到你治疗的首目标本回合内\n受到的所有伤害降低4%。",[2]="受到你治疗的首目标本回合内\n受到的所有伤害降低8%。"},
    挥毫     = {[1]= "受到你的治疗时，目标每带有\n一个增益状态，额外恢复25点气血。",[2]="受到你的治疗时，目标每带有\n一个增益状态，额外恢复50点气血。"},
    盏中晴雪 = {[1]= "若你的速度高于施法者，提升\n速度差×0.5的抗封等级。",[2]="若你的速度高于施法者，提升\n速度差×1的抗封等级。"},
    泪光盈盈 = {[1]= "笑里藏刀额外减少目标6点愤\n怒。",[2]="笑里藏刀额外减少目标12点\n愤怒。"},
    凭虚御风 = {[1]= "每点被消耗的风灵增加40点法\n术伤害结果，最多叠加三层，\n死亡后清零。",[2]="每点被消耗的风灵增加80点法\n术伤害结果，最多叠加三层，\n死亡后清零。"},
    钟灵     = {[1]= "被使用3级药是有一定几率获\n得1层风灵。",[2]="被使用3级药是有较大几率获\n得1层风灵。"},
    亡灵泣语 = {[1]= "你的锢魂术会使得目标额外受\n到5%的物法伤害。",[2]="你的锢魂术会使得目标额外受\n到10%的物法伤害。"},
    魂魇     = {[1]= "被你的物理伤害攻击的单位在\n当回合内的法术伤害结果减少\n100点。",[2]="被你的物理伤害攻击的单位在\n当回合内的法术伤害结果减少\n200点。"},
    业焰明光 = {[1]= "你的法术有25%的几率造\n成额外25%的伤害。",[2]="你的法术有25%的几率造\n成额外50%的伤害。"},
    流火     = {[1]= "攻击气血百分比小于你的单位\n时，增加8%的伤害。",[2]="攻击气血百分比小于你的单位\n时，增加16%的伤害。"},
    蛮血     = {[1]= "增加（1-自身气血/气血上限）\n×8%的狂暴几率。",[2]="增加（1-自身气血/气血上限）\n×16%的狂暴几率。"},
    狂战     = {[1]= "每有一个己方召唤兽被击飞\n，增加30点伤害力，可叠加\n4层，死亡后消失。",[2]="每有一个己方召唤兽被击飞\n，增加60点伤害力，可叠加\n4层，死亡后消失。"},
    镜花水月 = {[1]= "受到治疗时，有8%的几率获\n得一个等额度的护盾。",[2]="受到治疗时，有16%的几率获\n得一个等额度的护盾。"},
    澄明     = {[1]= "每回合结束时，增加3点抵\n抗封印等级。",[2]="每回合结束时，增加6点抵抗\n封印等级。"},
    情思悠悠 = {[1]= "地涌金莲的目标获得治疗量\n10%的护盾。",[2]="地涌金莲的目标获得治疗量\n20%的护盾。"},
    相思     = {[1]= "偶数回合结束时，增加3点\n速度。",[2]="每个回合结束时，增加3点\n速度。"},
    弦外之音 = {[1]= "回合结束时，每个主动法宝\n效果会增加你3点愤怒。",[2]="回合结束时，每个主动法宝\n效果会增加你6点愤怒。"},
    裂帛     = {[1]= "  伤害性法术首目标伤害增加\n  8%。",[2]="  伤害性法术首目标伤害增加\n  16%。"},
    定风波   = {[1]= "受到的法术暴击伤害降低30%。",[2]="受到的法术暴击伤害降低60%。"},
    沧浪赋   = {[1]= "攻击气血小于30%的目标时，\n额外提升120点的法术伤害。",[2]="攻击气血小于30%的目标时，\n额外提升240点的法术伤害。"},
    斗转参横 = {[1]= "带有状态生命之泉时，日月\n乾坤命中率增加3%。",[2]="带有状态生命之泉时，日月\n乾坤命中率增加6%。"},
    静笃     = {[1]= "每次击杀敌方单位，增加60点\n伤害。",[2]= "每次击杀敌方单位，增加120\n点伤害。"},
    玉魄     = {[1]= "消耗愤怒的100%转化为下一次\n释放恢复性技能时的治疗能力。",[2]="消耗愤怒的200%转化为下一次\n释放恢复性技能时的治疗能力。"},
    璇华     = {[1]= "使用五行法术时，增\n加10%的伤害。",[2]="五使用五行法术时，增\n加20%的伤害。"},
    威服天下 = {[1]= "暴击伤害增加12%。",[2]="暴击伤害增加24%。"},
    酣战     = {[1]= "每点消耗的战意，会提升30点\n物理暴击等级，可叠加6次，\n死亡后清零。",[2]="每点消耗的战意，会提升60\n点物理暴击等级，可叠加6次，\n死亡后清零。"},
    万物滋长 = {[1]= "使用特技时将会获得（消耗愤\n怒值×等级×5%）的护盾和\n气血回复。",[2]="使用特技时将会获得（消耗愤\n怒值×等级×10%）的护盾和\n气血回复。"},
    开辟     = {[1]= "每次使用如意神通，提升20点\n自身伤害，最多叠加6层，死\n亡后清零。",[2]="每次使用如意神通，提升40点\n自身伤害，最多叠加6层，死\n亡后清零。"},
    鸣空     = {[1]= "每当令目标浮空时，你获得12点狂暴等级并且造成的物理伤害结果提高2%，最多叠加6层，阵亡后清零",[2]="每当令目标浮空时，你获得24点狂暴等级并且造成的物理伤害结果提高2%，最多叠加6层，阵亡后清零"},
    骇神     = {[1]= "受到物理伤害时，若攻击者物理伤害低于你，伤害结果降低10%",[2]="受到物理伤害时，若攻击者物理伤害低于你，伤害结果降低20%"},

}

local 名称图 = {
    大唐官府 = 0x130113D0,化生寺 = 0x9EE14769,方寸山 = 0xC160C703,女儿村 = 0xA0498994,
    天宫 = 0xD840B4EA,普陀山 = 0x721B7187,龙宫 =0xA8758DA8,五庄观 = 0xEA50FC9B,
    魔王寨 = 0x87798740,狮驼岭 = 0x50CCD3E0,盘丝洞 = 0xDCF3158D,阴曹地府 = 0xD07C437B,
    神木林 = 0xC0CD7B01,凌波城 = 0x29CCB8D3,无底洞 = 0xAF274C97
}


local 渲染背景 = {
    神木林 = 0x92A0D697,
    凌波城 = 0x087BEF28,
    盘丝洞 = 0xD9EC5E82,
    女儿村 = 0x5E688B50,
    无底洞 = 0x8CBAED1B,
    天宫   = 0x6918E30A,
    狮驼岭 = 0xFAF8A22D,
    阴曹地府 = 0x97E88B79,
    方寸山 = 0x69C5B0D1,
    魔王寨 = 0xFD1F847B,
    普陀山 = 0x119883BC,
    大唐官府 = 0x754D40E2,  
    化生寺 = 0x5587BEF3,
    龙宫   = 0xF9A0A6B9,
    五庄观 = 0x5E4F6929,
}

local 光源底图 = {
    神木林 = 0x4902C991,
    凌波城 = 0x37364FA2,
    女儿村 = 0x350639BB,
    无底洞 = 0xA40E6707,
    天宫   = 0xB7F12AC8,
    大唐官府 = 0x593B5349,
    方寸山 = 0xC8326D89,
    魔王寨 = 0xF244CEF3,
    五庄观 = 0x5E4F6929,
}

local 神器模型 = {
    神木林 = {0x018013AE,0x48A22679,0x27504873}, 
    天宫 = {0x290ACCB0,0xB2A9682E,0xC68841B3}, 
    盘丝洞 = {0x131B5F07,0x507F5020,0xD2EAC261}, 
    凌波城 = {0x9B10DFD8,0x65BB498B,0x3AAA5BED}, 
    女儿村 = {0x909F588F,0x14488877,0xF57FB4C6}, 
    无底洞 = {0xF991023F,0x16E503E3,0x4428166E}, 
    狮驼岭 = {0x7A43A176,0x8167254F,0x3BECCD5C}, 
    阴曹地府 = {0x64DDB00C,0x523D76AB,0xD02AC1C7}, 
    大唐官府 = {0x828B7E27,0x780B0000,0x52E6A411}, 
    化生寺 = {0xE2F71BD1,0xDA956848,0x5CC81540}, 
    龙宫 = {0x6EDC07C1,0xC7A953A8,0xF288E253}, 
    五庄观 = {0xA00DD3E7,0xE1CF43ED,0xDE964585}, 
    九黎城 = {0x10000079,0x10000080,0x10000081},
}
local 神器模型1={
    普陀山={[1]={"pt1.png","pt2.png","pt3.png"},[2]={"pt11.png","pt22.png","pt33.png"}},
    魔王寨={[1]={"mw1.png","mw2.png","mw3.png"},[2]={"mw11.png","mw22.png","mw33.png"}},
    方寸山={[1]={"fc1.png","fc2.png","fc3.png"},[2]={"fc11.png","fc22.png","fc33.png"}},
    花果山={[1]={"hg1.png","hg2.png","hg3.png"},[2]={"hg11.png","hg22.png","hg33.png"}},

}


function 神器查看:初始化()
        local tcp =__res:取资源动画("pic/sqsc","sqbj.png","图片")
        self.标题背景= __res:取资源动画("pic/sqsc","tbwzbj.png","图片"):到精灵()
        self.标题= 标题字体:置颜色(__取颜色("黄色")):取精灵("神器查看")
        self:置宽高(tcp.宽度,tcp.高度)
        self:置精灵(tcp:到精灵())
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        self.背景动画=__res:取资源动画("jszy/xjjm",0xC057E026,"动画") 
        self.技能背景= __res:取资源动画("pic/sqsc","321.png","图片"):到精灵()
        self.属性框= __res:取资源动画("pic/sqsc","123.png","图片"):到精灵()
        -- self.神器名字 = nil
        -- self.神器渲染 = nil
        -- self.神器光源 = nil

        self.左上角=__res:取资源动画("jszy/xjjm",0xA46F777B,"精灵")
        self.右上角 =__res:取资源动画("jszy/xjjm",0x240C67DC,"精灵")
        self.左下角=__res:取资源动画("jszy/xjjm",0x8A3AED59,"精灵")
       
    

end
function 神器查看:更新(dt)
    self.背景动画:更新(dt)
end
function 神器查看:显示(x,y)
            self.标题背景:显示(x+(self.宽度-self.标题背景.宽度)//2,y)
            self.标题:显示(x+(self.宽度-self.标题.宽度)//2,y+8)
            self.背景动画:显示(x-90,y-70)
            self.属性框:显示(x +408,y +258)
            self.技能背景:显示(x +453 ,y + 290)
            if self.技能名称 then
                self.技能名称:显示(x +453+(self.技能背景.宽度-self.技能名称.宽度)//2,y + 296)
            end
            if self.焦点 then
                if self.焦点==1 then
                    self.左上角:显示(x,y+45)
                elseif self.焦点==2 then
                    self.右上角:显示(x+408,y+45)
                elseif self.焦点==3 then
                    self.左下角:显示(x,y+330)
                end
            end


end

-- function 神器查看:获得鼠标(x,y)
--         if self.物品 then
--             for i = 1, 8 do
--                 if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画:检查透明(x, y)  then
--                     __UI弹出.自定义提示:打开(self.物品[i],x+20,y+20)
--                 end
--             end
--         end
-- end





function 神器查看:打开(数据)
    if not 角色信息 or not 角色信息.门派 or 角色信息.门派=="无" or 角色信息.门派=="无门派" or not 数据 or not 数据.神器 or not 数据.神器.神器技能  then return end
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
   
    self.神器属性 = {}
    self.技能 = 数据.神器.神器技能.名称
    self.等级 = 数据.神器.神器技能.等级

    for n=1,3 do
        if 数据.神器.神器解锁[n]~=nil then
            self.神器属性[n]=数据.神器.神器解锁[n]
        end
    end
    self.技能名称=文本字体:置颜色(255,255,255):取精灵(self.技能)
    self.标题= 标题字体:置颜色(__取颜色("黄色")):取精灵("神器查看")
    if 角色信息.门派~="无" and 角色信息.门派~="无门派" then
        if 角色信息.门派=="花果山" then
            self.标题= 标题字体:置颜色(__取颜色("黄色")):取精灵("鸿蒙石")
        elseif 角色信息.门派=="九黎城" then
            self.标题= 标题字体:置颜色(__取颜色("黄色")):取精灵("魔息角")
        elseif 名称图[角色信息.门派] then
            self.标题=__res:取资源动画("jszy/xjjm",名称图[角色信息.门派],"精灵")
        end
    end 
    self.技能描述:清空()
    self.技能描述:置文本(技能介绍[self.技能][self.等级])
    self.主体:显示设置(角色信息.门派)
    self.焦点=nil
   
end


local  技能描述=神器查看:丰富文本("技能描述",460,325,165,120)


local 主体=神器查看:创建控件("主体")

function 主体:显示设置(门派)
    self:置宽高(神器查看.宽度,神器查看.高度)
    self.神器光源=nil
    self.神器渲染=nil
    self.棋子=nil
    self.门派=门派
    if 渲染背景[self.门派] then
        self.神器渲染=__res:取资源动画("jszy/xjjm",渲染背景[self.门派],"精灵")
    end
    if 光源底图[self.门派] then
        self.神器光源=__res:取资源动画("jszy/xjjm",光源底图[self.门派],"精灵")
    end
    for i = 1, 3 do
        self["按钮"..i]:显示设置(self.门派)
    end
    if self.门派=="神木林" then
        self.按钮1:置坐标(260,67)
        self.按钮2:置坐标(234,137)
        self.按钮3:置坐标(203,134)  
    elseif self.门派=="大唐官府" then
        self.按钮1:置坐标(260,114)
        self.按钮2:置坐标(39,191)
        self.按钮3:置坐标(119,240) 
    elseif self.门派=="化生寺" then
        self.按钮1:置坐标(336,136)
        self.按钮2:置坐标(201,70)
        self.按钮3:置坐标(86,216) 
    elseif self.门派=="女儿村" then
        self.按钮1:置坐标(146,63)
        self.按钮2:置坐标(193,185)
        self.按钮3:置坐标(108,155) 
    elseif self.门派=="方寸山" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(96,65)
            self["按钮"..i]:置宽高(350,350)
        end
      
    elseif self.门派=="龙宫" then
        self.按钮1:置坐标(127,66)
        self.按钮2:置坐标(232,66)
        self.按钮3:置坐标(76,71) 
    elseif self.门派=="普陀山" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(126,33)
            self["按钮"..i]:置宽高(380,380)
        end
    elseif self.门派=="五庄观" then
        self.按钮1:置坐标(156,86)
        self.按钮2:置坐标(193,186)
        self.按钮3:置坐标(153,159) 
        self.棋子=__res:取资源动画("jszy/xjjm",0x013253E7,"精灵")
    elseif self.门派=="天宫" then
        self.按钮1:置坐标(277,66)
        self.按钮2:置坐标(121,122)
        self.按钮3:置坐标(177,232) 
    elseif self.门派=="凌波城" then
        self.按钮1:置坐标(183,66)
        self.按钮2:置坐标(148,91)
        self.按钮3:置坐标(159,201) 
    elseif self.门派=="狮驼岭" then
        self.按钮1:置坐标(135,60)
        self.按钮2:置坐标(150,143)
        self.按钮3:置坐标(213,239) 
    elseif self.门派=="魔王寨" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(96,33)
            self["按钮"..i]:置宽高(350,350)
        end
    elseif self.门派=="阴曹地府" then
        self.按钮1:置坐标(115,126)
        self.按钮2:置坐标(165,173)
        self.按钮3:置坐标(151,261) 
    elseif self.门派=="无底洞" then
        self.按钮1:置坐标(323,60)
        self.按钮2:置坐标(234,151)
        self.按钮3:置坐标(137,238) 
    elseif self.门派=="盘丝洞" then
        self.按钮1:置坐标(165,120)
        self.按钮2:置坐标(125,53)
        self.按钮3:置坐标(138,189) 
	elseif self.门派=="九黎城" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(100,70)
            self["按钮"..i]:置宽高(380,300)
        end
    elseif self.门派=="花果山" then
        for i = 1, 3 do
            self["按钮"..i]:置坐标(96,37)
            self["按钮"..i]:置宽高(350,350)
        end
    end
   

end


function 主体:显示(x,y)
        if self.门派 then
            if self.神器渲染 then
                if self.门派=="神木林" then
                    self.神器渲染:显示(x + 110,y + 29) 
                elseif self.门派=="大唐官府" then
                    self.神器渲染:显示(x + 73,y + 50)
                elseif self.门派=="化生寺" then
                    self.神器渲染:显示(x + 53,y + 32)
                elseif self.门派=="女儿村" then
                    self.神器渲染:显示(x + 78,y + 12)
                elseif self.门派=="方寸山" then
                    self.神器渲染:显示(x + 111,y + 18)
                elseif self.门派=="龙宫" then
                    self.神器渲染:显示(x + 108,y + 18)
                elseif self.门派=="普陀山" then
                    self.神器渲染:显示(x + 138,y + 25)
                elseif self.门派=="五庄观" then
                    self.神器渲染:显示(x + 88,y + 44)
                elseif self.门派=="天宫" then
                    self.神器渲染:显示(x + 108,y + 46)
                elseif self.门派=="凌波城" then
                    self.神器渲染:显示(x + 85,y + 48)
                elseif self.门派=="狮驼岭" then
                    self.神器渲染:显示(x + 83,y + 47)
                elseif self.门派=="阴曹地府" then
                    self.神器渲染:显示(x + 88,y + 29)
                elseif self.门派=="无底洞" then
                    self.神器渲染:显示(x + 118,y + 28)
                elseif self.门派=="盘丝洞" then
                    self.神器渲染:显示(x + 97,y + 23)
                end
            end
            if self.神器光源 then
                if self.门派=="神木林" then
                    self.神器光源:显示(x + 109,y + 30) 
                elseif self.门派=="大唐官府" then
                    self.神器光源:显示(x + 75,y + 45) 
                elseif self.门派=="女儿村" then
                    self.神器光源:显示(x + 80,y + 10)
                elseif self.门派=="方寸山" then
                    self.神器光源:显示(x + 114,y + 13)
                elseif self.门派=="五庄观" then
                    self.神器光源:显示(x + 88,y + 41)
                elseif self.门派=="天宫" then
                    self.神器光源:显示(x + 114,y + 43)  
                elseif self.门派=="凌波城" then
                    self.神器光源:显示(x + 88,y + 45)
                elseif self.门派=="魔王寨" then
                    self.神器光源:显示(x + 88,y + 45)
                elseif self.门派=="无底洞" then
                    self.神器光源:显示(x + 116,y + 33) 
                end
            end
        end
  if self.棋子 then 
    self.棋子:显示(x + 89,y + 42)
  end 
end


for n = 1, 3 do
    local 临时函数=主体:创建按钮("按钮"..n)
    function 临时函数:显示设置(门派)
            local tpc = __res:取资源动画("jszy/jmxf",0x00000407,"图像"):拉伸(10,10)
            local tpc1 = __res:取资源动画("jszy/jmxf",0x00000407):取图像(2):拉伸(10,10)
            if 门派=="九黎城" then
                tpc = __res:取资源动画("jszy/jmtb",神器模型[门派][n],"图像")
                tpc1 = __res:取资源动画("jszy/jmtb",神器模型[门派][n]):取图像(2)
            elseif 神器模型1[门派] and 神器模型1[门派][1] then
                tpc = __res:取资源动画("pic/sqsc",神器模型1[门派][1][n],"图片")
                tpc1 =__res:取资源动画("pic/sqsc",神器模型1[门派][2][n],"图片")
            elseif 神器模型[门派] and 神器模型[门派][n] then
                tpc = __res:取资源动画("jszy/xjjm",神器模型[门派][n],"图像")
                tpc1 = __res:取资源动画("jszy/xjjm",神器模型[门派][n]):取图像(3)
            end
            self:置宽高(tpc.宽度,tpc.高度)
            self:置正常精灵(tpc:到精灵())
            self:置按下精灵(tpc:到精灵())
            if not __手机 then
                self:置经过精灵(tpc1:到精灵())
            end

    end
    function 临时函数:获得鼠标(x,y)
            for i = 1, 3 do
                神器查看["描述"..i]:清空()
                if i==n then
                    神器查看["描述"..i]:置可见(true)
                else
                    神器查看["描述"..i]:置可见(false)
                end
            end
            if 神器查看.神器属性[n] then
                for i=1,4 do
                    神器查看["描述"..n]:置文本(属性字段[神器查看.神器属性[n].神器五行属性[i]].."  +"..神器查看.神器属性[n].神器五行数值[i])
                end
            end
            神器查看.焦点=n
    end
    function 临时函数:失去鼠标(x,y)     
            神器查看.焦点=nil
            for i = 1, 3 do
                神器查看["描述"..i]:清空()
                神器查看["描述"..i]:置可见(false)
            end
    end
    function 临时函数:左键双击(x,y)  
          请求服务(6210)
          神器查看:置可见(false)
    end
    function 临时函数:左键弹起(x,y)  
            神器查看.焦点=nil 
            for i = 1, 3 do
                神器查看["描述"..i]:清空()
                神器查看["描述"..i]:置可见(false)
            end
          if __手机 then
                for i = 1, 3 do
                      神器查看["描述"..i]:清空()
                      if i==n then
                             神器查看["描述"..i]:置可见(true)
                        else
                             神器查看["描述"..i]:置可见(false)
                        end
                  end
                  if 神器查看.神器属性[n] then
                        for i=1,4 do
                            神器查看["描述"..n]:置文本(属性字段[神器查看.神器属性[n].神器五行属性[i]].."  +"..神器查看.神器属性[n].神器五行数值[i])
                        end
                  end
                  神器查看.焦点=n
          else
                请求服务(6210)
                神器查看:置可见(false)
          end  
        
    end

         




end


local 描述1=神器查看:丰富文本("描述1",40,65,160,100)
描述1.行间距=5

local 描述2=神器查看:丰富文本("描述2",450,65,160,100)
描述2.行间距=5

local 描述3=神器查看:丰富文本("描述3",40,300,160,100)
描述3.行间距=5

local 关闭 = 神器查看:创建按钮( "关闭", 605,2) 
function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x20FD5715),1)
end
function 关闭:左键弹起(x, y)
    神器查看:置可见(false)
end



  


















