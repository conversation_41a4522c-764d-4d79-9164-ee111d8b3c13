local 多开道具 = 窗口层:创建窗口("多开道具",0, 0, 525, 332)
local 格子设置={"一","二","三","四","五"}
function 多开道具:初始化()
        self.模型格子= __UI模型格子:创建()
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        if __手机 then
            self.关闭:置大小(25,25)
            self.关闭:置坐标(self.宽度-27, 2)
        else
            self.关闭:置大小(16,16)
            self.关闭:置坐标(self.宽度-18, 2)
        end
end


function 多开道具:打开(玩家id)
        self:置可见(not self.是否可见)
        if not self.是否可见 then
            return
        end
        self.起点 = 0
        self.分类 = "灵饰"
        self.选中编号 = nil
        self.抓取物品 = nil
        self.包裹类型 = "道具"
        self.抓取物品ID = nil
        self.窗口类型 = "主人公"
        self.道具网格:置数据()
        self.装备网格:置数据()
        self.灵饰网格:置数据()
        self.召唤兽装备:置数据()
        self:刷新(玩家id)
end

function 多开道具:刷新(玩家id)
        self.玩家id=玩家id
        self:重置窗口()
        self:模型重置() 
end



function 多开道具:重置窗口()
  
        self.角色按钮:置可见(false)
        self.装备网格:置可见(false)
        self.灵饰网格:置可见(false)
        self.宝宝按钮:置可见(false)
        self.名称选择:置可见(false)
        self.召唤兽装备:置可见(false)
        self[self.窗口类型]:置选中(true)
        self.角色信息=table.copy(_tp.多角色[self.玩家id])
        self:创建纹理精灵(function()
            置窗口背景("多开道具", 0, 0, 525, 332,true):显示(0,0)
            标题字体:置颜色(255,255,255,255)
            标题字体:取图像("存银"):显示(253,305)
            取输入背景(0, 0, 180, 22):显示(290,275)
            取输入背景(0, 0, 180, 22):显示(290,300)
            文本字体:置颜色(__取银子颜色(self.角色信息.银子))
            文本字体:取图像(self.角色信息.银子):显示(295,278)
            文本字体:置颜色(__取银子颜色(self.角色信息.存银))
            文本字体:取图像(self.角色信息.存银):显示(295,305)

            if self.窗口类型 == "主人公" then
                    self:角色显示()
            else
                self.名称选择:置可见(true)
                取白色背景(0, 0, 110, 120, true):显示(5, 60)
                取白色背景(0, 0, 120, 200, true):显示(120, 60)
                if self.窗口类型 == "召唤兽" then
                      self:宝宝显示()
                elseif self.窗口类型 == "子女" then
                      self:子女显示()
                else
                    self:坐骑显示() 
                end

            end
        end)
        self:重置背包()
end


function 多开道具:重置背包()
      self.图像=nil
      self.格子控件:置可见(false)
      self.背包控件.道具:置选中(false)
      self.背包控件.行囊:置选中(false)
      窗口层.灵饰:置可见(false)
      窗口层.锦衣:置可见(false)
      if self.包裹类型=="道具" then
          self.背包控件.道具:置选中(true)
          local 物品={}
          for n = 1, 20 do
              物品[n]=self.角色信息.道具列表[n+self.起点]
          end
          self.道具网格:置物品(物品)
          self.格子控件:置可见(true)
          for i, v in ipairs(格子设置) do
                self.格子控件[v]:置选中(false)
                if (i-1)*20==self.起点 then
                  self.格子控件[v]:置选中(true)
                end
          end
      elseif self.包裹类型=="行囊" then
            self.起点 = 0
            self.背包控件.行囊:置选中(true)
            self.道具网格:置物品(self.角色信息.行囊列表)
            self.图像=__res:取资源动画("jszy/fwtb",0xabcd0204,"图像"):平铺(16,210):到精灵()
      else
          self:关闭道具()
      end
end



function 多开道具:重置抓取()
      if self.抓取物品ID  then
          self.道具网格.选中编号=nil
          self.道具网格.焦点编号=nil
      end
      if __UI弹出.道具提示.是否可见 then
          __UI弹出.道具提示:置可见(false)
      end
      if 鼠标层.附加 and ggetype(鼠标层.附加)=="物品格子" then
          鼠标层.附加=nil
      end
      self.抓取物品 = nil
      self.抓取物品ID = nil
end



function 多开道具:关闭道具()
        self.起点 = 0
        self.分类 = "灵饰"
        self.模型格子:清空()
        self.选中编号 = nil
        self.抓取物品 = nil
        self.包裹类型 = "道具"
        self.抓取物品ID = nil
        self.窗口类型 = "主人公"
        self:置可见(false)
end


function 多开道具:更新(dt)
      if self.模型格子 then
          self.模型格子:更新(dt)
      end
end

function 多开道具:显示(x,y)
      if self.模型格子 then
          self.模型格子:显示(x,y)
      end
      if self.图像 then
          self.图像:显示(x+504,y+60)
      end
end

function 多开道具:模型重置()  
          self.模型格子:清空()
          if self.窗口类型 == "主人公" then
              self.模型格子:置数据(self.角色信息, "角色",120,220)
          elseif self.选中编号 then
              if self.窗口类型 == "召唤兽" and self.角色信息.宝宝列表[self.选中编号] then
                    self.模型格子:置数据(self.角色信息.宝宝列表[self.选中编号], "召唤兽",65,160)
              elseif self.窗口类型 == "子女" and  self.角色信息.子女列表 and self.角色信息.子女列表[self.选中编号] then
                    self.模型格子:置数据(self.角色信息.子女列表[self.选中编号], "召唤兽",65,160)
              elseif self.窗口类型 == "坐骑" and self.角色信息.坐骑列表[self.选中编号] then
                    self.模型格子:置数据(self.角色信息.坐骑列表[self.选中编号], "坐骑",65,160,self.角色信息.模型)
              end
          end
end

function 多开道具:角色显示()  
          self.角色按钮:置可见(true)
          self.装备网格:置可见(true)
          self.灵饰网格:置可见(true)
          self.装备网格:置数据(self.角色信息.装备)
          __res:取资源动画("dlzy", 0xAB3BFB4B,"图像"):显示(5, 115)
          __res:取资源动画("dlzy", 0x8F00251E,"图像"):显示(185, 115)
          __res:取资源动画("dlzy", 0xD7321A7B,"图像"):显示(5, 170)
          __res:取资源动画("dlzy", 0x219FF008,"图像"):显示(185, 170)
          __res:取资源动画("dlzy", 0xF611EC24,"图像"):显示(5, 225)
          __res:取资源动画("dlzy", 0xF55CDE6D,"图像"):显示(185, 225)
          取白色背景(0, 0, 110, 120, true):显示(65,115)
          文本字体:置颜色(255,255,255,255)
          文本字体:取图像("快速切换套装"):显示(65,240)
          if self.分类=="灵饰" then
              self.灵饰网格:置数据(self.角色信息.灵饰)
              self.角色按钮.锦衣:置文字(50,22,"锦衣")
              __res:取资源动画("dlzy", 0x841CBD61,"图像"):显示(5, 60)
              __res:取资源动画("dlzy", 0x9912E8B9,"图像"):显示(65, 60)
              __res:取资源动画("dlzy", 0x5795605E,"图像"):显示(125, 60)
              __res:取资源动画("dlzy", 0xEBD8985D,"图像"):显示(185, 60)
          else
              self.灵饰网格:置数据(self.角色信息.锦衣)
              self.角色按钮.锦衣:置文字(50,22,"灵饰")
              __res:取资源动画("dlzy", 0xE3347D1C,"图像"):显示(5, 60)
              __res:取资源动画("dlzy", 0xC0520208,"图像"):显示(65, 60)
              __res:取资源动画("dlzy", 0xF55CDE6D,"图像"):显示(125, 60)
              __res:取资源动画("dlzy", 0xCF39217A,"图像"):显示(185, 60)
          end
end
function 多开道具:宝宝显示()
          self.召唤兽装备:置可见(true)
          self.名称选择:重置(self.角色信息.宝宝列表)
          标题字体:置颜色(255,255,255,255)
          标题字体:取图像("召唤兽装备"):显示(5,245)
          文本字体:置颜色(255,255,255,255)
          文本字体:取图像("气血"):显示(5,188)
          文本字体:取图像("魔法"):显示(5,218)
          取输入背景(0, 0, 80, 23):显示(35,185)
          取输入背景(0, 0, 80, 23):显示(35,215)
          __res:取资源动画("dlzy",0x40C73F26,"图像"):显示(5,270)
          __res:取资源动画("dlzy",0xD09D407F,"图像"):显示(64,270)
          __res:取资源动画("dlzy",0xE3347D1C,"图像"):显示(123,270)
          __res:取资源动画("dlzy",0x3E1CFEA7,"图像"):显示(182,270) 
          if self.选中编号 and  self.角色信息.宝宝列表[self.选中编号] then
                self.名称选择:置选中(self.选中编号)
                self.召唤兽装备:置数据(self.角色信息.宝宝列表[self.选中编号].装备)
                文本字体:置颜色(0,0,0,255)
                文本字体:取图像(self.角色信息.宝宝列表[self.选中编号].气血.."/"..self.角色信息.宝宝列表[self.选中编号].最大气血):显示(40,188)
                文本字体:取图像(self.角色信息.宝宝列表[self.选中编号].魔法.."/"..self.角色信息.宝宝列表[self.选中编号].最大魔法):显示(40,218)
          end

end

function 多开道具:子女显示()  
        self.召唤兽装备:置可见(true)
        self.名称选择:重置(self.角色信息.子女列表)
        标题字体:置颜色(255,255,255,255)
        标题字体:取图像("子女装备"):显示(5,245)
        文本字体:置颜色(255,255,255,255)
        文本字体:取图像("气血"):显示(5,188)
        文本字体:取图像("魔法"):显示(5,218)
        取输入背景(0, 0, 80, 23):显示(35,185)
        取输入背景(0, 0, 80, 23):显示(35,215)
        __res:取资源动画("dlzy",0x40C73F26,"图像"):显示(5,270)
        __res:取资源动画("dlzy",0xD09D407F,"图像"):显示(64,270)
        __res:取资源动画("dlzy",0xE3347D1C,"图像"):显示(123,270)
        __res:取资源动画("dlzy",0x3E1CFEA7,"图像"):显示(182,270) 
        if self.选中编号 and  self.角色信息.子女列表 and  self.角色信息.子女列表[self.选中编号] then
              self.名称选择:置选中(self.选中编号)
              self.召唤兽装备:置数据(self.角色信息.子女列表[self.选中编号].装备)
              文本字体:置颜色(0,0,0,255)
              文本字体:取图像(self.角色信息.子女列表[self.选中编号].气血.."/"..self.角色信息.子女列表[self.选中编号].最大气血):显示(40,188)
              文本字体:取图像(self.角色信息.子女列表[self.选中编号].魔法.."/"..self.角色信息.子女列表[self.选中编号].最大魔法):显示(40,218)
        end

end

function 多开道具:坐骑显示()  
        self.宝宝按钮.乘骑:重置()
        self.宝宝按钮:置可见(true)
        self.名称选择:重置(self.角色信息.坐骑列表)
        __res:取资源动画("dlzy",0x3E1CFEA7,"图像"):显示(5,185)
        __res:取资源动画("dlzy",0x3E1CFEA7,"图像"):显示(60,185)
        if self.选中编号 and  self.角色信息.坐骑列表[self.选中编号] then
            self.名称选择:置选中(self.选中编号)
            self.宝宝按钮.乘骑:置禁止(false)
             if self.角色信息.坐骑列表[self.选中编号].饰品 and self.角色信息.坐骑列表[self.选中编号].饰品.名称 then
                local lxxs  = 取物品(self.角色信息.坐骑列表[self.选中编号].饰品.名称)
                if lxxs and  lxxs[12] then
                    __res:取资源动画(lxxs[11],lxxs[12],"图像"):显示(8,185) 
                end
             end
         end
end

local 窗口设置 = {"人物","召唤兽","坐骑","子女" }
for i, v in ipairs(窗口设置) do
      local 编号 = v
      if v=="人物" then
         编号="主人公"
      end
      local 临时函数 = 多开道具:创建蓝色单选按钮(v, 编号,5+(i-1)*60, 35,55,22)
      function  临时函数:左键弹起(x, y)
          if v=="人物" then
            多开道具.窗口类型 = "主人公"
          else
            多开道具.窗口类型 = v
          end
          多开道具.选中编号 = nil
          多开道具:重置抓取()
          多开道具:重置窗口()
          多开道具:模型重置() 
      end
end
local 角色按钮 = 多开道具:创建控件("角色按钮", 5, 60, 250,270)
local 按钮设置={"1","2","3","看"}
for i, v in ipairs(按钮设置) do
    local 临时函数 = 角色按钮:创建蓝色单选按钮(v, v,60+(i-1)*30,198,25,25)
    if i==1 then
      临时函数:置选中(true)
    end
end




  local 锦衣按钮 =角色按钮:创建红色按钮("锦衣", "锦衣", 25, 230,50,22)
  function 锦衣按钮:左键弹起(x, y)
        if 多开道具.分类=="锦衣" then
          多开道具.分类 = "灵饰"
        else
          多开道具.分类 = "锦衣"
        end
        多开道具:重置窗口()
  end
   

    local 法宝按钮 =角色按钮:创建红色按钮("法宝", "法宝", 90, 230,50,22)

    function 法宝按钮:左键弹起(x, y)
          local 物品=多开道具.道具网格:选中物品()
          if 多开道具.抓取物品ID and 多开道具.抓取类型 and 物品 and 物品.物品 and (物品.物品.总类 == 1000 or 物品.物品.总类 == 1005) then
              if 物品.物品.总类 == 1000  then
                 -- 请求服务(3701.1,{抓取id=多开道具.抓取物品ID,放置类型="法宝",抓取类型=多开道具.抓取类型})
              elseif 物品.物品.总类 == 1005 then
                 -- 请求服务(3701.1,{抓取id=多开道具.抓取物品ID,放置类型="灵宝",抓取类型=多开道具.抓取类型})
              end
          else
             -- 请求服务(3732)
          end
          多开道具:重置抓取() 
    end

    local 回收按钮 =角色按钮:创建红色按钮("回收", "回收", 155, 230,50,22)
    function 回收按钮:左键弹起(x, y)
          窗口层.多开回收:打开()
          请求服务(63,{参数=多开道具.玩家id,文本="回收系统",序列="获取信息"})
    end

 

local 道具网格 = 多开道具:创建道具网格("道具网格", 245, 60)

function 道具网格:获得鼠标(x, y,a)
  多开道具.装备网格:失去鼠标()
  多开道具.灵饰网格:失去鼠标()
  多开道具.召唤兽装备:失去鼠标()
      local 物品 = self:焦点物品()
      if 物品 and 物品.物品  then
          local xx,yy=引擎:取鼠标坐标()
          __UI弹出.道具提示:打开(物品.物品,xx+20,yy+20)
      end
end
function 道具网格:键盘按下(键码, 功能)
     
      self.按下按键=键码
      
end
function 道具网格:键盘弹起(键码, 功能)
      self.按下按键=nil
end



function 道具网格:右键弹起(x, y, a)
        local 物品 = self:焦点物品()
        if 物品 and 物品.物品 and self:焦点()~=0 then
              if self.按下按键 and self.按下按键==1073742049  then
                    self:提取(a)
              elseif 物品.物品.总类==2 or 物品.物品.总类=="坐骑饰品" then
                    self:使用(a)
              end
        end
end

function 道具网格:左键弹起(x, y, a)
  if type(a)~="number" then return end
  if 多开道具.抓取物品ID  then
      请求服务(63,{参数=多开道具.玩家id,文本="格子互换",抓取id = 多开道具.抓取物品ID,放置id = a+多开道具.起点,放置类型 = 多开道具.包裹类型,抓取类型 = 多开道具.抓取类型})
      多开道具:重置抓取()
  else
      多开道具:重置抓取()
      local 物品 = self:选中物品()
      if 物品 and 物品.物品 and self:选中()~=0 then
          if __手机 then
                __UI弹出.道具提示:打开(物品.物品,x+20,y+20,道具网格,"多开道具",a)
          else
              self:拿起(a)
          end
      end
  end
end


function 道具网格:拿起(编号)
      if 编号 and 编号~=0 then
            local 物品 = self.道具网格.子控件[编号]._spr 
            if not 物品 then return end
            鼠标层.附加= 物品
            多开道具.抓取类型 = 多开道具.包裹类型
            多开道具.抓取物品ID = 编号+多开道具.起点
      end
end

function 道具网格:提取(编号)
      if 编号 and 编号~=0 then
            local 物品 = self.道具网格.子控件[编号]._spr 
            if not 物品 then return end
            请求服务(63,{参数=多开道具.玩家id,文本="提取道具",类型=多开道具.包裹类型,道具=编号+多开道具.起点})
      end
end


function 道具网格:使用(编号)
        if 编号 and 编号~=0 then
              local 物品 = self.道具网格.子控件[编号]._spr 
              if not 物品 then return end
              if 多开道具.窗口类型=="主人公" then
                        请求服务(63,{参数=多开道具.玩家id,文本="佩戴装备",类型=多开道具.包裹类型,角色="主角",道具=编号+多开道具.起点})
              elseif 物品.物品.分类 and 物品.物品.分类 <10 and 多开道具.窗口类型=="召唤兽"  and  多开道具.选中编号 then
                        请求服务(63,{参数=多开道具.玩家id,文本="佩戴bb装备",类型=多开道具.包裹类型,角色="bb",道具=编号+多开道具.起点,编号=多开道具.选中编号})
              elseif 物品.物品.总类=="坐骑饰品" and 多开道具.窗口类型=="坐骑" and  多开道具.选中编号  then
                        请求服务(63,{参数=多开道具.玩家id,文本="穿戴坐骑饰品",类型=多开道具.包裹类型,角色="坐骑",道具=编号+多开道具.起点,编号=多开道具.选中编号})
              end
        end
end





local 装备网格 = 多开道具:创建网格("装备网格", 6, 116, 240, 165)
function 装备网格:初始化()
    self:创建格子(50, 50, 5, 130, 3, 2)
end

function 装备网格:左键弹起(x, y, a)
      local 物品=多开道具.道具网格:选中物品()
      if 多开道具.抓取物品ID and 多开道具.抓取类型  and 物品 and 物品.物品 and 物品.物品.总类 == 2 and  物品.物品.分类 <=6 then
          请求服务(63,{参数=多开道具.玩家id,文本="佩戴装备",类型=多开道具.抓取类型,角色="主角",道具=多开道具.抓取物品ID})
          多开道具:重置抓取()
      elseif self.子控件[a]._spr and self.子控件[a]._spr.物品 then
          if __手机 then
                __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,装备网格,"卸下",a)
          else
               self:卸下(a)
          end
    end
end

function 装备网格:右键弹起(x, y, a)
      if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
          self:卸下(a)
      end
end
function 装备网格:卸下(编号)
  if 编号 and 编号~=0 then
      请求服务(63,{参数=多开道具.玩家id,文本="卸下装备",类型=多开道具.包裹类型,角色="主角",道具=编号})
      多开道具:重置抓取()
  end
end

function 装备网格:获得鼠标(x, y,a)
  多开道具.道具网格.焦点编号=nil
  多开道具.灵饰网格:失去鼠标()
      if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr  and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点=nil
      end
      
      self.子控件[a]._spr.焦点=true
      self.焦点=a
      if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not 鼠标层.附加  then
          __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
      end
end
function 装备网格:失去鼠标(x, y)
      for i = 1, #self.子控件 do
          self.子控件[i]._spr.焦点=nil
      end
      self.焦点=nil

end


function 装备网格:置数据(数据)
    for i = 1, #self.子控件 do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,50,50)
        if 数据 and 数据[i] then
            lssj:置物品(数据[i],50,50, nil,true)
        end
        self.子控件[i]:置精灵(lssj)
    end
end




local 灵饰网格 = 多开道具:创建网格("灵饰网格", 6, 61, 250, 55)
function 灵饰网格:初始化()
   self:创建格子(50, 49, 0, 10, 1, 4)

end

function 灵饰网格:获得鼠标(x, y,a)
  多开道具.道具网格.焦点编号=nil
  多开道具.装备网格:失去鼠标()
    if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr  and self.子控件[self.焦点]._spr.焦点 then
        self.子控件[self.焦点]._spr.焦点=nil
    end
    self.子控件[a]._spr.焦点=true
    self.焦点=a
    if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not 鼠标层.附加  then
        __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end
function 灵饰网格:失去鼠标(x, y)
  for i = 1, #self.子控件 do
    self.子控件[i]._spr.焦点=nil
  end
  self.焦点=nil

end

function 灵饰网格:右键弹起(x, y, a)
      if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
          self.子控件[a]._spr.确定=nil
          self:卸下(a)
      end
end



function 灵饰网格:左键弹起(x, y, a)
      local 物品=多开道具.道具网格:选中物品()
      if 多开道具.抓取物品ID and 多开道具.抓取类型 and 鼠标层.附加 and 物品 and 物品.物品 and 物品.物品.总类 == 2 then
            if 多开道具.分类=="灵饰" and  物品.物品.灵饰 then
                请求服务(63,{参数=多开道具.玩家id,文本="佩戴装备",类型=多开道具.抓取类型,角色="主角",灵饰=true,道具=多开道具.抓取物品ID})
            elseif 多开道具.分类=="锦衣" and 物品.物品.分类>=15 and 物品.物品.分类<=17 then
                请求服务(63,{参数=多开道具.玩家id,文本="佩戴装备",类型=多开道具.抓取类型,角色="主角",锦衣=true,道具=多开道具.抓取物品ID})
            end
            多开道具:重置抓取()
      elseif self.子控件[a]._spr and self.子控件[a]._spr.物品 then
              if __手机 then
                    __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,灵饰网格,"卸下",a)
              else
                  self:卸下(a)
              end
         
      end
end


function 灵饰网格:卸下(编号)
    if 编号 and 编号~=0 then
        if 多开道具.分类=="灵饰" then
            请求服务(63,{参数=多开道具.玩家id,文本="卸下装备",类型=多开道具.包裹类型,角色="主角",灵饰=true,道具=编号})
        else
            请求服务(63,{参数=多开道具.玩家id,文本="卸下装备",类型=多开道具.包裹类型,角色="主角",锦衣=true,道具=编号})
        end
        多开道具:重置抓取()
    end
end



function 灵饰网格:置数据(数据)
  for i = 1, #self.子控件 do
      local lssj = __物品格子:创建()
      lssj:置物品(nil,50,50)
      if 数据 and 数据[i] then
          lssj:置物品(数据[i],50,50, nil,true)
      end
      self.子控件[i]:置精灵(lssj)
  end
end

local 召唤兽装备 = 多开道具:创建网格("召唤兽装备", 7, 272, 170, 55)
function 召唤兽装备:初始化()
       self:创建格子(51, 51, 8,8, 1, 3)
  
end

function 召唤兽装备:获得鼠标(x, y,a)
  多开道具.道具网格.焦点编号=nil
        if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr  and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点=nil
        end
        self.子控件[a]._spr.焦点=true
        self.焦点=a
        if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not 鼠标层.附加  then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
        end
end


function 召唤兽装备:失去鼠标(x, y)
  for i = 1, #self.子控件 do
    self.子控件[i]._spr.焦点=nil
  end
  self.焦点=nil

end


function 召唤兽装备:右键弹起(x, y,a)
      if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not 鼠标层.附加  then
        self:卸下(a)
     end
end



function 召唤兽装备:左键弹起(x, y, a)
        if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
              if __手机 then
                    __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,召唤兽装备,"卸下",a)
              else
                    self:卸下(a)
              end
        end
end

function 召唤兽装备:卸下(编号)
    if 编号 and 编号~=0 then
        请求服务(63,{参数=多开道具.玩家id,文本="卸下bb装备",类型=多开道具.包裹类型,角色="bb",道具=编号,编号=多开道具.选中编号})
    end
end

function 召唤兽装备:置数据(数据)
        for i = 1, #self.子控件 do
              local lssj = __物品格子:创建()
              lssj:置物品(nil,50,50)
              if 数据 and 数据[i] then
                  lssj:置物品(数据[i], 50,50, nil,true)
              end
              self.子控件[i]:置精灵(lssj)
        end
end


  local 名称选择 = 多开道具:创建列表("名称选择", 125, 65, 110, 190)
  function 名称选择:初始化()
        self:置文字(标题字体)
        self:置颜色(0,0,0,255)
        self.行间距 = 5
  end
  function 名称选择:重置(data)
      self:清空()
      if not data then data={} end
      self.数据=data
      for i, v in ipairs(data) do
          self:添加(v.名称)
      end
  end
  
  function 名称选择:左键弹起(x, y, i)
      if self.数据[i] and 多开道具.选中编号~=i then
        多开道具.选中编号 = i
        多开道具:重置窗口()
        多开道具:模型重置()
      else
        多开道具.选中编号=nil
      end
     
  end

local 提取按钮 =  多开道具:创建红色按钮("提取", "提取按钮", 245,277,42, 22) 
function 提取按钮:左键弹起(x, y)
          if  多开道具.角色信息.银子>=1 then
            窗口层.文本栏:打开("你确定要把该角色的银两全部提取出来么?",63,{参数=多开道具.玩家id,文本="提取银子"})
          else
            __UI弹出.提示框:打开("#Y/该角色已经没有银子了")
          end
end

local 仓库 =  多开道具:创建红色按钮("仓库", "仓库", 475, 275,42, 22) 
function 仓库:左键弹起(x, y)
      请求服务(63,{参数=多开道具.玩家id,文本="打开仓库",类型=多开道具.包裹类型})
end

local 整理 =  多开道具:创建红色按钮("整理", "整理", 475, 305,42, 22) 
function 整理:左键弹起(x, y)
     请求服务(63,{参数=多开道具.玩家id,文本="整理背包",类型=多开道具.包裹类型})
end

local 格子控件 = 多开道具:创建控件("格子控件", 503,63, 20,210)
local 清空 = 格子控件:创建按钮("清空",0, 175)
function 清空:初始化()
      self:创建按钮精灵(__res:取资源动画("dlzy",0x72116F63),1,"清")
end
function 清空:左键弹起(x, y)
       窗口层.文本栏:打开("你确定要把背包未加锁道具全部清空么，清空道具背包后道具无法找回?",63,{参数=多开道具.玩家id,文本="清空背包"})
end

local 宝宝按钮 = 多开道具:创建控件("宝宝按钮", 5, 60, 250,270)
local 乘骑 =  宝宝按钮:创建红色按钮("乘骑", "乘骑", 10, 220,60,22) --红色
function 乘骑:重置()
      if 多开道具.选中编号 and 多开道具.角色信息.坐骑列表[多开道具.选中编号] then
              self:置禁止(false)
              if  多开道具.角色信息.坐骑~=nil  and ((多开道具.角色信息.坐骑.认证码 and 多开道具.角色信息.坐骑.认证码==多开道具.角色信息.坐骑列表[多开道具.选中编号].认证码) or ( 多开道具.角色信息.坐骑[1]~=nil and 多开道具.角色信息.坐骑列表[多开道具.选中编号].认证码==多开道具.角色信息.坐骑[1].认证码)) then
                self:置文字(60,22,"下骑")
              else
                self:置文字(60,22,"乘骑")
              end
      else
            self:置文字(60,22,"乘骑")
            self:置禁止(true)
      end
end
 
  function 乘骑:左键弹起(x, y)
      if 多开道具.选中编号 and 多开道具.角色信息.坐骑列表[多开道具.选中编号] then
        if  多开道具.角色信息.坐骑~=nil  and ((多开道具.角色信息.坐骑.认证码 and 多开道具.角色信息.坐骑.认证码==多开道具.角色信息.坐骑列表[多开道具.选中编号].认证码) or ( 多开道具.角色信息.坐骑[1]~=nil and 多开道具.角色信息.坐骑列表[多开道具.选中编号].认证码==多开道具.角色信息.坐骑[1].认证码)) then
            请求服务(63,{参数=多开道具.玩家id,文本="角色下骑处理",序列=0})
        else
            请求服务(63,{参数=多开道具.玩家id,文本="角色乘骑处理",序列=多开道具.选中编号})
        end
      end

  end

  local 坐骑属性 =  宝宝按钮:创建红色按钮("属性", "坐骑属性", 85, 220,60,22)
  function 坐骑属性:左键弹起(x, y)
      if 窗口层.坐骑属性.是否可见 then
          窗口层.坐骑属性:置可见(false)
      else
          -- 请求服务(96)
      end
  end

  local 飞行 =  宝宝按钮:创建红色按钮("飞行", "飞行", 160, 220,60,22)
  function 飞行:左键按下(x, y)
    if not 多开道具.选中编号 or 多开道具.选中编号 == 0  then
        __UI弹出.提示框:打开("#Y请选中一个坐骑！")
        return
    end
    if 多开道具.角色信息.坐骑列表[多开道具.选中编号]~=nil and  多开道具.角色信息.坐骑列表[多开道具.选中编号].祥瑞 then 
        -- 请求服务(116)
     else
        __UI弹出.提示框:打开("#Y该坐骑无法设置飞行！")
    end
  end



local 背包控件 = 多开道具:创建控件("背包控件", 255,35, 260,25)
local 背包设置 = {
    {
      name = "道具",
      font = "道 具"
    },
    {
      name = "行囊",
      font = "行 囊"
    },
    {
      name = "任务",
      font = "任 务"
    }


}
for i, v in ipairs(背包设置) do
      local 临时函数 =背包控件:创建红色单选按钮( v.font,v.name, (i-1)*86,0,74,22)
      function  临时函数:左键弹起(x, y)
          if v.name =="道具" then
              if 多开道具.包裹类型 == "行囊" and 多开道具.抓取物品ID and 多开道具.抓取类型  then
                  请求服务(63,{参数=多开道具.玩家id,文本="格子互换1",抓取id=多开道具.抓取物品ID,放置类型="道具",抓取类型=多开道具.抓取类型})
              else
                  请求服务(63,{参数=多开道具.玩家id,文本="打开背包"})
              end
          elseif v.name =="行囊" then
            if 多开道具.包裹类型 == "道具" and 多开道具.抓取物品ID and 多开道具.抓取类型 then
              请求服务(63,{参数=多开道具.玩家id,文本="格子互换1",抓取id=多开道具.抓取物品ID,放置类型="行囊",抓取类型=多开道具.抓取类型})
            else
              请求服务(63,{参数=多开道具.玩家id,文本="索要行囊"})
            end
          elseif v.name =="任务" then
            多开道具:关闭道具()
            return
         end
         多开道具:重置抓取()
         多开道具.包裹类型 = v.name
         多开道具.起点=0
    end
end




for i, v in ipairs(格子设置) do
      local 临时函数 = 格子控件:创建单选按钮(v,0, (i-1)*35)
      function 临时函数:初始化()
            self:创建按钮精灵(__res:取资源动画("dlzy", 0x72116F63),1,i)
      end
      function 临时函数:左键弹起(x, y)
            if v == "一" then
              多开道具.起点 = 0
            elseif v == "二" then
              多开道具.起点 = 20
            elseif v == "三" then
              多开道具.起点 = 40
            elseif v == "四" then
              多开道具.起点 = 60 
            elseif v == "五" then
              多开道具.起点 = 80 
            end
            local 物品={}
            for n = 1, 20 do
              物品[n]=多开道具.角色信息.道具列表[n+多开道具.起点]
            end
            多开道具.道具网格:置数据(物品)
      end
end
local 加锁 = 多开道具:创建按钮("加锁",508,33)
function 加锁:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0x10000135),1)   
end
function 加锁:左键弹起(x, y)
       窗口层.物品加锁:打开()
end


local 转换 = 多开道具:创建按钮("转换",5, 5)
function 转换:初始化()
     self:创建按钮精灵(__res:取资源动画('dlzy',0x49D09C8B),1)
end

function 转换:左键弹起(x, y)
      __res.配置.行囊=0
      多开道具:关闭道具()
      窗口层.新行囊:打开()
      窗口层.灵饰:置可见(false)
      --窗口层.锦衣:置可见(false)
    __res:写出文件( "config.txt", zdtostring(__res.配置))
end



local 关闭 = 多开道具:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    多开道具:关闭道具()
  end











