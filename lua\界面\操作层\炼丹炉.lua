--[[
LastEditTime: 2024-11-06 13:34:31
--]]



local 炼丹炉 = 窗口层:创建窗口("炼丹炉", 0,0, 560, 365)
local 八卦 = {"乾","巽","坎","艮","坤","震","离","兑"}
function 炼丹炉:初始化()

  self:创建纹理精灵(function()
        __res:取资源动画("dlzy", 0x9E956BF6,"图像"):显示(0,0)
        __res:取资源动画("dlzy", 0xE8725FA6,"图像"):显示(0, 0)
      end
    )
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.转动动画=__res:取资源动画("dlzy", 0xAC5A14B2,"动画")
  self.八卦动画=__res:取资源动画("dlzy", 0xE8725FA6,"动画")

  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
  
end



function 炼丹炉:打开(数据)
      self:置可见(not self.是否可见)
      if not self.是否可见 then
          return
      end
      self.下注时间 =数据.时间.下注时间
      self.转盘时间 =数据.时间.转盘时间
      self.停止时间 =数据.时间.停止时间
      self.道具网格:置数据(数据.物品数据.道具)
      self:时间变动()
end



function 炼丹炉:刷新(数据)
        self.下注时间 =数据.下注时间
        self.转盘时间 =数据.转盘时间
        self.停止时间 =数据.停止时间
        self:时间变动()
end



function 炼丹炉:更新数据(数据)
      self.灵气 = 数据.灵气
      self.下注信息 = 数据.数据
      if 数据.物品价格 then
        self.物品价格 =  数据.物品价格
      end
      self.最大奖励 = {}
      if self.下注信息 ~= nil then
          self.最大奖励[1],self.最大奖励[2],self.最大奖励[3] = self:取最大奖励(self.下注信息)
      end
      if 数据.物品数据 then
          self.道具网格:置物品(数据.物品数据.道具)
      end
end

function 炼丹炉:开奖(数据)
    self.开奖数据 = nil
    for i=1,#八卦 do
        if 八卦[i] == 数据.吉位 then
            self.开奖数据 = i
        end
    end
    self.闪烁动画=__res:取资源动画("dlzy", 0xAC5A14B2):取精灵(self.开奖数据)
end

function 炼丹炉:时间变动()
       self.顶部字体=文本字体:置颜色(255,255,255,255):取精灵("炼丹结束")
        if self.下注时间 and self.下注时间 >0  then
            self.开始转动 = nil
            self.开奖数据 = nil
            self.停止转动 = nil
            self.顶部字体=文本字体:置颜色(255,255,255,255):取精灵("距炼丹开始还有"..self.下注时间.."秒")
        elseif self.转盘时间 and  self.转盘时间>0 and self.转盘时间<15 then
                  if self.转盘时间 <=15 and self.转盘时间 >=13  then
                        self.转动动画:置帧率(0.08)
                        self.八卦动画:置帧率(0.07)
                     
                  elseif self.转盘时间 <13 and self.转盘时间 >=11 then
                       self.转动动画:置帧率(0.11)
                       self.八卦动画:置帧率(0.08)
                  elseif self.转盘时间 <11 and self.转盘时间 >=9 then
                        self.转动动画:置帧率(0.14)
                        self.八卦动画:置帧率(0.09)
                  elseif self.转盘时间 <9 and self.转盘时间 >=7 then
                        self.转动动画:置帧率(0.17)
                        self.八卦动画:置帧率(0.1)
                  elseif self.转盘时间 <7 and self.转盘时间 >=5 then
                        self.转动动画:置帧率(0.2)
                        self.八卦动画:置帧率(0.11)
                  elseif self.转盘时间 <5 and self.转盘时间 >=3 then
                      self.转动动画:置帧率(0.23)
                      self.八卦动画:置帧率(0.125)
                  else
                      self.转动动画:置帧率(0.26)
                      self.八卦动画:置帧率(0.125)
                  end
                  if self.停止转动 then
                      self.开始转动 = nil 
                      self.顶部字体=文本字体:置颜色(255,255,255,255):取精灵("炼丹结束")
                  else
                      self.开始转动 = true
                      self.顶部字体=文本字体:置颜色(255,255,255,255):取精灵("炼丹进行中...")
                  end
        elseif self.停止时间 and self.停止时间>0 and self.停止时间<6 then
                if self.停止转动 then
                    self.开始转动 = nil 
                    self.顶部字体=文本字体:置颜色(255,255,255,255):取精灵("炼丹结束")
                else
                    self.顶部字体=文本字体:置颜色(255,255,255,255):取精灵("炼丹进行中...")
                end
        end
end

function 炼丹炉:更新(dt)
        if self.开始转动 then
            self.转动动画:更新(dt)
            self.八卦动画:更新(dt)
        
            if self.开奖数据 and self.转动动画.当前帧==self.开奖数据 and not self.停止转动  then
                self.停止转动=true
                self.开始转动 = nil 
            end
           
          
        end


end

function 炼丹炉:显示(x,y)
          if self.顶部字体 then
              self.顶部字体:显示(x+40+(200-self.顶部字体.宽度)//2,y+40)
          end
         if self.开始转动 then
              self.转动动画:显示(x,y)
              self.八卦动画:显示(x,y)
              
         elseif self.闪烁动画  then
                self.闪烁动画:显示(x,y)
         end
        


end







local 道具网格=炼丹炉:创建道具网格("道具网格",270,59)

function 道具网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
              __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
          end
end



function 炼丹炉:取最大奖励(数据)
	local 数组 = {[1]=0,[2]=0,[3]=0}
	local 信息= 0
	local 临时信息 = {}
	for i,v in pairs(数据) do
		临时信息[#临时信息+1] = {名称=i,数额=v}
	end
	table.sort(临时信息,function(a,b) return a.数额>b.数额 end )
	信息 = 临时信息[1].数额
	if math.floor(信息/1000) > 0 then
	    数组[1] = math.floor(信息/1000)
	    信息 = 信息 - 数组[1]*1000
	    数组[2] = math.floor(信息/100)
	    信息 = 信息 - 数组[2]*100
	    数组[3] = math.floor(信息/10)
	elseif math.floor(信息/100) > 0 then
		数组[2] = math.floor(信息/100)
	    信息 = 信息 - 数组[2]*100
	    数组[3] = math.floor(信息/10)
	elseif math.floor(信息/10) > 0 then
	    数组[3] = math.floor(信息/10)
	end
	return 数组[1],数组[2],数组[3]
end



local 注入按钮=炼丹炉:创建红色按钮("注 入","注入按钮",440,280,57,22)
local 关闭按钮=炼丹炉:创建红色按钮("关 闭","关闭按钮",440,307,57,22)
local 关闭 = 炼丹炉:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  炼丹炉:置可见(false)
end

