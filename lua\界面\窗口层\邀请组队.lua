--[[
LastEditTime: 2024-09-21 19:31:42
--]]
local 邀请组队 = 窗口层:创建窗口("邀请组队", 0, 0, 305, 84)
function 邀请组队:初始化()
        self.背景 = require("SDL.精灵")(0, 0, 0, 305, 84):置颜色(0, 0, 0, 210)
        self.头像背景=__res:取资源动画("dlzy", 0x360B8373,"精灵")
        self.可初始化=true
        if __手机 then
          self:置坐标(引擎.宽度 - 330, 引擎.高度 - 195)
        else
          self:置坐标(引擎.宽度 - 330, 引擎.高度 - 160)
        end
end
local 关闭控件=邀请组队:创建控件("关闭控件", 0, 0, 305, 84)
function 关闭控件:左键弹起() 
          if __手机 then
                if 邀请组队.出现时间 and os.time()<=邀请组队.出现时间 then
                    if 邀请组队.队伍状态=="申请入队" and __主显.主角.是否队长 then
                          请求服务(4018,{队伍id=邀请组队.队伍id,对方id=邀请组队.邀请id})
                    elseif 邀请组队.队伍状态=="邀请入队" then
                      请求服务(4017,{队伍id=邀请组队.队伍id,邀请id=邀请组队.邀请id})
                    end
                end
                邀请组队.出现时间=nil
                邀请组队:置可见(false)
          end
end
function 关闭控件:右键弹起() 
        if 邀请组队.出现时间 and os.time()<=邀请组队.出现时间  then
            if 邀请组队.队伍状态=="申请入队" and __主显.主角.是否队长 then
                  请求服务(4018,{队伍id=邀请组队.队伍id,对方id=邀请组队.邀请id})
            elseif 邀请组队.队伍状态=="邀请入队" then
                请求服务(4017,{队伍id=邀请组队.队伍id,邀请id=邀请组队.邀请id})
            end
            
        end
        邀请组队.出现时间=nil
        邀请组队:置可见(false)
end

function 邀请组队:更新(dt) 
          if self.出现时间 and os.time()>self.出现时间  then
              self:置可见(false)
          end
end
function 邀请组队:显示(x, y)      
        self.背景:显示(x, y)
        if  self.头像背景 then
          self.头像背景:显示(x+10,y+10)
        end
        if  self.头像 then
          self.头像:显示(x+10,y+10)
        end


end
function 邀请组队:打开(内容)
    self:置可见(true)
    self:刷新(内容)
  
end
function 邀请组队:刷新(内容)
      self.出现时间=os.time()+600
      self.队伍id=内容.队长id
		  self.邀请id=内容.邀请id
      self.名称=内容.名称
      self.等级=内容.等级
      self.队伍状态 =内容.状态
      local lssj = __头像格子:创建()
      lssj:置头像(内容.模型,50,50) 
      self.头像=lssj
      self.申请文本:清空()
      if self.队伍状态=="申请入队" and __主显.主角.是否队长  then
          self.申请文本:置文本("#G"..self.名称.."#Y("..self.等级.."级)#W申请加入你的队伍，是否接受？")
      elseif self.队伍状态=="邀请入队" then
        self.申请文本:置文本("#G"..self.名称.."#Y("..self.等级.."级)#W邀请你加入他的队伍，是否接受？")
      end
      
     
end

local 申请文本 = 邀请组队:创建文本("申请文本", 70, 5, 220, 45)
function 申请文本:初始化()
    self:置文字(文本字体)
end

local 接受 =邀请组队:创建红色按钮("接受", "接受", 65, 53,100,22)
function 接受:左键弹起()
        if 邀请组队.出现时间 and os.time()<=邀请组队.出现时间  then
            if 邀请组队.队伍状态=="申请入队" and __主显.主角.是否队长 then
                  请求服务(4016,{队伍id=邀请组队.队伍id,对方id=邀请组队.邀请id})
            elseif 邀请组队.队伍状态=="邀请入队" then
                请求服务(4015,{队伍id=邀请组队.队伍id,邀请id=邀请组队.邀请id})
            end    
        end
        邀请组队.出现时间=nil
        邀请组队:置可见(false)
end
local 拒绝 =邀请组队:创建红色按钮("拒绝", "拒绝", 185, 53,100,22) 
function 拒绝:左键弹起()
        if 邀请组队.出现时间 and os.time()<=邀请组队.出现时间  then
              if 邀请组队.队伍状态=="申请入队" and __主显.主角.是否队长 then
                    请求服务(4018,{队伍id=邀请组队.队伍id,对方id=邀请组队.邀请id})
              elseif 邀请组队.队伍状态=="邀请入队" then
                  请求服务(4017,{队伍id=邀请组队.队伍id,邀请id=邀请组队.邀请id})
              end
        end
        邀请组队.出现时间=nil
        邀请组队:置可见(false)
end

