

local 世界boss = 窗口层:创建窗口("世界boss", 0,0, 640, 480)

function 世界boss:初始化()
        self:创建纹理精灵(function()
            置窗口背景("世界boss", 0, 0, 640, 480, true):显示(0, 0)
            __res:取资源动画("pic", "boss1.png","图片"):显示(10,60)
            __res:取资源动画("pic", "boss2.png","图片"):显示(330,90)
            __res:取资源动画("pic", "boss3.png","图片"):显示(330,290)
            __res:取资源动画("pic", "bossjyt.png","图片"):显示(380,65)
            标题字体:置颜色(__取颜色("红色")):取图像("BOSS血条:"):显示(308,65)
            标题字体:置颜色(255,255,255,255):取图像("活动状态显示:              显示本队伍信息"):显示(330,260)
        end
        )
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-39, 4)
        else
              self.关闭:置大小(16,16)
              self.关闭:置坐标(self.宽度-38, 4)
        end
end

function 世界boss:打开(内容)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.图像 = nil
  self:刷新(内容)
end 
function 世界boss:刷新(内容)
        self.数据=内容
        self.图像=self:创建纹理精灵(function()
                if self.数据.开启 then
                      self.boss血量:置位置(内容.气血.当前 /self.数据.气血.上限 *100)
                      标题字体:置颜色(__取颜色("绿色")):取图像("                    进行中"):显示(330,260)
                else
                      标题字体:置颜色(__取颜色("红色")):取图像("                    已结束"):显示(330,260)
                end 
                if self.数据.排行 ~= nil then
                    for i = 1, 10 do
                        if self.数据.排行[i] then
                          说明字体:置颜色(__取颜色("红色")):取图像(self.数据.排行[i].名称):显示(20, 85+math.floor((i-1)*31.5))
                          说明字体:置颜色(__取颜色("蓝色")):取图像(self.数据.排行[i].等级):显示(105, 85+math.floor((i-1)*31.5))
                          说明字体:置颜色(__取颜色("绿色")):取图像(self.数据.排行[i].门派):显示(160, 85+math.floor((i-1)*31.5))
                          说明字体:置颜色(0,0,0,255):取图像(self.数据.排行[i].伤害):显示(230, 85+math.floor((i-1)*31.5))
                        end
                    end
                end
        end,1)
end




function 世界boss:显示(x,y)
  if self.图像 then
    self.图像:显示(x,y)
  end
end





local boss血量 = 世界boss:创建我的进度(__res:取资源动画("pic", "jindu.png","图片"):拉伸(240,20),"boss血量", 380, 65, 240, 20)


function boss血量:更新(dt)
      self.图像=self:创建纹理精灵(function()
          if 世界boss.数据 and 世界boss.数据.开启 then
            local  血量比例= math.floor(世界boss.数据.气血.当前/世界boss.数据.气血.上限*100) 
            local 显示文字 = 世界boss.数据.气血.当前.."/"..世界boss.数据.气血.上限
            if  血量比例>=90 then
                标题字体:置颜色(__取颜色("紫色")):取描边图像(显示文字):显示(120-标题字体:取宽度(显示文字)//2,2) 
            elseif 血量比例>=70 and 血量比例<90 then
                标题字体:置颜色(__取颜色("蓝色")):取描边图像(显示文字):显示(120-标题字体:取宽度(显示文字)//2,2) 
            elseif 血量比例>=30 and 血量比例<70 then
                标题字体:置颜色(__取颜色("绿色")):取描边图像(显示文字):显示(120-标题字体:取宽度(显示文字)//2,2) 
            else
                标题字体:置颜色(__取颜色("红色")):取描边图像(显示文字):显示(120-标题字体:取宽度(显示文字)//2,2) 
            end
          end
      end,1,240,20)
end


function boss血量:显示(x,y)
  if self.图像 then
     self.图像:显示(x,y)
  end

end



local 领取奖励 = 世界boss:创建红色按钮("领取奖励", "领取奖励", 350,442, 80, 30)

function 领取奖励:左键弹起(x, y)
  请求服务(57)
  end

local 开启挑战 = 世界boss:创建红色按钮("开启挑战", "开启挑战", 520,442, 80, 30)
function 开启挑战:左键弹起(x, y)
   请求服务(56)
    end
  

local 关闭 = 世界boss:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  世界boss:置可见(false)
end






