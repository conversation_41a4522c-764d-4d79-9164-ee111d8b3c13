--[[
Author: GGELUA
Date: 2024-10-01 23:19:16
Last Modified by: GGELUA
Last Modified time: 2024-10-18 23:34:52
--]]
-- <AUTHOR> GGELUA
-- @Date                : 2022-04-10 14:32:28
-- @Last Modified by    : baidwwy
-- @Last Modified time  : 2022-11-02 20:00:37

local GGF = require('GGE.函数')
local _HASH = require('gmx.fls.hash')

local _FLS = {}

local function _loadfls(name, file)
    local fls = require('gmx.fls')(file)
    if fls then
        if _FLS[name] then
            fls:GetList(_FLS[name])
        elseif fls then
            _FLS[name] = fls:GetList()
        end
        return true
    end
end

local fls = class('fls')

function fls:初始化(path)
    for file in GGF.遍历目录(path) do
        if file:sub(-3) == 'fls' then
            local name = GGF.取文件名(file, true)

            if not _loadfls(name, file) then
            --    print(file)
            end
        end
    end
end

function fls:取数据(path)
    local r = path
    if type(path) == 'string' then
        r = self:是否存在(path)
    end
    if type(r) == 'table' and r.fls then
        return r.fls:GetData(r.id)
    end
end

function fls:是否存在(path)
    local name, hash = gge.utf8togbk(path):match('(%w+)/(.+)')
    if name and _FLS[name] and hash then
        if hash:sub(1, 2) == '0x' then --shape/0x00000000.igs
            hash = tonumber(hash:match('(.+)%.%a%a%a'))
        else
            hash = _HASH(hash)
        end
        return _FLS[name][hash]
    end
end

return fls
