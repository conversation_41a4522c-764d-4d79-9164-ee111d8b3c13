{

	"GGE类_初始化": {
		"scope": "lua",
		"prefix": "class",
		"description": "GGE类",
		"body": ["local ${1:$TM_FILENAME_BASE} = class('${1}')",
	"",
		"function ${1}:初始化(${2})",
			"${3}",
		"end",
	"",
		"function ${1}:更新(dt)",
			"",
		"end",
	"",
		"function ${1}:显示(x,y)",
			"",
		"end",
	"",
		"return ${1}"]
		
	},

	"GGE类_方法": {
		"scope": "lua",
		"prefix": "classfunction",
		"description": "GGE类方法",
		"body": [
		"function ${1:$TM_FILENAME_BASE}:${2:名称}(${3})",
			"${4}",
		"end",
		]
	},

	"GGE类_类名": {
		"scope": "lua",
		"prefix": "class",
		"description": "GGE类",
		"body": ["local ${1:$TM_FILENAME_BASE} = class('${1}')",
	"",
		"function ${1}:${1}(${2})",
			"${3}",
		"end",
	"",
		"function ${1}:更新(dt)",
			"",
		"end",
	"",
		"function ${1}:显示(x,y)",
			"",
		"end",
	"",
		"return ${1}"]
		
	}
}