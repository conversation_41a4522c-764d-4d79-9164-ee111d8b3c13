--[[
LastEditTime: 2025-06-24 13:03:21
--]]
--[[
LastEditTime: 2025-06-23 21:38:28
--]]
--[[
LastEditTime: 2025-06-18 22:00:44
--]]

local tt = os.clock()
print('编译文件')
编译目录('ggelua',true)
编译目录('./lua',true)

if arg[1] == 'Windows' then
    print('复制文件')
    for path,rel in 遍历目录('lib') do
        local hash = 取文件名(path)
        if 复制文件(path, './build/lib/'..hash) then
            print('Copy ->',path,'=>','./build/lib/'..hash)
        end
    end
    -- 复制文件('SDL2.dll', './build/SDL2.dll')
    -- 复制文件('SDL_image.dll', './build/SDL_image.dll')
    -- 复制文件('SDL_mixer.dll', './build/SDL_mixer.dll')
    -- 复制文件('SDL_ttf.dll', './build/SDL_ttf.dll')
    -- 复制文件('lua54.dll', './build/lua54.dll')
    -- 复制文件('ggelua.dll', './build/ggelua.dll')  

    
    联接目录('./assets', './build/assets')
    写出Windows('./build/MHXY.exe', '.vscode/MY.ico')---pc名称 MHXY

    --写出单文件('./build/MHXY.exe', nil,'.vscode/MY.ico')
elseif arg[1] == 'Android' then
     写出Android('mygame','梦灵西游','.vscode/ico.png')--安卓名称  龙途西游
elseif arg[1] == 'iOS' then
        写出iPhoneOS('mygame', '梦灵西游', '.vscode/ico.png') --名称 龙途西游
-- elseif arg[1] == 'HOTUPDATE' then
--     print('生成热更文件 ggelua.com')
--     print('目标路径: ' .. arg[2] .. '/ggelua')
--     -- 编译所有脚本到ggelua.com
--     编译目录('ggelua',true)
--     编译目录('./lua',true)
--     写出脚本(arg[2] .. '/ggelua')
elseif arg[1] == 'GGELUA' then
    -- 生成ggelua文件（无扩展名）
    print('生成热更新文件: ggelua')
    print('目标路径: ' .. arg[2] .. '/ggelua')
    编译目录('ggelua',true)
    编译目录('./lua',true)
    写出脚本(arg[2] .. '/ggelua')
elseif arg[1] == 'GGELUA_COM' then
    -- 生成ggelua.com文件（带扩展名）
    print('生成热更新文件: ggelua.com')
    print('目标路径: ' .. arg[2] .. '/ggelua.com')
    编译目录('ggelua',true)
    编译目录('./lua',true)
    写出脚本(arg[2] .. '/ggelua.com')
end
print('编译完成\n用时:' .. os.clock() - tt .. '秒')



