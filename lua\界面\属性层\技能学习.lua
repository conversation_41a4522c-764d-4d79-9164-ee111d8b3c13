
local 技能学习 = 窗口层:创建窗口("技能学习", 0, 0, 400, 480)

function 技能学习:初始化()
  self:创建纹理精灵(function()
        置窗口背景("技能学习", 0, 0, 400, 480, true):显示(0, 0)
        蓝白标题背景(180, 315, true):显示(15, 35) 
        蓝白标题背景(180, 210, true):显示(205, 35)
        取白色背景(0, 0, 180, 100, true):显示(205, 250)
        标题字体:置颜色(255, 255, 255, 255)
        标题字体:取图像("技能名称"):显示(40,40)
        标题字体:取图像("等 级"):显示(140,40)
        标题字体:取图像("相关法术"):显示(260,40)
        文本字体:置颜色(255, 255, 255, 255)
        文本字体:取图像("可用经验"):显示(15,360)
        文本字体:取图像("升级经验"):显示(205,360)
        文本字体:取图像("可用金钱"):显示(15,385)
        文本字体:取图像("升级金钱"):显示(205,385)
        文本字体:取图像("存    款"):显示(15,410)
        文本字体:取图像("储 备 金"):显示(205,410)
        取输入背景(0, 0, 120, 22):显示(75,357)
        取输入背景(0, 0, 120, 22):显示(75,382)
        取输入背景(0, 0, 120, 22):显示(75,407)
        取输入背景(0, 0, 120, 22):显示(265,357)
        取输入背景(0, 0, 120, 22):显示(265,382)
        取输入背景(0, 0, 120, 22):显示(265,407)
      end
    )
  self.可初始化=true
  self.焦点精灵=require('SDL.精灵')(0, 0, 0, 170, 40):置颜色(255, 255, 0, 128)
  self.选中精灵=require('SDL.精灵')(0, 0, 0, 170, 40):置颜色(0, 0, 240, 128)
  self.技能={}
  for i = 1, 7 do
      self.技能[i]=__技能格子:创建()
      self.技能[i]:置数据()
      self.技能[i].矩形=require("SDL.矩形")(0,0,170,40)
  end
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
end





function 技能学习:打开()
      if 角色信息.门派 == "无门派" then
          __UI弹出.提示框:打开("#Y拜入门派才可以学习师门技能")
          return false
      end
      self:置可见(not self.是否可见)
      if not self.是否可见 then
          return
      end
      self.选中 = nil
      self.焦点 = nil
      self.技能文本:清空()
      self:显示设置()
end



function 技能学习:显示设置()
      for i = 1, 7 do
          self.技能[i]:置数据()
          if 角色信息.师门技能[i] then
              self.技能[i]:置数据(角色信息.师门技能[i].名称)
              self.技能[i].名称图片= 文本字体:置颜色(0,0,0,255):取精灵(角色信息.师门技能[i].名称)
              self.技能[i].等级图片= 文本字体:置颜色(0,0,0,255):取精灵(角色信息.师门技能[i].等级.."/180")
          end
      end
      self:学习设置()
end

function 技能学习:学习设置()
      self.图像 =nil
      if self.选中 then
          self.图像=self:创建纹理精灵(function()
            文本字体:置颜色(0,0,0,255):取图像(角色信息.当前经验):显示(80,2)
            文本字体:置颜色(__取银子颜色(角色信息.银子)):取图像(角色信息.银子):显示(80,25)
            文本字体:置颜色(__取银子颜色(角色信息.存银)):取图像(角色信息.存银):显示(80,50)
            文本字体:置颜色(0,0,0,255):取图像(技能消耗.经验[角色信息.师门技能[技能学习.选中].等级 + 1]):显示(270,2)
            文本字体:置颜色(__取银子颜色(技能消耗.金钱[角色信息.师门技能[技能学习.选中].等级 + 1])):取图像(技能消耗.金钱[角色信息.师门技能[技能学习.选中].等级 + 1]):显示(270,25)
            文本字体:置颜色(__取银子颜色(角色信息.储备)):取图像(角色信息.储备):显示(270,50)
          end,1,nil,80)
          self.包含列表:置数据()
          self.技能文本:清空()
          self.技能文本:置文本("#B" .. self.技能[self.选中].数据.名称)
          self.技能文本:置文本("#K" .. (self.技能[self.选中].数据.介绍 or "无效果"))
      end


end






function 技能学习:左键弹起(x,y)
  self.选中=nil
  if self.技能 then
      for i = 1, 7 do
          if self.技能[i].数据 and self.技能[i].矩形 and self.技能[i].矩形:检查点(x, y)  then
              self.选中=i
              self:学习设置()
              if __手机 then
                  __UI弹出.技能详情:打开(self.技能[i].数据,x+20,y+20)
              end
          end
      end
  end
end

function 技能学习:获得鼠标(x,y)
      self.焦点=nil
      for i = 1, 7 do
          if self.技能[i].数据 and self.技能[i].矩形:检查点(x, y)  then
              self.焦点=i
              __UI弹出.技能详情:打开(self.技能[i].数据,x+20,y+20)
          end
      end
end


function 技能学习:失去鼠标(x,y)
    self.焦点=nil
end






function 技能学习:显示(x,y)
    for i = 1, 7 do
        if self.技能[i].数据 then
            if self.选中 and self.选中==i then
                  self.选中精灵:显示(x+20,y+58+(i-1)*41)
            end
            if self.焦点 and self.焦点==i then
                  self.焦点精灵:显示(x+20,y+58+(i-1)*41)
            end
            self.技能[i].矩形:置坐标(x+20,y+60+(i-1)*41)
            self.技能[i].名称图片:显示(x+65,y+72+(i-1)*41)
            self.技能[i].等级图片:显示(x+185-self.技能[i].等级图片.宽度,y+72+(i-1)*41)
            self.技能[i]:显示(x+20,y+60+(i-1)*41)
        end
    end
    if self.图像 then
        self.图像:显示(x,y+360)
    end
end



local 包含列表=技能学习:创建列表("包含列表", 210,65,165,175)

local 滑块=技能学习:创建竖向滑块("包含滑块",375,60,10,185,true)
包含列表:绑定滑块(滑块.滑块)

function 包含列表:初始化()
      self.行高度 = 32
      self.行间距 = 4
end

function 包含列表:置数据()
    self:清空()
    if 技能学习.选中 and 角色信息.师门技能[技能学习.选中] then
         for i, v in ipairs(角色信息.师门技能[技能学习.选中].包含技能) do
              self:添加():创建纹理精灵(
              function ()
                local lxxs = 取技能(v.名称)
                if v.学会 then
                    __res:取资源动画(lxxs[6], lxxs[7],"图像"):拉伸(32,32):显示(0,0)
                else
                      __res:取资源动画(lxxs[6], lxxs[7],"图像"):拉伸(32,32):到灰度():显示(0,0)
                end
                文本字体:置颜色(0,0,0,255):取图像(v.名称):显示(37,10)
              end
            )

         end
    end
end
function 包含列表:获得鼠标(x,y,a)
  if 技能学习.选中 and 角色信息.师门技能[技能学习.选中] and 角色信息.师门技能[技能学习.选中].包含技能[a] then
       __UI弹出.技能详情:打开(角色信息.师门技能[技能学习.选中].包含技能[a],x+20,y+20)
  end

end

function 包含列表:左键弹起(x,y,a)
      if 技能学习.选中 and 角色信息.师门技能[技能学习.选中] and 角色信息.师门技能[技能学习.选中].包含技能[a] and __手机  then
          __UI弹出.技能详情:打开(角色信息.师门技能[技能学习.选中].包含技能[a],x+20,y+20)
      end
end

local 技能文本 = 技能学习:创建文本("技能文本", 210, 255, 170, 90)

local 学习一次 = 技能学习:创建红色按钮("学习一次","学习一次",70,445,74,22)
function 学习一次:左键弹起(x,y)
    if 技能学习.选中 then
        请求服务(3711,{序列=技能学习.选中})
    end 
end

local 学习十次 = 技能学习:创建红色按钮("学习十次","学习十次",260,445,74,22)
function 学习十次:左键弹起(x,y)
  if 技能学习.选中 then
      请求服务(3711.1,{序列=技能学习.选中})
  end 
end


local 关闭 = 技能学习:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  技能学习:置可见(false)
end





