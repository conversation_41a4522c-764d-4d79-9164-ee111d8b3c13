


local  大地图d = 窗口层:创建窗口("大地图d",0, 0, 670, 510)
function  大地图d:初始化()
  self:创建纹理精灵(function()
              取黑色背景(0, 0, 670, 510, true):显示(0, 0)
              __res:取资源动画('dlzy',0xE9EC7A25,"图像"):显示(15, 15)
            end
          )

    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true

    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
end

function  大地图d:打开(传送)
    self:置可见(true)
    self.超级传送=nil
    if 传送 then
      self.超级传送=传送
    end
    
end



local 龙窟 = 大地图d:创建按钮("龙窟", 51+15, 166+15)
function 龙窟:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xA0B51368), 1)
end
function 龙窟:左键弹起(x, y)
  窗口层.世界小地图:打开(1174,大地图d.超级传送)
  大地图d:置可见(false)
end

local 凤巢 = 大地图d:创建按钮( "凤巢", 329+15, 145+15)
function 凤巢:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x13659C02), 1)
end
function 凤巢:左键弹起(x, y)
  窗口层.世界小地图:打开(1174,大地图d.超级传送)
  大地图d:置可见(false)
end
local 女娲神殿 = 大地图d:创建按钮( "女娲神殿", 160+15, 21+15)
function 女娲神殿:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xEDEEF9AF), 1)
end
function 女娲神殿:左键弹起(x, y)

  窗口层.世界小地图:打开(1201,大地图d.超级传送)
  大地图d:置可见(false)
end

local 关闭 = 大地图d:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  大地图d:置可见(false)
end



