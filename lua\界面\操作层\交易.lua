local 交易 = 窗口层:创建窗口("交易", 0,0, 500, 350)
local lsb = {
  "现有现金",
  "给予银两"
}
function 交易:初始化()
   self:创建纹理精灵(function()
    置窗口背景("交易", 0, 0, 500, 350, true):显示(0, 0)
    文本字体:置颜色(255,255,255,255)
    文本字体:取图像("已方支付银两"):显示(10, 36)
    取输入背景(0, 0, 115, 23):显示(100, 32)
    文本字体:取图像("你正在和"):显示(10, 155)
    文本字体:取图像("交易"):显示(185, 155)
    文本字体:取图像("等级"):显示(10, 185)
    文本字体:取图像("关系"):显示(125, 185)
    文本字体:取图像("对方支付银两"):显示(10, 225)
    取输入背景(0, 0, 115, 23):显示(100, 222)
    文本字体:取图像("确认"):显示(188, 95)
    文本字体:取图像("确认"):显示(188, 285)
   end
 )
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  self.禁止右键=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
end






function 交易:打开(名称,等级,类型)
      self:置可见(true)
      self.类型="道具"
      self.对象名称=名称
	    self.对象等级=等级 or 0
	    self.对象类型=类型 or "陌生人"
      self.道具列表=table.copy(_tp.道具列表)
      self.宝宝={}
      self.格子={}
      self.对方物品={}
      self.材料物品={}
      self.己方银两=0
      self.对方银子=0
      self.对方宝宝={}
      self.锁定状态=false
      self.对方开关=false
      self.银两:置数值(0)
      self.道具网格:置数据()
      self.自己确认:置选中(self.锁定状态)
      self.对方确认:置选中(self.对方开关)
      
      self:显示设置()

end


function 交易:设置我方数据(数据)
  if 数据.锁定 then
      self.锁定状态=true
    else
      self.锁定状态=false
    end
    self.自己确认:置选中(self.锁定状态)
  end




function 交易:设置对方数据(数据)
  if 数据.锁定 then
        self.对方银子=数据.银子
        for n=1,3 do
          self.对方物品[n]=table.copy(数据.道具[n]) 
        end
        self.对方宝宝=数据.bb
        self.对方开关=true
   else
        self.对方物品={}
        self.对方银子=0
        self.对方宝宝={}
        self.对方开关=false
   end
   self:银两设置()
   self.对方确认:置选中(self.对方开关)
   self.对方列表:置数据()
end





function 交易:显示设置()
    self[self.类型]:置选中(true)
    self.道具网格:置可见(false)
    self.材料网格:置可见(false)
    self.对方网格:置可见(false)
    self.名称选择:置可见(false)
    self.名称滑块:置可见(false)
    self.自己列表:置可见(false)
    self.对方列表:置可见(false)
    self.道具网格:置物品(self.道具列表)
    self.材料网格:置物品(self.材料物品)
    self.对方网格:置物品(self.对方物品)
    self.名称选择:置数据()
    self.自己列表:置数据()
    self.对方列表:置数据()
    self.图像 = self:创建纹理精灵(function()
      文本字体:置颜色(__取颜色("黄色"))
      文本字体:取图像(self.对象名称):显示(70+(120-文本字体:取宽度(self.对象名称))//2, 155)
      文本字体:取图像(self.对象等级):显示(55, 185)
      文本字体:取图像("陌生人"):显示(165, 185)


        if self.类型=="道具" then
          local xx=0
          local yy = 0
          for i = 1, 6 do
              取输入背景(0, 0, 50, 23):显示(10+xx*60, 120+yy*190)
              xx=xx+1
              if xx>=3 then
                xx=0
                yy=yy+1
              end
          end
           self.道具网格:置可见(true)
           self.材料网格:置可见(true)
           self.对方网格:置可见(true)
        else
            取白色背景(0, 0, 260, 240, true):显示(225,63)  
            self.名称选择:置可见(true)
            self.名称滑块:置可见(true)
            self.自己列表:置可见(true)
            self.对方列表:置可见(true)


        end
    end,1
  )
    self:银两设置()

end


function 交易:银两设置()
      self.银两:置颜色(__取银子颜色(self.己方银两))
      if self.己方银两 ~= self.银两:取数值() then
          self.银两:置数值(self.己方银两)
      end
    self.对方网格:置物品(self.对方物品)
    self.图像2 = self:创建纹理精灵(function()

     if self.对方开关 then
        文本字体:置颜色(__取银子颜色(self.对方银子)):取图像(self.对方银子):显示(105, 225)
     end
     
      if self.类型=="道具" then
        for n=1,3 do
            if self.材料物品[n] and self.材料物品[n].数量~=nil then 
                  文本字体:置颜色(0,0,0,255):取图像(self.材料物品[n].数量):显示(10+(25-文本字体:取宽度(self.材料物品[n].数量)//2)+(n-1)*60, 123)
            end
            if  self.对方物品  and self.对方物品[n] and self.对方物品[n].数量~=nil  then 
                  文本字体:置颜色(0,0,0,255):取图像(self.对方物品[n].数量):显示(10+(25-文本字体:取宽度(self.对方物品[n].数量)//2)+(n-1)*60, 315)
            end
        end
      end
    end,1
  )


end

function 交易:显示(x, y)
  if self.图像 then
      self.图像:显示(x, y)
  end
  if self.图像2 then
      self.图像2:显示(x, y)
  end
end


function 交易:关闭界面(发送)
     if not 发送 then
          请求服务(3719)
     end
      self.类型="道具"
      self.对象名称=""
	    self.对象类型="陌生人"
      self.宝宝={}
      self.格子={}
      self.对方物品={}
      self.材料物品={}
      self.道具列表={}
      self.对象等级=0
      self.己方银两=0
      self.对方银子=0
      self.对方宝宝={}
      self.锁定状态=false
      self.对方开关=false
      self.银两:置数值(0)
      self.道具网格:置数据()
      self.自己确认:置选中(false)
      self.对方确认:置选中(false)
      self:置可见(false)
  end


function 交易:右键弹起(x, y)
        self:关闭界面()
end

local 银两 = 交易:创建文本输入("银两",105,36, 100, 18)
function 银两:初始化()
  self:取光标精灵()
  self:置限制字数(12)
  self:置颜色(0, 0, 0, 255)
  self:置模式(2)
end

function 银两:输入事件()
      if  not 交易.锁定状态 then
          交易.己方银两 = self:取数值()
      end
      交易:银两设置()
end



local 自己列表 = 交易:创建列表("自己列表",10,65,170,85)
function 自己列表:初始化()
      self.焦点精灵=nil
      self.选中精灵=nil
      self.行高度=23
      self.行间距=7
end


function 自己列表:置数据()
          self:清空()
         for i = 1, 3 do
           self:添加():创建纹理精灵(function()
                  取输入背景(0, 0, 170, 23):显示(0,0)
                  if 交易.宝宝 and 交易.宝宝[i] then
                      文本字体:置颜色(0,0,0,255):取图像(角色信息.宝宝列表[交易.宝宝[i]].名称):显示(5,4)
                  end
            end)
         end
end


function 自己列表:左键弹起(x, y,a)
          if 交易.宝宝 and 交易.宝宝[a] then
                if __手机 then
                    local 事件 =function (编号)
                        if 编号==1 then
                            窗口层.召唤兽查看:打开(角色信息.宝宝列表[交易.宝宝[a]])
                        else
                            if not 交易.锁定状态 then
                                交易.宝宝[a] = nil
                                交易.名称选择:置数据()
                                self:置数据()
                            end
                        end
                    end
                    __UI弹出.临时按钮:打开({"查看","选择"},事件,x,y)
                elseif not 交易.锁定状态 then
                      交易.宝宝[a] = nil
                      交易.名称选择:置数据()
                      self:置数据()
                end
          end
end

function 自己列表:右键弹起(x, y,a)
      if 交易.宝宝 and 交易.宝宝[a] then
          窗口层.召唤兽查看:打开(角色信息.宝宝列表[交易.宝宝[a]])
      end
end


local 对方列表 = 交易:创建列表("对方列表",10,255,170,85)
function 对方列表:初始化()
      self.焦点精灵=nil
      self.选中精灵=nil
      self.行高度=23
      self.行间距=7
end


function 对方列表:置数据()
          self:清空()
         for i = 1, 3 do
           self:添加():创建纹理精灵(function()
                  取输入背景(0, 0, 170, 23):显示(0,0)
                  if 交易.对方宝宝 and 交易.对方宝宝[i] then
                      文本字体:置颜色(0,0,0,255):取图像(交易.对方宝宝[i].名称):显示(5,4)
                  end
            end)
         end
end


function 对方列表:左键弹起(x, y,a)
      if 交易.对方宝宝 and 交易.对方宝宝[a] and __手机 then
            窗口层.召唤兽查看:打开(交易.对方宝宝[a])
      end
end

function 对方列表:右键弹起(x, y,a)
    if 交易.对方宝宝 and 交易.对方宝宝[a] then
          窗口层.召唤兽查看:打开(交易.对方宝宝[a])
    end
end





local 道具网格 = 交易:创建背包网格("道具网格", 225, 63)
function 道具网格:获得鼠标(x, y,a)
    local 物品 = self:焦点物品()
    if 物品 and 物品.物品 then
        __UI弹出.道具提示:打开(物品.物品,x+25,y+25)
    end
end




function 道具网格:左键弹起(x, y, a)
      local 物品 = self:选中物品()
      if 物品 and 物品.物品 and self:选中()~=0 then
            if __手机 then
                __UI弹出.道具提示:打开(物品.物品,x+20,y+20,道具网格,"选择",1)
            else
                  self:选择(1)
            end
      end
end



function 道具网格:选择(编号)
  if 编号 and 编号~=0 then
     local 物品 = self:选中物品()
      if 物品 and not 物品.物品.不可交易 and not 物品.物品.加锁 and not 交易.锁定状态 then
          交易:设置物品(self:选中())
      end
  end
end







function 交易:设置物品(id)
  local 编号=0
  for n=1,3 do
    if not self.材料物品[n] and 编号==0 then 编号=n end
  end
  if 编号 == 0 then
      if self.材料物品[1] and self.材料物品[1].原始编号 then
          self.道具列表[self.材料物品[1].原始编号]=self.材料物品[1]
          self.材料物品[1] = nil
          self.格子[1]= nil
      end
      self.材料物品[1]= self.道具列表[id]
      self.格子[1]=id
      self.材料物品[1].原始编号=id
      self.道具列表[id]=nil
  else
      if self.材料物品[编号] and self.材料物品[编号].原始编号 then
          self.道具列表[self.材料物品[编号].原始编号]=self.材料物品[编号]
          self.材料物品[编号] = nil
          self.格子[编号] = nil
      end
      self.材料物品[编号]= self.道具列表[id]
      self.材料物品[编号].原始编号=id
      self.格子[编号] = id
      self.道具列表[id]=nil
  end
  self.道具网格:置物品(self.道具列表)
  self.材料网格:置物品(self.材料物品)
  交易:银两设置()
end





local 材料网格 = 交易:创建网格("材料网格", 10, 65, 180, 50)
function 材料网格:初始化()
  self:创建格子(50, 50,10,10,1,3)
end
function 材料网格:左键弹起(x, y, a)
    if 交易.材料物品[a] and 交易.材料物品[a].原始编号 and not 交易.锁定状态 and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
        if __手机 then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25,材料网格,"取消",a)
        else
              self:取消(a)
        end
    end
end


function 材料网格:取消(编号)
      if 编号 and 编号~=0 then
            交易.道具列表[交易.材料物品[编号].原始编号]=交易.材料物品[编号]
            交易.道具网格:置物品(交易.道具列表)
            交易.材料物品[编号] = nil
            self:置物品(交易.材料物品)
            交易:银两设置()
      end
end


function 材料网格:获得鼠标(x, y, a)
      if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点 = nil 
      end
      self.焦点=nil
      if 交易.材料物品[a] and 交易.材料物品[a].原始编号 and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
            self.焦点=a
            self.子控件[a]._spr.焦点=true
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25)
      end
end
function 材料网格:失去鼠标(x, y)
  for i, v in ipairs(self.子控件) do
      if v._spr and v._spr.焦点 then
          v._spr.焦点=nil
      end
  end
  self.焦点=nil

end





function 材料网格:置物品(数据)
    for i, v in ipairs(self.子控件) do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,50,50,nil,true)
        if 数据 and 数据[i] then
          lssj:置物品(数据[i],50,50,"数量",true,true)
        end
        self.子控件[i]:置精灵(lssj)
    end 
end




local 对方网格 = 交易:创建网格("对方网格", 10, 255, 180, 50)
function 对方网格:初始化()
  self:创建格子(50, 50,10,10,1,3)
end

function 对方网格:置物品(数据)
    for i, v in ipairs(self.子控件) do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,50,50,nil,true)
        if 数据 and 数据[i] then
          lssj:置物品(数据[i],50,50,"数量",true,true)
        end
        self.子控件[i]:置精灵(lssj)
    end 
end

function 对方网格:左键弹起(x, y, a)
    if self.子控件[a] and self.子控件[a]._spr and self.子控件[a]._spr.物品 and 交易.对方开关 and __手机 then
      __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end

function 对方网格:获得鼠标(x, y, a)
    if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr.焦点 then
        self.子控件[self.焦点]._spr.焦点 = nil 
    end
    self.焦点=nil
    if self.子控件[a] and self.子控件[a]._spr and self.子控件[a]._spr.物品 and 交易.对方开关 then
          self.焦点=a
          self.子控件[a]._spr.焦点=true
          __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end
function 对方网格:失去鼠标(x, y)
    for i, v in ipairs(self.子控件) do
        if v._spr and v._spr.焦点 then
            v._spr.焦点=nil
        end
    end
    self.焦点=nil

end

local 名称选择 = 交易:创建列表("名称选择", 230, 68, 245, 230)
local 滑块=交易:创建竖向滑块("名称滑块",475,62,10,240,true)
名称选择:绑定滑块(滑块.滑块)


  function 名称选择:初始化()
    self.行高度= 37
    self.行间距 = 2
  end
  function 名称选择:置数据()
      self:清空()
      for i, v in ipairs(角色信息.宝宝列表) do
          self:添加():创建纹理精灵(function()
            __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
            local lssj = 取头像(v.模型)
            __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
            if v.加锁 then
              __res:取资源动画("jszy/xjiem",0X85655274,"图像"):显示(225,22)
            end
            文本字体:置颜色(0,0,0,255)
            for n = 1, 3 do
               if 交易.宝宝[n]~=nil and 交易.宝宝[n]== i then
                    文本字体:置颜色(__取颜色("红色"))
               end
            end
            文本字体:取图像(v.名称):显示(40,4)
            文本字体:取图像(v.等级.."级"):显示(40,20)
          end
        )
      end
  end


  function 名称选择:左键弹起(x, y, i)
      if 角色信息.宝宝列表[i] and not 交易.锁定状态 and not 角色信息.宝宝列表[i].加锁 and not 角色信息.宝宝列表[i].不可交易 then
           if __手机 then
                  local 事件=function (编号)
                          if 编号==1 then
                                local 判定 =0
                                for n = 1, 3 do
                                    if 交易.宝宝[n]~=nil and 交易.宝宝[n]== i then
                                        判定=n
                                    end
                                end
                                if 判定==0 then
                                      交易:设置宝宝(i)
                                elseif 交易.宝宝[判定]==i then
                                      交易.宝宝[判定]=nil
                                      self:置数据()
                                      交易.自己列表:置数据()
                                end
                          else
                              窗口层.召唤兽查看:打开(角色信息.宝宝列表[i])
                          end
                    end
                  __UI弹出.临时按钮:打开({"选择","查看"},事件,x,y)
            else
                  local 判定 =0
                  for n = 1, 3 do
                      if 交易.宝宝[n]~=nil and 交易.宝宝[n]== i then
                          判定=n
                      end
                  end
                  if 判定==0 then
                        交易:设置宝宝(i)
                  elseif 交易.宝宝[判定]==i then
                        交易.宝宝[判定]=nil
                        self:置数据()
                        交易.自己列表:置数据()
                  end
            end
      end
  end

  function 名称选择:右键弹起(x, y, i)
      if 角色信息.宝宝列表[i]  then
          窗口层.召唤兽查看:打开(角色信息.宝宝列表[i])
      end
  end

  function 交易:设置宝宝(id)
    local 编号=0
    for n=1,3 do
      if not self.宝宝[n] and 编号==0 then 编号=n end
    end
    if 编号 == 0 then
        if self.宝宝[1] then
            self.宝宝[1]= nil
        end
        self.宝宝[1]=id
    else
        if self.宝宝[编号]  then
            self.宝宝[编号] = nil
        end
        self.宝宝[编号] = id
    end
    self.自己列表:置数据()
    self.名称选择:置数据()
  end




local  状态按钮={"道具","召唤兽"}
for i, v in ipairs(状态按钮) do
    local 临时函数=交易:创建红色单选按钮(v,v, 260+(i-1)*140, 32, 70,22)
    function  临时函数:左键弹起(x, y)
          交易.类型=v
          交易.道具网格:置数据()
          交易:显示设置()
    end
end

local  交易按钮={"确定","取消"}
for i, v in ipairs(交易按钮) do
  local 临时函数=交易:创建红色按钮(v, v,260+(i-1)*140, 315,70,22)
  function  临时函数:左键弹起(x, y)
      if v=="取消" then
        交易:关闭界面()
      elseif v=="确定" then

          请求服务(3738)

      end
  end
end

local  确认按钮={"自己确认","对方确认"}
for i, v in ipairs(确认按钮) do
  local 临时函数=交易:创建多选按钮(v, 190, 70+(i-1)*190)
  function 临时函数:初始化()
        self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000009))
  end
  
    function  临时函数:左键弹起(x, y)
          if v=="自己确认" then
              请求服务(3717,{银子=tonumber(交易.己方银两),道具=交易.格子,bb=交易.宝宝})
          end
    end



end

 

local 关闭 = 交易:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    交易:关闭界面()
end





