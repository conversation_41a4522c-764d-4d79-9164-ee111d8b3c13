取属性背景 = function(x, y, w, h, lx)
  local nsf = require("SDL.图像")(w, h + y)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(1, 200, 0, 20, 20)["显示"](__res:getPNGCC(1, 200, 0, 20, 20), 0, 0 + y)
    __res:getPNGCC(1, 200, 20, 20, 160)["拉伸"](__res:getPNGCC(1, 200, 20, 20, 160), 20, h - 40)["显示"](__res:getPNGCC(1, 200, 20, 20, 160)["拉伸"](__res:getPNGCC(1, 200, 20, 20, 160), 20, h - 40), 0, 20 + y)
    __res:getPNGCC(1, 200, 180, 20, 20)["显示"](__res:getPNGCC(1, 200, 180, 20, 20), 0, h - 20 + y)
    __res:getPNGCC(1, 220, 0, 160, 20)["拉伸"](__res:getPNGCC(1, 220, 0, 160, 20), w - 40, 20)["显示"](__res:getPNGCC(1, 220, 0, 160, 20)["拉伸"](__res:getPNGCC(1, 220, 0, 160, 20), w - 40, 20), 20, 0 + y)
    __res:getPNGCC(1, 220, 20, 60, 160)["拉伸"](__res:getPNGCC(1, 220, 20, 60, 160), w - 40, h - 40)["显示"](__res:getPNGCC(1, 220, 20, 60, 160)["拉伸"](__res:getPNGCC(1, 220, 20, 60, 160), w - 40, h - 40), 20, 20 + y)
    __res:getPNGCC(1, 230, 170, 140, 30)["拉伸"](__res:getPNGCC(1, 230, 170, 140, 30), w - 40, 20)["显示"](__res:getPNGCC(1, 230, 170, 140, 30)["拉伸"](__res:getPNGCC(1, 230, 170, 140, 30), w - 40, 20), 20, h - 20 + y)
    __res:getPNGCC(1, 380, 0, 20, 20)["显示"](__res:getPNGCC(1, 380, 0, 20, 20), w - 20, 0 + y)
    __res:getPNGCC(1, 380, 20, 20, 160)["拉伸"](__res:getPNGCC(1, 380, 20, 20, 160), 20, h - 40)["显示"](__res:getPNGCC(1, 380, 20, 20, 160)["拉伸"](__res:getPNGCC(1, 380, 20, 20, 160), 20, h - 40), w - 20, 20 + y)
    __res:getPNGCC(1, 380, 180, 20, 20)["显示"](__res:getPNGCC(1, 380, 180, 20, 20), w - 20, h - 20 + y)
    nsf["渲染结束"](nsf)
  end
  if lx then
    return nsf
  else
    return nsf["到精灵"](nsf)
  end
end
