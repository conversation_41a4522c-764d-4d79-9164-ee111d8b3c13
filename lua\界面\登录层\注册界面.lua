--[[
LastEditTime: 2025-03-31 05:28:49
--]]
--[[
LastEditTime: 2025-03-30 21:19:25
--]]


local 注册账号 = 登录层:创建控件("注册账号",引擎.宽度2-250,引擎.高度2-190, 660, 384)
function 注册账号:初始化()
    self:置精灵(__res:取资源动画("ui","00010020.was","精灵"))
end

local 账号输入 = 注册账号:创建文本输入("账号输入", 110, 93-20, 145, 16)
function 账号输入:初始化()
    self.取光标精灵(self)
    self:置模式(1)
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式)
    self:置颜色(255, 255, 255, 255)

end

local 密码输入 = 注册账号:创建文本输入("密码输入", 110, 143-22, 145, 16)
function 密码输入:初始化()
    self.取光标精灵(self)
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(255, 255, 255, 255)

end



local 确认密码 = 注册账号:创建文本输入("确认密码", 110, 193-25, 145, 16)
function 确认密码:初始化()
    self.取光标精灵(self)
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(255, 255, 255, 255)
end


local 邀请码输入 = 注册账号:创建文本输入("邀请码输入", 110, 213, 145, 16)
function 邀请码输入:初始化()
    self.取光标精灵(self)
    self:置限制字数(8)
    self:置模式(self.英文模式 | self.数字模式)
    self:置颜色(255, 255, 255, 255)
end

function 注册账号:键盘弹起(键码, 功能)
    if 键码 == 9 then
        if self.账号输入._输入焦点 then
            self.密码输入:置焦点(true)
        elseif self.密码输入._输入焦点 then
            self.确认密码:置焦点(true)
        elseif self.确认密码._输入焦点 then
            self.邀请码输入:置焦点(true)
        else
            self.账号输入:置焦点(true)
        end
    end
end

local 返回 = 注册账号:创建按钮("返回", 460, 291)
function 返回:初始化()
  self:创建按钮精灵(__res:取资源动画("ui","00010114.was"),1)
end
function 返回:左键弹起(x, y, msg)
    注册账号:置可见(false)
    登录层.登录游戏:置可见(true,true)
end  
local 注册账号 = 注册账号:创建按钮("注册账号", 145, 248)
function 注册账号:初始化()
  self:创建按钮精灵(__res:取资源动画("ui","00010104.was"),1)
end
function 注册账号:左键弹起(x, y, msg)
    if not 账号输入:取文本() or not 密码输入:取文本()  or not 确认密码:取文本() then
        __UI弹出.提示框:打开("#R请先输入账号或密码或确认密码")
    elseif 账号输入:取文本() == "" or 密码输入:取文本() == "" or 确认密码:取文本() == "" then
          __UI弹出.提示框:打开("#R请先输入账号或密码或确认密码")
    elseif not 邀请码输入:取文本() or  邀请码输入:取文本()== "" then
          __UI弹出.提示框:打开("#R请先输入授权码")
    elseif #账号输入:取文本()<6 or #密码输入:取文本() <6 or #确认密码:取文本() <6 then
        __UI弹出.提示框:打开("#R账号或密码或确认密码长度过短")
    elseif    密码输入:取文本() ~= 确认密码:取文本()  then
        __UI弹出.提示框:打开("#R请检查密码与确认密码是否一致")
    else
        请求服务(34,_版本号..fgc..账号输入:取文本()..fgc..密码输入:取文本()..fgc..邀请码输入:取文本())--这个应该也没问题了
    end
end
  








































































