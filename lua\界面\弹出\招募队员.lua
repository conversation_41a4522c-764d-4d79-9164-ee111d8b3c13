__UI弹出["招募队员"] = __UI界面["创建弹出窗口"](__UI界面, "招募队员", 267 + abbr.py.x, 74 + abbr.py.y, 200, 395)
local 招募队员 = __UI弹出["招募队员"]
function 招募队员:初始化()
  local nsf = require("SDL.图像")(200, 395)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 200, 395, true)["显示"](取黑透明背景(0, 0, 200, 395, true), 0, 0)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 招募队员:打开(数据)
  self:置可见(true)
  self.角色列表["重置"](self.角色列表)
end
local 角色列表 = 招募队员["创建列表"](招募队员, "角色列表", 10, 12, 180, 320)
function 角色列表:初始化()
  self:置文字(字体18)
  self.行高度 = 60
  self.行间距 = 5
end
function 角色列表:左键弹起(x, y, i, item, msg)
  招募队员["选中"] = i
end
function 角色列表:重置()
  self.清空(self)
  for i, v in ipairs(__主控["招募信息"]) do
    self.添加(self)
    local nsf = require("SDL.图像")(180, 60)
    if nsf["渲染开始"](nsf) then
      __res:getPNGCC(3, 757, 291, 57, 57)["拉伸"](__res:getPNGCC(3, 757, 291, 57, 57), 60, 60)["显示"](__res:getPNGCC(3, 757, 291, 57, 57)["拉伸"](__res:getPNGCC(3, 757, 291, 57, 57), 60, 60), 0, 0)
      local lssj = 取头像(v["造型"])
      if 0 == lssj[2] then
        lssj[2] = lssj[1]
      end
      __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), 54, 54)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), 54, 54), 3, 3)
      字体18["置颜色"](字体18, 255, 255, 255)
      字体18["取图像"](字体18, v["名称"])["显示"](字体18["取图像"](字体18, v["名称"]), 70, 0)
      字体18["取图像"](字体18, v["门派"])["显示"](字体18["取图像"](字体18, v["门派"]), 70, 20)
      字体18["取图像"](字体18, v["等级"])["显示"](字体18["取图像"](字体18, v["等级"]), 70, 40)
      if v.id == 角色信息["数字id"] then
        字体18["置颜色"](字体18, __取颜色("红色"))
        字体18["取描边图像"](字体18, "已登陆", 255, 255, 255, 255)["显示"](字体18["取描边图像"](字体18, "已登陆", 255, 255, 255, 255), 0, 0)
        v["已登录"] = true
      end
      if v["已招募"] then
        字体18["置颜色"](字体18, __取颜色("红色"))
        字体18["取描边图像"](字体18, "已招募", 255, 255, 255, 255)["显示"](字体18["取描边图像"](字体18, "已招募", 255, 255, 255, 255), 0, 0)
      end
      nsf["渲染结束"](nsf)
    end
    self.子控件[i]["置精灵"](self.子控件[i], nsf["到精灵"](nsf))
  end
end
local 招募角色按钮 = 招募队员["创建我的按钮"](招募队员, __res:getPNGCC(2, 487, 802, 150, 43, true), "招募角色按钮", 30, 338, "招募角色")
function 招募角色按钮:左键弹起(x, y)
  if 招募队员["选中"] and not __主控["招募信息"][招募队员["选中"]]["已登录"] and not __主控["招募信息"][招募队员["选中"]]["已招募"] then
    发送数据(4014, {
      ["序列"] = __主控["招募信息"][招募队员["选中"]].id
    })
  end
end
