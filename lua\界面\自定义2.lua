战斗连击显示 = {}
战斗连击单位 = 0
多重战斗开关 = false

__秘制食谱子类 = function()
    return {
        "红罗羹",
        "绿芦羹",
        "玉露羹",
        "百岁香",
        "神仙饮",
        "八珍玉液",
        "五味露",
        "福灵沙",
        "千金露",
        "同心肉脯",
        "忠义肉脯"
    }
end
local format = string.format
__道具提示 = function(item, 文本)
    if nil == item then
        return
    end
    -- 文本["置文本"](文本, "            #Y" .. item["名称"])
    -- 文本["置文本"](文本, "\n")
    文本["置文本"](文本, item["介绍"] or "")
    local zls = item["总类"]
    for i, v in ipairs(__取物品功能(item)) do
        文本["置文本"](文本, v)
    end
    if 1 == zls then
        if item.名称 == "炫彩ID" then
			if item.特效 then
				-- local dsd="#G"
				-- if item.特效=="蓝色ID" then
				--    dsd="#S"
				--    elseif item.特效=="紫色ID" then
				--    dsd="#Y"
				--      elseif item.特效=="黄金ID" then
				--    dsd="#Y"
				-- end
				文本["置文本"](文本,format("#Y/【特效】#G/%s",item.特效))
			end
		end
        if 10 == item["分类"] and 2 == item["子类"] then
            文本["置文本"](文本, "#Y【效果】:" .. item["阶品"])
        end
    elseif 2 == zls and item["灵饰"] then
        if item["鉴定"] then
            文本["置文本"](文本, "【装备条件】等级" .. item["级别限制"])
            文本["置文本"](文本, "【灵饰类型】" .. item["部位类型"])
            文本["置文本"](文本, "#Y等级 " .. item["级别限制"])
            文本["置文本"](文本, "#Y" .. item["幻化属性"]["基础"]["类型"] ..
                " +" .. item["幻化属性"]["基础"]["数值"])
            if nil ~= item["修理失败"] then
                文本["置文本"](文本, "#Y耐久度 " ..
                    math.ceil(item["耐久"] or 500) .. "   修理失败 " .. item["修理失败"] .. "次")
            else
                文本["置文本"](文本, "#Y耐久度 " .. math.ceil(item["耐久"] or 500))
            end
            if nil ~= item["特效"] then
                文本["置文本"](文本, string.format("#S特效：%s", item["特效"]))
            end
            if item["幻化等级"] > 0 then
                文本["置文本"](文本, "#Y精炼等级 " .. item["幻化等级"])
            end
            for n = 1, #item["幻化属性"]["附加"] do
                if 0 == item["幻化等级"] then
                    文本["置文本"](文本,
                        "#G" .. item["幻化属性"]["附加"][n]["类型"] .. " +" .. item["幻化属性"]["附加"][n]
                        ["数值"])
                else
                    文本["置文本"](文本,
                        "#G" ..
                        item["幻化属性"]["附加"][n]["类型"] ..
                        " +" .. item["幻化属性"]["附加"][n]["数值"] ..
                        " #F[+" .. item["幻化属性"]["附加"][n]["强化"] .. "]")
                end
            end
            if nil ~= item["制造者"] then
                文本["置文本"](文本, "制造者：" .. item["制造者"])
            end
        else
            文本["置文本"](文本, "【装备条件】等级" .. item["级别限制"])
            文本["置文本"](文本, "【灵饰类型】" .. item["部位类型"])
            文本["置文本"](文本, "#Y等级 " .. item["级别限制"])
            if nil ~= item["制造者"] then
                文本["置文本"](文本, string.format("#W制造者：%s", item["制造者"]))
            end
            if nil == jd then
                文本["置文本"](文本, "#Y未鉴定物品")
            end
        end
    elseif 2 == zls and nil == item["灵饰"] and item.召唤兽装备 then
        文本:置文本("【装备对象】召唤兽")
        文本:置文本("【装备条件】等级"..item.级别限制)
        文本:置文本("#Y/等级"..item.级别限制)
        local 气血 = item.气血
		local 魔法 = item.魔法
		local 命中 = item.命中
		local 伤害 = item.伤害
		local 防御 = item.防御
		local 速度 = item.速度
		local sx = ""
		local fh = "+"
		if 命中 ~= 0  and 命中 ~= nil  then
			sx = sx.."命中率 +"..命中.."%".."  "
		end
		if 防御 ~= 0 and 防御 ~= nil  then
			sx = sx.."防御 +"..防御.." "
		end
		if 速度 ~= 0 and 速度 ~= nil  then
			sx = sx.."速度 +"..速度.." "
		end
		if 伤害 ~= 0 and 伤害 ~= nil    then
			if 伤害<0 then
				fh = ""
			end
			sx = sx.."伤害 "..fh..伤害 .." "
		end
		fh = "+"
		if 气血 ~= 0 and 气血 ~= nil then
			if 气血<0 then
				fh = ""
			end
			sx = sx.."气血 "..fh..气血.." "
		end
		fh = "+"
		if 魔法 ~= 0 and 魔法 ~= nil  then
			if 魔法<0 then
				fh = ""
			end
			sx = sx.."魔法 "..fh..魔法.." "
		end

		if sx ~= "" then
            文本:置文本("#Y/"..sx)
		end
		if item.修理失败 ~= nil then
            文本:置文本("#Y/耐久度 "..qz((item.耐久 or 500)).."   修理失败 " ..item.修理失败.."次")
			-- self.道具文本:添加文本("#Y/耐久度 "..到整数((item.耐久 or 500)).."   修理失败 " ..item.修理失败.."次")
		else
            文本:置文本("#Y/耐久度 "..qz((item.耐久 or 500)))
			-- self.道具文本:添加文本("#Y/耐久度 "..到整数((item.耐久 or 500)))
		end

		local fj = ""
		local 体质 = item.体质
		local 力量 = item.力量
		local 耐力 = item.耐力
		local 魔力 = item.魔力
		local 敏捷 = item.敏捷
		fh = "+"
		if 体质 ~= 0 and 体质 ~= nil    then
			if 体质<0 then
				fh = ""
			end
			fj = fj.."体质 "..fh..体质 .." "
		end
		fh = "+"
		if 力量 ~= 0 and 力量 ~= nil  then
			if 力量<0 then
				fh = ""
			end
			fj = fj.."力量 "..fh..力量.." "
		end
		fh = "+"
		if 耐力 ~= 0 and 耐力 ~= nil  then
			if 耐力<0 then
				fh = ""
			end
			fj = fj.."耐力 "..fh..耐力.." "
		end
		fh = "+"
		if 魔力 ~= 0 and 魔力 ~= nil then
			if 魔力<0 then
				fh = ""
			end
			fj = fj.."魔力 "..fh..魔力.." "
		end
		fh = "+"
		if 敏捷 ~= 0 and 敏捷 ~= nil  then
			if 敏捷<0 then
				fh = ""
			end
			fj = fj.."敏捷 "..fh..敏捷.." "
		end
        -- if item.词条~=nil and #item.词条>0 then
		-- 	文本:置文本("#G/词条属性:")
		-- 	for i=1,#item.词条 do

        --         文本:置文本("#Y/"..item.词条[i].类型.."#G+#F"..item.词条[i].数值.."%")
		-- end
		-- end
		if item.特效~=nil then
			文本:置文本("#P/特效："..item.特效)
		end
		if fj ~= "" then
            文本:置文本("#G/"..fj)
			-- self.道具文本:添加文本("#G/"..fj)
		end
		if item.套装效果 ~= nil then
            文本:置文本(string.format("#S/套装效果：%s",item.套装效果[1]..item.套装效果[2]))
			-- self.道具文本:添加文本(string.format("#S/套装效果：%s",item.套装效果[1]..item.套装效果[2]))
		end
		if item.制造者 ~= nil then
            文本:置文本(string.format("#W/制造者：%s",item.制造者))
			-- self.道具文本:添加文本(string.format("#W/制造者：%s",item.制造者))
		end
		if item.精魄~=nil and item.精魄等级~=nil then
			local num = 0
			if item.精魄=="躲避" then
				num=20*item.精魄等级
			elseif item.精魄=="速度" then
				num=6*item.精魄等级
			elseif item.精魄=="伤害" then
				num=10*item.精魄等级
			elseif item.精魄=="灵力" then
				num=4*item.精魄等级
			elseif item.精魄=="防御" then
				num=8*item.精魄等级
			elseif item.精魄=="气血" then
				num=30*item.精魄等级
			end
			if item.精魄等级>0 then
                文本:置文本("#Y/镶嵌效果")
                文本:置文本("#Y/+"..num..item.精魄.." 镶嵌等级: "..item.精魄等级)
				-- self.道具文本:添加文本("#Y/镶嵌效果")
				-- self.道具文本:添加文本("#Y/+"..num..item.精魄.." 镶嵌等级: "..item.精魄等级)
			end
		end
    elseif zls == 193 then
		if item.名称=="本命法宝门派秘籍" then

			if not item.技能  then
			self.道具文本:添加文本("#Y/未鉴定\n#Y/适用门派：#R/"..item.门派)
			else
			self.道具文本:添加文本("#Y/当前效果：#R/"..item.技能.."\n#Y/适用门派：#R/"..item.门派)
			end
		end
    elseif zls == 2 and item.分类 == 15 or item.分类 == 16 or item.分类 == 17 or item.分类 == 18  then
        -- if item.级别 ~=nil then
        --         local 等级 = item.级别
        --         文本:置文本("#S升级锦衣每级增加属性20\n#G当前锦衣等级"..等级)
        --       end
    elseif 2 == zls and nil == item["灵饰"]  and nil==item["召唤兽装备"] and item["分类"] <= 14 then
        if item["鉴定"] then
            if item.子类 == 911 then
                文本["置文本"](文本, "#W【装备条件】等级" .. item["级别限制"])
                if nil == item["角色限制"][3] and nil ~= item["角色限制"][2] then
                    文本["置文本"](文本, "#W【装备角色】" .. item["角色限制"][1] ..
                        "、" .. item["角色限制"][2])
                elseif nil == item["角色限制"][2] then
                    文本["置文本"](文本, "#W【装备角色】" .. item["角色限制"][1])
                else
                    文本["置文本"](文本,
                        "#W【装备角色】" .. item["角色限制"][1] ..
                        "、" .. item["角色限制"][2] .. "、" .. item["角色限制"][3])
                end
        elseif 3 == item["分类"] then
                文本["置文本"](文本, "#W【装备条件】等级" .. item["级别限制"])
                if nil == item["角色限制"][3] and nil ~= item["角色限制"][2] then
                    文本["置文本"](文本, "#W【装备角色】" .. item["角色限制"][1] ..
                        "、" .. item["角色限制"][2])
                elseif nil == item["角色限制"][2] then
                    文本["置文本"](文本, "#W【装备角色】" .. item["角色限制"][1])
                else
                    文本["置文本"](文本,
                        "#W【装备角色】" .. item["角色限制"][1] ..
                        "、" .. item["角色限制"][2] .. "、" .. item["角色限制"][3])
                end
            elseif 1 == item["分类"] or 4 == item["分类"] then
                文本["置文本"](文本, "#W【装备条件】等级" .. item["级别限制"])
                文本["置文本"](文本, "#W【装备角色】" .. item["性别限制"])
            elseif item["分类"] > 6 and item["分类"] < 10 then
                文本["置文本"](文本, "#W【角色限制】召唤兽")
            elseif item["分类"] >= 10 then
                文本["置文本"](文本, "#W【装备等级】" .. item["级别限制"])
            else
                文本["置文本"](文本, "#W【装备条件】等级" .. item["级别限制"])
                文本["置文本"](文本, "#W【装备角色】无")
            end
            文本["置文本"](文本, "#Y等级 " .. item["级别限制"] .. " 五行 " .. item["五行"])
            local sx = ""
            local 气血 = item["气血"]
            local 魔法 = item["魔法"]
            local 命中 = item["命中"]
            local 伤害 = item["伤害"]
            local 防御 = item["防御"]
            local 速度 = item["速度"]
            local 躲避 = item["躲避"]
            local 灵力 = item["灵力"]
            local 体质 = item["体质"]
            local 魔力 = item["魔力"]
            local 力量 = item["力量"]
            local 耐力 = item["耐力"]
            local 敏捷 = item["敏捷"]
            if item["分类"] <= 6 then
                if nil ~= item["宝石属性"] then
                    if nil ~= item["宝石属性"]["气血"] and 4 ~= item["分类"] or
                        0 ~= 气血 and nil ~= 气血 and 4 ~= item["分类"] then
                        sx = sx .. "气血 +" .. (气血 or 0) + (item["宝石属性"]["气血"] or 0) .. " "
                    end
                    if nil ~= item["宝石属性"]["魔法"] or 0 ~= 魔法 and nil ~= 魔法 then
                        sx = sx .. "魔法 +" .. (魔法 or 0) + (item["宝石属性"]["魔法"] or 0) .. " "
                    end
                    if nil ~= item["宝石属性"]["命中"] and 1 ~= item["分类"] or
                        0 ~= 命中 and nil ~= 命中 and 1 ~= item["分类"] then
                        sx = sx .. "命中 +" .. (命中 or 0) + (item["宝石属性"]["命中"] or 0) .. " "
                    end
                    if nil ~= item["宝石属性"]["伤害"] and 1 ~= item["分类"] or
                        0 ~= 伤害 and nil ~= 伤害 and 1 ~= item["分类"] then
                        sx = sx .. "伤害 +" .. (伤害 or 0) + (item["宝石属性"]["伤害"] or 0) .. " "
                    end
                    if nil ~= item["宝石属性"]["防御"] or 0 ~= 防御 and nil ~= 防御 then
                        sx = sx .. "防御 +" .. (防御 or 0) + (item["宝石属性"]["防御"] or 0) .. " "
                    end
                    if nil ~= item["宝石属性"]["灵力"] and 4 ~= item["分类"] or
                        0 ~= 灵力 and nil ~= 灵力 and 4 ~= item["分类"] then
                        sx = sx .. "灵力 +" .. (灵力 or 0) + (item["宝石属性"]["灵力"] or 0) .. " "
                    end
                    if nil ~= item["宝石属性"]["敏捷"] and 6 == item["分类"] or
                        0 ~= 敏捷 and nil ~= 敏捷 and 6 == item["分类"] then
                        sx = sx .. "敏捷 +" .. (敏捷 or 0) + (item["宝石属性"]["敏捷"] or 0) .. " "
                    end
                else
                    if 0 ~= 气血 and nil ~= 气血 and 4 ~= item["分类"] then
                        sx = sx .. "气血 +" .. 气血 .. " "
                    end
                    if 0 ~= 魔法 and nil ~= 魔法 then
                        sx = sx .. "魔法 +" .. 魔法 .. " "
                    end
                    if 0 ~= 命中 and nil ~= 命中 and 1 ~= item["分类"] then
                        sx = sx .. "命中 +" .. 命中 .. " "
                    end
                    if 0 ~= 伤害 and nil ~= 伤害 and 1 ~= item["分类"] then
                        sx = sx .. "伤害 +" .. 伤害 .. " "
                    end
                    if 0 ~= 防御 and nil ~= 防御 then
                        sx = sx .. "防御 +" .. 防御 .. " "
                    end
                    if 0 ~= 灵力 and nil ~= 灵力 and 4 ~= item["分类"] then
                        sx = sx .. "灵力 +" .. 灵力 .. " "
                    end
                    if 0 ~= 敏捷 and nil ~= 敏捷 and 6 == item["分类"] then
                        sx = sx .. "敏捷 +" .. 敏捷 .. " "
                    end
                end
            elseif item["分类"] >= 7 and item["分类"] <= 9 then
                if 0 ~= 气血 and nil ~= 气血 then
                    sx = sx .. "气血 +" .. 气血 .. " "
                end
                if 0 ~= 魔法 and nil ~= 魔法 then
                    sx = sx .. "魔法 +" .. 魔法 .. " "
                end
                if 0 ~= 命中 and nil ~= 命中 then
                    sx = sx .. "命中 +" .. 命中 .. " "
                end
                if 0 ~= 伤害 and nil ~= 伤害 then
                    sx = sx .. "伤害 +" .. 伤害 .. " "
                end
                if 0 ~= 防御 and nil ~= 防御 then
                    sx = sx .. "防御 +" .. 防御 .. " "
                end
                if 0 ~= 速度 and nil ~= 速度 then
                    sx = sx .. "速度 +" .. 速度 .. " "
                end
                if 0 ~= 躲避 and nil ~= 躲避 then
                    sx = sx .. "躲避 +" .. 躲避 .. " "
                end
            end
            if "" ~= sx then
                文本["置文本"](文本, "#Y" .. sx)
            end
            if nil ~= item["修理失败"] then
                文本["置文本"](文本, "#Y耐久度 " ..
                    math.ceil(item["耐久"] or 500) .. "   修理失败 " .. item["修理失败"] .. "次")
            else
                文本["置文本"](文本, "#Y耐久度 " .. math.ceil(item["耐久"] or 500))
            end
            if nil ~= item["锻炼等级"] then
                local bsz = ""
                for i = 1, #item["镶嵌宝石"] do
                    local sw = "、"
                    if i == #item["镶嵌宝石"] then
                        sw = ""
                    end
                    bsz = bsz .. item["镶嵌宝石"][i] .. sw
                end
                if item["分类"] <= 6 then
                    文本["置文本"](文本, string.format("#Y锻炼等级 %s  镶嵌宝石 %s", item["锻炼等级"]
                        , bsz))
                elseif item["分类"] >= 7 and item["分类"] <= 9 then
                    文本["置文本"](文本, string.format("#Y锻造效果 "))
                end
                if 1 == item["分类"] then
                    sx = ""
                    if nil ~= item["宝石属性"]["命中"] and 0 ~= item["宝石属性"]["命中"] then
                        sx = sx .. "命中 +" .. item["宝石属性"]["命中"] .. " "
                    end
                    if nil ~= item["宝石属性"]["伤害"] and 0 ~= item["宝石属性"]["伤害"] then
                        sx = sx .. "伤害 +" .. item["宝石属性"]["伤害"] .. " "
                    end
                    if "" ~= sx then
                        文本["置文本"](文本, "#G" .. sx)
                    end
                elseif 3 == item["分类"] then
                    sx = ""
                    if nil ~= item["宝石属性"]["躲避"] and 0 ~= item["宝石属性"]["躲避"] then
                        sx = sx .. "躲避 +" .. item["宝石属性"]["躲避"] .. " "
                    end
                    if "" ~= sx then
                        文本["置文本"](文本, "#G" .. sx)
                    end
                elseif 4 == item["分类"] then
                    sx = ""
                    if nil ~= item["宝石属性"]["气血"] and 0 ~= item["宝石属性"]["气血"] then
                        sx = sx .. "气血 +" .. item["宝石属性"]["气血"] .. " "
                    end
                    if nil ~= item["宝石属性"]["灵力"] and 0 ~= item["宝石属性"]["灵力"] then
                        sx = sx .. "灵力 +" .. item["宝石属性"]["灵力"] .. " "
                    end
                    if "" ~= sx then
                        文本["置文本"](文本, "#G" .. sx)
                    end
                elseif 5 == item["分类"] or 6 == item["分类"] then
                    sx = ""
                    if nil ~= item["宝石属性"]["躲避"] and 0 ~= item["宝石属性"]["躲避"] then
                        sx = sx .. "躲避 +" .. item["宝石属性"]["躲避"] .. " "
                    end
                    if nil ~= item["宝石属性"]["速度"] and 0 ~= item["宝石属性"]["速度"] then
                        sx = sx .. "速度 +" .. item["宝石属性"]["速度"] .. " "
                    end
                    if "" ~= sx then
                        文本["置文本"](文本, "#G" .. sx)
                    end
                elseif 7 == item["分类"] then
                    sx = ""
                    if nil ~= item["宝石属性"]["伤害"] and 0 ~= item["宝石属性"]["伤害"] then
                        sx = sx .. "+" .. item["宝石属性"]["伤害"] .. "伤害"
                    end
                    if nil ~= item["宝石属性"]["灵力"] and 0 ~= item["宝石属性"]["灵力"] then
                        sx = sx .. "+" .. item["宝石属性"]["灵力"] .. "灵力"
                    end
                    if "" ~= sx then
                        文本["置文本"](文本, "#Y" .. sx .. "  锻造等级：" .. item["锻炼等级"])
                    end
                elseif 8 == item["分类"] then
                    sx = ""
                    if nil ~= item["宝石属性"]["躲避"] and 0 ~= item["宝石属性"]["躲避"] then
                        sx = sx .. "+" .. item["宝石属性"]["躲避"] .. "躲避"
                    end
                    if nil ~= item["宝石属性"]["速度"] and 0 ~= item["宝石属性"]["速度"] then
                        sx = sx .. "+" .. item["宝石属性"]["速度"] .. "速度"
                    end
                    if "" ~= sx then
                        文本["置文本"](文本, "#Y" .. sx .. "  锻造等级：" .. item["锻炼等级"])
                    end
                elseif 9 == item["分类"] then
                    sx = ""
                    if nil ~= item["宝石属性"]["防御"] and 0 ~= item["宝石属性"]["防御"] then
                        sx = sx .. "+" .. item["宝石属性"]["防御"] .. "防御"
                    end
                    if nil ~= item["宝石属性"]["气血"] and 0 ~= item["宝石属性"]["气血"] then
                        sx = sx .. "+" .. item["宝石属性"]["气血"] .. "气血"
                    end
                    if "" ~= sx then
                        文本["置文本"](文本, "#Y" .. sx .. "  锻造等级：" .. item["锻炼等级"])
                    end
                end
            end
            local ds = ""
            local lsb = {
                "体质",
                "魔力",
                "力量",
                "耐力",
                "敏捷"
            }
            for i = 1, #lsb do
                if nil ~= item[lsb[i]] and 0 ~= item[lsb[i]] then
                    if item[lsb[i]] < 0 then
                        ds = ds .. lsb[i] .. " " .. item[lsb[i]] .. " "
                    else
                        ds = ds .. lsb[i] .. " +" .. item[lsb[i]] .. " "
                    end
                end
            end
            if "" ~= ds and 6 ~= item["分类"] then
                文本["置文本"](文本, "#G" .. ds)
            end
            if item.萃灵属性 and item.萃灵属性.次数 and item.萃灵属性.属性 then
				文本:置文本(format("#F/萃灵次数：%s",item.萃灵属性.次数))
				local sx=false
				for k,v in pairs(item.萃灵属性.属性) do
					if v>0 then
						if not sx then
							sx=format("%s+%s",k,v).." "
						else
							sx=sx..format("%s+%s",k,v).." "
						end
					end
				end
				if sx  then
					文本:置文本("#F/萃灵属性：#F/"..sx)
				end
			end

            if item.临时附魔 ~= nil then
				local 临时表 = {"气血","魔法","命中","伤害","体质","魔力","耐力","防御","速度","灵力","愤怒","力量","气血回复效果","法伤结果","法术防御","法术伤害","物伤结果"}
				for i=1,#临时表 do
					if item.临时附魔[临时表[i]] ~= nil then
						if 临时表[i]=="气血回复效果" then
                            文本:置文本(string.format("#G/临时气血回复".." %s %s",item.临时附魔[临时表[i]].数值 ,os.date("%m/%d %H:%M",item.临时附魔[临时表[i]].时间)))
                            -- 文本:置文本(string.format("#G/临时气血回复".." %s %s",item.临时附魔[临时表[i]].数值 ,os.date("%m/%d %H:%M",item.临时附魔[临时表[i]].时间)))
							-- 文本:置文本(format("#G/临时气血回复".." %s %s",item.临时附魔[临时表[i]].数值 ,os.date("%m/%d %H:%M",item.临时附魔[临时表[i]].时间)))
							-- self.道具文本2:添加文本(format("#G/临时气血回复".." %s %s",item.临时附魔[临时表[i]].数值 ,os.date("%m/%d %H:%M",item.临时附魔[临时表[i]].时间)))
						else
                            文本:置文本(string.format("#G/临时"..临时表[i].." %s %s",item.临时附魔[临时表[i]].数值 ,os.date("%m/%d %H:%M",item.临时附魔[临时表[i]].时间)))
							-- self.道具文本:添加文本(format("#G/临时"..临时表[i].." %s %s",item.临时附魔[临时表[i]].数值 ,os.date("%m/%d %H:%M",item.临时附魔[临时表[i]].时间)))
							-- self.道具文本2:添加文本(format("#G/临时"..临时表[i].." %s %s",item.临时附魔[临时表[i]].数值 ,os.date("%m/%d %H:%M",item.临时附魔[临时表[i]].时间)))
						end
					end
				end
			end
            if nil ~= item["特技"] then
                文本["置文本"](文本, string.format("#S特技：%s", item["特技"]))
            end
            if item.特效 ~= nil and item.特效[1] then
                文本:置文本(string.format("#S/特效：%s %s %s",item.特效[1],item.特效[2]  or "" ,item.特效[3] or ""))
				-- self.道具文本:添加文本(format("#S/特效：%s %s %s",item.特效[1],item.特效[2]  or "" ,item.特效[3] or ""))
				-- self.道具文本2:添加文本(format("#S/特效：%s %s %s",item.特效[1],item.特效[2]  or "" ,item.特效[3] or ""))
			end
            -- if nil ~= item["特效"] then
            --     文本["置文本"](文本, string.format("#S特效：%s", item["特效"]))
            -- end
            if nil ~= item["套装效果"] then
                文本["置文本"](文本, string.format("#S套装效果：%s", item["套装效果"][1] ..item["套装效果"][2]))
            end
           -- if item.完美度~=nil and item.鉴定 then
			--	文本:置文本(string.format("#Y/装备评分: "..item.完美度))
			--end
            -- if item.强化次数~=nil and item.鉴定 then
            --     if item.强化次数>=100 then
            --         文本:置文本(string.format("#G强化次数".."+MAX"))
            --     else
            --         文本:置文本(string.format("#G强化次数".."+"..item.强化次数))
            --    end
            --   end
            if item.新特效 ~= nil and item.新特效数值 ~= nil then
				if item.新特效数值+0>=6 then
                    文本:置文本(string.format("#S/%s +%s",item.新特效,item.新特效数值))
					-- self.道具文本:添加文本(format("#S/%s +%s",item.新特效,item.新特效数值))
					-- self.道具文本2:添加文本(format("#S/%s +%s",item.新特效,item.新特效数值))
				else
                    文本:置文本(string.format("#S/%s +%s％",item.新特效,item.新特效数值))
					-- self.道具文本:添加文本(format("#S/%s +%s％",item.新特效,item.新特效数值))
					-- self.道具文本2:添加文本(format("#S/%s +%s％",item.新特效,item.新特效数值))
				end
			end
            if nil ~= item["开运孔数"] and nil ~= item["开运孔数"]["当前"] and 0 ~= item["开运孔数"]["当前"] then
                文本["置文本"](文本,string.format("#G开运孔数：%s", item["开运孔数"]["当前"] .."孔/" .. item["开运孔数"]["上限"] .. "孔"))
            end

            if item.星位~=nil then
				-- table.print(item.星位)
				for n=1,5 do
					if item.星位[n]~=nil then--and item.星位[n].符石属性 then
						local  组合语句="#G符石："
						for k,v in pairs(item.星位[n].符石属性) do
							组合语句=组合语句..k.." +"..v.." "
						end
                        文本:置文本(组合语句)
					end
				end
			end
			if item.星位组~=nil then
				if item.星位~=nil and item.星位[6] ~= nil then
					local 临时属性 = ""
					for k,v in pairs(item.星位[6].符石属性) do
						临时属性 = k.." +"..v
					end
                    文本:置文本("#G/星位："..临时属性)
					if item.星位[6].相互~=nil then
						local 临时属性 = ""
						for k,v in pairs(item.星位[6].相互) do
							临时属性 = k.." +"..v
						end
                        文本:置文本("#G/星相互合："..临时属性)
					end
				else
                    文本:置文本("#G/星位：已开")
				end
			end
			if item.星位~=nil and item.星位.组合~=nil then
                文本:置文本(string.format("#F/符石组合： %s",item.星位.组合))
                文本:置文本(string.format("#F/门派条件： %s",item.星位.门派 or "无"))
                文本:置文本(string.format("#F/部位条件： %s",取符石部位(item.星位.部位)))
                文本:置文本(string.format("#F/%s",取符石组合说明(item.星位.组合,item.星位.组合等级)))
                
				-- self.道具文本:添加文本(format("#F/符石组合： %s",item.星位.组合))
				-- self.道具文本:添加文本(format("#F/门派条件： %s",item.星位.门派 or "无"))
				-- self.道具文本:添加文本(format("#F/部位条件： %s",取符石部位(item.星位.部位)))
				-- self.道具文本:添加文本(format("#F/%s",取符石组合说明(item.星位.组合,item.星位.组合等级)))
				-- self.道具文本3:添加文本(format("#F/符石组合： %s",item.星位.组合))
				-- self.道具文本3:添加文本(format("#F/门派条件： %s",item.星位.门派 or "无"))
				-- self.道具文本3:添加文本(format("#F/部位条件： %s",取符石部位(item.星位.部位)))
				-- self.道具文本3:添加文本(format("#F/%s",取符石组合说明(item.星位.组合,item.星位.组合等级)))
			end


            if nil ~= item["专用"] then
                文本["置文本"](文本, string.format("#Y玩家%s专用", item["专用"]))
            end
            if nil ~= item["制造者"] then
                文本["置文本"](文本, string.format("#W制造者：%s", item["制造者"]))
            end
            if nil ~= item["限时"] then
                文本["置文本"](文本, string.format("#Y有效期至：%s", os.date("%Y-%m-%d %H:%M:%S", item["限时"
                    ])))
            end
            if item.熔炼效果~=nil then
				local sx = ""
				local lsb = {"气血","魔法","防御","速度","灵力","体质","魔力","力量","耐力","敏捷"}
				for i=1,#lsb do
					if item.熔炼效果[lsb[i]] ~= nil and item.熔炼效果[lsb[i]]~=0 then
						if item.熔炼效果[lsb[i]] < 0 then
							sx = sx..lsb[i].." "..item.熔炼效果[lsb[i]].." "
						else
							sx = sx..lsb[i].." +"..item.熔炼效果[lsb[i]].." "
						end
					end
				end
				if sx ~= "" then
                    文本["置文本"](文本, "#Y熔炼效果：\n" .. " #Y" .. sx)
					-- self.道具文本:添加文本("#Y/熔炼效果：\n".." #Y/"..sx)
					-- self.道具文本2:添加文本("#Y/熔炼效果：\n".." #Y/"..sx)
				end
            end
        else
            if 3 == item["分类"] then
                if nil == item["角色限制"][2] then
                    文本["置文本"](文本, "#W【装备角色】" .. item["角色限制"][1])
                else
                    文本["置文本"](文本, "#W【装备角色】" .. item["角色限制"][1] ..
                        "、" .. item["角色限制"][2])
                end
            elseif 1 == item["分类"] or 4 == item["分类"] then
                文本["置文本"](文本, "#W【装备角色】" .. item["性别限制"])
            elseif item["分类"] > 6 then
                文本["置文本"](文本, "#W【角色限制】召唤兽")
            else
                文本["置文本"](文本, "#W【角色限制】无角色限制")
            end
            if nil ~= item["制造者"] then
                文本["置文本"](文本, string.format("#W制造者：%s", item["制造者"]))
            end
            if nil == jd then
                文本["置文本"](文本, "#Y未鉴定物品")
            end
        end
    elseif zls == "萃灵晶石" then
		if item.属性 then
            文本:置文本(取同名物品描述(item.属性))
			文本:置文本("#Y/萃灵属性："..item.属性)
		end
    elseif 3 == zls then
        if 1 == item["分类"] then
            if nil == item["附带技能"] then
                item["附带技能"] = "随机"
            end
            文本["置文本"](文本, "#Y所带技能: " .. item["附带技能"])
        elseif 3 == item["分类"] then
            if item["级别限制"] then
                文本["置文本"](文本, "#Y等级 " .. item["级别限制"])
            else
                文本["置文本"](文本, "#Y随机")
            end
        elseif item.分类 == 10 then --图册
			if item.种类 and item.级别限制 ~= nil then
				文本:置文本("#Y/种类 "..item.种类.."，等级 "..item.级别限制)
			elseif item.级别限制 ~= nil then
				文本:置文本("#Y/等级 "..item.级别限制)
			else
				文本:置文本("#Y/随等级提升而提升")
			end
		elseif item.分类 == 5 then --天眼珠--炼妖石
			if item.种类 and item.级别限制 ~= nil then
				文本:置文本("#Y/种类 "..item.种类.."，等级 "..item.级别限制)
			elseif item.级别限制 ~= nil then
				文本:置文本("#Y/等级 "..item.级别限制)
			else
				文本:置文本("#Y随等级提升而提升")
			end
        elseif 11 == item["分类"] then
            文本["置文本"](文本, "#Y/等级 "..item.级别限制.."，灵气 "..item.灵气)
        end
    -- elseif 4 == zls then
    elseif 5 == zls then
        if 1 == item["分类"] then
            if item["特效"] and nil ~= item["子类"] then
                local it = __主控["取武器子类"](__主控, item["特效"])
                文本["置文本"](文本, "#Y种类 " .. it .. ", 等级 " .. item["子类"])
            else
                文本["置文本"](文本, "#Y随机")
            end
        elseif 2 == item["分类"] or 21 == item["分类"] then
            if item["名称"] == "元灵晶石" and nil == item["子类"] then
                文本["置文本"](文本, "#Y随机60-140级的元灵晶石")
            elseif nil ~= item["子类"] then
                文本["置文本"](文本, string.format("#Y等级 %d", item["子类"]))
            else
                文本["置文本"](文本, "#Y随机")
            end
        elseif 3 == item["分类"] then
            文本["置文本"](文本, "#Y功效 用于分解装备获得宝石")
        elseif 4 == item["分类"] then
        elseif 5 == item["分类"] then
            文本["置文本"](文本, string.format("#Y附加技能： %s", item["附带技能"]))
        elseif 6 == item["分类"] then
            local sdlsd={星辉石=1,附魔宝珠=1,附法宝珠=1,钨金=1,珍珠=1,战魄=1,精致碎石锤=2,碎石锤=2}
			if not sdlsd[item.名称]  then
                文本["置文本"](文本, string.format("#W【镶嵌装备】%s", item["角色限制"]))
                文本["置文本"](文本, string.format("#W【镶嵌效果】%s", item["特效"]))
            end
            if not sdlsd[item.名称]  or sdlsd[item.名称]~=2 then
                if item.临时lv then
                    文本["置文本"](文本, string.format("#Y等级 %s", item["临时lv"]))
                elseif item["级别限制"] then
                    文本["置文本"](文本, string.format("#Y等级 %s", item["级别限制"]))
                end
            end
           
        elseif 20 == item["分类"] then
            if nil ~= item["特效"] and nil ~= item["子类"] then
                文本["置文本"](文本, "#Y种类 " .. item["特效"] .. ", 等级 " .. item["子类"])
            else
                文本["置文本"](文本, "#Y随机60-100级的灵饰指南书")
            end
        end
    elseif zls == "召唤兽镶嵌" then
        -- print(111111)
        if item.特效 then
            -- table.print(item)
			local desc="属性随机"
			if item.特效=="躲避" then
				desc="增加20点躲避"
			elseif item.特效=="速度" then
				desc="增加6点速度"
			elseif item.特效=="伤害" then
				desc="增加10点伤害"
			elseif item.特效=="灵力" then
				desc="增加4点灵力"
			elseif item.特效=="防御" then
				desc="增加8点防御"
			elseif item.特效=="气血" then
				desc="增加30点气血"
			end
			if item.子类<=2 then
				文本:置文本("#W/【镶嵌装备】项圈")
				文本:置文本("#Y/等级:"..item.级别限制..","..desc)
			elseif item.子类==3 or item.子类==4 then
				文本:置文本("#W/【镶嵌装备】护腕")
				文本:置文本("#Y/等级:"..item.级别限制..","..desc)
			else
				文本:置文本("#W/【镶嵌装备】铠甲")
				文本:置文本("#Y/等级:"..item.级别限制..","..desc)
			end
		else
			if lv then
				文本:置文本(format("#Y/等级 %s",lv))
			else
				文本:置文本("#Y/（购买后随机生成。)")
			end
		end
    elseif zls == 889 then
		if item.名称=="未激活的符石" then
			文本:置文本("【激活方式】鼠标右键点击使用激活")
			文本:置文本("#Y/当前状态:#R/未激活")
			文本:置文本("#Y/等级 "..item.子类.."  "..item.颜色)
			local 组合语句="#Y/"
			for k,v in pairs(item.符石属性) do
				组合语句 = 组合语句..k.." +"..v.."  "
			end
			文本:置文本(组合语句)
		elseif item.名称=="未激活的星石" then
			文本:置文本("#Y/镶嵌部位: "..取星石部位(item.子类))
			文本:置文本("#R/激活后为专用，无法转移给他人。")
			文本:置文本("【激活方式】鼠标右键点击使用激活")
			文本:置文本("#Y/当前状态:#R/未激活")
			文本:置文本("#Y/等级 4")
			文本:置文本("#G/镶嵌在星位后，激活属性")
		elseif item.分类 == 91 and item.类型==nil then
			文本:置文本("#Y/等级 4")
			local ys = 取星位颜色(item.子类)
			文本:置文本("#Y/正面颜色: "..ys[1].." 反面颜色: "..ys[2])
			文本:置文本("#Y/镶嵌部位: "..取星石部位(item.子类))
			文本:置文本("#G/镶嵌于开运装备的星位，穿戴整套镶有星石的装备可触发符石套装效果")
			文本:置文本("#Y/耐久 150")
		elseif item.分类 == 91 and item.类型~=nil then
			文本:置文本("#Y/等级 4")
			local ys = 取星位颜色(item.子类)
			文本:置文本("#Y/颜色: "..ys[item.类型])
			文本:置文本("#Y/镶嵌部位: "..取星石部位(item.子类))
			文本:置文本("#G/镶嵌于开运装备的星位，左键点击翻转")
			文本:置文本("#G/星位: "..item.sx)
			文本:置文本("#Y/耐久 150")
		elseif item.名称=="符石卷轴" then
			文本:置文本("#w/【说明】需要加入此卷轴才可以合成三级符石，在合成过程中会被消耗。")
		else
			文本:置文本("【用途】可镶嵌有开孔数的装备上")
			local 组合语句="#Y/"
			for k,v in pairs(item.符石属性) do
				组合语句 = 组合语句..k.." +"..v.."  "
			end
			文本:置文本("#Y/等级 "..item.子类.."  "..item.颜色)
			文本:置文本(组合语句)
			文本:置文本("#Y/耐久 150")
		end    
    elseif 7 == zls then
        if 1 == item["分类"] then
            if item["名称"] == "藏宝图" then
                文本["置文本"](文本, "#Y【类型】普通藏宝图")
            else
                文本["置文本"](文本, "#Y【类型】高级藏宝图")
            end
            if item.地图名称 then
                文本["置文本"](文本, string.format("#Y【坐标】#G%s(%s,%s)", item["地图名称"], item.x, item.y))
            end
        end
    
    elseif 9 == zls then
        if 1 == item["分类"] then
            文本["置文本"](文本, "#Y右键双击使用")
        end
    elseif zls == 99 then
		文本:置文本("#Y/使用次数："..(item.次数 or "永久"))
	elseif zls == 100 then
		if item.使用期限 then
			-- os.date("%m/%d %H:%M",item.临时附魔[临时表[i]].时间)
			文本:置文本("#Y/"..os.date("%m-%d %H:%M",item.使用期限).."到期")
		end
    elseif 10 == zls then
        if 4 == item["分类"] then
            文本["置文本"](文本, "#Y右键双击使用")
        end
    elseif 11 == zls then
        if 1 == item["分类"] then
            if nil == item["地图"] then
                文本["置文本"](文本, "#Y右击后定标")
            else
                local map = 取地图名称(item["地图"])
                文本["置文本"](文本, string.format("#Y%s(%d,%d)", map, item.x, item.y))
                文本["置文本"](文本, string.format("#Y还可使用%s次", item["次数"]))
            end
        elseif 2 == item["分类"] then
            文本["置文本"](文本, string.format("#Y场景:%s,还能使用%s次", 取地图名称(item["地图"]),
                item["次数"]))
        end
    elseif 12 == zls then
        文本["置文本"](文本, string.format("#Y变化对象：%s", item["子类"]))
    elseif 13 == zls then
        文本["置文本"](文本, string.format("#Y阵型:%s", item["子类"]))
    elseif 21 == zls and nil ~= item["特效"] then
        文本["置文本"](文本, string.format("#Y说明：%s", item["特效"]))
    elseif 30 == zls then
        文本["置文本"](文本, string.format("#W卡片类型：%s", item["造型"]))
        文本["置文本"](文本, string.format("#W技能要求：%s", 数字转大写(item["等级"]) .. "级变化之术"))
        local 变身卡技能 = item["技能"]
        if "" == 变身卡技能 then
            变身卡技能 = "无"
        end
        文本["置文本"](文本, string.format("#W【附加技能】%s", 变身卡技能))
        local 组合语句 = ""
        if 0 == item["属性"] then
            组合语句 = "无"
        elseif 1 == item["单独"] then
            if 1 == item["正负"] then
                组合语句 = item["类型"] .. "+" .. item["属性"]
            else
                组合语句 = item["类型"] .. "-" .. item["属性"]
            end
        elseif 1 == item["正负"] then
            组合语句 = item["类型"] .. "+" .. item["属性"] .. "%"
        else
            组合语句 = item["类型"] .. "-" .. item["属性"] .. "%"
        end
        文本["置文本"](文本, string.format("#W【属性影响】%s", 组合语句))
        文本["置文本"](文本, string.format("#Y等级：%s  剩余使用次数：%s", item["等级"], item["次数"
            ]))
        文本["置文本"](文本, string.format("#Y持续时间：%s分钟", "15分钟*(1+变化之术等级)"))
    elseif 170 == zls then
        if 2 == item["分类"] then
            文本["置文本"](文本, string.format("#W卡片类型：%s", item["造型"]))
            local 变身卡技能 = item["技能"]
            if "" == 变身卡技能 then
                变身卡技能 = "无"
            end
            文本["置文本"](文本, string.format("#W【附加技能】%s", 变身卡技能))
            local 组合语句 = ""
            if 0 == item["属性"] then
                组合语句 = "无"
            elseif 1 == item["单独"] then
                if 1 == item["正负"] then
                    组合语句 = item["类型"] .. "+" .. item["属性"]
                else
                    组合语句 = item["类型"] .. "-" .. item["属性"]
                end
            elseif 1 == item["正负"] then
                组合语句 = item["类型"] .. "+" .. item["属性"] .. "%"
            else
                组合语句 = item["类型"] .. "-" .. item["属性"] .. "%"
            end
            文本["置文本"](文本, string.format("#W【属性影响】%s", 组合语句))
            文本["置文本"](文本, string.format("#Y等级：%s  剩余使用次数：%s", item["等级"],
                item["次数"]))
            文本["置文本"](文本, string.format("#Y持续时间：%s分钟", "1440"))
        end
    elseif 55 == zls then
        文本["置文本"](文本, "【用途】:可以镶嵌在有开运孔数的装备上")
        文本["置文本"](文本, "#Y等级 " .. item["级别限制"] .. "    " .. item["特效"])
        local sx = ""
        local 气血 = item["气血"]
        local 魔法 = item["魔法"]
        local 命中 = item["命中"]
        local 伤害 = item["伤害"]
        local 防御 = item["防御"]
        local 速度 = item["速度"]
        local 躲避 = item["躲避"]
        local 灵力 = item["灵力"]
        local 体质 = item["体质"]
        local 魔力 = item["魔力"]
        local 力量 = item["力量"]
        local 耐力 = item["耐力"]
        local 敏捷 = item["敏捷"]
        local 法术伤害 = item["法术伤害"]
        local 法术防御 = item["法术防御"]
        local 固定伤害 = item["固定伤害"]
        if 0 ~= 气血 and nil ~= 气血 then
            sx = sx .. "气血 +" .. 气血 .. " "
        end
        if 0 ~= 魔法 and nil ~= 魔法 then
            sx = sx .. "魔法 +" .. 魔法 .. " "
        end
        if 0 ~= 命中 and nil ~= 命中 then
            sx = sx .. "命中 +" .. 命中 .. " "
        end
        if 0 ~= 伤害 and nil ~= 伤害 then
            sx = sx .. "伤害 +" .. 伤害 .. " "
        end
        if 0 ~= 防御 and nil ~= 防御 then
            sx = sx .. "防御 +" .. 防御 .. " "
        end
        if 0 ~= 速度 and nil ~= 速度 then
            sx = sx .. "速度 +" .. 速度 .. " "
        end
        if 0 ~= 躲避 and nil ~= 躲避 then
            sx = sx .. "躲避 +" .. 躲避 .. " "
        end
        if 0 ~= 灵力 and nil ~= 灵力 then
            sx = sx .. "灵力 +" .. 灵力 .. " "
        end
        if 0 ~= 体质 and nil ~= 体质 then
            sx = sx .. "体质 +" .. 体质 .. " "
        end
        if 0 ~= 魔力 and nil ~= 魔力 then
            sx = sx .. "魔力 +" .. 魔力 .. " "
        end
        if 0 ~= 力量 and nil ~= 力量 then
            sx = sx .. "力量 +" .. 力量 .. " "
        end
        if 0 ~= 耐力 and nil ~= 耐力 then
            sx = sx .. "耐力 +" .. 耐力 .. " "
        end
        if 0 ~= 敏捷 and nil ~= 敏捷 then
            sx = sx .. "敏捷 +" .. 敏捷 .. " "
        end
        if 0 ~= 法术伤害 and nil ~= 法术伤害 then
            sx = sx .. "法术伤害 +" .. 法术伤害 .. " "
        end
        if 0 ~= 法术防御 and nil ~= 法术防御 then
            sx = sx .. "法术防御 +" .. 法术防御 .. " "
        end
        if 0 ~= 固定伤害 and nil ~= 固定伤害 then
            sx = sx .. "固定伤害 +" .. 固定伤害 .. " "
        end
        if "" ~= sx then
            文本["置文本"](文本, "#Y" .. sx)
        end
    elseif 100 == zls then
    -- elseif 101 == zls then
    elseif zls == 101 then
		if item.技能=="龙附" or item.技能=="轻如鸿毛" or item.技能=="魔王护持" or item.技能=="盘丝舞" or item.技能=="元阳护体" then
			文本:置文本(string.format("#Y/以"..item.等级.."级的"..item.技能.."法术效果在一定时间内强化一件武器"))
		elseif item.技能=="嗜血" or item.技能=="莲华妙法" or item.技能=="担山赶月" then --
			文本:置文本(string.format("#Y/以"..item.等级.."级的"..item.技能.."法术效果在一定时间内强化一件项链"))
		elseif item.技能=="神兵护法" or item.技能=="尸气漫天" then
			文本:置文本(string.format("#Y/以"..item.等级.."级的"..item.技能.."法术效果在一定时间内强化一件帽子"))
		elseif item.技能=="拈花妙指" or item.技能=="神木呓语" then
			文本:置文本(string.format("#Y/以"..item.等级.."级的"..item.技能.."法术效果在一定时间内强化一件鞋子"))
		elseif item.技能=="浩然正气" or item.技能=="一气化三清" then
			文本:置文本(string.format("#Y/以"..item.等级.."级的"..item.技能.."法术效果在一定时间内强化一件衣服"))
		elseif item.技能=="穿云破空" or item.技能=="神力无穷" then
			文本:置文本(string.format("#Y/以"..item.等级.."级的"..item.技能.."法术效果在一定时间内强化一件腰带"))
        else
			if item.等级 then
				文本:置文本(string.format("#Y/等级："..item.等级.." 购买后随机生成"))
			 else
			 	文本:置文本(string.format("#Y/等级：180 购买后随机生成"))
			end
		end
    elseif zls == 103 then
		if item.灵气 ~= nil then
			文本:置文本("\n#Y/灵性: "..item.灵气)
		end
	elseif zls == "可使用" then
		if item.灵气 ~= nil then
			文本:置文本("#Y/灵气: "..item.灵气)
		end
	elseif zls == "天赋符" then
		if item.属性 ~= nil then
			文本:置文本("#Y/临时属性: #R"..item.属性)
		else
			文本:置文本("#Y/当前状态: #R未激活")
		end
    elseif 106 == zls then
        if nil ~= item["附加参数3"] then
            文本["置文本"](文本, "#Y" .. item["附加参数3"])
        end
    elseif 111 == zls then
        文本["置文本"](文本, "#Y等级：" .. item["子类"])
    elseif 112 == zls and nil ~= item["装备坐骑"] then
        文本["置文本"](文本, "#W【类型】坐骑装饰品")
        文本["置文本"](文本, "#W【装备坐骑】" .. item["装备坐骑"])
        文本["置文本"](文本,
            string.format("#Y境界%s，耐久%s，战斗中增加统驭召唤兽的%s，需要等级%s", item["气血"]
                , item["耐久"], item["特技"], item["级别限制"]))
    elseif 115 == zls then
    elseif 154 == zls then
        文本["置文本"](文本, string.format("#Y祖传特制超神秘的#R%s#Y的烹饪方法",
            __秘制食谱子类()[item["子类"]]))
    elseif zls == 160 then
        文本["置文本"](文本,
            "#Y礼包内有" .. item["子类"] .. "#Y个" .. item["特效"] .. "#Y。打开礼包身上需要保留" ..
            item["子类"] .. "#Y个空位。")
    elseif zls == 203 then
        if nil == item["特效"] then
            文本["置文本"](文本, "#Y随机生成内丹")
        else
            文本["置文本"](文本, "#Y所带内丹技能:" .. item["特效"])
            文本["置文本"](文本, __取内丹介绍(item["特效"]))
        end
    elseif zls == 204 then
        文本["置文本"](文本, "#Y等级 " .. item["级别限制"])
        local sx = ""
		local 气血 = item.幻化元身属性.气血
		local 魔法 = item.幻化元身属性.魔法
		local 命中 = item.幻化元身属性.命中
		local 伤害 = item.幻化元身属性.伤害
		local 防御 = item.幻化元身属性.防御
		local 速度 = item.幻化元身属性.速度
		local 躲避 = item.幻化元身属性.躲避
		local 灵力 = item.幻化元身属性.灵力
		local 体质 = item.幻化元身属性.体质
		local 魔力 = item.幻化元身属性.魔力
		local 力量 = item.幻化元身属性.力量
		local 耐力 = item.幻化元身属性.耐力
		local 敏捷 = item.幻化元身属性.敏捷
		if 气血 ~= 0 and 气血 ~= nil then
			sx = sx.."气血 +"..气血.." "
		end
		if 魔法 ~= 0 and 魔法 ~= nil then
			sx = sx.."魔法 +"..魔法.." "
		end
		if 命中 ~= 0  and 命中 ~= nil then
			sx = sx.."命中 +"..命中.." "
		end
		if 伤害 ~= 0 and 伤害 ~= nil then
			sx = sx.."伤害 +"..伤害.." "
		end
		if 防御 ~= 0 and 防御 ~= nil  then
			sx = sx.."防御 +"..防御.." "
		end
		if 速度 ~= 0 and 速度 ~= nil  then
			sx = sx.."速度 +"..速度.." "
		end
		if 灵力 ~= 0 and 灵力 ~= nil  then
			sx = sx.."灵力 +"..灵力.." "
		end
		if 敏捷 ~= 0 and 敏捷~=nil and item.元身序列 == 25 then
			sx = sx.."敏捷 +"..敏捷.." "
		end
        if "" ~= sx then
            文本["置文本"](文本, "#Y" .. sx)
        end
        local ds = ""
		if 体质 ~= nil and 体质 ~= 0 then
			ds = ds..体质.." "
		end
		if 魔力 ~= nil and 魔力 ~= 0  then
			ds = ds..魔力.." "
		end
		if 力量 ~= nil and 力量 ~= 0 then
			ds = ds..力量.." "
		end
		if 耐力 ~= nil and 耐力 ~= 0 then
			ds = ds..耐力.." "
		end
		if 敏捷 ~= nil and 敏捷 ~= 0 and item.元身序列 ~= 25 then
			ds = ds..敏捷.." "
		end
        if ds ~= "" then
            文本["置文本"](文本, "#G" .. ds)
        end
        if item.幻化元身属性.特技 ~= nil then
			文本:置文本("#S/"..item.幻化元身属性.特技)
		end
		if item.幻化元身属性.特效 ~= nil then
			文本:置文本("#S/"..item.幻化元身属性.特效)
		end
        if nil ~= item["幻化次数"] then
            文本["置文本"](文本, "#Y幻化次数 " .. item["幻化次数"])
        end
    elseif zls == 1000 then
        文本["置文本"](文本, string.format("【法宝类型】：%s", 取法宝类型(item["子类"])))
        文本["置文本"](文本, string.format("【法宝等级】：%s级", 数字转大写(item["分类"])))
        文本["置文本"](文本, string.format("【等级限制】：%s级", item["特技"]))
        文本["置文本"](文本, string.format("#Y灵气:#G%d #Y五行:#G%s", item["魔法"], item["五行"]))
        文本["置文本"](文本, string.format("#G#Y修炼境界：第#G%d#Y层 %s", item["气血"],
            取境界(item["气血"], item["分类"])))
        local xz = ""
        if item["名称"] == "月光宝盒" and nil ~= item["地图"] then
            xz = string.format("#Y传送至%s(%s,%s),", 取地图名称(item["地图"]), item.x, item.y)
        end
        if 0 == item["使用"] then
            xz = xz .. "需要佩戴才可在战斗中发挥效用"
        elseif 2 == item["使用"] then
            xz = xz .. "无需佩戴,非战斗中选择使用"
        else
            xz = xz .. string.format("无需佩戴，在战斗中选择使用，使用回合限制：#G%d#Y",
                item["角色限制"])
        end
        文本["置文本"](文本, string.format("#Y%s", xz))
    elseif zls == 1002 then
        local 消耗 = ""
        if 1 == item["使用"] then
            消耗 = "1/3/7"
        elseif 2 == item["使用"] then
            消耗 = "1"
        elseif 3 == item["使用"] then
            消耗 = "全部"
        end
        文本["置文本"](文本, "#W【使用条件】")
        文本["置文本"](文本, string.format("#W%s门派专用，消耗#G%s#W点灵元", item["特技"], 消耗))
        文本["置文本"](文本, "#F【战斗效果】")
        文本["置文本"](文本, string.format("#F%s", item["效果"]))
        文本["置文本"](文本, string.format("#Y灵气：#G%s", item["魔法"]))
        文本["置文本"](文本, string.format("#Y修炼境界：第#G%d#Y层 %s", item["气血"],
            取境界(item["气血"], item["分类"])))
    elseif zls == 1003 then
        文本["置文本"](文本, string.format("#Y五行:#G%s", item["五行"]))
    end
end
__取内丹介绍 = function(a)
    if "迅敏" == a then
        return "#Y/拥有此技能会提高自己的伤害力与速度。"
    elseif "狂怒" == a then
        return "#Y/在此狂乱状态下必杀的伤害更高，但是受到水、土系法术攻击时承受额外的伤害。"
    elseif "阴伤" == a then
        return "#Y/疯狂攻击的第二次伤害更高，但是受到火、雷系法术攻击时承受额外的伤害。"
    elseif "静岳" == a then
        return "#Y/拥有此技能会提高自己的灵力与气血。"
    elseif "擅咒" == a then
        return "#Y/你对目标的法术伤害得到提升。"
    elseif "灵身" == a then
        return "#Y/法术带来的爆发性更强烈，只是受到强力与高强力技能打击时，所受到的伤害增加。"
    elseif "矫健" == a then
        return "#Y/拥有此技能会提高自身的气血与速度。"
    elseif "深思" == a then
        return "#Y/高冥思的效果得到加强。"
    elseif "坚甲" == a then
        return "#Y/拥有此技能后对敌人造成的反震伤害得到加强。"
    elseif "钢化" == a then
        return "#Y/带有高级防御、防御技能时的防御值增加，但是所带来的代价是受到法术伤害额外打击。"
    elseif "慧心" == a then
        return "#Y/只要集中精神，抵御封印的能力就会加强。"
    elseif "撞击" == a then
        return "#Y/物理攻击时命中率得到增加，效果与体质点成正比，同时增加一定的伤害结果。"
    elseif "无畏" == a then
        return "#Y/拥有此技能能够更好的突破反震、高级反震技能，对目标造成更大的物理伤害。"
    elseif "愤恨" == a then
        return "#Y/拥有此技能能够更好的突破幸运、高级幸运技能，对目标造成更大的物理伤害。"
    elseif "淬毒" == a then
        return "#Y/满淬毒汁的毒牙使对手更加胆寒。"
    elseif "狙刺" == a then
        return "#Y/该召唤兽对第一目标造成的法术伤害更大。"
    elseif "连环" == a then
        return "#Y/拥有此技能时，召唤兽触发连击的几率增加。"
    elseif "圣洁" == a then
        return "#Y/拥有此技能后，你的召唤兽使用法术攻击时驱鬼和高驱鬼效果得到提升。"
    elseif "灵光" == a then
        return "#Y/法术的精修必然会为你带来更多好处。"
    elseif "神机步" == a then
        return "#Y/入场时你总是活力无限，3回合内极大的增加你的躲避力。"
    elseif "腾挪劲" == a then
        return "#Y/神奇的护盾，有一定几率能够将你所受的一部分物理伤害腾挪到另一个世界。"
    elseif "玄武躯" == a then
        return "#Y/你放弃了伤害，得到的是气血的大幅度提升。"
    elseif "龙胄铠" == a then
        return "#Y/你放弃了伤害，得到的是防御的大幅度提升。"
    elseif "玉砥柱" == a then
        return "#Y/最尖锐的矛也遇到了一点麻烦。"
    elseif "碎甲刃" == a then
        return "#Y/以千钧之力击碎目标的护甲，2回合内降低目标一定防御值，效果与自身力量点相关。"
    elseif "阴阳护" == a then
        return "#Y/激发潜力，保护他人！你在保护其他目标的时候所承受的伤害减少。"
    elseif "凛冽气" == a then
        return "#Y/霸气外露，你的在场是本方所有召唤兽的强心剂，逃跑的几率也减少了。"
    elseif "舍身击" == a then
        return "#Y/置之死地而后生，你对目标造成的物理伤害值受到力量点的加成。"
    elseif "电魂闪" == a then
        return "#Y/神奇的法术，总在不经意间给你惊喜，有可能将目标携带的增益状态驱散。"
    elseif "通灵法" == a then
        return "#Y/敏锐的洞察力！能够找出目标法术防御的漏洞，对处在法术减免状态的目标造成更大的伤害。"
    elseif "双星爆" == a then
        return "#Y/强大的法术攻击接踵而来，法术连击的威力更强大。"
    elseif "催心浪" == a then
        return "#Y/虽然带有法术波动技能时法术力量无法控制，但是总会向更好的方向发展。"
    elseif "隐匿击" == a then
        return "#Y/专家级隐身。"
    elseif "生死决" == a then
        return "#Y/此刻你如天神下凡。"
    elseif "血债偿" == a then
        return "#Y/战友的离去反而激起了你的斗志。"
    elseif "隔山打牛" == a then
        return "#Y/拥有此技能后对你犹如战神附体。"
    elseif "暗度陈仓" == a then
        return "#Y/隐匿于黑暗之处，令你的攻击不会被发现"
    elseif "借力打力" == a then
        return "#Y/你的反击会让攻击你的敌人闻风丧胆"
    elseif "凌波微步" == a then
        return "#Y/飘忽不定，拥有此技能令敌人很难攻击到你"
    elseif "万佛朝宗" == a then
        return "#Y/以彼之道还之彼身，将大部分物理伤害返还攻击你的敌人"
    end
end
__每日提示 = function(lshs,名称,活动时间,NPC,组队,难度)
    lshs:置文本("#Y"..名称)
    lshs["置文本"](lshs, "")
    if 活动时间 then
		lshs:置文本("活动时间：#G"..活动时间.大写日期.."，"..活动时间.时..":"..活动时间.分..":"..活动时间.秒.."，准时开启")
	else
		lshs:置文本("活动时间：#G".."全天")
	end
	lshs:置文本("活动NPC："..NPC)
	lshs:置文本("是否组队："..组队)
	local sadw="★"
	if 难度 then
		for i=2,难度 do
			sadw=sadw.."★"
		end
	end
	lshs:置文本("难度："..sadw)
end

__取帮助文档 = function(ml, lx)
    local fhz = "该项目目前没有介绍正在完善"
    
    return fhz
end
function 取两点距离(src,dst)
	if src.x==nil then
		src.x=1
	end
	if src.y==nil then
		src.y=1
	end
	if not dst then --12.18
		dst={x=1,y=1}
	end
	if dst.x==nil then
		dst.x=1
	end
	if dst.y==nil then
		dst.y=1
	end
	return math.sqrt(math.pow(src.x-dst.x,2) + math.pow(src.y-dst.y,2))
end
生成XY = function(x, y)
    local f = {}
    f.x = tonumber(x) or 0
    f.y = tonumber(y) or 0
    setmetatable(f, {
        __add = function(a, b)
            return 生成XY(a.x + b.x, a.y + b.y)
        end,
        __sub = function(a, b)
            return 生成XY(a.x - b.x, a.y - b.y)
        end
    })
    return f
end
function DeepCopy(object)
    local lookup_table = {}
    local function _copy(object)
        if type(object) ~= "table" then
            return object
        elseif lookup_table[object] then
            return lookup_table[object]
        end
        local new_table = {}
        lookup_table[object] = new_table
        for key, value in pairs(object) do
            new_table[_copy(key)] = _copy(value)
        end
        return setmetatable(new_table, getmetatable(object))
    end
    return _copy(object)
end
local xys = 生成XY
local pi = math.pi
local pi2 = pi*2
local sqrt = math.sqrt
local pow = math.pow
local abs = math.abs
local atan = math.atan
local deg = math.deg
local floor = math.floor
local cos = math.cos
local sin = math.sin
local insert = table.insert
local remove = table.remove

function 取两点孤度(src,dst)
	if dst == nil then return 0 end
	if(dst.y ==src.y and dst.x==src.x)then
		return 0
	elseif(dst.y >=src.y and dst.x<=src.x)then
		return pi-abs(atan((dst.y-src.y)/(dst.x-src.x)))
	elseif(dst.y <=src.y and dst.x>=src.x)then
		return pi2-abs(atan((dst.y-src.y)/(dst.x-src.x)))
	elseif(dst.y <=src.y and dst.x<=src.x)then
		return atan((dst.y-src.y)/(dst.x-src.x))+pi

	elseif(dst.y >=src.y and dst.x>=src.x)then
		return atan((dst.y-src.y)/(dst.x-src.x))
	end
end
function 取移动坐标(src,dst,r)
	local a = 取两点孤度 (src,dst)
	return xys(r* cos(a) +src.x ,r* sin(a)+src.y)
end
function 取两点角度(src,dst)
	return deg(取两点孤度(src,dst))
end
function 角度算四方向(角)
	local 方向 = 0
	if(角 >0 and 角 < 91) then
		方向 = 0 --"东北"
	elseif(角 > 90 and 角 < 181) then
		方向 = 1 --"西北"
	elseif(角 > 180 and 角 < 271) then
		方向 = 2 --"西南"
	elseif(角 > 270 or  角 == 0) then
		方向 = 3 --"东南"
	end
	return 方向
end

function 角度算八方向(角)
	local 方向 = 0
	if(角 > 157 and 角 < 203) then
		方向 = 5 --"左"
	elseif(角 >202 and 角 < 248) then
		方向 = 2 --"左上"
	elseif(角 > 247 and 角 < 293) then
		方向 = 6 --"上"
	elseif(角 > 292 and 角 < 338) then
		方向 = 3 --"右上"
	elseif(角 > 337 or 角 < 24 ) then
		方向 = 7        --"右"
	elseif( 角 > 23 and 角 < 69 ) then
		方向 = 0       --"右下"
	elseif(角 > 68 and 角 < 114 )then
		方向 = 4 --"下"
	elseif(角 > 113 ) then
		方向 = 1 --"左下"
	end
	return 方向

end

table.tostring = function (t)
	local mark={}
	local assign={}
	local function ser_table(tbl,parent)
		mark[tbl]=parent
		local tmp={}
		for k,v in pairs(tbl) do
			local key= type(k)=="number" and "["..k.."]" or "[".. string.format("%q", k) .."]"
			if type(v)=="table" then
				local dotkey= parent.. key
				if mark[v] then
					table.insert(assign,dotkey.."='"..mark[v] .."'")
				else
					table.insert(tmp, key.."="..ser_table(v,dotkey))
				end
			elseif type(v) == "string" then
				table.insert(tmp, key.."=".. string.format('%q', v))
			elseif type(v) == "number" or type(v) == "boolean" then
				table.insert(tmp, key.."=".. tostring(v))
			end
		end
		return "{"..table.concat(tmp,",").."}"
	end
	return "do local ret="..ser_table(t,"ret")..table.concat(assign," ").." return ret end"
end
function 分割字符(str,tv)
	local t = tv or {}
	local i = 1
	local ascii = 0
	while true do
		ascii = string.byte(str, i)
		if ascii then
			if ascii < 127 then
				table.insert(t, string.sub(str, i, i))
				i = i+1
			else
				table.insert(t, string.sub(str, i, i+1))
				i = i+2
			end
		else
			break
		end
	end
	return t
end
两点算近战攻击坐标 = function(target, attacker, offset)
    local jd = floor(取两点角度(target, attacker))
    local fx = 角度算四方向(jd)

    if fx == 0 then
        return 生成XY(target.x + offset.x, target.y + offset.y), 生成XY(target.x + offset.x * 0.2 + 1, target.y + offset.y * 0.2 - 2)
    elseif fx == 1 then
        return 生成XY(target.x - offset.x + 5, target.y + offset.y), 生成XY(target.x - offset.x * 0.2 + 1, target.y + offset.y * 0.2)
    elseif fx == 2 then
        return 生成XY(target.x - offset.x, target.y - offset.y), 生成XY(target.x - offset.x * 0.2 + 1, target.y + offset.y * 0.2)
    elseif fx == 3 then
        return 生成XY(target.x + offset.x, target.y - offset.y - 8), 生成XY(target.x - offset.x * 0.2 + 1, target.y + offset.y * 0.2)
    end
end

九黎城攻击技能 = {枫影二刃 = 1,三荒尽灭 = 1,力辟苍穹 = 1,铁血生风 = 1,一斧开天 = 1,魔神之刃 = 1}

Fight特效加速={}--越小越慢
Fight特效加速["生命之泉"]={附加y=-75}
Fight特效加速["炼气化神"]={附加y=-75}
Fight特效加速["紧箍咒"]={附加y=-35}
Fight特效加速["龙腾"]={加速=0.03}
Fight特效加速["月光"]={加速=0.03}
Fight特效加速["唧唧歪歪"]={加速=0.015}
Fight特效加速["新_唧唧歪歪"]={加速=0.02}
Fight特效加速["牵魂蛛丝"]={加速=0.04}
Fight特效加速["血雨"]={加速=0.04}
Fight特效加速["碎星诀"]={附加y=-50,加速=0.03}
Fight特效加速["追魂刺"]={加速=0.04}
Fight特效加速["新_金刚护法"]={加速=0.04}
Fight特效加速["新_金刚护体"]={加速=0.02}
Fight特效加速["新_失心符"]={加速=0.04}
Fight特效加速["新_日月乾坤"]={加速=0.04}
Fight特效加速["百爪狂杀"]={加速=1.5}
Fight特效加速["法术暴击"]={加速=3}
Fight特效加速["琴音三叠1"]={加速=3}
Fight特效加速["琴音三叠2"]={附加x=100,加速=3}
Fight特效加速["琴音三叠3"]={加速=0.04}

skill无需状态={
    ["减防御"]=1,
	["超级永恒"]=1,
    ["发瘟匣"]=1,
    ["修罗隐身"]=1,
    ["满天花雨"]=1,
    ["其疾如风"]=1,
    ["其徐如林"]=1,
    ["侵掠如火"]=1,
    ["岿然如山"]=1,
    ["无间地狱"]=1,
    ["清静菩提"]=1,
    ["媚眼如丝"]=1,
    ["晶清诀"]=1,
    ["铜头铁臂"]=1,
    ["水清诀"]=1,
    ["玉清诀"]=1,
    ["反间之计"]=1,
    ["北冥之渊"]=1,
    ["天煞"]=1,
    ["苍鸾怒击"]=1,
    ["狂怒"]=1,
    ["九梵清莲"]=1,
    ["傲视"]=1,
    ["舍生取义"]=1,
    ["无需特效"]=1,
    ["河东狮吼"]=1,
    ["光辉之甲"]=1,
    ["圣灵之甲"]=1,
    ["流云诀"]=1,
    ["复活"]=1,
    ["破甲术"]=1,
    ["啸风诀"]=1,
    ["野兽之力"]=1,
    ["碎甲术"]=1,
    ["魂飞魄散"]=1,
    ["汲魂"]=1,
    ["绝殇"]=1,
    ["宁心"]=1,
    ["知己知彼"]=1,
    ["翩鸿一击"]=1,
    ["天魔解体"]=1,
    ["分身术"]=1,
    ["照妖镜"]=1,
    ["魔兽之印"]=1,
    ["诸天看护"]=1,
    ["无畏布施（减）"]=1,
    ["狂袭"]=1,
    ["感念"]=1,
    ["顾盼生姿"]=1,
    ["八戒上身"]=1,
    ["杀威铁棒"]=1,
    ["龙骇"]=1,
    ["破浪"]=1,
    ["盘龙"]=1,
    ["龙啸"]=1,
    ["清吟"]=1,
    ["潜龙在渊"]=1,
    ["鹰啸"]=1,
    ["肝胆"]=1,
    ["长啸"]=1,
    ["练魂"]=1,
    ["天雷斩"]=1,
    ["雷怒霆激"]=1,
    ["沙威铁棒"]=1,
    ["威震凌霄"]=1,
    ["天地洞明"]=1,
    ["灵刃"]=1,
    ["灵法"]=1,
    ["灵断"]=1,
    ["御风"]=1,
    ["怒吼"]=1,
    ["阳护"]=1,
    ["护佑"]=1,
    ["疯狂"]=1,
    ["魔息术"]=1,
    ["重创"]=1,
    ["攻伐"]=1,
    ["蔓延"]=1,
    ["还元"]=1,
    ["怒霆"]=1,
    ["魔冥"]=1,
    ["六道无量"]=1,
    ["愈勇"]=1,
    ["放下屠刀"]=1,
    ["诵经"]=1,
    ["凭虚御风"]=1,
    ["惊锋"]=1,
    ["魂魇"]=1,
    ["狂战"]=1,
    ["酣战"]=1,
    ["开辟"]=1,
    ["清吟"]=1,
    ["柳暗花明"]=1,
    ["莲音"]=1,
    ["超级赐福·四季"]=1,
	["超级赐福·五福"]=1,
	["超级赐福·六临"]=1,
	["超级赐福·七神"]=1,
	["超级赐福·八欲"]=1,
	["超级赐福·九功"]=1,
	["超级赐福·十满"]=1,
	["超级赐福·万利"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,
    ["清吟"]=1,

}

skill法术特效后置={
	["阎罗令"]=1,
    -- ["清吟"]=1,
    -- ["清吟"]=1,
    -- ["清吟"]=1,
}


skill恢复={
	["无穷妙道"]=1,
	["地涌金莲"]=1,
	["星月之惠"]=1,
	["玉清诀"]=1,
	["晶清诀"]=1,
	["冰清诀"]=1,
	["水清诀"]=1,
	["四海升平"]=1,
	["命归术"]=1,
	["气归术"]=1,
	["凝神诀"]=1,
	["凝气诀"]=1,
	["命疗术"]=1,
	["心疗术"]=1,
	["气疗术"]=1,
	["归元咒"]=1,
	["乾天罡气"]=1,
	["我佛慈悲"]=1,
	["杨柳甘露"]=1,
	["推拿"]=1,
	["推气过宫"]=1,
	["活血"]=1,
	["妙手回春"]=1,
	["救死扶伤"]=1,
	["解毒"]=1,
	["百毒不侵"]=1,
	["宁心"]=1,
	["解封"]=1,
	["清心"]=1,
	["驱魔"]=1,
	["驱尸"]=1,
	["寡欲令"]=1,
	["复苏"]=1,
	["普渡众生"]=1,
	["慈航普度"]=1,
	["起死回生"]=1,
	["回魂咒"]=1,
	["舍生取义"]=1,
	["自在心法"]=1,
	["还阳术"]=1,
	["醍醐灌顶"]=1,
	["净土灵华"]=1,
	["重生"]=1,
	["莲花心音"]=1,
	["由己渡人"]=1,
	["妙悟"]=1,
	["六尘不染"]=1,
	["电光火石"]=1,
	["魍魉追魂"]=1,
	["清风望月"]=1,
	["绝处逢生"]=1,
	["金刚怒目"]=1,
}

skill物攻={
	--超级技能
	["超级壁垒击破"]=1,
	["牛刀小试"]=1,
	["剑荡四方"]=1,
	["翻江搅海"]=1,
	["满天花雨"]=1,
	["破血狂攻"]=1,
	["弱点击破"]=1,
	["善恶有报"]=1,
	["惊心一剑"]=1,
	["壁垒击破"]=1,
	["蚩尤之搏"]=1,
	["横扫千军"]=1,
    ["狂莽一击"]=1,
	["破碎无双"]=1,
	["狮搏"]=1,
	["象形"]=1,
	["连环击"]=1,
	["鹰击"]=1,
	["烟雨剑法"]=1,
	["飘渺式"]=1,
	["天雷斩"]=1,
	["裂石"]=1,
	["断岳势"]=1,
	["天崩地裂"]=1,
	["浪涌"]=1,
	["惊涛怒"]=1,
	["力劈华山"]=1,
	["破釜沉舟"]=1,
	["死亡召唤"]=1,
	["疯狂鹰击"]=1,
	["同伤式"]=1,
	["六道无量"]=1,
	["百爪狂杀"]=1,
	["翩鸿一击"]=1,
	["长驱直入"]=1,
	["腾雷"]=1,
	["天神怒斩"]=1,
	["水击三千"]=1,
	["惊天动地"]=1,
	["摧枯拉朽"]=1,
	["披挂上阵"]=1,
	["葬玉焚花"]=1,
	["风雷斩"]=1,
	["日光耀"]=1,
	["靛沧啸"]=1,
	["巨岩击"]=1,
	["苍茫刺"]=1,
	["地裂焚"]=1,
	["威仪九霄"]=1,
	["千蛛噬魂"]=1,
	["蛛丝缠绕"]=1,
	["绝命毒牙"]=1,
	["无赦咒令"]=1,
	["百鬼噬魂"]=1,
	["生杀予夺"]=1,
	["血影蚀心"]=1,
	["困兽之斗"]=1,
	["敲金击玉"]=1,
	["金击式"]=1,
	["天命剑法"]=1,
	["落土止息"]=1,
	["威震凌霄"]=1,
	["当头一棒"]=1,
	["神针撼海"]=1,
	["杀威铁棒"]=1,
	["泼天乱棒"]=1,
	["棒打雄风"]=1,
	["花谢花飞"]=1,
    ["枫影二刃"] = 1,
    ["三荒尽灭"] = 1,
    ["力辟苍穹"] = 1,
    ["铁血生风"] = 1,
    ["一斧开天"] = 1,
    ["魔神之刃"] = 1

}
skill封印={
	["摧心术"]=1,
	["反间之计"]=1,
	["催眠符"]=1,
	["失心符"]=1,
	["落魄符"]=1,
	["失忆符"]=1,
	["追魂符"]=1,
	["离魂符"]=1,
	["失魂符"]=1,
	["定身符"]=1,
	["莲步轻舞"]=1,
	["如花解语"]=1,
	["似玉生香"]=1,
	["碎玉弄影"]=1,
	["娉婷袅娜"]=1,
	["错乱"]=1,
	["百万神兵"]=1,
	["日月乾坤"]=1,
	["威慑"]=1,
	["含情脉脉"]=1,
	["魔音摄魂"]=1,
	["夺魄令"]=1,
	["惊魂掌"]=1,
	["煞气诀"]=1,
	["秘传封印"]=1,
	["顺势而为"]=1,
	["妖风四起"]=1,
	["金刚镯"]=1,
	["偷龙转凤"]=1,
	["毁灭之光"]=1,
	["一笑倾城"]=1,
	["镇妖"]=1,
	["掌心雷"]=1,
	["八戒上身"]=1,
}
skill减益={
	["尸腐毒"]=1,
	["紧箍咒"]=1,
	["放下屠刀"]=1,
	["碎甲术"]=1,
	["破甲术"]=1,
	["河东狮吼"]=1,
	["雾杀"]=1,
	["笑里藏刀"]=1,
	["锢魂术"]=1,
	["月下霓裳"]=1,
	["否极泰来"]=1,
	["尸腐无常"]=1,
	["碎甲符"]=1,
	["凋零之歌"]=1,
	["魂飞魄散"]=1,
	["落花成泥"]=1,
	["雷浪穿云"]=1,
	["知己知彼"]=1,
	["画地为牢"]=1,
	["牵魂蛛丝"]=1,
	["噬毒"]=1,
	["凝滞术"]=1,
	["停陷术"]=1,
	["死亡之音"]=1,
}
skill增益={
    ["超级永恒"]=1,
	["变身"]=1,
	["移魂化骨"]=1,
	["狂怒"]=1,
	["后发制人"]=1,
	["杀气诀"]=1,
	["安神诀"]=1,
	["分身术"]=1,
	["达摩护体"]=1,
	["金刚护法"]=1,
	["潜龙在渊"]=1,
	["天雷灌注"]=1,
	["雷怒霆激"]=1,
	["霹雳弦惊"]=1,
	["金刚护体"]=1,
	["韦陀护法"]=1,
	["一苇渡江"]=1,
	["佛法无边"]=1,
	["楚楚可怜"]=1,
	-- ["天神护法"]=1,
	["乘风破浪"]=1,
	["神龙摆尾"]=1,
	["生命之泉"]=1,
	["炼气化神"]=1,
	["天地同寿"]=1,
	["乾坤妙法"]=1,
	["普渡众生"]=1,
	["灵动九天"]=1,
	["幽冥鬼眼"]=1,
	["修罗隐身"]=1,
	["火甲术"]=1,
	["魔王回首"]=1,
	["定心术"]=1,
	["极度疯狂"]=1,
	["魔息术"]=1,
	["天魔解体"]=1,
	["盘丝阵"]=1,
	["不动如山"]=1,
	["碎星诀"]=1,
	["镇魂诀"]=1,
	["明光宝烛"]=1,
	["金身舍利"]=1,
	["炎护"]=1,
	["蜜润"]=1,
	["法术防御"]=1,
	["太极护法"]=1,
	["罗汉金钟"]=1,
	["流云诀"]=1,
	["啸风诀"]=1,
	["野兽之力"]=1,
	["魔兽之印"]=1,
	["光辉之甲"]=1,
	["圣灵之甲"]=1,
	["牛劲"]=1,
	["毒萃"]=1,
	["波澜不惊"]=1,
	["其疾如风"]=1,
	["其徐如林"]=1,
	["侵掠如火"]=1,
	["岿然如山"]=1,
	["龙战于野"]=1,
	["花语歌谣"]=1,
	["无双战魂"]=1,
	["无间地狱"]=1,
	["媚眼如丝"]=1,
	["清静菩提"]=1,
	["鸣雷诀"]=1,
	["逆鳞"]=1,
	["钟馗论道"]=1,
	["诸天看护"]=1,
	["渡劫金身"]=1,
	["天神护体"]=1,
	["凝神术"]=1,
	["颠倒五行"]=1,
	["莲心剑意"]=1,
	["剑意莲心"]=1,
	["幻镜术"]=1,
	["同舟共济"]=1,
	["真君显灵"]=1,
	["无畏布施"]=1,
	["北冥之渊"]=1,
	["功德无量"]=1,
	["森罗迷瘴"]=1,
	["心随意动"]=1,
	["燃血术"]=1,
	["化羽为血"]=1,
	["气慑天军"]=1,
	["九幽除名"]=1,
	["铜头铁臂"]=1,
	["无所遁形"]=1,
	["呼子唤孙"]=1,
	["齐天神通"]=1,
	["金刚不坏"]=1,
	["灵能激发"]=1,
	["修罗咒"]=1,
	["身似菩提"]=1,
	["菩提心佑"]=1,
	["心如明镜"]=1,
	["法力陷阱"]=1,
}

skill无需物理={
    ["高级连击"]=1,
    ["理直气壮"]=1,
    ["乘胜追击"]=1,
    ["无需特效"]=1,
    ["武神之怒"]=1,
    ["力劈华山"]=1,
    ["千蛛噬魂"]=1,
    ["蛛丝缠绕"]=1,
    ["狂莽一击"]=1,
    ["六道无情"]=1,
    ["破釜沉舟"]=1,
    ["满天花雨"]=1,
    ["惊涛怒"]=1,
    ["翻江搅海"]=1,
    ["神针撼海"]=1,
    ["神牛攻击"]=1,
    ["葬玉焚花"]=1,
    ["自矜"]=1,
    ["威震凌霄"]=1,
    ["腾雷"]=1,
    ["同伤式"]=1,
    ["死亡召唤"]=1,
 --    ["日光耀"]=1,
	-- ["靛沧啸"]=1,
	-- ["巨岩击"]=1,
	-- ["苍茫刺"]=1,
	-- ["地裂焚"]=1,
}

skill无需法术={
    ["高级连击"]=1,
    ["其疾如风"]=1,
    ["其徐如林"]=1,
    ["侵掠如火"]=1,
    ["无间地狱"]=1,
    ["媚眼如丝"]=1,
    ["修罗隐身"]=1,
    ["法术防御"]=1,
    ["无需特效"]=1,
    ["傲视"]=1,
    ["无敌牛虱"]=1,
    ["无敌牛妖"]=1,
    ["无畏布施"]=1,
    ["高级进击必杀"]=1,
    ["进击必杀"]=1,
    ["高级进击法爆"]=1,
    ["进击法爆"]=1,
    ["雷怒霆激"]=1,
    ["气慑天军"]=1,
    ["赤焰"]=1,
    ["阳护"]=1,
    ["盾气"]=1,
    ["高级盾气"]=1,
    ["遗志"]=1,
    ["高级遗志"]=1,
    ["金刚不坏"]=1,
    ["北冥之渊"]=50,
    ["鲲鹏出场"]=1,
    ["超级永恒"]=1,
}


skill法攻={
    ["水攻"]=10,
    ["观照万象"]=10,
	["烈火"]=20,
	["落岩"]=10,
	["雷击"]=10,
	["尸腐毒"]=20,
	["勾魂"]=20,
	["摄魄"]=20,
	["瘴气"]=20,
	["雨落寒沙"]=20,
	["夺命咒"]=30,

	["落叶萧萧"]=50,
	["蛊木迷瘴"]=50,
	["荆棘舞"]=40,
	["尘土刃"]=50,
	["冰川怒"]=30,
	["唧唧歪歪"]=50,
	["谆谆教诲"]=40,
	["五雷咒"]=50,
	["落雷符"]=50,
	["雷霆万钧"]=30,

	["龙卷雨击"]=50,
	["二龙戏珠"]=50,
	["龙腾"]=40,
	["三昧真火"]=20,
	["飞砂走石"]=50,
	["泰山压顶"]=50,
	["水漫金山"]=50,
	["亢龙归海"]=50,
	["风雷韵动"]=30,
	["地狱烈火"]=50,
    ["超级三昧真火"]=20,
	["奔雷咒"]=40,
    ["超级泰山压顶"]=50,
	["超级水漫金山"]=50,
	["超级地狱烈火"]=50,
	["超级奔雷咒"]=40,
	["月光"]=50,
	["上古灵符"]=50,
	["血雨"]=50,
	["魔火焚世"]=50,
	["八凶法阵"]=30,
	["流沙轻音"]=50,
	["叱咤风云"]=50,
	["食指大动"]=50,
	["风卷残云"]=50,

	["天降灵葫"]=50,
	["摇头摆尾"]=50,
	["风云变色"]=50,
	["魔焰滔天"]=50,
	["扶摇万里"]=50,
	["五蕴神焰"]=50,
	["漫卷狂沙"]=50,
	["古藤秘咒"]=40,
	["疾风秋叶"]=50,
	["枯木逢春"]=50,
	["棒掀北斗"]=50,
	["灵彻太虚"]=50,
	["兴风作浪"]=50,
	["棍打诸神"]=50,
	["意马心猿"]=50,
	["天罗地网"]=50,
	["子母神针"]=50,
	["飞花摘叶"]=30,
	["姐妹同心"]=50,
	["鸿渐于陆"]=50,

	["自爆"]=50,

	["五雷轰顶"]=50,
	["龙吟"]=50,
	["苍茫树"]=30,
	["靛沧海"]=30,
	["日光华"]=30,
	["地裂火"]=30,
	["巨岩破"]=30,
	["判官令"]=10,
	["阎罗令"]=10,
	["黄泉之息"]=40,
	["追魂刺"]=50,
	["云暗天昏"]=50,
	["诅咒之伤"]=50,
	["吸血特技"]=10,
	["夜舞倾城"]=50,
	["琴音三叠"]=50,
	["琴音三叠1"]=50,
	["琴音三叠2"]=50,
	["琴音三叠3"]=50,
}

skill无需抖动={
    ["地狱烈火"]=1,
    ["泰山压顶"]=1,
    ["龙吟"]=1,
    ["五雷咒"]=1,
    ["龙卷雨击"]=1,
    ["飞砂走石"]=1,
    ["扶摇万里"]=1,
    ["月光"]=1,
    ["唧唧歪歪"]=1,
    ["谆谆教诲"]=1,
    ["风云变色"]=50,
    ["天降灵葫"]=50,
    ["五蕴神焰"]=50,
    ["夜舞倾城"]=50,
    ["诅咒之伤"]=50,
    ["追魂刺"]=50,
    ["扶摇万里"]=50,
    ["流沙轻音"]=50,
    ["琴音三叠"]=50,
	["琴音三叠1"]=50,
	["琴音三叠2"]=50,
	["琴音三叠3"]=50,

}

skill战斗道具={
	["醉仙果"]=5,
    ["逍遥镜"]=5,   
    ["七珍丸"]=5,
    ["九转续命丹"]=5,
    ["十全大补丸"]=5,
    ["固本培元丹"]=5,
    ["舒筋活络丸"]=5,
    ["凝气丸"]=5,
	["金创药"]=5,
    ["小还丹"]=5,
    ["千年保心丹"]=5,
    ["金香玉"]=5,
    ["五龙丹"]=5,
    ["翡翠豆腐"]=5,
    ["佛跳墙"]=5,
    ["蛇蝎美人"]=5,
    ["风水混元丹"]=5,
    ["定神香"]=5,
    ["十香返生丸"]=5,
    ["佛光舍利子"]=5,
    ["九转回魂丹"]=5,
    ["珍露酒"]=5,
    ["虎骨酒"]=5,
    ["女儿红"]=5,
    ["蛇胆酒"]=5,
    ["醉生梦死"]=5,
    ["梅花酒"]=5,
    ["百味酒"]=5,
    ["天不老"]=5,
    ["紫石英"]=5,
    ["血色茶花"]=5,
    ["熊胆"]=5,
    ["鹿茸"]=5,
    ["六道轮回"]=5,
    ["凤凰尾"]=5,
    ["硫磺草"]=5,
    ["龙之心屑"]=5,
    ["火凤之睛"]=5,
    ["四叶花"]=5,
    ["天青地白"]=5,
    ["七叶莲"]=5,
    ["丁香水"]=5,
    ["月星子"]=5,
    ["仙狐涎"]=5,
    ["地狱灵芝"]=5,
    ["麝香"]=5,
    ["血珊瑚"]=5,
    ["餐风饮露"]=5,
    ["白露为霜"]=5,
    ["天龙水"]=5,
    ["孔雀红"]=5,
    ["紫丹罗"]=5,
    ["佛手"]=5,
    ["旋复花"]=5,
    ["龙须草"]=5,
    ["百色花"]=5,
    ["香叶"]=5,
    ["白玉骨头"]=5,
    ["鬼切草"]=5,
    ["灵脂"]=5,
    ["曼陀罗花"]=5,
    ["飞刀"]=4,
    ["飞蝗石"]=4,
    ["铁蒺黎"]=4,
    ["无影神针"]=4,
    ["孔雀翎"]=4,
    ["含沙射影"]=4,
    ["回龙镊魂镖"]=4,
    ["寸阴若梦"]=4,
    ["魔睛子"]=4,
    ["顺逆神针"]=4,
    ["乾坤袋"]=4,
    ["惊魂铃"]=4,
    ["鬼泣"]=4,
    ["发瘟匣"]=4,
    ["断线木偶"]=4,
    ["摄魂"]=4,
    ["无魂傀儡"]=4,
    ["无尘扇"]=4,
    ["缚妖索"]=4,
    ["捆仙绳"]=4,
    ["缚龙索"]=4,
    ["现形符"]=4,
    ["番天印"]=4,
    ["落雨金钱"]=4,
    ["照妖镜"]=4,
    ["落宝金钱"]=4,
    ["无字经"]=4,
    ["舞雪冰蝶"]=4,
    ["紫火如意"]=4,
    ["金钱镖"]=4,
    ["乾坤玄火塔"]=2,
    ["混元伞"]=2,
    ["五彩娃娃"]=2,
    ["万鬼幡"]=2,
    ["聚妖铃"]=2,
    ["苍白纸人"]=2,
    ["干将莫邪"]=2,
    ["铸兵锤"]=2,
    ["分水"]=2,
    ["缩地尺"]=2,
    ["赤焰"]=2,
    ["天煞"]=2,
    ["神木宝鼎"]=2,
    ["金蟾"]=2,
    ["九梵清莲"]=2,
    ["苍灵雪羽"]=2,
    ["璞玉灵钵"]=2,
    ["烽火狼烟"]=2,
    ["清心咒"]=5,
    ["罗汉珠"]=5,
}
Fightdiwo={}
Fightdiwo["干将莫邪"]=1
Fightdiwo["苍白纸人"]=1
Fightdiwo["混元伞"]=1
Fightdiwo["乾坤玄火塔"]=1
Fightdiwo["护盾"]=1
Fightdiwo["无畏布施"]=1
Fightdiwo["盾气"]=1
Fightdiwo["高级盾气"]=1

Fightztqz={}
Fightztqz["寡欲令"]={py={0,25},cp = true}
Fightztqz["驱魔"]={py={0,25},cp = true}
Fightztqz["韦陀护法"]={py={0,-15},cp = true}
Fightztqz["红袖添香"]={py={10,0},cp = true}
Fightztqz["天罗地网"]={py={1,3},cp = false}
Fightztqz["乾坤玄火塔"]={py={1,3},cp = false}
Fightztqz["干将莫邪"]={py={1,3},cp = false}
Fightztqz["莲步轻舞"]={py={1,3},cp = false}
Fightztqz["如花解语"]={py={1,3},cp = false}
Fightztqz["定身符"]={py={5,2} ,cp = false}
Fightztqz["镇妖"]={py={0,0},cp = false}
Fightztqz["失忆符"]={py={0,0},cp = false}
Fightztqz["催眠符"]={py={0,0},cp = false}
Fightztqz["落魄符"]={py={0,0},cp = false}
Fightztqz["追魂符"]={py={0,0},cp = false}
Fightztqz["含情脉脉"]={py={0,0},cp = false}
Fightztqz["血雨"]={py={0,0},cp = false}
Fightztqz["离魂符"]={py={0,0},cp = false}
Fightztqz["失心符"]={py={0,0},cp = false}
Fightztqz["失魂符"]={py={0,0},cp = false}
Fightztqz["碎甲符"]={py={0,0},cp = false}
Fightztqz["象形"]={py={0,0},cp = false}
Fightztqz["似玉生香"]={py={0,0},cp = false}
Fightztqz["横扫千军"]={py={0,0},cp = false}
Fightztqz["后发制人"]={py={0,0},cp = false}
Fightztqz["苍白纸人"]={py={0,0},cp = false}
Fightztqz["混元伞"]={py={0,0},cp = false}
Fightztqz["乘风破浪"]={py={0,0},cp = false}
Fightztqz["一苇渡江"]={py={0,0},cp = false}
Fightztqz["逆鳞"]={py={0,0},cp = false}
Fightztqz["夺魄令"]={py={0,0},cp = false}
Fightztqz["百万神兵"]={py={0,0},cp = false}
Fightztqz["护法紫丝"]={py={0,0},cp = false}
Fightztqz["死亡召唤"]={py={0,0},cp = false}
Fightztqz["法术防御"]={py={0,0},cp = false}
Fightztqz["魔音摄魂"]={py={0,0},cp = false}
Fightztqz["炎护"]={py={0,0},cp = false}
Fightztqz["妖风四起"]={py={0,0},cp = false}
Fightztqz["娉婷袅娜"]={py={0,0},cp = false}
Fightztqz["月下霓裳"]={py={0,0},cp = false}
Fightztqz["颠倒五行"]={py={2,-31},cp = true}
Fightztqz["脱壳"]={py={2,-31},cp = true}
Fightztqz["极度疯狂"]={py={2,-31},cp = true}
Fightztqz["鸣雷诀"]={py={2,-31},cp = true}
Fightztqz["变身"]={py={2,-31},cp = true}
Fightztqz["九幽除名"]={py={2,-31},cp = true}
Fightztqz["肝胆相照"]={py={2,-31},cp = true}
Fightztqz["雾杀"]={py={2,-31},cp = true}
Fightztqz["惊魂掌"]={py={2,-31},cp = true}
Fightztqz["魔王回首"]={py={2,-31},cp = true}
Fightztqz["牛劲"]={py={2,-31},cp = true}
Fightztqz["幽冥鬼眼"]={py={2,-31},cp = true}
Fightztqz["摄魂"]={py={2,-31},cp = true}
Fightztqz["镇魂诀"]={py={2,-31},cp = true}
Fightztqz["战诀"]={py={2,-31},cp = true}
Fightztqz["杀气诀"]={py={2,-31},cp = true}
Fightztqz["无畏布施"]={py={2,-31},cp = true}
Fightztqz["金蟾"]={py={2,-31},cp = true}
Fightztqz["重伤"]={py={2,-75},cp = true}
Fightztqz["蚀天"]={py={2,-75},cp = true}
Fightztqz["龙战于野"]={py={0,-115},cp = true}
Fightztqz["定心术"]={py={0,-115},cp = true}
Fightztqz["天神护体"]={py={0,-115},cp = true}
Fightztqz["木精"]={py={0,-115},cp = true}
Fightztqz["幻镜术"]={py={2,12},cp = true}
Fightztqz["普渡众生"]={py={2,-75},cp = true}
Fightztqz["生命之泉"]={py={2,-75},cp = true}
Fightztqz["炼气化神"]={py={2,-75},cp = true}
Fightztqz["锢魂术"]={py={2,-75},cp = true}
Fightztqz["花语歌谣"]={py={2,-75},cp = true}
Fightztqz["蜜润"]={py={2,-75},cp = true}
Fightztqz["碎甲刃"]={py={2,-75},cp = true}
Fightztqz["魔冥"]={py={2,-75},cp = true}
Fightztqz["自矜"]={py={2,-75},cp = true}
Fightztqz["龙骇"]={py={3,-85},cp = true}
Fightztqz["灵能激发"]={py={3,-85},cp = true}
Fightztqz["明光宝烛"]={py={3,-95},cp = true}
Fightztqz["金身舍利"]={py={3,-95},cp = true}
Fightztqz["苍灵雪羽"]={py={3,-95},cp = true}
Fightztqz["璞玉灵钵"]={py={3,-95},cp = true}
Fightztqz["烽火狼烟"]={py={-4,-85},cp = true}
Fightztqz["舞雪冰蝶"]={py={-4,-85},cp = true}
Fightztqz["落花成泥"]={py={0,-75},cp = true}
Fightztqz["无尘扇"]={py={-2,-65},cp = true}
Fightztqz["断线木偶"]={py={-2,-65},cp = true}
Fightztqz["无魂傀儡"]={py={-2,-65},cp = true}
Fightztqz["风灵"]={py={-3,-95},cp = true}
Fightztqz["移魂化骨"]={py={-3,-95},cp = true}
Fightztqz["碎星诀"]={py={-2,-125},cp = true}
Fightztqz["愈勇"]={py={-2,-125},cp = true}
Fightztqz["瘴气"]={py={-2,-48},cp = true}
Fightztqz["无所遁形"]={py={0,-48},cp = true}
Fightztqz["齐天神通"]={py={0,-48},cp = true}
Fightztqz["清净"]={py={0,-60},cp = true}
Fightztqz["九幽除名"]={py={-2,-48},cp = false}
Fightztqz["真君显灵"]={py={-2,-30},cp = true}
Fightztqz["不动如山"]={py={-2,-30},cp = true}
Fightztqz["天地同寿"]={py={-2,-30},cp = true}
Fightztqz["腾雷"]={py={-2,-30},cp = true}
Fightztqz["护盾"]={py={-5,15},cp = true}


Fighttxz={}
Fighttxz["唧唧歪歪"]=1.15
Fighttxz["横扫千军"]=1.05
Fighttxz["浪涌"]=1.05
Fighttxz["反震"]=1.6
Fighttxz["防御"]=1.6
Fighttxz["捕捉开始"]=1.55
Fighttxz["暴击"]=1.55
Fighttxz["龙卷雨击1"]=1.15
Fighttxz["地裂火"]=1.15
Fighttxz["龙卷雨击2"]=1.5
Fighttxz["龙卷雨击3"]=1.0
Fighttxz["龙卷雨击4"]=1.15
Fighttxz["龙吟"]=1.45
Fighttxz["龙腾"]=1.75
Fighttxz["泰山压顶"]=1.4
Fighttxz["连环击"]=2.4
Fighttxz["天雷斩"]=1.6
Fighttxz["地狱烈火"]=1.6
Fighttxz["水漫金山"]=1.45
Fighttxz["鹰击"]=1.45
Fighttxz["上古灵符"]=1.45
Fighttxz["五雷轰顶"]=1
Fighttxz["天罗地网"]=0.85
Fighttxz["狮搏"]=1.4
Fighttxz["被击中"]=1.28
Fighttxz["二龙戏珠"]=1.28
Fighttxz["法术暴击"]=1.2
Fighttxz["月光"]=2.4
Fighttxz["翻江搅海"]=3
Fighttxz["尘土刃"]=1.5
Fighttxz["泰山压顶1"]=1.25
Fighttxz["泰山压顶2"]=1.4
Fighttxz["泰山压顶3"]=1.1
Fighttxz["归元咒"]=0.95
Fighttxz["乾天罡气"]=0.95
Fighttxz["巨岩破"]=0.95
Fighttxz["推拿"]=0.95
Fighttxz["活血"]=0.95
Fighttxz["推气过宫"]=0.95
Fighttxz["惊心一剑"]=1.05
Fighttxz["牛刀小试"]=1.05
Fighttxz["力劈华山"]=1.05
Fighttxz["食指大动"]=2

function 取法宝类型(sj)
	local lssj = {"恢复","辅助","防御","攻击","诅咒"}
	return lssj[sj+0]
end

Jingtaicw={}
Jingtaicw["剑会天下·新秀"]={x=-53,y=8}
Jingtaicw["剑会天下·百胜"]={x=-53,y=8}
Jingtaicw["剑会天下·千胜"]={x=-53,y=8}
Jingtaicw["剑会天下·万胜"]={x=-53,y=8}
Jingtaicw["剑会天下·神话"]={x=-53,y=8}

QUshoujuesx={}
QUshoujuesx["高级法术暴击"]="高级法暴"
QUshoujuesx["高级精神集中"]="高级精神"
QUshoujuesx["高级否定信仰"]="高级信仰"
QUshoujuesx["高级魔之心"]="高级魔心"
QUshoujuesx["火属性吸收"]="火吸"
QUshoujuesx["水属性吸收"]="水吸"
QUshoujuesx["土属性吸收"]="土吸"
QUshoujuesx["雷属性吸收"]="雷吸"
QUshoujuesx["高级火属性吸收"]="高级火吸"
QUshoujuesx["高级水属性吸收"]="高级水吸"
QUshoujuesx["高级土属性吸收"]="高级土吸"
QUshoujuesx["高级雷属性吸收"]="高级雷吸"
QUshoujuesx["高级法术抵抗"]="高级法抗"
QUshoujuesx["高级神佑复生"]="高级神佑"
QUshoujuesx["高级鬼魂术"]="高级鬼魂"
QUshoujuesx["高级法术波动"]="高级法波"
QUshoujuesx["高级法术连击"]="高级法连"
QUshoujuesx["高级进击必杀"]="高级进必"
QUshoujuesx["高级进击法暴"]="高级进暴"

__jingmaimiaoshu={}
__jingmaimiaoshu["大唐官府"]="dt"
__jingmaimiaoshu["方寸山"]="fcs"
__jingmaimiaoshu["花果山"]="hgs"
__jingmaimiaoshu["化生寺"]="hss"
__jingmaimiaoshu["凌波城"]="lbc"
__jingmaimiaoshu["龙宫"]="lg"
__jingmaimiaoshu["魔王寨"]="mwz"
__jingmaimiaoshu["女儿村"]="nrc"
__jingmaimiaoshu["盘丝洞"]="psd"
__jingmaimiaoshu["普陀山"]="pts"
__jingmaimiaoshu["神木林"]="sml"
__jingmaimiaoshu["狮驼岭"]="stl"
__jingmaimiaoshu["天宫"]="tg"
__jingmaimiaoshu["无底洞"]="wdd"
__jingmaimiaoshu["五庄观"]="wzg"
__jingmaimiaoshu["阴曹地府"]="ycdf"




__jingmaimiaoshu["昂扬"]="angyang"
__jingmaimiaoshu["暴突"]="baotu"
__jingmaimiaoshu["催迫"]="cuipo"
__jingmaimiaoshu["摧枯拉朽"]="cuigulaxiu"
__jingmaimiaoshu["笃志"]="duzhu"
__jingmaimiaoshu["奋战"]="fz"
__jingmaimiaoshu["奉还"]="fenghuan"
__jingmaimiaoshu["攻伐"]="gongfa"
__jingmaimiaoshu["孤勇"]="guyong"
__jingmaimiaoshu["惊天动地"]="jingtiandongdi"
__jingmaimiaoshu["亢强"]="kangqiang"
__jingmaimiaoshu["历战"]="lizhan"
__jingmaimiaoshu["厉兵"]="libing"
__jingmaimiaoshu["烈光"]="lieguang"
__jingmaimiaoshu["灵能"]="lingneng" --lingnneg
__jingmaimiaoshu["怒伤"]="nushang"
__jingmaimiaoshu["披挂上阵"]="piguashangzhen"
__jingmaimiaoshu["破刃"]="poren"
__jingmaimiaoshu["破势"]="poshi"
__jingmaimiaoshu["潜心"]="qianxin"
__jingmaimiaoshu["熟练"]="shulian"
__jingmaimiaoshu["肃杀"]="susha"
__jingmaimiaoshu["突刺"]="tuci"
__jingmaimiaoshu["效法"]="xiaofa"
__jingmaimiaoshu["勇进"]="yongjing"
__jingmaimiaoshu["诛伤"]="zhushang"
__jingmaimiaoshu["追戮"]="zhuinu"






__jingmaimiaoshu["宝诀"]="baojue"
__jingmaimiaoshu["必果"]="biguo"
__jingmaimiaoshu["策电"]="cedian"
__jingmaimiaoshu["穿透"]="chuantou"
__jingmaimiaoshu["符威"]="fuwei"
__jingmaimiaoshu["幻变"]="huanbian"
__jingmaimiaoshu["精炼"]="jinglian"
__jingmaimiaoshu["雷法·倒海"]="leifadaohai"
__jingmaimiaoshu["雷法·翻天"]="leihaifantian"
__jingmaimiaoshu["炼魂"]="jinghun"----------------丢失
__jingmaimiaoshu["咒诀"]="zoujue"
__jingmaimiaoshu["天篆"]="tianzhuan"
__jingmaimiaoshu["震怒"]="zhennu"
__jingmaimiaoshu["吞雷"]="tunlei"
__jingmaimiaoshu["神机"]="shengji"
__jingmaimiaoshu["造化"]="zaohua"
__jingmaimiaoshu["五雷·挪移"]="5leinuoyi"
__jingmaimiaoshu["制约"]="zhiyue"
__jingmaimiaoshu["灵咒"]="lingzhou"
__jingmaimiaoshu["专神"]="zhuansheng"
__jingmaimiaoshu["灵威"]="linggan"
__jingmaimiaoshu["驱雷"]="qulei"
__jingmaimiaoshu["余悸"]="yuji"
__jingmaimiaoshu["妙用"]="miaoyong"
__jingmaimiaoshu["怒霆"]="nuting"
__jingmaimiaoshu["顺势"]="shunshi"
__jingmaimiaoshu["灵能"]="lingnneg"


__jingmaimiaoshu["棒打雄风"]="bangdaxiongf"
__jingmaimiaoshu["变通"]="biantong"
__jingmaimiaoshu["朝拜"]="chaobai"
__jingmaimiaoshu["逞胜"]="chensheng"
__jingmaimiaoshu["冲霄"]="chongxiao"
__jingmaimiaoshu["大圣"]="dasheng"
__jingmaimiaoshu["荡魔"]="dangmo"
__jingmaimiaoshu["得意"]="deyi"
__jingmaimiaoshu["斗战"]="douzhan"
__jingmaimiaoshu["斗志"]="douzhi"
__jingmaimiaoshu["锻炼"]="duanlian"
__jingmaimiaoshu["翻天"]="fantian"
__jingmaimiaoshu["伏妖"]="fuyao"
__jingmaimiaoshu["豪胆"]="haodan"
__jingmaimiaoshu["火眼"]="huoyan"
__jingmaimiaoshu["金睛"]="jingjing"
__jingmaimiaoshu["闹海"]="naohai"
__jingmaimiaoshu["闹天"]="naotian"
__jingmaimiaoshu["齐天神通"]="qitianst"
__jingmaimiaoshu["圈养"]="quanyang"
__jingmaimiaoshu["胜意"]="shengyi"
__jingmaimiaoshu["贪天"]="tantian"
__jingmaimiaoshu["添威"]="tianwei"
__jingmaimiaoshu["填海"]="tianhai"
__jingmaimiaoshu["铁骨"]="tiegu"
__jingmaimiaoshu["通天"]="tongtian"
__jingmaimiaoshu["顽心"]="wanxin"
__jingmaimiaoshu["顽性"]="wanxing"
__jingmaimiaoshu["忘形"]="wangxing"
__jingmaimiaoshu["威仪"]="weiyi"
__jingmaimiaoshu["威震"]="weizhen"
__jingmaimiaoshu["显圣"]="xiansheng"
__jingmaimiaoshu["压邪"]="yaxie"
__jingmaimiaoshu["愈勇"]="yuyong"
__jingmaimiaoshu["战神"]="zhansheng"
__jingmaimiaoshu["逐胜"]="zhuoshen"
__jingmaimiaoshu["自在"]="zizai"



__jingmaimiaoshu["磅礴"]="pangpo"
__jingmaimiaoshu["悲悯"]="beimin"
__jingmaimiaoshu["持戒"]="chijie"
__jingmaimiaoshu["达摩"]="damo"
__jingmaimiaoshu["佛法"]="fofa"
__jingmaimiaoshu["佛佑"]="foyou"
__jingmaimiaoshu["佛缘"]="foyuan"
__jingmaimiaoshu["抚琴"]="fuqin"
__jingmaimiaoshu["化瘀"]="huayu"
__jingmaimiaoshu["慧定"]="huiding"
__jingmaimiaoshu["解惑"]="jiehuo"
__jingmaimiaoshu["金刚"]="jinggang"
__jingmaimiaoshu["静气"]="jingqi"
__jingmaimiaoshu["聚念"]="qunian"
__jingmaimiaoshu["妙手"]="miaoshou"
__jingmaimiaoshu["虔诚"]="qiancheng"
__jingmaimiaoshu["舍利"]="sheli"
__jingmaimiaoshu["生花"]="shenghua"
__jingmaimiaoshu["圣手"]="shengshou1"
__jingmaimiaoshu["施他"]="shita"
__jingmaimiaoshu["授业"]="shouye"
__jingmaimiaoshu["诵经"]="songjing"
__jingmaimiaoshu["诵律"]="songlv"
__jingmaimiaoshu["韦陀"]="weituo"
__jingmaimiaoshu["无碍"]="wuai"
__jingmaimiaoshu["无量"]="wuliang"
__jingmaimiaoshu["悟彻"]="wuche"
__jingmaimiaoshu["修习"]="xiuxi"
__jingmaimiaoshu["自在"]="zizai2"
__jingmaimiaoshu["坐禅"]="zuochan"


__jingmaimiaoshu["乘势"]="chengshi"
__jingmaimiaoshu["耳目一新"]="ermuyixin"
__jingmaimiaoshu["贯通"]="guantong"
__jingmaimiaoshu["混元"]="hunyuan"
__jingmaimiaoshu["惊涛"]="jingtao"
__jingmaimiaoshu["妙得"]="miaoai"
__jingmaimiaoshu["凝息"]="ningxi"
__jingmaimiaoshu["巧变"]="qiaobian"
__jingmaimiaoshu["杀罚"]="shafa"
__jingmaimiaoshu["闪雷"]="shanlei"
__jingmaimiaoshu["盛势"]="shengshi"
__jingmaimiaoshu["石摧"]="shicui"
__jingmaimiaoshu["天眼"]="tianyan"
__jingmaimiaoshu["天泽"]="tianze"
__jingmaimiaoshu["威震"]="weizhen"
__jingmaimiaoshu["无双"]="wushuang"
__jingmaimiaoshu["蓄势"]="xushi"
__jingmaimiaoshu["追袭"]="zhuixi"








__jingmaimiaoshu["傲岸"]="aoan"
__jingmaimiaoshu["叱咤"]="chicha"
__jingmaimiaoshu["骇浪"]="hailang"
__jingmaimiaoshu["呼风"]="hufeng"
__jingmaimiaoshu["唤雨"]="huanyu"
__jingmaimiaoshu["惊鸿"]="jinghong"
__jingmaimiaoshu["狂浪"]="kuanglang"
__jingmaimiaoshu["凛然"]="lingran"
__jingmaimiaoshu["龙钩"]="longgou"
__jingmaimiaoshu["龙息"]="longxi"
__jingmaimiaoshu["龙啸"]="longxiao"
__jingmaimiaoshu["沐雨"]="muyu"
__jingmaimiaoshu["盘龙"]="panlong"
__jingmaimiaoshu["睥睨"]="pini"
__jingmaimiaoshu["潜龙在渊"]="qianlongzy"
__jingmaimiaoshu["雨魄"]="yupo"
__jingmaimiaoshu["云变"]="yunbian"
__jingmaimiaoshu["云魂"]="yunhun"
__jingmaimiaoshu["斩浪"]="zhanlang"





__jingmaimiaoshu["不忿"]="bufen"
__jingmaimiaoshu["摧山"]="cuishan"
__jingmaimiaoshu["焚尽"]="fengjin"
__jingmaimiaoshu["风火燎原"]="fenghuoly"
__jingmaimiaoshu["固基"]="guji"
__jingmaimiaoshu["激怒"]="jinu"
__jingmaimiaoshu["极炙"]="jizhi"
__jingmaimiaoshu["焦土"]="jiaotu"
__jingmaimiaoshu["烬藏"]="zhicang"
__jingmaimiaoshu["惊悟"]="jingyu"
__jingmaimiaoshu["狂劲"]="kuangjin"
__jingmaimiaoshu["烈火真言"]="leihuozheny"
__jingmaimiaoshu["烈焰"]="lieyan"
__jingmaimiaoshu["漫卷狂沙"]="manjuanks"
__jingmaimiaoshu["魔焱"]="moyan"
__jingmaimiaoshu["咆哮"]="paoxiao"
__jingmaimiaoshu["融骨"]="ronggu"
__jingmaimiaoshu["升温"]="shenwen"
__jingmaimiaoshu["五蕴神焰"]="5yunsheny"
__jingmaimiaoshu["邪火"]="xiehuo"
__jingmaimiaoshu["旋阳"]="xuanyang"
__jingmaimiaoshu["焰威"]="yanwei"
__jingmaimiaoshu["焰星"]="yanxing"
__jingmaimiaoshu["折服"]="zhefu"
__jingmaimiaoshu["真炎"]="zhenyan"
__jingmaimiaoshu["炙烤"]="zhikao"
__jingmaimiaoshu["咒言"]="zhouyan"

__jingmaimiaoshu["暗刃"]="1"
__jingmaimiaoshu["百花"]="2"
__jingmaimiaoshu["乘胜"]="3"
__jingmaimiaoshu["痴念"]="4"
__jingmaimiaoshu["毒芒"]="5"
__jingmaimiaoshu["毒雾"]="6"
__jingmaimiaoshu["飞花"]="7"
__jingmaimiaoshu["风行"]="8"
__jingmaimiaoshu["花刺"]="9"
__jingmaimiaoshu["花骨"]="10"
__jingmaimiaoshu["花开"]="11"
__jingmaimiaoshu["花落"]="12"
__jingmaimiaoshu["花谢花飞"]="13"
__jingmaimiaoshu["花雨"]="14"
__jingmaimiaoshu["空灵"]="15"
__jingmaimiaoshu["曼珠"]="16"
__jingmaimiaoshu["怒放"]="17"
__jingmaimiaoshu["轻刃"]="18"
__jingmaimiaoshu["清澈"]="19"
__jingmaimiaoshu["涂毒"]="20"
__jingmaimiaoshu["汹涌"]="21"
__jingmaimiaoshu["训宠"]="22"
__jingmaimiaoshu["驯宠"]="23"
__jingmaimiaoshu["叶护"]="24"
__jingmaimiaoshu["抑怒"]="25"
__jingmaimiaoshu["重明"]="26"
__jingmaimiaoshu["追毒"]="27"


__jingmaimiaoshu["丹香"]="1"
__jingmaimiaoshu["附骨"]="2"
__jingmaimiaoshu["幻镜"]="3"
__jingmaimiaoshu["结阵"]="4"
__jingmaimiaoshu["绝媚"]="5"
__jingmaimiaoshu["绝命毒牙"]="6"
__jingmaimiaoshu["狂击"]="7"
__jingmaimiaoshu["连绵"]="8"
__jingmaimiaoshu["玲珑"]="9"
__jingmaimiaoshu["凌弱"]="10"
__jingmaimiaoshu["罗刹"]="11"
__jingmaimiaoshu["罗网"]="12"
__jingmaimiaoshu["媚态"]="13"
__jingmaimiaoshu["魔音"]="14"
__jingmaimiaoshu["魔瘴"]="15"
__jingmaimiaoshu["扑袭"]="16"
__jingmaimiaoshu["千蛛"]="17"
__jingmaimiaoshu["牵魂蛛丝"]="18"
__jingmaimiaoshu["杀戮"]="19"
__jingmaimiaoshu["天网"]="20"
__jingmaimiaoshu["亡缚"]="21"
__jingmaimiaoshu["忘忧"]="22"
__jingmaimiaoshu["意乱"]="23"
__jingmaimiaoshu["引诛"]="24"
__jingmaimiaoshu["粘附"]="25"
__jingmaimiaoshu["障眼"]="26"
__jingmaimiaoshu["制怒"]="27"


__jingmaimiaoshu["安忍"]="1"
__jingmaimiaoshu["慈佑"]="2"
__jingmaimiaoshu["赐咒"]="3"
__jingmaimiaoshu["低眉"]="4"
__jingmaimiaoshu["抖擞"]="5"
__jingmaimiaoshu["度厄"]="6"
__jingmaimiaoshu["顿悟"]="7"
__jingmaimiaoshu["法华"]="8"
__jingmaimiaoshu["感念"]="9"
__jingmaimiaoshu["慧眼"]="10"
__jingmaimiaoshu["静心"]="11"
__jingmaimiaoshu["困兽"]="12"
__jingmaimiaoshu["莲动"]="13"
__jingmaimiaoshu["莲华"]="14"
__jingmaimiaoshu["莲音"]="15"
__jingmaimiaoshu["怒目"]="16"
__jingmaimiaoshu["普照"]="17"
__jingmaimiaoshu["清净"]="18"
__jingmaimiaoshu["万象"]="19"
__jingmaimiaoshu["无怖"]="20"
__jingmaimiaoshu["无尽"]="21"
__jingmaimiaoshu["相生"]="22"
__jingmaimiaoshu["业障"]="23"
__jingmaimiaoshu["因缘"]="24"
__jingmaimiaoshu["馀威"]="25"
__jingmaimiaoshu["缘起"]="26"
__jingmaimiaoshu["湛然"]="27"
__jingmaimiaoshu["执念"]="28"
__jingmaimiaoshu["智念"]="29"
__jingmaimiaoshu["庄严"]="30"


__jingmaimiaoshu["苍埃"]="1"
__jingmaimiaoshu["苍风"]="2"
__jingmaimiaoshu["毒萃"]="3"
__jingmaimiaoshu["奉愿"]="4"
__jingmaimiaoshu["伏毒"]="5"
__jingmaimiaoshu["归原"]="6"
__jingmaimiaoshu["焕新"]="7"
__jingmaimiaoshu["激活"]="8"
__jingmaimiaoshu["寄生"]="9"
__jingmaimiaoshu["绞藤"]="10"
__jingmaimiaoshu["劲草"]="11"
__jingmaimiaoshu["枯木逢春"]="12"
__jingmaimiaoshu["狂叶"]="13"
__jingmaimiaoshu["凉秋"]="14"
__jingmaimiaoshu["灵归"]="15"
__jingmaimiaoshu["灵精"]="16"
__jingmaimiaoshu["灵木"]="17"
__jingmaimiaoshu["灵秀"]="18"
__jingmaimiaoshu["灵佑"]="19"
__jingmaimiaoshu["迷缚"]="20"
__jingmaimiaoshu["碾杀"]="21"
__jingmaimiaoshu["薪火"]="22"
__jingmaimiaoshu["秀木"]="23"
__jingmaimiaoshu["萦风"]="24"
__jingmaimiaoshu["追击"]="25"
__jingmaimiaoshu["滋养"]="26"


__jingmaimiaoshu["不羁"]="1"
__jingmaimiaoshu["逞凶"]="2"
__jingmaimiaoshu["癫狂"]="3"
__jingmaimiaoshu["钢牙"]="4"
__jingmaimiaoshu["功勋"]="5"
__jingmaimiaoshu["健壮"]="6"
__jingmaimiaoshu["矫健"]="7"
__jingmaimiaoshu["九天"]="8"
__jingmaimiaoshu["攫取"]="9"
__jingmaimiaoshu["狂化"]="10"
__jingmaimiaoshu["狂乱"]="11"
__jingmaimiaoshu["狂啸"]="12"
__jingmaimiaoshu["狂血"]="13"
__jingmaimiaoshu["狂躁"]="14"
__jingmaimiaoshu["困兽之斗"]="15"
__jingmaimiaoshu["拟形"]="16"
__jingmaimiaoshu["念主"]="17"
__jingmaimiaoshu["怒火"]="18"
__jingmaimiaoshu["屏息"]="19"
__jingmaimiaoshu["狮噬"]="20"
__jingmaimiaoshu["守势"]="21"
__jingmaimiaoshu["狩猎"]="22"
__jingmaimiaoshu["威压"]="23"
__jingmaimiaoshu["象踏"]="24"
__jingmaimiaoshu["协同"]="25"
__jingmaimiaoshu["协战"]="26"
__jingmaimiaoshu["雄风"]="27"
__jingmaimiaoshu["夜视"]="28"
__jingmaimiaoshu["饮血"]="29"
__jingmaimiaoshu["长啸"]="30"
__jingmaimiaoshu["争宠"]="31"
__jingmaimiaoshu["追逐"]="32"


__jingmaimiaoshu["藏招"]="1"
__jingmaimiaoshu["存雄"]="2"
__jingmaimiaoshu["电掣"]="3"
__jingmaimiaoshu["电光火石"]="4"
__jingmaimiaoshu["共鸣"]="5"
__jingmaimiaoshu["轰鸣"]="6"
__jingmaimiaoshu["激越"]="7"
__jingmaimiaoshu["疾雷"]="8"
__jingmaimiaoshu["坚壁"]="9"
__jingmaimiaoshu["劲健"]="10"
__jingmaimiaoshu["惊霆"]="11"
__jingmaimiaoshu["惊曜"]="12"
__jingmaimiaoshu["慨叹"]="13"
__jingmaimiaoshu["雷霆汹涌"]="14"
__jingmaimiaoshu["缭乱"]="15"
__jingmaimiaoshu["怒电"]="16"
__jingmaimiaoshu["频变"]="17"
__jingmaimiaoshu["气势"]="18"
__jingmaimiaoshu["神采"]="19"
__jingmaimiaoshu["神尊"]="20"
__jingmaimiaoshu["套索"]="21"
__jingmaimiaoshu["霆震"]="22"
__jingmaimiaoshu["威吓"]="23"
__jingmaimiaoshu["威仪九霄"]="24"
__jingmaimiaoshu["仙音"]="25"
__jingmaimiaoshu["啸傲"]="26"
__jingmaimiaoshu["震荡"]="27"

__jingmaimiaoshu["秉幽"]="1"
__jingmaimiaoshu["刺骨"]="2"
__jingmaimiaoshu["独一"]="3"
__jingmaimiaoshu["夺血"]="4"
__jingmaimiaoshu["分魄"]="5"
__jingmaimiaoshu["诡印"]="6"
__jingmaimiaoshu["鬼袭"]="7"
__jingmaimiaoshu["护法"]="8"
__jingmaimiaoshu["华光"]="9"
__jingmaimiaoshu["魂守"]="10"
__jingmaimiaoshu["纠缠"]="11"
__jingmaimiaoshu["聚魂"]="12"
__jingmaimiaoshu["烈煞"]="13"
__jingmaimiaoshu["灵变"]="14"
__jingmaimiaoshu["灵通"]="15"
__jingmaimiaoshu["灵照"]="16"
__jingmaimiaoshu["罗汉"]="17"
__jingmaimiaoshu["弥愤"]="18"
__jingmaimiaoshu["冥煞"]="19"
__jingmaimiaoshu["牵动"]="20"
__jingmaimiaoshu["深刻"]="21"
__jingmaimiaoshu["盛怒"]="22"
__jingmaimiaoshu["踏魂"]="23"
__jingmaimiaoshu["踏魄"]="24"
__jingmaimiaoshu["血潮"]="25"
__jingmaimiaoshu["阴魅"]="26"
__jingmaimiaoshu["萦魄"]="27"
__jingmaimiaoshu["涌泉"]="28"
__jingmaimiaoshu["余咒"]="29"
__jingmaimiaoshu["羽裂"]="30"
__jingmaimiaoshu["烛照"]="31"
__jingmaimiaoshu["椎骨"]="32"











__jingmaimiaoshu["刺果"]="1"
__jingmaimiaoshu["存神"]="2"
__jingmaimiaoshu["存思"]="3"
__jingmaimiaoshu["道果"]="4"
__jingmaimiaoshu["还元"]="5"
__jingmaimiaoshu["剑气"]="6"
__jingmaimiaoshu["剑势"]="7"
__jingmaimiaoshu["聚力"]="8"
__jingmaimiaoshu["炼果"]="9"
__jingmaimiaoshu["落土止息"]="10"
__jingmaimiaoshu["木摧"]="11"
__jingmaimiaoshu["纳气"]="12"
__jingmaimiaoshu["凝神"]="13"
__jingmaimiaoshu["滂沱"]="14"
__jingmaimiaoshu["起雨"]="15"
__jingmaimiaoshu["气盛"]="16"
__jingmaimiaoshu["强击"]="17"
__jingmaimiaoshu["三元"]="18"
__jingmaimiaoshu["守中"]="19"
__jingmaimiaoshu["无极"]="20"
__jingmaimiaoshu["行气"]="21"
__jingmaimiaoshu["蓄志"]="22"
__jingmaimiaoshu["玄机"]="23"
__jingmaimiaoshu["饮露"]="24"
__jingmaimiaoshu["运转"]="25"







__jingmaimiaoshu["毒炽"]="1"
__jingmaimiaoshu["毒慑"]="2"
__jingmaimiaoshu["扼命"]="3"
__jingmaimiaoshu["恶焰"]="4"
__jingmaimiaoshu["拘魄"]="5"
__jingmaimiaoshu["狂宴"]="6"
__jingmaimiaoshu["六道"]="7"
__jingmaimiaoshu["轮回"]="8"
__jingmaimiaoshu["冥视"]="9"
__jingmaimiaoshu["破印"]="10"
__jingmaimiaoshu["入魂"]="11"
__jingmaimiaoshu["生杀予夺"]="12"
__jingmaimiaoshu["蚀骨"]="13"
__jingmaimiaoshu["噬毒"]="14"
__jingmaimiaoshu["通瞑"]="15"
__jingmaimiaoshu["无赦咒令"]="16"
__jingmaimiaoshu["阴翳"]="17"
__jingmaimiaoshu["幽冥"]="18"
__jingmaimiaoshu["狱火"]="19"
__jingmaimiaoshu["瘴幕"]="20"
__jingmaimiaoshu["咒令"]="21"

function 判断是否为空表(t)
    return _G.next( t ) == nil
end
QUshoujuesx={}
QUshoujuesx["高级法术暴击"]="高级法暴"
QUshoujuesx["高级精神集中"]="高级精神"
QUshoujuesx["高级否定信仰"]="高级信仰"
QUshoujuesx["高级魔之心"]="高级魔心"
QUshoujuesx["火属性吸收"]="火吸"
QUshoujuesx["水属性吸收"]="水吸"
QUshoujuesx["土属性吸收"]="土吸"
QUshoujuesx["雷属性吸收"]="雷吸"
QUshoujuesx["高级火属性吸收"]="高级火吸"
QUshoujuesx["高级水属性吸收"]="高级水吸"
QUshoujuesx["高级土属性吸收"]="高级土吸"
QUshoujuesx["高级雷属性吸收"]="高级雷吸"
QUshoujuesx["高级法术抵抗"]="高级法抗"
QUshoujuesx["高级神佑复生"]="高级神佑"
QUshoujuesx["高级鬼魂术"]="高级鬼魂"
QUshoujuesx["高级法术波动"]="高级法波"
QUshoujuesx["高级法术连击"]="高级法连"
QUshoujuesx["高级进击必杀"]="高级进必"
QUshoujuesx["高级进击法暴"]="高级进暴"
function 取兽决缩写(技能)
	if 技能 then
		if QUshoujuesx[技能] then
		    return QUshoujuesx[技能]
		end
        return 技能
	end
end