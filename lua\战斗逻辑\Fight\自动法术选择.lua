__UI弹出["自动法术选择"]=__UI界面["创建弹出窗口"](__UI界面, "自动法术选择", 547-289+105-37+0+289-105 + abbr.py.x, 110 + abbr.py.y, 275, 366)
local 自动法术选择 = __UI弹出["自动法术选择"]
function 自动法术选择:初始化()
  local nsf = require("SDL.图像")(354+289-105, 406)
  if nsf["渲染开始"](nsf, 0, 0, 0, 0) then
    -- __res:getPNGCC(2, 0, 63, 354, 396)["显示"](__res:getPNGCC(2, 0, 63, 354, 396), 0+289-105, 10)
    -- local 宽度 = 字体20["取宽度"](字体20, "法术")
    -- 字体20["置颜色"](字体20, 255, 255, 255)
    -- 字体20["取图像"](字体20, "法术")["显示"](字体20["取图像"](字体20, "法术"), 177.0 - 宽度 / 2+289-105, 16)
    取黑透明背景(0, 0, 265, 360, true):显示(0, 0)
    nsf["渲染结束"](nsf)
  end

  self:置精灵(nsf["到精灵"](nsf))
end

function 自动法术选择:打开(技能组, lx, silllx)
  self:置可见(true)
  self.类型 =lx
  self.选中 = nil
  self.图像2=nil
  self:置技能(技能组)
end

local 技能网格 = 自动法术选择["创建网格"](自动法术选择, "技能网格", 5, 20, 239+9, 269-32)
function 技能网格:初始化()
  self:创建格子(75, 75, 3, 12, 9, 3, true)
end
function 技能网格:左键单击(x, y, a, b, msg)
  -- print(111)
  if self.子控件[a]._spr and not self.子控件[a]["技能信息"]["剩余冷却回合"] then
    if 调试模式 then
      print(self.子控件[a]["技能信息"].名称)
    end
    自动法术选择:设置自动(self.子控件[a]["技能信息"].名称,"法术")
    -- -- if not 自动法术选择["选中"] or 自动法术选择["选中"] ~= a then
    --   自动法术选择["选中"] = a
    -- -- else
    --   __UI界面["窗口层"]["自动法术选择"]["置可见"](__UI界面["窗口层"]["自动法术选择"], false)
    -- -- end
  end
end
function 自动法术选择:置技能(data)
  self.技能数据 = data
  -- 自动法术选择.技能网格:创建格子(100, 100, 15, 12, math.ceil(#data+1), 3, true)
  for i = 1, #技能网格["子控件"] do
    if self.技能数据[i] then
      local nsf = require("SDL.图像")(100, 100)
      local lssc = 取技能(self.技能数据[i]["名称"], self.类型)
      local wenj="shape/jn/"
            if lssc[10] then
                wenj="shape/xinzengsucai/"
            end
      if nsf["渲染开始"](nsf) then
        -- nsf["渲染清除"](nsf, 0, 0, 0, 255)
        if self.技能数据[i]["剩余冷却回合"] then
          __res:取图像(__res["取地址"](__res, wenj, lssc[7])):拉伸(40, 40):到灰度():显示(5+14, 5+6)
        else
          __res:取图像(__res["取地址"](__res, wenj, lssc[7])):拉伸(40, 40):显示(5+14, 5+6)
        end
        -- __主控["公用蒙版2"]["显示"](__主控["公用蒙版2"], -4, -3)
        local 宽度 = 字体16["取宽度"](字体16, self.技能数据[i]["名称"])
        字体16["置颜色"](字体16, 255, 255, 255)
        字体16:取图像(self.技能数据[i]["名称"]):置混合(0):显示(50.0 - 宽度 / 2-11, 80-15-6)
        nsf["渲染结束"](nsf)
      end
      local sc = nsf["到精灵"](nsf)
      -- sc["置混合"](sc, 2)
      技能网格["子控件"][i]["置精灵"](技能网格["子控件"][i], sc)
      技能网格["子控件"][i]["技能信息"] = self.技能数据[i]
    else
      技能网格["子控件"][i]["置精灵"](技能网格["子控件"][i])
    end
  end
end

function 自动法术选择:设置自动(操作,lx)
  -- local fssj={目标类型=self.类型,目标=8,类型=lx,敌我=0,参数=操作,id=角色信息.数字id}
  发送数据(5556,{sdfjse={目标类型=self.类型,目标=8,类型=lx,敌我=0,参数=操作,id=角色信息.数字id}})
  self:置可见(false)
end

for i, v in ipairs({
  {
      name = "防御",
      x = 28,
      y = 294-11,
      tcp = __res:getPNGCC(2, 329, 3, 54, 54)
  },
  {
      name = "攻击",
      x = 150+28,
      y = 294-11,
      tcp = __res:getPNGCC(2, 780, 3, 54, 54)
  },
}) do
  local 临时函数 = 自动法术选择["创建我的按钮"](自动法术选择, v.tcp, v.name, v.x, v.y)
  function 临时函数:左键弹起(x, y)
    自动法术选择:设置自动(v.name,v.name)
      -- if v.name == "防御按钮" then
      --     -- 战斗界面["命令类型"] = "防御"
      --     -- 战斗界面["设置指令1"](战斗界面, 0)
      -- elseif v.name == "攻击按钮" then
      --     -- 战斗界面["命令类型"] = "攻击"
      --     -- 战斗界面["操作重置"](战斗界面)
      -- end
  end
end