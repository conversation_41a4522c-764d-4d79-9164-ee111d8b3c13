local 状态 = class("状态")
local SDL = require("SDL")
function 状态:初始化(t)
    self.名称 = t.名称
    self.历劫 = t.历劫
    if t.月卡 and t.月卡.开通 then
        self.月卡=true
    end
    self:置名称(t.名称)
    self:置称谓("")
    if t.当前称谓 then
        self:置称谓(t.当前称谓)
        
    end
    if t.称谓 and type(t.称谓)~="table" and type(t.称谓)=="string" then
        self:置称谓(t.称谓)
    end
    
    self:置队长(t.队长)
    self:置战斗(t.战斗开关)
    self:置摆摊(t.摊位名称)
    self.动画组={}
end




-- function 状态:置名称(v)
--     if v then
--         if self.月卡 then
--             self._name = 名字14:置颜色(255,20,147,255):取描边投影精灵(v,0,0,0,80)
--         else
--             if ggetype(self)=="主角" or ggetype(self)=="玩家" then
--                     self._name = 名字14:置颜色(1, 255, 1,255):取描边投影精灵(v,0,0,0,80)
--                     if v=="游戏管理员" then
--                         self._name = 名字14:置颜色(248, 8, 65,255):取描边投影精灵(v,0,0,0,80)
--                     end
--             else
--                 self._name = 名字14:置颜色(181,182,6,255):取描边投影精灵(v,0,0,0,80)
--             end
--         end
--         self._name:置中心(self._name.宽度 // 2,-self._name.高度+3)
--     end
-- end


function 状态:置名称(v)
  if v then
      if self.月卡 then
          self._name = 标题字体:置颜色(255,20,147,255):取投影精灵(v,0,0,0,120)
      else
          if ggetype(self)=="主角" or ggetype(self)=="玩家" then
                  self._name = 标题字体:置颜色(138, 231, 142, 255):取投影精灵(v,0,0,0,120)
                  if v=="游戏管理员" then
                      self._name = 标题字体:置颜色(248, 8, 65,255):取投影精灵(v,0,0,0,120)
                  end
          else
              self._name = 标题字体:置颜色(201, 191, 92, 255):取投影精灵(v,0,0,0,120)

              
          end
      end
      self._name:置中心(self._name.宽度 // 2,-self._name.高度+3)
  end
end


-- function 状态:置称谓(v)
--     self._title=nil
--     self.当前称谓=""
--     if v and "无" ~= v and "" ~= v then
--             self.当前称谓=v
--             -- if v=="武神坛冠军" then
--             --     self._title =__res:取资源动画('dlzy',0xAABBCC1A,"动画")
--             -- elseif v=="武神坛亚军" then
--             --     self._title =__res:取资源动画('dlzy',0xAABBCC1B,"动画")

--             -- elseif v=="武神坛季军" then
--             --     self._title =__res:取资源动画('dlzy',0xAABBCC1C,"动画")
--             -- elseif v=="英雄大会冠军" then
--             --     self._title =__res:取资源动画('jszy/ddck',0x5DA1F100,"动画")
--             -- elseif v=="英雄大会亚军" then
--             --     self._title =__res:取资源动画('jszy/ddck',0x5DA1F101,"动画")
--             -- else
--                 self._title = 名字14:置颜色(116,160,219,255):取描边投影精灵(v,0,0,0,80)
--                 self._title:置中心(self._title.宽度 // 2, -self._title.高度+3)
--                 self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
--             --end
         
--     else
--         self._name:置中心(self._name.宽度 // 2,-self._name.高度+3)
--     end
-- end

function 状态:置称谓(v)
  self._title=nil
  self.当前称谓=""
  if v and "无" ~= v and "" ~= v then
          self.当前称谓=v
          if v=="武神坛冠军" then
              self._title =__res:取资源动画('dlzy',0xAABBCC1A,"动画")
              self._title:置中心(0, -15)
              self._name:置中心(self._name.宽度 // 2, -self._name.高度-30)
          elseif v=="武神坛亚军" then
              self._title =__res:取资源动画('dlzy',0xAABBCC1C,"动画")
              self._title:置中心(0, -15)
              self._name:置中心(self._name.宽度 // 2, -self._name.高度-30)
          elseif v=="武神坛季军" then
              self._title =__res:取资源动画('dlzy',0xAABBCC1B,"动画")
              self._title:置中心(0, -15)
              self._name:置中心(self._name.宽度 // 2, -self._name.高度-30)
          elseif v=="英雄大会冠军" then
              self._title =__res:取资源动画('jszy/ddck',0x5DA1F100,"动画")
              self._title:置中心(0, -15)
              self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
          elseif v=="英雄大会亚军" then
              self._title =__res:取资源动画('jszy/ddck',0x5DA1F101,"动画")
              self._title:置中心(-5, -15)
              self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
          elseif v=="千亿称号[血]" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69006801,"动画")
                self._title:置中心(45, -15)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
          elseif v=="千亿称号[法]" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69004301,"动画")
                self._title:置中心(45, -15)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)

          elseif v=="千亿称号[速]" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69003D01,"动画")
                self._title:置中心(45, -15)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
          elseif v=="千亿称号[防]" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69004901,"动画")
                self._title:置中心(45, -15)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
          elseif v=="千亿称号[伤]" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007801,"动画")
                self._title:置中心(45, -15)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)


          elseif v=="绝世英豪" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007816,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._name.高度-15)
          elseif v=="横扫天下" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007817,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._name.高度-15)
          elseif v=="独孤求败" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007818,"动画")  
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._name.高度-15)
          elseif v=="合格菜商" then
                self._title =__res:取资源动画('jszy/lianghao',0x00000087,"动画")  
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._name.高度-15)                
          elseif v=="镇妖塔10层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007802,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔20层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007803,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔30层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007804,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔40层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007805,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔50层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007806,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔60层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007807,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔70层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007808,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔80层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007809,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔90层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007810,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔100层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007811,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)

              elseif v=="镇妖塔110层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007821,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔120层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007822,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔130层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007823,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔140层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007824,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔150层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007825,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔160层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007826,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔170层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007827,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔180层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007828,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔190层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007829,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)
            elseif v=="镇妖塔200层" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007830,"动画")
                self._title:置中心(60, 0)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度+15)



            elseif string.find(v,"镇妖塔") and ggetype(self)~="NPC" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007812,"动画")
                self._title:置中心(45, -15)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
            elseif v=="初露锋芒" then
                self._title =__res:取资源动画('jszy/xcwsc',0x69007813,"动画")
                self._title:置中心(35, -15)
                self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
          else
             
              if v=="虹光" then
                self._title = 标题字体:置颜色(255, 1, 1, 255):取投影精灵(v,0,0,0,120)
              elseif v=="云影" then
                  self._title = 标题字体:置颜色(0,0,240, 255):取投影精灵(v,0,0,0,120)

              elseif v=="彩虹霸主" or  v=="帮战之星"  then
                  self._title = 标题字体:置颜色(255, 1, 255, 255):取投影精灵(v,0,0,0,120)
              else
                self._title = 标题字体:置颜色(110, 161, 211, 255):取投影精灵(v,0,0,0,120)
              end
              self._title:置中心(self._title.宽度 // 2, -self._title.高度+3)
              self._name:置中心(self._name.宽度 // 2, -self._title.高度-self._name.高度)
          end 
  else
      self._name:置中心(self._name.宽度 // 2,-self._name.高度+3)
  end
end


function 状态:置队长(v)
    self.是否队长 = v
    self.队长令牌 = __res:取资源动画('dlzy',0x2231EBB4,"动画")
    self.队长令牌:置中心(0, 100)
end

function 状态:置战斗(v)
    self.是否战斗 = v
    self.战斗令牌 = __res:取资源动画('dlzy',0x97C79EDB,"动画")
    self.战斗令牌:置中心(0, 100)
end

function 状态:置任务(v)
    self.是否任务 = v
    self.任务令牌 = __res:取资源动画('dlzy',0x953624BC,"动画")
    self.任务令牌:置中心(0, 100)
end

function 状态:置任战(v)
    self.是否任战 = v
    self.任战令牌 = __res:取资源动画('dlzy',0x3D3AA29E,"动画")
    self.任战令牌:置中心(0, 100)
end


function 状态:置摆摊(v)
    self.是否摆摊 = v
    if not v then
        self.store = nil
    else
          local 字体颜色 = {8, 108, 240}
          if self.玩家ID and __关注摊位[self.玩家ID] then
            字体颜色 = {255, 1, 1}
          end
          local w = 文本字体:取宽度(v)+40
          self.store =取横排图像(__res:取资源动画('gy',1954046210,"图像"),w,10,nil,v,文本字体,字体颜色)
          self.store:置中心(w//2, 120)
    end
end


function 状态:加入动画(名称)
    table.insert(self.动画组,_tp:载入特效(名称))
end



function 状态:添加喊话(内容,特效)

    if not 内容 or 内容 == '' then
        return
    end
    if not self._shout then

        self._shout = require('对象/精灵喊话')(self.人物.静立.高度,特效)
    end
    self._shout:添加(内容)
end

function 状态:更新(dt)

    if self._shout then
        self._shout:更新(dt)
    end
   if self.是否队长 then
        self.队长令牌:更新(dt)
   end
   if self.是否战斗 then
        self.战斗令牌:更新(dt)
   end
   if self.是否任务 then
      self.任务令牌:更新(dt)
   end
   if self.是否任战 then
      self.任战令牌:更新(dt)
   end
   if self.动画组 and self.动画组[1] then
        self.动画组[1]:更新(dt)
        if self.动画组[1]:取当前帧()>= self.动画组[1]:取帧数() then
            table.remove(self.动画组,1)
        end
   end
   if self.当前称谓 and  self.当前称谓~="无" and  self.当前称谓~="" and (self.当前称谓=="武神坛冠军" or 
        self.当前称谓=="武神坛亚军" or self.当前称谓=="武神坛季军"  or self.当前称谓=="英雄大会冠军" or 
        self.当前称谓=="英雄大会亚军" or self.当前称谓=="千亿称号[血]" or self.当前称谓=="千亿称号[法]" or 
        self.当前称谓=="千亿称号[速]" or self.当前称谓=="千亿称号[防]" or self.当前称谓=="千亿称号[伤]" or
        self.当前称谓=="绝世英豪" or self.当前称谓=="横扫天下" or self.当前称谓=="独孤求败" or
        self.当前称谓=="初露锋芒"     or (string.find(self.当前称谓,"镇妖塔") and ggetype(self)~="NPC")) then
            self._title:更新(dt)
    end




end

function 状态:显示底层(xy)
end

function 状态:显示(xy)
  
      if self._title then
      
          self._title:显示(xy)
   
      end
      if self._name then
          self._name:显示(xy)
      end
      if ggetype(self)=="主角" or ggetype(self)=="NPC" or __res.配置.显示玩家~=1 then
          if self.是否队长 then
              self.队长令牌:显示(xy)
          end
          if self.是否战斗 then
              self.战斗令牌:显示(xy)
          end
          if self.是否任务 then
              self.任务令牌:显示(xy)
          end
          if self.是否任战 then
            self.任战令牌:显示(xy)
          end
          if self.动画组 and self.动画组[1] then
              self.动画组[1]:显示(xy)
          end
      end
end

function 状态:显示顶层(xy)

    if self.store and __res.配置.屏蔽摊位~=1  then
        self.store:显示(xy)
    end
    if self._shout and (__res.配置.显示玩家~=1 or ggetype(self)=="主角") then
        if self.store then
            xy.y = xy.y - 30
        end
        self._shout:显示(xy) --喊话
    end

    
end

function 状态:消息事件(t)
    if self.是否摆摊 and t.鼠标 then
        for _, v in ipairs(t.鼠标) do
            if  self.store:检查点(v.x, v.y)  then
                if v.type == SDL.MOUSE_UP  then
                    if v.button == SDL.BUTTON_LEFT then
                        v.type = nil
                        self.store:置高亮(true)
                        if __手机 then
                            t.摆摊 = self
                        end
                    elseif  v.button == SDL.BUTTON_RIGHT and not __手机  then 
                        t.摆摊 = self
                    end
                elseif not v.button and  v.type == SDL.MOUSE_MOTION and not __手机 then
                        self.store:置高亮(true)

                end
            elseif not __手机 then
                self.store:置高亮(false)
            end
        end
    end
end

return 状态
