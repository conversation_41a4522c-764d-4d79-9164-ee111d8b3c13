local cangku = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "cangku", 97 + abbr.py.x, 21 + abbr.py.y, 777, 488)
function cangku:初始化()
  self:置精灵(置窗口背景("仓库", 0, 12, 723, 476))
  self.格子图=__res:getPNGCC(3, 694, 4, 338, 273)
  self.数量框=__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 80, 35)
  -- __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 12, 132)
  --   __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 372, 132)
  --   __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 80, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 80, 35), 69, 429)
end
function cangku:打开(道具,道具仓库总数,召唤兽仓库总数)
  self:置可见(true)
  self.道具仓库总数 = 道具仓库总数
  self.召唤兽仓库总数 = 召唤兽仓库总数
  cangku["仓库页数"]=1
  self.道具类型 = "道具"
  self.仓库类型 = "道具"
  self.物品控件["重置"](self.物品控件, 1)
  self.物品控件["仓库网格"]["置物品"](self.物品控件["仓库网格"], 道具, 1)
  self.物品控件["道具网格"]["置物品"](self.物品控件["道具网格"], __主控["道具列表"])
  self.物品["置选中"](self.物品, true)
  self.召唤兽控件["置可见"](self.召唤兽控件, false)
  cangku["物品控件"].道具按钮:置选中(true)
end
function cangku:刷新道具仓库总数(道具,总数)
  self.道具仓库总数 = 总数
  cangku["物品控件"]["重置"](cangku["物品控件"], cangku["仓库页数"])
  cangku["物品控件"]["仓库网格"]["置物品"](cangku["物品控件"]["仓库网格"], cangku["数据"], cangku["仓库页数"])
  cangku["物品控件"]["道具网格"]["置物品"](cangku["物品控件"]["道具网格"], __主控["道具列表"])
  cangku["召唤兽控件"]["置可见"](cangku["召唤兽控件"], false)
  cangku["物品控件"].道具按钮:置选中(true)
end

function cangku:刷新道具(类型,数据)
  self.道具类型 = 类型
  cangku["道具选中"] = nil
  cangku["仓库选中"] = nil
  -- table.print(数据)
  cangku["物品控件"]["道具网格"]["置物品"](cangku["物品控件"]["道具网格"], 数据.道具)
end

function cangku:刷新召唤兽仓库总数(总数,仓库数据)
  self.仓库类型="召唤兽"
  self.bb仓库页数=1
  cangku["道具选中"] = nil
  cangku["仓库选中"] = nil
  cangku["右侧bb选中"] = nil
  cangku["左侧bb选中"] = nil
 	self.召唤兽仓库总数 = 总数
  self.召唤兽仓库数据 = 仓库数据
  self.召唤兽控件:重置(仓库数据)
end
function cangku:刷新仓库bb(数据,页数)
  self.bb仓库页数=页数
  cangku["右侧bb选中"] = nil
  cangku["左侧bb选中"] = nil
  cangku.召唤兽控件:重置(数据)
end
for i, v in ipairs({
  {
    name = "物品",
    x = 720,
    y = 71,
    tcp = __res:getPNGCC(1, 686, 0, 54, 98, true),
    tcp2 = __res:getPNGCC(1, 1132, 0, 54, 99, true),
    font = "物\n品"
  },
  {
    name = "召唤兽",
    x = 720,
    y = 168+2,
    tcp = __res:getPNGCC(1, 686, 0, 54, 98, true),
    tcp2 = __res:getPNGCC(1, 1132, 0, 54, 99, true),
    font = "召\n唤\n兽"
  },
  {
    name = "跨服",
    x = 720,
    y = 265+2*2,
    tcp = __res:getPNGCC(1, 686, 0, 54, 98, true),
    tcp2 = __res:getPNGCC(1, 1132, 0, 54, 99, true),
    font = "跨\n服"
  },
  {
    name = "设置",
    x = 720,
    y = 362+2*3,
    tcp = __res:getPNGCC(1, 686, 0, 54, 98, true),
    tcp2 = __res:getPNGCC(1, 1132, 0, 54, 99, true),
    font = "设\n置"
  }
}) do
  local 临时函数 = cangku["创建我的单选按钮"](cangku, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
  function  临时函数:左键弹起(x, y)
    if v.name == "物品" then
      cangku["物品控件"]["置可见"](cangku["物品控件"], true)
      cangku["召唤兽控件"]["置可见"](cangku["召唤兽控件"], false)
      发送数据(6705,{序列=1})
      
    elseif v.name == "召唤兽" then
      发送数据(6801)
      cangku["物品控件"]["置可见"](cangku["物品控件"], false)
      cangku["召唤兽控件"]["置可见"](cangku["召唤兽控件"], true)
    end
  end
end



local 物品控件 = cangku["创建控件"](cangku, "物品控件", 0, 0, 777, 488)

local 道具按钮 = 物品控件["创建我的单选按钮"](物品控件, __res:getPNGCC(3, 511, 11, 117, 43, true), __res:getPNGCC(3, 390, 12, 118, 43, true), "道具按钮", 371, 64, "道具")
function 道具按钮:左键按下(消息, x, y)
end
local 行囊按钮 = 物品控件["创建我的单选按钮"](物品控件, __res:getPNGCC(3, 511, 11, 117, 43, true), __res:getPNGCC(3, 390, 12, 118, 43, true), "行囊按钮", 371+140, 64, "行囊")
function 行囊按钮:左键按下(消息, x, y)
end
local 整理按钮 = 物品控件["创建我的按钮"](物品控件, __res:getPNGCC(3, 511, 11, 117, 43, true), "整理按钮", 225, 426, "整理")
function 整理按钮:左键按下(消息, x, y)
  发送数据(3816)
end

function 物品控件:重置(total)
  cangku["仓库选中"] = nil
  self:置可见(true)
  local nsf = require("SDL.图像")(777, 488)
  if nsf["渲染开始"](nsf) then
    cangku.格子图:显示(12,132)
    cangku.格子图:显示(372, 132)
    cangku.数量框:显示(69, 429)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, cangku["仓库页数"] .. "/" .. cangku["道具仓库总数"]):显示(93,433+4)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "仓库" .. total)["置混合"](字体18["取图像"](字体18, "仓库" .. total), 0)["显示"](字体18["取图像"](字体18, "仓库" .. total)["置混合"](字体18["取图像"](字体18, "仓库" .. total), 0), 15, 75)
    字体18["取图像"](字体18, "双击道具可快速转移")["置混合"](字体18["取图像"](字体18, "双击道具可快速转移"), 0)["显示"](字体18["取图像"](字体18, "双击道具可快速转移")["置混合"](字体18["取图像"](字体18, "双击道具可快速转移"), 0), 505, 433+4)
    nsf["渲染结束"](nsf)
  end
  cangku["图像"] = nsf["到精灵"](nsf)
end

local 仓库网格 = 物品控件["创建网格"](物品控件, "仓库网格", 12, 132, 339, 272)
function 仓库网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 仓库网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    if cangku["仓库选中"] then
      if cangku["仓库选中"] == a then
        发送数据(6703,{页数=cangku.仓库页数,物品=cangku["仓库选中"],类型=cangku.道具类型}) --取
        cangku["仓库选中"] = nil
      else
        self.子控件[cangku["仓库选中"]]._spr["确定"] = nil
        self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 470, 86, w, h, "取出", a)
        cangku["仓库选中"] = a
        self.子控件[a]._spr["确定"] = true
      end
    else
      self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 470, 86, w, h, "取出", a)
      cangku["仓库选中"] = a
      self.子控件[a]._spr["确定"] = true
    end
  end
end
function 仓库网格:置物品(数据, 页数)
  cangku["数据"] = 数据
  cangku["仓库页数"] = 页数
  cangku.物品控件:重置(页数)
  cangku["仓库选中"] = nil
  for i = 1, #self.子控件 do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "道具仓库")
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
local 道具网格 = 物品控件["创建网格"](物品控件, "道具网格", 373, 132, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    if cangku["道具选中"] then
      if cangku["道具选中"] == a then
        发送数据(6702,{页数=cangku.仓库页数,物品=cangku["道具选中"],当前页面 = 1,类型=cangku.道具类型}) --存放
        cangku["道具选中"] = nil
      else
        self.子控件[cangku["道具选中"]]._spr["确定"] = nil
        self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 120, 86, w, h, "存入", a)
        cangku["道具选中"] = a
        self.子控件[a]._spr["确定"] = true
      end
    else
      self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 120, 86, w, h, "存入", a)
      cangku["道具选中"] = a
      self.子控件[a]._spr["确定"] = true
    end
  end
end
function 道具网格:置物品(数据)
  cangku["道具选中"] = nil
  for i = 1, #self.子控件 do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "道具仓库")
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
for i, v in ipairs({
  {
    name = "增加",
    x = 82,
    y = 63+9,
    tcp = __res:getPNGCC(1, 641, 320, 29, 29),
  },
  {
    name = "左翻",
    x = 16,
    y = 424,
    tcp = __res:getPNGCC(4, 969, 4, 46, 46)
  },
  {
    name = "右翻",
    x = 155,
    y = 424,
    tcp = __res:getPNGCC(4, 1016, 4, 46, 46)
  }
}) do
  local 临时函数 = 物品控件["创建我的按钮"](物品控件, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "左翻" and cangku["仓库页数"] > 1 then
      发送数据(6705,{序列=cangku.仓库页数- 1})
    elseif v.name == "右翻" and cangku["仓库页数"] < cangku["道具仓库总数"] then
      发送数据(6705,{序列=cangku.仓库页数+ 1})
    elseif v.name == "增加" then
      __UI界面["窗口层"]["对话栏"]:打开("","","本次租借需要消耗#R"..((cangku.道具仓库总数-3)*100000+300000).."#W两银子，你是否需要进行购买仓库操作？",{"确定购买仓库","让我再想想"})
    end
  end
end





















local 召唤兽控件 = cangku["创建控件"](cangku, "召唤兽控件", 0, 0, 777, 488)
function 召唤兽控件:重置(data2)
  self:置可见(true)
  cangku["图像"] = nil
  cangku["图像2"] = nil
  local nsf = require("SDL.图像")(777, 488)["置像素"](require("SDL.图像")(777, 488), 20, 20, 0, 0, 0, 255)
  if nsf["渲染开始"](nsf) then
    local lssj = 取白色背景(0, 0, 306, 396, true)
    lssj["显示"](lssj, 17, 62)
    lssj["显示"](lssj, 400, 62)
    
    local 左1=41
    __res:getPNGCC(4, 719, 281, 左1, 35):显示(17, 62)
    local 中间=306-左1-左1
    __res:getPNGCC(4, 719+左1, 281, 中间, 35):显示(17+左1, 62)
    __res:getPNGCC(4, 719+(351-左1), 281, 左1, 35):显示(17+左1+中间, 62)

    __res:getPNGCC(4, 719, 281, 左1, 35):显示(400, 62)
    __res:getPNGCC(4, 719+左1, 281, 中间, 35):显示(400+左1, 62)
    __res:getPNGCC(4, 719+(351-左1), 281, 左1, 35):显示(400+左1+中间, 62)
    local shu=__res:getPNGCC(4,1085, 281, 3, 35)
    shu:显示(17+149, 62)
    shu:显示(400+149, 62)
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18["取图像"](字体18, "名字")["显示"](字体18["取图像"](字体18, "名字"), 58, 70)
    字体18["取图像"](字体18, "等级")["显示"](字体18["取图像"](字体18, "等级"), 225, 70)
    字体18["取图像"](字体18, "名字")["显示"](字体18["取图像"](字体18, "名字"), 441, 70)
    字体18["取图像"](字体18, "等级")["显示"](字体18["取图像"](字体18, "等级"), 608, 70)
    nsf["渲染结束"](nsf)
  end
  cangku["图像"] = nsf["到精灵"](nsf)
  cangku["召唤兽仓库数据"] = data2
  self.右侧bb列表["重置"](self.右侧bb列表, cangku["召唤兽数据"])
  self.左侧bb列表["重置"](self.左侧bb列表, cangku["召唤兽仓库数据"])
end

local 右侧bb列表 = 召唤兽控件["创建列表"](召唤兽控件, "右侧bb列表", 404, 100, 298, 350)
function 右侧bb列表:初始化()
  self:置文字(字体20)
  self.行高度 = 50
  self.行间距 = 0
end
function 右侧bb列表:重置()
  cangku["右侧bb选中"] = nil
  self.清空(self)
  for _, v in ipairs(角色信息.宝宝列表) do
    local nsf = require("SDL.图像")(298, 50)
    if nsf["渲染开始"](nsf) then
      if 1 == _ % 2 then
      end
     
      字体16["置颜色"](字体16, __取颜色("浅黑"))
      字体16["取图像"](字体16, v["名称"])["显示"](字体16["取图像"](字体16, v["名称"]), 11, 15)
      字体16["取图像"](字体16, v["等级"])["显示"](字体16["取图像"](字体16, v["等级"]), 205, 15)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 右侧bb列表:左键弹起(x, y, i, item, msg)
  cangku["右侧bb选中"] = i
end
local 左侧bb列表 = 召唤兽控件["创建列表"](召唤兽控件, "左侧bb列表", 17, 100, 298, 350)
function 左侧bb列表:初始化()
  self:置文字(字体20)
  self.行高度 = 50
  self.行间距 = 0
end
function 左侧bb列表:重置(data)
  cangku["左侧bb选中"] = nil
  self.清空(self)
  for _, v in ipairs(data) do
    local nsf = require("SDL.图像")(298, 50)
    if nsf["渲染开始"](nsf) then
      if 1 == _ % 2 then
      end
      字体16["置颜色"](字体16, __取颜色("浅黑"))
      字体16["取图像"](字体16, v["名称"])["显示"](字体16["取图像"](字体16, v["名称"]), 11, 15)
      字体16["取图像"](字体16, v["等级"])["显示"](字体16["取图像"](字体16, v["等级"]), 205, 15)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 左侧bb列表:左键弹起(x, y, i, item, msg)
  cangku["左侧bb选中"] = i
end

for i, v in ipairs({
  {
    name = "存入",
    x = 335,
    y = 160,
    tcp = __res:getPNGCC(4, 969, 4, 46, 46)
  },
  {
    name = "取出",
    x = 335,
    y = 335,
    tcp =  __res:getPNGCC(4, 1016, 4, 46, 46)
  }
}) do
  local 临时函数 = 召唤兽控件["创建我的按钮"](召唤兽控件, v.tcp, v.name, v.x, v.y)
 function  临时函数:左键弹起(x, y)
    if v.name == "存入" and cangku["右侧bb选中"] and 角色信息.宝宝列表[cangku["右侧bb选中"]] then
      if 角色信息.宝宝列表[cangku["右侧bb选中"]].参战信息~=nil then
        __UI弹出.提示框:打开("Y/参战的召唤兽无法存入仓库。")
      else
        发送数据(6802,{类型="存",认证码=角色信息.宝宝列表[cangku["右侧bb选中"]]["认证码"],页数= cangku.bb仓库页数})
      end
    elseif v.name == "取出" and cangku["左侧bb选中"] and cangku["召唤兽仓库数据"][cangku["左侧bb选中"]] then
      发送数据(6802,{类型="取",认证码=cangku["召唤兽仓库数据"][cangku["左侧bb选中"]].认证码,页数=cangku.bb仓库页数})
    end
  end
end
local 关闭 = cangku["创建我的按钮"](cangku, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 690, 0)
function 关闭:左键弹起(x, y, msg)
  cangku["置可见"](cangku, false)
end