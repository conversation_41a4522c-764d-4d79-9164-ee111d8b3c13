--[[
LastEditTime: 2025-06-14 00:24:18
--]]
--[[
LastEditTime: 2024-10-30 13:56:59
--]]

local 流动公告 = 界面层:创建控件("流动公告")
function 流动公告:初始化()
    self:置坐标(0, 35)
    self:置宽高(引擎.宽度,20)
    self.可初始化=true
    self.文本数据={}
    self.创建计数=1
end
function 流动公告:打开(内容)
   
          self:置可见(true)
          if not self.创建计数 then self.创建计数=1 end
          if not self.文本数据 then self.文本数据={} end
          local w = 文本字体:取宽度(内容)
          local txts = {
                tiem = os.clock(),
                x=引擎.宽度+50,
                w=w,
                name=self.创建计数,
                ffs =  self:丰富文本("公告文本"..self.创建计数,引擎.宽度+50,0,w,20,true)
            }
          self.创建计数=self.创建计数+1
          txts.ffs:置文字(文本字体)
          txts.ffs:置文本("#Y"..内容)
          table.insert(self.文本数据,txts)
          self:置精灵(require('SDL.精灵')(0, 0, 0,引擎.宽度,20):置颜色(0, 0, 0, 150),true) 
end
function 流动公告:更新(dt)
      if self.文本数据 and self.文本数据[1] then
            if not  self.文本数据[1].ffs.是否可见 then
              self.文本数据[1].ffs:置可见(true)
            end
            self.文本数据[1].x=self.文本数据[1].x-2
           -- self.文本数据[1].ffs.x=self.文本数据[1].x
            if self.文本数据[1].x+self.文本数据[1].w <=0 then
                  self:删除控件("公告文本"..self.文本数据[1].name)
                  table.remove(self.文本数据, 1)
                  if #self.文本数据<=0 then
                      self:置可见(false)
                  end
            end
      elseif self.是否可见 then
              self:置可见(false)
      end
end

function 流动公告:显示(x,y)
      if self.文本数据 and self.文本数据[1] then
          self.文本数据[1].ffs:显示(self.文本数据[1].x,y+3)
      end
end
