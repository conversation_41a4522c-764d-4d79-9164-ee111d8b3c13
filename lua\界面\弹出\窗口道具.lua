
__UI弹出["窗口道具"] = __UI界面["创建弹出窗口"](__UI界面, "窗口道具", 190 + abbr.py2.x, 90 + abbr.py2.y, 370, 300)
local 窗口道具 = __UI弹出["窗口道具"]
function 窗口道具:初始化()
  local nsf = require("SDL.图像")(370, 300)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 370, 300, true)["显示"](取黑透明背景(0, 0, 370, 300, true), 0, 0)
    __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 17, 14)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.选中道具 = nil
end
function 窗口道具:打开(sj,内丹,吸附石)
  self:置可见(true)
  self.符纸格子 = nil
  self.符纸总类 = nil
  self.符纸分类 = nil
  self.格子=sj.格子
  self.宝宝 = nil
  self.选中兽决 = nil
  if not 内丹 and not 吸附石 then
    self.符纸格子 = sj[1]
    self.符纸总类 = sj[2]
    self.符纸分类 = sj[3]
    self.符纸子类 = sj[4]
    self.功能分类 = sj[5]
    self.伙伴id=sj[6]
  elseif 内丹 then
    self.宝宝 = 内丹
    self.功能分类 = "打内丹"
  elseif 吸附石 then
    self.选中兽决 = 0
    self.功能分类 = "吸附石"
  end
  if self.功能分类 == "打内丹" then
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 203)
  elseif self.功能分类 == "吸附石" then
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 3)
  elseif self.功能分类 == "光武拓印" then
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 2)
  elseif self.功能分类 == "获取法宝" then
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 1000)
  -- elseif self.功能分类 == "特性宝珠" then
  -- 	self.物品[is]:显示(dt,x,y,self.鼠标,{2,2})
  else
    self.道具网格["置物品"](self.道具网格, __主控["道具列表"], 2)
  end
  
end

local 道具网格 = 窗口道具["创建网格"](窗口道具, "道具网格", 17, 14, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end

function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and not self.子控件[a]._spr["物品禁止"] and self.子控件[a]._spr["物品"] then
    local wuping=self.子控件[a]._spr["物品"]
    发送数据(128,{编号=a,格子=窗口道具.格子})
    窗口道具["置可见"](窗口道具, false)

  end
end
function 道具网格:置物品(data, zl, fl)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], nil, "道具选择",huise,true) --置物品(数据, 背景, 类型,huise,子类描述)
      lssj["置禁止"](lssj, zl, fl)
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
