local 法宝 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "法宝", 110 + abbr.py.x, 10 + abbr.py.y, 726, 484)
function 法宝:初始化()
  local nsf = require("SDL.图像")(726, 484)
   if nsf["渲染开始"](nsf) then
    置窗口背景("法宝", 0, 12, 719, 472, true)["显示"](置窗口背景("法宝", 0, 12, 719, 472, true), 0, 0)
    -- 字体18["置颜色"](字体18, __取颜色("白色"))
    -- 字体18["取图像"](字体18, "已装备法宝")["显示"](字体18["取图像"](字体18, "已装备法宝"), 28, 53)
    -- 字体18["取图像"](字体18, "已装备灵宝")["显示"](字体18["取图像"](字体18, "已装备灵宝"), 28, 150)
    -- local lssc = 取输入背景(0, 0, 216, 23)
    -- 字体18["取图像"](字体18, "法宝名称")["显示"](字体18["取图像"](字体18, "法宝名称"), 28, 251)
    -- lssc["显示"](lssc, 109, 249)
    -- 字体18["取图像"](字体18, "升级经验")["显示"](字体18["取图像"](字体18, "升级经验"), 28, 313)
    -- lssc["显示"](lssc, 109, 311)
    -- 字体18["取图像"](字体18, "修炼经验")["显示"](字体18["取图像"](字体18, "修炼经验"), 28, 368)
    -- lssc["显示"](lssc, 109, 366)
    -- 字体18["取图像"](字体18, "法宝移动至")["显示"](字体18["取图像"](字体18, "法宝移动至"), 355, 447)
    -- 字体16["置颜色"](字体16, __取颜色("白色"))
    -- 字体16["取图像"](字体16, "双击法宝进行装备")["显示"](字体16["取图像"](字体16, "双击法宝进行装备"), 560, 103)
     
    
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.miaoshu={}
	self.miaoshu["速度"]={"速　　度",177}
	self.miaoshu["气血"]={"气　　血",202}
	self.miaoshu["伤害"]={"伤　　害",227}
	self.miaoshu["防御"]={"防    御",227}
	self.miaoshu["封印命中"]={"封印命中",227}
	self.miaoshu["法术伤害"]={"法术伤害",227}
	self.miaoshu["固定伤害"]={"固定伤害",227}
	self.miaoshu["物理暴击"]={"物理暴击",252}
	self.miaoshu["治疗能力"]={"治疗能力",252}
	self.miaoshu["法术暴击"]={"法术暴击",252}
	self.miaoshu["法术防御"]={"法术防御",252}
	self.miaoshu["抵抗封印"]={"抵抗封印",277}
end
local 神器属性 = {
  大唐官府 = {"伤　　害","物理暴击"},化生寺 = {"防　　御","治疗能力"},方寸山 = {"封印命中","法术伤害"},女儿村 = {"封印命中","固定伤害"},天宫 = {"法术伤害","封印命中"},
  普陀山 = {"固定伤害","治疗能力"},龙宫 = {"法术伤害","法术暴击"},五庄观 = {"伤　　害","封印命中"},魔王寨 = {"法术伤害","法术暴击"},狮驼岭 = {"伤　　害","物理暴击"},
  盘丝洞 = {"封印命中","法术防御"},阴曹地府 = {"伤　　害","法术防御"},神木林 = {"法术伤害","法术暴击"},凌波城 = {"伤　　害","物理暴击"},无底洞 = {"封印命中","治疗能力"},
  花果山 = {"伤害","物理暴击"},九黎城 = {"伤害","物理暴击"}
}

local 神器技能 = {
  藏锋敛锐 = {[1]= "横扫千军消耗的气血\n有50%的几率转化\n为等量护盾。",[2]="横扫千军消耗的气血\n有100%的几率转化\n为等量护盾。"},
  惊锋     = {[1]= "每次攻击提升自身10\n点伤害，最多叠加12\n层，死亡后清零。",[2]="每次攻击提升自\n身20点伤害，最多叠加\n12层，死亡后清零。"},
  披坚执锐 = {[1]= "遭受攻击时，有4%的几\n率免受90%的伤害。",[2]="遭受攻击时，有8%的\n几率免受90%的伤害。"},
  金汤之固 = {[1]= "气血小于30%时，提\n升240点抗封等级。",[2]="气血小于30%时，提\n升480点抗封等级。"},
  风起云墨 = {[1]= "受到你治疗的首目标\n本回合内受到的所\n有伤害降低4%。",[2]="受到你治疗的首目\n标本回合内受到的\n所有伤害降低8%。"},
  挥毫     = {[1]= "受到你的治疗时，目标\n每带有一个增益状态，\n额外恢复25点气血。",[2]="受到你的治疗时，目\n标每带有一个增益状态，\n额外恢复50点气血。"},
  盏中晴雪 = {[1]= "若你的速度高于施\n法者，提升速度差×0.5\n的抗封等级。",[2]="若你的速度高于施法\n者，提升速度差×1的\n抗封等级。"},
  泪光盈盈 = {[1]= "笑里藏刀额外减少\n目标6点愤怒。",[2]="笑里藏刀额外减\n少目标12点愤怒。"},
  凭虚御风 = {[1]= "每点被消耗的风灵\n增加40点法术伤害\n结果，最多叠加三层，\n死亡后清零。",[2]="每点被消耗的风灵增\n加80点法术伤害结\n果，最多叠加三层，死亡\n后清零。"},
  钟灵     = {[1]= "被使用3级药是有\n一定几率获得1层风灵。",[2]="被使用3级药是有\n较大几率获得1层风灵。"},
  亡灵泣语 = {[1]= "你的锢魂术会使得\n目标额外受到8%的物\n法伤害。",[2]="你的锢魂术会使得\n目标额外受到10%\n的物法伤害。"},
  魂魇     = {[1]= "被你的物理伤害攻\n击的单位在当回合内\n的法术伤害结果减少100点。",[2]="被你的物理伤害攻\n击的单位在当回合\n内的法术伤害结果减\n少200点。"},
  业焰明光 = {[1]= "你的单体法术有50%\n的几率造成额外25%\n的伤害。",[2]="你的单体法术有50%\n的几率造成额外50%\n的伤害。"},
  流火     = {[1]= "攻击气血百分比小\n于你的单位时，增加8%\n的伤害。",[2]="攻击气血百分比小\n于你的单位时，\n增加16%的伤害。"},
  蛮血     = {[1]= "增加（1-自身气血/气血\n上限）×8%的狂暴几率。",[2]="增加（1-自身气血/气血\n上限）×16%的狂暴\n几率。"},
  狂战     = {[1]= "每有一个己方召唤兽\n被击飞，增加30点\n伤害力，可叠加4层，\n死亡后消失。",[2]="每有一个己方召唤\n兽被击飞，增加60点\n伤害力，可叠加4层，\n死亡后消失。"},
  镜花水月 = {[1]= "受到治疗时，有8%的\n几率获得一个等额\n度的护盾。",[2]="受到治疗时，有16%\n的几率获得一个等额\n度的护盾。"},
  澄明     = {[1]= "每回合结束时，增加\n3点抵抗封印等级。",[2]="每回合结束时，增加\n6点抵抗封印等级。"},
  情思悠悠 = {[1]= "地涌金莲的目标获得\n治疗量10%的护盾。",[2]="地涌金莲的目标获得\n治疗量20%的护盾。"},
  相思     = {[1]= "偶数回合结束时，增\n加3点速度。",[2]="每个回合结束时，\n增加3点速度。"},
  弦外之音 = {[1]= "回合结束时，每个主\n动法宝效果会增加\n你3点愤怒。",[2]="回合结束时，每个主\n动法宝效果会增加\n你6点愤怒。"},
  裂帛     = {[1]= "伤害性法术首目标伤\n害增加8%。",[2]="伤害性法术首目标\n伤害增加16%。"},
  定风波   = {[1]= "受到的法术暴击伤害\n降低30%。",[2]="受到的法术暴击\n伤害降低60%。"},
  沧浪赋   = {[1]= "攻击气血小于30%的目\n标时，额外提升120点\n的法术伤害。",[2]="攻击气血小于30%\n的目标时，额外提\n升240点的法术伤害。"},
  斗转参横 = {[1]= "带有状态生命之泉时，\n日月乾坤命中率增加3%。",[2]="带有状态生命之泉时，\n日月乾坤命中率增加8%。"},
  静笃     = {[1]= "每次击杀敌方单位，\n增加60点伤害。",[2]= "每次击杀敌方单位，\n增加120点伤害。"},
  玉魄     = {[1]= "消耗愤怒的100%转化\n为下一次释放恢复性技\n能时的治疗能力。",[2]="消耗愤怒的200%转\n化为下一次释放恢复\n性技能时的治疗能力。"},
  璇华     = {[1]= "五行法术克制目标五\n行时，增加10%的伤害。",[2]="五行法术克制目标\n五行时，增加20%的\n伤害。"},
  威服天下 = {[1]= "暴击伤害增加12%。",[2]="暴击伤害增加24%。"},
  酣战     = {[1]= "每点消耗的战意，会\n提升20点物理暴击等级，\n可叠加6次，死亡后清零。",[2]="每点消耗的战意，\n会提升40点物理暴击\n等级，可叠加6次，\n死亡后清零。"},
  万物滋长 = {[1]= "使用特技时将会获得\n（消耗愤怒值×等级×5%）\n的护盾和气血回复。",[2]="使用特技时将会获得\n（消耗愤怒值×等级×10%）\n的护盾和气血回复。"},
  开辟     = {[1]= "每次使用如意神通，\n提升20点自身伤害，最\n多叠加6层，死亡后清零。",[2]="每次使用如意神通，\n提升40点自身伤害\n，最多叠加6层，\n死亡后清零。"},
  鸣空     = {[1]= "每当令目标浮空时，\n你获得12点狂暴等级并且\n造成的物理伤害结果提高2%，\n最多叠加6层，\n阵亡后清零",[2]="每当令目标浮空时，\n你获得24点狂暴等级并且\n造成的物理伤害结果提高2%，\n最多叠加6层，\n阵亡后清零"},
  骇神     = {[1]= "受到物理伤害时，\n若攻击者物理伤害低于你，\n伤害结果降低10%",[2]="受到物理伤害时，\n若攻击者物理伤害低于你，\n伤害结果降低20%"},
}
local 门派神器名称 = {
    大唐官府 = "轩辕剑",化生寺 = "墨魂笔",方寸山 = "黄金甲",女儿村 = "泪痕碗",天宫 = "独弦琴",
    普陀山 = "华光玉",龙宫 = "清泽谱",五庄观 = "星斗盘",魔王寨 = "明火珠",狮驼岭 = "噬魂齿",
    盘丝洞 = "昆仑镜",阴曹地府 = "四神鼎",神木林 = "月光草",凌波城 = "天罡印",无底洞 = "玲珑结",
    花果山 = "鸿蒙石",九黎城 = "魔息角"
}
local txta=require("SDL.图像")
local tupianji={
      轩辕剑= txta("assets/shape/pic/xyj.png"),
      明火珠= txta("assets/shape/pic/mhz.png"),
      墨魂笔= txta("assets/shape/pic/mhb.png"),
      黄金甲= txta("assets/shape/pic/hjj.png"),
      泪痕碗= txta("assets/shape/pic/lhw.png"),
      独弦琴= txta("assets/shape/pic/dxq.png"),
      华光玉= txta("assets/shape/pic/hgy.png"),
      清泽谱= txta("assets/shape/pic/qzp.png"),
      星斗盘= txta("assets/shape/pic/xdp.png"),
      噬魂齿= txta("assets/shape/pic/shc.png"),
      昆仑镜= txta("assets/shape/pic/klj.png"),
      四神鼎= txta("assets/shape/pic/ssd.png"),
      月光草= txta("assets/shape/pic/ygc.png"),
      天罡印= txta("assets/shape/pic/tgy.png"),
      玲珑结= txta("assets/shape/pic/llj.png"),
      鸿蒙石= txta("assets/shape/pic/hms.png"),
      魔息角= txta("assets/shape/pic/000.png"),
          }
function 法宝:打开()
  self:置可见(true)
  self.法宝按钮["置选中"](self.法宝按钮, true)
  self.移动 = false
  self.法宝界面:置可见(true)

  self.神器界面:置可见(false)
end
function 法宝:打开刷新(神器是否有,神器是否佩戴,位置)
  self.门派神器名称=门派神器名称[角色信息.门派]
  self.神器图片=tupianji
  self.选中道具 = nil
  self.选中法宝 = nil
  self.选中灵宝 = nil
  self.是否有神器 = 神器是否有
  self.神器库=__主控["法宝佩戴"]
  self.神器属性={}

  self.是否佩戴神器 = 神器是否佩戴

  self.神器格子 = 位置
  self.移动 = false
  if self.法宝按钮["是否选中"] then
    self.道具网格["置物品"](self.道具网格, __主控["法宝列表"])


  end

 
  if  self.是否有神器 and self.是否佩戴神器 and self.神器按钮["是否选中"] then
    self.新神器库={}
    self.新神器库[1]={名称=self.门派神器名称}
    self.神器界面.装备神器["置物品"](self.神器界面.装备神器,self.新神器库)
    
  else
  self.法宝界面.装备法宝["置物品"](self.法宝界面.装备法宝, __主控["法宝佩戴"])

  end
 

  self:刷新()
end
function 法宝:刷新()
  self.选中道具 = nil
  self.选中法宝 = nil
  self.选中灵宝 = nil
  self.移动 = false
  if self.法宝按钮["是否选中"] then
    self.道具网格["置物品"](self.道具网格, __主控["法宝列表"])

  
  end

  if  self.是否有神器 and self.是否佩戴神器  and self.神器按钮["是否选中"] then
    self.新神器库={}
    self.新神器库[1]={名称=self.门派神器名称}
    self.神器界面.装备神器["置物品"](self.神器界面.装备神器,self.新神器库)
    
  else
  self.法宝界面.装备法宝["置物品"](self.法宝界面.装备法宝, __主控["法宝佩戴"])

  end
  

end
function 法宝:更新法宝经验(数据)
  if 数据.id==法宝["选中道具"] and 法宝["选中道具"] and __主控["法宝列表"][法宝["选中道具"]] then
    __主控["法宝列表"][法宝["选中道具"]].当前经验=数据.当前经验
    __主控["法宝列表"][法宝["选中道具"]].升级经验=数据.升级经验
    __主控["法宝列表"][法宝["选中道具"]].气血=数据.境界
    __主控["法宝列表"][法宝["选中道具"]].魔法=数据.灵气
    -- 法宝界面:shuaxintuxiang()
  end
end
local 法宝界面 = 法宝["创建控件"](法宝, "法宝界面", 0, 0, 700, 488)

function 法宝界面:shuaxintuxiang()

     local nsf = require("SDL.图像")(596+200, 300+137+50)
   
    if nsf["渲染开始"](nsf) then
    
      字体18["置颜色"](字体18, __取颜色("白色"))
      字体18["取图像"](字体18, "已装备法宝")["显示"](字体18["取图像"](字体18, "已装备法宝"), 28, 53)
      if 法宝["法宝按钮"]["是否选中"] then
      local lssc = 取输入背景(0, 0, 216, 23)
      字体18["取图像"](字体18, "法宝名称")["显示"](字体18["取图像"](字体18, "法宝名称"), 28, 251)
      lssc["显示"](lssc, 109, 249)
      字体18["取图像"](字体18, "升级经验")["显示"](字体18["取图像"](字体18, "升级经验"), 28, 313)
      lssc["显示"](lssc, 109, 311)
      字体18["取图像"](字体18, "修炼经验")["显示"](字体18["取图像"](字体18, "修炼经验"), 28, 368)
      lssc["显示"](lssc, 109, 366)
      字体18["取图像"](字体18, "法宝移动至")["显示"](字体18["取图像"](字体18, "法宝移动至"), 355, 447)
      字体16["置颜色"](字体16, __取颜色("白色"))
      字体16["取图像"](字体16, "双击法宝进行装备")["显示"](字体16["取图像"](字体16, "双击物品进行装备"), 560, 103)
      if 法宝["选中道具"] and __主控["法宝列表"][法宝["选中道具"]] and __主控["法宝列表"][法宝["选中道具"]].当前经验~=nil then
      字体18["置颜色"](字体18, 139,33,31)
      字体18:取图像(__主控["法宝列表"][法宝["选中道具"]].名称):显示(28+103, 251)
      字体18:取图像(__主控["法宝列表"][法宝["选中道具"]].升级经验):显示(28+103, 313)
      字体18:取图像(__主控["法宝列表"][法宝["选中道具"]].当前经验):显示(28+103, 368)
      end
    end
      nsf["渲染结束"](nsf)
    
   法宝.图像 = nsf["到精灵"](nsf)

  end
end

function 法宝界面:lingbaotuxiang()
  local nsf = require("SDL.图像")(596+200, 300+137+50)

    if nsf["渲染开始"](nsf) then
      if 法宝["灵宝按钮"]["是否选中"] then
      字体18["置颜色"](字体18, __取颜色("白色"))
      字体18["取图像"](字体18, "已装备灵宝")["显示"](字体18["取图像"](字体18, "已装备灵宝"), 28, 53+90)
 
      local lssc = 取输入背景(0, 0, 216, 23)
      字体18["取图像"](字体18, "灵宝名称")["显示"](字体18["取图像"](字体18, "灵宝名称"), 28, 251)
      lssc["显示"](lssc, 109, 249)
      字体18["取图像"](字体18, "升级经验")["显示"](字体18["取图像"](字体18, "升级经验"), 28, 313)
      lssc["显示"](lssc, 109, 311)
      字体18["取图像"](字体18, "修炼经验")["显示"](字体18["取图像"](字体18, "修炼经验"), 28, 368)
      字体18["取图像"](字体18, "灵宝移动至")["显示"](字体18["取图像"](字体18, "灵宝移动至"), 355, 447)
      字体16["置颜色"](字体16, __取颜色("白色"))
      字体16["取图像"](字体16, "双击灵宝进行装备")["显示"](字体16["取图像"](字体16, "双击灵宝进行装备"), 560, 103)
      lssc["显示"](lssc, 109, 366)
     
      if 法宝["选中道具"] and __主控["灵宝列表"][法宝["选中道具"]] and __主控["灵宝列表"][法宝["选中道具"]].当前经验~=nil then
      字体18["置颜色"](字体18, 139,33,31)
      字体18:取图像(__主控["灵宝列表"][法宝["选中道具"]].名称):显示(28+103, 251)
      字体18:取图像(__主控["灵宝列表"][法宝["选中道具"]].升级经验):显示(28+103, 313)
      字体18:取图像(__主控["灵宝列表"][法宝["选中道具"]].当前经验):显示(28+103, 368)
      end
    end
       nsf["渲染结束"](nsf)
    
    法宝.图像 = nsf["到精灵"](nsf)
  end
end
function 法宝:佩戴神器(内容)
  
  self.是否佩戴神器 = 内容.是否
  self.神器格子=内容.格子
  self:按钮重置()
  self.新加物品={}
  self.新神器库={}
 if self.神器按钮["是否选中"] then
   if self.是否有神器 and self.是否佩戴神器 then
    self.新神器库[1]={名称=self.门派神器名称}
    self.神器界面.装备神器["置物品"](self.神器界面.装备神器,self.新神器库)
    self.神器界面.切换按钮:置可见(true)
  else
    self.神器界面.装备神器["置物品"](self.神器界面.装备神器,self.新神器库)
    self.神器界面.切换按钮:置可见(false)
  end
end
end
local 神器界面 = 法宝["创建控件"](法宝, "神器界面", 0, 0, 700, 488)
function 神器界面:shenqituxiang()
    local nsf = require("SDL.图像")(700, 380)
    if nsf["渲染开始"](nsf) then
     
        if 法宝["神器按钮"]["是否选中"]  then
          字体18["置颜色"](字体18, __取颜色("白色"))
      字体18["取图像"](字体18, "已装备灵宝")["显示"](字体18["取图像"](字体18, "已装备神器"), 28, 53)
   
       字体18["置颜色"](字体18, __取颜色("白色"))
       字体18["取图像"](字体18, "加成")["显示"](字体18["取图像"](字体18, "加成"), 28+30, 160)
       字体18["取图像"](字体18, "技能")["显示"](字体18["取图像"](字体18, "技能"), 28+115+50, 160)
        if  法宝.是否有神器 then
          法宝.神器图片[法宝.门派神器名称]:显示(300+88,  160-19)
          字体18["取图像"](字体18, "加成")["显示"](字体18["取图像"](字体18, 法宝.神器属性.神器技能.name), 28+115+30, 160+30)  
          字体18["取图像"](字体18, "加成")["显示"](字体18["取图像"](字体18, 神器技能[法宝.神器属性.神器技能.name][法宝.神器属性.神器技能.lv]), 28+115+30, 160+60)  
          字体18["取图像"](字体18, "加成")["显示"](字体18["取图像"](字体18,  "剩余灵气："..法宝.神器属性.灵气), 505, 350)  
          字体22["置颜色"](字体22, __取颜色("白色"))
          local nb=1
          for k,v in pairs(法宝.属性数值) do
            if v>0 then
              字体18["取图像"](字体18, "加成")["显示"](字体18["取图像"](字体18, 法宝.miaoshu[k][1].." +"..v), 28, 160+30+25*nb)  
            else
              字体18["取图像"](字体18, "加成")["显示"](字体18["取图像"](字体18, 法宝.miaoshu[k][1].." +0"), 28, 160+30+25*nb)  
            end
            nb=nb+1
          end
       end 
      end
      end
      nsf["渲染结束"](nsf)
    

  法宝.图像 = nsf["到精灵"](nsf)
end
function 法宝:按钮重置()
  if  法宝["神器按钮"]["是否选中"] then
   if  not 法宝.是否佩戴神器  then
    神器界面:我的按钮置文字(神器界面["装备神器按钮"], __res:getPNGCC(3, 536, 560, 102, 35, true), "装 备")
   else
    神器界面:我的按钮置文字(神器界面["装备神器按钮"], __res:getPNGCC(3, 536, 560, 102, 35, true), " 卸 下")
   end
  end
 end
 
local 装备神器 = 神器界面["创建网格"](神器界面, "装备神器", 28, 83, 300, 55)
function 装备神器:初始化()
  self:创建格子(55, 55, 0, 27, 1, 1)
end

function 装备神器:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    if 法宝["选中神器"] and self.子控件[法宝["选中神器"]]._spr["物品"] then
      if 法宝["选中灵宝"] == a then
        发送数据(3753, {
          ["序列"] = a
        })
      else
        self.子控件[法宝["选中神器"]]._spr["确定"] = nil
      end
    end
    法宝["选中神器"] = a
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520, 86, w, h, "装备神器", a)
  end
end
function 装备神器:置物品(数据)
  for i = 1, #self.子控件 do
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, 数据[i], "白格子", "战斗道具")
    self.子控件[i]["置精灵"](self.子控件[i], lssj)
  end
end

function 法宝:神器刷新(数据)
  self.神器属性 = 数据
  self.属性数值 = {速度=0,气血=0,抵抗封印=0}
  for n=1,2 do
    if 神器属性[角色信息.门派][n] == "伤　　害" then
      self.属性数值.伤害 = 0
    elseif 神器属性[角色信息.门派][n] == "防　　御" then
      self.属性数值.防御 = 0
    else
        self.属性数值[神器属性[角色信息.门派][n]] = 0
    end
   end
  for k,v in pairs(self.神器属性.神器解锁) do
    for n=1,#v.神器五行属性 do
      if self.属性数值[v.神器五行属性[n]] then
      self.属性数值[v.神器五行属性[n]] = self.属性数值[v.神器五行属性[n]] + v.神器五行数值[n]
      end
    end
  end
  法宝.神器界面:shenqituxiang()
end
local 关闭 = 法宝["创建我的按钮"](法宝, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 676, 0)
function 关闭:左键弹起(x, y, msg)
  法宝["置可见"](法宝, false)
end
local 装备法宝 = 法宝界面["创建网格"](法宝界面, "装备法宝", 28, 83, 300, 55)
function 装备法宝:初始化()
  self:创建格子(55, 55, 0, 27, 1, 4)
end
function 装备法宝:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    if 法宝["选中法宝"] and self.子控件[法宝["选中法宝"]]._spr["物品"] then
      if 法宝["选中法宝"] == a then
        发送数据(3734, {
          ["序列"] = a
        })
      
        if  法宝["选中法宝"]==法宝.神器格子 then
          发送数据(3734,{序列=法宝.神器格子,神器=true})
          法宝.法宝界面.装备法宝["置物品"](法宝.法宝界面.装备法宝, __主控["法宝佩戴"])
         end
      else
        self.子控件[法宝["选中法宝"]]._spr["确定"] = nil
      end
      
    end
   
    法宝["选中法宝"] = a
  
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520, 86, w, h, "装备法宝", a)
  end
end
function 装备法宝:置物品(数据)
  for i = 1, #self.子控件 do
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, 数据[i], "白格子", "战斗道具")
    self.子控件[i]["置精灵"](self.子控件[i], lssj)
  end
end

local 道具网格 = 法宝["创建网格"](法宝, "道具网格", 363, 124, 339, 272-10)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 10, 5, true)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    if 法宝["选中道具"] and self.子控件[法宝["选中道具"]]._spr["物品"] then
      if 法宝["选中道具"] == a then
        if 法宝["法宝按钮"]["是否选中"] then
          local ha
          for i, v in ipairs(法宝.法宝界面["装备法宝"]["子控件"]) do
            if not v._spr["物品"] then
              ha = i
              break
            end
          end
          --print(ha)
          if ha then
            发送数据(3735, {
              ["序列"] = 法宝["选中道具"],
              ["序列1"] = ha
            })
          else
            __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请先卸下法宝")
          end
       

        end
        
      else
        self.子控件[法宝["选中道具"]]._spr["确定"] = nil
        if 法宝["移动"] then
          if 法宝["法宝按钮"]["是否选中"] then
            发送数据(3701, {
              ["抓取id"] = 法宝["选中道具"],
              ["放置id"] = a,
              ["放置类型"] = "法宝",
              ["抓取类型"] = "法宝"
            })
         
          
          end
        end
      end
    end
    法宝["选中道具"] = a
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 70, 86, w, h, "法宝", a)

     if 法宝.法宝按钮["是否选中"] then
      法宝界面:shuaxintuxiang()

     elseif 法宝.神器按钮["是否选中"] then
      法宝.神器界面:shenqituxiang()  
     end
  elseif 法宝["移动"] and 法宝["选中道具"] then
    if 法宝["法宝按钮"]["是否选中"] then
      发送数据(3701, {
        ["抓取id"] = 法宝["选中道具"],
        ["放置id"] = a,
        ["放置类型"] = "法宝",
        ["抓取类型"] = "法宝"
      })
   
    end
  end
end
function 道具网格:置物品(data)
  for i = 1, #self.子控件 do
   
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data[i], "白格子", "战斗道具")
    self.子控件[i]["置精灵"](self.子控件[i], lssj)
  end
end

for i, v in ipairs({
  {
    name = "法宝按钮",
    x = 466-105,
    y = 65,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true),
    font = "法宝"
  },
  {
    name = "神器按钮",
    x = 466,
    y = 65,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true),
    tcp2 = __res:getPNGCC(3, 876, 289, 85, 36, true),
    font = "神器"
  },
 
}) do
  local 临时函数 = 法宝["创建我的单选按钮"](法宝, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "法宝按钮" then
      法宝["法宝按钮"]["置选中"](法宝["法宝按钮"], true)
      法宝["刷新"](法宝)
     
      法宝.法宝界面:置可见(true)
      法宝.道具网格:置可见(true)
      法宝界面.使用按钮:置可见(true)
      法宝界面.修炼按钮:置可见(true)
      法宝界面.道具神器:置可见(true)
      法宝界面.行囊神器:置可见(true)
      神器界面:shenqituxiang()
      神器界面:置可见(false)
      法宝.法宝界面:置可见(true)
    elseif v.name == "神器按钮" then
      法宝["神器按钮"]["置选中"](法宝["神器按钮"], true)
         发送数据(6203)
      法宝["刷新"](法宝)
    
      法宝.神器界面:置可见(true)
      法宝.法宝界面:置可见(false)
      法宝.道具网格:置可见(false)
      --神器界面.装备神器按钮:置可见(true)
      法宝:按钮重置()
    
    end
  end
end
local 使用按钮 = 法宝界面["创建我的按钮"](法宝界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "使用按钮", 22,438, "使用")
function 使用按钮:左键弹起(x, y, msg)
  if 法宝["法宝按钮"]["是否选中"] then
    if 法宝["选中道具"] then
      发送数据(3736, {
        ["序列"] = 法宝["选中道具"]
      })
    end
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请先选中要使用的法宝")
  end
end
local 修炼按钮 = 法宝界面["创建我的按钮"](法宝界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "修炼按钮", 130,438, "修炼")
function 修炼按钮:左键弹起(x, y, msg)
  if 法宝["法宝按钮"]["是否选中"] then
      if 法宝["选中道具"] then
              发送数据(3733, {
                ["序列"] = 法宝["选中道具"]
              })
      end
  
    else
            __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请先选中要修炼的法宝")
     end
end
local 补充按钮 = 法宝界面["创建我的按钮"](法宝界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "补充按钮", 240,438, "补充灵气")
function 补充按钮:左键弹起(x, y, msg)
  if 法宝["法宝按钮"]["是否选中"] then
        if 法宝["选中道具"] then
              发送数据(3733, {
                ["序列"] = 法宝["选中道具"]
              })
            end
         
          else
            __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请先选中要修炼的法宝")
          end
end
local 切换按钮 = 神器界面["创建我的按钮"](神器界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "切换按钮", 22,438, "切换技能")
function 切换按钮:左键弹起(x, y, msg)
  if 法宝["神器按钮"]["是否选中"] then
    if 法宝.是否佩戴神器 then
      发送数据(3821)
    --  __UI界面["窗口层"]["对话栏"]:打开("","","扣除500W银子切换神器技能成功！请重新打开神器界面查看！",{"确定切换神器技能","取消"})                   
   end
  end
end
local 修复按钮 = 神器界面["创建我的按钮"](神器界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "修复按钮", 322+105+30,438, "修复")
function 修复按钮:左键弹起(x, y, msg)
  if 法宝["神器按钮"]["是否选中"] then
    if 法宝.是否佩戴神器 then
      发送数据(6210) 
    end
  end
end
local 补充神器 = 神器界面["创建我的按钮"](神器界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "补充神器", 322+210+60,438, "补充灵气")
function 补充神器:左键弹起(x, y, msg)
  if 法宝["神器按钮"]["是否选中"] then
    if 法宝.是否佩戴神器 then
   --  __UI界面["窗口层"]["对话栏"]:打开("","","切换神器技能需要消耗100#R仙玉，#W你是否确定操作？",{"确定切换神器技能","取消"})                   
   end
  end
end
local 装备神器按钮 = 神器界面["创建我的按钮"](神器界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "装备神器按钮", 220+100,438, "装 备")
function 装备神器按钮:左键弹起(x, y, msg)
  if 法宝["神器按钮"]["是否选中"] then
    if 法宝.是否有神器  then
        if  not 法宝.是否佩戴神器  then
             发送数据(3735, {
              ["序列"] ="神器",
              ["序列1"] = 1
            })
        elseif 法宝.是否佩戴神器 and 法宝.神器格子 then
           print(法宝.神器格子)
            发送数据(3734,{序列=法宝.神器格子,神器=true})
         
        
          
         
        end
      end

     end
 
end
local 道具神器 = 法宝界面["创建我的按钮"](法宝界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "道具神器", 322+110+30,438, "道具")
function 道具神器:左键弹起(x, y, msg)
  if 法宝["法宝按钮"]["是否选中"] then
     if 法宝["选中道具"] then
              发送数据(3746, {
                ["序列"] = 法宝["选中道具"],
                ["放置类型"] = "道具",
                ["抓取类型"] = "法宝"
              })
              法宝["关闭"]["左键弹起"](法宝["关闭"])
            end
          else
            __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请先选中要移动的法宝")
          end
end
local 行囊神器 = 法宝界面["创建我的按钮"](法宝界面, __res:getPNGCC(3, 536, 560, 102, 35, true), "行囊神器", 322+210+60,438, "行囊")
function 行囊神器:左键弹起(x, y, msg)
  if 法宝["法宝按钮"]["是否选中"] then
            if 法宝["选中道具"] then
              发送数据(3746, {
                ["序列"] = 法宝["选中道具"],
                ["放置类型"] = "行囊",
                ["抓取类型"] = "法宝"
              })
              法宝["关闭"]["左键弹起"](法宝["关闭"])
            end
          else
            __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请先选中要移动的法宝")
          end
end


