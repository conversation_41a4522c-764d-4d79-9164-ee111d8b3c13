local 文字 = "    丹青生有一宝画，乃是上古流传下来的珍品，名曰《乾\n坤九曜图》。此画中有雾楼云阁，亭台水榭，青山秀水，蕊\n珠宫阙，只要轻轻将画卷展开，便可身临其境。各路神魔仙\n怪因为厌倦尘世杀戮而隐居在此，再也不问时世。天帝命丹\n青生将此画卷谨慎收藏，不再沾染红尘血腥。只可惜近日魔\n神将要现世，画中诸雄无不感到了他的怨念与仇恨而杀意萌\n动。他们忘却了修身养性的要诀，却没有忘记运用盖世的神\n功手段来杀戮他们看到的一切……于是，丹青生手执画卷，\n在此等待有缘的侠义之士，来化解他们的戾气。\n"
local 生死劫 = 窗口层:创建窗口("生死劫", 0, 0, 511, 520)
function 生死劫:初始化()
    self:创建纹理精灵(function()
    置窗口背景("生死劫", 0, 12, 503, 502, true):显示(0, 0)
    说明字体:置颜色(255,255,255,255)
    说明字体:取图像(文字):显示(21, 60)
  end
)
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true

  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end
function 生死劫:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
end


for i, v in ipairs({
  {
    name = "止戈",
    x = 20,
    y = 359,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "止戈"
  },
  {
    name = "清心",
    x = 195,
    y = 359,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "清心"
  },
  {
    name = "雷霆",
    x = 367,
    y = 359,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "雷霆"
  },
  {
    name = "惜花",
    x = 20,
    y = 410,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "惜花"
  },
  {
    name = "忘情",
    x = 195,
    y = 410,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "忘情"
  },
  {
    name = "卧龙",
    x = 367,
    y = 410,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "卧龙"
  },
  {
    name = "天象",
    x = 20,
    y = 461,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "天象"
  },
  {
    name = "轮回",
    x = 195,
    y = 461,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "轮回"
  },
  {
    name = "娑罗",
    x = 367,
    y = 461,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(114, 41),
    font = "娑罗"
  }
}) do
  local 临时函数 = 生死劫:创建按钮(v.name, v.x, v.y)

 function  临时函数:初始化()
  self:创建按钮精灵(v.tcp,1,v.font)
  end 
  function  临时函数:左键弹起(x, y)
    if v.name == "止戈" then
      请求服务(1511)
      请求服务(38,{序列=生死劫[1]})
    elseif v.name == "清心" then
      请求服务(38,{序列=生死劫[2]})
    elseif v.name == "雷霆" then
      请求服务(38,{序列=生死劫[3]})
    elseif v.name == "惜花" then
      请求服务(38,{序列=生死劫[4]})
    elseif v.name == "忘情" then
      请求服务(38,{序列=生死劫[5]})
    elseif v.name == "卧龙" then
      请求服务(38,{序列=生死劫[6]})
    elseif v.name == "天象" then
      请求服务(38,{序列=生死劫[7]})
    elseif v.name == "轮回" then
      请求服务(38,{序列=生死劫[8]})
    elseif v.name == "娑罗" then
      请求服务(38,{序列=生死劫[9]})

    end
    生死劫["关闭"]["左键弹起"](生死劫["关闭"])
  end
end
local 关闭 = 生死劫:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  生死劫:置可见(false)
end
