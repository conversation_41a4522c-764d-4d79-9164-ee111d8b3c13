.class Lorg/libsdl/app/SDLJoystickHandler;
.super Ljava/lang/Object;
.source "SDLControllerManager.java"


# direct methods
.method constructor <init>()V
    .locals 0

    .line 126
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public handleMotionEvent(Landroid/view/MotionEvent;)Z
    .locals 1
    .param p1, "event"    # Landroid/view/MotionEvent;

    .line 134
    const/4 v0, 0x0

    return v0
.end method

.method public pollInputDevices()V
    .locals 0

    .line 141
    return-void
.end method
