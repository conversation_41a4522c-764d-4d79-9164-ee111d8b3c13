local ggf = require("GGE.函数")
local SDL = require("SDL")
local en64 = require("base64").encode
local de64 = require("base64").decode
local unpk = require("cmsgpack").unpack
local pack = require("cmsgpack").pack
local s_format = string.format
local s_unpack = string.unpack
local t_insert = table.insert
local t_remove = table.remove
local ipairs = ipairs
local pairs = pairs
local assert = assert
local tonumber = tonumber
local toutf8 = gge.gbktoutf8
local togbk = gge.utf8togbk
local 配置 = class("资源配置")
function 配置:初始化配置()
    -- local SDLF = require("SDL.函数")
    -- local cfg = zdloadstring(SDL.LoadFile(SDLF.取内部存储路径() .. "/config.txt"))
    -- cfg = cfg or {}
    -- self.配置 = {}
    -- self.配置.音乐 = cfg.音乐 or 50
    -- self.配置.音效 = cfg.音效 or 50
    -- self.配置.账号 = cfg.账号 and tostring(cfg.账号) or ""
    -- self.配置.密码 = cfg.密码 and tostring(cfg.密码) or ""
    -- self.配置.锦衣 = cfg.锦衣 or 1
    -- self.配置.分辨率 = cfg.分辨率 or 1
    -- self.配置.地图特效 = cfg.地图特效 or 0
    -- self.配置.显示玩家 = cfg.显示玩家 or 0
    -- self.配置.变身造型 = cfg.变身造型 or 0
    -- self.配置.传闻消息 = cfg.传闻消息 or 0
    -- self.配置.屏蔽摊位 = cfg.屏蔽摊位 or 0
    -- self.配置.显示蓝条 = cfg.显示蓝条 or 0
    -- self.配置.活动消息 = cfg.活动消息 or 0
    -- self.配置.锦衣效果 = cfg.锦衣效果 or 0
    -- self.配置.光环足迹 = cfg.光环足迹 or 0
    -- self.配置.版本号 = cfg.版本号 or "1.00"
    -- if __手机 then
    --     self.配置.资源包 = cfg.资源包 or {}
    -- end
    -- if not __手机 and not ggf.判断文件(SDLF.取内部存储路径() .."/config.txt") then
    --      self:写出文件("config.txt", zdtostring(self.配置))
    -- end
    -- if  not  ggf.判断文件(SDLF.取内部存储路径() .."/ggwb.txt") then
    --     self:写出文件("ggwb.txt", "")
    -- end
    -- self.公告文本=SDL.LoadFile(SDLF.取内部存储路径() .. "/ggwb.txt")


    local SDLF = require("SDL.函数")
    self.配置 = {}
    self.配置.音乐 =  50
    self.配置.音效 =  50
    self.配置.账号 = ""
    self.配置.密码 = ""
    self.配置.行囊 =  0
    self.配置.锦衣 =  1
    self.配置.分辨率 = 1
    self.配置.地图特效 =  0
    self.配置.显示玩家 =  0
    self.配置.变身造型 =  0
    self.配置.传闻消息 = 0
    self.配置.屏蔽摊位 =  0
    self.配置.显示蓝条 = 0
    self.配置.活动消息 =  0
    self.配置.锦衣效果 =  0
    self.配置.光环足迹 =  0
    self.配置.坐骑显示 =  1
    self.配置.版本号 = "1.00"
    if __手机 then
        self.配置.资源包 = {}
    end
    if not ggf.判断文件(SDLF.取内部存储路径() .."/config.txt") then
        self:写出文件("config.txt", zdtostring(self.配置))
    else
        local cfg = zdloadstring(SDL.LoadFile(SDLF.取内部存储路径() .. "/config.txt"))
        for k, v in pairs(self.配置) do
            if cfg[k] then
                if k=="账号" or k=="密码" or k=="版本号" then
                    self.配置[k]=tostring(cfg[k])
                elseif k=="资源包" then
                    self.配置[k]= cfg[k]
                else
                    self.配置[k]=tonumber(cfg[k])
                end
            end
        end
    end

    if  not  ggf.判断文件(SDLF.取内部存储路径() .."/ggwb.txt") then
        self:写出文件("ggwb.txt", "")
    end
    self.公告文本=SDL.LoadFile(SDLF.取内部存储路径() .. "/ggwb.txt")







end

local _split = function(str, mark)
    local t = {}
    local N = 1
    for line in str:gmatch("([^" .. mark .. "]+)") do
        if tonumber(line) then
            t[N] = tonumber(line)
        else
            t[N] = line
        end
        N = N + 1
    end
    return t
end
function 配置:depp(str)
    if not str then
        return
    end
    str = toutf8(str)
    local line = _split(str, "\r\n")
    local h = _split(line[1], " ")
    local n = 1
    local ret = {}
    local num = t_remove(h, 1)
    if num > 1000 then
        for i = 1, num - 1000 do
            ret[i] = {
                a = h[i],
                b = h[i + 1]
            }
            n = n + 1
            for j = 1, line[n] do
                ret[i][j] = {}
                local N = 1
                for c = 2, 4 do
                    for _, v in ipairs(_split(line[n + c], " ")) do
                        ret[i][j][N] = v
                        N = N + 1
                    end
                end
                n = n + 5
            end
        end
    else
        for i = 1, num do
            ret[i] = {
                a = h[i],
                b = h[i + 1]
            }
            n = n + 1
            for j = 1, line[n] do
                ret[i][j] = {}
                local N = 1
                for c = 1, 3 do
                    for _, v in ipairs(_split(line[n + c], " ")) do
                        ret[i][j][N] = v
                        N = N + 1
                    end
                end
                n = n + 3
            end
        end
    end
    return ret
end

return 配置
