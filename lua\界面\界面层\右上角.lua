-- <AUTHOR> GGELUA
-- @Last Modified by    : baidwwy
-- @Date                : 2024-09-04 16:18:56
-- @Last Modified time  : 2024-10-16 19:36:46

local 右上角 = __UI界面["界面层"]["创建控件"](__UI界面["界面层"], "右上角", 670 + abbr.py.x, 0, 290,370)
function 右上角:初始化()
    local nsf = require("SDL.图像")(280, 67)
    if nsf["渲染开始"](nsf) then
        __res:getPNGCC(9, 1452, 3, 50, 50):显示(163, 3)--头像框
        __res:getPNGCC(9, 1379, 3, 67, 14):显示(214, 3)--头像框
        __res:getPNGCC(9, 1379, 3, 67, 14):显示(214, 15)--头像框
        __res:getPNGCC(9, 1379, 3, 67, 14):显示(214, 27)--头像框
        __res:getPNGCC(9, 1379, 3, 67, 14):显示(214, 39)--头像框

        __res:getPNGCC(9, 1379, 3, 67, 14):显示(97,  3)--头像框
        __res:getPNGCC(9, 1379, 3, 67, 14):显示(97,  15)--头像框
        __res:getPNGCC(9, 1379, 3, 67, 14):显示(97,  27)--头像框
        __res:getPNGCC(9, 1620, 3, 38, 38):显示(58, 3)--头像框

        -- 字体18["置颜色"](字体18, __取颜色("白色"))
        -- local tsf = 字体18["取图像"](字体18, "名称")
        -- tsf["显示"](tsf, 50,50)

        nsf["渲染结束"](nsf)
    end
    self:置精灵(nsf["到精灵"](nsf))
end

function 右上角:显示(x, y)
end

function 右上角:重置人物()
    右上角["人物气血条"]["置位置"](右上角["人物气血条"], math.floor(角色信息["气血"] /角色信息["最大气血"] * 100))
    右上角["人物魔法条"]["置位置"](右上角["人物魔法条"], math.floor(角色信息["魔法"] /角色信息["最大魔法"] * 100))
    右上角["人物愤怒条"]["置位置"](右上角["人物愤怒条"], math.floor(角色信息["愤怒"] / 150 * 100))
    右上角["人物经验条"]["置位置"](右上角["人物经验条"], math.floor(角色信息["当前经验"] /角色信息["最大经验"] * 100))
end

function 右上角:重置召唤兽()
    if 角色信息["参战宝宝"]["名称"] then
        右上角["召唤兽气血条"]["置位置"](右上角["召唤兽气血条"],
            math.floor(角色信息["参战宝宝"]["气血"] / 角色信息["参战宝宝"]["最大气血"] * 100))
        右上角["召唤兽魔法条"]["置位置"](右上角["召唤兽魔法条"],
            math.floor(角色信息["参战宝宝"]["魔法"] / 角色信息["参战宝宝"]["最大魔法"] * 100))
        右上角["召唤兽经验条"]["置位置"](右上角["召唤兽经验条"],
            math.floor(角色信息["参战宝宝"]["当前经验"] / 角色信息["参战宝宝"]["最大经验"] * 100))
    else
        右上角["召唤兽气血条"]["置位置"](右上角["召唤兽气血条"], 0)
        右上角["召唤兽魔法条"]["置位置"](右上角["召唤兽魔法条"], 0)
        右上角["召唤兽经验条"]["置位置"](右上角["召唤兽经验条"], 0)
    end
end

local 人物头像 = 右上角["创建网格"](右上角, "人物头像", 163, 4, 60, 60)--角色头像大小位置
function 人物头像:初始化()
    self:创建格子(60, 60, 0, 0, 1, 1)
end

function 人物头像:左键弹起(x, y, a, b, msg)
    发送数据(7)
end

function 人物头像:置头像(数据)
    local lssj = __头像格子["创建"]()
    lssj["置头像"](lssj, 数据, "右上角", 50, 50)
    人物头像["子控件"][1]["置精灵"](人物头像["子控件"][1], lssj)
    右上角["重置人物"](右上角)
end

for i, v in ipairs({
    {
        name = "人物气血条",
        x = 226,
        y = 6,
        w = 51,
        h = 7,
        tcp = __res:getPNGCC(9, 1379, 51, 50,8)
    },
    {
        name = "人物魔法条",
        x = 225,
        y = 18,
        w = 51,
        h = 7,
        tcp = __res:getPNGCC(9, 1508, 3, 50, 8)
    },
    {
        name = "人物愤怒条",
        x = 226,
        y = 30,
        w = 50,
        h = 7,
        tcp = __res:getPNGCC(9, 1564, 3, 50, 8)
    },
    {
        name = "人物经验条",
        x = 226,
        y = 42,
        w = 50,
        h = 7,
        tcp = __res:getPNGCC(9, 1379, 37, 50, 8)
    }
}) do
    local 临时函数 = 右上角["创建我的进度"](右上角, v.tcp, v.name, v.x, v.y, v.w, v.h)
    function 临时函数:左键弹起(x, y)
        __UI弹出["气魔补充"]["打开"](__UI弹出["气魔补充"], "人物")
    end
end
local 召唤兽头像 = 右上角["创建网格"](右上角, "召唤兽头像", 60, 4, 55, 55)
function 召唤兽头像:初始化()
    self:创建格子(55, 55, 0, 0, 1, 1)
end

function 召唤兽头像:左键弹起(x, y, a, b, msg)
    发送数据(5001)
end

function 召唤兽头像:置头像(数据)
    local lssj = __头像格子["创建"]()
    lssj["置头像"](lssj, 数据, "右上角", 36, 36,"bb")
    召唤兽头像["子控件"][1]["置精灵"](召唤兽头像["子控件"][1], lssj)
    右上角["重置召唤兽"](右上角)
end

for i, v in ipairs({
    {
        name = "召唤兽气血条",
        x = 109,
        y = 6,
        w = 51,
        h = 7,
        tcp = __res:getPNGCC(9, 1379, 51, 50,8)
    },
    {
        name = "召唤兽魔法条",
        x = 109,
        y = 18,
        w = 50,
        h = 8,
        tcp = __res:getPNGCC(9, 1508, 3, 50, 8)
    },
    {
        name = "召唤兽经验条",
        x = 109,
        y = 30,
        w = 50,
        h = 8,
        tcp = __res:getPNGCC(9, 1379, 37, 50, 8)
    }
}) do
    local 临时函数 = 右上角["创建我的进度"](右上角, v.tcp, v.name, v.x, v.y, v.w, v.h)
    function 临时函数:左键弹起(x, y)
        __UI弹出["气魔补充"]["打开"](__UI弹出["气魔补充"], "召唤兽")
    end
end


local 状态网格 = 右上角["创建网格"](右上角, "状态网格", 163, 67, 120, 60)
function 状态网格:初始化()
    self:创建格子(26, 26, 2, 2, 2, 4)
end

function 状态网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a]._spr then
        __UI弹出["状态提示"]["打开"](__UI弹出["状态提示"], self.数据)
    end
end

function 状态网格:置数据(data)
    for i = 1, #self.子控件 do
        if data[i] then
            local nsf = require("SDL.图像")(26, 26)
            if nsf["渲染开始"](nsf) then
                local lssj = __取界面小图标外框(data[i][2])
                __res["取图像"](__res, __res["取地址"](__res, "shape/ui/jm/", lssj[1]))["显示"](__res["取图像"
                    ](__res, __res["取地址"](__res, "shape/ui/jm/", lssj[1])), 0, 0)
                lssj = __取界面小图标(data[i][6])
                __res["取图像"](__res, __res["取地址"](__res, "shape/ui/jm/", lssj[1]))["显示"](__res["取图像"
                    ](__res, __res["取地址"](__res, "shape/ui/jm/", lssj[1])), 4, 4)
                nsf["渲染结束"](nsf)
            end
            self.子控件[i]["置精灵"](self.子控件[i], nsf["到精灵"](nsf))
        else
            self.子控件[i]["置精灵"](self.子控件[i])
        end
    end
    self.数据 = data
end

-- local 追踪 = 右上角["创建我的按钮"](右上角, __res:getPNGCC(1, 330, 390, 175, 24), "追踪", 115, 130)
-- function 追踪:左键弹起(x, y, msg)
-- end

local 任务栏 = __UI界面["界面层"]["右上角"]["创建控件"](__UI界面["界面层"]["右上角"], "任务栏", 80, 67, 210, 300)

function 任务栏:初始化()
    -- local nsf = require("SDL.图像")(200, 200)
    -- if nsf["渲染开始"](nsf) then
    --   -- __res:getPNGCC(4, 540, 51, 467, 89)["显示"](__res:getPNGCC(4, 540, 51, 467, 89), 23, 395)
    --   字体16["置颜色"](字体16, __取颜色("白色"))
    --   字体16["取图像"](字体16, "任务追踪")["显示"](字体16["取图像"](字体16, "任务追踪"), 98, 67)

    --   nsf["渲染结束"](nsf)
    -- end
    -- self:置精灵(nsf["到精灵"](nsf))
  end


local 追踪 = 任务栏["创建我的按钮"](任务栏, __res:getPNGCC(1, 330, 390, 170, 24, true)["拉伸"](__res:getPNGCC(1, 330, 390, 175, 24, true), 170, 24), "任务追踪", 35,  63, "任务追踪")
function 追踪:左键弹起(x, y, msg)

end




local 追踪1 = 任务栏["创建我的按钮"](任务栏, __res:getPNGCC(11, 3, 49, 11, 16), "追踪1", 175, 67)
function 追踪1:左键弹起(x, y, msg)
end

local 追踪2 = 任务栏["创建我的按钮"](任务栏, __res:getPNGCC(11, 76, 3, 19, 21), "追踪2", 35, 64)
function 追踪2:左键弹起(x, y, msg)
end

local 追踪3 = 任务栏["创建我的按钮"](任务栏, __res:getPNGCC(11, 49, 3, 21, 20), "追踪3", 58, 65)
function 追踪3:左键弹起(x, y, msg)
end

local 右关闭 = 右上角["创建我的按钮"](右上角, __res:getPNGCC(11, 49, 29, 12, 18), "右关闭", 272, 133)
function 右关闭:左键弹起(x, y, msg)
    if 右上角["任务栏"]["是否可见"] then
        右上角["任务栏"]["置可见"](右上角["任务栏"], false)
        self:置坐标(272, 133)
    else
        右上角["任务栏"]["置可见"](右上角["任务栏"], true)
        self:置坐标(272, 133)
    end
end


local 追踪列表 = 任务栏["创建列表"](任务栏, "追踪列表", 35, 90, 175, 235)--(显示x，显示y)
function 追踪列表:初始化()
    self:置文字(字体16)
    self.行高度 = 102
    self.选中精灵 = nil
    self.焦点精灵 = nil
    self.行间距 = 1
    
  
    --     字体18["置颜色"](字体18, __取颜色("白色"))
    -- 字体18["取图像"](字体18, "任务追踪")["显示"](字体18["取图像"](字体18, "任务追踪"), 250, 130)

    --         字体18["置颜色"](字体18, __取颜色("白色"))
    --     local tsf = 字体18["取图像"](字体18, "名称")
    --     tsf["显示"](tsf, 50,50)

end


function 追踪列表:qingkong(data)
    self:重置(data)
end
function 追踪列表:重置(data)
   -- table.print(data)
    local tbsj = {}
    self.清空(self)
    local go1
    for i, v in ipairs(data) do
        if v[1] and "摄妖香" ~= v[1] and "罗羹效果" ~= v[1] and "变身卡" ~= v[1] then
            go1=true
            local r = self:添加(v[2])
            local beij=__res:getPNGCC(1, 682, 386, 219, 126)
            r["置精灵"](r,beij["拉伸"](beij, 219, 100)["到精灵"]((beij["拉伸"](beij, 219, 100))))
            -- r["置精灵"](r,
            --     __res:getPNGCC(1, 680, 383, 217, 126)["拉伸"](__res:getPNGCC(1, 680, 383, 217, 126), 170, 90)["到精灵"
            --     ]((__res:getPNGCC(1, 680, 383, 217, 126)["拉伸"](__res:getPNGCC(1, 680, 383, 217, 126), 170, 90))))
            local 文本 = r["创建我的文本"](r, "追踪文本", 5, 5, 160, 90)
            文本["置文本"](文本, "#G" .. v[1])
            文本["置文本"](文本, v[2])
            
            r["置可见"](r, true, true)
            function 文本:回调左键弹起(cb, msg)
                local lssj = 分割文本(cb, "*")
                if "npc查询" == lssj[3] then
                    --1=名称 2 = 地图id 
                   -- table.print(lssj)
                    -- print(11111111111)
                    local npc名称=lssj[1]
                    local 地图名称=lssj[2]
                    local xxt=__主控["假人信息"][lssj[2]][lssj[1]].x
                    local yyt=__主控["假人信息"][lssj[2]][lssj[1]].y
                    local dituid=__主控["假人信息"][lssj[2]][lssj[1]]["地图id"]
                    __UI弹出["提示框"]["打开"](__UI弹出["提示框"],"#Y在" ..地图名称 .."（" .. xxt .."#Y," .. yyt.."）处")
                    if __主显.主角 then 
                        if  __主显.主角.是否队长 or (not __主显.主角.是否队长 and  not __主显.主角.是否组队) then
                            if 角色信息["地图数据"]["编号"] == dituid then
                                local xy = require("GGE.坐标")(xxt * 20,yyt * 20):floor()
                                __主显["主角"]["设置路径"](__主显["主角"], xy)
                            end
                            if __xsssss then
                                local 事件 = function()
                                    发送数据(6333,{xydt=dituid,x=xxt,y=yyt})
                                end
                                local wb = "请问是否跳转至#Y"..npc名称.."#W身边？"
                                __UI界面.窗口层.文本栏:打开(wb, 285, 155, 390, 200, 事件)
                            else


                                if 角色信息.剧情 and 角色信息.剧情.地图 then
                                    -- table.print(角色信息.剧情)
                                    local jr=场景取假人表(角色信息.剧情.地图,nil,1)
                                    if jr[角色信息.剧情.编号] and jr[角色信息.剧情.编号].名称==npc名称 then
                                        -- table.print(jr)
                                        local 事件 = function()
                                            发送数据(6304,{ditu=角色信息.剧情.地图,x=xxt,y=yyt,编号=角色信息.剧情.编号})
                                        end
                                        local wb = "请问是否跳转至#Y"..npc名称.."#W身边？"
                                        __UI界面.窗口层.文本栏:打开(wb, 285, 155, 390, 200, 事件)
                                    end
                                end
                            end
                        end
                    end
                elseif lssj[3] == "临时npc" then
                    local xxt=lssj[4]
                    local yyt=lssj[5]
                    local npc名称=lssj[1]
                   -- table.print(lssj)
                    -- .1 [子鼠]
                    -- .2 [1001]
                    -- .3 [临时npc]
                    -- .4 [67]
                    -- .5 [141]
                    __UI弹出["提示框"]["打开"](__UI弹出["提示框"],"#Y在" ..取地图名称(lssj[2]+0) .."（" .. xxt .."#Y," .. yyt.."）处")
                    if __xsssss then
                        if __主显.主角 then 
                            if  __主显.主角.是否队长 or (not __主显.主角.是否队长 and  not __主显.主角.是否组队) then
                                local 事件 = function()
                                    发送数据(6333,{xydt=lssj[2],x=xxt,y=yyt})
                                end
                                local wb = "请问是否跳转至#Y"..npc名称.."#W身边？"
                                __UI界面.窗口层.文本栏:打开(wb, 285, 155, 390, 200, 事件)
                            end
                        end
                    else
                        if 角色信息["地图数据"]["编号"]+0 == lssj[2]+0 then
                            -- local 内容={x=lssj[4]+0,y=lssj[5]+0,距离=0}
                            -- if 引擎.场景.飞行 then
                            --     tp.场景.人物:设置路径无障碍(内容)
                            -- else
                            --     tp.场景.人物:设置路径(内容)
                            -- end
                            
                            local xy = require("GGE.坐标")(xxt * 20,yyt * 20):floor()
                            __主显["主角"]["设置路径"](__主显["主角"], xy)
                        else
                            __UI弹出.提示框:打开("#Y/这个NPC不在这个地图")
                        end
                    end
                elseif "bz提示" == lssj[3] then
                end
            end
        elseif "摄妖香" == data[i][1] and data[i][4] and data[i][4] > 0 then
            table.insert(tbsj, {
                "摄妖香",
                "绿色",
                "摄妖香剩余:",
                "分",
                data[i][4],
                "摄妖香"
            })
        elseif "罗羹效果" == data[i][1] then
            if data[i][4][1] and data[i][4][1] > 0 then
                table.insert(tbsj, {
                    "红罗羹",
                    "橙色",
                    "储备气血剩余:",
                    nil,
                    data[i][4][1],
                    "丹药黄"
                })
            end
            if data[i][4][2] and data[i][4][2] > 0 then
                table.insert(tbsj, {
                    "八珍玉液",
                    "橙色",
                    "储备气血上限剩余:",
                    nil,
                    data[i][4][2],
                    "丹药紫"
                })
            end
            if data[i][4][3] and data[i][4][3] > 0 then
                table.insert(tbsj, {
                    "绿芦羹",
                    "橙色",
                    "储备魔法剩余:",
                    nil,
                    data[i][4][3],
                    "丹药蓝"
                })
            end
        elseif "变身卡" == data[i][1] and data[i][4] and data[i][4] > 0 then
            table.insert(tbsj, {
                "变身卡",
                "绿色",
                "变身卡剩余:",
                "分",
                data[i][4],
                "变身卡"
            })
        end
    end




    if not go1 then
        local r = self:添加()
        local beij=__res:getPNGCC(1, 682, 386, 219, 126)
        r["置精灵"](r,beij["拉伸"](beij, 219, 100)["到精灵"]((beij["拉伸"](beij, 219, 100))))
        -- r["置精灵"](r,
        --     __res:getPNGCC(1, 680, 383, 217, 126)["拉伸"](__res:getPNGCC(1, 680, 383, 217, 126), 170, 90)["到精灵"
        --     ]((__res:getPNGCC(1, 680, 383, 217, 126)["拉伸"](__res:getPNGCC(1, 680, 383, 217, 126), 170, 90))))
        local 文本 = r["创建我的文本"](r, "追踪文本", 5, 5, 160, 90)
        文本["置文本"](文本, "\n当前追踪列表没有任务")
        r["置可见"](r, true, true)
        
    end
    右上角["状态网格"]["置数据"](右上角["状态网格"], tbsj)
    if 角色信息["剧情"] ~= nil and 角色信息["地图数据"]["编号"] == 角色信息["剧情"]["地图"] and
        nil == 角色信息["剧情"]["附加"]["战斗"] then
        for k, v in pairs(__主显["地图"]["对象"]) do
            if ggetype(v) == "NPC" then
                if v.pid == 角色信息["剧情"]["编号"] then
                    v["置任务"](v, true)
                else
                    v["置任务"](v, false)
                end
            end
        end
    elseif 角色信息["剧情"] ~= nil and 角色信息["地图数据"]["编号"] == 角色信息["剧情"]["地图"]
        and nil ~= 角色信息["剧情"]["附加"]["战斗"] then
        for k, v in pairs(__主显["地图"]["对象"]) do
            if ggetype(v) == "NPC" then
                if v.pid == 角色信息["剧情"]["编号"] then
                    v["置任战"](v, true)
                else
                    v["置任战"](v, false)
                end
            end
        end
    end
    右上角["任务栏"]["置可见"](右上角["任务栏"], true)
    右上角.右关闭:置坐标(272, 133)
end

function 追踪列表:左键弹起(x, y, i, item, msg)
end

local 玩家头像 = 右上角["创建控件"](右上角, "玩家头像", 45, 115, 60, 60)
function 玩家头像:初始化()
    self:置精灵(__res:getPNGCC(3, 683, 284, 73, 73, true)["拉伸"](__res:getPNGCC(3, 683, 284, 73, 73, true), 60, 60)["到精灵"]((__res:getPNGCC(3, 683, 284, 73, 73, true)["拉伸"](__res:getPNGCC(3, 683, 284, 73, 73, true), 60, 60))))
    self:置可见(false)
end

local 头像网格 = 玩家头像["创建网格"](玩家头像, "头像网格", 0, 0, 60, 60)
function 头像网格:初始化()
    self:创建格子(60, 60, 0, 0, 1, 1)
end

function 头像网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a] and self.子控件[a]._spr and  self.子控件[a]._spr["数据"] then
        __UI弹出["玩家信息弹出"]["打开"](__UI弹出["玩家信息弹出"], self.子控件[a]._spr["数据"])
    end
end

function 头像网格:置头像(data)
    local lssj = __头像格子["创建"]()
    lssj["置头像"](lssj, data, "右上角", 60, 60)
    self.子控件[1]["置精灵"](self.子控件[1], lssj)
    if not 玩家头像["是否可见"] then
        玩家头像["置可见"](玩家头像, true)
    end
end

function 右上角:重置界面()
    self["置可见"](self, true, true)
    self.玩家头像:置可见(false)
end
