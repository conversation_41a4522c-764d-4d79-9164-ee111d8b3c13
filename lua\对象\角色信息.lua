--[[
Author: GGELUA
Date: 2024-03-28 07:56:09
Last Modified by: GGELUA
Last Modified time: 2024-03-28 09:13:02
--]]

local 角色信息 = class("角色信息")
function 角色信息:初始化()
    self.模型 = "剑侠客"
    self.坐标 = require("GGE.坐标")(2320, 1120)
    self.名称 = "孤城月影"
end

function 角色信息:重置属性(属性)

    for n, v in pairs(属性) do
        if type(n) ~= "function" and type(n) ~= "运行父函数" and "存档数据" ~= n and "宝宝列表" ~= n then
            if type(n) == "table" then
                self[n] = zdloadstring(zdtostring(v))
            else
                self[n] = v
            end
        end
    end
    self.坐标:pack(self.地图数据.x, self.地图数据.y)
    __UI界面.界面层.玩家界面:重置人物()
    __UI界面.界面层.玩家界面:重置召唤兽()
end

return 角色信息

--你这号去哪里了 应该是在朱紫国朱紫国有地铁啊 上端游看看

