{
     "folders": [
        {
            "path": "."
        }
        {
            "path": "../../GGELUA/GGELUA/ggelua",
            "name": "GGELUA"
        }
    ],

    "settings": {
        // "fileheader.customMade": {
        //     "Author":"GGELUA",//作者名称
        //     "Date": "Do not edit", // 设置后默认设置文件生成时间
        //     "LastEditors": "GGELUA",
        //     "LastEditTime": "Do not edit", // 设置后，保存文件更改默认更新最后编辑时间
        // },
        // "fileheader.configObj": {
        //     "specialOptions": {
        //         "LastEditTime": "Last Modified time",
        //         "LastEditors": "Last Modified by",
        //     },
        // },
        "fileheader.customMade": {
            "LastEditTime": "Do not edit", // 设置后，保存文件更改默认更新最后编辑时间
        },
       
		"terminal.integrated.defaultProfile.windows": "Command Prompt"
    },
}