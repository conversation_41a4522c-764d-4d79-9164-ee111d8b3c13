--[[
LastEditTime: 2024-10-21 11:18:05
--]]


local SDL = require("SDL")
function 战斗层:初始化()
    self:置宽高(引擎.宽度,引擎.高度)
end

function 战斗层:重新初始化()
    self:置宽高(引擎.宽度,引擎.高度)
    for k, v in self:遍历控件() do
        v:置可见(false)
        if v.可初始化 then
            v:初始化()
            if v.重新初始化 then
                v:重新初始化()
            end
        end
    end
end



-- function 战斗层:消息事件(msg)
--     for i, v in ipairs(msg.鼠标) do
--         local x, y = v.x, v.y
--         if v.button == SDL.BUTTON_LEFT then
--             if v.type == SDL.MOUSE_DOWN then
--                 self.按下 = os.time()
--             elseif v.type == SDL.MOUSE_UP and self.按下 then
--                 self.按下 = false

--             end
--         elseif v.button ~= SDL.BUTTON_RIGHT or v.type == SDL.MOUSE_UP then
--         end
--     end
-- end

