--[[
LastEditTime: 2024-10-20 17:11:46
--]]
local 自动抓鬼 = 战斗层:创建窗口("自动抓鬼", 180, 5, 200, 100)
function 自动抓鬼:初始化()
  self.自定义={}
  self.自定义.大模型=0x00149FEC
  self.自定义.资源="jszy/zdmx"
  self.自定义.名称="自动抓鬼"
  self.自定义.介绍="#Y剩余抓鬼次数:0"
end

function 自动抓鬼:打开(数据)
    self:置可见(true)
    self.进程 = "开启"
    self.仙玉 = 0
    self.事件 = "自动抓鬼"
    self.自定义.名称="自动抓鬼"
    self.自定义.介绍="#Y剩余抓鬼次数:0"
    self:刷新(数据)
end


function 自动抓鬼:刷新(数据)
  self.进程 = 数据.进程
  self.仙玉 = 数额尾数转换(数据.仙玉+0)
  self.事件 = 数据.事件
  self.自定义.名称=数据.事件
  self.自定义.介绍="#Y剩余抓鬼次数:"..self.仙玉
  self:创建纹理精灵(function()
    置窗口背景(self.事件,0,0,200,100,true):显示(0,0)
    标题字体:置颜色(255,255,255,255):取图像("剩余次数:"..self.仙玉.."次"):显示(15,35)
  end
)
end

-- function 自动抓鬼:显示(x,y)
--     if self.图像 then 
--        self.图像:显示(x,y)
--     end
-- end

function 自动抓鬼:更新(dt)
    if self.是否可见 then
        if self.进程 == "关闭"  then
            self:置可见(false)
        end
    end
end






 local 取消按钮 = 自动抓鬼:创建红色按钮("关 闭", "取消按钮", 60, 65,80,22)
 function 取消按钮:左键弹起(x, y)
      请求服务(42)
      自动抓鬼:置可见(false)
 end
