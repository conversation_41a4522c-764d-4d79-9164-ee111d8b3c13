local 人物属性 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "人物属性", 55 + abbr.py.x, 22 + abbr.py.y, 773, 483)

function 人物属性:初始化()
  self:置精灵(置窗口背景("人物属性", 0, 12, 720, 470))
  self.选中技能 = nil
end
function 人物属性:打开()
  self:置可见(true)
  self.生活选中 = nil
  人物属性["人物按钮"]["置选中"](人物属性["人物按钮"], true)
  人物属性["人物"]["重置"](人物属性["人物"])
end
local 关闭 = 人物属性["创建我的按钮"](人物属性, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 680, 0)
function 关闭:左键弹起(x, y, msg)
  __UI界面["窗口层"]["人物属性"]["置可见"](__UI界面["窗口层"]["人物属性"], false)
end
local 人物按钮 = 人物属性["创建我的单选按钮"](人物属性, __res:getPNGCC(1, 686, 0, 54, 98, true), __res:getPNGCC(1, 1132, 0, 54, 99, true), "人物按钮", 719, 61, "人\n物")
function 人物按钮:左键按下(消息, x, y)
  人物属性["人物"]["重置"](人物属性["人物"])
end
local 技能按钮 = 人物属性["创建我的单选按钮"](人物属性, __res:getPNGCC(1, 686, 0, 54, 98, true), __res:getPNGCC(1, 1132, 0, 54, 99, true), "技能按钮", 719, 162, "技\n能")
function 技能按钮:左键按下(消息, x, y)
  人物属性["技能"]["重置"](人物属性["技能"])
end
local 修炼按钮 = 人物属性["创建我的单选按钮"](人物属性, __res:getPNGCC(1, 686, 0, 54, 98, true), __res:getPNGCC(1, 1132, 0, 54, 99, true), "修炼按钮", 719, 262, "修\n炼")
function 修炼按钮:左键按下(消息, x, y)
  人物属性["修炼"]["重置"](人物属性["修炼"])
end
local 人物 = 人物属性["创建控件"](人物属性, "人物", 0, 0, 773, 483)
function 人物:初始化()
  -- self.梦幻秀 = __res:getPNGCC(1, 792, 0, 270, 380)["到精灵"]((__res:getPNGCC(1, 792, 0, 270, 380)))
  -- self.经验背景 = __res:getPNGCC(1, 400, 109, 185, 17)["到精灵"]((__res:getPNGCC(1, 400, 109, 185, 17)))
  local nsf = require("SDL.图像")(315, 470)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(1, 792, 0, 270, 380)["显示"](__res:getPNGCC(1, 792, 0, 270, 380), 22, 50)
    __res:getPNGCC(1, 400, 109, 185, 17)["显示"](__res:getPNGCC(1, 400, 109, 185, 17), 63, 441)
    字体18["置颜色"](字体18, 255, 255, 255)
    字体18:取图像("经验"):置混合(0):显示(22, 440)
    local lssj = 取头像(角色信息["模型"])
    if 角色信息["模型"] == '影精灵' then
      __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[6]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[6])), 60, 160)
    else
      __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[6]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[6])), 153, 383)
    end
    字体18["取图像"](字体18, 角色信息["名称"]):显示(67, 52)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.门派 = "无\n门\n派"
end
function 人物:重置()
  self:置可见(true)
  人物属性["技能"]["置可见"](人物属性["技能"], false)
  人物属性["修炼"]["置可见"](人物属性["修炼"], false)
  人物["属性重置"](人物)
  人物["属性按钮"]["置选中"](人物["属性按钮"], true)
  人物["属性"]["普通属性按钮"]["置选中"](人物["属性"]["普通属性按钮"], true)
  人物["属性"]["置可见"](人物["属性"], true)
  人物["信息"]["置可见"](人物["信息"], false)
  if 角色信息.等级>=175 then
    人物["升级按钮"]["置可见"](人物["升级按钮"], false)
  else
    人物["升级按钮"]["置可见"](人物["升级按钮"], true)
  end
end
function 人物:属性重置()
  人物["经验条"]["置位置"](人物["经验条"], math.floor(角色信息["当前经验"] / 角色信息["最大经验"] * 100))
  人物["属性"]["气血条"]["置位置"](人物["属性"]["气血条"], math.floor(角色信息["气血"] / 角色信息["最大气血"] * 100))
  人物["属性"]["魔法条"]["置位置"](人物["属性"]["魔法条"], math.floor(角色信息["魔法"] / 角色信息["最大魔法"] * 100))
  人物["属性"]["愤怒条"]["置位置"](人物["属性"]["愤怒条"], math.floor(角色信息["愤怒"] / 150 * 100))
  人物["属性"]["普通属性"]["重置"](人物["属性"]["普通属性"])
  self.门派 = __主控["取门派"](__主控)
  local nsf = require("SDL.图像")(315, 200)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18:取图像(角色信息["等级"] .. "级"):置混合(0):显示(202, 52)
    字体18:取图像(self.门派):置混合(0):显示(27, 112)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
function 人物:显示(x, y)
  if self.图像 then
    self.图像["显示"](self.图像, x, y)
  end
end
local 经验条 = 人物["创建进度"](人物, "经验条", 63, 442, 183, 16)
function 经验条:初始化()
  self:置精灵(__res:getPNGCC(1, 587, 108, 183, 16)["到精灵"]((__res:getPNGCC(1, 587, 108, 183, 16))))
end
function 经验条:显示(x, y)
  字体18["显示"](字体18, x + 10, y - 2, string.format("%s/%s", 角色信息["当前经验"], 角色信息["最大经验"]))
end
local 升级按钮 = 人物["创建我的按钮"](人物, __res:getPNGCC(1, 626, 28, 58, 34, true), "升级按钮", 250, 433, "升级")
function 升级按钮:左键弹起(x, y, msg)
  发送数据(9)
end

local 属性按钮 = 人物["创建我的单选按钮"](人物, __res:getPNGCC(1, 401, 65, 175, 43, true), __res:getPNGCC(1, 963, 495, 175, 43, true), "属性按钮", 329, 61, "属 性")
function 属性按钮:左键按下(消息, x, y)
  人物["信息按钮"]["置选中"](人物["信息按钮"], false)
  人物["属性按钮"]["置选中"](人物["属性按钮"], true)
  人物["属性重置"](人物)
  人物["属性"]["置可见"](人物["属性"], true)
  人物["信息"]["置可见"](人物["信息"], false)
end
local 信息按钮 = 人物["创建我的单选按钮"](人物, __res:getPNGCC(1, 401, 65, 175, 43, true), __res:getPNGCC(1, 963, 495, 175, 43, true), "信息按钮", 520, 61, "信 息")
function 信息按钮:左键按下(消息, x, y)
  人物["属性按钮"]["置选中"](人物["属性按钮"], false)
  人物["信息按钮"]["置选中"](人物["信息按钮"], true)
  人物["信息"]["重置"](人物["信息"])
  人物["属性"]["置可见"](人物["属性"], false)
  人物["信息"]["置可见"](人物["信息"], true)
end


local 属性 = 人物属性["人物"]["创建控件"](人物属性["人物"], "属性", 329, 105, 367, 359)
function 属性:初始化()
  local nsf = require("SDL.图像")(367, 359)
  if nsf["渲染开始"](nsf) then
    local lssj = 取属性背景(0, 0, 367, 200, true)
    lssj["显示"](lssj, 0, 158)
    字体18["置颜色"](字体18, 255, 255, 255)
    lssj = __res:getPNGCC(1, 400, 127, 305, 18)
    local 类型 = {
      "气血",
      "魔法",
      "愤怒"
    }
    for i = 1, 3 do
      lssj["显示"](lssj, 62, 13 + (i - 1) * 30)
      字体18["取图像"](字体18, 类型[i])["置混合"](字体18["取图像"](字体18, 类型[i]), 0)["显示"](字体18["取图像"](字体18, 类型[i])["置混合"](字体18["取图像"](字体18, 类型[i]), 0), 10, 13 + (i - 1) * 30)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
local 普通属性按钮 = 属性["创建我的单选按钮"](属性, __res:getPNGCC(1, 449, 28, 174, 35, true), __res:getPNGCC(1, 964, 459, 173, 35, true), "普通属性按钮", 0, 111, "普通属性")
function 普通属性按钮:左键按下(消息, x, y)
  属性["普通属性"]["置可见"](属性["普通属性"], true)
  属性["更多属性"]["置可见"](属性["更多属性"], false)
end
local 更多属性按钮 = 属性["创建我的单选按钮"](属性, __res:getPNGCC(1, 449, 28, 174, 35, true), __res:getPNGCC(1, 964, 459, 173, 35, true), "更多属性按钮", 192, 111, "更多属性")
function 更多属性按钮:左键按下(消息, x, y)
  属性["普通属性"]["置可见"](属性["普通属性"], false)
  属性["更多属性"]["置可见"](属性["更多属性"], true)
  属性["更多属性"]["更多属性列表"]["重置"](属性["更多属性"]["更多属性列表"])
end
local 气血条 = 属性["创建进度"](属性, "气血条", 62, 14, 305, 16)
function 气血条:初始化()
  self:置精灵(__res:getPNGCC(1, 400, 163, 305, 16)["到精灵"]((__res:getPNGCC(1, 400, 163, 305, 16))))
end
function 气血条:显示(x, y)
  字体18["显示"](字体18, x + 100, y - 2, string.format("%s/%s/%s", 角色信息["气血"], 角色信息["气血上限"], 角色信息["最大气血"]))
end
local 魔法条 = 属性["创建进度"](属性, "魔法条", 62, 44, 305, 16)
function 魔法条:初始化()
  self:置精灵(__res:getPNGCC(1, 401, 145, 305, 16)["到精灵"]((__res:getPNGCC(1, 401, 145, 305, 16))))
end
function 魔法条:显示(x, y)
  字体18["显示"](字体18, x + 100, y - 2, string.format("%s/%s", 角色信息["魔法"], 角色信息["最大魔法"]))
end
local 愤怒条 = 属性["创建进度"](属性, "愤怒条", 62, 74, 305, 16)
function 愤怒条:初始化()
  self:置精灵(__res:getPNGCC(1, 401, 181, 304, 16)["到精灵"]((__res:getPNGCC(1, 401, 181, 304, 16))))
end
function 愤怒条:显示(x, y)
  字体18["显示"](字体18, x + 100, y - 2, string.format("%s/%s", 角色信息["愤怒"], 150))
end
local 普通属性 = 属性["创建控件"](属性, "普通属性", 0, 158, 367, 202)
function 普通属性:初始化()
  local nsf = require("SDL.图像")(367, 202)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, 255, 255, 255)
    local lssj = __res:getPNGCC(1, 552, 0, 101, 23)
    local 类型 = {
      "命中",
      "伤害",
      "防御",
      "速度",
      "法伤",
      "法防",
      "体质",
      "魔力",
      "力量",
      "耐力",
      "敏捷",
      "潜能"
    }
    local pyx = 0
    local pyy = 0
    for i = 1, 12 do
      if i > 6 then
        pyx = 168
        pyy = -192
      end
      lssj["显示"](lssj, 74 + pyx, 8 + pyy + (i - 1) * 32)
      字体18["取图像"](字体18, 类型[i])["置混合"](字体18["取图像"](字体18, 类型[i]), 0)["显示"](字体18["取图像"](字体18, 类型[i])["置混合"](字体18["取图像"](字体18, 类型[i]), 0), 22 + pyx, 8 + pyy + (i - 1) * 32)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 普通属性:重置()
  local 类型 = {
    "命中",
    "伤害",
    "防御",
    "速度",
    "法伤",
    "法防",
    "体质",
    "魔力",
    "力量",
    "耐力",
    "敏捷",
    "潜力"
  }
  local nsf = require("SDL.图像")(367, 202)
  字体18["置颜色"](字体18, 39, 53, 81)
  if nsf["渲染开始"](nsf) then
    local pyx = 0
    local pyy = 0
    for i = 1, 12 do
      if i > 6 then
        pyx = 168
        pyy = -192
      end
      字体18["取图像"](字体18, 角色信息[类型[i]])["置混合"](字体18["取图像"](字体18, 角色信息[类型[i]]), 0)["显示"](字体18["取图像"](字体18, 角色信息[类型[i]])["置混合"](字体18["取图像"](字体18, 角色信息[类型[i]]), 0), 82 + pyx, 11 + pyy + (i - 1) * 32)
    end
    nsf["渲染结束"](nsf)
  end
  self.数据 = nsf["到精灵"](nsf)
end
function 普通属性:显示(x, y)
  if self.数据 then
    self.数据["显示"](self.数据, x, y)
  end
end
local 加点按钮 = 普通属性["创建我的按钮"](普通属性, __res:getPNGCC(1, 626, 28, 58, 34, true), "加点按钮", 298, 162, "加点")
function 加点按钮:左键弹起(x, y, msg)
  __UI界面["窗口层"]["人物加点"]["打开"](__UI界面["窗口层"]["人物加点"])
end
local 更多属性 = 属性["创建控件"](属性, "更多属性", 0, 158, 367, 202)
function 更多属性:初始化()
  self:置可见(false)
end
local 更多属性列表 = 更多属性["创建列表"](更多属性, "更多属性列表", 10, 5, 357, 192)
function 更多属性列表:初始化()
  self:置文字(字体18)
  self.选中精灵 = nil
  self.焦点精灵 = nil
  self.行高度 = self.行高度 + 9
end
function 更多属性列表:重置()
  self.清空(self)
  local sxxx = {
    "灵力",
    "物理暴击等级",
    "穿刺等级",
    "狂暴等级",
    "法术暴击等级",
    "法术伤害结果",
    "封印命中等级",
    "固定伤害",
    "治疗能力",
    "抗物理暴击等级",
    "格挡值",
    "抗法术暴击等级",
    "抵抗封印等级",
    "气血回复效果",
    "躲避"
  }
  self:置颜色(255, 255, 255)
  for i = 1, #sxxx do
    self.添加(self)
    local nsf = require("SDL.图像")(340, 23)
    if nsf["渲染开始"](nsf) then
      local lssj = 取输入背景(0, 0, 150, 23)
      lssj["显示"](lssj, 148, 0)
      字体18["置颜色"](字体18, 255, 255, 255)
      字体18["取图像"](字体18, sxxx[i])["置混合"](字体18["取图像"](字体18, sxxx[i]), 0)["显示"](字体18["取图像"](字体18, sxxx[i])["置混合"](字体18["取图像"](字体18, sxxx[i]), 0), 0, 0)
      字体18["置颜色"](字体18, 39, 53, 81)
      if sxxx[i] == "物理暴击等级" or sxxx[i] == "法术暴击等级" then
				字体18:取图像(角色信息[sxxx[i]].."("..string.format("%.2f",角色信息[sxxx[i]]*10/角色信息.等级).."%)"):显示(159, 0)
        -- 字体18:取图像(string.format("%s(+%s%s", 角色信息[sxxx[i]], 角色信息[sxxx[i]] * 0.05, "%)")):显示(159, 0)
			elseif sxxx[i] == "封印命中等级" then
				字体18:取图像(角色信息[sxxx[i]].."("..string.format("%.2f",角色信息[sxxx[i]]*10/(角色信息.等级+25)).."%)"):显示(159, 0)
			elseif sxxx[i] == "穿刺等级" then
				字体18:取图像(角色信息[sxxx[i]].."("..string.format("%.2f",角色信息[sxxx[i]]*0.0017).."%)"):显示(159, 0)
			elseif sxxx[i] == "狂暴等级" then
				字体18:取图像(角色信息[sxxx[i]].."("..string.format("%.2f",角色信息[sxxx[i]]*0.005).."%)"):显示(159, 0)
      else
        字体18:取图像(角色信息[sxxx[i]]):显示(159, 0)
      end

      -- 字体18:取图像(string.format("%s(+%s%s", 角色信息[sxxx[i]], 角色信息[sxxx[i]] * 0.05, "%)")):显示(159, 0)
      nsf["渲染结束"](nsf)
    end
    self.子控件[i]["置精灵"](self.子控件[i], nsf["到精灵"](nsf))
  end
end
local 信息 = 人物属性["人物"]["创建控件"](人物属性["人物"], "信息", 326, 105, 366, 355)
function 信息:初始化()
  local nsf = require("SDL.图像")(366, 355)
  if nsf["渲染开始"](nsf) then
    local lssj = 取属性背景(0, 0, 366, 355, true)
    lssj["显示"](lssj, 0, 0)
    lssj = 取输入背景(0, 0, 160, 23)
    字体18["置颜色"](字体18, 255, 255, 255)
    local 类型 = {
      "I D",
      "称谓",
      "帮派",
      "人气",
      "帮贡",
      "门贡",
      "活力",
      "体力"
    }
    for i = 1, #类型 do
      lssj["显示"](lssj, 76, 12 + (i - 1) * 43)
      字体18["取图像"](字体18, 类型[i])["显示"](字体18["取图像"](字体18, 类型[i]), 25, 12 + (i - 1) * 43)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 信息:重置()
  local nsf = require("SDL.图像")(366, 355)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, 39, 53, 81)
    local 类型 = {
      "数字id",
      "当前称谓",
      "BPMC",
      "人气",
      "BG",
      "门贡",
      "活力",
      "体力"
    }
    for i = 1, #类型 do
      if 角色信息[类型[i]] and 角色信息[类型[i]] ~= "" then
        字体18["取图像"](字体18, 角色信息[类型[i]])["显示"](字体18["取图像"](字体18, 角色信息[类型[i]]), 85, 13 + (i - 1) * 43)
      end
    end
    nsf["渲染结束"](nsf)
  end
  self.数据 = nsf["到精灵"](nsf)
end
function 信息:显示(x, y)
  if self.数据 then
    self.数据["显示"](self.数据, x, y)
  end
end
local 称谓按钮 = 信息["创建我的按钮"](信息, __res:getPNGCC(2, 493, 765, 118, 35, true), "称谓按钮", 253-10, 49, "更换称谓")
function 称谓按钮:左键弹起(x, y, msg)
  __UI界面["窗口层"]["人物称谓"]["打开"](__UI界面["窗口层"]["人物称谓"]) --暂未开放
end
local 技能 = 人物属性["创建控件"](人物属性, "技能", 0, 0, 773, 483)
function 技能:初始化()
  local nsf = require("SDL.图像")(773, 483)
  if nsf["渲染开始"](nsf) then
    取属性背景(0, 0, 687, 300, true)["显示"](取属性背景(0, 0, 687, 300, true), 17, 108)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 技能:重置()
  self:置可见(true)
  人物属性["人物"]["置可见"](人物属性["人物"], false)
  人物属性["修炼"]["置可见"](人物属性["修炼"], false)
  技能["师门技能"]["重置"](技能["师门技能"])
end
local 师门技能按钮 = 技能["创建我的单选按钮"](技能, __res:getPNGCC(1, 401, 65, 175, 43, true)["拉伸"](__res:getPNGCC(1, 401, 65, 175, 43, true), 125, 35), __res:getPNGCC(1, 963, 495, 175, 43, true)["拉伸"](__res:getPNGCC(1, 963, 495, 175, 43, true), 125, 35), "师门技能按钮", 14, 61, "师门技能")
function 师门技能按钮:左键按下(消息, x, y)
  技能["师门技能"]["重置"](技能["师门技能"])
end
local 辅助技能按钮 = 技能["创建我的单选按钮"](技能, __res:getPNGCC(1, 401, 65, 175, 43, true)["拉伸"](__res:getPNGCC(1, 401, 65, 175, 43, true), 125, 35), __res:getPNGCC(1, 963, 495, 175, 43, true)["拉伸"](__res:getPNGCC(1, 963, 495, 175, 43, true), 125, 35), "辅助技能按钮", 149, 61, "辅助技能")
function 辅助技能按钮:左键按下(消息, x, y)
  技能["辅助技能"]["重置"](技能["辅助技能"])
end
local 剧情技能按钮 = 技能["创建我的单选按钮"](技能, __res:getPNGCC(1, 401, 65, 175, 43, true)["拉伸"](__res:getPNGCC(1, 401, 65, 175, 43, true), 125, 35), __res:getPNGCC(1, 963, 495, 175, 43, true)["拉伸"](__res:getPNGCC(1, 963, 495, 175, 43, true), 125, 35), "剧情技能按钮", 284, 61, "剧情技能")
function 剧情技能按钮:左键按下(消息, x, y)
  技能["剧情技能"]["重置"](技能["剧情技能"])
end
local 经脉流派按钮 = 技能["创建我的按钮"](技能, __res:getPNGCC(1, 401, 65, 175, 43, true)["拉伸"](__res:getPNGCC(1, 401, 65, 175, 43, true), 125, 35), "经脉流派按钮", 419, 61, "经脉流派")
function 经脉流派按钮:左键弹起(x, y, msg)
  __UI界面["窗口层"]["经脉流派"]["打开"](__UI界面["窗口层"]["经脉流派"])
end
local 师门技能 = 技能["创建控件"](技能, "师门技能", 17, 108, 700, 372)
function 师门技能:初始化()
  local nsf = require("SDL.图像")(688, 372)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(2, 794, 575, 295, 291)["显示"](__res:getPNGCC(2, 794, 575, 295, 291), 7, 7)
    __res:getPNGCC(2, 795, 885, 373, 115)["显示"](__res:getPNGCC(2, 795, 885, 373, 115), 308, 7)
    字体18["置颜色"](字体18, 255, 255, 255)
    字体18["取图像"](字体18, "特")["显示"](字体18["取图像"](字体18, "特"), 5, 318)
    字体18["取图像"](字体18, "技")["显示"](字体18["取图像"](字体18, "技"), 5, 337)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self:置可见(true)
end
function 师门技能:重置()
  人物属性["技能"]["师门技能"]["置可见"](人物属性["技能"]["师门技能"], true)
  人物属性["技能"]["辅助技能"]["置可见"](人物属性["技能"]["辅助技能"], false)
  人物属性["技能"]["剧情技能"]["置可见"](人物属性["技能"]["剧情技能"], false)
  人物属性["技能"]["师门技能按钮"]["置选中"](人物属性["技能"]["师门技能按钮"], true)
  人物属性["选中技能"] = nil
  人物属性["技能"]["师门技能"]["师门技能网格"]["置技能"](人物属性["技能"]["师门技能"]["师门技能网格"], 角色信息["师门技能"])
  人物属性["技能"]["师门技能"]["特殊技能网格"]["置技能"](人物属性["技能"]["师门技能"]["特殊技能网格"], 角色信息["特殊技能"])
end
local 师门技能网格 = 师门技能["创建网格"](师门技能, "师门技能网格", 0, 0, 300, 300)
function 师门技能网格:初始化()
  self:创建格子(60, 60, 5, 5, 1, 7)
end
function 师门技能网格:左键弹起(x, y, a, b, msg)
  if not self.子控件[a]._spr["模型"] then
    return
  end
  师门技能["技能文本"]["清空"](师门技能["技能文本"])
  师门技能["技能文本"]["置文本"](师门技能["技能文本"], "#Y" .. 角色信息["师门技能"][a]["名称"])
  师门技能["技能文本"]["置文本"](师门技能["技能文本"], "#K" .. 师门技能["师门技能网格"]["子控件"][a]._spr["数据"][1])
  师门技能["包含技能网格"]["置技能"](师门技能["包含技能网格"], 角色信息["师门技能"][a]["包含技能"])
  if 人物属性["选中技能"] then
    self.子控件[人物属性["选中技能"]]._spr["确定"] = nil
  end
  人物属性["选中技能"] = a
  self.子控件[a]._spr["确定"] = true
end
function 师门技能网格:置技能(数据)
  local 坐标 = {
    {32, 70},
    {32, 174},
    {125, 17},
    {125, 120},
    {123, 225},
    {219, 70},
    {219, 174}
  }
  for i = 1, #师门技能网格["子控件"] do
    local lssj = __技能格子["创建"]()
    lssj["置数据"](lssj, 数据[i], 55, 55)
    师门技能网格["子控件"][i]["置精灵"](师门技能网格["子控件"][i], lssj)
    师门技能网格["子控件"][i]["置坐标"](师门技能网格["子控件"][i], 坐标[i][1], 坐标[i][2])
  end
end
local 技能文本 = 师门技能["创建文本"](师门技能, "技能文本", 316, 20, 355, 100)
function 技能文本:初始化()
end
local 包含技能网格 = 师门技能["创建网格"](师门技能, "包含技能网格", 308, 127, 370, 166)
function 包含技能网格:初始化()
  self:创建格子(178, 64, 10, 10, 8, 2)
end
function 包含技能网格:左键弹起(x, y, a, b, msg)
  if not self.子控件[a] or not self.子控件[a]._spr or  not self.子控件[a]._spr["模型"] then
    return
  end
  self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
end
function 包含技能网格:置技能(数据)
  for i = 1, #包含技能网格["子控件"] do
    local lssj = __技能格子2["创建"]()
    lssj["门派"] = 角色信息["门派"]
    lssj["置数据"](lssj, 数据[i], 5, 5, 55, 55, 178, 64, true)
    包含技能网格["子控件"][i]["置精灵"](包含技能网格["子控件"][i], lssj)
  end
end
local 特殊技能网格 = 师门技能["创建网格"](师门技能, "特殊技能网格", 35, 310, 385, 56)
function 特殊技能网格:初始化()
  self:创建格子(55, 55, 0, 10, 1, 6)
end
function 特殊技能网格:左键弹起(x, y, a, b, msg)
  if not self.子控件[a]._spr["模型"] then
    return
  end
end
function 特殊技能网格:置技能(数据)
  for i = 1, #特殊技能网格["子控件"] do
    local lssj = __技能格子3["创建"]()
    lssj["门派"] = "特技"
    lssj["置数据"](lssj, 数据[i], 55, 55)
    特殊技能网格["子控件"][i]["置精灵"](特殊技能网格["子控件"][i], lssj)
  end
end
local 特效按钮 = 师门技能["创建我的按钮"](师门技能, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 66, 41), "特效按钮", 432, 319, "特效")
function 特效按钮:左键弹起(x, y, msg)
end
local 战斗设置按钮 = 师门技能["创建我的按钮"](师门技能, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 114, 41), "战斗设置按钮", 501, 319, "战斗设置")
function 战斗设置按钮:左键弹起(x, y, msg)
end
local 使用按钮 = 师门技能["创建我的按钮"](师门技能, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 66, 41), "使用按钮", 619, 319, "使用")
function 使用按钮:左键弹起(x, y, msg)
end
local 辅助技能 = 技能["创建控件"](技能, "辅助技能", 17, 108, 700, 372)
function 辅助技能:初始化()
  local nsf = require("SDL.图像")(700, 372)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 437, 290, true)["显示"](取白色背景(0, 0, 437, 290, true), 7, 4)
    取白色背景(0, 0, 233, 290, true)["显示"](取白色背景(0, 0, 233, 290, true), 447, 4)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 辅助技能:重置()
  人物属性["技能"]["师门技能"]["置可见"](人物属性["技能"]["师门技能"], false)
  人物属性["技能"]["剧情技能"]["置可见"](人物属性["技能"]["剧情技能"], false)
  人物属性["技能"]["辅助技能"]["置可见"](人物属性["技能"]["辅助技能"], true)
  人物属性["技能"]["辅助技能按钮"]["置选中"](人物属性["技能"]["辅助技能按钮"], true)
  人物属性["选中技能"] = nil
  人物属性.生活选中 = nil
  人物属性["技能"]["辅助技能"]["辅助技能网格"]["置技能"](人物属性["技能"]["辅助技能"]["辅助技能网格"], 角色信息["辅助技能"])
end
local 辅助技能网格 = 辅助技能["创建网格"](辅助技能, "辅助技能网格", 20, 10, 437, 275)
function 辅助技能网格:初始化()
  self:创建格子(190, 61, 10, 10, 10, 2, true)
end
function 辅助技能网格:左键弹起(x, y, a, b, msg)
  if not self.子控件[a]._spr["模型"] then
    return
  end
  辅助技能["技能文本"]["清空"](辅助技能["技能文本"])
  辅助技能["技能文本"]["置文本"](辅助技能["技能文本"], "#R" .. 角色信息["辅助技能"][a]["名称"])
  辅助技能["技能文本"]["置文本"](辅助技能["技能文本"], "#K" .. 辅助技能["辅助技能网格"]["子控件"][a]._spr["数据"][1])
  local 临时消耗 = 生活技能消耗(角色信息["辅助技能"][a].等级 + 1,角色信息["辅助技能"][a].名称)--生活技能消耗(角色信息["辅助技能"][a]["等级"] + 1) --
  辅助技能["技能文本"]["置文本"](辅助技能["技能文本"], "#Y/学习消耗：" .. 临时消耗["经验"] .. "点人物经验、" .. 临时消耗["帮贡"] .. "点帮贡、" .. 临时消耗["需求"] .. "点需要帮贡、" .. 临时消耗["金钱"] .. "两银子#R/（>=40级的生活技能必须在加入帮派后才可学习）")
  if 人物属性["生活选中"] then
    self.子控件[人物属性["生活选中"]]._spr["确定"] = nil
  end
  人物属性["生活选中"] = a
  self.子控件[a]._spr["确定"] = true
end
function 辅助技能网格:置技能(数据)
  for i = 1, #辅助技能网格["子控件"] do
    local lssj = __技能格子2["创建"]()
    lssj["门派"] = "生活"
    lssj["置数据"](lssj, 数据[i], 2, 2, 55, 55, 190, 61, true, true)
    辅助技能网格["子控件"][i]["置精灵"](辅助技能网格["子控件"][i], lssj)
    if 人物属性["生活选中"] and 人物属性["生活选中"]==i then
      self.子控件[i]._spr["确定"] = true
    end
  end
end
local 技能文本 = 辅助技能["创建文本"](辅助技能, "技能文本", 448+11, 16, 219, 270)
function 技能文本:初始化()
end
local 烹饪按钮 = 辅助技能["创建我的按钮"](辅助技能, __res:getPNGCC(3, 2, 507, 124, 41, true), "烹饪按钮", 0, 316, "烹饪")
function 烹饪按钮:左键弹起(x, y, msg)
  发送数据(3713)
end
local 炼药按钮 = 辅助技能["创建我的按钮"](辅助技能, __res:getPNGCC(3, 2, 507, 124, 41, true), "炼药按钮", 140, 316, "炼药")
function 炼药按钮:左键弹起(x, y, msg)
  发送数据(3714)
end
local 摆摊按钮 = 辅助技能["创建我的按钮"](辅助技能, __res:getPNGCC(3, 2, 507, 124, 41, true), "摆摊按钮", 280, 316, "摆摊")
function 摆摊按钮:左键弹起(x, y, msg)
  发送数据(3720)
end
local 学习按钮 = 辅助技能["创建我的按钮"](辅助技能, __res:getPNGCC(3, 2, 507, 124, 41, true), "学习按钮", 420, 316, "学习")
function 学习按钮:左键弹起(x, y, msg)
  if 人物属性["生活选中"] then
    发送数据(3712, {
      ["序列"] = 人物属性["生活选中"]
    })
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y/请先选中要学习的生活技能")
  end
end
-- local 辅助使用按钮 = 辅助技能["创建我的按钮"](辅助技能, __res:getPNGCC(3, 2, 507, 124, 41, true), "辅助使用按钮", 560, 316, "团队")
-- function 辅助使用按钮:左键弹起(x, y, msg)
--   if 人物属性["选中技能"] then
--     发送数据(72, {
--       ["名称"] = 角色信息["辅助技能"][人物属性["选中技能"]]["名称"],
--       ["等级"] = 角色信息["辅助技能"][人物属性["选中技能"]]["等级"]
--     })
--   else
--     __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请先选中要使用的生活技能")
--   end
-- end
local 剧情技能 = 技能["创建控件"](技能, "剧情技能", 17, 108, 700, 372)
function 剧情技能:初始化()
  local nsf = require("SDL.图像")(700, 372)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 437, 290, true)["显示"](取白色背景(0, 0, 437, 290, true), 7, 4)
    取白色背景(0, 0, 233, 290, true)["显示"](取白色背景(0, 0, 233, 290, true), 447, 4)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 剧情技能:重置()
  人物属性["技能"]["师门技能"]["置可见"](人物属性["技能"]["师门技能"], false)
  人物属性["技能"]["辅助技能"]["置可见"](人物属性["技能"]["辅助技能"], false)
  人物属性["技能"]["剧情技能"]["置可见"](人物属性["技能"]["剧情技能"], true)
  人物属性["技能"]["剧情技能按钮"]["置选中"](人物属性["技能"]["剧情技能按钮"], true)
  人物属性["选中技能"] = nil
  人物属性["技能"]["剧情技能"]["剧情技能网格"]["置技能"](人物属性["技能"]["剧情技能"]["剧情技能网格"], 角色信息["剧情技能"])
end
local 剧情技能网格 = 剧情技能["创建网格"](剧情技能, "剧情技能网格", 20, 10, 437, 275)
function 剧情技能网格:初始化()
  self:创建格子(190, 61, 10, 10, 10, 2, true)
end
function 剧情技能网格:左键弹起(x, y, a, b, msg)
  if not self.子控件[a]._spr["模型"] then
    return
  end
  辅助技能["技能文本"]["清空"](辅助技能["技能文本"])
  辅助技能["技能文本"]["置文本"](辅助技能["技能文本"], "#R" .. 角色信息["剧情技能"][a]["名称"])
  辅助技能["技能文本"]["置文本"](辅助技能["技能文本"], "#K" .. 剧情技能["剧情技能网格"]["子控件"][a]._spr["数据"][1])
  if 人物属性["选中技能"] then
    self.子控件[人物属性["选中技能"]]._spr["确定"] = nil
  end
  人物属性["选中技能"] = a
  self.子控件[a]._spr["确定"] = true
end
function 剧情技能网格:置技能(数据)
  for i = 1, #剧情技能网格["子控件"] do
    local lssj = __技能格子2["创建"]()
    lssj["门派"] = "剧情"
    lssj["置数据"](lssj, 数据[i], 2, 2, 55, 55, 190, 61, true, true)
    剧情技能网格["子控件"][i]["置精灵"](剧情技能网格["子控件"][i], lssj)
  end
end
local 技能文本 = 剧情技能["创建文本"](剧情技能, "技能文本", 448, 10, 219, 270)
function 技能文本:初始化()
end
local 修炼 = 人物属性["创建控件"](人物属性, "修炼", 0, 0, 773, 483)
function 修炼:初始化()
  local nsf = require("SDL.图像")(420, 483)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 973, 713, 43, 323)["显示"](__res:getPNGCC(3, 973, 713, 43, 323), 15, 116)
    __res:getPNGCC(3, 1028, 714, 40, 255)["显示"](__res:getPNGCC(3, 1028, 714, 40, 255), 375, 116)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.人物修 = nil
  self.bb修 = nil
end
function 修炼:显示(x, y)
  字体18["显示"](字体18, x + 60, y + 60, "角色自身修炼  当前：" .. 角色信息["修炼"]["当前"])
  字体18["显示"](字体18, x + 420, y + 60, "召唤兽控制修炼  当前：" .. 角色信息["bb修炼"]["当前"])
end
function 修炼:重置()
  self:置可见(true)
  人物属性["人物"]["置可见"](人物属性["人物"], false)
  人物属性["技能"]["置可见"](人物属性["技能"], false)
  修炼["自身修炼列表"]["重置"](修炼["自身修炼列表"])
  修炼["召唤兽控制列表"]["重置"](修炼["召唤兽控制列表"])
  self.人物修 = nil
  self.bb修 = nil
end
local 自身修炼列表 = 修炼["创建列表"](修炼, "自身修炼列表", 58, 117, 295, 335)
function 自身修炼列表:初始化()
  self:置文字(字体18)
  self.行高度 = 40
  self.行间距 = 31
  self.选中精灵 = require('SDL.精灵')(0, 0, 0, self.宽度, 0):置颜色(255, 0, 240, 128)
end
function 自身修炼列表:重置()
  self.清空(self)
  local sxxx = {
    "攻击修炼",
    "防御修炼",
    "法术修炼",
    "抗法修炼",
    "猎术修炼"
  }
  self:置颜色(255, 255, 255)
  local lssj3 = __res:getPNGCC(1, 400, 127, 305, 18)["拉伸"](__res:getPNGCC(1, 400, 127, 305, 18), 290, 18)
  local lssj4 = __res:getPNGCC(1, 401, 145, 305, 16)["拉伸"](__res:getPNGCC(1, 401, 145, 305, 16), 288, 16)
  for i = 1, #sxxx do
    self.添加(self)
    local nsf = require("SDL.图像")(295, 40)
    if nsf["渲染开始"](nsf) then
      local lssj = 计算修炼等级经验(角色信息["修炼"][sxxx[i]][1], 角色信息["修炼"][sxxx[i]][3])
      local lssj2 = 角色信息["修炼"][sxxx[i]][2] / lssj
      if lssj2 > 1 then
        lssj2 = 1
      end
      lssj3["显示"](lssj3, 0, 22)
      lssj4["平铺"](lssj4, math.floor(288 * lssj2), 16)["显示"](lssj4["平铺"](lssj4, math.floor(288 * lssj2), 16), 1, 23)
      字体18["置颜色"](字体18, 255, 255, 255)
      字体18["取图像"](字体18, string.format("%s %s/%s", sxxx[i], 角色信息["修炼"][sxxx[i]][1], 角色信息["修炼"][sxxx[i]][3]))["置混合"](字体18["取图像"](字体18, string.format("%s %s/%s", sxxx[i], 角色信息["修炼"][sxxx[i]][1], 角色信息["修炼"][sxxx[i]][3])), 0)["显示"](字体18["取图像"](字体18, string.format("%s %s/%s", sxxx[i], 角色信息["修炼"][sxxx[i]][1], 角色信息["修炼"][sxxx[i]][3]))["置混合"](字体18["取图像"](字体18, string.format("%s %s/%s", sxxx[i], 角色信息["修炼"][sxxx[i]][1], 角色信息["修炼"][sxxx[i]][3])), 0), 0, 0)
      字体18["置颜色"](字体18, 39, 53, 81)
      字体18["取图像"](字体18, string.format("%s/%s", 角色信息["修炼"][sxxx[i]][2], lssj))["显示"](字体18["取图像"](字体18, string.format("%s/%s", 角色信息["修炼"][sxxx[i]][2], lssj)), 124, 22)
      nsf["渲染结束"](nsf)
    end
    self.子控件[i]["置精灵"](self.子控件[i], nsf["到精灵"](nsf))
  end
end
function 自身修炼列表:左键弹起(x, y, i, item, msg)
  local sxxx = {
    "攻击修炼",
    "防御修炼",
    "法术修炼",
    "抗法修炼",
    "猎术修炼"
  }
  修炼["人物修"] = sxxx[i]
end
local 召唤兽控制列表 = 修炼["创建列表"](修炼, "召唤兽控制列表", 417, 117, 295, 335)
function 召唤兽控制列表:初始化()
  self:置文字(字体18)
  self.行高度 = 40
  self.行间距 = 31
  self.选中精灵 = require('SDL.精灵')(0, 0, 0, self.宽度, 0):置颜色(1,255,1, 128)
end
function 召唤兽控制列表:重置()
  self.清空(self)
  local sxxx = {
    "攻击控制力",
    "防御控制力",
    "法术控制力",
    "抗法控制力"
  }
  self:置颜色(255, 255, 255)
  local lssj3 = __res:getPNGCC(1, 400, 127, 305, 18)["拉伸"](__res:getPNGCC(1, 400, 127, 305, 18), 290, 18)
  local lssj4 = __res:getPNGCC(1, 401, 145, 305, 16)["拉伸"](__res:getPNGCC(1, 401, 145, 305, 16), 288, 16)
  for i = 1, #sxxx do
    self.添加(self)
    local nsf = require("SDL.图像")(295, 40)
    if nsf["渲染开始"](nsf) then
      local lssj = 计算修炼等级经验(角色信息["bb修炼"][sxxx[i]][1], 角色信息["bb修炼"][sxxx[i]][3])
      local lssj2 = 角色信息["bb修炼"][sxxx[i]][2] / lssj
      if lssj2 > 1 then
        lssj2 = 1
      end
      lssj3["显示"](lssj3, 0, 22)
      lssj4["平铺"](lssj4, math.floor(288 * lssj2), 16)["显示"](lssj4["平铺"](lssj4, math.floor(288 * lssj2), 16), 1, 23)
      字体18["置颜色"](字体18, 255, 255, 255)
      字体18["取图像"](字体18, string.format("%s %s/%s", sxxx[i], 角色信息["bb修炼"][sxxx[i]][1], 角色信息["bb修炼"][sxxx[i]][3]))["置混合"](字体18["取图像"](字体18, string.format("%s %s/%s", sxxx[i], 角色信息["bb修炼"][sxxx[i]][1], 角色信息["bb修炼"][sxxx[i]][3])), 0)["显示"](字体18["取图像"](字体18, string.format("%s %s/%s", sxxx[i], 角色信息["bb修炼"][sxxx[i]][1], 角色信息["bb修炼"][sxxx[i]][3]))["置混合"](字体18["取图像"](字体18, string.format("%s %s/%s", sxxx[i], 角色信息["bb修炼"][sxxx[i]][1], 角色信息["bb修炼"][sxxx[i]][3])), 0), 0, 0)
      字体18["置颜色"](字体18, 39, 53, 81)
      字体18["取图像"](字体18, string.format("%s/%s", 角色信息["bb修炼"][sxxx[i]][2], lssj))["显示"](字体18["取图像"](字体18, string.format("%s/%s", 角色信息["bb修炼"][sxxx[i]][2], lssj)), 124, 22)
      nsf["渲染结束"](nsf)
    end
    self.子控件[i]["置精灵"](self.子控件[i], nsf["到精灵"](nsf))
  end
end
function 召唤兽控制列表:左键弹起(x, y, i, item, msg)
  local sxxx = {
    "攻击控制力",
    "防御控制力",
    "法术控制力",
    "抗法控制力"
  }
  修炼["bb修"] = sxxx[i]
end
local 设为当前 = 修炼["创建我的按钮"](修炼, __res:getPNGCC(3, 2, 507, 124, 41, true), "设为当前", 377, 400, "设为当前")
function 设为当前:左键弹起(x, y, msg)
  发送数据(14, {
    ["人物"] = 修炼["人物修"],
    bb = 修炼["bb修"]
  })
end
