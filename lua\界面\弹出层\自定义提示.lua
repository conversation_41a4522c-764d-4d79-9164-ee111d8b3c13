

if __手机 then
   __UI弹出.自定义提示 = 界面:创建弹出窗口("自定义提示", 0, 0, 0, 0)
else
   __UI弹出.自定义提示 = 界面:创建提示控件("自定义提示", 0, 0)
end
local 自定义提示 = __UI弹出.自定义提示
function 自定义提示:初始化()
end


function 自定义提示:左键弹起()
  self:置可见(false)
end
function 自定义提示:打开(数据,x,y)
  self:置可见(true)
    self.名称=nil
    self.数据 = 数据
    self.大模型=nil
    self.宽度 = 360
    self.高度 = 140
    self.显示图像=nil
    self.显示文本:置文字(文本字体):清空()
    local _,h = 0,0
    if 数据 and 数据.介绍 then
        _,h =self.显示文本:置文本(数据.介绍)
    end

    if 数据 and 数据.备注 and 数据.备注~="" and 数据.备注~="无" then
        _,h =self.显示文本:置文本(数据.备注)
    end
   
    if 数据 and 数据.名称 then
         self.名称 =数据.名称
    end
    if 数据 and 数据.资源 and  数据.大模型 then
        self.大模型 = __res:取资源动画(数据.资源,数据.大模型,"动画")
    end
    if 数据 and 数据.资源 and 数据.大动画 then
       self.显示图像= __res:取资源动画(self.数据.资源, self.数据.大动画,"图像")
        if self.显示图像.宽度>130 then
            local zz =math.floor((self.显示图像.高度/self.显示图像.宽度)*130) 
            self.显示图像=__res:取资源动画(self.数据.资源, self.数据.大动画,"图像"):拉伸(130,zz)
        elseif self.显示图像.宽度<80 then     
            local zz =math.floor((self.显示图像.高度/self.显示图像.宽度)*80) 
            self.显示图像=__res:取资源动画(self.数据.资源, self.数据.大动画,"图像"):拉伸(80,zz)
        elseif self.显示图像.高度>130 then
            local zz =math.floor((self.显示图像.宽度/self.显示图像.高度)*130) 
            self.显示图像=__res:取资源动画(self.数据.资源, self.数据.大动画,"图像"):拉伸(zz,130)
        elseif self.显示图像.高度<80 then     
                local zz =math.floor((self.显示图像.宽度/self.显示图像.高度)*80) 
                self.显示图像=__res:取资源动画(self.数据.资源, self.数据.大动画,"图像"):拉伸(zz,80)
        end
    end
    self.显示文本:置高度(h)
    if self.高度<h+60 then
        self.高度 = h+60
    end
    if x+self.宽度 >引擎.宽度 then
        x = 引擎.宽度 - self.宽度- 5
    elseif x<0 then
        x = 0
    end
    if y+self.高度 >引擎.高度 then
        y = 引擎.高度 - self.高度- 5
    elseif y<0 then
        y = 0
    end
    self:提示显示(x,y,self.宽度, self.高度)


  
end








function 自定义提示:提示显示(x,y,w,h)
    --local nsf = 取九宫图像(__res:getPNGCC(2, 230, 964, 401, 52),w,h,10,true)
    local nsf = 取九宫图像(__res:取资源动画("dlzy", 0xB5FDF1AC,"图像"),w,h,20,true)
    if nsf:渲染开始() then
        if self.显示图像 then
            self.显示图像:显示((140-self.显示图像.宽度)//2, 15)
        end
        if  self.名称 then
            道具字体:置颜色(252, 252, 8)
            道具字体:取图像(self.名称,252, 252, 8,180):显示(140,20)
        end
      nsf:渲染结束()
    end
    self:置精灵(nsf:到精灵())
    self:置坐标(x, y)
    self:置宽高(w,h)

end





local 显示文本 = 自定义提示:创建文本("显示文本", 140, 50, 210, 100)--x,y,w,h



function 自定义提示:更新(dt)
  if self.大模型 then
     self.大模型:更新(dt)
  end
end
function 自定义提示:显示(x,y)
    if self.大模型 then
      self.大模型:显示(x+70,y+125)
    end
end



