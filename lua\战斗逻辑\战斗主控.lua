local 战斗主控 = class("战斗主控")
local ggf = require("GGE.函数")
local 排序 = function(a, b)
    return a.显示xy.y < b.显示xy.y
end
function 战斗主控:初始化()
    self.背景 = require("SDL.精灵")(0, 0, 0, 引擎.宽度, 引擎.高度):置颜色(20, 20, 80, 180) 
    self.背景圆=__res:取资源动画("dlzy",0x00D17553,"精灵")   -- 0x10092123
    self.背景状态 = 0
    self.进程 = "加载"
    self.加载数量 = 0
    self.战斗单位 = {}
    self.数字图片 = {}-- _tp.战斗文字[5]
    for i=1,10 do
        self.数字图片[i] = __res:取资源动画("dlzy", 0x4EE0010A):取精灵(i)
    end

    self.血条背景 =__res:取资源动画("dlzy", 0x4D0A334C,"精灵")

    if not _tp.观战中 then
        self.请等待 =  __res:取资源动画("ui", 3994489772,"精灵")
    else
        self.请等待 = __res:取资源动画("ui", 4254597813,"精灵")
    end
    self.拼接偏移 = require("GGE.坐标")()
    self.战斗快捷键法术 = nil
    self.宠物战斗快捷键法术 = nil
    self.拼接特效 = {}
    self.间隔等待 = 20
    self.显示排序 = {}
    self.状态显示 = false
    self.状态显示2 = false
    self.回合数 = 0
    self.特殊状态 = {}
    self.显示表 = {}
    self.自动开关=false
    self.连击背景=__res:取资源动画("jszy/jljnsc", 0x00000024,"精灵")
    self.回合计数 = 标题字体:置颜色(255,255,255,255):取精灵("当前回合："..self.回合数)
   


end

function 战斗主控:是否开启自动()
    for n = 1, #self.战斗单位 do
        if self.战斗单位[n].数据.自动战斗 and self.战斗单位[n].类型 == "角色" and
                self.战斗单位[n].数字id == 角色信息.数字id then
                self.自动开关=true
                self.多开数据={}
        end
    end
end

function 战斗主控:加载单位(数据)
    
    self.战斗单位[#self.战斗单位 + 1] = __战斗单位()
    self.战斗单位[#self.战斗单位]:创建单位(数据, self.队伍id,#self.战斗单位)
    if #self.战斗单位 == self.单位总数 then
        if not _tp.观战中 then
            请求服务(5501,{名称=角色信息.名称})
        end
        self:是否开启自动()
    end
end

function 战斗主控:设置命令回合(数据,回合)
        界面层.战斗界面:更新类型(数据)
        self.进程 = "命令"
        self.命令数据 = {
            计时 = os.time(),
            分 = 9,
            秒 = 9
        }
        self.回合数 = 回合
        self.回合计数 = 标题字体:置颜色(255,255,255,255):取精灵("当前回合："..self.回合数)
        self:设置自动()
        self:聊天外框战斗()
end


function 战斗主控:设置重连命令回合(数据,分,秒)
        界面层.战斗界面:更新类型(数据)
        self.进程="命令"
        self.命令数据={计时=os.time(),分=分,秒=秒}
        self:设置自动()

end

function 战斗主控:设置战斗流程(内容)
    self.战斗流程 = 内容
    for n = 1, #self.战斗流程 do
        if self.战斗流程 ~= nil and self.战斗流程[n] ~= nil then
            self.战斗流程[n].执行 = false
            self.战斗流程[n].允许 = false
        end
    end
    if 0 == #self.战斗流程 then
        self.进程 = "等待"
        if false == _tp.观战中 then
            请求服务(__发送流程)
        end
        return
    end
    self.进程 = "计算"
end


function 战斗主控:设置自动()
    if __多开操作  then
         self.多开数据={}
          for k,v in pairs(self.战斗单位) do
                if v.敌我 == 1 then
                    self.多开数据[v.数据.位置] = {}
                    if v.数据.自动指令 then
                        if v.数据.自动指令.类型 == "法术" then
                            self.多开数据[v.数据.位置].参数 = v.数据.自动指令.参数
                        else
                            self.多开数据[v.数据.位置].参数 = v.数据.自动指令.类型
                        end
                    else
                        self.多开数据[v.数据.位置].参数 = "攻击"
                    end
                    self.多开数据[v.数据.位置].编号 = v.编号
                end
        end
        战斗层.多开自动:显示重置()
    elseif 战斗层.战斗自动.是否可见  then
            战斗层.战斗自动:显示重置()
    end
   
end


function 战斗主控:鼠标右键状态(编号)
      if not 编号 or 编号==0 then return end
      local 返回数据={}
      if self.战斗单位[编号] then
          local 单位数据 =self.战斗单位[编号]
          if 单位数据.类型=="角色" and 单位数据.门派 and 单位数据.门派 ~="无" then
              local 门派=单位数据.门派
              if 门派=="凌波城" then
                    if 单位数据.战意 and 单位数据.战意>0 then
                      local 资源= 取状态图标("战意")
                      local 添加数据={
                                图标 = 资源[1],
                                资源 = 资源[2],
                                说明="战意:\n当前层数:"..单位数据.战意
                              }
                        table.insert(返回数据, 添加数据)
                    end
                    if 单位数据.超级战意 and 单位数据.超级战意>0 then
                          local 资源= 取状态图标("超级战意")
                          local 添加数据={
                                  图标 = 资源[1],
                                  资源 = 资源[2],
                                  说明="超级战意:\n当前层数:"..单位数据.超级战意
                                }
                          table.insert(返回数据, 添加数据)
                    end
              elseif 门派=="神木林"  then
                    if 单位数据.经脉流派 =="灵木药宗"  then
                        if (单位数据.灵药红 and 单位数据.灵药红>0) or (单位数据.灵药蓝 and 单位数据.灵药蓝>0)  or (单位数据.灵药黄 and 单位数据.灵药黄>0)  then
                              local 资源= 取状态图标("灵药")
                              local 添加数据={
                                        图标 = 资源[1],
                                        资源 = 资源[2],
                                        }
                              添加数据.说明 ="灵药:\n"
                              if v.灵药红>0 then
                                      添加数据.说明=添加数据.说明.."灵药·红:"..单位数据.灵药红.."\n"
                              end
                              if v.灵药蓝>0 then
                                      添加数据.说明=添加数据.说明.."灵药·蓝:"..单位数据.灵药蓝.."\n"
                              end
                              if v.灵药黄>0 then
                                  添加数据.说明=添加数据.说明.."灵药·黄:"..单位数据.灵药黄.."\n"
                              end
                              table.insert(返回数据, 添加数据)
                          end
                        elseif 单位数据.风灵 and 单位数据.风灵>0   then
                                local 资源= 取状态图标("风灵")
                                local 添加数据={
                                        图标 = 资源[1],
                                        资源 = 资源[2],
                                        说明="风灵:\n当前层数:"..单位数据.风灵,
                                    }
                                table.insert(返回数据,添加数据)
                        end       
              elseif 门派=="方寸山"  and 单位数据.经脉流派 =="五雷正宗" then
                        if 单位数据.符咒 and 单位数据.符咒>0  then
                            local 资源= 取状态图标("符咒")
                            local 添加数据={
                                    图标 = 资源[1],
                                    资源 = 资源[2],
                                    说明="符咒:\n当前层数:"..单位数据.符咒,
                                }
                            table.insert(返回数据, 添加数据)
                        end
                        if (单位数据.雷法崩裂 and 单位数据.雷法崩裂>0) or  (单位数据.雷法震煞 and 单位数据.雷法震煞>0) or 
                            (单位数据.雷法坤伏 and 单位数据.雷法坤伏>0) or  (单位数据.雷法翻天 and 单位数据.雷法翻天>0) or  
                            (单位数据.雷法倒海 and 单位数据.雷法倒海>0 ) then
                                  local 资源= 取状态图标("雷法")
                                  local 添加数据={
                                          图标 = 资源[1],
                                          资源 = 资源[2],
                                      }
                                  添加数据.说明 ="雷法:\n"
                                  if 单位数据.雷法崩裂>0 then
                                    添加数据.说明=添加数据.说明.."崩裂:"..单位数据.雷法崩裂.."  "
                                  end
                                  if 单位数据.雷法震煞>0 then
                                    添加数据.说明=添加数据.说明.."震煞:"..单位数据.雷法震煞.."\n"
                                  end
                                  if 单位数据.雷法坤伏>0 then
                                    添加数据.说明=添加数据.说明.."坤伏:"..单位数据.雷法坤伏.."  "
                                  end
                                  if 单位数据.雷法翻天>0 then
                                    添加数据.说明=添加数据.说明.."翻天:"..单位数据.雷法翻天
                                  end
                                  if 单位数据.雷法倒海>0 then
                                    添加数据.说明=添加数据.说明.."倒海:"..单位数据.雷法倒海
                                  end
                                  table.insert(返回数据, 添加数据)
                        end
              elseif 门派=="普陀山" and 单位数据.经脉流派 =="落伽神女"  then
                            if (单位数据.五行珠金 and 单位数据.五行珠金>0) or (单位数据.五行珠木 and 单位数据.五行珠木>0) or 
                              (单位数据.五行珠水 and 单位数据.五行珠水>0) or (单位数据.五行珠火 and 单位数据.五行珠火>0) or  
                              (单位数据.五行珠土 and 单位数据.五行珠土>0 ) then
                                          local 资源= 取状态图标("五行珠")
                                          local 添加数据={
                                                图标 = 资源[1],
                                                资源 = 资源[2],
                                              }
                                          添加数据.说明 ="当前五行珠:\n"
                                          if 单位数据.五行珠金>0 then
                                              添加数据.说明=添加数据.说明.."金 "
                                          end
                                          if 单位数据.五行珠木>0 then
                                              添加数据.说明=添加数据.说明.."木 "
                                          end
                                          if 单位数据.五行珠水>0 then
                                              添加数据.说明=添加数据.说明.."水 "
                                          end
                                          if 单位数据.五行珠火>0 then
                                              添加数据.说明=添加数据.说明.."火 "
                                          end
                                          if 单位数据.五行珠土>0 then
                                              添加数据.说明=添加数据.说明.."土 "
                                          end
                                          table.insert(返回数据, 添加数据)
                            end
              elseif 门派=="五庄观" and 单位数据.经脉流派 =="万寿真仙" and 单位数据.人参娃娃 and 单位数据.人参娃娃.回合 and 单位数据.人参娃娃.回合>0 then
                                local 资源= 取状态图标("轮回")
                                local 添加数据={
                                      图标 = 资源[1],
                                      资源 = 资源[2],
                                      说明="人参娃娃:\n当前层数:"..单位数据.人参娃娃.层数.."\n剩余回合:"..单位数据.人参娃娃.回合,
                                    }
                                table.insert(返回数据, 添加数据)
              elseif 门派=="大唐官府"and 单位数据.经脉流派 =="无双战神" and 单位数据.剑意  and 单位数据.剑意>0   then
                                local 资源= 取状态图标("剑意")
                                local 添加数据={
                                      图标 = 资源[1],
                                      资源 = 资源[2],
                                      说明="剑意:\n当前层数:"..单位数据.剑意,
                                }
                                table.insert(返回数据, 添加数据)
              end
          end
          if 单位数据.状态特效  then
              for i, n in pairs(单位数据.状态特效) do
                  local 资源= 取状态图标(i)
                  if 资源 and 资源[2] and 资源[1] then
                        local 添加数据={
                              图标 = 资源[1],
                              资源 = 资源[2],
                        }
                        添加数据.说明=i
                        if n.护盾值 and n.护盾值~=0 then
                            添加数据.说明=添加数据.说明.."\n剩余护盾:"..n.护盾值
                        end
                        if n.回合 then
                            添加数据.说明=添加数据.说明.."\n剩余回合:"..n.回合
                        end
                        table.insert(返回数据,添加数据)
                  end
              end
          end
      end

      return 返回数据
end

function 战斗主控:聊天外框战斗()
    if  界面层.聊天控件.聊天窗口 then
          local 返回数据 ={}
          返回数据.回合=self.回合数
          for k,v in pairs(self.战斗单位) do
            if v.敌我 == 1 and v.类型=="角色" then
                  返回数据[v.数据.位置] = {}
                  返回数据[v.数据.位置].名称=v.名称
                  返回数据[v.数据.位置].状态数据={}
                  if v.门派 and v.门派~="无" then
                      if v.门派=="凌波城" then
                          if v.战意 and v.战意>0 then
                              local 资源= 取状态图标("战意")
                              local 添加数据={
                                        图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                        说明="战意:\n当前层数:"..v.战意,
                                        选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                        回合=文本字体:置颜色(255,255,255,255):取精灵(v.战意)
                                      }
                              table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                            end
                            if v.超级战意 and v.超级战意>0 then
                                  local 资源= 取状态图标("超级战意")
                                  local 添加数据={
                                          图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                          说明="超级战意:\n当前层数:"..v.超级战意,
                                          选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                          回合=文本字体:置颜色(255,255,255,255):取精灵(v.超级战意)
                                        }
                                  table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                            end
                      elseif v.门派=="神木林"  then
                              if v.经脉流派 =="灵木药宗"  then
                                    if (v.灵药红 and v.灵药红>0) or (v.灵药蓝 and v.灵药蓝>0)  or (v.灵药黄 and v.灵药黄>0)  then
                                          local 资源= 取状态图标("灵药")
                                          local 添加数据={
                                              图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                              选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                             
                                          }
                                          添加数据.说明 ="灵药:\n"
                                          local 回合 = 0
                                          if v.灵药红>0 then
                                            添加数据.说明=添加数据.说明.."灵药·红:"..v.灵药红.."\n"
                                            回合=回合+1
                                          end
                                          if v.灵药蓝>0 then
                                            添加数据.说明=添加数据.说明.."灵药·蓝:"..v.灵药蓝.."\n"
                                            回合=回合+1
                                          end
                                          if v.灵药黄>0 then
                                              添加数据.说明=添加数据.说明.."灵药·黄:"..v.灵药黄.."\n"
                                              回合=回合+1
                                          end
                                          添加数据.回合=文本字体:置颜色(255,255,255,255):取精灵(回合)
                                          table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                                    end
                              elseif v.风灵  and v.风灵>0   then
                                      local 资源= 取状态图标("风灵")
                                      local 添加数据={
                                              图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                              说明="风灵:\n当前层数:"..v.风灵,
                                              选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                              回合=文本字体:置颜色(255,255,255,255):取精灵(v.风灵)
                                          }
                                      table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                              end       
                      elseif v.门派=="方寸山"  and v.经脉流派 =="五雷正宗" then
                              if v.符咒 and v.符咒>0  then
                                  local 资源= 取状态图标("符咒")
                                  local 添加数据={
                                          图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                          说明="符咒:\n当前层数:"..v.符咒,
                                          选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                          回合=文本字体:置颜色(255,255,255,255):取精灵(v.符咒)
                                      }
                                  table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                              end
                              if (v.雷法崩裂 and v.雷法崩裂>0) or  (v.雷法震煞 and v.雷法震煞>0) or (v.雷法坤伏 and v.雷法坤伏>0) or  (v.雷法翻天 and v.雷法翻天>0) or  (v.雷法倒海 and v.雷法倒海>0 ) then
                                        local 资源= 取状态图标("雷法")
                                        local 添加数据={
                                              图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                              选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                            
                        
                                            }
                                        添加数据.说明 ="雷法:\n"
                                        local 回合 = 0
                                        if v.雷法崩裂>0 then
                                          添加数据.说明=添加数据.说明.."雷法崩裂:"..v.雷法崩裂.."\n"
                                          回合=回合+1
                                        end
                                        if v.雷法震煞>0 then
                                          添加数据.说明=添加数据.说明.."雷法震煞:"..v.雷法震煞.."\n"
                                          回合=回合+1
                                        end
                                        if v.雷法坤伏>0 then
                                          添加数据.说明=添加数据.说明.."雷法坤伏:"..v.雷法坤伏.."\n"
                                          回合=回合+1
                                        end
                                        if v.雷法翻天>0 then
                                          添加数据.说明=添加数据.说明.."雷法翻天:"..v.雷法翻天.."\n"
                                          回合=回合+1
                                        end
                                        if v.雷法倒海>0 then
                                          添加数据.说明=添加数据.说明.."雷法倒海:"..v.雷法倒海.."\n"
                                          回合=回合+1
                                        end
                                        添加数据.回合=文本字体:置颜色(255,255,255,255):取精灵(回合)
                                        table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                              end
                      elseif v.门派=="普陀山" and v.经脉流派 =="落伽神女"  then
                              if (v.五行珠金 and v.五行珠金>0) or  (v.五行珠木 and v.五行珠木>0) or (v.五行珠水 and v.五行珠水>0) or  (v.五行珠火 and v.五行珠火>0) or  (v.五行珠土 and v.五行珠土>0 ) then
                                            local 资源= 取状态图标("五行珠")
                                            local 添加数据={
                                                  图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                                  选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                          
                                                }
                                            添加数据.说明 ="当前五行珠:\n"
                                            local 回合 = 0
                                            if v.五行珠金>0 then
                                              添加数据.说明=添加数据.说明.."金 "
                                              回合=回合+1
                                            end
                                            if v.五行珠木>0 then
                                              添加数据.说明=添加数据.说明.."木 "
                                              回合=回合+1
                                            end
                                            if v.五行珠水>0 then
                                              添加数据.说明=添加数据.说明.."水 "
                                              回合=回合+1
                                            end
                                            if v.五行珠火>0 then
                                              添加数据.说明=添加数据.说明.."火 "
                                              回合=回合+1
                                            end
                                            if v.五行珠土>0 then
                                              添加数据.说明=添加数据.说明.."土 "
                                              回合=回合+1
                                            end
                                            添加数据.回合=文本字体:置颜色(255,255,255,255):取精灵(回合)
                                            table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                              end
                      elseif v.门派=="五庄观" and v.经脉流派 =="万寿真仙" and  v.人参娃娃  and v.人参娃娃.回合 and  v.人参娃娃.回合>0 then
                                      local 资源= 取状态图标("轮回")
                                      local 添加数据={
                                            图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                            说明="人参娃娃:\n当前层数:"..v.人参娃娃.层数.."\n剩余回合:"..v.人参娃娃.回合,
                                            选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                            回合=文本字体:置颜色(255,255,255,255):取精灵(v.人参娃娃.回合)
                                          }
                                      table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                      elseif v.门派=="大唐官府"and v.经脉流派 =="无双战神" and v.剑意  and v.剑意>0   then
                                      local 资源= 取状态图标("剑意")
                                      local 添加数据={
                                        图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                        说明="剑意:\n当前层数:"..v.剑意,
                                        选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                        回合=文本字体:置颜色(255,255,255,255):取精灵(v.剑意)
                                      }
                                      table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                      end
                  end
                  if v.状态特效  then
                      for i, n in pairs(v.状态特效) do
                          local 资源= 取状态图标(i)
                          if 资源 and 资源[2] and 资源[1] then
                                local 添加数据={
                                      图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                      选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                }
                                添加数据.说明=i
                                if n.护盾值 and n.护盾值~=0 then
                                    添加数据.说明=添加数据.说明.."\n剩余护盾:"..n.护盾值
                                end
                                if n.回合 then
                                    添加数据.说明=添加数据.说明.."\n剩余回合:"..n.回合
                                    添加数据.回合=文本字体:置颜色(255,255,255,255):取精灵(n.回合)
                                end
                                table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                          end
                      end
                  end
              end
          end
          界面层.聊天控件.聊天窗口:置状态数据(返回数据)
      end

end

function 战斗主控:设置状态(编号,流程)

  if not 编号 or not self.战斗单位[编号] or not 流程 then
      return
  end
  if 流程.添加状态 then
      if type(流程.添加状态)=="string" then
          self.战斗单位[编号]:增加状态(流程.添加状态)
      elseif type(流程.添加状态)=="table" then
            for k,v in pairs(流程.添加状态) do
                if type(k) =="string" and type(v) =="table" then

                    self.战斗单位[编号]:增加状态(k,v)
                elseif type(k) =="number" and type(v) =="string" then
                      self.战斗单位[编号]:增加状态(v)
                end
            end
      end
  end
  if 流程.取消状态 then
      if type(流程.取消状态)=="string" then
          self.战斗单位[编号]:取消状态(流程.取消状态)
      elseif type(流程.取消状态)=="table" then
            for k,v in pairs(流程.取消状态) do
                if type(v)=="string" then
                    self.战斗单位[编号]:取消状态(v)
                  
                end
            end
      end
  end
  if 流程.增加气血 then
      self:设置气血(编号,流程.增加气血,2)
  end
  if 流程.扣除气血 then
      self:设置气血(编号,流程.扣除气血,1)
  end
  if 流程.多人状态 then
      for k,v in pairs(流程.多人状态) do
          if v.状态 and  self.战斗单位[v.挨打方] and not self.战斗单位[v.挨打方].状态特效[v.状态] then
            if v.特效 then
                self:设置特效(v.挨打方,v.特效)
            end
              self.战斗单位[v.挨打方]:增加状态(v.状态)
          end

      end
  end
  流程.扣除气血 = nil
  流程.增加气血 = nil
  流程.添加状态 = nil
  流程.取消状态 = nil
  流程.多人状态 = nil
end



function 战斗主控:设置特效(编号,特效,法术)
 
  if not 特效 or not 编号 or not self.战斗单位[编号] then return end
  if type(特效)=="string" then
      if 法术 then
          self.战斗单位[编号]:添加法术特效(特效)
      else
          self.战斗单位[编号]:添加攻击特效(特效)
      end
  elseif type(特效)=="table" then
          
          for k,v in pairs(特效) do
            
              if type(v)=="string" then
                  if 法术 then
                      self.战斗单位[编号]:添加法术特效(v)
                  else
                      self.战斗单位[编号]:添加攻击特效(v)
                  end
              end
          end
  end
end




function 战斗主控:设置动作(编号,动作,复原,更新)
  if not 动作 or not 编号 or not self.战斗单位[编号] then return end
  self.战斗单位[编号]:换动作(动作,复原,更新)
end

function 战斗主控:设置攻击帧(编号,动作)
  if not 编号 or not self.战斗单位[编号] then return end
  if 动作 then
      self.战斗单位[编号]:置帧率(动作,0.11)
  else
      self.战斗单位[编号]:置帧率("攻击",0.11)
  end
end

function 战斗主控:设置气血(编号,掉血,类型,护盾,伤势,伤势类型)
    if not 编号 or not self.战斗单位[编号] then return end
    if 掉血 then
        if 类型 then
            self.战斗单位[编号]:设置掉血(掉血,类型,护盾)
        else
            self.战斗单位[编号]:设置掉血(掉血,1,护盾)
        end
    end
    if 伤势 then
        if 伤势类型 then
            self.战斗单位[编号]:设置伤势(伤势,伤势类型)
        else
            self.战斗单位[编号]:设置伤势(伤势,1)
        end
    end
end

function 战斗主控:设置抖动(编号,停止)
  if not 编号 or not self.战斗单位[编号] then return end
  if 停止 then
      self.战斗单位[编号]:停止抖动()
  else
      self.战斗单位[编号]:开始抖动()
  end
end

function 战斗主控:设置击退(编号,死亡)
  if not 编号 or not self.战斗单位[编号] then return end
  self.战斗单位[编号]:开启击退(死亡)
end

function 战斗主控:设置死亡(编号,死亡)
  if not 编号 or not self.战斗单位[编号] then return end
  self.战斗单位[编号]:设置死亡(死亡)
end


function 战斗主控:取状态(编号)
  return 编号 and self.战斗单位[编号] and self.战斗单位[编号]:取状态()
end

function 战斗主控:取进攻完毕(编号)
  return self:取状态(编号) and self.战斗单位[编号].动作~="攻击" and self.战斗单位[编号].动作~="暴击"
end


function 战斗主控:开启移动(编号,目标)
        if self.战斗单位[编号].移动开关 then return end
        local 临时移动 = self.战斗单位[目标]:取移动坐标("挨打", 编号)
        local 距离 = 0
        local 距离2 = 0
        if 1 == self.敌我 then
            距离2 = 40
            距离 = 20
        else
            距离2 = -40
            距离 = -10
        end
        if self.战斗单位[编号].显示xy:取距离(临时移动.x + 距离2, 临时移动.y + 距离) >= 50 then
            self.战斗单位[编号].移动开关 = true
            self.战斗单位[编号].移动坐标 = 临时移动
        end
end

function 战斗主控:中路移动(编号)
    if self.战斗单位[编号].移动开关 then return end
    self.战斗单位[编号].移动开关=true
    self.战斗单位[编号].移动坐标=require("GGE.坐标")(引擎.宽度2, 引擎.高度2) 
end

function 战斗主控:开启返回(编号)
          if self.战斗单位[编号].返回开关 then return end
          self.战斗单位[编号].返回开关=true
          self.战斗单位[编号].移动坐标.x=self.战斗单位[编号].初始xy.x
          self.战斗单位[编号].移动坐标.y=self.战斗单位[编号].初始xy.y
    
end

function 战斗主控:开启分身(编号,挨打)
      self.战斗单位[编号]:关闭影子()
      if 挨打 and 挨打[1] then
          for k,v in pairs(挨打) do
              local 坐标= self.战斗单位[v.挨打方]:取移动坐标("挨打")
              local 移动表={移动坐标=坐标}
              self.战斗单位[编号]:设置影子动画(移动表,k)
          end
          self.战斗单位[编号]:开启影子(#挨打)
      end
end

function 战斗主控:取物理攻击中(编号)
        if not 编号 or not self.战斗单位[编号] then return true end
        return self.战斗单位[编号]:取间隔() >= self.战斗单位[编号]:取中间()+self.战斗单位[编号].攻击帧
end

function 战斗主控:取法术攻击中(编号)
          if not 编号 or not self.战斗单位[编号] then return true end
          return self.战斗单位[编号]:取间隔() >= self.战斗单位[编号]:取中间()
end

function 战斗主控:取停止移动(编号)
          if not 编号 or not self.战斗单位[编号] then return true end
          return  not self.战斗单位[编号].移动开关 and not self.战斗单位[编号].影子移动
end
function 战斗主控:物理特效处理(流程)
  local 返回 = "攻击"
  for k,v in pairs(流程.特效) do
      if v=="力劈华山" or v=="暴击" then
          返回 = "暴击"
      end
  end
  return 返回
end

function 战斗主控:躲避流程处理(编号)
  self.战斗单位[编号]:开启躲避()
  self:设置抖动(编号,1)
  self:设置动作(编号,"待战",nil,true)
end
function 战斗主控:反震反击开始(编号,流程,挨打)
  if self:取进攻完毕(编号) then
      if 流程.反震伤害 then
            self:设置特效(编号,"反震")
            self:设置动作(编号,"挨打",nil,true)
            self:设置气血(编号,流程.反震伤害,1)
            self:设置击退(编号)
            流程.反震伤害 = nil
            return  17
      elseif 流程.反击伤害 then
              for k,v in pairs(挨打) do
                  self:设置攻击帧(v.挨打方)
              end
              return 5
      else
            return  17
      end
  end
  return false
end

function 战斗主控:反击流程开始(编号,挨打)
  local 通过 = #挨打
  for k,v in pairs(挨打) do
      if v.死亡 or v.死亡1 or self:取状态(v.挨打方) then
          if not v.死亡 and not v.死亡1 then
            self:设置动作(v.挨打方,"攻击",true)
          end
          通过 = 通过 - 1
      end
  end
  if 通过<=0 then
      return true
  else
      return false
  end
end

function 战斗主控:反击流程动作(编号,流程,挨打)
  local 通过 = #挨打
  for k,v in pairs(挨打) do
      if v.死亡 or v.死亡1 or self:取物理攻击中(v.挨打方) then
          通过 = 通过 - 1
      end
  end
  if 通过<=0 then
      self:设置特效(编号,"被击中")
      self:设置动作(编号,"挨打",nil,true)
      self:设置气血(编号,流程.反击伤害,1)
      self:设置击退(编号)
      流程.反击伤害 = nil
      return true
  end
  return false
end

function 战斗主控:反击流程结束(挨打)
local 通过 = #挨打
for k,v in pairs(挨打) do
    if self:取进攻完毕(v.挨打方) then
        通过 = 通过 - 1
    end
end
if 通过<=0 then
    return true
else
    return false
end
end




function 战斗主控:保护流程开始(编号,目标,保护)
        self:设置特效(保护,"保护",1)
        self.战斗单位[保护].移动开关=true
        self.战斗单位[保护].移动坐标=self.战斗单位[目标]:取移动坐标("保护") 
        _tp:播放特效音乐(取音效("保护"))
        self.执行流程=11
end

function 战斗主控:保护流程动作(编号,保护,挨打)
          if self:取停止移动(保护) then
              self.战斗单位[保护].方向 = self.战斗单位[保护].初始方向
              self.战斗单位[保护]:换方向(self.战斗单位[保护].初始方向)
              self:设置动作(保护,"待战",nil,true)
              local 动作 = self:物理特效处理(挨打)
              self:设置动作(编号,动作,true)
              self:设置攻击帧(编号,动作)
              self.执行流程 = 12
          end
end






function 战斗主控:保护流程攻击(编号,目标,保护,挨打)
        if self:取物理攻击中(编号) then
            self:设置物理掉血(目标,挨打)
            self:设置特效(保护.编号,"被击中")
            self:设置动作(保护.编号,"挨打",nil,true)
            self:设置气血(保护.编号,保护.伤害,挨打.类型)
            self:设置击退(保护.编号)
            self.执行流程 = 13
         end
 end


  
function 战斗主控:保护流程返回(编号,保护)
        if self:取进攻完毕(编号) then
          self:开启返回(保护)
          self.执行流程 = 14
        end
end
  
function 战斗主控:保护流程结束(保护,流程)
          if self:取状态(保护.编号) then
              self.战斗单位[保护.编号].方向 = self.战斗单位[保护.编号].初始方向
              self.战斗单位[保护.编号]:换方向(self.战斗单位[保护.编号].初始方向)
              self.战斗单位[保护.编号].显示xy.x=self.战斗单位[保护.编号].初始xy.x
              self.战斗单位[保护.编号].显示xy.y=self.战斗单位[保护.编号].初始xy.y
              if 保护.死亡 then
                  self:设置死亡(保护.编号,保护.死亡)
              end
              if 流程.反震伤害 or 流程.反击伤害 then
                  self.执行流程=4
              else
                  self.执行流程=17
              end
          end
  end

function 战斗主控:物理返回流程(编号,流程,挨打)
  if self:取进攻完毕(编号) then
      for k,v in pairs(挨打) do
          if v.死亡 then
              self:设置死亡(v.挨打方,v.死亡)
          end
          if v.死亡1 then
              self:设置死亡(v.挨打方,v.死亡1)
          end
      end
      self:设置气血(编号,流程.减少气血,1)
      if 流程.反击死亡 or 流程.反震死亡 or 流程.返回 then
          if 流程.分身攻击 then
              self.战斗单位[编号].影子返回=true
              return 38
          else
              self:开启返回(编号)
              return 18
          end
      else
          return 20
      end
  end
  return false
end


function 战斗主控:物理结束流程(编号,流程,挨打)
  local 通过 = #挨打
  for k,v in pairs(挨打) do
      if self:取状态(v.挨打方) then
        通过 = 通过 - 1
      end
  end

  if self:取状态(编号) and 通过<=0 then
      self.战斗单位[编号].方向 = self.战斗单位[编号].初始方向
      self.战斗单位[编号]:换方向(self.战斗单位[编号].初始方向)
      if 流程.队友 then
          self.战斗单位[流程.队友].方向 = self.战斗单位[流程.队友].初始方向
          self.战斗单位[流程.队友]:换方向(self.战斗单位[流程.队友].初始方向)
      end
      if 流程.反震死亡 then
            self:设置死亡(编号,流程.反震死亡)
      elseif 流程.反击死亡 then
            self:设置死亡(编号,流程.反击死亡)
      elseif 流程.死亡 then
            self:设置死亡(编号,流程.死亡)
      end
      return true
  end
  return false
end

function 战斗主控:浮空检测(挨打)
  for k,v in pairs(挨打) do
    if v.挨打方 and self.战斗单位[v.挨打方]
      and self.战斗单位[v.挨打方].气血>0
      and self.战斗单位[v.挨打方].状态特效
      and self.战斗单位[v.挨打方].状态特效.浮空 then
        return true
    end
  end
  return false
end





function 战斗主控:施法前检测(挨打)
  for i=#挨打,1,-1 do
        if not 挨打[i].挨打方 or 挨打[i].挨打方==0 or not self.战斗单位[挨打[i].挨打方] then
          table.remove(挨打,i)
        end
  end
end


function 战斗主控:施法流程开始(编号,挨打,流程,法术)
  self:施法前检测(挨打)
  if 流程.全屏动画 then
      self:设置动作(编号,"施法",true)
      self:置全屏技能(流程.全屏动画,self.战斗单位[挨打[1].挨打方])
      if 法术 then
          for k,v in pairs(挨打) do
              self:设置抖动(v.挨打方)
              self.战斗单位[v.挨打方].受击名称 = v.特效[1]
          end
      else
          流程.全屏动画 = nil
      end
      return true
  elseif 流程.先手动画 then
        self:设置动作(编号,"施法",true)
        for k,v in pairs(挨打) do
            self:设置特效(v.挨打方,流程.先手动画,1)
        end
        流程.先手动画 = nil
        return true
  else
      self:设置动作(编号,"施法",true)
      return true
  end
  return false
end

function 战斗主控:施法流程完成(编号,挨打,流程)
local 通过=#挨打
for k,v in pairs(挨打) do
    if self:取状态(v.挨打方) then
          通过 = 通过 - 1
    end
end
if 通过<=0 then
    return true
end
return false
end







function 战斗主控:设置物理掉血(目标,挨打,抖动)
if not 抖动 then
  self:设置抖动(目标,1)
end
self:设置动作(目标,"挨打",nil,true)
self:设置气血(目标,挨打.伤害,挨打.类型,挨打.护盾值,挨打.伤势,挨打.伤势类型)
self:设置击退(目标)
self:设置气血(目标,挨打.伤害1,挨打.类型1)
self:设置状态(目标,挨打)
end


function 战斗主控:设置法术掉血(目标,挨打,抖动)
if not 抖动 then
  self:设置抖动(目标,1)
end
self:设置动作(目标,"挨打",nil,true)
self:设置气血(目标,挨打.伤害,挨打.类型,挨打.护盾值,挨打.伤势,挨打.伤势类型)
self:设置击退(目标,挨打.死亡)
self:设置气血(目标,挨打.伤害1,挨打.类型1)
if 挨打.死亡1 then
  self:设置死亡(目标,挨打.死亡1)
end
self:设置状态(目标,挨打)
end





function 战斗主控:单体物理流程(编号,挨打,流程)
  if self.执行流程==1 then --物理攻击
      if 流程.先手动画 then
          if self:施法流程开始(编号,挨打,流程) then
              self.执行流程=19
          end
      elseif 流程.全屏动画 then
          if self:施法流程开始(编号,挨打,流程) then
              self.执行流程=19
          end
      elseif self.战斗单位[编号].动画.攻击方式==1 then  --判断是否是弓弩
            self:设置动作(编号,"攻击",true)
            self.执行流程=15
      elseif self.战斗单位[编号].模型=="蜈蚣精" then
              self.战斗单位[编号].移动开关=false
              self.执行流程=2
      elseif self:取状态(挨打[1].挨打方) and self:取状态(编号) then --保持原有旧的操作
              self:开启移动(编号,挨打[1].挨打方)
              self.执行流程=2
      end
  elseif self.执行流程==2 and self:取停止移动(编号) then
        if not 流程.保护数据 then
            local 动作 = self:物理特效处理(挨打[1])
            self:设置动作(编号,动作,true)
            if self:浮空检测(挨打) then
                self.战斗单位[编号].攻击偏移={x=-12,y=-70}
            end
            if 挨打[1].死亡 then
               self:设置攻击帧(编号,动作)
            end
            self.执行流程=3
        else
            self.执行流程=10
        end
   elseif self.执行流程==3 and self:取物理攻击中(编号) then
            if 流程.躲避 then
                self:躲避流程处理(挨打[1].挨打方)
                self.执行流程=17
            else
                self:设置特效(挨打[1].挨打方,挨打[1].特效)
                self:设置物理掉血(挨打[1].挨打方,挨打[1])
                self:设置状态(编号,流程)
                if 流程.反震伤害 or 流程.反击伤害 then
                    self.执行流程=4
                else
                    self.执行流程=17
                end
            end
    elseif self.执行流程 == 4  then
            local 结束 = self:反震反击开始(编号,流程,挨打)
            if 结束 then
                self.执行流程 = 结束
            end
    elseif self.执行流程 == 5 and self:反击流程开始(编号,挨打) then
            self.执行流程 = 6
    elseif self.执行流程 == 6 and self:反击流程动作(编号,流程,挨打)then
            self.执行流程 = 7
    elseif self.执行流程 == 7 and self:反击流程结束(挨打) then
            self.执行流程 = 17
    elseif self.执行流程==10 then
            self:保护流程开始(编号,挨打[1].挨打方,流程.保护数据.编号)
    elseif self.执行流程==11 then
            self:保护流程动作(编号,流程.保护数据.编号,挨打[1])
    elseif self.执行流程==12 then
            self:保护流程攻击(编号,挨打[1].挨打方,流程.保护数据,挨打[1])
    elseif self.执行流程==13 then
            self:保护流程返回(编号,流程.保护数据.编号)
    elseif self.执行流程==14 then
            self:保护流程结束(流程.保护数据,流程)
    elseif self.执行流程==15 and self:取物理攻击中(编号) then
            self.战斗单位[挨打[1].挨打方]:设置弓弩(self.战斗单位[编号].显示xy,self.战斗单位[编号].初始方向)
            self.战斗单位[编号].移动开关=false
            self.执行流程 = 16
    elseif self.执行流程== 16 and not self.战斗单位[挨打[1].挨打方].弓弩开关 then
            if not 流程.保护数据 then
                if 流程.躲避 then
                    self:躲避流程处理(挨打[1].挨打方)
                    self.执行流程=17
                else
                    self:设置特效(挨打[1].挨打方,挨打[1].特效)
                    self:设置物理掉血(挨打[1].挨打方,挨打[1])
                    self:设置状态(编号,流程)
                    if 流程.反震伤害 or 流程.反击伤害 then
                        self.执行流程=4
                    else
                        self.执行流程=17
                    end
                end
            else
               self.执行流程=10
            end
    elseif self.执行流程== 17 then
            local 返回 = self:物理返回流程(编号,流程,挨打)
            if 返回 then
                self.执行流程= 返回
            end
    elseif self.执行流程== 18 and self:物理结束流程(编号,流程,挨打) then
              self.执行流程 = 20
    elseif self.执行流程== 19 and self.战斗单位[编号].动作=="待战" then
            self.执行流程= 1

    end
end








function 战斗主控:群体物理流程(编号,挨打,流程)
if self.执行流程==30 then
  if #挨打==0 then
      self.执行流程=20
  else
      for k,v in pairs(挨打) do
          self:设置特效(v.挨打方,v.特效)
          self:设置物理掉血(v.挨打方,v)
      end

  end
  if 流程.反震伤害 or 流程.反击伤害 then
      self.执行流程=4
  else
      self.执行流程=31
  end
elseif self.执行流程==31 and self:施法流程完成(编号,挨打,流程) then
    self:设置状态(编号,流程)
    if 流程.保护数据 then
        for k,v in pairs(流程.保护数据) do
            if type(v)=="table" and v.编号 then
                self.设置气血(v.编号,v.伤害,1)
                self.设置击退(v.编号,v.死亡)
            end
        end
    end
    self.执行流程=20
elseif self.执行流程==32 then
    self:施法前检测(挨打)
    if #挨打 <= 0 then
          self.执行流程=20
          return
    end
    if 流程.分身攻击 then
        if self.战斗单位[编号].影子开关 then
            self.战斗单位[编号].影子移动=false
            self.执行流程=36
        else
           self:开启分身(编号,挨打)
           self.执行流程=35
        end
    else
        self:中路移动(编号)
        self.执行流程=33
    end

elseif self.执行流程==33 and self:取停止移动(编号) then
        self:设置动作(编号,"施法",true)
        self.执行流程=34
elseif self.执行流程==34 and self:取法术攻击中(编号) then
      for k,v in pairs(挨打) do
          self:设置特效(v.挨打方,v.特效)
          self:设置物理掉血(v.挨打方,v)
      end
      if 流程.保护数据 then
          for k,v in pairs(流程.保护数据) do
              if type(v)=="table" and v.编号 then
                self.设置气血(v.编号,v.伤害,1)
                self.设置击退(v.编号,v.死亡)
              end
          end
      end
      self.执行流程=34.1
elseif self.执行流程==34.1 and self:施法流程完成(编号,挨打,流程) then
      self:设置状态(编号,流程)
      if #挨打 <= 0 then
            self.执行流程=20
      else
          if 流程.反震伤害 or 流程.反击伤害 then
              self.执行流程=4
          else
              self.执行流程=17
          end
      end
elseif self.执行流程==35 and self.战斗单位[编号].影子开关 then
      self.战斗单位[编号].影子移动=true
      self.执行流程=36
elseif self.执行流程==36 and not self.战斗单位[编号].影子移动 then
      self:设置动作(编号,"攻击",true)
      if self:浮空检测(挨打) then
          self.战斗单位[编号].攻击偏移={x=-12,y=-70}
      end
      self.执行流程=37
elseif self.执行流程==37 and self:取物理攻击中(编号) then
      for k,v in pairs(挨打) do
          self:设置特效(v.挨打方,v.特效)
          self:设置物理掉血(v.挨打方,v)
      end
      self:设置状态(编号,流程)
      if 流程.保护数据 then
          for k,v in pairs(流程.保护数据) do
              if type(v)=="table" and v.编号 then
                self.设置气血(v.编号,v.伤害,1)
                self.设置击退(v.编号,v.死亡)
              end
          end
      end
      if 流程.反震伤害 or 流程.反击伤害 then
          self.执行流程=4
      else
          self.执行流程=17
      end

elseif self.执行流程== 38 and not self.战斗单位[编号].影子返回 then
      self.战斗单位[编号]:关闭影子()
      self.执行流程=18
end
end


function 战斗主控:法术流程处理(编号,挨打,流程)
  if self.执行流程==21 and self:施法流程开始(编号,挨打,流程,1) then
      self.执行流程=22
  elseif self.执行流程==22 then
          if 流程.全屏动画 then
              self.执行流程=23
              流程.全屏动画 = nil
          elseif self:取法术攻击中(编号) then
                  if #挨打 <= 0 then
                      self.执行流程=20
                      return
                  end
                  self.掉血流程=挨打[1].特效[1]
                  for k,v in pairs(挨打) do
                      self:设置抖动(v.挨打方)
                      self:设置动作(v.挨打方,"待战",nil,true)
                      self:设置特效(v.挨打方,v.特效[1],1)
                      self.战斗单位[v.挨打方].受击名称 = v.特效[1]
                  end
                  self.执行流程=23

          end
  elseif self.执行流程==23 and not self.掉血流程 then
          for k,v in pairs(挨打) do
                v.特效[1]=nil
                self:设置特效(v.挨打方,v.特效)
                self:设置法术掉血(v.挨打方,v) --(编号,目标,挨打,流程,法术,特效,抖动)
          end
          self.执行流程=24
  elseif self.执行流程==24 and self:施法流程完成(编号,挨打,流程) then
          self.执行流程 = 25
  elseif self.执行流程==25 and not self.拼接特效 then
          self:设置状态(编号,流程)
          self.执行流程 = 26
  elseif self.执行流程==26 then
            self:设置气血(编号,流程.减少气血,1)
            if 流程.死亡 then
                self:设置击退(编号,流程.死亡)
            end
            self.执行流程=20
  elseif self.执行流程==27 and self:施法流程开始(编号,挨打,流程) then
          self.执行流程 = 28
  elseif self.执行流程==28 and self:取法术攻击中(编号) then
          for k,v in pairs(挨打) do
              self:设置特效(v.挨打方,v.特效[1],1)
              --self.掉血流程=v.特效[1]
          end
          self.执行流程 = 29
  elseif self.执行流程==29 and not self.掉血流程 then
          for k,v in pairs(挨打) do
              v.特效[1]=nil
              self:设置特效(v.挨打方,v.特效,1)
              self:设置气血(v.挨打方,v.伤害,v.类型,v.护盾值,v.伤势,v.伤势类型)
              self:设置气血(v.挨打方,v.伤害1,v.类型1)
              self:设置状态(v.挨打方,v)
              if v.死亡 then
                  self:设置动作(v.挨打方,"挨打",nil,true)
                  self:设置击退(v.挨打方,v.死亡)
              end
              if v.死亡1 then
                  self:设置死亡(v.挨打方,v.死亡1)
              end
              if v.复活 then
                  self:设置动作(v.挨打方,"待战")
                  self.战斗单位[v.挨打方].停止更新=false
              end
          end
          if 流程.受益方 then
            for k,v in pairs(流程.受益方) do
                self:设置气血(v.受益方,v.伤害,2)
            end
          end
          self.执行流程 = 24
  end

end


function 战斗主控:结束计算()
  for k,v in pairs(self.战斗单位) do
      if not v:取状态() or not v:取法术状态() then
        return
      end
  end
  table.remove(self.战斗流程,1)
  self.执行流程=0
  self.进程 = "计算"
  if #self.战斗流程==0 then
      self.进程="等待"
      请求服务(__发送流程)
  end
end







function 战斗主控:流程更新()
            if not self.战斗流程[1] then return end
            if not self.执行流程 then return  end
            if self.执行流程 == 20 then
                self:结束计算()
            elseif self.执行流程>=1 and self.执行流程 <= 19 then
                  self:单体物理流程(self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1])

            elseif self.执行流程>=21 and self.执行流程 <= 29 then
                    self:法术流程处理(self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1])
            elseif self.执行流程>=30 and self.执行流程 <= 38 then
                    self:群体物理流程(self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1])
            elseif self.执行流程==39 then
                  self.战斗单位[self.战斗流程[1].挨打方.挨打方]= __战斗单位()
                  self.战斗单位[self.战斗流程[1].挨打方.挨打方]:创建单位(self.战斗流程[1].挨打方.数据,self.战斗流程[1].挨打方.队伍,self.战斗流程[1].挨打方.挨打方)
                  if self.战斗流程[1].提示 then
                      self:设置动作(self.战斗流程[1].攻击方,"施法",true)
                  end
                  self.执行流程 = 39.1
            elseif self.执行流程==39.1 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
                   self.战斗单位[self.战斗流程[1].挨打方.挨打方]= __战斗单位()
                    self.战斗单位[self.战斗流程[1].挨打方.挨打方]:创建单位(self.战斗流程[1].挨打方.数据,self.战斗流程[1].挨打方.队伍,self.战斗流程[1].挨打方.挨打方)
                    self.战斗流程[1].延时等待=os.time()
                    self.执行流程 = 39.2
            elseif self.执行流程==39.2 and os.time()-self.战斗流程[1].延时等待>=1 then
                    self.执行流程=20
            elseif self.执行流程==40 then --套装变身
                    if not self.战斗流程[1].变身套 or __res.配置.变身造型~=1 then
                        self:设置动作(self.战斗流程[1].攻击方,"施法",true)
                        self:设置特效(self.战斗流程[1].攻击方,"动物套效果")
                    end
                    self.执行流程=41
            elseif self.执行流程==41 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
                    if not self.战斗流程[1].变身套 or __res.配置.变身造型~=1 then
                        self.战斗单位[self.战斗流程[1].攻击方]:更改模型(self.战斗流程[1].参数,1)
                    end
                    if self.战斗流程[1].死亡 then
                      self:设置死亡(self.战斗流程[1].攻击方,self.战斗流程[1].死亡)
                    end
                    self.执行流程=20
            elseif self.执行流程==42 then
                self.战斗单位[self.战斗流程[1].攻击方]:设置抓捕动画(self.战斗流程[1].挨打方.挨打方,self.战斗流程[1].宝宝,self.战斗流程[1].捕捉成功,self.战斗流程[1].名称,self.战斗流程[1].目标)
                self.执行流程=43
            elseif self.执行流程==43 and not self.战斗单位[self.战斗流程[1].攻击方].抓捕开关 then
                  self.执行流程=20
            elseif self.执行流程==44 then
                    self.战斗单位[self.战斗流程[1].攻击方].方向 = self.战斗单位[self.战斗流程[1].攻击方].逃跑方向
                    self:设置动作(self.战斗流程[1].攻击方,"返回")
                    self.战斗流程[1].等待计时=os.time()
                    self.执行流程=45
            elseif self.执行流程==45 and os.time()-self.战斗流程[1].等待计时>=3 then
                    if not self.战斗流程[1].成功 then
                        _tp:播放特效音乐(取音效("逃跑失败"))
                        self.执行流程=46
                    else
                        _tp:播放特效音乐(取音效("逃跑成功"))
                        self.执行流程=48
                        self.战斗单位[self.战斗流程[1].攻击方].逃跑开关=true
                        if self.战斗流程[1].追加 then
                          self.战斗单位[self.战斗流程[1].追加].方向 = self.战斗单位[self.战斗流程[1].追加].逃跑方向
                          self:设置动作(self.战斗流程[1].追加,"返回")
                          self.战斗单位[self.战斗流程[1].追加].逃跑开关=true
                        end
                    end    
            elseif self.执行流程==46 then
                    self.战斗流程[1].等待计时=os.time()
                    self.战斗流程[1].初始方向=self.战斗单位[self.战斗流程[1].攻击方].逃跑方向
                    self:设置动作(self.战斗流程[1].攻击方,"待战")
                    self.执行流程=47
            elseif self.执行流程==47 then
              self.战斗流程[1].初始方向 = self.战斗流程[1].初始方向 +1
                    if self.战斗流程[1].初始方向>=4 then self.战斗流程[1].初始方向 = 0 end
                    self.战斗单位[self.战斗流程[1].攻击方].方向 = self.战斗流程[1].初始方向
                    self:设置动作(self.战斗流程[1].攻击方,"待战")
                    if os.time()-self.战斗流程[1].等待计时>=1 then
                        self.战斗单位[self.战斗流程[1].攻击方].方向 = self.战斗单位[self.战斗流程[1].攻击方].初始方向
                        self:设置动作(self.战斗流程[1].攻击方,"待战")
                        self.执行流程 = 20
                    end
            elseif self.执行流程==48 then
                    self.战斗单位[self.战斗流程[1].攻击方].显示xy.x=self.战斗单位[self.战斗流程[1].攻击方].显示xy.x+self.战斗单位[self.战斗流程[1].攻击方].逃跑坐标
                    self.战斗单位[self.战斗流程[1].攻击方].显示xy.y=self.战斗单位[self.战斗流程[1].攻击方].显示xy.y+self.战斗单位[self.战斗流程[1].攻击方].逃跑坐标
                    if not self.战斗流程[1].追加 then
                        if self.战斗单位[self.战斗流程[1].攻击方].显示xy:取距离(self.战斗单位[self.战斗流程[1].攻击方].初始xy.x, self.战斗单位[self.战斗流程[1].攻击方].初始xy.y) >= 500 then
                          self.执行流程 = 49
                        end
                    else
                        self.战斗单位[self.战斗流程[1].追加].显示xy.x=self.战斗单位[self.战斗流程[1].追加].显示xy.x+self.战斗单位[self.战斗流程[1].追加].逃跑坐标
                        self.战斗单位[self.战斗流程[1].追加].显示xy.y=self.战斗单位[self.战斗流程[1].追加].显示xy.y+self.战斗单位[self.战斗流程[1].追加].逃跑坐标
                        if self.战斗单位[self.战斗流程[1].攻击方].显示xy:取距离(self.战斗单位[self.战斗流程[1].攻击方].初始xy.x, self.战斗单位[self.战斗流程[1].攻击方].初始xy.y) >= 500  and self.战斗单位[self.战斗流程[1].追加].显示xy:取距离(self.战斗单位[self.战斗流程[1].追加].初始xy.x, self.战斗单位[self.战斗流程[1].追加].初始xy.y) >= 500 then
                          self.执行流程=49
                        end
                    end
            elseif self.执行流程==49 then
                    self.战斗单位[self.战斗流程[1].攻击方].是否显示=false
                    if self.战斗流程[1].追加 then
                      self.战斗单位[self.战斗流程[1].追加].是否显示=false
                    end
                    if self.战斗流程[1].结束 and  角色信息.数字id==self.战斗流程[1].id then
                        请求服务(5506)
                        self.执行流程=0
                    else
                        self.执行流程=20
                    end
            elseif self.执行流程==50 then
                  for k,v in pairs(self.战斗流程[1].挨打方) do
                        self:设置特效(v.挨打方,v.特效,1)
                        self:设置气血(v.挨打方,v.伤害,v.类型,v.护盾值,v.伤势,v.伤势类型)
                        self:设置状态(v.挨打方,v)
                        if v.类型==1 then
                          self:设置动作(v.挨打方,"挨打",nil,true)
                          self:设置击退(v.挨打方,v.死亡)
                        end
                        if v.复活 then
                            self:设置动作(v.挨打方,"待战")
                        end
                  end
                  if self.战斗流程[1].受益方 then
                      for k,v in pairs(self.战斗流程[1].受益方) do
                          self:设置气血(v.受益方,v.伤害,2)
                      end
                    end
                    self.执行流程 = 51
            elseif self.执行流程==51 and self:施法流程完成(self.战斗流程[1].攻击方,self.战斗流程[1].挨打方,self.战斗流程[1]) then
                    self.执行流程 = 20
            elseif self.执行流程==52 then
                    if self.战斗流程[1].id==角色信息.数字id or self.战斗流程[1].id==0 then
                        __UI弹出.提示框:打开(self.战斗流程[1].内容)
                    end
                    self.执行流程=20
            elseif self.执行流程==53 then --合击
                    self:开启移动(self.战斗流程[1].攻击方,self.战斗流程[1].挨打方[1].挨打方)
                    self:开启移动(self.战斗流程[1].队友,self.战斗流程[1].挨打方[1].挨打方)
                    self.执行流程=54
            elseif self.执行流程==54 and self:取停止移动(self.战斗流程[1].攻击方) and self:取停止移动(self.战斗流程[1].队友) then
                    self:设置动作(self.战斗流程[1].攻击方,"攻击",true)
                    self:设置动作(self.战斗流程[1].队友,"攻击",true)
                    self.执行流程=55
            elseif self.执行流程==55 and self:取物理攻击中(self.战斗流程[1].攻击方) then --and self:取物理攻击中(self.战斗流程[1].队友) 
                      self:设置特效(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].挨打方[1].特效)
                      self:设置物理掉血(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].挨打方[1])
                      self:设置状态(self.战斗流程[1].攻击方,self.战斗流程[1])
                      self.执行流程 = 56
            elseif self.执行流程==56 and self:取进攻完毕(self.战斗流程[1].队友) then
                        self:开启返回(self.战斗流程[1].队友)
                        if self.战斗流程[1].反震伤害 or self.战斗流程[1].反击伤害 then
                            self.执行流程=4
                        else
                            self.执行流程=17
                        end
            elseif self.执行流程==57 then
                      self:施法前检测(self.战斗流程[1].挨打方)
                      if #self.战斗流程[1].挨打方 <= 0 then
                            self.执行流程=20
                            return
                      end
                      self:设置动作(self.战斗流程[1].攻击方,"施法",true)
                      for k,v in pairs(self.战斗流程[1].挨打方) do
                          self.战斗单位[v.挨打方]:设置飞镖(self.战斗单位[self.战斗流程[1].攻击方].显示xy,self.战斗单位[self.战斗流程[1].攻击方].初始方向,v.伤害,v.死亡)
                      end
                      self.执行流程=24

            end

end





function 战斗主控:取消状态(内容)
    if 内容.id and 内容.名称 and self.战斗单位[内容.id] then
        self.战斗单位[内容.id]:取消状态(内容.名称) 
    end
end

  function 战斗主控:鬼魂复活(内容)
        if 内容.id and  self.战斗单位[内容.id] then
                self.战斗单位[内容.id].停止更新=false
                self.战斗单位[内容.id]:换动作("待战")
                self.战斗单位[内容.id]:设置掉血(内容.气血,2)
        end
  end

  function 战斗主控:刷新技能(内容)
    if 内容.id and  self.战斗单位[内容.id] then
        self.战斗单位[内容.id].主动技能=内容.主动技能
    end

  end
  
-- function 战斗主控:状态同步(内容)
--     local 不是状态 ={ 战意=true,超级战意=true,风灵=true,剑意=true,灵药红=true,灵药蓝=true,灵药黄=true,
--                      符咒=true,雷法崩裂=true,雷法震煞=true,雷法坤伏=true,雷法翻天=true,雷法倒海=true,
--                      五行珠金=true,五行珠木=true,五行珠水=true,五行珠火=true,五行珠土=true,人参娃娃=true,
--                      护盾=true
--     }


--     for k,v in pairs(内容) do
--         if type(v) =="table"  and v and self.战斗单位[k] then
--             for z,n in pairs(v) do
--                 if not 不是状态[z] and self.战斗单位[k].状态特效  then
--                     -- if not self.战斗单位[k].状态特效[z] then
--                     --     self.战斗单位[k]:增加状态(z)
--                     --     self.战斗单位[k].状态特效[z].回合=n.回合
--                     -- else
--                     --     self.战斗单位[k].状态特效[z].回合=n.回合
--                     -- end
--                     if self.战斗单位[k].状态特效[z] and  self.战斗单位[k].状态特效[z].回合 and n.回合 then
--                         self.战斗单位[k].状态特效[z].回合=n.回合
--                     end
--                 else
--                     if z=="护盾" then
--                         self.战斗单位[k].护盾=n.护盾值
--                     else
--                         if self.战斗单位[k][z] then
--                             self.战斗单位[k][z]  = n
--                         end
--                     end
                   
--                 end
--             end
--         end
--     end
-- end


function 战斗主控:状态同步(内容,序号)
    local 不是状态 ={ 战意=true,超级战意=true,风灵=true,剑意=true,灵药红=true,灵药蓝=true,灵药黄=true,
                     符咒=true,雷法崩裂=true,雷法震煞=true,雷法坤伏=true,雷法翻天=true,雷法倒海=true,
                     五行珠金=true,五行珠木=true,五行珠水=true,五行珠火=true,五行珠土=true,人参娃娃=true,
                     护盾=true,护体灵盾=true
    }


    for k,v in pairs(内容) do
        if type(v) =="table"  and v and self.战斗单位[k] then
            for z,n in pairs(v) do
                if not 不是状态[z] and self.战斗单位[k].状态特效  then
                    if self.战斗单位[k].状态特效[z] and n.回合 then
                        self.战斗单位[k].状态特效[z].回合=n.回合
                    end
                else
                    if z=="护盾" or z=="护体灵盾" then
                        if self.战斗单位[k].状态特效.护盾 then
                            if n.回合 then
                                self.战斗单位[k].状态特效.护盾.回合=n.回合
                            end
                            if n.护盾值 and n.护盾值~=0 then
                                self.战斗单位[k].状态特效.护盾.护盾值 = n.护盾值
                            else
                                self.战斗单位[k]:取消状态("护盾") 
                            end
                        end
                    elseif 不是状态[z] and  self.战斗单位[k][z] then
                            self.战斗单位[k][z]  = n
                    end
                   
                end
            end
            for z,n in pairs(self.战斗单位[k].状态特效) do
                if not n.回合 then
                    self.战斗单位[k]:取消状态(z)
                end
            end
            if 序号 and 序号==5519.1 then
                if self.战斗单位[k].气血<=0 then
                    if self.战斗单位[k].状态特效 and self.战斗单位[k].状态特效.复活  then
                      self.战斗单位[k]:开启击退(2)
                    else
                      self.战斗单位[k]:开启击退(1)
                    end
                else
                    for z,n in pairs(v) do
                      if not 不是状态[z] then
                          self.战斗单位[k]:增加状态(z)
                      end
                    end
                end
            end
        end
    end

    self:聊天外框战斗()
end


function 战斗主控:设置血量同步(数据)
    for k=1,#数据 do
        if self.战斗单位[k] then
            self.战斗单位[k]:结束同步(数据[k].气血, 数据[k].最大气血,数据[k].气血上限, 数据[k].魔法,数据[k].最大魔法,数据[k].愤怒)
        end
    end
  end

  function 战斗主控:设置魔法愤怒同步(数据)
        for k, v in pairs(数据) do
            if self.战斗单位[k] then
                self.战斗单位[k]:结束同步(nil, nil,nil, v.魔法,v.最大魔法,v.愤怒)
                if v.自动指令 then
                  self.战斗单位[k].数据.自动指令=v.自动指令
                end
                if v.自动战斗 then
                    self.战斗单位[k].数据.自动战斗=v.自动战斗
                    if self.战斗单位[k].类型 == "角色" and self.战斗单位[k].数字id == 角色信息.数字id then
                        self.自动开关 = v.自动战斗
                    end
                end
            end
        end
        self:设置自动()


  end






  
function 战斗主控:置全屏技能(技能, 单位)

  -- 初始化默认值
  self.全屏加速 = 1.5
  self.掉血流程 = 0
  self.掉血帧 = 0
  self.是否延迟 = false
  self.拼接特效 = {}
  local qp = false

  -- 定义特效配置表
  local 特效配置 = {
      龙卷雨击 = {
          数量 = 7,
          特效前缀 = "龙卷雨击",
          偏移 = {
              {x=-50,y=-100}, {x=-50,y=-100}, {x=-200,y=-100},
              {x=-50,y=0}, {x=-200,y=0}, {x=-50,y=50,延迟=1},
              {x=-100,y=-50}
          },
          背景状态 = 1,
          掉血流程 = 6,
          掉血帧 = 42,
          全屏加速 = 2.5,
          延迟 = true
      },
      龙吟 = {
          数量 = 5,
          特效前缀 = "龙吟",
          偏移 = {
              {x=-80,y=-40}, {x=-80,y=-80}, {x=-80,y=-30,延迟=0.1},
              {x=-80,y=-80}, {x=-80,y=-80}
          },
          背景状态 = 1,
          掉血流程 = 5,
          掉血帧 = 6,
          全屏加速 = 2,
          延迟 = true
      },
      雨落寒沙 = {
          数量 = 1,
          特效前缀 = function() return 单位.敌我 == 1 and "雨落寒沙_我方" or "雨落寒沙_敌方" end,
          偏移 = {
              function() return {x=单位.敌我 == 1 and -130 or -100, y=-30} end
          },
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1,
          延迟 = true
      },
      子母神针 = {
          数量 = 1,
          特效前缀 = function() return 单位.敌我 == 1 and "雨落寒沙_我方" or "雨落寒沙_敌方" end,
          偏移 = {
              function() return {x=单位.敌我 == 1 and -130 or -100, y=-30} end
          },
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1,
          延迟 = true
      },
      破釜沉舟 = {
          数量 = 1,
          特效前缀 = function() return 单位.敌我 == 1 and "破釜沉舟_我方" or "破釜沉舟_敌方" end,
          偏移 = {
              function() return {x=单位.敌我 == 1 and -300 or 100, y=单位.敌我 == 1 and -250 or 100} end
          },
          全屏加速 = 1
      },
      翻江搅海 = {
          数量 = 1,
          特效前缀 = "翻江搅海1",
          偏移 = {{x=-100,y=0}},
          全屏加速 = 1
      },
      武神怒击 = {
          数量 = 2,
          特效前缀 = "武神怒击",
          偏移 = {
              {x=-255,y=-200}, {x=-200,y=-150}
          },
          背景状态 = 3,
          全屏加速 = 1
      },
      飞砂走石 = {
          数量 = 5,
          特效前缀 = "飞砂走石",
          偏移 = {
              {x=-90,y=50}, {x=-90,y=10}, {x=30,y=-50,延迟=0.3},
              {x=-180,y=30,延迟=0.2}, {x=-180,y=30}
          },
          掉血流程 = 4,
          掉血帧 = 28,
          全屏加速 = 2,
          延迟 = true
      },
      秘传飞砂走石 = {
          数量 = 5,
          特效前缀 = "飞砂走石",
          偏移 = {
              {x=-90,y=50}, {x=-90,y=10}, {x=30,y=-50,延迟=0.3},
              {x=-180,y=30,延迟=0.2}, {x=-180,y=30}
          },
          掉血流程 = 4,
          掉血帧 = 28,
          全屏加速 = 2,
          延迟 = true
      },
      摇头摆尾 = {
          数量 = 1,
          特效前缀 = "摇头摆尾",
          偏移 = {{x=-50,y=20}},
          掉血流程 = 1,
          掉血帧 = 15,
          全屏加速 = 1.5,
          延迟 = true
      },
      绝幻魔音 = {
          数量 = 1,
          特效前缀 = "绝幻魔音",
          偏移 = {{x=-100,y=-50}},
          掉血流程 = 1,
          掉血帧 = 12,
          全屏加速 = 1,
          延迟 = true
      },
      碎甲术 = {
          数量 = 1,
          特效前缀 = "碎甲术",
          偏移 = {{x=-200,y=-200}},
          掉血流程 = 1,
          掉血帧 = 17,
          全屏加速 = 0.8,
          延迟 = true
      },
      雷浪穿云 = {
          数量 = 1,
          特效前缀 = "雷浪穿云",
          偏移 = {{x=-50,y=-50}},
          背景状态 = 1,
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1,
          延迟 = true
      },
      刀光剑影 = {
          数量 = 3,
          特效前缀 = "刀光剑影",
          偏移 = {
              {x=-255,y=-200}, {x=-200,y=-150}, {x=-50,y=20,延迟=0.5}
          },
          背景状态 = 3,
          掉血流程 = 3,
          掉血帧 = 15,
          全屏加速 = 1,
          延迟 = true
      },
      毁灭之光 = {
          数量 = 3,
          特效前缀 = "毁灭之光",
          偏移 = {
              {x=-255,y=-200}, {x=-200,y=-150}, {x=-50,y=20,延迟=0.5}
          },
          背景状态 = 3,
          掉血流程 = 3,
          掉血帧 = 8,
          全屏加速 = 1,
          延迟 = true
      },
      叱咤风云 = {
          数量 = 1,
          特效前缀 = "叱咤风云",
          偏移 = {{x=-50,y=-50}},
          背景状态 = 1,
          掉血流程 = 1,
          掉血帧 = 23,
          全屏加速 = 1.5,
          延迟 = true
      },
      八凶法阵 = {
          数量 = 1,
          特效前缀 = "八凶法阵",
          偏移 = {{x=-80,y=-50}},
          背景状态 = 3,
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1.2,
          延迟 = true
      },
      天降灵葫 = {
          数量 = 1,
          特效前缀 = "天降灵葫",
          偏移 = {{x=-80,y=-20}},
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1.2,
          延迟 = true
      },
      河东狮吼 = {
          数量 = 1,
          特效前缀 = "河东狮吼",
          偏移 = {{x=-50,y=20}},
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 0.8
      },
      侵掠如火 = {
          数量 = 1,
          特效前缀 = "侵掠如火",
          偏移 = {{x=-50,y=20}},
          全屏加速 = 1
      },
      其徐如林 = {
          数量 = 1,
          特效前缀 = "其徐如林",
          偏移 = {{x=-50,y=20}},
          全屏加速 = 1
      },
      不动如山 = {
          数量 = 1,
          特效前缀 = "不动如山",
          偏移 = {{x=-50,y=20}},
          全屏加速 = 1
      },
      其疾如风 = {
          数量 = 1,
          特效前缀 = "其疾如风",
          偏移 = {{x=-50,y=20}},
          全屏加速 = 1
      },
      停陷术 = {
          数量 = 1,
          特效前缀 = "停陷术",
          偏移 = {{x=-80,y=-20}},
          掉血流程 = 1,
          掉血帧 = 18
      },
      巨岩破 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      日光华 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      靛沧海 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      苍茫树 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      地裂火 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      奔雷咒 = {
          数量 = 9,
          特效前缀 = "奔雷咒",
          偏移 = {
              {x=-150,y=20}, {x=10,y=-50}, {x=-60,y=-20,延迟=0.1},
              {x=-120,y=10,延迟=0.1}, {x=30,y=-50,延迟=0.1},
              {x=-60,y=-20,延迟=0.1}, {x=-150,y=20}, {x=10,y=-50},
              {x=-60,y=-20}
          },
          背景状态 = 1,
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 2,
          延迟 = true
      },
      超级奔雷咒 = {
          数量 = 9,
          特效前缀 = "奔雷咒",
          偏移 = {
              {x=-150,y=20}, {x=10,y=-50}, {x=-60,y=-20,延迟=0.1},
              {x=-120,y=10,延迟=0.1}, {x=30,y=-50,延迟=0.1},
              {x=-60,y=-20,延迟=0.1}, {x=-150,y=20}, {x=10,y=-50},
              {x=-60,y=-20}
          },
          背景状态 = 1,
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 2,
          延迟 = true
      },
      泰山压顶 = {
          数量 = 2,
          特效前缀 = "泰山压顶",
          偏移 = {
              {x=-50,y=10}, {x=-50,y=10,延迟=0.3}
          },
          背景状态 = 2,
          掉血流程 = 2,
          掉血帧 = 3,
          全屏加速 = 1.3,
          延迟 = true
          
      },
      超级泰山压顶 = {
          数量 = 2,
          特效前缀 = "泰山压顶",
          偏移 = {
              {x=-50,y=10}, {x=-50,y=10,延迟=0.3}
          },
          背景状态 = 2,
          掉血流程 = 2,
          掉血帧 = 3,
          全屏加速 = 1.3,
          延迟 = true
      },
      水漫金山 = {
          数量 = 8,
          特效前缀 = "水漫金山",
          偏移 = {
              {x=-150,y=-150}, {x=-200,y=-150}, {x=0,y=-100,延迟=0.1},
              {x=50,y=-100}, {x=-150,y=0,延迟=0.1}, {x=-200,y=0},
              {x=-50,y=-100}, {x=-100,y=-100}
          },
          背景状态 = 2,
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 2,
          延迟 = true
      },
      超级水漫金山 = {
          数量 = 8,
          特效前缀 = "水漫金山",
          偏移 = {
              {x=-150,y=-150}, {x=-200,y=-150}, {x=0,y=-100,延迟=0.1},
              {x=50,y=-100}, {x=-150,y=0,延迟=0.1}, {x=-200,y=0},
              {x=-50,y=-100}, {x=-100,y=-100}
          },
          背景状态 = 2,
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 2,
          延迟 = true
      },
      风卷残云 = {
          数量 = 1,
          特效前缀 = "风卷残云",
          偏移 = {{x=-50,y=20}},
          掉血流程 = 1,
          掉血帧 = 12,
          全屏加速 = 1.1,
          延迟 = true
      },
      枯木逢春 = {
          数量 = 1,
          特效前缀 = "风卷残云",
          偏移 = {{x=-50,y=20}},
          掉血流程 = 1,
          掉血帧 = 12,
          全屏加速 = 1.1,
          延迟 = true
      },
      落叶萧萧 = {
          数量 = 10,
          特效前缀 = "落叶萧萧",
          偏移 = {
              {x=-70,y=-30}, {x=-150,y=0,延迟=0.5}, {x=-90,y=-10,延迟=0.1},
              {x=-90,y=50,延迟=0.1}, {x=-150,y=40,延迟=0.1}, {x=-70,y=0,延迟=0.1},
              {x=-20,y=0,延迟=0.1}, {x=-20,y=30,延迟=0.1}, {x=20,y=-30,延迟=0.1},
              {x=-70,y=-30,延迟=0.1}
          },
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 0.8,
          延迟 = true
      },
      地狱烈火 = {
          数量 = 31,
          特效前缀 = "地狱烈火",
          偏移 = {
              {x=-300,y=50}, {x=-300,y=-60,延迟=0.2}, {x=-300,y=-60},
              {x=-300,y=50}, {x=-300,y=50}, {x=-300,y=150},
              {x=-300,y=150,延迟=0.2}, {x=-150,y=-110}, {x=-150,y=-110},
              {x=-150,y=0}, {x=-150,y=0}, {x=-150,y=80},
              {x=-150,y=80,延迟=0.2}, {x=0,y=-140}, {x=0,y=-140},
              {x=0,y=-60}, {x=0,y=-60}, {x=0,y=20},
              {x=0,y=20,延迟=0.2}, {x=150,y=-180}, {x=150,y=-180},
              {x=150,y=-100}, {x=150,y=-100}, {x=150,y=-20},
              {x=150,y=-20,延迟=0.3}, {x=300,y=-230}, {x=300,y=-230},
              {x=300,y=-150}, {x=300,y=-150}, {x=300,y=-70},
              {x=300,y=-70}
          },
          背景状态 = 3,
          掉血流程 = 25,
          掉血帧 = 20,
          全屏加速 = 3,
          延迟 = true
      },
      超级地狱烈火 = {
          数量 = 31,
          特效前缀 = "地狱烈火",
          偏移 = {
              {x=-300,y=50}, {x=-300,y=-60,延迟=0.2}, {x=-300,y=-60},
              {x=-300,y=50}, {x=-300,y=50}, {x=-300,y=150},
              {x=-300,y=150,延迟=0.2}, {x=-150,y=-110}, {x=-150,y=-110},
              {x=-150,y=0}, {x=-150,y=0}, {x=-150,y=80},
              {x=-150,y=80,延迟=0.2}, {x=0,y=-140}, {x=0,y=-140},
              {x=0,y=-60}, {x=0,y=-60}, {x=0,y=20},
              {x=0,y=20,延迟=0.2}, {x=150,y=-180}, {x=150,y=-180},
              {x=150,y=-100}, {x=150,y=-100}, {x=150,y=-20},
              {x=150,y=-20,延迟=0.3}, {x=300,y=-230}, {x=300,y=-230},
              {x=300,y=-150}, {x=300,y=-150}, {x=300,y=-70},
              {x=300,y=-70}
          },
          背景状态 = 3,
          掉血流程 = 25,
          掉血帧 = 20,
          全屏加速 = 3,
          延迟 = true
      }
  }

  -- 从配置表中获取当前技能配置
  local 当前配置 = 特效配置[技能]

  if 当前配置 then
      -- 创建特效
      for s = 1, 当前配置.数量 do
          local 特效名称 = type(当前配置.特效前缀) == "function" and 当前配置.特效前缀() or
                        (当前配置.数量 > 1 and 当前配置.特效前缀..s or 当前配置.特效前缀)

          local 偏移数据 = 当前配置.偏移[s]
          if type(偏移数据) == "function" then
              偏移数据 = 偏移数据()
          end

          self.拼接特效[s] = {
              特效 = 单位:加载特效(特效名称),
              偏移 = 偏移数据 or {x=0,y=0}
          }

          if 偏移数据 and 偏移数据.延迟 then
              self.拼接特效[s].延迟 = 偏移数据.延迟
          end
      end

      -- 设置属性
      self.是否延迟 = 当前配置.延迟 or false
      self.背景状态 = 当前配置.背景状态 or nil
      self.掉血流程 = 当前配置.掉血流程 or 0
      self.掉血帧 = 当前配置.掉血帧 or 0
      self.全屏加速 = 当前配置.全屏加速 or 1.5
      qp = true
  end

  -- 设置拼接偏移
  if qp then
      if 单位.敌我 == 1 then
          self.拼接偏移.x, self.拼接偏移.y = 引擎.宽度2+220, 引擎.高度2+130
      else
          self.拼接偏移.x, self.拼接偏移.y = 引擎.宽度2-75, 引擎.高度2-75
      end
  end

end




function 战斗主控:释放()
    self.进程 = "加载"
    self.加载数量 = 0
    self.战斗单位 = {}
    self.战斗流程 = {}
    self.显示排序 = {}
    self.拼接特效 = {}
    self.状态显示 = false
    self.状态显示2 = false
    self.回合数 = 0
    self.背景状态 = 0
end





function 战斗主控:更新(dt,x,y)
        self.显示表 = {}
        for n = 1, #self.战斗单位 do
            self.战斗单位[n]:更新(dt)
            table.insert(self.显示表,self.战斗单位[n])
        end
        if __多开操作 and not 战斗层.多开自动.是否可见 then
            战斗层.多开自动:打开()
        elseif self.自动开关 and not __多开操作 and not 战斗层.战斗自动.是否可见  then
            战斗层.战斗自动:打开()
            if 战斗层.多开自动.是否可见 then
              战斗层.多开自动:置可见(false)
            end
        end
    table.sort(self.显示表, 排序)
    if self.进程 == "计算" and self.战斗流程 ~= nil and self.战斗流程[1] ~= nil then
          if self.战斗流程[1].允许==false then
              if self.战斗流程[1].提示 and self.战斗流程[1].提示.允许 then
                  self.战斗单位[self.战斗流程[1].攻击方]:提示技能(self.战斗流程[1].提示)
              end
              self.战斗流程[1].允许=true
              self.执行流程=self.战斗流程[1].流程
              self.进程="执行"
          end
          if self.背景状态 then
              self.背景状态 = 1
          end
    elseif self.进程 == "执行" then
        self:流程更新()
        self:拼接动画(dt)
        if self.掉血流程 and (not self.拼接特效 or #self.拼接特效==0) then
          if not self.战斗流程[1].挨打方 then
              self.掉血流程 = nil
          else
              local 通过 = #self.战斗流程[1].挨打方
              for k,v in pairs(self.战斗流程[1].挨打方) do
                  if not v.挨打方 or not self.战斗单位[v.挨打方] or self.战斗单位[v.挨打方]:取法术状态() then
                      通过 = 通过 - 1
                  end
              end
              if 通过<=0 then
                  self.掉血流程 = nil
              end
          end
      end

        
     
    end
end

function 战斗主控:拼接动画(dt)
    if not self.拼接特效 then return end
    for n=1,#self.拼接特效 do
      if self.拼接特效[n] then
        if self.是否延迟 then
          if self.拼接特效[n].延迟  then
            self:拼接延迟计算(n)
            break
          else
            self:拼接动画处理(n,dt)
          end
        else
          self:拼接动画处理(n,dt)
          if #self.拼接特效==self.掉血流程 then self.掉血流程=nil end
        end
      end
    end
    if #self.拼接特效==0 then self.拼接特效=nil self.背景状态=nil  end
end

function 战斗主控:拼接延迟计算(n)
  if self.拼接特效[n].延迟时间==nil then
      self.拼接特效[n].延迟时间=os.clock()+self.拼接特效[n].延迟
  elseif self.拼接特效[n].延迟时间-os.clock()<=0 then
      self.拼接特效[n].延迟=nil
      self.拼接特效[n].延迟时间=nil
  end
end

function 战斗主控:拼接动画处理(n,dt)
  self.拼接特效[n].特效:更新(dt*self.全屏加速)
  table.insert(self.显示表,self.拼接特效[n])
  if type(self.掉血流程)=="number" and n>=self.掉血流程 then
      if self.掉血帧 and self.拼接特效[n].特效:取当前帧() >= self.掉血帧 then
          self.掉血流程=nil
      elseif not self.掉血帧 and self.拼接特效[n].特效:取当前帧()>=self.拼接特效[n].特效:取帧数() then
          self.掉血流程=nil
      end
  end
  if self.拼接特效[n].特效:取当前帧()>=self.拼接特效[n].特效:取帧数() then
      table.remove(self.拼接特效,n)
  end
end

function 战斗主控:显示(x, y)
    self.背景:显示(0, 0)
    self.背景圆:显示(引擎.宽度2 - 235,引擎.高度2 - 125)
    for i = 1, #self.显示表 do
        if self.显示表[i].特效 then
            self.显示表[i].特效:显示(
                math.floor(self.拼接偏移.x + self.显示表[i].偏移.x),
                math.floor(self.拼接偏移.y + self.显示表[i].偏移.y)
            )
        else
            self.显示表[i]:显示(x, y)
        end
    end
    if self.进程 == "命令" then
        self.秒显示 = 0
        self.分显示 = 0
        self.结果 = os.time() - self.命令数据.计时
        self.显示时间 = 0
        if self.结果 >= 1 then
            self.命令数据.计时 = os.time()
            self.命令数据.秒 = self.命令数据.秒 - 1
            if self.命令数据.秒 < 0 then
                if self.命令数据.分 <= 0 and self.命令数据.秒 <= 0 then
                    __UI界面.界面层.战斗界面:退出()
                    self.进程 = "等待"
                    self.显示时间 = 1
                elseif self.命令数据.秒 <= 0 then
                    self.命令数据.秒 = 9
                    self.命令数据.分 = self.命令数据.分 - 1
                end
            end
        end
        if 0 == self.显示时间 then
            self.分显示 = self.命令数据.分 + 1
            if self.分显示 > 10 then
                self.分显示 = 1
            end
            self.秒显示 = self.命令数据.秒 + 1
            if self.秒显示 > 10 then
                self.秒显示 = 1
            end
            self.数字图片[self.分显示]:显示(引擎.宽度2- 75, 60)
            self.数字图片[self.秒显示]:显示(引擎.宽度2 - 25, 60)
        end
    elseif self.进程 == "等待" then
          if  __UI界面.界面层.战斗界面.是否可见 then
              __UI界面.界面层.战斗界面:退出()
          end
          self.请等待:显示(引擎.宽度2- 75, 60)
    elseif self.进程 == "执行" then
        if self.战斗流程[1].九黎连击  then
            self.连击背景:显示(引擎.宽度-180,140)
            if self.战斗流程[1].九黎连击<10 then
                九黎连击:置颜色(255,255,255,255):取精灵(self.战斗流程[1].九黎连击):显示(引擎.宽度-160,87)
            else
                九黎连击:置颜色(255,255,255,255):取精灵(self.战斗流程[1].九黎连击):显示(引擎.宽度-177,87)
            end
        end
    end
    if self.回合计数 then
          self.回合计数:显示(引擎.宽度2- self.回合计数.宽度//2, 10)
    end

    for n = 1, #self.战斗单位 do
        if self.战斗单位[n].掉血开关 then
            if 1 == self.战斗单位[n].伤害类型 then
                self.战斗单位[n]:掉血显示()
            elseif 3 == self.战斗单位[n].伤害类型 or 4 == self.战斗单位[n].伤害类型 then
                self.战斗单位[n]:暴击显示()
            elseif self.战斗单位[n].伤害类型==5 then
                self.战斗单位[n]:回血暴击()
            else
                self.战斗单位[n]:加血显示()
            end
        end
        if self.战斗单位[n].技能提示 then
            self.战斗单位[n]:提示显示()
        end
    end
end

return 战斗主控



   -- if self.拼接特效 then
        --     for n = 1, #self.拼接特效 do
        --         if self.拼接特效[n] then
        --             if self.拼接特效[n].延时 then
        --                 self.拼接特效[n].延时 = self.拼接特效[n].延时 - 1
        --                 if 1 == self.拼接特效[n].延时 then
        --                     self.拼接特效[n].延时 = nil
        --                 end
        --             else
        --                 self.拼接特效[n].特效:更新(dt * self.全屏加速)
        --                 if not self.拼接特效[n].延时 then
        --                     _加入显示(self.拼接特效[n])
        --                 end
        --                 if nil ~= self.拼接特效[n].结束时间 then
        --                     if not self.拼接特效[n].结束时间原始 then
        --                         self.拼接特效[n].结束时间原始 = self.拼接特效[n].结束时间
        --                     end
        --                     self.拼接特效[n].结束时间 = self.拼接特效[n].结束时间 - 1
        --                     if self.拼接特效[n].结束时间 < 30 then
        --                         self.拼接特效[n].偏移.y = self.拼接特效[n].偏移.y + 4.5
        --                     end
        --                     if self.拼接特效[n].结束时间 <= self.拼接特效[n].结束时间原始 // 3
        --                         and not self.拼接特效[n].特效:是否播放() then
        --                         self.掉血流程 = nil
        --                     end
        --                     if self.拼接特效[n].结束时间 <= self.拼接特效[n].结束时间原始 // 2
        --                         and not self.拼接特效[n].特效:是否播放() then
        --                         self.击退流程 = nil
        --                     end
        --                     if 0 == self.拼接特效[n].结束时间 and
        --                         not self.拼接特效[n].特效:是否播放() then
        --                         table.remove(self.拼接特效, n)
        --                     end
        --                 else
        --                     if math.ceil(#self.拼接特效 / 3) == n and
        --                         self.拼接特效[n].特效:取当前帧() >=
        --                         math.ceil(self.拼接特效[n].特效:取帧数() /1.9) then
        --                         self.掉血流程 = nil
        --                     end
        --                     if #self.拼接特效 == n and
        --                         self.拼接特效[n].特效:取当前帧() >=
        --                         math.ceil(self.拼接特效[n].特效:取帧数() /1.2) then
        --                         self.掉血流程 = nil
        --                     end
        --                     if not self.拼接特效[n].特效:是否播放() then
        --                         table.remove(self.拼接特效, n)
        --                     end
        --                 end
        --             end
        --         end
        --     end
        --     if 0 == #self.拼接特效 then
        --         self.拼接特效 = nil
        --         self.背景状态 = nil
        --     end
        -- end