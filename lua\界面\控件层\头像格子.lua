--[[
LastEditTime: 2024-10-13 02:43:19
--]]


local 头像格子 = class("头像格子")
function 头像格子:初始化()
  self.py = {x = 0, y = 0}
  self.确定 = nil
  self.数据宽=0
  self.数据高=0
  self.头像选中 = nil

end

function 头像格子:置头像(模型,w, h,类型,格子)
  if not w then w =32 end
  if not h then h =32 end
  self.头像选中 = __res.UI素材[3]:复制区域(1124, 677, 59, 60):拉伸(w, h):到精灵()
  self.确定 = nil
  self.资源 =nil
  self.小模型资源=nil
  self.头像格子 = nil
  self.数据宽=0
  self.数据高=0
  self.py = {x = 5, y = 5}
  self.数据=模型
  if 模型  and 模型~="" and 模型~= 0  then
      local lssj = 取头像(模型)
      if not lssj[2] then
          lssj[2] = lssj[1]
      end
      self.头像图像=__res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(w - 10, h - 10)
      self.资源 = lssj[7]
      self.小模型资源=lssj[2]
  end
  self:计算显示(w,h,类型,格子)
end

function 头像格子:计算显示(w,h,类型,格子)
        self.类型=类型
        self.数据宽=w
        self.数据高=h
        local nsf = require('SDL.图像')(w+2, h+2)
        if nsf:渲染开始() then
              if 格子 then
                  self.头像格子 = 格子
                  __res:getPNGCC(3, 683, 284, 73, 73):拉伸(w, h):显示(0, 0)
              end
              if self.头像图像 then
                  if self.类型 and (type(self.类型)=="string" or type(self.类型)=="number" ) then
                      self.头像图像:显示(self.py.x, self.py.y)
                      说明字体:置颜色(242, 30, 26):取描边图像(self.类型):显示(2,2)
                  else
                      self.头像图像:显示(self.py.x, self.py.y)
                  end
              end
            nsf:渲染结束()
        end
        self.小模型 = nsf:到精灵()
end

function 头像格子:置偏移(x, y)
  self.py = {x = x, y = y}
  self:计算显示(self.数据宽,self.数据高,self.类型,self.头像格子)
end
function 头像格子:显示(x, y)
  if self.小模型 then
    self.小模型:显示(x, y)
  end
  if self.确定  then
     self.头像选中:显示(x, y)
  end
end
return 头像格子

