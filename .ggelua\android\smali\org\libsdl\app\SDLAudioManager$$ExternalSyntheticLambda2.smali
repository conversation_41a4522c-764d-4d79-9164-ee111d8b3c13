.class public final synthetic Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;
.super Ljava/lang/Object;
.source "D8$$SyntheticClass"

# interfaces
.implements Ljava/util/function/ToIntFunction;


# static fields
.field public static final synthetic INSTANCE:Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;


# direct methods
.method static synthetic constructor <clinit>()V
    .locals 1

    new-instance v0, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;

    invoke-direct {v0}, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;-><init>()V

    sput-object v0, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;->INSTANCE:Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;

    return-void
.end method

.method private synthetic constructor <init>()V
    .locals 0

    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method


# virtual methods
.method public final applyAsInt(Ljava/lang/Object;)I
    .locals 0

    check-cast p1, Landroid/media/AudioDeviceInfo;

    invoke-virtual {p1}, Landroid/media/AudioDeviceInfo;->getId()I

    move-result p1

    return p1
.end method
