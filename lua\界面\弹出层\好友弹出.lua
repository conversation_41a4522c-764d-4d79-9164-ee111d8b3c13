--[[
Author: GGELUA
Date: 2024-03-03 22:16:40
Last Modified by: GGELUA
Last Modified time: 2024-03-04 04:11:18
--]]
__UI弹出["好友弹出"] = 界面:创建弹出窗口("好友弹出", 0, 0, 247, 279)
local 好友弹出 = __UI弹出["好友弹出"]
function 好友弹出:初始化()
  self:置精灵(取黑透明背景(0, 0, 247, 279))
  self:置坐标((引擎.宽度-self.宽度)// 2,(引擎.高度-self.高度) // 2)
end
function 好友弹出:显示(x, y)
  if self.图像 then
    self.图像:显示(x, y)
  end
  if self.图像2 then
    self.图像2:显示( x, y)
  end
end
function 好友弹出:打开(data)
  self:置可见(true)
  self:重置(data)
end
function 好友弹出:重置(data)
  self.图像 = self:创建纹理精灵(function()
    __res:getPNGCC(3, 757, 291, 57, 57):拉伸(50, 50):显示(18, 13)
    local lssj = 取头像(data["模型"])
    if 0 == lssj[2] then
      lssj[2] = lssj[1]
    end
    __res:取资源动画(lssj[7], lssj[2],"图像"):拉伸(48, 48):显示(19, 14)
    
    说明字体:置颜色(255,255,255,255)
    说明字体:取图像(data["名称"]):显示(81, 15)
    说明字体:取图像(data["等级"] .. "级"):显示(81, 42)
    说明字体:取图像(data["门派"] or "无门派"):显示(133, 42)
  end,1
)
  self.数据 = data
end
for i, v in ipairs({
  {
    name = "好友属性",
    x = 18,
    y = 76,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(98, 52),
    font = "好友属性"
  },
  {
    name = "消息聊天",
    x = 130,
    y = 76,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(98, 52),
    font = "消息聊天"
  },
  {
    name = "分组",
    x = 18,
    y = 141,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(98, 52),
    font = "分组"
  },
  {
    name = "组队",
    x = 130,
    y = 141,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(98, 52),
    font = "组队"
  },
  {
    name = "给予",
    x = 18,
    y = 206,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(98, 52),
    font = "给予"
  },
  {
    name = "交易",
    x = 130,
    y = 206,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(98, 52),
    font = "交易"
  }
}) do
  local 临时函数 = 好友弹出:创建蓝色按钮(v.font, v.name, v.x, v.y, 98, 52,说明字体)
 function  临时函数:左键弹起(x, y)
    if v.name == "好友属性" then
      窗口层["好友属性"]["打开"](窗口层["好友属性"], 好友弹出["数据"])
    elseif v.name == "消息聊天" then
      窗口层.好友消息["打开"](窗口层.好友消息, 好友弹出["数据"])
    end
    好友弹出["置可见"](好友弹出, false)
  end
end
