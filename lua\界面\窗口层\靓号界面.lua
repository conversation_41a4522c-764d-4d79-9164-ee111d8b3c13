--[[
LastEditTime: 2024-11-04 19:57:40
--]]


local 靓号界面 = 窗口层:创建窗口("靓号界面", 0,0, 300, 400)

function 靓号界面:初始化()
  self:创建纹理精灵(function()
      置窗口背景("角色靓号", 0, 0, 300, 400, true):显示(0, 0)
      取白色背景(0, 0, 280, 280, true):显示(10, 80)
      取输入背景(0, 0, 135, 23):显示(90, 50)
      任务字体:置颜色(__取颜色("青色")):取图像("当前靓号:"):显示(10,48)
  end)
   self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
   self.可初始化=true
   if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end

end



local 角色靓号 = 靓号界面:丰富文本("角色靓号", 95, 53, 125,23,true)


local 改变 = 靓号界面:创建红色按钮("改变", "改变", 45,370, 60, 22)
  function 改变:左键弹起(x, y)

    if 靓号界面.选择 then
       请求服务(201,{编号=靓号界面.选择})
    end
      
   end

  local 隐藏 = 靓号界面:创建红色按钮("隐藏", "隐藏", 190,370, 60, 22)
  function 隐藏:左键弹起(x, y)
       请求服务(201,{编号=0})
  end





function 靓号界面:打开(编号)
  self:置可见(true,true)
  self:刷新(编号)
  self.选择=nil
  self.改变:置禁止(true)
  self.靓号选择:重置()
end

function 靓号界面:刷新(编号)
    self.角色靓号:清空()
    if 编号~=nil and 编号~=0 then
        self.角色靓号:置文本(金色id(角色信息.数字id,tonumber(编号)))
    else
        self.角色靓号:置文本("#H"..角色信息.数字id)
    end
end 



local 靓号选择 = 靓号界面:创建列表("靓号选择", 20, 90, 260, 200)
  function 靓号选择:初始化()
    self.行高度 = 25
  end
  function 靓号选择:重置()
      self:清空()
      for i = 1, 7 do
        self:添加()
        local 临时文本 = self.子控件[i]:丰富文本("临时文本"..i, 5, 1, 250,23,true)
        临时文本:清空()
        临时文本:置文本(金色id(角色信息.数字id,i))
        临时文本:置可见(true)
      end

  end
  
  function 靓号选择:左键弹起(x, y, i)
      靓号界面.选择=i
      靓号界面.改变:置禁止(false)
  end
  local 关闭 = 靓号界面:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    靓号界面:置可见(false)
  end


