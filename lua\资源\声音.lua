-- <AUTHOR> GGELUA
-- @Last Modified by    : baidwwy
-- @Date                : 2022-04-10 14:32:28
-- @Last Modified time  : 2024-09-30 18:55:43

local _背景音乐, _战斗音乐
local _战斗缓存 = {}

local random = math.random

local 声音 = class('资源声音')

function 声音:初始化声音()
    self.音乐音量 = self.配置.音乐 and self.配置.音乐音量
    self.音效音量 = self.配置.音效 and self.配置.音效音量
    if self.音乐音量 > 0 then
        self:登录音乐()
    end
end

function 声音:登录音乐()
    self:地图音乐('login01')
end

function 声音:地图音乐(id)
    if _背景音乐 then
        _背景音乐:停止()
        _背景音乐 = nil
    end
    if self.音乐音量 == 0 then
        return
    end
    _背景音乐 = self:getmusic('music/%s.ogg', id)
    if _背景音乐 then
        _背景音乐:播放(true):置音量(self.音乐音量)
    end
end

function 声音:进入战斗()
    if _背景音乐 then
        _背景音乐:暂停()
    end
    _战斗音乐 = self:getmusic('music/battle%02d.ogg', math.random(3))
    if _战斗音乐 then
        _战斗音乐:播放(true):置音量(self.音乐音量)
    end
end

function 声音:退出战斗()
    if _战斗音乐 then
        _战斗音乐:停止()
    end
    if _背景音乐 then
        _背景音乐:恢复()
    end
end

function 声音:动作音效(id, act)
    local snd = self:getsound('sound/char/%04d/%s.wav', id, act)

    if snd then
        snd:播放():置音量(self.音效音量)
    end
    return snd
end

function 声音:动画音效(name)
    local snd = self:getsound('sound/addon/%s.wav', name)

    if snd then
        snd:播放():置音量(self.音效音量)
    end
    return snd
end

function 声音:界面音效(file, ...)
    local snd = self:getsound(file, ...)
    if snd then
        snd:播放():置音量(self.音效音量)
    end
end

function 声音:技能音效(id, name)
    local snd = self:getsound('sound/magic/%04d.wav', id)

    if snd then
        snd:播放():置音量(self.音效音量)
    end
    return snd
end

function 声音:置音乐音量(v)
    if type(v) == 'number' then
        self.配置.音乐 = v > 0
        self.配置.音乐音量 = v
    else
        self.配置.音乐 = v
    end
    self.音乐音量 = self.配置.音乐 and self.配置.音乐音量 or 0

    if _背景音乐 then
        _背景音乐:置音量(self.音乐音量)
    end
    if _战斗音乐 then
        _战斗音乐:置音量(self.音乐音量)
    end
end

function 声音:置音效音量(v)
    if type(v) == 'number' then
        self.配置.音效 = v > 0
        self.配置.音效音量 = v
    else
        self.配置.音效 = v
    end
    self.音效音量 = self.配置.音效 and self.配置.音效音量 or 0

    for i, v in ipairs(self._环境音效) do
        v:置音量(self.音效音量)
    end
end

function 声音:置音量(v)
    self:置音乐音量(v)
    self:置音效音量(v)
end

return 声音
