local 子女养育 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "子女养育", 80 + abbr.py.x, 6 + abbr.py.y, 768, 521)
function 子女养育:初始化()
  local nsf = require("SDL.图像")(768, 521)
  if nsf["渲染开始"](nsf) then
    置窗口背景("子女养育", 0, 12, 701, 509, true)["显示"](置窗口背景("子女养育", 0, 12, 701, 509, true), 60, 0)
    取白色背景(0, 0, 672, 449, true)["显示"](取白色背景(0, 0, 672, 449, true), 75, 52)
    取灰色背景(0, 0, 360, 210, true)["置透明"](取灰色背景(0, 0, 360, 210, true), 100)["显示"](取灰色背景(0, 0, 360, 210, true)["置透明"](取灰色背景(0, 0, 360, 210, true), 100), 366, 258)
    取灰色背景(0, 0, 265, 405, true)["置透明"](取灰色背景(0, 0, 265, 405, true), 100)["显示"](取灰色背景(0, 0, 265, 405, true)["置透明"](取灰色背景(0, 0, 265, 405, true), 100), 96, 76)
    local lssj = 取输入背景(0, 0, 70, 23)
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18["取图像"](字体18, "养育金")["显示"](字体18["取图像"](字体18, "养育金"), 366, 70)
    lssj["显示"](lssj, 426, 68)
    字体18["取图像"](字体18, "疲劳")["显示"](字体18["取图像"](字体18, "疲劳"), 506, 70)
    lssj["显示"](lssj, 546, 68)
    字体18["取图像"](字体18, "叛逆")["显示"](字体18["取图像"](字体18, "叛逆"), 626, 70)
    lssj["显示"](lssj, 666, 68)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 子女养育:打开()
  self:置可见(true)
  self.头像网格["置头像"](self.头像网格, 角色信息["子女列表"])
  self.养育属性["置选中"](self.养育属性, true)
  self.养育属性["左键弹起"](self.养育属性)
end
function 子女养育:刷新(lx)
  if not lx then
    lx = self.属性类型
  else
    self.属性类型 = lx
  end
  --print(子女养育["选中子女"],123,角色信息["子女列表"])
  --print(table.tostring(角色信息["子女列表"]))
  local lsb = 角色信息["子女列表"][子女养育["选中子女"]]
  if "养育属性" == lx then
    local nsf = require("SDL.图像")(768, 521)
    if nsf["渲染开始"](nsf) then
      local lssj = 取输入背景(0, 0, 70, 23)
      --table.print(lsb)
      字体18["置颜色"](字体18, __取颜色("浅黑"))

      
      if lsb.结果==nil then
        lsb.结果="无"
      end
    if lsb.门派==nil then
      lsb.门派="无"
    end
    if lsb.模型 == '小毛头' then
      __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000001))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/",  0x10000001)), 97, 100)
    elseif lsb.模型 == '小丫丫' then
      __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000002))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000002)), 97, 100)
    elseif lsb.模型 == '小魔头' then
      __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000003))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000003)), 97, 100)
    elseif lsb.模型 == '小精灵' then
      __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000004))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000004)), 97, 100)
    elseif lsb.模型 == '小仙灵' then
      __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000005))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000005)), 97, 100)
    elseif lsb.模型 == '小仙女' then
      __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000006))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000006)), 97, 100)

    end



    
    --  字体18["取图像"](字体18, lsb["养育"]["养育金"])["显示"](字体18["取图像"](字体18, lsb["养育"]["养育金"]), 436, 70)
     -- 字体18["取图像"](字体18, lsb["养育"]["疲劳"])["显示"](字体18["取图像"](字体18, lsb["养育"]["疲劳"]), 556, 70)
    --  字体18["取图像"](字体18, lsb["养育"]["叛逆值"])["显示"](字体18["取图像"](字体18, lsb["养育"]["叛逆值"]), 676, 70)
      字体18["取图像"](字体18, "名称：" .. lsb["名称"])["显示"](字体18["取图像"](字体18, "名称：" .. lsb["名称"]), 390, 270)
     字体18["取图像"](字体18, "成长：" .. lsb["成长"])["显示"](字体18["取图像"](字体18, "成长：" .. lsb["成长"]), 390, 299)
      字体18["取图像"](字体18, "根骨：" .. lsb.培养["根骨"])["显示"](字体18["取图像"](字体18, "根骨：" .. lsb.培养["根骨"]), 390, 328)
      字体18["取图像"](字体18, "智力：" .. lsb.培养["智力"])["显示"](字体18["取图像"](字体18, "智力：" .. lsb.培养["智力"]), 390, 357)
    --  字体18["取图像"](字体18, "定力：" .. lsb["定力"])["显示"](字体18["取图像"](字体18, "定力：" .. lsb["定力"]), 390, 386)
      字体18["取图像"](字体18, "结局：" .. lsb["结果"])["显示"](字体18["取图像"](字体18, "结局：" .. lsb["结果"]), 390, 395)
      字体18["取图像"](字体18, "师门：" .. lsb["门派"])["显示"](字体18["取图像"](字体18, "师门：" .. lsb["门派"]), 535, 270)
    --  字体18["取图像"](字体18, "念力：" .. lsb["念力"])["显示"](字体18["取图像"](字体18, "念力：" .. lsb["念力"]), 535, 299)
      字体18["取图像"](字体18, "武力：" .. lsb.培养["武力"])["显示"](字体18["取图像"](字体18, "武力：" .. lsb.培养["武力"]), 535, 300)
      字体18["取图像"](字体18, "灵敏：" .. lsb.培养["灵敏"])["显示"](字体18["取图像"](字体18, "灵敏：" .. lsb.培养["灵敏"]), 535, 357-28)
      字体18["取图像"](字体18, "忠诚：" .. lsb["忠诚"])["显示"](字体18["取图像"](字体18, "忠诚：" .. lsb["忠诚"]), 535, 386-28)
    end
    self.图像 = nsf["到精灵"](nsf)
  elseif "战斗属性" == lx then
    local nsf = require("SDL.图像")(768, 521)
    if nsf["渲染开始"](nsf) then
      local lssj = 取输入背景(0, 0, 70, 23)
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      if lsb.模型 == '小毛头' then
        __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000001))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/",  0x10000001)), 97, 100)
      elseif lsb.模型 == '小丫丫' then
        __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000002))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000002)), 97, 100)
      elseif lsb.模型 == '小魔头' then
        __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000003))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000003)), 97, 100)
      elseif lsb.模型 == '小精灵' then
        __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000004))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000004)), 97, 100)
      elseif lsb.模型 == '小仙灵' then
        __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000005))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000005)), 97, 100)
      elseif lsb.模型 == '小仙女' then
        __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000006))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000006)), 97, 100)
  
      end

      --字体18["取图像"](字体18, lsb["养育"]["养育金"])["显示"](字体18["取图像"](字体18, lsb["养育"]["养育金"]), 436, 70)
      --字体18["取图像"](字体18, lsb["养育"]["疲劳"])["显示"](字体18["取图像"](字体18, lsb["养育"]["疲劳"]), 556, 70)
      --字体18["取图像"](字体18, lsb["养育"]["叛逆值"])["显示"](字体18["取图像"](字体18, lsb["养育"]["叛逆值"]), 676, 70)
      字体18["取图像"](字体18, "名称：" .. lsb["名称"])["显示"](字体18["取图像"](字体18, "名称：" .. lsb["名称"]), 390, 270)
      字体18["取图像"](字体18, "等级：" .. lsb["等级"])["显示"](字体18["取图像"](字体18, "等级：" .. lsb["等级"]), 390, 299)
      字体18["取图像"](字体18, "气血：" .. lsb["最大气血"])["显示"](字体18["取图像"](字体18, "气血：" .. lsb["最大气血"]), 390, 328)
      字体18["取图像"](字体18, "攻击：" .. lsb["伤害"])["显示"](字体18["取图像"](字体18, "攻击：" .. lsb["伤害"]), 390, 357)
      字体18["取图像"](字体18, "法伤：" .. lsb["灵力"])["显示"](字体18["取图像"](字体18, "法伤：" .. lsb["灵力"]), 390, 386)
      字体18["取图像"](字体18, "速度：" .. lsb["速度"])["显示"](字体18["取图像"](字体18, "速度：" .. lsb["速度"]), 390, 415)
      字体18["取图像"](字体18, "师门：" .. lsb["门派"])["显示"](字体18["取图像"](字体18, "师门：" .. lsb["门派"]), 535, 270)
      字体18["取图像"](字体18, "魔法：" .. lsb["最大魔法"])["显示"](字体18["取图像"](字体18, "魔法：" .. lsb["最大魔法"]), 535, 299)
      字体18["取图像"](字体18, "防御：" .. lsb["防御"])["显示"](字体18["取图像"](字体18, "防御：" .. lsb["防御"]), 535, 328)
      字体18["取图像"](字体18, "法防：" .. lsb["灵力"])["显示"](字体18["取图像"](字体18, "法防：" .. lsb["灵力"]), 535, 357)
    end
    self.图像 = nsf["到精灵"](nsf)
  end
end
local 关闭 = 子女养育["创建我的按钮"](子女养育, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 718, 0)
function 关闭:左键弹起(x, y, msg)
  子女养育["置可见"](子女养育, false)
end
for i, v in ipairs({
  {
    name = "工作",
    x = 530,
    y = 118,
    tcp = __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000172))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000172)), 97, 100),

    font = ""
  },
  {
    name = "学习",
    x = 453,
    y = 118,
    tcp = __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000171))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000171)), 97, 100),
    font = ""
  },
  {
    name = "生活",
    x = 375,
    y = 116,
    tcp = __res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000170))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/smap/", 0x10000170)), 97, 100),
    font = ""
  },
  {
    name = "拜师",
    x = 366,
    y = 420,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 103, 41),
    font = "拜师"
  },
  {
    name = "能力",
    x = 483,
    y = 420,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 103, 41),
    font = "能力"
  },
  {
    name = "加点",
    x = 600,
    y = 420,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 103, 41),
    font = "加点"
  },
}) do
  local 临时函数 = 子女养育["创建我的按钮"](子女养育, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
 if v.name == "生活" then
  发送数据(64.1, {类型="生活",编号= 子女养育["选中子女"]})
elseif v.name == "学习" then
  发送数据(64.1, {类型="学习",编号= 子女养育["选中子女"]}) 
elseif  v.name == "工作" then
  发送数据(64.1, {类型="工作",编号= 子女养育["选中子女"]})  
elseif v.name == "加点"  then
  __UI界面.窗口层.子女加点:置可见(true)
    elseif v.name == "拜师" then
      发送数据(65.2, {
        编号= 子女养育["选中子女"]
      })
    elseif v.name == "能力" then
       发送数据(67.4, {
        编号= 子女养育["选中子女"]
      })
    end
  end
end
for i, v in ipairs({
  {
    name = "养育属性",
    x = 366,
    y = 217,
    tcp = __res:getPNGCC(3, 1040, 201, 149, 37, true)["拉伸"](__res:getPNGCC(3, 1040, 201, 149, 37, true), 120, 41),
    tcp2 = __res:getPNGCC(3, 1039, 160, 148, 36, true)["拉伸"](__res:getPNGCC(3, 1039, 160, 148, 36, true), 120, 41),
    font = "养育属性"
  },
  {
    name = "战斗属性",
    x = 523,
    y = 217,
    tcp = __res:getPNGCC(3, 1040, 201, 149, 37, true)["拉伸"](__res:getPNGCC(3, 1040, 201, 149, 37, true), 120, 41),
    tcp2 = __res:getPNGCC(3, 1039, 160, 148, 36, true)["拉伸"](__res:getPNGCC(3, 1039, 160, 148, 36, true), 120, 41),
    font = "战斗属性"
  }
}) do
  local 临时函数 = 子女养育["创建我的单选按钮"](子女养育, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    子女养育["刷新"](子女养育, v.name)
  end
end
local 头像网格 = 子女养育["创建网格"](子女养育, "头像网格", 0, 66, 70, 150)
function 头像网格:初始化()
  self:创建格子(70, 70, 10, 0, 1, 1)
end
function 头像网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["数据"] then
    if 子女养育["选中子女"] then
      self.子控件[子女养育["选中子女"]]._spr["确定"] = nil
    end
    子女养育["选中子女"] = a
    self.子控件[a]._spr["确定"] = true
    子女养育["养育属性"]["左键弹起"](子女养育["养育属性"])
  end
end
function 头像网格:置头像(数据)
  for i = 1, #头像网格["子控件"] do
    local lssj = __头像格子["创建"]()
    lssj["置头像"](lssj, 数据[i], "大")
    头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i], lssj)
  end
  子女养育["选中子女"] = 1
  self.子控件[1]._spr["确定"] = true
end


local 子女加点 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "子女加点", 258 + abbr.py.x, 60 + abbr.py.y, 377, 300)
function 子女加点:初始化()
  local nsf = require("SDL.图像")(445, 460)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("子女加点", 0, 12, 367, 157+17+58-39+60, true):显示( 0, 0)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35), 72-24-20, 60+60-29+50)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35), 72-24+50, 60+60-29+50)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35), 72-24+120, 60+60-29+50)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35), 72-24+190, 60+60-29+50)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 40, 35), 72-24+260, 60+60-29+50)
  
    字体18:置颜色(__取颜色("白色"))
    字体18:取图像("设定后孩子升级会自定按照设定加点"):显示(32,33+8+16)

    字体18:取图像("体"):显示(72-24-20+10, 60+60-29+50-30)
    字体18:取图像("魔"):显示(72-24-20+10+70, 60+60-29+50-30)
    字体18:取图像("力"):显示(72-24-20+10+140, 60+60-29+50-30)
    字体18:取图像("耐"):显示(72-24-20+10+210, 60+60-29+50-30)
    字体18:取图像("敏"):显示(72-24-20+10+280, 60+60-29+50-30)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 子女加点:打开()
  self.发送时间=os.time()
  self:置可见(true)
  self.shuru:置文本("")
end



local 关闭 = 子女加点["创建我的按钮"](子女加点, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 403-75, 0)
function 关闭:左键弹起(x, y, msg)
  子女加点["置可见"](子女加点, false)
  子女加点["体"]["清空"](子女加点["体"])
  子女加点["魔"]["清空"](子女加点["魔"])
  子女加点["力"]["清空"](子女加点["力"])
  子女加点["耐"]["清空"](子女加点["耐"])
  子女加点["敏"]["清空"](子女加点["敏"])
end



local 体 = 子女加点["创建我的输入"](子女加点, "体", 29, 150, 254, 35, nil, 88, "黑色", 字体20)
local 魔 = 子女加点["创建我的输入"](子女加点, "魔", 99, 150, 254, 35, nil, 88, "黑色", 字体20)
local 力 = 子女加点["创建我的输入"](子女加点, "力", 169, 150, 254, 35, nil, 88, "黑色", 字体20)
local 耐 = 子女加点["创建我的输入"](子女加点, "耐", 239, 150, 254, 35, nil, 88, "黑色", 字体20)
local 敏 = 子女加点["创建我的输入"](子女加点, "敏", 309, 150, 254, 35, nil, 88, "黑色", 字体20)
local 确定 = 子女加点["创建我的按钮"](子女加点, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 130, 41, true), 130, 41), "确定", 153-26, 337-189-25+18+50, "确定")
function 确定:左键弹起(x, y, msg)
  local 加点方式 = {子女加点.体:取文本()+子女加点.体:取文本()+子女加点.体:取文本()+子女加点.体:取文本()+子女加点.体:取文本()}
  if 加点方式 ~= 5 then

    __UI弹出["提示框"]:打开("#Y/加点方式不能超过5点")
    return
  end

  if 子女加点.体:取文本()~= ""  then
      发送数据(66.3,{加点方式={[1]=子女加点.体:取文本(),[2]=子女加点.魔:取文本(),[3]=子女加点.力:取文本(),[4]=子女加点.耐:取文本(),[5]=子女加点.敏:取文本()},编号= 子女加点["选中子女"]})
      __UI界面.窗口层.子女加点:置可见(false)

      子女加点.体:置文本("")
      子女加点.魔:置文本("")
      子女加点.力:置文本("")
      子女加点.耐:置文本("")
      子女加点.敏:置文本("")
      -- table.print(加点方式)
    end

    __UI弹出["提示框"]:打开("#Y/加点已完成")

end
