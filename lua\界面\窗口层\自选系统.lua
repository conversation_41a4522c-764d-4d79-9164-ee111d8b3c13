
local 自选系统 = 窗口层:创建窗口("自选系统", 0,0, 305, 180)
local 选项按钮={"双加1","双加2","特效","特技"}
function 自选系统:初始化()
  self:创建纹理精灵(function()
              置窗口背景("自选灵饰", 0, 0, 305, 180, true):显示(0, 0)
              标题字体:置颜色(255,255,255,255)
              for i, v in ipairs(选项按钮) do
                  if i==1 or i==2 then
                      取输入背景(0, 0, 100, 22):显示(50+(i-1)*145,65)
                      标题字体:取图像("双加:"):显示(10+(i-1)*145,68)
                  else
                      取输入背景(0, 0, 245, 22):显示(50,95+(i-3)*30)
                      标题字体:取图像(选项按钮[i]..":"):显示(10,98+(i-3)*30)
                  end
              end
          end
  )




  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end



 
end
function 自选系统:打开(数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
     return
  end
  self.级别= 数据.级别
  self.部位="随机"
  if 数据.部位~=nil then
     self.部位=数据.部位
  end
  for i, v in ipairs(选项按钮) do
      self[v]="" 
  end
  if __UI弹出.弹出列表.是否可见 then
      __UI弹出.弹出列表:置可见(false)
  end
  self:显示设置()
  self.图像=标题字体:置颜色(255,255,255,255):取精灵("当前部位:  "..self.部位.."    礼包等级: "..self.级别.." 级")

end
function 自选系统:显示(x,y)
    if self.图像 then
       self.图像:显示(x+10,y+40)
    end

    for i, v in ipairs(选项按钮) do
        if self[v.."显示"] then
            if i==1 or i==2 then
                self[v.."显示"]:显示(x+55+(i-1)*145, y+68)
            else
                self[v.."显示"]:显示(x+55, y+98+(i-3)*30)
            end
        end
    end

  
end


function 自选系统:显示设置()
  for i, v in ipairs(选项按钮) do
      if self[v]=="" then
         self[v.."显示"]=nil
      else
          self[v.."显示"]=文本字体:置颜色(0,0,0,255):取精灵(self[v])
      end
  end
    if self.部位=="武器" or self.部位=="衣服" or self.部位=="随机"  then
        self.双加1按钮:置禁止(false)
        self.双加2按钮:置禁止(false)
    else
        self.双加1按钮:置禁止(true)
        self.双加2按钮:置禁止(true)
    end
end




for i, v in ipairs(选项按钮) do
      local 临时按钮=自选系统:创建按钮(v.."按钮")
      if i==1 or i==2 then
          临时按钮:置坐标(130+(i-1)*145,66)
      else
          临时按钮:置坐标(275,96+(i-3)*30)
      end
      function 临时按钮:初始化()
          self:创建按钮精灵(__res:取资源动画("jszy/xjiem",0x00000050),1)
      end
      function 临时按钮:左键弹起(x, y)
              local 列表数据={}
              local 宽=0
              if v=="特技" then
                列表数据 = {"气疗术","心疗术","命疗术","凝气诀","气归术","命归术","四海升平","回魂咒","起死回生",
                            "水清诀","冰清诀","玉清诀","晶清诀","弱点击破","冥王暴杀","放下屠刀","河东狮吼","碎甲术",
                            "破甲术","破血狂攻","慈航普渡","笑里藏刀","罗汉金钟","破碎无双","圣灵之甲","野兽之力","琴音三叠"}
                宽=250
              elseif v=="特效" then
                  列表数据={"无级别限制","神佑","珍宝","必中","神农","简易","绝杀","专注","精致","再生"}
                  宽=250
              else
                  列表数据={"体质","力量","耐力","魔力","敏捷"}
                  if  v =="双加1" and 自选系统.双加2~="" then
                        for n=1,#列表数据 do
                            if 列表数据[n]==自选系统.双加2 then
                                table.remove(列表数据, n)
                                break
                            end
                        end
                  elseif v =="双加2" and 自选系统.双加1~="" then
                        for n=1,#列表数据 do
                            if 列表数据[n]==自选系统.双加1 then
                                table.remove(列表数据, n)
                                break
                            end
                        end
                  end
                  宽=100
              end
              local 事件 =function (a)
                    if 列表数据[a] then
                        自选系统[v] = 列表数据[a]
                        自选系统:显示设置()
                    end
              end
              local xx,yy=self:取坐标()
              __UI弹出.弹出列表:打开(列表数据,nil,事件,xx-宽+20,yy+20,宽,nil,标题字体,5)
      end

end


local 确定 = 自选系统:创建红色按钮("确定","确定", 125, 152,50, 22)  
function 确定:左键弹起(x, y) 
    if 自选系统.特技== nil or 自选系统.特技==""  then
      __UI弹出.提示框:打开('#Y请选择特技！')
		elseif 自选系统.特效== nil or 自选系统.特效==""  then
      __UI弹出.提示框:打开('#Y请选择特效！')
		else
      local 发送内容={等级=自选系统.级别}
      for i, v in ipairs(选项按钮) do
            发送内容[v]=自选系统[v]
      end
		  请求服务(3740,发送内容)
      自选系统:置可见(false)
		end
    
  end


  local 关闭 = 自选系统:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    自选系统:置可见(false)
  end



