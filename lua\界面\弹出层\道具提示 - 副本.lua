if __手机 then
   __UI弹出.道具提示 = 界面:创建弹出窗口("道具提示", 0, 0, 0, 0)
else
   __UI弹出.道具提示 = 界面:创建提示控件("道具提示", 0, 0)
end

--创建提示控件


local 道具提示 = __UI弹出.道具提示



local 神话属性={
  洪荒力魄={描述="物伤结果提高4%",部位="所有部位",限制="无"},
  太初神念={描述="法伤结果提高4%",部位="所有部位",限制="无"},
  鸿蒙元魂={描述="固伤结果提高4%",部位="所有部位",限制="无"},
  无极命源={描述="治疗结果提高4%",部位="所有部位",限制="无"},
  不灭金身={描述="受到物伤消弱4%",部位="所有部位",限制="无"},
  法天象地={描述="受到法伤削弱4%",部位="所有部位",限制="无"},
  道法自然={描述="技能连击提高4%",部位="所有部位",限制="无"},
  超然物外={描述="受到固伤消弱4%",部位="所有部位",限制="无"},
  无相法门={描述="封印几率提高5%",部位="所有部位",限制="无"},
  飞天遁地={描述="抗封几率提高5%",部位="所有部位",限制="无"},
  兽语御灵={描述="召唤兽造成的物理、法术伤害提高8%",部位="头盔|项链",限制="无"},
  奇经山海={描述="对召唤兽造成的物理、法术伤害削弱8%",部位="头盔|项链",限制="无"},
  连破六合={描述="横扫千军有15%几率不进入“休息”状态",部位="武器|衣服",限制="大唐官府"},
  无双剑意={描述="物理伤害结果增加8%",部位="武器|衣服",限制="大唐官府"},
  十方光明={描述="推气过宫有12%几率触发满血,失败后附加15%治疗气血加成",部位="武器|衣服",限制="化生寺"},
  万佛朝宗={描述="推气过宫有18%几率触发连击",部位="武器|衣服",限制="化生寺"},
  花语倾城={描述="战斗开始时有12%几率速度增加15%,失败后增加角色等级的速度",部位="武器|衣服",限制="女儿村"},
  一笑倾国={描述="战斗开始时有12%几率伤害增加15%,失败后增加角色等级的伤害",部位="武器|衣服",限制="女儿村"},
  卫灵神咒={描述="战斗开始时有12%几率提高8%封印命中等级,失败后增加角色等级*0.6的封印命中等级",部位="武器|衣服",限制="方寸山"},
  雷法天尊={描述="法术伤害结果增加8%",部位="武器|衣服",限制="方寸山"},
  霹霹电芒={描述="雷霆万钧和天雷斩有15%几率额外作用1个单位",部位="武器|衣服",限制="天宫"},
  天神下凡={描述="雷霆万钧和天雷斩有15几率触发连击",部位="武器|衣服",限制="天宫"},
  慧眼观世={描述="战斗开始时有12%几率固定伤害增加8%,失败后增加角色等级/2的固定伤害",部位="武器|衣服",限制="普陀山"},
  九天玄女={描述="普渡众生有15%几率额外作用1个单位",部位="武器|衣服",限制="普陀山"},
  沧溟龙魄={描述="战斗开始时有10%几率法术伤害增加8%,失败后增加角色等级*0.8的法术伤害",部位="武器|衣服",限制="龙宫"},
  蛟龙出海={描述="龙卷雨击有12%几率触发连击",部位="武器|衣服",限制="龙宫"},
  潇湘骤雨={描述="烟雨剑法有15%几率增加一次攻击,飘渺式有15%几率额外作用1个单位",部位="武器|衣服",限制="五庄观"},
  地仙老祖={描述="日月乾坤有15%几率增加10%基础封印命中,天命剑法50%触发5次以上攻击",部位="武器|衣服",限制="五庄观"},
  鹰抟九天={描述="物理伤害结果增加10%",部位="武器|衣服",限制="狮驼岭"},
  鹏程万里={描述="鹰击有16%几率不休息",部位="武器|衣服",限制="狮驼岭"},
  五火神焰={描述="法术伤害结果增加10%",部位="武器|衣服",限制="魔王寨"},
  魔王在世={描述="飞砂走石有15%几率连击一次,有8%几率额外作用1个单位",部位="武器|衣服",限制="魔王寨"},
  无常索命={描述="战斗开始时有12%几率提高10%固定伤害,失败后增加角色等级的固定伤害",部位="武器|衣服",限制="阴曹地府"},
  钟馗转生={描述="物理伤害结果提升10%",部位="武器|衣服",限制="阴曹地府"},
  天网蛛狩={描述="战斗开始时有12%几率提高8%封印命中等级,失败后增加角色等级/2的封印命中等级",部位="武器|衣服",限制="盘丝洞"},
  九尾天狐={描述="含情脉脉有16%几率无视对方抗封等级直接命中",部位="武器|衣服",限制="盘丝洞"},
  月影重光={描述="法术攻击有18%几率触发连击",部位="武器|衣服",限制="神木林"},
  月神附体={描述="治疗类技能结果有12%几率翻倍,失败后附加相应等级*2的治疗量",部位="武器|衣服",限制="神木林"},
  超级战息={描述="战斗开始时有15%几率提高8%物理暴击等级,失败后增加角色等级*0.6的物理暴击等级",部位="武器|衣服",限制="凌波城"},
  劈山救母={描述="浪涌有16%几率额外作用1个单位",部位="武器|衣服",限制="凌波城"},
  烛照九幽={描述="夺命咒有12%几率触发连击",部位="武器|衣服",限制="无底洞"},
  藕断丝连={描述="地涌金莲有12%几率额外作用1个单位",部位="武器|衣服",限制="无底洞"},
  逐鹿中原={描述="物理伤害结果增加5%",部位="武器|衣服",限制="九黎城"},
  五马分尸={描述="每次增加连击值时有8%几率+1连击值",部位="武器|衣服",限制="九黎城"}
}

local 境界属性={
    健体={类型="人物体质",分类="所有部位",共鸣=false,优秀=6,稀有=12,传说=24,神话=24},
    千钧={类型="人物力量",分类="所有部位",共鸣=false,优秀=6,稀有=12,传说=24,神话=24},
    疾风={类型="人物敏捷",分类="所有部位",共鸣=false,优秀=6,稀有=12,传说=24,神话=24},
    灵脉={类型="人物魔力",分类="所有部位",共鸣=false,优秀=6,稀有=12,传说=24,神话=24},
    坚韧={类型="人物耐力",分类="所有部位",共鸣=false,优秀=6,稀有=12,传说=24,神话=24},


    尖锋={类型="人物伤害",分类="头盔|武器|腰带",共鸣=true,优秀=12,稀有=24,传说=48,神话=48},
    攻心={类型="法术伤害",分类="头盔|武器|腰带",共鸣=true,优秀=12,稀有=24,传说=48,神话=48},
    破竹={类型="穿刺等级",分类="头盔|武器|腰带",共鸣=true,优秀=12,稀有=24,传说=48,神话=48},
    摧破={类型="固定伤害",分类="头盔|武器|腰带",共鸣=true,优秀=12,稀有=24,传说=48,神话=48},
    杀意={类型="物理暴击",分类="头盔|武器|腰带",共鸣=true,优秀=8,稀有=20,传说=40,神话=40},
    会心={类型="法术暴击",分类="头盔|武器|腰带",共鸣=true,优秀=8,稀有=20,传说=40,神话=40},
    无双={类型="法伤结果",分类="头盔|武器|腰带",共鸣=true,优秀=8,稀有=20,传说=40,神话=40},
    桎梏={类型="封印命中",分类="头盔|武器|腰带",共鸣=true,优秀=8,稀有=20,传说=40,神话=40},
    金身={类型="抗法术暴击",分类="头盔|武器|腰带",共鸣=true,优秀=16,稀有=32,传说=64,神话=64},


    血脉={类型="人物气血",分类="项链|衣服|鞋子",共鸣=true,优秀=40,稀有=80,传说=160,神话=160},
    神速={类型="人物速度",分类="项链|衣服|鞋子",共鸣=true,优秀=10,稀有=18,传说=36,神话=36},
    厚甲={类型="人物防御",分类="项链|衣服|鞋子",共鸣=true,优秀=10,稀有=18,传说=36,神话=36},
    化劲={类型="格挡值",分类="项链|衣服|鞋子",共鸣=true,优秀=10,稀有=18,传说=36,神话=36},
    护心={类型="法术防御",分类="项链|衣服|鞋子",共鸣=true,优秀=10,稀有=18,传说=36,神话=36},
    战神={类型="狂暴等级",分类="项链|衣服|鞋子",共鸣=true,优秀=10,稀有=18,传说=36,神话=36},
    济世={类型="治疗能力",分类="项链|衣服|鞋子",共鸣=true,优秀=10,稀有=18,传说=36,神话=36},
    无羁={类型="抵抗封印",分类="项链|衣服|鞋子",共鸣=true,优秀=16,稀有=32,传说=64,神话=64},
    无敌={类型="抗物理暴击",分类="项链|衣服|鞋子",共鸣=true,优秀=16,稀有=32,传说=64,神话=64}
}





function 道具提示:初始化()
end


-- function 道具提示:左键弹起()
--   self:置可见(false)
-- end
function 道具提示:打开(数据,x,y,类型,编号)
  self:置可见(true)
  local _,h = 0,0
  local h1 = 0
  local h2 = 0
  local h3 = 0
  self.横杠=nil
  self.编号= nil
  self.名称 = nil
  self.数据 = 数据
  self.宽度 = 360
  self.高度 = 140
  self.顶部文本:置宽度(210)
  self.顶部文本:置文字(文本字体):清空()
  self.下边文本:置文字(文本字体):置可见(false):清空()
  self.右边文本:置文字(文本字体):置可见(false):清空()
  local 取提示 = self:道具提示(数据)
  for i, v in ipairs(取提示.顶部) do
      _,h=self.顶部文本:置文本(v)
  end
  h3=h+50
  self.下边文本:置坐标(140,h3+2)
  if 数据.总类==2 then
      self.下边文本:置可见(true)
     if  not 取提示.右边 or (取提示.右边 and not 取提示.右边[2]) then
            for i, v in ipairs(取提示.下边) do
              _,h1 =self.下边文本:置文本(v)
            end
            self.下边文本:置高度(h1)
            h3=h3+h1
      else
              self.顶部文本:清空()
              self.顶部文本:置宽高(365,140)
              for i, v in ipairs(取提示.顶部) do
                _,h=self.顶部文本:置文本(v)
              end
              self.右边文本:置可见(true)
              self.宽度 = self.宽度 + 150
              self.横杠=true
              for i, v in ipairs(取提示.下边) do
                  _,h1 =self.下边文本:置文本(v)
              end
              self.下边文本:置高度(h1)
              self.下边文本:置坐标(20,140)
              for i, v in ipairs(取提示.右边) do
                _,h2 =self.右边文本:置文本(v)
              end
              self.右边文本:置高度(h2)
              if h1<=h2 then
                  h3= 140+h2
              else
                  h3= 140+h1
              end
      end
  end
  self.顶部文本:置高度(h)
  if self.高度<h3+10 then
     self.高度 = h3+10
  end
  self.卸下:置可见(false)
  self.拆分:置可见(false)
  self.丢弃:置可见(false)
  self.符石:置可见(false)
  self.替换:置可见(false)
  if __手机 then
      self:按钮配置(类型,编号)
  end
  if x+self.宽度 >引擎.宽度 then
      x = 引擎.宽度 - self.宽度- 5
  elseif x<0 then
      x = 0
  end
  if y+self.高度 >引擎.高度 then
      y = 引擎.高度 - self.高度- 5
  elseif y<0 then
      y = 0
  end
  self:提示显示(x,y,self.宽度, self.高度)
 
end








function 道具提示:提示显示(x,y,w,h)
    local nsf = 取九宫图像(__res:取资源动画("dlzy", 0xB5FDF1AC,"图像"),w,h,20,true)
    if nsf:渲染开始() then
        if self.数据.装备境界 then
            if self.数据.装备境界.品质=="优秀" then
                  取九宫图像(__res:取资源动画("jszy/zbpjsjxt", 0x00000033,"图像"),w,h,30,true):显示(0, 0)
            elseif self.数据.装备境界.品质=="稀有" then
                    取九宫图像(__res:取资源动画("jszy/zbpjsjxt", 0x00000032,"图像"),w,h,30,true):显示(0, 0)
            elseif self.数据.装备境界.品质=="传说" then
                    取九宫图像(__res:取资源动画("jszy/zbpjsjxt", 0x00000031,"图像"),w,h,30,true):显示(0, 0)
            elseif self.数据.装备境界.品质=="神话" then
                    取九宫图像(__res:取资源动画("jszy/zbpjsjxt", 0x00000034,"图像"),w,h,30,true):显示(0, 0)
            end
        end
        if self.横杠 then
            __res:取资源动画("dlzy", 0x381EAF65,"图像"):拉伸(480,2):显示(15, 132)
        end
        if self.数据 and self.数据.资源 and self.数据.大模型资源 then
            __res:取资源动画( self.数据.资源, self.数据.大模型资源,"图像"):显示(10, 10)
        end
        if  self.名称 then
          道具字体:置颜色(252, 252, 8)
          道具字体:取图像(self.名称,252, 252, 8,180):显示(140,20)
        end
      nsf:渲染结束()
    end
    self:置精灵(nsf:到精灵())
    self:置坐标(x, y)
    self:置宽高(w,h)

end





local 顶部文本 = 道具提示:创建文本("顶部文本", 140, 50, 210,0)--x,y,w,h
local 下边文本 = 道具提示:创建文本("下边文本", 140, 70, 210,0)
local 右边文本 = 道具提示:创建文本("右边文本", 245, 140, 250,0)



local 按钮设置={"卸下","拆分","丢弃","符石","替换"}
for i, v in ipairs(按钮设置) do
    local 临时按钮 = 道具提示:创建蓝色按钮(v,v, 0, 0,100,35,标题字体)
    function 临时按钮:左键弹起(x,y)
        local 包裹类型 = 窗口层.新行囊.包裹类型 
        local 选中编号 = 窗口层.新行囊.选中编号
        local 窗口类型 = 窗口层.新行囊.窗口类型
        if __res.配置.行囊==1 then
            包裹类型 = 窗口层.道具行囊.包裹类型 
            选中编号 = 窗口层.道具行囊.选中编号
            窗口类型 = 窗口层.道具行囊.窗口类型
        end
        if 道具提示.类型 and 道具提示.编号 and 道具提示.编号~=0 then
            if  v=="拆分" and 道具提示.类型=="道具"  and 道具提示.类型==包裹类型 then
                  请求服务(88,{编号=道具提示.编号,类型=道具提示.类型})
                  --  __UI弹出.组合输入框:打开("拆分处理",{"请输入 "..道具提示.数据.名称.." 分离的数量","白色",道具提示.编号,包裹类型,道具提示.数据.数量})
            elseif  v=="丢弃" and 道具提示.类型=="道具" and 道具提示.类型==包裹类型  then
                    窗口层.文本栏:打开("确认#R销毁#Y" .. 道具提示.数据.名称 .. "#W?", 3702, {
                        物品 = 道具提示.编号,
                        类型 = 包裹类型
                      })
            elseif  v=="卸下" then   
                    if 道具提示.类型=="装备法宝"  or 道具提示.类型=="装备灵宝"   then
                        if  道具提示.类型=="装备法宝"   then
                            请求服务(3734,{序列=道具提示.编号})
                        else
                            请求服务(3734.1,{序列=道具提示.编号})
                        end
                    else
                        if 窗口类型=="主人公"  then
                            if 道具提示.数据.灵饰 then 
                                请求服务(3704,{类型=包裹类型,灵饰=true,角色="主角",道具=道具提示.编号})
                            elseif 道具提示.数据.分类<=6 then
                                  请求服务(3704,{类型=包裹类型,角色="主角",道具=道具提示.编号})
                            else
                                请求服务(3704,{类型=包裹类型,锦衣=true,角色="主角",道具=道具提示.编号})
                            end
                        elseif 窗口类型=="召唤兽" and 选中编号  then
                                请求服务(3709,{类型=包裹类型,角色="bb",道具=道具提示.编号,编号=选中编号})
                        elseif 窗口类型=="坐骑" and 选中编号  then
                                请求服务(3756,{类型=1,角色=1,道具=1,编号=选中编号 })
                        end
                    end
            elseif  v=="符石" and 道具提示.类型=="道具" and 道具提示.类型==包裹类型 then
                      请求服务(3800,{装备=道具提示.编号,类型=道具提示.类型})
                
            elseif  v=="替换" and 窗口层.符石镶嵌.是否可见 then
                          窗口层.符石镶嵌:替换符石(道具提示.编号)
                       
                end
        end
        if 鼠标层.附加 and ggetype(鼠标层.附加)=="物品格子"  then
            鼠标层.附加=nil
        end
        道具提示:置可见(false)

    end
end



function 道具提示:按钮配置(类型,编号)
  self.类型 = 类型
  self.编号 = 编号
  if  self.类型 and self.数据 then
        if self.数据.数量 and self.类型=="道具" then
            self.拆分:置可见(true)
        end
        if self.类型=="道具" then
            self.丢弃:置可见(true)
            local 窗口类型 = 窗口层.新行囊.窗口类型
            if __res.配置.行囊==1 then
                窗口类型 = 窗口层.道具行囊.窗口类型
            end
            if self.数据.总类==2 and not self.数据.灵饰 and self.数据.分类<=6 and self.数据.开运孔数 
                and self.数据.开运孔数.当前 and self.数据.开运孔数.当前  ~= 0 and 窗口类型=="主人公" then
                self.符石:置可见(true)
            end
        elseif (self.类型=="卸下" or self.类型=="装备法宝"  or self.类型=="装备灵宝") and self.编号 then
                self.卸下:置可见(true)
        elseif self.类型=="镶嵌符石"  then
                self.替换:置可见(true)
        end
      if self.横杠 then
          self.符石:置坐标(280,self.高度)
          self.丢弃:置坐标(390,self.高度)
          self.卸下:置坐标(390,self.高度)
      else
            if self.类型=="道具" then
                self.拆分:置坐标(130,self.高度)
            end
            self.符石:置坐标(130,self.高度)
            self.丢弃:置坐标(240,self.高度)
            self.替换:置坐标(240,self.高度)
            if self.类型=="装备法宝" or self.类型=="装备灵宝"  then
                self.卸下:置坐标(240,self.高度)
            else
                self.卸下:置坐标(20,self.高度-50)
            end
      end
      if self.类型~="卸下"  or  self.横杠 or self.类型=="装备法宝" or self.类型=="装备灵宝" or self.类型=="道具" then
          self.高度 = self.高度 + 45
      end
  end
end









function 道具提示:道具提示 (item)
          if item == nil then
              return
          end
          local 顶部寄存={}
          local 下边寄存={}
          local 右边寄存={}
          --顶部寄存[#顶部寄存+1]="#Y" .. item.名称
          self.名称=item.名称
          顶部寄存[#顶部寄存+1]=item.介绍 or ""
          local zls = item.总类
          for i, v in ipairs(__取物品功能(item)) do
            顶部寄存[#顶部寄存+1]=v
          end
          if 1 == zls then
              if 10 == item.分类 and 2 == item.子类 then
                顶部寄存[#顶部寄存+1]="#Y【效果】:" .. item.阶品
              end
          elseif zls == 67 then
                顶部寄存[#顶部寄存+1]="#Y等级 "..item.级别限制
          elseif zls == 65 then
                顶部寄存[#顶部寄存+1]="#Y右键直接打开幻化界面"
          elseif zls == 66 then
                顶部寄存[#顶部寄存+1]="#Y等级 "..item.级别限制
                local sx = ""
                local 气血 = item.幻化元身属性.气血
                local 魔法 = item.幻化元身属性.魔法
                local 命中 = item.幻化元身属性.命中
                local 伤害 = item.幻化元身属性.伤害
                local 防御 = item.幻化元身属性.防御
                local 速度 = item.幻化元身属性.速度
                local 躲避 = item.幻化元身属性.躲避
                local 灵力 = item.幻化元身属性.灵力
                local 体质 = item.幻化元身属性.体质
                local 魔力 = item.幻化元身属性.魔力
                local 力量 = item.幻化元身属性.力量
                local 耐力 = item.幻化元身属性.耐力
                local 敏捷 = item.幻化元身属性.敏捷
                if 气血 ~= 0 and 气血 ~= nil then
                  sx = sx.."气血 +"..气血.." "
                end
                if 魔法 ~= 0 and 魔法 ~= nil then
                  sx = sx.."魔法 +"..魔法.." "
                end
                if 命中 ~= 0  and 命中 ~= nil then
                  sx = sx.."命中 +"..命中.." "
                end
                if 伤害 ~= 0 and 伤害 ~= nil then
                  sx = sx.."伤害 +"..伤害.." "
                end
                if 防御 ~= 0 and 防御 ~= nil  then
                  sx = sx.."防御 +"..防御.." "
                end
                if 速度 ~= 0 and 速度 ~= nil  then
                  sx = sx.."速度 +"..速度.." "
                end
                if 灵力 ~= 0 and 灵力 ~= nil  then
                  sx = sx.."灵力 +"..灵力.." "
                end
                if 敏捷 ~= 0 and 敏捷~=nil and item.元身序列 == 25 then
                  sx = sx.."敏捷 +"..敏捷.." "
                end
                if sx ~= "" then
                  下边寄存[#下边寄存+1]="#Y"..sx
                end

                local ds = ""
                if 体质 ~= nil and 体质 ~= 0 then
                  ds = ds..体质.." "
                end
                if 魔力 ~= nil and 魔力 ~= 0  then
                  ds = ds..魔力.." "
                end
                if 力量 ~= nil and 力量 ~= 0 then
                  ds = ds..力量.." "
                end
                if 耐力 ~= nil and 耐力 ~= 0 then
                  ds = ds..耐力.." "
                end
                if 敏捷 ~= nil and 敏捷 ~= 0 and item.元身序列 ~= 25 then
                  ds = ds..敏捷.." "
                end
                if ds ~= "" then
                  顶部寄存[#顶部寄存+1]="#G"..ds
                end
                if item.幻化元身属性.特技 ~= nil then
                  顶部寄存[#顶部寄存+1]="#S"..item.幻化元身属性.特技
                end
                if item.幻化元身属性.特效 ~= nil then
                  顶部寄存[#顶部寄存+1]="#S"..item.幻化元身属性.特效
                end
                if item.幻化次数 ~= nil then
                  顶部寄存[#顶部寄存+1]="#Y幻化次数："..item.幻化次数
                end
                顶部寄存[#顶部寄存+1]=" "
            elseif zls == 2 then
                  if item.灵饰 then
                      顶部寄存[#顶部寄存+1]="【装备条件】等级"..item.级别限制
                      顶部寄存[#顶部寄存+1]="【灵饰类型】"..item.部位类型
                      if item.鉴定 then
                          下边寄存[#下边寄存+1]="#Y等级 "..item.级别限制
                          下边寄存[#下边寄存+1]="#Y"..item.幻化属性.基础.类型.." +"..item.幻化属性.基础.数值
                          if item.耐久 ~= nil then
                              item.耐久=math.floor(item.耐久)
                              if item.修理失败~=nil and item.修理失败~=0 then
                                下边寄存[#下边寄存+1]=string.format("#Y耐久度：%s  修理失败 %s次",item.耐久,item.修理失败)
                              else
                                下边寄存[#下边寄存+1]=string.format("#Y耐久度 "..item.耐久)
                              end
                              if item.专用 ~= nil then
                                下边寄存[#下边寄存+1]=string.format("#Y不可交易")
                              end
                          end
                          if item.特效 ~= nil then
                            下边寄存[#下边寄存+1]=string.format("#S特效：%s",item.特效)
                          end
                          if item.幻化等级>0 then  
                            下边寄存[#下边寄存+1]="#Y精炼等级 "..item.幻化等级 
                          end
                          for n=1,#item.幻化属性.附加 do
                              if item.幻化等级==0 then
                                下边寄存[#下边寄存+1]="#G"..item.幻化属性.附加[n].类型.." +"..item.幻化属性.附加[n].数值
                              else
                                下边寄存[#下边寄存+1]="#G"..item.幻化属性.附加[n].类型.." +"..item.幻化属性.附加[n].数值.." #X[+"..item.幻化属性.附加[n].强化.."]"
                              end
                          end
                          if item.制造者~=nil then
                                下边寄存[#下边寄存+1]="制造者："..item.制造者
                          end
                          if item.附加特性 ~= nil and item.附加特性.幻化等级 ~= nil then
                                下边寄存[#下边寄存+1]="#S特效： "..item.附加特性.幻化类型.."("..item.附加特性.幻化等级.."级)"
                                if item.附加特性.幻化类型 == "心源" then
                                    下边寄存[#下边寄存+1]="#S物理防御 +"..(item.附加特性.幻化等级*2)
                                elseif item.附加特性.幻化类型=="气血方刚" then
                                    下边寄存[#下边寄存+1]="#S气血上限 +"..(item.附加特性.幻化等级*7)
                                elseif item.附加特性.幻化类型=="通真达灵" then
                                    下边寄存[#下边寄存+1]="#S法术防御 +"..(item.附加特性.幻化等级*2)
                                elseif item.附加特性.幻化类型=="心无旁骛" then
                                    下边寄存[#下边寄存+1]="#S抵抗封印等级 +"..(item.附加特性.幻化等级*2)
                                elseif item.附加特性.幻化类型=="健步如飞" then
                                    下边寄存[#下边寄存+1]="#S速度 +"..(item.附加特性.幻化等级*1)
                                elseif item.附加特性.幻化类型=="回春之术" then
                                    下边寄存[#下边寄存+1]="#S治疗能力 +"..(item.附加特性.幻化等级*1)
                                elseif item.附加特性.幻化类型=="风雨不动" then
                                    下边寄存[#下边寄存+1]="#S抗法术暴击等级 +"..(item.附加特性.幻化等级*2)
                                elseif item.附加特性.幻化类型=="固若金汤" then
                                    下边寄存[#下边寄存+1]="#S抗物理暴击等级 +"..(item.附加特性.幻化等级*2)
                                elseif item.附加特性.幻化类型=="气壮山河" then
                                    下边寄存[#下边寄存+1]="#S气血回复效果 +"..(item.附加特性.幻化等级*1)
                                elseif item.附加特性.幻化类型=="锐不可当" then
                                    下边寄存[#下边寄存+1]="#S固定伤害 +"..(item.附加特性.幻化等级*1)
                                end
                                if item.灵饰套装~=nil then
                                    if item.灵饰套装<2 then
                                        local 文本 = 取钟灵石套装(item.附加特性.幻化类型,item.灵饰套装)
                                        下边寄存[#下边寄存+1]="#C【套装效果】"..文本.."(未生效)["..item.灵饰套装.."\\2]"
                                    elseif item.灵饰套装<=3 then
                                        local 文本 = 取钟灵石套装(item.附加特性.幻化类型,item.灵饰套装)
                                        下边寄存[#下边寄存+1]="#S【套装效果】"..文本.."(已生效)["..item.灵饰套装.."\\2]"
                                        local 文本1 = 取钟灵石套装(item.附加特性.幻化类型,4)
                                        下边寄存[#下边寄存+1]="#C【套装效果】"..文本1.."(未生效)["..item.灵饰套装.."\\4]"
                                    else
                                        local 文本 = 取钟灵石套装(item.附加特性.幻化类型,4)
                                        下边寄存[#下边寄存+1]="#S【套装效果】"..文本.."(已生效)["..item.灵饰套装.."\\4]"
                                    end
                                end
                            end
                      else
                          下边寄存[#下边寄存+1]="#G未鉴定物品"
                      end
                  elseif item.分类 == 15 or item.分类 == 16 or item.分类 == 17 or item.分类 == 18 then
                          if item.分类 == 15 then
                            下边寄存[#下边寄存+1]="#S气血 +10%"
                            下边寄存[#下边寄存+1]="#S魔法 +10%"
                            下边寄存[#下边寄存+1]="#S伤害 +5%"
                            下边寄存[#下边寄存+1]="#S法伤 +5%"
                          elseif item.分类 == 16 then
                            下边寄存[#下边寄存+1]="#S防御 +5%"
                            下边寄存[#下边寄存+1]="#S法防 +5%"
                          elseif item.分类 == 17 then
                            下边寄存[#下边寄存+1]="#S速度 +8%"
                          end
                  elseif  not item.灵饰 then
                        local 介绍长度 = string.len(item.介绍)
                        if item.鉴定 then
                              if item.分类 == 3 then
                                    顶部寄存[#顶部寄存+1]="#W【装备条件】等级"..item.级别限制
                                    if item.角色限制[2] == nil then
                                        顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1]
                                    elseif item.角色限制[3] == nil then
                                        顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1].."、"..item.角色限制[2]
                                    elseif item.角色限制[4] == nil then
                                        顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1].."、"..item.角色限制[2].."、"..item.角色限制[3]
                                        介绍长度 = 介绍长度 + 26
                                    else
                                        顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1].."、"..item.角色限制[2].."、"..item.角色限制[3].."、"..item.角色限制[4]
                                        介绍长度 = 介绍长度 + 26
                                    end
                              elseif item.分类 == 1 then
                                      顶部寄存[#顶部寄存+1]="#W【装备条件】等级"..item.级别限制
                                      顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.性别限制
                              elseif item.分类 == 4 then
                                    顶部寄存[#顶部寄存+1]="#W【装备条件】等级"..item.级别限制
                                    if  string.find(item.名称,"(坤)") and item.角色限制 and item.角色限制[1]=="影精灵" then
                                        顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1]
                                    else
                                        顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.性别限制
                                    end
                              elseif item.分类 > 6 and  item.分类 < 10 then
                                    顶部寄存[#顶部寄存+1]="#W【角色限制】召唤兽"
                              elseif item.分类 >= 10 then
                                    顶部寄存[#顶部寄存+1]="#W【装备等级】"..item.级别限制
                              else
                                    顶部寄存[#顶部寄存+1]="#W【装备条件】等级"..item.级别限制
                                    顶部寄存[#顶部寄存+1]="#W【装备角色】无"
                              end
                              if 介绍长度<116 then
                                  顶部寄存[#顶部寄存+1]="#Y等级 "..item.级别限制.." 五行 "..item.五行
                              else
                                  下边寄存[#下边寄存+1]="#Y等级 "..item.级别限制.." 五行 "..item.五行
                              end
                              local 基础显示 =""
                              if item.分类==1 then
                                  if item.魔法 ~= 0 and item.魔法 ~= nil  then
                                      基础显示 = 基础显示.."#Y魔法 +"..item.魔法.." "
                                  end
                                  if item.防御 ~= 0 and item.防御 ~= nil   then
                                        基础显示 = 基础显示.."#Y防御 +"..item.防御.." "
                                  end
                              elseif item.分类==2 then
                                  if item.灵力 ~= 0 and item.灵力 ~= nil  then
                                      基础显示 =基础显示.."#Y灵力 +"..item.灵力.." "
                                  end
                              elseif item.分类==3 then
                                  if item.命中 ~= 0 and item.命中 ~= nil  then
                                    基础显示 =基础显示.."#Y命中 +"..item.命中.." "
                                  end
                                  if item.伤害 ~= 0 and item.伤害 ~= nil  then
                                    基础显示 =基础显示.."#Y伤害 +"..item.伤害.." "
                                  end
                              elseif item.分类==4 then
                                      if  string.find(item.名称,"(坤)") and item.角色限制 and item.角色限制[1]=="影精灵" then
                                            if item.命中 ~= 0 and item.命中 ~= nil  then
                                              基础显示 =基础显示.."#Y命中 +"..item.命中.." "
                                            end
                                            if item.伤害 ~= 0 and item.伤害 ~= nil  then
                                              基础显示 =基础显示.."#Y伤害 +"..item.伤害.." "
                                            end
                                        else
                                            if item.防御 ~= 0 and item.防御 ~= nil  then
                                                基础显示 =基础显示.."#Y防御 +"..item.防御.." "
                                            end
                                        end
                              elseif item.分类==5 then
                                  if item.气血 ~= 0 and item.气血 ~= nil  then
                                    基础显示 =基础显示.."#Y气血 +"..item.气血.." "
                                  end
                                  if item.防御 ~= 0 and item.防御 ~= nil  then
                                    基础显示 =基础显示.."#Y防御 +"..item.防御.." "
                                  end
                              elseif item.分类==6 then
                                  if item.敏捷 ~= 0 and item.敏捷 ~= nil  then
                                    基础显示 =基础显示.."#Y敏捷 +"..item.敏捷.." "
                                  end
                                  if item.防御 ~= 0 and item.防御 ~= nil  then
                                      基础显示 =基础显示.."#Y防御 +"..item.防御.." "
                                  end
                              elseif item.分类>=7 and item.分类<=9 then
                                  if item.气血 ~= 0 and item.气血 ~= nil  then
                                      基础显示 =基础显示.."#Y气血 +"..item.气血.." "
                                  end
                                  if item.魔法 ~= 0 and item.魔法 ~= nil  then
                                    基础显示 =基础显示.."#Y魔法 +"..item.魔法.." "
                                  end
                                  if item.分类==7 then
                                      if item.命中 ~= 0 and item.命中 ~= nil  then ---- 伤害
                                        基础显示 =基础显示.."#Y命中 +"..item.命中.." "
                                      end
                                  elseif item.分类==8 then
                                      if item.速度 ~= 0 and item.速度 ~= nil  then ---- 伤害
                                        基础显示 =基础显示.."#Y速度 +"..item.速度.." "
                                      end
                                  elseif  item.分类==9 then
                                      if item.防御 ~= 0 and item.防御 ~= nil  then ---- 伤害
                                        基础显示 =基础显示.."#Y防御 +"..item.防御.." "
                                      end
                                  end
                                  if item.伤害 ~= 0 and item.伤害 ~= nil  then
                                      基础显示 =基础显示.."#Y伤害 +"..item.伤害.." "
                                  end
                                  if item.灵力 ~= 0 and item.灵力 ~= nil  then
                                      基础显示 =基础显示.."#Y灵力 +"..item.灵力.." "
                                  end
                              end
                              if 基础显示~="" then 
                                  if 介绍长度<58 then
                                      顶部寄存[#顶部寄存+1]=基础显示
                                   else
                                      下边寄存[#下边寄存+1]=基础显示
                                  end
                              end
                              local 属性显示 =""
                            if item.分类==3 or item.分类==4  or item.分类==7 or item.分类==8 or item.分类==9 then
                                  if item.分类==7 then
                                        if item.速度 ~= 0 and item.速度 ~= nil  then
                                            属性显示 =属性显示.."#G速度 +"..item.速度.." "
                                        end
                                        if item.防御 ~= 0 and item.防御 ~= nil  then
                                             属性显示 =属性显示.."#G防御 +"..item.防御.." "
                                        end
                                  elseif  item.分类==8 then
                                        if item.防御 ~= 0 and item.防御 ~= nil  then
                                             属性显示 =属性显示.."#G防御 +"..item.防御.." "
                                        end
                                        if item.命中 ~= 0 and item.命中 ~= nil  then
                                             属性显示 =属性显示.."#G命中 +"..item.命中.." "
                                        end
                                  elseif  item.分类==9 then
                                        if item.命中 ~= 0 and item.命中 ~= nil  then
                                             属性显示 =属性显示.."#G命中 +"..item.命中.." "
                                        end
                                        if item.速度 ~= 0 and item.速度 ~= nil  then
                                            属性显示 =属性显示.."#G速度 +"..item.速度.." "
                                        end
                                  end
                                  if item.体质 ~= 0 and item.体质 ~= nil  then
                                      属性显示 =属性显示.."#G体质 +"..item.体质.." "
                                  end
                                  if item.魔力 ~= 0 and item.魔力 ~= nil  then
                                      属性显示 =属性显示.."#G魔力 +"..item.魔力.." "
                                  end
                                  if item.力量 ~= 0 and item.力量 ~= nil  then
                                      属性显示 =属性显示.."#G力量 +"..item.力量.." "
                                  end
                                  if item.耐力 ~= 0 and item.耐力 ~= nil  then
                                      属性显示 =属性显示.."#G耐力 +"..item.耐力.." "
                                  end
                                  if item.敏捷 ~= 0 and item.敏捷 ~= nil  then
                                      属性显示 =属性显示.."#G敏捷 +"..item.敏捷.." "
                                  end
                            end
                              if 属性显示~="" then
                                  下边寄存[#下边寄存+1]=属性显示
                              end

                            if item.耐久 ~= nil then
                                  item.耐久=math.floor(item.耐久)
                                  if item.修理失败~=nil and item.修理失败~=0 then
                                      下边寄存[#下边寄存+1]=string.format("#Y耐久度：%s  修理失败 %s次",item.耐久,item.修理失败)
                                  else
                                      下边寄存[#下边寄存+1]="#Y耐久度 "..item.耐久
                                  end
                             end

                              if item.锻炼等级 ~= nil then
                                  local bsz = ""
                                  for i=1,#item.镶嵌宝石 do
                                      local sw = "、"
                                      if i == #item.镶嵌宝石 then
                                          sw = ""
                                      end
                                      bsz = bsz..item.镶嵌宝石[i]..sw
                                  end
                                  下边寄存[#下边寄存+1]=string.format("#Y锻炼等级 %s   镶嵌宝石 %s",item.锻炼等级,bsz)
                                  local 宝石显示 = ""
                                  if item.分类==1 then
                                    if item.命中 ~= 0 and item.命中 ~= nil  then
                                            宝石显示 =宝石显示.."#G命中 +"..item.命中.." "
                                      end
                                      if item.伤害 ~= 0 and item.伤害 ~= nil  then
                                        宝石显示 =宝石显示.."#G伤害 +"..item.伤害.." "
                                      end
                                  elseif item.分类==3 then
                                      if item.躲避 ~= 0 and item.躲避 ~= nil  then
                                        宝石显示 =宝石显示.."#G躲避 +"..item.躲避.." "
                                      end
                                  elseif item.分类==4 then
                                      if item.灵力 ~= 0 and item.灵力 ~= nil  then
                                        宝石显示 =宝石显示.."#G灵力 +"..item.灵力.." "
                                      end
                                      if item.气血 ~= 0 and item.气血 ~= nil  then
                                        宝石显示 =宝石显示.."#G气血 +"..item.气血.." "
                                      end
                                  elseif item.分类==5 or item.分类==6 then
                                      if item.速度 ~= 0 and item.速度 ~= nil  then
                                        宝石显示 =宝石显示.."#G速度 +"..item.速度.." "
                                      end
                                      if item.躲避 ~= 0 and item.躲避 ~= nil  then
                                        宝石显示 =宝石显示.."#G躲避 +"..item.躲避.." "
                                      end
                                  end
                                  if 宝石显示~="" then
                                      下边寄存[#下边寄存+1]=宝石显示
                                  end
                              end
                              if item.镶嵌等级 ~= nil and item.分类 > 6 and  item.分类 < 10  then
                                  下边寄存[#下边寄存+1]=string.format("#Y锻炼等级 %s   镶嵌属性 %s",item.镶嵌等级,item.镶嵌宝石)
                              end

                              if item.临时附魔 ~= nil then
                                  if item.分类 == 1 then
                                      if item.临时附魔.命中.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时命中+"..item.临时附魔.命中.数值.."    #R"..item.临时附魔.命中.时间
                                      end
                                      if item.临时附魔.耐力.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时耐力+"..item.临时附魔.耐力.数值.."    #R"..item.临时附魔.耐力.时间
                                      end
                                  elseif item.分类 == 2 then
                                      if item.临时附魔.体质.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时体质+"..item.临时附魔.体质.数值.."    #R"..item.临时附魔.体质.时间
                                      end
                                      if item.临时附魔.法术伤害.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时法伤+"..item.临时附魔.法术伤害.数值.."    #R"..item.临时附魔.法术伤害.时间
                                      end
                                  elseif item.分类 == 3 then
                                      if item.临时附魔.伤害.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时伤害+"..item.临时附魔.伤害.数值.."    #R"..item.临时附魔.伤害.时间
                                      end
                                      if item.临时附魔.气血.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时气血+"..item.临时附魔.气血.数值.."    #R"..item.临时附魔.气血.时间
                                      end
                                      if item.临时附魔.魔法.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时魔法+"..item.临时附魔.魔法.数值.."    #R"..item.临时附魔.魔法.时间
                                      end
                                      if item.临时附魔.防御.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时防御+"..item.临时附魔.防御.数值.."    #R"..item.临时附魔.防御.时间
                                      end
                                  elseif item.分类 == 4 then
                                      if item.临时附魔.法术防御.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时法防+"..item.临时附魔.法术防御.数值.."    #R"..item.临时附魔.法术防御.时间
                                      end
                                      if item.临时附魔.魔力.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时魔力+"..item.临时附魔.魔力.数值.."    #R"..item.临时附魔.魔力.时间
                                      end
                                  elseif item.分类 == 5 then
                                      if item.临时附魔.愤怒.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时愤怒+"..item.临时附魔.愤怒.数值.."    #R"..item.临时附魔.愤怒.时间
                                      end
                                      if item.临时附魔.伤害.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时伤害#P+"..item.临时附魔.伤害.数值.."    #R"..item.临时附魔.伤害.时间
                                      end
                                  elseif item.分类 == 6 then
                                      if item.临时附魔.速度.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时速度#P+"..item.临时附魔.速度.数值.."     #R"..item.临时附魔.速度.时间
                                      end
                                      if item.临时附魔.魔法.数值 ~= 0 then
                                          下边寄存[#下边寄存+1]="#G临时魔法#P+"..item.临时附魔.魔法.数值.."     #R"..item.临时附魔.魔法.时间
                                      end
                                  end
                              end

                              if item.赐福 ~= nil then
                                  if item.赐福.总类=="基础" then
                                    下边寄存[#下边寄存+1]="#P[仙人赐福]#R"..item.赐福.类型.."#P +#R"..item.赐福.数值
                                  else
                                    下边寄存[#下边寄存+1]="#P[仙人赐福]#R"..item.赐福.类型.."#P +#R"..item.赐福.数值.."%"
                                  end
                              end

                              if item.特技 ~= nil then
                                  下边寄存[#下边寄存+1]=string.format("#S特技：%s",item.特技)
                              end
                              if item.特效 ~= nil then
                                    下边寄存[#下边寄存+1]=string.format("#S特效：%s",item.特效)
                              end
                              if item.第二特效 ~= nil then--"龙宫
                                    下边寄存[#下边寄存+1]=string.format("#S特效：%s",item.第二特效)
                              end
                              if item.套装效果 ~= nil then
                                    下边寄存[#下边寄存+1]=string.format("#S套装效果：%s",item.套装效果[1]..item.套装效果[2])
                              end

                              if item.装备境界 and item.装备境界.品质~="普通" then
                                  local 装备词条 = item.装备境界.词条
                                  local 装备品质 = item.装备境界.品质
                                  if item.装备境界.品质~="传说" and item.装备境界.品质~="神话" and item.装备境界.升级值 and item.装备境界.升级值>0 then
                                      下边寄存[#下边寄存+1]="#G境界值:"..item.装备境界.升级值
                                  end
                                  if item.装备境界.洗练值 and (item.装备境界.洗练值>0 or (装备词条 and (装备词条[1] or 装备词条[2] or 装备词条[3]))) then
                                      下边寄存[#下边寄存+1]="#G仙宝值:"..item.装备境界.洗练值
                                  end
                                  if 装备词条 and  装备词条[1] and 境界属性[装备词条[1].类型]  then
                                      下边寄存[#下边寄存+1]="#G["..装备词条[1].类型.."] "..境界属性[装备词条[1].类型].类型.." +"..装备词条[1].数额.."("..境界属性[装备词条[1].类型][装备品质]..")"
                                  end
                                  if 装备词条 and  装备词条[2] and 装备词条[2].类型 and 境界属性[装备词条[2].类型] then
                                    下边寄存[#下边寄存+1]="#G["..装备词条[2].类型.."] "..境界属性[装备词条[2].类型].类型.." +"..装备词条[2].数额.."("..math.floor(境界属性[装备词条[2].类型][装备品质]/2)..")"
                                  end
                                  if 装备词条 and  装备词条[3] and 装备词条[3].类型 and 境界属性[装备词条[3].类型] then
                                    下边寄存[#下边寄存+1]="#G["..装备词条[3].类型.."] "..境界属性[装备词条[3].类型].类型.." +"..装备词条[3].数额.."("..math.floor(境界属性[装备词条[3].类型][装备品质]/2)..")"
                                  end
                                  if item.装备境界.神话值 and (item.装备境界.神话值>0 or (item.装备境界.神话词条~=nil and item.装备境界.神话词条~=""))  then
                                        下边寄存[#下边寄存+1]="#R神话值:"..item.装备境界.神话值
                                  end
                                  if item.装备境界.神话词条~=nil and item.装备境界.神话词条~="" then
                                      if 境界属性[item.装备境界.神话词条] then
                                          下边寄存[#下边寄存+1]="#R["..item.装备境界.神话词条.."] "..境界属性[item.装备境界.神话词条].类型.." +"..境界属性[item.装备境界.神话词条][装备品质]
                                      elseif 神话属性[item.装备境界.神话词条] then
                                              下边寄存[#下边寄存+1]="#R限制门派:"..神话属性[item.装备境界.神话词条].限制
                                              下边寄存[#下边寄存+1]="#R["..item.装备境界.神话词条.."] "..神话属性[item.装备境界.神话词条].描述
                                      end
                                  end
                                  if 装备词条 and 装备词条[1] and 装备词条[2] and 装备词条[3] then
                                      if 装备词条[1].类型 ~=装备词条[2].类型 and 装备词条[1].类型~=装备词条[3].类型 and 装备词条[2].类型~=装备词条[3].类型  then
                                              if 境界属性[装备词条[1].类型].共鸣 then
                                                  下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                  下边寄存[#下边寄存+1]="#C["..装备词条[1].类型.."] 效果提升25%(1\\3)"
                                              elseif not 境界属性[装备词条[1].类型].共鸣 and 境界属性[装备词条[2].类型].共鸣  then
                                                      下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                      下边寄存[#下边寄存+1]="#C["..装备词条[2].类型.."] 效果提升25%(1\\3)"
                                              elseif not 境界属性[装备词条[1].类型].共鸣 and not 境界属性[装备词条[2].类型].共鸣  and 境界属性[装备词条[3].类型].共鸣 then
                                                      下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                      下边寄存[#下边寄存+1]="#C["..装备词条[3].类型.."] 效果提升25%(1\\)"
                                              end
                                        elseif 装备词条[1].类型 ==装备词条[2].类型 and 装备词条[1].类型~=装备词条[3].类型  then
                                                if 境界属性[装备词条[1].类型].共鸣 then
                                                      下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                      下边寄存[#下边寄存+1]="#C["..装备词条[1].类型.."] 效果提升25%(2\\3)"
                                                elseif not 境界属性[装备词条[1].类型].共鸣  and 境界属性[装备词条[3].类型].共鸣 then
                                                        下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                        下边寄存[#下边寄存+1]="#C["..装备词条[3].类型.."] 效果提升25%(1\\3)"
                                                end
                                        elseif 装备词条[1].类型 ~=装备词条[2].类型 and 装备词条[1].类型==装备词条[3].类型 then
                                                if 境界属性[装备词条[1].类型].共鸣 then
                                                      下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                      下边寄存[#下边寄存+1]="#C["..装备词条[1].类型.."] 效果提升25%(2\\3)"
                                                elseif not 境界属性[装备词条[1].类型].共鸣  and 境界属性[装备词条[2].类型].共鸣   then
                                                      下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                      下边寄存[#下边寄存+1]="#C["..装备词条[2].类型.."] 效果提升25%(1\\3)"
                                                end
                                      elseif 装备词条[1].类型 ~=装备词条[2].类型 and 装备词条[2].类型==装备词条[3].类型 then
                                              if 境界属性[装备词条[2].类型].共鸣 then
                                                  下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                  下边寄存[#下边寄存+1]="#C["..装备词条[2].类型.."] 效果提升25%(2\\3)"
                                              elseif not 境界属性[装备词条[2].类型].共鸣  and 境界属性[装备词条[1].类型].共鸣   then
                                                      下边寄存[#下边寄存+1]="#C共鸣效果:"
                                                      下边寄存[#下边寄存+1]="#C["..装备词条[1].类型.."] 效果提升25%(1\\3)"
                                              end
                                      elseif 装备词条[1].类型 ==装备词条[2].类型 and 装备词条[1].类型==装备词条[3].类型 and 境界属性[装备词条[1].类型].共鸣 then
                                              下边寄存[#下边寄存+1]="#S共鸣效果:"
                                              下边寄存[#下边寄存+1]="#S["..装备词条[1].类型.."] 效果提升25%(3\\3)"
                                      end
                                  end
                  
                  
                              end
                



                              if item.镶嵌四相 ~= nil then
                                  下边寄存[#下边寄存+1]=string.format("#S四相 %s",item.镶嵌四相)
                              end
                              if item.开运孔数 ~= nil and item.开运孔数.当前 ~= nil and item.开运孔数.当前  ~= 0 then
                                  下边寄存[#下边寄存+1]="#G开运孔数："..item.开运孔数.当前 .."孔\\"..item.开运孔数.上限 .."孔"
                              end
                              if item.星位~=nil then
                                      右边寄存[#右边寄存+1]="#Y符石属性:"
                                  for n=1,5 do
                                      if item.星位[n]~=nil then
                                          local  组合语句="#G符石："
                                          for k,v in pairs(item.星位[n].符石属性) do
                                              组合语句=组合语句..k.." +"..v.." "
                                          end
                                          右边寄存[#右边寄存+1]=组合语句
                                      end
                                  end
                                end
                                if item.星位组~=nil then
                                    if item.星位~=nil and item.星位[6] ~= nil then
                                        local 临时属性 = ""
                                        for k,v in pairs(item.星位[6].符石属性) do
                                            临时属性 = k.." +"..v
                                        end
                                          右边寄存[#右边寄存+1]="#G星位: "..临时属性
                                        if item.星位[6].相互~=nil then
                                            local 临时属性 = ""
                                            for k,v in pairs(item.星位[6].相互) do
                                                临时属性 = k.." +"..v
                                            end
                                            右边寄存[#右边寄存+1]="#G星相互合: "..临时属性
                                        end
                                    else
                                          右边寄存[#右边寄存+1]="#G星位: 已开"

                                    end
                                end
                                if item.星位~=nil and item.星位.组合~=nil then
                                  右边寄存[#右边寄存+1]=string.format("#X符石组合： %s",item.星位.组合)
                                  右边寄存[#右边寄存+1]=string.format("#X门派条件： %s",item.星位.门派 or "无")
                                  右边寄存[#右边寄存+1]=string.format("#X部位条件： %s",取符石部位(item.星位.部位))
                                  右边寄存[#右边寄存+1]=string.format("#X%s",取符石组合说明(item.星位.组合,item.星位.组合等级))
                              end
                              if item.专用 ~= nil then
                                  下边寄存[#下边寄存+1]=string.format("#Y玩家%s专用",item.专用)
                              end
                              if item.制造者 ~= nil then
                                  下边寄存[#下边寄存+1]=string.format("#W制造者：%s",item.制造者)
                              end
                              if item.限时 ~= nil then
                                下边寄存[#下边寄存+1]=string.format("#S有效期至：%s",os.date("%Y-%m-%d %H:%M:%S",item.限时))
                              end
                              if item.熔炼属性 ~= nil then
                                  右边寄存[#右边寄存+1]=string.format("#Y熔炼效果：")
                 
                                  if item.熔炼属性[4] ~= nil then
                                      右边寄存[#右边寄存+1]=string.format("#F%s%s%s %s%s%s",item.熔炼属性[3][3],math.abs(item.熔炼属性[3][2]),item.熔炼属性[3][1],item.熔炼属性[4][3],math.abs(item.熔炼属性[4][2]),item.熔炼属性[4][1])
                                      右边寄存[#右边寄存+1]=string.format("#F%s%s%s %s%s%s",item.熔炼属性[1][3],math.abs(item.熔炼属性[1][2]),item.熔炼属性[1][1],item.熔炼属性[2][3],math.abs(item.熔炼属性[2][2]),item.熔炼属性[2][1])
                                  elseif item.熔炼属性[3] ~= nil then
                                      右边寄存[#右边寄存+1]=string.format("#F%s%s%s %s%s%s",item.熔炼属性[3][3],math.abs(item.熔炼属性[3][2]),item.熔炼属性[3][1],item.熔炼属性[1][3],math.abs(item.熔炼属性[1][2]),item.熔炼属性[1][1])
                                      右边寄存[#右边寄存+1]=string.format("#F%s%s%s",item.熔炼属性[2][3],math.abs(item.熔炼属性[2][2]),item.熔炼属性[2][1])
                                  elseif item.熔炼属性[2] ~= nil then
                                      右边寄存[#右边寄存+1]=string.format("#F%s%s%s %s%s%s",item.熔炼属性[1][3],math.abs(item.熔炼属性[1][2]),item.熔炼属性[1][1],item.熔炼属性[2][3],math.abs(item.熔炼属性[2][2]),item.熔炼属性[2][1])
                                  else
                                      右边寄存[#右边寄存+1]=string.format("#F%s%s%s",item.熔炼属性[1][3],math.abs(item.熔炼属性[1][2]),item.熔炼属性[1][1])
                                      
                                  end
                              end
                              if item.幻化特性 ~= nil then
                                  下边寄存[#下边寄存+1]="#Y特性："..item.幻化特性.名称.."+"..item.幻化特性.数额.."%"
                                 
                              end
                        else
                              if item.分类 == 3 then
                                  顶部寄存[#顶部寄存+1]="#W【装备条件】等级"..item.级别限制
                                    if item.角色限制[2] == nil then
                                      顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1]
                                    elseif item.角色限制[3] == nil then
                                      顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1].."、"..item.角色限制[2]
                                    elseif item.角色限制[4] == nil then
                                      顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1].."、"..item.角色限制[2].."、"..item.角色限制[3]
                                    else
                                      顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.角色限制[1].."、"..item.角色限制[2].."、"..item.角色限制[3].."、"..item.角色限制[4]
                                    end
                              elseif item.分类 == 1 or item.分类 == 4 then
                                    顶部寄存[#顶部寄存+1]="#W【装备角色】"..item.性别限制
                              elseif item.分类 > 6 then
                                    顶部寄存[#顶部寄存+1]="#W【角色限制】召唤兽"
                              else
                                    顶部寄存[#顶部寄存+1]="#W【角色限制】无角色限制"
                              end
  
                              下边寄存[#下边寄存+1]="#Y等级 "..item.级别限制
                              下边寄存[#下边寄存+1]="#G未鉴定物品"
   
                        end
                  end
            elseif zls == 3 then
                  if item.分类 == 1 then
                      if item.附带技能==nil then
                             item.附带技能="购买后随机生成"
                      end
                      顶部寄存[#顶部寄存+1]="#Y所带技能: "..item.附带技能
                  elseif item.分类 == 3 then
                      if item.子类==0 then
                          if item.附带技能 == nil then
                            顶部寄存[#顶部寄存+1]="#Y随机生成内丹"
                          else
                            顶部寄存[#顶部寄存+1]="#Y所带内丹技能:"..item.附带技能
                            顶部寄存[#顶部寄存+1]=取内丹介绍(item.附带技能)
                          end
                      else
                          顶部寄存[#顶部寄存+1]="#Y等级 "..item.级别限制
                      end
                     elseif item.分类 == 10 then
                      顶部寄存[#顶部寄存+1]="#Y种类 "..item.种类..",等级 "..item.级别限制
                     elseif item.分类 == 11 then
                      顶部寄存[#顶部寄存+1]="#Y等级 "..item.级别限制..",灵气 "..item.灵气
                  end
            elseif zls == 4 then
                    if item.名称 == "神兵图鉴" then
                      顶部寄存[#顶部寄存+1]="#W【功效】 使用可以鉴定等级≤图鉴等级的衣服和武器"
                      顶部寄存[#顶部寄存+1]=string.format("#Y等级: %d",item.子类)
                    elseif item.名称 == "灵宝图鉴" then
                      顶部寄存[#顶部寄存+1]="#W【功效】 使用可以鉴定等级≤图鉴等级的头盔、腰带、饰品和鞋子"
                      顶部寄存[#顶部寄存+1]=string.format("#Y等级: %d",item.子类)
                    elseif item.名称 == "灵饰图鉴" then
                      顶部寄存[#顶部寄存+1]="#W【功效】 使用可以鉴定等级≤图鉴等级的灵饰"
                      顶部寄存[#顶部寄存+1]=string.format("#Y等级: %d",item.子类)
                    end
                    if item.名称 == "附魔宝珠" or item.名称 == "超级附魔宝珠" then
              
                      顶部寄存[#顶部寄存+1]=string.format("#Y等级: %s",item.级别限制)
              
                    end
                elseif zls == 5 then
                  if item.分类 == 1 then
                    local it = _tp:取武器子类(item.特效)
                    顶部寄存[#顶部寄存+1]="#Y种类 "..it..", 等级 "..item.子类
                  elseif item.分类 == 2 or item.分类 == 21 then
                    if item.名称=="元灵晶石" and item.子类==nil then
                      顶部寄存[#顶部寄存+1]="#Y购买后随机生成元灵晶石"
                        else
                          顶部寄存[#顶部寄存+1]=string.format("#Y等级 %d",item.子类)
                        end
                      elseif item.名称=="点化石" then
                    if item.附带技能==nil then
                       item.附带技能="购买后随机生成"
                    end
                    顶部寄存[#顶部寄存+1]="#Y所带技能: "..item.附带技能
                  elseif item.名称=="精魄灵石" then
                    if  item.级别限制 then
                      local 显示属性 = ""
                      if item.子类==1 then
                        顶部寄存[#顶部寄存+1]="#W【镶嵌装备】项圈"
                          if item.属性 =="伤害" then
                            顶部寄存[#顶部寄存+1]="#Y等级:"..item.级别限制..",增加10点伤害"
                          else
                            顶部寄存[#顶部寄存+1]="#Y等级:"..item.级别限制..",增加4点灵力"
                          end
                        elseif item.子类==2 then
                          顶部寄存[#顶部寄存+1]="#W【镶嵌装备】护腕"
                          顶部寄存[#顶部寄存+1]="#Y等级:"..item.级别限制..",增加6点速度"
                        else
                          顶部寄存[#顶部寄存+1]="#W【镶嵌装备】铠甲"
                           if item.属性 =="气血" then
                            顶部寄存[#顶部寄存+1]="#Y等级:"..item.级别限制..",增加30点气血"
                            else
                              顶部寄存[#顶部寄存+1]="#Y等级:"..item.级别限制..",增加8点防御"
                            end
                        end
                         else
                          顶部寄存[#顶部寄存+1]="#Y（购买后随机生成。)"
                         end
              
                  elseif item.分类 == 3 then
                    顶部寄存[#顶部寄存+1]="#Y功效 用于分解装备获得宝石"
                  elseif item.分类 == 4 then
                    --顶部寄存[#顶部寄存+1]=string.format("#Y功效 %s",item.特效))
                  elseif item.分类 == 6 then
                       if item.名称~="星辉石" and item.名称 ~= "钟灵石" and item.名称 ~= "钨金" then
                        顶部寄存[#顶部寄存+1]=string.format("#W【镶嵌装备】%s",item.角色限制)
                        顶部寄存[#顶部寄存+1]=string.format("#W【镶嵌效果】%s",item.特效)
                    elseif item.名称=="钨金" then
                      顶部寄存[#顶部寄存+1]=string.format("#W【镶嵌装备】%s","熔炼装备属性")
                      顶部寄存[#顶部寄存+1]=string.format("#W【镶嵌效果】%s","增加12维属性")
                    elseif item.名称=="钟灵石" then
              
              
                      顶部寄存[#顶部寄存+1]=string.format("#Y附加特性：%s(等级%s)",item.附加特性,item.级别限制)
                             if item.附加特性=="气血方刚" then
                              顶部寄存[#顶部寄存+1]="#S所有部位:增加气血上限"
                        elseif item.附加特性=="通真达灵" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加法术防御"
                        elseif item.附加特性=="心无旁骛" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加抵抗封印等级"
                        elseif item.附加特性=="健步如飞" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加速度"
                        elseif item.附加特性=="回春之术" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加治疗能力"
                        elseif item.附加特性=="风雨不动" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加抗法术暴击等级"
                        elseif item.附加特性=="固若金汤" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加抗物理暴击等级"
                        elseif item.附加特性=="气壮山河" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加气血回复效果"
                        elseif item.附加特性=="锐不可当" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加固定伤害"
                        elseif item.附加特性=="心源" then
                          顶部寄存[#顶部寄存+1]="#S所有部位:增加物理防御"
                        end
              
              
              
              
                    end
                    if  item.名称~="钟灵石" then
                      顶部寄存[#顶部寄存+1]=string.format("#Y等级 %s",item.级别限制)
                    end
                  elseif item.分类 == 7 then
                    顶部寄存[#顶部寄存+1]="#Y购买后随机生成"..item.名称
                  elseif item.分类 == 8 then
                    顶部寄存[#顶部寄存+1]="#Y购买后随机生成"..item.名称
                     elseif item.分类 == 20 then
                         if item.特效~=nil and item.子类~=nil then
                          顶部寄存[#顶部寄存+1]="#Y种类 "..item.特效..", 等级 "..item.子类
                        else
                          顶部寄存[#顶部寄存+1]="#Y购买后随机生成灵饰指南书"
                         end
                  end
                elseif zls == 7 then
                  if item.分类 == 1 then
                    if item.名称=="藏宝图" then
                      顶部寄存[#顶部寄存+1]="#Y【类型】普通藏宝图"
                          elseif item.名称=="高级藏宝图" then
                            顶部寄存[#顶部寄存+1]="#Y【类型】高级藏宝图"
                          elseif item.名称=="玲珑宝图" then
                            顶部寄存[#顶部寄存+1]="#Y【类型】玲珑宝图"
                    end
                    顶部寄存[#顶部寄存+1]=string.format("#Y【坐标】#G%s(%s,%s)",item.地图名称,item.x,item.y)
                    --顶部寄存[#顶部寄存+1]=string.format("#Y坐标 X：%d Y：%d",item.气血,item.魔法))
                  end
                elseif zls == 9 then
                  if item.名称 == "清灵净瓶" then
                    顶部寄存[#顶部寄存+1]="#Y使用后随机开出初级,中级,高级清灵仙露"
                  end
                  if item.分类 == 1 and item.名称 ~= "魔兽残卷" then
                    顶部寄存[#顶部寄存+1]="#Y右键使用"
                  end
                elseif zls == 10 then
                  if item.分类 == 4 then
                    顶部寄存[#顶部寄存+1]="#Y右键使用"
                  elseif item.分类 == 3 then
                    顶部寄存[#顶部寄存+1]="#Y右键使用可增加150点的体力和活力"
                  end
                elseif zls == 11 then
                     if item.分类==1 then
                         if item.地图==nil then
                          顶部寄存[#顶部寄存+1]="#Y右击后定标"
                          else
                           local map = 取地图名称(item.地图)
                           顶部寄存[#顶部寄存+1]=string.format("#Y%s(%d,%d)",map,item.x,item.y)
                           顶部寄存[#顶部寄存+1]=string.format("#Y还可使用%s次",item.次数)
                         end
                      elseif item.分类==2 then
                        顶部寄存[#顶部寄存+1]=string.format("#Y场景:%s,还能使用%s次",取地图名称(item.地图),item.次数)
                      end
              
                elseif zls == 12 then
                  顶部寄存[#顶部寄存+1]=string.format("#Y变化对象：%s",item.子类)
                elseif zls == 13 then
                  顶部寄存[#顶部寄存+1]=string.format("#Y阵型:%s",item.子类)
           
                elseif zls == 21 then
                        if item.特效 then
                            顶部寄存[#顶部寄存+1]=string.format("#Y说明：%s",item.特效)
                        end
                        if item.附带词条 then
                            if 境界属性[item.附带词条] then
                                顶部寄存[#顶部寄存+1]="#Y所属属性:"..境界属性[item.附带词条].类型
                                顶部寄存[#顶部寄存+1]="#Y附魔部位:"..境界属性[item.附带词条].分类
                                if item.数额 and item.数额+0>0 then
                                  顶部寄存[#顶部寄存+1]="#G类型:"..item.附带词条.."  数值:"..item.数额
                                else
                                  顶部寄存[#顶部寄存+1]="#G类型:"..item.附带词条.."  数值:"..境界属性[item.附带词条].神话
                                end
                            elseif 神话属性[item.附带词条] then
                                  顶部寄存[#顶部寄存+1]="#Y限制门派:"..神话属性[item.附带词条].限制
                                  顶部寄存[#顶部寄存+1]="#Y附魔部位:"..神话属性[item.附带词条].部位
                                  顶部寄存[#顶部寄存+1]="#G神话类型:"..item.附带词条
                            else
                                  顶部寄存[#顶部寄存+1]="#Y该物品数据错误,请联系管理员"
                            end
                        end

                elseif zls == 30 then
                  顶部寄存[#顶部寄存+1]=string.format("#W卡片类型：%s",item.造型)
                  顶部寄存[#顶部寄存+1]=string.format("#W技能要求：%s",数字转大写(item.等级).."级变化之术")
                  local 变身卡技能=item.技能
                  if 变身卡技能=="" then 变身卡技能="无" end
                  顶部寄存[#顶部寄存+1]=string.format("#S【附加技能】%s",变身卡技能)
                  local 组合语句=""
                  if item.属性==0 then
                        组合语句="无"
                      elseif item.单独==1 then
                        if item.正负==1 then
                           组合语句=item.类型.."+"..item.属性
                          else
                           组合语句=item.类型.."-"..item.属性
                          end
                     else
                       if item.正负==1 then
                           组合语句=item.类型.."+"..item.属性.."%"
                          else
                           组合语句=item.类型.."-"..item.属性.."%"
                          end
                    end
                    顶部寄存[#顶部寄存+1]=string.format("#G【属性影响】%s",组合语句)
                    顶部寄存[#顶部寄存+1]=string.format("#Y等级：%s  剩余使用次数：%s",item.等级,item.次数)
                    顶部寄存[#顶部寄存+1]=string.format("#Y持续时间：%s分钟","15分钟*(1+变化之术等级)")
                -- elseif zls == 55 then --
                -- 	local fssx = qwp(item.名称)
                -- 	self.属性1 = fssx[18]
                -- 	self.属性值1 = fssx[19]
                -- 	self.属性2 = fssx[21]
                -- 	self.属性值2 = fssx[22]
                -- 	self.等级=fssx[16]
                -- 	self.颜色=fssx[20]
                -- 	if item.耐久度 ~=nil then
                -- 	    self.耐久=math.floor(item.耐久度)
                -- 	end
                -- 	顶部寄存[#顶部寄存+1]="#W/【用途】 可镶嵌在有开运孔数的装备上")
                -- 	顶部寄存[#顶部寄存+1]=string.format("#Y等级：%s  %s",self.等级,self.颜色))
                -- 	if fssx[16] == 1 or fssx[16] == 2 then
                -- 		顶部寄存[#顶部寄存+1]=string.format("#Y"..self.属性1.." +%s",self.属性值1))
                -- 	else
                -- 		顶部寄存[#顶部寄存+1]=string.format("#Y"..self.属性1.." +%s "..self.属性2.." +%s ",self.属性值1,self.属性值2))
                -- 	end
                -- 	if item.耐久度~=nil then
               --      		顶部寄存[#顶部寄存+1]=string.format("#Y耐久度 "..item.耐久度))\\
              
                -- 	end
              
              
                elseif zls == 150 then --最初西游会员卡
                        if item.分类 == 1 and item.子类 == 10 and  string.find(item.名称,"会员卡")~=nil then
                          if item.限时 ~= nil then
                            顶部寄存[#顶部寄存+1]=string.format("#S有效期至：%s",os.date("%Y-%m-%d %H:%M:%S",item.限时))

                      end
              
                    if item.专用 ~= nil then
                      顶部寄存[#顶部寄存+1]=string.format("#S不可交易")
                      顶部寄存[#顶部寄存+1]=string.format("#Y玩家ID:#R %s #Y专用",item.专用)
              
                    end
                  end
                elseif zls == 151 then
                  if item.名称=="房屋地契" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y房屋规模: %s ",item.房屋规模)
                    顶部寄存[#顶部寄存+1]=string.format("#G房屋样式: %s ",item.房屋样式)
                    顶部寄存[#顶部寄存+1]=string.format("#G庭院样式: %s ",item.庭院规模)
                  elseif item.名称=="设计图" then
                    顶部寄存[#顶部寄存+1]="#Y"..item.类型..",等级 "..item.等级
                  elseif item.名称=="归墟之证" then
                   
                        if item.等级 and item.类型  then
                            local 星级=""
                            for i = 1, tonumber(item.等级) do
                                星级=星级.."★"
                            end
                            顶部寄存[#顶部寄存+1]="#Y类型:"..item.类型
                            顶部寄存[#顶部寄存+1]="#Y难度:"..星级
                        end
                  end
                elseif zls == 889 then
                  if item.名称=="一级未激活符石" or item.名称=="二级未激活符石" or item.名称=="三级未激活符石" then
                    顶部寄存[#顶部寄存+1]="【激活方式】鼠标右键点击使用激活"
                    顶部寄存[#顶部寄存+1]="#Y当前状态:#R未激活"
                    if item.子类~=nil and item.颜色~=nil then
                      顶部寄存[#顶部寄存+1]="#Y等级 "..item.子类.."  "..item.颜色
                    end
                    if  item.符石属性~=nil then
                        local 组合语句="#Y"
                        for k,v in pairs(item.符石属性) do
                          组合语句 = 组合语句..k.." +"..v.."  "
                        end
                      顶部寄存[#顶部寄存+1]=组合语句
                    end
                  elseif item.名称=="未激活的星石" then
                    顶部寄存[#顶部寄存+1]="#Y镶嵌部位: "..取星石部位(item.子类)
                    顶部寄存[#顶部寄存+1]="#R激活后为专用，无法转移给他人。"
                    顶部寄存[#顶部寄存+1]="【激活方式】鼠标右键点击使用激活"
                    顶部寄存[#顶部寄存+1]="#Y当前状态:#R未激活"
                    顶部寄存[#顶部寄存+1]="#Y等级 4"
                    顶部寄存[#顶部寄存+1]="#G镶嵌在星位后，激活属性"
                  elseif item.分类 == 91 and item.类型==nil then
                    顶部寄存[#顶部寄存+1]="#Y等级 4"
                    local ys = 取星位颜色(item.子类)
                    顶部寄存[#顶部寄存+1]="#Y正面颜色: "..ys[1].." 反面颜色: "..ys[2]
                    顶部寄存[#顶部寄存+1]="#Y镶嵌部位: "..取星石部位(item.子类)
                    顶部寄存[#顶部寄存+1]="#G镶嵌于开运装备的星位，穿戴整套镶有星石的装备可触发符石套装效果"
                    顶部寄存[#顶部寄存+1]="#Y耐久 150"
                  elseif item.分类 == 91 and item.类型~=nil then
                    顶部寄存[#顶部寄存+1]="#Y等级 4"
                    local ys = 取星位颜色(item.子类)
                    顶部寄存[#顶部寄存+1]="#Y颜色: "..ys[item.类型]
                    顶部寄存[#顶部寄存+1]="#Y镶嵌部位: "..取星石部位(item.子类)
                    顶部寄存[#顶部寄存+1]="#G镶嵌于开运装备的星位，左键点击翻转"
                    顶部寄存[#顶部寄存+1]="#G星位: "..item.sx
                    顶部寄存[#顶部寄存+1]="#Y耐久 150"
                  elseif item.名称=="符石卷轴" then
                    顶部寄存[#顶部寄存+1]="#w【说明】需要加入此卷轴才可以合成三级符石，在合成过程中会被消耗。"
                  else
                      顶部寄存[#顶部寄存+1]="【用途】可镶嵌有开孔数的装备上"
                      if item.不可交易 then
                        顶部寄存[#顶部寄存+1]="#Y不可交易"
                      end
                      local 组合语句="#Y"
                      for k,v in pairs(item.符石属性) do
                        组合语句 = 组合语句..k.." +"..v.."  "
                      end
                      顶部寄存[#顶部寄存+1]="#Y等级 "..item.子类.."  "..item.颜色
                      顶部寄存[#顶部寄存+1]=组合语句
                      顶部寄存[#顶部寄存+1]="#Y耐久 150"
                  end
                elseif zls == 56 then
                  顶部寄存[#顶部寄存+1]="#W【激活方式】#Y鼠标右键点击使用激活"
                elseif zls == 101 then
                  顶部寄存[#顶部寄存+1]="#W【期限】#G效果持续一周"
              
                  if item.类型=="嗜血" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【体质】#Y强化一件#S项链")
                  elseif item.类型=="莲华妙法" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【法伤】#Y强化一件#S项链")
                  elseif item.类型=="轻如鸿毛" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【魔法】#Y强化一件#S武器")
                  elseif item.类型=="神木呓语" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【魔法】#Y强化一件#S鞋子")
                  elseif item.类型=="拈花妙指" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【速度】#Y强化一件#S鞋子")
                  elseif item.类型=="盘丝舞" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【防御】#Y强化一件#S武器")
                  elseif item.类型=="一气化三清" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【魔力】#Y强化一件#S衣服")
                  elseif item.类型=="浩然正气" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【法防】#Y强化一件#S衣服")
                  elseif item.类型=="龙附" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【伤害】#Y强化一件#S武器")
                  elseif item.类型=="穿云破空" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【伤害】#Y强化一件#S腰带")
                  elseif item.类型=="神兵护法" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【命中】#Y强化一件#S帽子")
                  elseif item.类型=="魔王护持" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【气血】#Y强化一件#S武器")
                  elseif item.类型=="元阳护体" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【气血】#Y强化一件#S武器")
                  elseif item.类型=="神力无穷" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【愤怒】#Y强化一件#S腰带")
                  elseif item.类型=="尸气漫天" then
                    顶部寄存[#顶部寄存+1]=string.format("#Y以"..item.等级.."级的#Z"..item.类型.."#Y法术效果\n#G【耐力】#Y强化一件#S帽子")
                  end
                elseif zls == "召唤兽道具" then
              
                  if item.名称 == "初级清灵仙露" or item.名称 == "中级清灵仙露" or item.名称 == "高级清灵仙露"  or item.名称 == "超级清灵仙露"  or item.名称 == "终级清灵仙露" then
                    顶部寄存[#顶部寄存+1]="#Y灵气: "..item.灵气
                  end
                elseif zls == 1000 then
                --	顶部寄存[#顶部寄存+1]=string.format("【法宝等级】%s级法宝",item.分类))
                顶部寄存[#顶部寄存+1]=string.format("#G%d#Y级法宝,修炼境界：\n#Y第#G%d#Y层 %s",item.分类,item.气血,取境界(item.气血,item.分类))
                顶部寄存[#顶部寄存+1]=string.format("#Y灵气:#G%d #Y五行:#G%s",item.魔法,item.五行)
                  local xz=""
                  if item.名称=="月光宝盒" and  item.地图~=nil then
                           xz=string.format("#Y传送至%s(%s,%s),",取地图名称(item.地图),item.x,item.y)
                     end
                  if item.使用 == 0 then
                      xz =xz.."#Z佩戴战斗中发挥效用"
                  elseif item.使用 ==2 then
                      xz = xz.."无需佩戴,非战斗中选择使用"
                  else
                      xz = xz..string.format("#Z无需佩戴，在战斗中选择使用\n#Y使用回合限制：#G%d#Y",item.角色限制)
                  end
                  顶部寄存[#顶部寄存+1]=string.format("#Y%s",xz)
                  顶部寄存[#顶部寄存+1]=string.format("#Y使用装备限制：\n#G人物等级≥%d",item.特技)
                    elseif zls == 1005 then   ----------灵宝
                  local 消耗 = ""
                  if item.使用 == 1 then
                    消耗="1\\3\\7"
                  elseif item.使用 == 2 then
                    消耗="1"
                  elseif item.使用 == 3 then
                    消耗="全部"
                  end
                  顶部寄存[#顶部寄存+1]="#W【使用条件】"
                  顶部寄存[#顶部寄存+1]=string.format("#G%s门派专用，消耗%s点灵元",item.特技,消耗)
                  顶部寄存[#顶部寄存+1]="#F【战斗效果】"
                  顶部寄存[#顶部寄存+1]=string.format("#P%s",item.效果)
                  顶部寄存[#顶部寄存+1]=string.format("#Y灵气：#G%s",item.魔法)
                  顶部寄存[#顶部寄存+1]=string.format("#Y修炼境界：第#G%d#Y层 %s",item.气血,取境界(item.气血,item.分类))
                elseif zls == 1003 then
              
                elseif zls == 2000 then
                  顶部寄存[#顶部寄存+1]="【基本伤害】"..item.分类
                  顶部寄存[#顶部寄存+1]=" #Y耐久度 "..math.floor(item.耐久)
                elseif zls == "坐骑饰品" then
                  顶部寄存[#顶部寄存+1]=string.format("【类    型】 %s",item.总类)
                  顶部寄存[#顶部寄存+1]=string.format("【装备坐骑】 %s",item.子类)
                end



        return {顶部=顶部寄存,下边=下边寄存,右边=右边寄存}
end








