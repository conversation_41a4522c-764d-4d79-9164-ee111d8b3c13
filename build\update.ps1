Start-Sleep -Seconds 3
$newExe = ".\\MHXY_new.exe"
$currentExe = "MHXY.exe"
$backupExe = "MHXY.exe.bak"
if (-not (Test-Path $newExe)) { exit 1 }
if (Test-Path $backupExe) { Remove-Item $backupExe -Force }
if (Test-Path $currentExe) { Move-Item $currentExe $backupExe }
Move-Item $newExe $currentExe
if (-not (Test-Path $currentExe)) {
    if (Test-Path $backupExe) { Move-Item $backupExe $currentExe }
    exit 1
}
Start-Process $currentExe
Start-Sleep -Seconds 1
