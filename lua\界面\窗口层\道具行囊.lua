local 道具行囊 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "道具行囊", 50 + abbr.py.x, 22 + abbr.py.y, 769, 486)
function 道具行囊:初始化()
  self:置精灵(置窗口背景("道具行囊", 0, 12, 718, 474))
  self.格子背景 = __res:getPNGCC(3, 694, 4, 338, 273)["到精灵"]((__res:getPNGCC(3, 694, 4, 338, 273)))
end
function 道具行囊:显示(x, y)
  self.格子背景["显示"](self.格子背景, x + 350, y + 108)
end
local 抓取物品20格子计算=0
function 道具行囊:打开()
  self:置可见(true)
  self:重置("人物", self.人物界面)
  道具行囊["人物界面"]["模型重置"](道具行囊["人物界面"])
  道具行囊["人物"]["置选中"](道具行囊["人物"], true)
  道具行囊["格子类型"]["道具"]["置选中"](道具行囊["格子类型"]["道具"], true)
  道具行囊["道具网格"]["置物品"](道具行囊["道具网格"], __主控["道具列表"])
  self.道具分页控件:置可见(true)
  self.道具分页控件.分页1:置选中(true)
  self.分页状态=1
  抓取物品20格子计算=0
  self.包裹类型 = "道具"
  self.抓取类型 = nil
  self.抓取物品ID = nil
  self.选中召唤兽 = nil
  self.选中坐骑 = nil
  self.选中子女 = nil
  self.移动 = false
  self.动画 = {}
end

function 道具行囊:重置抓取()
  if self.抓取物品ID  and 道具行囊["道具网格"]["子控件"][抓取物品20格子计算] and 道具行囊["道具网格"]["子控件"][抓取物品20格子计算]._spr then
    道具行囊["道具网格"]["子控件"][抓取物品20格子计算]._spr["确定"] = nil
  end
  if __UI弹出["道具详情"]["是否可见"] then
    __UI弹出["道具详情"]["置可见"](__UI弹出["道具详情"], false)
  end
  self.抓取物品 = nil
  self.抓取物品ID = nil
  抓取物品20格子计算=0
  self.移动 = false
end
function 道具行囊:重置(类型, 控件)
  if 控件 then
    self.人物界面["置可见"](self.人物界面, 控件 == self.人物界面, not self.人物界面["是否实例"])
    self.召唤兽界面["置可见"](self.召唤兽界面, 控件 == self.召唤兽界面, not self.召唤兽界面["是否实例"])
    self.坐骑界面["置可见"](self.坐骑界面, 控件 == self.坐骑界面, not self.坐骑界面["是否实例"])
    self.子女界面["置可见"](self.子女界面, 控件 == self.子女界面, not self.子女界面["是否实例"])
  end
  if "人物" == 类型 then
    self.清空(self)
    self.人物界面["重置"](self.人物界面)
    self.人物界面["灵饰网格"]["置物品"](self.人物界面["灵饰网格"], 角色信息["灵饰"])
    self.人物界面["装备网格"]["置物品"](self.人物界面["装备网格"], 角色信息["装备"])
    self.人物界面["模型重置"](self.人物界面)
  elseif "召唤兽" == 类型 then
    self.清空(self)
    self.召唤兽界面["头像网格"]["置头像"](self.召唤兽界面["头像网格"], 角色信息["宝宝列表"])
    if self.选中召唤兽 then
      self.召唤兽界面["装备网格"]["置物品"](self.召唤兽界面["装备网格"], 角色信息["宝宝列表"][self.选中召唤兽]["装备"])
    else
      self.召唤兽界面["装备网格"]["置物品"](self.召唤兽界面["装备网格"], {})
    end
    self.召唤兽界面["模型重置"](self.召唤兽界面)
  elseif "坐骑" == 类型 then
    self.清空(self)
    self.坐骑界面["头像网格"]["置头像"](self.坐骑界面["头像网格"], 角色信息["坐骑列表"])
    if self.选中坐骑 then
      self.坐骑界面["装备网格"]["置物品"](self.坐骑界面["装备网格"], 角色信息["坐骑列表"][self.选中坐骑]["饰品"])
    else
      self.坐骑界面["装备网格"]["置物品"](self.坐骑界面["装备网格"], nil)
    end
    self.坐骑界面["模型重置"](self.坐骑界面)
  elseif "子女" == 类型 then
     self.清空(self)
     self.子女界面["头像网格"]["置头像"](self.子女界面["头像网格"], 角色信息["子女列表"])
     self.子女界面["模型重置"](self.子女界面)
  end
  self.重置抓取(self)
end
function 道具行囊:清空()
  self.召唤兽界面["模型格子"]["清空"](self.召唤兽界面["模型格子"])
  self.坐骑界面["模型格子"]["清空"](self.坐骑界面["模型格子"])
  self.人物界面["模型格子"]["清空"](self.人物界面["模型格子"])
  self.子女界面["模型格子"]["清空"](self.子女界面["模型格子"])
end
function 道具行囊:重置道具() 
  local fdfere={}
  for n=(self.分页状态-1)*20+1,(self.分页状态-1)*20+20 do
    if __主控["道具列表"][n] then
      fdfere[n-(self.分页状态-1)*20]=__主控["道具列表"][n]
    end
  end
  道具行囊["道具网格"]["置物品"](道具行囊["道具网格"], fdfere)
end
local 关闭 = 道具行囊["创建我的按钮"](道具行囊, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 678, 0)
function 关闭:左键弹起(x, y, msg)
  道具行囊["重置抓取"](道具行囊)
  道具行囊["清空"](道具行囊)
  __UI界面["窗口层"]["道具行囊"]["置可见"](__UI界面["窗口层"]["道具行囊"], false)
end

for i, v in ipairs({
  {
    name = "人物",
    x = 716,
    y = 58,
    tcp = __res:getPNGCC(1, 686, 0, 54, 98, true),
    tcp2 = __res:getPNGCC(1, 1132, 0, 54, 99, true),
    font = "人\n物"
  },
  {
    name = "召唤兽",
    x = 716,
    y = 162,
    tcp = __res:getPNGCC(1, 686, 0, 54, 98, true),
    tcp2 = __res:getPNGCC(1, 1132, 0, 54, 99, true),
    font = "召\n唤\n兽"
  },
  {
    name = "坐骑",
    x = 716,
    y = 266,
    tcp = __res:getPNGCC(1, 686, 0, 54, 98, true),
    tcp2 = __res:getPNGCC(1, 1132, 0, 54, 99, true),
    font = "坐\n骑"
  },
  {
    name = "子女",
    x = 716,
    y = 371,
    tcp = __res:getPNGCC(1, 686, 0, 54, 98, true),
    tcp2 = __res:getPNGCC(1, 1132, 0, 54, 99, true),
    font = "子\n女"
  }
}) do
  local 临时函数 = 道具行囊["创建我的单选按钮"](道具行囊, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    道具行囊["重置"](道具行囊, v.name, 道具行囊[v.name .. "界面"])
  end
end




for i, v in ipairs({
  {
    name = "更多按钮",
    x = 350,
    y = 430,
    tcp = __res:getPNGCC(4, 356, 537, 117, 43, true):拉伸(135, 43),--:拉伸(117, 43)
    font = "更多操作    "
  },
  {
    name = "摆摊",
    x = 565,
    y = 430,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    font = "摆 摊"
  }
}) do
  local 临时函数 = 道具行囊["创建我的按钮"](道具行囊, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "更多按钮" then
      -- 发送数据(3749, {
      --   ["方式"] = "索取"
      -- })
      __UI弹出.道具弹出更多:打开("道具")
    elseif v.name == "摆摊" then
      发送数据(3720)
      关闭["左键弹起"](关闭)
    end
  end
end
local 格子类型 = 道具行囊["创建控件"](道具行囊, "格子类型", 335, 62, 370, 45)
for i, v in ipairs({
  {
    name = "道具",
    x = 0,
    y = 0,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true),
    font = "道 具"
  },
  {
    name = "行囊",
    x = 126,
    y = 0,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true),
    font = "行 囊"
  },
  {
    name = "任务",
    x = 249,
    y = 0,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true),
    font = "任 务"
  }
}) do
  local 临时函数 = 格子类型["创建我的单选按钮"](格子类型, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "道具" then
      if 道具行囊["包裹类型"] == "行囊" and 道具行囊["抓取物品ID"] and 道具行囊["抓取类型"] and 道具行囊["移动"] then
        发送数据(3746, {
          ["序列"] = 道具行囊["抓取物品ID"],
          ["放置类型"] = "道具",
          ["抓取类型"] = 道具行囊["抓取类型"]
        })
        道具行囊["重置抓取"](道具行囊)
        return false
      else
        发送数据(3699)
        道具行囊["包裹类型"] = "道具"
        道具行囊["重置抓取"](道具行囊)
      end
    elseif v.name == "行囊" then
      if "道具" == 道具行囊["包裹类型"] and 道具行囊["抓取物品ID"] and 道具行囊["抓取类型"] and 道具行囊["移动"] then
        发送数据(3746, {
          ["序列"] = 道具行囊["抓取物品ID"],
          ["放置类型"] = "行囊",
          ["抓取类型"] = 道具行囊["抓取类型"]
        })
        道具行囊["重置抓取"](道具行囊)
        return false
      else
        发送数据(3700)
        道具行囊["包裹类型"] = "行囊"
        道具行囊["重置抓取"](道具行囊)
      end
    elseif v.name == "任务" then
      道具行囊["包裹类型"] = "任务包裹"
      if 道具行囊["包裹类型"] == "任务包裹" and 道具行囊["抓取物品ID"] and 道具行囊["抓取类型"] and 道具行囊["移动"] then
        发送数据(3746, {
          ["序列"] = 道具行囊["抓取物品ID"],
          ["放置类型"] = "任务包裹",
          ["抓取类型"] = 道具行囊["抓取类型"]
        })
        道具行囊["重置抓取"](道具行囊)
        self:置选中(false)
      else
        发送数据(3750)
        道具行囊["包裹类型"] = "任务包裹"
        道具行囊["重置抓取"](道具行囊)
      end
    end
    道具行囊.道具分页控件:置可见(道具行囊["包裹类型"] == "道具")
    if 道具行囊["包裹类型"] == "道具" then
      道具行囊.道具分页控件.分页1:置选中(true)
      道具行囊.分页状态=1
    end
  end
end
local 道具网格 = 道具行囊["创建网格"](道具行囊, "道具网格", 350, 110, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    if not 道具行囊["移动"] then
      道具行囊["重置抓取"](道具行囊)
      self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 40, 86, w, h, "道具", a)
      道具行囊["抓取类型"] = 道具行囊["包裹类型"]
      道具行囊["抓取物品ID"] = a+(道具行囊.分页状态-1)*20
      抓取物品20格子计算=a
      self.子控件[a]._spr["确定"] = true
    else
      发送数据(3701, {
        ["抓取id"] = 道具行囊["抓取物品ID"],
        ["放置id"] = a+(道具行囊.分页状态-1)*20,
        ["放置类型"] = 道具行囊["包裹类型"],
        ["抓取类型"] = 道具行囊["抓取类型"]
      })
      道具行囊["重置抓取"](道具行囊)
    end
  elseif 道具行囊["移动"] then
    发送数据(3701, {
      ["抓取id"] = 道具行囊["抓取物品ID"],
      ["放置id"] = a+(道具行囊.分页状态-1)*20,
      ["放置类型"] = 道具行囊["包裹类型"],
      ["抓取类型"] = 道具行囊["抓取类型"]
    })
    道具行囊["重置抓取"](道具行囊)
  else
    道具行囊["重置抓取"](道具行囊)
  end
end

function 道具网格:置物品(数据)
  for i = 1, #道具网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "道具行囊")
      lssj["置偏移"](lssj, 10, 10)
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)
    else
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i])
    end
  end
end

if asdadr then
  local 道具分页控件 = 道具行囊["创建我的控件"](道具行囊, "道具分页控件", 347, 386, 363, 40) 
  for i = 1, 4 do
    local 临时函数 = 道具分页控件["创建我的单选按钮"](道具分页控件, __res:getPNGCC(3, 1069, 1156, 60, 37, true):拉伸(85,37), __res:getPNGCC(3, 1069, 1116, 60, 37, true):拉伸(85,37), "分页"..i, (i-1)*87, 0, ""..i)
    function  临时函数:左键弹起(x, y)
      道具行囊.分页状态=i
      local fdfere={}
      for n=(i-1)*20+1,(i-1)*20+20 do
        if __主控["道具列表"][n] then
          fdfere[n-(i-1)*20]=__主控["道具列表"][n]
        end
      end
      道具行囊["道具网格"]:置物品(fdfere)
    end
  end
else
  local 道具分页控件 = 道具行囊["创建我的控件"](道具行囊, "道具分页控件", 347, 386, 363, 40)
  for i = 1, 4 do
    local 临时函数 = 道具分页控件["创建我的单选按钮"](道具分页控件, __res:getPNGCC(5, 0, 408, 87, 39, true):拉伸(85,37), __res:getPNGCC(5, 0, 370, 87, 38, true):拉伸(85,37), "分页"..i, (i-1)*87, 0, ""..i)
    function  临时函数:左键弹起(x, y)
      道具行囊.分页状态=i
      local fdfere={}
      for n=(i-1)*20+1,(i-1)*20+20 do
        if __主控["道具列表"][n] then
          fdfere[n-(i-1)*20]=__主控["道具列表"][n]
        end
      end
      道具行囊["道具网格"]:置物品(fdfere)
    end
  end
end

local 人物界面 = 道具行囊["创建我的控件"](道具行囊, "人物界面", 0, 0, 333, 486)
function 人物界面:初始化()
  local nsf = require("SDL.图像")(333, 486)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 383, 64, 294, 275)["显示"](__res:getPNGCC(3, 383, 64, 294, 275), 29, 63)
    __res:getPNGCC(3, 523, 393, 144, 161)["显示"](__res:getPNGCC(3, 523, 393, 144, 161), 103, 131)
    local lssj = 取输入背景(0, 0, 106, 23)
    lssj["显示"](lssj, 72, 352)
    lssj["显示"](lssj, 224, 352)
    lssj = 取输入背景(0, 0, 255, 23)
    lssj["显示"](lssj, 72, 391)
    字体18["置颜色"](字体18, 255, 255, 255)
    字体18["取图像"](字体18, "现金")["置混合"](字体18["取图像"](字体18, "现金"), 0)["显示"](字体18["取图像"](字体18, "现金")["置混合"](字体18["取图像"](字体18, "现金"), 0), 26, 352)
    字体18["取图像"](字体18, "储备")["置混合"](字体18["取图像"](字体18, "储备"), 0)["显示"](字体18["取图像"](字体18, "储备")["置混合"](字体18["取图像"](字体18, "储备"), 0), 182, 352)
    字体18["取图像"](字体18, "存银")["置混合"](字体18["取图像"](字体18, "存银"), 0)["显示"](字体18["取图像"](字体18, "存银")["置混合"](字体18["取图像"](字体18, "存银"), 0), 26, 391)
    nsf["渲染结束"](nsf)
  end
  self.模型格子 = __UI模型格子["创建"]()
  self:置精灵(nsf["到精灵"](nsf))
  self.动画 = {}
end
function 人物界面:重置()
  local nsf = require("SDL.图像")(333, 486)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取银子颜色(角色信息["银子"]))
    字体18["取图像"](字体18, 角色信息["银子"])["置混合"](字体18["取图像"](字体18, 角色信息["银子"]), 0)["显示"](字体18["取图像"](字体18, 角色信息["银子"])["置混合"](字体18["取图像"](字体18, 角色信息["银子"]), 0), 80, 354)
    字体18["置颜色"](字体18, __取银子颜色(角色信息["储备"]))
    字体18["取图像"](字体18, 角色信息["储备"])["置混合"](字体18["取图像"](字体18, 角色信息["储备"]), 0)["显示"](字体18["取图像"](字体18, 角色信息["储备"])["置混合"](字体18["取图像"](字体18, 角色信息["储备"]), 0), 236, 354)
    字体18["置颜色"](字体18, __取银子颜色(角色信息["存银"]))
    字体18["取图像"](字体18, 角色信息["存银"])["置混合"](字体18["取图像"](字体18, 角色信息["存银"]), 0)["显示"](字体18["取图像"](字体18, 角色信息["存银"])["置混合"](字体18["取图像"](字体18, 角色信息["存银"]), 0), 80, 394)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
function 人物界面:模型重置()
  self.模型格子["置数据"](self.模型格子, 角色信息, "角色", 175, 250)
end
local 灵饰网格 = 人物界面["创建网格"](人物界面, "灵饰网格", 30, 63, 297, 57)
function 灵饰网格:初始化()
  self:创建格子(57, 57, 0, 22, 1, 4)
end
function 灵饰网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520, 86, w, h, "灵饰", a)
  end
end
-- local fwrrw={}
--   for i = 1, 4 do
--     if 数据[i] then
--       fwrrw[fghh[数据[i].部位类型]]=数据[i]
--     end
--   end
--   for i = 1, #灵饰网格["子控件"] do
--     if fwrrw[i] then
--       local lssj = __物品格子["创建"]()
--       lssj["置物品"](lssj, fwrrw[i], nil, "装备")
--       灵饰网格["子控件"][i]["置精灵"](灵饰网格["子控件"][i], lssj)
--     else
--       灵饰网格["子控件"][i]["置精灵"](灵饰网格["子控件"][i])
--     end
--   end
function 灵饰网格:置物品(数据)
  for i = 1, #灵饰网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "装备")
      灵饰网格["子控件"][i]["置精灵"](灵饰网格["子控件"][i], lssj)
    else
      灵饰网格["子控件"][i]["置精灵"](灵饰网格["子控件"][i])
    end
  end
end
local 装备网格 = 人物界面["创建网格"](人物界面, "装备网格", 30, 135, 296, 203)
function 装备网格:初始化()
  self:创建格子(57, 57, 16, 181, 3, 2)
end
function 装备网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520, 86, w, h, "装备", a)
  end
end
function 装备网格:置物品(数据)
  for i = 1, #装备网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "装备")
      装备网格["子控件"][i]["置精灵"](装备网格["子控件"][i], lssj)
    else
      装备网格["子控件"][i]["置精灵"](装备网格["子控件"][i])
    end
  end
end
local 法宝 = 人物界面["创建我的按钮"](人物界面, __res:getPNGCC(3, 511, 11, 117, 43, true), "法宝", 26, 430, "法 宝")
function 法宝:左键弹起(x, y, msg)
  if 道具行囊["包裹类型"] == "道具" and 道具行囊["抓取物品ID"] and 道具行囊["抓取类型"] and 道具行囊["移动"] and 抓取物品20格子计算~=0 and 道具行囊["道具网格"]["子控件"][抓取物品20格子计算]._spr["物品"]["总类"] == 1000 then
    发送数据(3746, {
      ["序列"] = 道具行囊["抓取物品ID"],
      ["放置类型"] = "法宝",
      ["抓取类型"] = 道具行囊["抓取类型"]
    })
    道具行囊["重置抓取"](道具行囊)
  elseif not 道具行囊["抓取物品ID"] then
    道具行囊["关闭"]["左键弹起"](道具行囊["关闭"])
    __UI界面["窗口层"]["法宝"]["打开"](__UI界面["窗口层"]["法宝"])
    发送数据(3732)
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y不是什么东西都可以放到法宝里的")
  end
end
local 锦衣 = 人物界面["创建我的按钮"](人物界面, __res:getPNGCC(3, 511, 11, 117, 43, true), "锦衣", 197, 430.0, "锦 衣")
function 锦衣:左键弹起(x, y, msg)
  __UI弹出["锦衣弹出"]["打开"](__UI弹出["锦衣弹出"], 角色信息["锦衣"])
end
local 召唤兽界面 = 道具行囊["创建我的控件"](道具行囊, "召唤兽界面", 0, 0, 350, 486)
function 召唤兽界面:初始化()
  local nsf = require("SDL.图像")(350, 486)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 375, 388, 145, 149)["显示"](__res:getPNGCC(3, 375, 388, 145, 149), 105, 105)
    local lssj = 取输入背景(0, 0, 168, 23)
    lssj["显示"](lssj, 31, 68)
    lssj = 取输入背景(0, 0, 106, 23)
    lssj["显示"](lssj, 74, 272)
    lssj["显示"](lssj, 226, 272)
    字体18["置颜色"](字体18, 255, 255, 255)
    字体18["取图像"](字体18, "气血")["置混合"](字体18["取图像"](字体18, "气血"), 0)["显示"](字体18["取图像"](字体18, "气血")["置混合"](字体18["取图像"](字体18, "气血"), 0), 31, 274)
    字体18["取图像"](字体18, "魔法")["置混合"](字体18["取图像"](字体18, "魔法"), 0)["显示"](字体18["取图像"](字体18, "魔法")["置混合"](字体18["取图像"](字体18, "魔法"), 0), 185, 274)
    取属性背景(0, 0, 333, 163, true)["显示"](取属性背景(0, 0, 333, 163, true), 10, 310)
    nsf["渲染结束"](nsf)
  end
  self.模型格子 = __UI模型格子["创建"]()
  self:置精灵(nsf["到精灵"](nsf))
  self:置可见(false)
end
function 召唤兽界面:重置()
  local nsf = require("SDL.图像")(333, 486)
  if nsf["渲染开始"](nsf) then
    if 道具行囊["选中召唤兽"] and 角色信息["宝宝列表"][道具行囊["选中召唤兽"]] then
      字体18["置颜色"](字体18, 0, 0, 0)
      字体18["取图像"](字体18, 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["名称"])["置混合"](字体18["取图像"](字体18, 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["名称"]), 0)["显示"](字体18["取图像"](字体18, 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["名称"])["置混合"](字体18["取图像"](字体18, 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["名称"]), 0), 40, 72)
      字体18["取图像"](字体18, string.format("%s/%s", 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["气血"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["最大气血"]))["置混合"](字体18["取图像"](字体18, string.format("%s/%s", 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["气血"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["最大气血"])), 0)["显示"](字体18["取图像"](字体18, string.format("%s/%s", 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["气血"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["最大气血"]))["置混合"](字体18["取图像"](字体18, string.format("%s/%s", 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["气血"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["最大气血"])), 0), 86, 273)
      字体18["取图像"](字体18, string.format("%s/%s", 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["魔法"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["最大魔法"]))["置混合"](字体18["取图像"](字体18, string.format("%s/%s", 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["魔法"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["最大魔法"])), 0)["显示"](字体18["取图像"](字体18, string.format("%s/%s", 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["魔法"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["最大魔法"]))["置混合"](字体18["取图像"](字体18, string.format("%s/%s", 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["魔法"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["最大魔法"])), 0), 229, 273)
      __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 300-30, 400-205)
    end
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
function 召唤兽界面:模型重置()
  if 道具行囊["选中召唤兽"] and 角色信息["宝宝列表"][道具行囊["选中召唤兽"]] then
    self.模型格子["置数据"](self.模型格子, 角色信息["宝宝列表"][道具行囊["选中召唤兽"]], "召唤兽", 178, 210)
    召唤兽界面["装备网格"]["置物品"](召唤兽界面["装备网格"], 角色信息["宝宝列表"][道具行囊["选中召唤兽"]]["装备"])
  end
end
local 装备网格 = 召唤兽界面["创建网格"](召唤兽界面, "装备网格", 31, 104, 305, 150)
function 装备网格:初始化()
  self:创建格子(55, 55, 38, 184, 2, 2)
end
function 装备网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520, 86, w, h, "装备", a)
  end
end
function 装备网格:置物品(数据)
  for i = 1, #装备网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], "白格子", "装备")
      装备网格["子控件"][i]["置精灵"](装备网格["子控件"][i], lssj)
    else
      装备网格["子控件"][i]["置精灵"](装备网格["子控件"][i])
    end
  end
end

-- local bb饰品网格 = 召唤兽界面["创建网格"](召唤兽界面, "bb饰品网格", 31, 104, 305, 150)
-- function bb饰品网格:初始化()
--   self:创建格子(55, 55, 38, 184, 1, 1)
-- end
-- function bb饰品网格:左键弹起(x, y, a, b, msg)
--   if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
--     local x, y = self.子控件[a]["取坐标"](self.子控件[a])
--     local w, h = self.子控件[a]["取宽高"](self.子控件[a])
--     self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 520, 86, w, h, "装备", a)
--   end
-- end
-- function bb饰品网格:置物品()
--   for i = 1, #bb饰品网格["子控件"] do
--     if 数据[i] then
--       local lssj = __物品格子["创建"]()
--       lssj["置物品"](lssj, 数据[i], "白格子", "装备")
--       bb饰品网格["子控件"][i]["置精灵"](bb饰品网格["子控件"][i], lssj)
--     else
--       bb饰品网格["子控件"][i]["置精灵"](bb饰品网格["子控件"][i])
--     end
--   end
-- end



local 头像网格 = 召唤兽界面["创建网格"](召唤兽界面, "头像网格", 20, 323, 315, 140)
function 头像网格:初始化()
  self:创建格子(73, 73, 6, 6, 3, 4, true)
end
function 头像网格:左键弹起(x, y, a, b, msg)
  if 道具行囊["选中召唤兽"] then
    self.子控件[道具行囊["选中召唤兽"]]._spr["确定"] = nil
    道具行囊["选中召唤兽"] = nil
  end
  if self.子控件[a]._spr then
    self.子控件[a]._spr["确定"] = true
    道具行囊["选中召唤兽"] = a
  end
  召唤兽界面["重置"](召唤兽界面)
  召唤兽界面["模型重置"](召唤兽界面)
end
function 头像网格:置头像(数据)
  for i = 1, #头像网格["子控件"] do
    if 数据[i] then
      local lssj = __头像格子["创建"]()
      lssj["置头像"](lssj, 数据[i], "大")
      头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i], lssj)
    else
      头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i])
    end
  end
end
local 坐骑界面 = 道具行囊["创建我的控件"](道具行囊, "坐骑界面", 0, 0, 350, 486)
function 坐骑界面:初始化()
  local nsf = require("SDL.图像")(350, 486)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 375, 388, 145, 149)["显示"](__res:getPNGCC(3, 375, 388, 145, 149), 30, 104)
    local lssj = 取输入背景(0, 0, 168, 23)
    lssj["显示"](lssj, 30, 68)
    lssj = 取输入背景(0, 0, 75, 23)
    lssj["显示"](lssj, 73, 270)
    lssj["显示"](lssj, 234, 270)
    字体18["置颜色"](字体18, 255, 255, 255)
    字体18["取图像"](字体18, "好感")["置混合"](字体18["取图像"](字体18, "好感"), 0)["显示"](字体18["取图像"](字体18, "好感")["置混合"](字体18["取图像"](字体18, "好感"), 0), 29, 270)
    字体18["取图像"](字体18, "饱食")["置混合"](字体18["取图像"](字体18, "饱食"), 0)["显示"](字体18["取图像"](字体18, "饱食")["置混合"](字体18["取图像"](字体18, "饱食"), 0), 189, 270)
    取属性背景(0, 0, 330, 163, true)["显示"](取属性背景(0, 0, 330, 163, true), 10, 310)
    nsf["渲染结束"](nsf)
  end
  self.模型格子 = __UI模型格子["创建"]()
  self:置精灵(nsf["到精灵"](nsf))
  self:置可见(false)
end
function 坐骑界面:重置()
  local nsf = require("SDL.图像")(333, 486)
  if nsf["渲染开始"](nsf) then
    if 道具行囊["选中坐骑"] then
      字体18["置颜色"](字体18, 0, 0, 0)
      字体18["取图像"](字体18, 角色信息["坐骑列表"][道具行囊["选中坐骑"]]["名称"])["置混合"](字体18["取图像"](字体18, 角色信息["坐骑列表"][道具行囊["选中坐骑"]]["名称"]), 0)["显示"](字体18["取图像"](字体18, 角色信息["坐骑列表"][道具行囊["选中坐骑"]]["名称"])["置混合"](字体18["取图像"](字体18, 角色信息["坐骑列表"][道具行囊["选中坐骑"]]["名称"]), 0), 38, 72)
      字体18["取图像"](字体18, "100")["置混合"](字体18["取图像"](字体18, "100"), 0)["显示"](字体18["取图像"](字体18, "100")["置混合"](字体18["取图像"](字体18, "100"), 0), 80, 273)
      字体18["取图像"](字体18, "100")["置混合"](字体18["取图像"](字体18, "100"), 0)["显示"](字体18["取图像"](字体18, "100")["置混合"](字体18["取图像"](字体18, "100"), 0), 240, 273)
    end
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
function 坐骑界面:模型重置()
  if 道具行囊["选中坐骑"] then
    self.模型格子["置数据"](self.模型格子, 角色信息["坐骑列表"][道具行囊["选中坐骑"]], "坐骑", 100, 210, 角色信息["模型"])
    坐骑界面["装备网格"]["置物品"](坐骑界面["装备网格"], 角色信息["坐骑列表"][道具行囊["选中坐骑"]]["饰品"])
  end
end
local 装备网格 = 坐骑界面["创建网格"](坐骑界面, "装备网格", 190, 122, 55, 55)
function 装备网格:初始化()
  self:创建格子(55, 55, 0, 0, 1, 1)
end
function 装备网格:置物品(数据)
  for i = 1, #装备网格["子控件"] do
    if 数据 then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据, "白格子", "装备")
      装备网格["子控件"][i]["置精灵"](装备网格["子控件"][i], lssj)
    else
      装备网格["子控件"][i]["置精灵"](装备网格["子控件"][i])
    end
  end
end
local 头像网格 = 坐骑界面["创建网格"](坐骑界面, "头像网格", 15, 317, 315, 154)
function 头像网格:初始化()
  self:创建格子(73, 73, 4, 8, 2, 4)
end
function 头像网格:左键弹起(x, y, a, b, msg)
  if 道具行囊["选中坐骑"] then
    self.子控件[道具行囊["选中坐骑"]]._spr["确定"] = nil
    道具行囊["选中坐骑"] = nil
  end
  if self.子控件[a]._spr then
    self.子控件[a]._spr["确定"] = true
    道具行囊["选中坐骑"] = a
    if 角色信息["坐骑"] and 角色信息["坐骑列表"] and 角色信息["坐骑"]["认证码"] == 角色信息["坐骑列表"][道具行囊["选中坐骑"]]["认证码"] then
      坐骑界面["我的按钮置文字"](坐骑界面, 坐骑界面["骑乘"], __res:getPNGCC(3, 126, 563, 111, 36, true), "下骑")
    else
      坐骑界面["我的按钮置文字"](坐骑界面, 坐骑界面["骑乘"], __res:getPNGCC(3, 126, 563, 111, 36, true), "骑乘")
    end
  end
  坐骑界面["重置"](坐骑界面)
  坐骑界面["模型重置"](坐骑界面)
end
function 头像网格:置头像(数据)
  for i = 1, #头像网格["子控件"] do
    if 数据[i] then
      local lssj = __头像格子["创建"]()
      lssj["置头像"](lssj, 数据[i], "大")
      头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i], lssj)
    else
      头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i])
    end
  end
end
local 骑乘 = 坐骑界面["创建我的按钮"](坐骑界面, __res:getPNGCC(3, 126, 563, 111, 36, true), "骑乘", 211, 62, "骑乘")
function 骑乘:左键弹起(x, y, msg)
  if 道具行囊["选中坐骑"] then
    if 角色信息["坐骑"] and 角色信息["坐骑列表"] and 角色信息["坐骑"]["认证码"] == 角色信息["坐骑列表"][道具行囊["选中坐骑"]]["认证码"] then
      发送数据(27, {
        ["序列"] = 0
      })
      坐骑界面["我的按钮置文字"](坐骑界面, self, __res:getPNGCC(3, 126, 563, 111, 36, true), "骑乘")
    else
      发送数据(26, {
        ["序列"] = 道具行囊["选中坐骑"]
      })
      坐骑界面["我的按钮置文字"](坐骑界面, self, __res:getPNGCC(3, 126, 563, 111, 36, true), "下骑")
    end
  end
end
local 属性 = 坐骑界面["创建我的按钮"](坐骑界面, __res:getPNGCC(3, 244, 563, 78, 34, true), "属性", 256, 144, "属性")
function 属性:左键弹起(x, y, msg)
  __UI界面["窗口层"].坐骑属性栏:打开()
end
local 子女界面 = 道具行囊["创建我的控件"](道具行囊, "子女界面", 0, 0, 350, 486)
function 子女界面:初始化()
  local nsf = require("SDL.图像")(350, 486)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 375, 388, 145, 149)["显示"](__res:getPNGCC(3, 375, 388, 145, 149), 30, 120)
    local lssj = 取输入背景(0, 0, 166, 23)
    lssj["显示"](lssj, 30, 68)
    取属性背景(0, 0, 330, 163, true)["显示"](取属性背景(0, 0, 330, 163, true), 10, 310)
    nsf["渲染结束"](nsf)
  end
  self.模型格子 = __UI模型格子["创建"]()
  self:置精灵(nsf["到精灵"](nsf))
  self:置可见(false)
end
function 子女界面:重置()
  local nsf = require("SDL.图像")(333, 486)
  if nsf["渲染开始"](nsf) then
    if 道具行囊["选中子女"] then
      字体18["置颜色"](字体18, __取颜色("黑色"))
      字体18["取图像"](字体18, 角色信息["子女列表"][道具行囊["选中子女"]]["名称"])["置混合"](字体18["取图像"](字体18, 角色信息["子女列表"][道具行囊["选中子女"]]["名称"]), 0)["显示"](字体18["取图像"](字体18, 角色信息["子女列表"][道具行囊["选中子女"]]["名称"])["置混合"](字体18["取图像"](字体18, 角色信息["子女列表"][道具行囊["选中子女"]]["名称"]), 0), 38, 72)
    end
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
function 子女界面:模型重置()
  if 道具行囊["选中子女"] then
    self.模型格子["置数据"](self.模型格子, 角色信息["子女列表"][道具行囊["选中子女"]], "子女", 100, 210)
  end
end
local 头像网格 = 子女界面["创建网格"](子女界面, "头像网格", 15, 317, 315, 154)
function 头像网格:初始化()
  self:创建格子(73, 73, 4, 8, 1, 4)
end
function 头像网格:左键弹起(x, y, a, b, msg)
  if 道具行囊["选中子女"] then
    self.子控件[道具行囊["选中子女"]]._spr["确定"] = nil
    道具行囊["选中子女"] = nil
  end
  if self.子控件[a]._spr then
    self.子控件[a]._spr["确定"] = true
    道具行囊["选中子女"] = a
  end
  子女界面["重置"](子女界面)
  子女界面["模型重置"](子女界面)
end
function 头像网格:置头像(数据)
  for i = 1, #头像网格["子控件"] do
    if 数据[i] then
      local lssj = __头像格子["创建"]()
      lssj["置头像"](lssj, 数据[i], "大")
      头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i], lssj)
    else
      头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i])
    end
  end
end
for i, v in ipairs({
  {
    name = "参战",
    x = 211,
    y = 62,
    tcp = __res:getPNGCC(3, 126, 563, 111, 36, true),
    font = "参战"
  },
  {
    name = "培养",
    x = 160,
    y = 270,
    tcp = __res:getPNGCC(3, 126, 563, 111, 36, true),
    font = "培养"
  },
  {
    name = "属性",
    x = 31,
    y = 270,
    tcp = __res:getPNGCC(3, 126, 563, 111, 36, true),
    font = "属性"
  }
}) do
  local 临时函数 = 子女界面["创建我的按钮"](子女界面, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if 道具行囊["选中子女"] then
      if v.name == "参战" then
        发送数据(6702, {
          ["序列"] = 道具行囊["选中子女"]
        })
      elseif v.name == "属性" then
        __UI界面["窗口层"]["子女查看"]["打开"](__UI界面["窗口层"]["子女查看"], 角色信息["子女列表"][道具行囊["选中子女"]])
      elseif v.name == "培养" then
        __UI界面["窗口层"]["子女养育"]["打开"](__UI界面["窗口层"]["子女养育"], 角色信息["子女列表"][道具行囊["选中子女"]])       
      end
    end
  end
end
