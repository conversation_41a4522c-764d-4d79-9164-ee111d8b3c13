
local 人物属性 = 窗口层:创建窗口("人物属性", 0, 0, 323, 440)
local bd = {"体质","魔力","力量","耐力","敏捷","潜力"}
local bd1 = {"命中","伤害","防御","速度","法伤","法防"}
local 取属性=function(种族,三维,门派)
        local 输出属性={气血=0,魔法=0,命中=0,伤害=0,防御=0,速度=0,法伤=0,法防=0}
        if 种族=="人" or 种族 == 1 then
              输出属性.命中=math.floor(三维.力量*2)
              输出属性.伤害=math.floor(三维.力量*0.67)
              输出属性.防御=math.floor(三维.耐力*1.5)
              输出属性.速度=math.floor(三维.敏捷)
              输出属性.法伤=math.floor(三维.体质*0.3+三维.魔力*0.7+三维.耐力*0.2+三维.力量*0.4)
              输出属性.气血=math.floor(三维.体质*5)
              输出属性.魔法=math.floor(三维.魔力*3)
        elseif 种族 =="魔" or 种族 == 2 then
                输出属性.命中=math.floor(三维.力量*2.3)
                输出属性.伤害=math.floor(三维.力量*0.77)
                输出属性.防御=math.floor(三维.耐力*214/153)
                输出属性.速度=math.floor(三维.敏捷)
                输出属性.法伤=math.floor(三维.体质*0.3+三维.魔力*0.7+三维.耐力*0.2+三维.力量*0.4)
                输出属性.气血=math.floor(三维.体质*6)
                输出属性.魔法=math.floor(三维.魔力*2.5)
        elseif 种族 =="仙" or 种族 == 3 then
                输出属性.命中=math.floor(三维.力量*1.7)
                输出属性.伤害=math.floor(三维.力量*0.57)
                输出属性.防御=math.floor(三维.耐力*1.6)
                输出属性.速度=math.floor(三维.敏捷)
                输出属性.法伤=math.floor(三维.体质*0.3+三维.魔力*0.7+三维.耐力*0.2+三维.力量*0.4)
                输出属性.气血=math.floor(三维.体质*4.5)
                输出属性.魔法=math.floor(三维.魔力*3.5)
        end
        if 门派 =="九黎城" then
              输出属性.命中=math.floor(三维.力量*2.3)
              输出属性.伤害=math.floor(三维.力量*0.77)
              输出属性.防御=math.floor(三维.耐力*214/153)
              输出属性.速度=math.floor(三维.敏捷)
              输出属性.法伤=math.floor(三维.体质*0.3+三维.魔力*0.7+三维.耐力*0.2+三维.力量*0.4)
              输出属性.气血=math.floor(三维.体质*6)
              输出属性.魔法=math.floor(三维.魔力*2.5)
        end
        输出属性.法防=math.floor(输出属性.法伤*0.7)

        return 输出属性
end





function 人物属性:初始化()
      
        self:置坐标(引擎.宽度 - 330,80)
        self.状态="属性"
        self.可初始化=true
        if __手机 then
            self.关闭:置大小(25,25)
            self.关闭:置坐标(self.宽度-27, 2)
        else
            self.关闭:置大小(16,16)
            self.关闭:置坐标(self.宽度-18, 2)
        end  
end



function 人物属性:打开()
        self:置可见(not self.是否可见)
        if not self.是否可见 then
              return
        end
        self.状态="属性"
        self.选中师门=nil
        self.选中技能=nil
        self:显示设置()
end


function 人物属性:显示(x,y)
        if self.图像 then
            self.图像:显示(x+23,y+35)
        end
end




function 人物属性:显示设置()
        self.图像=nil
        self.称谓:置可见(false)
        self.帮贡:置可见(false)
        self.活力:置可见(false)
        self.体力:置可见(false)
        self.升级:置可见(false)
        self.重置:置可见(false)
        self.使用:置可见(false)
        self.自动:置可见(false)
        self.经验条:置可见(false)
        self.功德录:置可见(false)
        self.更多属性:置可见(false)
        self.推荐加点:置可见(false)
        self.确认加点:置可见(false)
        self.奇经八脉:置可见(false)
        self.包含列表:置可见(false)
        self.师门列表:置可见(false)
        self.特技网格:置可见(false)
        self.辅助控件:置可见(false)
        self.修炼控件:置可见(false)
        for i=1,5 do
            self[bd[i].."加"]:置可见(false)
            self[bd[i].."减"]:置可见(false)
        end
 





        self[self.状态.."技能"]:置选中(true)
        self.临时加点={体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
        self.预览属性={气血=0,魔法=0,命中=0,伤害=0,防御=0,速度=0,法伤=0,法防=0}
        self.角色信息 = table.copy(角色信息)
        self:创建纹理精灵(function ()
              if self.状态=="属性" then
                   self:人物显示()
              elseif self.状态=="师门" then
                    self:师门显示()
              elseif self.状态=="辅助" then 
                        置窗口背景("辅助技能",0, 0, 300, 440,true):显示(23,0)
                        local xx=0
                        local yy=0
                        for i = 1, 12 do
                            __res:取资源动画("jszy/fwtb",0x00000030,"图像"):显示(59+xx*60,86+yy*85)
                            取输入背景(0, 0, 40, 23):显示(59+xx*60,135+yy*85)
                            xx=xx+1
                            if xx>=4 then
                                xx=0
                                yy=yy+1
                            end
                          
                        end

              elseif self.状态=="修炼" then 
                      置窗口背景("人物修炼",0, 0, 300, 440,true):显示(23,0)
                      文本字体:置颜色(255, 255, 255, 255)
                      文本字体:取图像("角色自身修炼  当前:"):显示(43,35)
                      文本字体:取图像("召唤兽控制修炼  当前:"):显示(43,220)
                      取白色背景(0, 0, 255, 150, true):显示(43, 60)
                      取白色背景(0, 0, 255, 150, true):显示(43, 245)
                     
              end
        end)

        if self.状态=="辅助" then 
              self.辅助控件:置可见(true)
              self.辅助控件.状态="生活技能"
              self.辅助控件:显示设置()
        elseif self.状态=="修炼" then 
              self.修炼控件:置可见(true)
              self.修炼控件:显示设置()
        end    
        if __手机 then
            self.重置:置文字(40,22,"加点")
        else
            self.重置:置文字(40,22,"重置")
        end
end

function 人物属性:人物显示()
        self.称谓:置可见(true)
        self.帮贡:置可见(true)
        self.活力:置可见(true)
        self.体力:置可见(true)
        self.升级:置可见(true)
        self.重置:置可见(true)
        self.经验条:置可见(true)
        self.更多属性:置可见(true)
        self.推荐加点:置可见(true)
        self.确认加点:置可见(true)
        for i=1,5 do
            self[bd[i].."加"]:置可见(true)
            self[bd[i].."减"]:置可见(true)
        end
        置窗口背景("人物属性",0, 0, 300, 440,true):显示(23,0)
        for i=1,4 do
            取输入背景(0, 0, 130, 23):显示(68,35+(i-1)*26)
            取输入背景(0, 0, 60, 23):显示(248,35+(i-1)*26)
        end
        文本字体:置颜色(255, 255, 255,255)
        文本字体:取图像("名称"):显示(36,38)
        文本字体:取图像("帮派"):显示(36,90)
        文本字体:取图像("门派"):显示(36,116)
        文本字体:取图像("等级"):显示(211,38)
        文本字体:取图像("人气"):显示(211,64)
        文本字体:取图像("门贡"):显示(211,116)
        取输入背景(0, 0, 240, 23):显示(68,145)
        文本字体:取图像("气血"):显示(36,148)
        for i=1,2 do
            取输入背景(0, 0, 95, 23):显示(68,171+(i-1)*26)
            取输入背景(0, 0, 95, 23):显示(213,171+(i-1)*26)
        end
        文本字体:取图像("魔法"):显示(36,174)
        文本字体:取图像("愤怒"):显示(36,200)
        for i=1,6 do
          取输入背景(0, 0, 95, 23):显示(68,227+(i-1)*26)
          取输入背景(0, 0, 44, 23):显示(213,227+(i-1)*26)
          文本字体:取图像(bd1[i]):显示(36,230+(i-1)*26)
          文本字体:取图像(bd[i]):显示(178,230+(i-1)*26)
        end
        __res:取资源动画("pic","bossjyt.png","图片"):拉伸(190, 18):显示(68,415)---经验条背景
        文本字体:取图像("经验"):显示(36,415)
        文本字体:置颜色(0, 0, 0, 255)
        文本字体:取图像(self.角色信息.名称):显示(73,39)
        if self.角色信息.当前称谓 and self.角色信息.当前称谓~="" then
            文本字体:取图像(self.角色信息.当前称谓):显示(73,65)
        end
        if self.角色信息.帮派 then
            文本字体:取图像(self.角色信息.帮派):显示(73,91)
        end
        if self.角色信息.门派 then
            文本字体:取图像(self.角色信息.门派):显示(73,117)
        else
            文本字体:取图像("无门派"):显示(73,117)
        end
        文本字体:取图像(self.角色信息.等级):显示(253,39)
        文本字体:取图像(self.角色信息.人气):显示(253,65)
        if self.角色信息.帮贡 then
            文本字体:取图像(self.角色信息.帮贡):显示(253,91)
        else
            文本字体:取图像(0):显示(253,91)
        end
        if self.角色信息.门贡 then
            文本字体:取图像(self.角色信息.门贡):显示(253,117)
        else
            文本字体:取图像(0):显示(253,117)
        end
        文本字体:取图像(self.角色信息.气血.."/"..self.角色信息.气血上限.."/"..self.角色信息.最大气血):显示(73,149)
        文本字体:取图像(self.角色信息.魔法.."/"..self.角色信息.最大魔法):显示(73,175)
        文本字体:取图像(self.角色信息.愤怒.."/150"):显示(73,201)
        文本字体:取图像(self.角色信息.活力.."/"..self.角色信息.最大活力):显示(218,175)
        文本字体:取图像(self.角色信息.体力.."/"..self.角色信息.最大体力):显示(218,201)
        for i=1,6 do
            文本字体:取图像(self.角色信息[bd1[i]]):显示(73,231+(i-1)*26)
        end
        for i = 1, 5 do
            文本字体:取图像(self.角色信息[bd[i]]):显示(218,231+(i-1)*26)
        end
        self.经验条.显示经验= 文本字体:置颜色(255, 255, 255, 255):取投影精灵(string.format("%s/%s", self.角色信息.当前经验, self.角色信息.最大经验),0,0,0,255)
        self.经验条:置位置(math.floor(self.角色信息.当前经验 / self.角色信息.最大经验 * 100))
        self:属性显示()
end
function 人物属性:属性显示()
        for i=1,5 do
            self[bd[i].."加"]:置禁止(true)
            self[bd[i].."减"]:置禁止(true)
            if self.角色信息 and self.角色信息.潜力>0 then
                self[bd[i].."加"]:置禁止(false)
            end
            if self.临时加点[bd[i]]>0 then
                self[bd[i].."减"]:置禁止(false)
            end
        end
        if self.角色信息 then
            self.预览属性=取属性(self.角色信息.种族,self.临时加点,self.角色信息.门派)
        end
        self.图像=self:创建纹理精灵(function ()
              if self.角色信息 then
                if self.预览属性.气血>0 then
                      文本字体:置颜色(__取颜色("红色")):取图像("+"..self.预览属性.气血):显示(50+(230-文本字体:取宽度("+"..self.预览属性.气血)),111)
                end
                if self.预览属性.魔法>0 then
                    文本字体:置颜色(__取颜色("红色")):取图像("+"..self.预览属性.魔法):显示(50+(85-文本字体:取宽度("+"..self.预览属性.魔法)),137)
                end
            
                for i=1,6 do
                    if self.预览属性[bd1[i]]>0 then
                        文本字体:置颜色(__取颜色("红色")):取图像("+"..self.预览属性[bd1[i]]):显示(50+(85-文本字体:取宽度("+"..self.预览属性[bd1[i]])),193+(i-1)*26)
                    end
                end
                for i=1,5 do
                    if self.临时加点[bd[i]]>0 then
                        文本字体:置颜色(__取颜色("红色")):取图像("+"..self.临时加点[bd[i]]):显示(195+(38-文本字体:取宽度("+"..self.临时加点[bd[i]])),193+(i-1)*26)
                    end
                end
                文本字体:置颜色(0,0,0,255):取图像(self.角色信息.潜力):显示(195,326)
              end
          end,1,300, 405)

end


function 人物属性:师门显示()
          self.使用:置可见(true)
          self.自动:置可见(true)
          self.功德录:置可见(true)
          self.奇经八脉:置可见(true)
          self.包含列表:置可见(true)
          self.师门列表:置可见(true)
          self.特技网格:置可见(true)
          self.师门列表:置数据()
          self.包含列表:置数据()
          self.特技网格:置数据()
          置窗口背景("师门技能",0, 0, 300, 440,true):显示(23,0)
          蓝白标题背景(130,320,true):显示(38,35)
          蓝白标题背景(130,320,true):显示(178,35)
          标题字体:置颜色(255, 255, 255, 255):取图像("师门技能"):显示(68,40)
          标题字体:置颜色(255, 255, 255, 255):取图像("师门法术"):显示(213,40)
          文本字体:置颜色(255, 255, 255, 255):取图像("特技"):显示(38,370)
          for i=1,6 do
              __res:取资源动画("jszy/fwtb",0x00000031,"图像"):显示(38+(i-1)*35,395)
          end

end




local 经验条 = 人物属性:创建进度("经验条", 69, 416, 188, 16)  
function 经验条:初始化()
  self:置精灵(__res:取资源动画("dlzy",0x3906F9F1,"图像"):拉伸(188,16):到精灵())
end
function 经验条:显示(x, y)
      if self.显示经验 then
          self.显示经验:显示(x + (self.宽度-self.显示经验.宽度)//2, y-1)
      end
end


local 称谓 = 人物属性:创建红色按钮("称谓", "称谓", 25, 62,40,22) --红色
function 称谓:左键弹起(x, y)
    窗口层.人物称谓:打开()
end
local 帮贡 = 人物属性:创建红色按钮("帮贡", "帮贡", 205, 88,40,22) --红色
local 活力 = 人物属性:创建红色按钮("活力", "活力", 170, 173,40,22) --红色
local 体力 = 人物属性:创建红色按钮("体力", "体力", 170, 199,40,22) --红色

local 更多属性 = 人物属性:创建红色按钮("更多属性", "更多属性", 36, 385,74,22) --红色

function 更多属性:左键弹起(x, y)
    窗口层.更多属性:打开()
end
local 推荐加点 = 人物属性:创建红色按钮("推荐加点", "推荐加点", 135, 385,74,22) --红色

function 推荐加点:左键弹起(x, y)
        if 人物属性.角色信息 then
              local 是否推荐 =true
              local 记录加点 = {}
              local 已加属性 = 人物属性.角色信息.等级*5+5-人物属性.角色信息.潜力
              if 人物属性.角色信息.潜能果~=nil then
                已加属性=已加属性+人物属性.角色信息.潜能果
              end
              if 人物属性.角色信息.月饼~=nil then
                已加属性=已加属性+人物属性.角色信息.月饼*2
              end
              if 人物属性.角色信息.五虎上将 ~= nil then
                  if 人物属性.角色信息.五虎上将 == 1 then
                    已加属性 = 已加属性 + 10
                  elseif 人物属性.角色信息.五虎上将 == 2 then
                    已加属性 = 已加属性 + 30
                  elseif 人物属性.角色信息.五虎上将 == 3 then
                    已加属性 = 已加属性 + 60
                  elseif 人物属性.角色信息.五虎上将 >= 4 then
                    已加属性 = 已加属性 + 100
                  elseif 人物属性.角色信息.五虎上将 >= 5 then
                    已加属性 = 已加属性 + 150
                  end
              end
              if 人物属性.角色信息.飞升 then
                  已加属性 = 已加属性 +100
              end
              if  人物属性.角色信息.加点记录 then
                  for i=1,5 do
                    记录加点[bd[i]]=0
                    if 人物属性.角色信息.加点记录[bd[i]]~=0 and 已加属性>=5  then
                        记录加点[bd[i]]=math.floor(人物属性.角色信息.加点记录[bd[i]]/已加属性*5)
                        是否推荐 = false
                    end
                  end
              end
              if 是否推荐 then
                人物属性.临时加点.力量 = 人物属性.临时加点.力量 + 人物属性.角色信息.潜力
                人物属性.角色信息.潜力 = 0
            else
                  local 循环次数 = 人物属性.角色信息.潜力
                  for i=1,循环次数 do
                      if 人物属性.角色信息.潜力>0 then
                          for k,v in pairs(记录加点) do
                              if 人物属性.角色信息.潜力>= v then
                                人物属性.角色信息.潜力 = 人物属性.角色信息.潜力-v
                                人物属性.临时加点[k]=人物属性.临时加点[k]+v
                              end
                          end
                      end
                  end
            end
            人物属性:属性显示()  

        end
end
local 确认加点 = 人物属性:创建红色按钮("确认加点", "确认加点", 235, 385,74,22) --红色

function 确认加点:左键弹起(x, y)
          local 是否发送 =false
          for i=1,5 do
                  if 人物属性.临时加点[bd[i]]>0 then
                        是否发送 =true
                  end
          end
          if 是否发送 then
                请求服务(8, 人物属性.临时加点)
          else
               __UI弹出.提示框:打开('#Y请先加点后在操作')
          end
       
end
local 升级 = 人物属性:创建红色按钮("升级", "升级", 265, 412,45,22) --红色

function 升级:左键弹起(x, y)
        请求服务(9)
end

local 重置 = 人物属性:创建红色按钮("重置", "重置", 265, 360,40,22) --红色

function 重置:左键弹起(x, y)
    if __手机 then
          窗口层.属性加点:打开(人物属性.角色信息,"角色")
    else
          人物属性:显示设置()
    end
end


for i=1,5 do
      local 加号 = 人物属性:创建按钮(bd[i].."加",265,230+(i-1)*26)
      function 加号:初始化()
        self:创建按钮精灵(__res:取资源动画("jszy/dd",0x10000004),1)
      end
      local 减号 = 人物属性:创建按钮(bd[i].."减",290,230+(i-1)*26)
      function 减号:初始化()
        self:创建按钮精灵(__res:取资源动画("jszy/dd",0x10000003),1)
      end
      function 加号:左键弹起(x, y)
              if 人物属性.角色信息 and 人物属性.角色信息.潜力>0 then
                人物属性.临时加点[bd[i]]=人物属性.临时加点[bd[i]]+1
                人物属性.角色信息.潜力=人物属性.角色信息.潜力-1
                人物属性:属性显示()
              end
      end
      function 减号:左键弹起(x, y)
              if 人物属性.临时加点[bd[i]]>0 and 人物属性.角色信息 then
                人物属性.临时加点[bd[i]]=人物属性.临时加点[bd[i]]-1
                人物属性.角色信息.潜力=人物属性.角色信息.潜力+1
                人物属性:属性显示()
              end
      end

    
end






-----------------------------------------------------------------------------------------
local 师门列表=人物属性:创建列表("师门列表", 43,65,120,290)
function 师门列表:初始化()
          self.行高度 = 40
          self.行间距 = 1
end

function 师门列表:置数据()
        人物属性.选中技能=nil
        self:清空()
        for i, v in ipairs(角色信息.师门技能) do
          self:添加():创建纹理精灵(function ()
                  local lxxs = 取技能(v.名称)
                  __res:取资源动画(lxxs[6], lxxs[7],"图像"):显示(0,0)
                  文本字体:置颜色(0,0,0,255):取图像(v.名称):显示(45,3)
                  文本字体:置颜色(0,0,0,255):取图像(v.等级):显示(45,20)
            end)

       end


end

function 师门列表:获得鼠标(x,y,a)
    if  角色信息.师门技能[a] then
        __UI弹出.技能详情:打开(角色信息.师门技能[a],x+25,y+25)
    end
end

function 师门列表:左键弹起(x,y,a)
    人物属性.选中技能=nil
    if  角色信息.师门技能[a] then
        人物属性.选中技能=a
        人物属性.包含列表:置数据()
        if __手机 then
            __UI弹出.技能详情:打开(角色信息.师门技能[a],x+25,y+25)
        end
    end
end


local 包含列表=人物属性:创建列表("包含列表", 183,65,120,290)

function 包含列表:初始化()
          self.行高度 = 32
          self.行间距 = 4
end

function 包含列表:置数据()
        self:清空()
        人物属性.选中师门=nil
        if 人物属性.选中技能 and 角色信息.师门技能[人物属性.选中技能] then
             for i, v in ipairs(角色信息.师门技能[人物属性.选中技能].包含技能) do
                self:添加():创建纹理精灵(function ()
                        local lxxs = 取技能(v.名称)
                        if v.学会 then
                            __res:取资源动画(lxxs[6], lxxs[7],"图像"):拉伸(32,32):显示(0,0)
                        else
                              __res:取资源动画(lxxs[6], lxxs[7],"图像"):拉伸(32,32):到灰度():显示(0,0)
                        end
                        文本字体:置颜色(0,0,0,255):取图像(v.名称):显示(37,10)
                  end)

             end
        end
end
function 包含列表:获得鼠标(x,y,a)
      if 人物属性.选中技能 and 角色信息.师门技能[人物属性.选中技能] and 角色信息.师门技能[人物属性.选中技能].包含技能[a] then
           __UI弹出.技能详情:打开(角色信息.师门技能[人物属性.选中技能].包含技能[a],x+25,y+25)
      end
end



function 包含列表:左键弹起(x,y,a)
      人物属性.选中师门=nil
      if 人物属性.选中技能 and 角色信息.师门技能[人物属性.选中技能] and 角色信息.师门技能[人物属性.选中技能].包含技能[a] then
        人物属性.选中师门=a
          if __手机 then
                __UI弹出.技能详情:打开(角色信息.师门技能[人物属性.选中技能].包含技能[a],x+25,y+25,包含列表,"拿起",a)
          else
              self:拿起(a)
          end
      end

end


function 包含列表:拿起(编号)
        if 编号 and 编号~=0 then
            if 界面层.玩家界面.快捷控件.是否可见 and not 界面层.玩家界面.快捷控件.锁住开关  then
                    local lxxs = 取技能(角色信息.师门技能[人物属性.选中技能].包含技能[编号].名称)
                    鼠标层.附加 =__res:取资源动画(lxxs[6], lxxs[7],"图像"):拉伸(32,32):到精灵()
                    鼠标层.附加.技能数据={id=编号,类型=1,名称=角色信息.师门技能[人物属性.选中技能].包含技能[编号].名称}
            end
        end
end


local 特技网格 = 人物属性:创建网格("特技网格", 41, 398,210,30)
function 特技网格:初始化()
    self:创建格子(26, 26, 9, 9, 1, 6)
end
function 特技网格:置数据()
      for i, v in ipairs(self.子控件) do
            local lssj =  __技能格子:创建()
            lssj:置数据(角色信息.特殊技能[i],nil,true)
           v:置精灵(lssj)
      end

end
function 特技网格:获得鼠标(x,y,a)
  if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
      self.子控件[self.焦点]._spr.焦点=nil
  end
  self.焦点=nil
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
          self.焦点=a
          self.子控件[a]._spr.焦点=true
          __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+25,y+25)
    end
end

function 特技网格:失去鼠标(x,y)
  for i, v in ipairs(self.子控件) do
      if v._spr.焦点 then
          v._spr.焦点=nil
      end
  end
  self.焦点=nil
end


local 功德录 = 人物属性:创建红色按钮("功德录", "功德录", 115,365,60,22) --红色

function 功德录:左键弹起(x, y)
    请求服务(105)
end



local 奇经八脉 = 人物属性:创建红色按钮("奇经八脉", "奇经八脉", 185, 365,74,22) --红色

function 奇经八脉:左键弹起(x, y)
      请求服务(3746)
end

local 使用 = 人物属性:创建红色按钮("使用", "使用", 265, 365,44,22) --红色
function 使用:左键弹起(x, y)
      if 人物属性.选中师门 and 人物属性.选中技能 and 角色信息.师门技能[人物属性.选中技能] and 角色信息.师门技能[人物属性.选中技能].包含技能[人物属性.选中师门] then
            请求服务(13.1,{名称=角色信息.师门技能[人物属性.选中技能].包含技能[人物属性.选中师门].名称,类型=1})
      end
end

local 自动 = 人物属性:创建红色按钮("自动", "自动", 250, 400,54,22) --红色
-------------------------------------------------------------------------------------------------------------

local 辅助控件 =  人物属性:创建控件("辅助控件", 23, 35, 300, 405)
function 辅助控件:初始化() 
      self.状态="生活技能"
end

function 辅助控件:显示设置()
    self[self.状态]:置选中(true)
    self.选中技能=nil
    self:技能设置()
end

function 辅助控件:技能设置()
        self:创建纹理精灵(function ()
          local xx=0
          local yy=0
          for i = 1, 12 do
              if self.状态=="生活技能" then
                    if 角色信息.辅助技能[i] then
                        文本字体:置颜色(0,0,0,255):取图像(角色信息.辅助技能[i].等级):显示(36+(40-文本字体:取宽度(角色信息.辅助技能[i].等级))//2+xx*60,104+yy*85)
                    end
              elseif self.状态=="剧情技能" then
                    if 角色信息.剧情技能[i] then
                        文本字体:置颜色(0,0,0,255):取图像(角色信息.剧情技能[i].等级):显示(36+(40-文本字体:取宽度(角色信息.剧情技能[i].等级))//2+xx*60,104+yy*85)
                    end
              elseif self.状态=="其他技能" then
                  if 角色信息.强化技能[i] then
                      文本字体:置颜色(0,0,0,255):取图像(角色信息.强化技能[i].等级):显示(36+(40-文本字体:取宽度(角色信息.强化技能[i].等级))//2+xx*60,104+yy*85)
                  end
              end
              xx=xx+1
              if xx>=4 then
                  xx=0
                  yy=yy+1
              end
          end
      end)
      self.技能网格:置数据()

end

local 技能网格 = 辅助控件:创建网格("技能网格",37,51,230,220)
function 技能网格:初始化()
        self:创建格子(40,40,45,20,3,4,true)
end
function 技能网格:置数据()
        for i, v in ipairs(self.子控件) do
            local lssj =  __技能格子:创建()
            lssj:置数据(nil)
            if 辅助控件.状态=="生活技能" then
               if 角色信息.辅助技能[i] then
                    lssj:置数据(角色信息.辅助技能[i])
                    local 临时消耗 = 生活技能消耗(角色信息.辅助技能[i].等级 + 1,1)
                    lssj.数据.介绍 = lssj.数据.介绍.."\n#Y学习消耗：" .. 临时消耗.经验 .. "点经验、"  .. 临时消耗.金钱 .. "两银子"
               end
                
            elseif 辅助控件.状态=="剧情技能" then
                    if 角色信息.辅助技能[i] then
                          lssj:置数据(角色信息.剧情技能[i])
                    end
            elseif 辅助控件.状态=="其他技能" then
                  if 角色信息.强化技能[i] then
                        lssj:置数据(角色信息.强化技能[i])
                        local 临时消耗 = 生活技能消耗(角色信息.强化技能[i].等级 + 1,2)
                        lssj.数据.介绍 = lssj.数据.介绍.."\n#Y学习消耗：" .. 临时消耗.经验 .. "点经验、"  .. 临时消耗.金钱 .. "两银子"
                  end
            end
            v:置精灵(lssj)
            local 临时显示 = v
            function 临时显示:显示(x,y)
                if 辅助控件.选中技能 and 辅助控件.选中技能==i and  辅助控件.状态~="剧情技能" then
                    标题字体:置颜色(__取颜色("绿色")):取投影精灵("学习"):显示(x+5,y+20)
                end
            end

        end
end




function 技能网格:获得鼠标(x,y,a)
      if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点=nil
      end
      self.焦点=nil
      if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
              self.焦点=a
              self.子控件[a]._spr.焦点=true
              __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+25,y+25)
        end
end

function 技能网格:失去鼠标(x,y)
    for i, v in ipairs(self.子控件) do
        if v._spr.焦点 then
            v._spr.焦点=nil
        end
    end
    self.焦点=nil
end

function 技能网格:右键弹起(x,y,a)
      if 辅助控件.状态=="剧情技能" and self.子控件[a]._spr and self.子控件[a]._spr.数据  then
            请求服务(13.1,{名称=self.子控件[a]._spr.数据.名称,类型=3})
      end
end

function 技能网格:左键弹起(x,y,a)
        if self.子控件[a]._spr and self.子控件[a]._spr.数据  then
            if 辅助控件.状态~="剧情技能" then
                  辅助控件.选中技能=a
                  if __手机 then
                      __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+25,y+25)
                  end
              else
                    if __手机 then
                        __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+25,y+25,技能网格,"拿起",a)
                    else
                        self:拿起(a)
                    end
              end
        else
            辅助控件.选中技能=nil
        end
end

function 技能网格:拿起(编号)
      if 编号 and 编号~=0 then
            if 界面层.玩家界面.快捷控件.是否可见 and not 界面层.玩家界面.快捷控件.锁住开关  then
                  鼠标层.附加 =self.子控件[编号]._spr
                  鼠标层.附加.技能数据={id=编号,类型=3,名称=self.子控件[编号]._spr.数据.名称}
            end
      end
end





local 辅助配置={"生活技能","剧情技能","其他技能"}
for i, v in ipairs(辅助配置) do
        local 临时函数 = 辅助控件:创建蓝色单选按钮(v, v,20+(i-1)*90, 0,74,22)
        function 临时函数:左键弹起(x, y)
                if 辅助控件.状态~=v then
                    辅助控件.状态=v
                    辅助控件:显示设置()
                end
        end
end


local 辅助按钮={"烹饪","炼药","打造","摆摊","学习","团队"}
local xx=0
local yy=0
for i, v in ipairs(辅助按钮) do
    local 临时函数 = 辅助控件:创建红色按钮(v, v,20+xx*90,325+yy*35,74,22)
    function 临时函数:左键弹起(x, y)
              if v=="烹饪" then
                  请求服务(3713)
              elseif v=="炼药" then
                    请求服务(3714)
              elseif v=="打造" then
                    窗口层.对话栏:打开("物件_打铁炉","物件_打铁炉","你好!我能帮你什么忙吗#24？",{"打造些东西","查看熟练度","购买熟练度","查看额外加成","装备回收","打造任务","神器相关","我们后会有期"})
              elseif v=="摆摊" then
                    请求服务(3720)
              elseif v=="学习" then
                      if 辅助控件.选中技能 then
                            if 辅助控件.状态=="其他技能" then
                                  请求服务(3712.1,{序列=辅助控件.选中技能})
                            elseif 辅助控件.状态=="生活技能" then
                                  请求服务(3712,{序列=辅助控件.选中技能})
                            end
                      else
                        __UI弹出.提示框:打开("#Y请先选中要学习的生活技能")
                      end
              end
              
    end
    xx=xx+1
    if xx>=3 then
      xx=0
      yy=yy+1
    end
end




local 修炼控件 =  人物属性:创建控件("修炼控件", 23, 35, 300, 405)
function 修炼控件:初始化()
      self.人物当前= 文本字体:置颜色(255, 255, 255, 255):取精灵("攻击修炼")
      self.宠物当前= 文本字体:置颜色(255, 255, 255, 255):取精灵("攻击控制力")
end

function 修炼控件:显示(x,y)
          if self.人物当前 then
              self.人物当前:显示(x+155,y)
          end
          if self.宠物当前 then
              self.宠物当前:显示(x+170,y+185)
          end
end

function 修炼控件:显示设置()
          self.选中人物=nil
          self.选中宠物=nil
          self:修炼显示()
end

function 修炼控件:修炼显示()
        self.人物当前= 文本字体:置颜色(255, 255, 255, 255):取精灵(角色信息.修炼.当前)
        self.宠物当前= 文本字体:置颜色(255, 255, 255, 255):取精灵(角色信息.bb修炼.当前)
        self.角色列表:置数据()
        self.宠物列表:置数据()
end

local as = {"攻击修炼","防御修炼","法术修炼","抗法修炼","猎术修炼"}
local as1 = {"攻击控制力","防御控制力","法术控制力","抗法控制力"}

local 角色列表=修炼控件:创建列表("角色列表", 23, 32, 245, 140)
function 角色列表:初始化()
    self.行高度=20
    self.行间距=7
end

function 角色列表:置数据()
          self:清空()
          for i, v in ipairs(as) do
            self:添加():创建纹理精灵(function()
                  文本字体:置颜色(0,0,0,255)
                  文本字体:取图像(v):显示(0,2)
                  文本字体:取图像("经验"):显示(130,2)

                   
                  文本字体:置颜色(__取颜色("紫色"))
                  文本字体:取图像(string.format("%s/%s",角色信息.修炼[v][1],角色信息.修炼[v][3])):显示(60,2)
                  local lssj = _tp:计算修炼经验(角色信息.修炼[v][1])
                  文本字体:取图像(string.format("%s/%s",角色信息.修炼[v][2],lssj)):显示(165,2)
                end)
          end
          if 修炼控件.选中人物 then
              for i, v in ipairs(as) do
                  if 修炼控件.选中人物==v then
                      self:置选中(i)
                  end
              end
          end
end

function 角色列表:左键弹起(x,y,i)
      修炼控件.选中人物=nil
      if as[i] then
          修炼控件.选中人物=as[i]
      end
end

local 宠物列表=修炼控件:创建列表("宠物列表", 23, 220, 245, 140)
function 宠物列表:初始化()
      self.行高度=20
      self.行间距=13
end

function 宠物列表:置数据()
      self:清空()
      for i, v in ipairs(as1) do
        self:添加():创建纹理精灵(function()
              文本字体:置颜色(0,0,0,255)
              文本字体:取图像(v):显示(0,2)
              文本字体:取图像("经验"):显示(135,2)
              文本字体:置颜色(__取颜色("紫色"))
              文本字体:取图像(string.format("%s/%s",角色信息.bb修炼[v][1],角色信息.bb修炼[v][3])):显示(75,2)
              local lssj = _tp:计算修炼经验(角色信息.bb修炼[v][1])
              文本字体:取图像(string.format("%s/%s",角色信息.bb修炼[v][2],lssj)):显示(170,2)
            end)
      end
      if 修炼控件.选中宠物 then
          for i, v in ipairs(as1) do
              if 修炼控件.选中宠物==v then
                  self:置选中(i)
              end
          end
      end
end

function 宠物列表:左键弹起(x,y,i)
      修炼控件.选中宠物=nil
      if as1[i] then
          修炼控件.选中宠物=as1[i]
      end
end

local 设为当前 = 修炼控件:创建红色按钮("设为当前","设为当前",200,370,74,22)
function 设为当前:左键弹起(x, y)
      if 修炼控件.选中人物 or 修炼控件.选中宠物 then
            请求服务(14, {人物 = 修炼控件.选中人物,bb = 修炼控件.选中宠物})
      end
end





local 属性技能=人物属性:创建单选按钮("属性技能",0,38)

function 属性技能:初始化()
        self:创建按钮精灵(__res:取资源动画("dlzy",0xF6B14F50),nil,"人\n物\n属\n性")
end

function 属性技能:左键弹起(x, y)
      if 人物属性.状态~="属性" then
          人物属性.状态="属性"
          人物属性:显示设置()
      end
end
local 师门技能=人物属性:创建单选按钮("师门技能",0,107)
function 师门技能:初始化()
        self:创建按钮精灵(__res:取资源动画("dlzy",0xF6B14F50),nil,"师\n门\n技\n能")
end

function 师门技能:左键弹起(x, y)
      if 人物属性.状态~="师门" then
          人物属性.状态="师门"
          人物属性:显示设置()
      end
end

local 辅助技能=人物属性:创建单选按钮("辅助技能",0,176)
function 辅助技能:初始化()
        self:创建按钮精灵(__res:取资源动画("dlzy",0xF6B14F50),nil,"辅\n助\n技\n能")
end

function 辅助技能:左键弹起(x, y)
       if 人物属性.状态~="辅助" then
            人物属性.状态="辅助"
            人物属性:显示设置()
        end
end




local 修炼技能=人物属性:创建单选按钮("修炼技能",0,245)

function 修炼技能:初始化()
      self:创建按钮精灵(__res:取资源动画("dlzy",0xF6B14F50),nil,"修\n炼\n技\n能")
end

function 修炼技能:左键弹起(x, y)
        if 人物属性.状态~="修炼" then
            人物属性.状态="修炼"
            人物属性:显示设置()
        end
end



local 关闭 = 人物属性:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    人物属性:置可见(false)
end