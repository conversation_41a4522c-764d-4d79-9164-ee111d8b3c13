local 坐骑属性栏 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "坐骑属性栏", 80+67 + abbr.py.x, 20+42-54 + abbr.py.y, 555+29, 550-31)

function 坐骑属性栏:初始化()
  local nsf = require("SDL.图像")(555+29, 550-31)
  if nsf["渲染开始"](nsf) then
    -- __res:getPNGCC(5, 0, 0, 683, 450):显示(0,5)
    -- 字体20:置颜色(__取颜色("浅黑"))
    -- 字体20:取图像("伙 伴"):显示(683/2-20,9)
    置窗口背景("坐骑属性栏", 0, 12, 407,524-31, true):显示(0, 0)
    self.lssc = 取输入背景(0, 0, 130, 23)
    self.lssc:显示(20+79,96+8)
    self.lssc:显示(20+79,96+8+33)
    -- self.lssc:显示(20+79,96+8+33*2)
    -- self.lssc:显示(20+79,96+8+33*3+90)
    self.lssc:显示(20+79,96+8+33*4+90)
    self.lssc:显示(20+79,96+8+33*5+90)
    self.lssc:显示(20+79,96+8+33*6+90)
    字体20:置颜色(__取颜色("白色"))
    字体20:取图像("等   级"):显示(20,96+9)
    字体20:取图像("成   长"):显示(20,96+9+33)
    字体20:取图像("主属性"):显示(20,96+9+33*2)
    字体20:取图像("统驭的召唤兽"):显示(20,96+9+33*3)
    字体20:取图像("是否骑乘："):显示(20,96+9+33*3+90)
    字体20:取图像("好感度"):显示(20,96+9+33*4+90)
    字体20:取图像("饱食度"):显示(20,96+9+33*5+90)
    字体20:取图像("经   验"):显示(20,96+9+33*6+90)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 坐骑属性栏:打开(内容)
  self:置可见(true)
  self:我的按钮置文字(self.zhu请选择, __res:getPNGCC(3, 378, 347, 143, 37, true):拉伸(210, 37), "请选择：")
  self.坐骑选择:置可见(false)
  self.xuanxiang={}
  self.选中=nil
  self.图像2 =nil
  self.头像网格1["置头像"](self.头像网格1, nil)
  self.头像网格2["置头像"](self.头像网格2, nil)
  for n = 1,#角色信息.坐骑列表 do
    self.xuanxiang[n]=角色信息.坐骑列表[n].模型
  end
end
function 坐骑属性栏:shuaxin()
  if 角色信息.坐骑列表[坐骑属性栏.选中] then
    self.xuanxiang={}
    for n = 1,#角色信息.坐骑列表 do
      self.xuanxiang[n]=角色信息.坐骑列表[n].模型
    end
    self:fdkgdfild(坐骑属性栏.选中)
  end
end
function 坐骑属性栏:放生wwqq()
  self:我的按钮置文字(self.zhu请选择, __res:getPNGCC(3, 378, 347, 143, 37, true):拉伸(210, 37), "请选择：")
  self.坐骑选择:置可见(false)
  self.xuanxiang={}
  self.选中=nil
  self.图像2 =nil
  for n = 1,#角色信息.坐骑列表 do
    self.xuanxiang[n]=角色信息.坐骑列表[n].模型
  end
  __UI弹出.提示框:打开("#Y/这只坐骑永久的消失了……")
end

function 坐骑属性栏:统御(dhfdf, bb编号)
  if 角色信息.坐骑列表[坐骑属性栏.选中] and 角色信息.宝宝列表[bb编号] then
    发送数据(59,{认证码=角色信息.宝宝列表[bb编号].认证码,位置 = dhfdf,坐骑认证码 = 角色信息.坐骑列表[坐骑属性栏.选中].认证码})
  end
end
function 坐骑属性栏:删除统御召唤兽(内容)
  if 角色信息.坐骑列表[坐骑属性栏.选中] then
    for n = 1,#角色信息.坐骑列表 do
      if 内容 and 内容.认证码==角色信息.坐骑列表[坐骑属性栏.选中].认证码 then
       -- print(内容.认证码)
        角色信息.坐骑列表[n]=内容
        break
      end
    end
  end
  self:shuaxin()
end
function 坐骑属性栏:刷新经验(内容)
  if 角色信息.坐骑列表[坐骑属性栏.选中] then
    角色信息.坐骑列表[坐骑属性栏.选中]=内容
    self:fdkgdfild(坐骑属性栏.选中)
  end
end

function 坐骑属性栏:fdkgdfild(ds)
  self.图像2 =nil
  self.头像网格1["置头像"](self.头像网格1, nil)
  self.头像网格2["置头像"](self.头像网格2, nil)
  if 角色信息.坐骑列表[ds] then
    self.选中=ds
    local bb=角色信息.坐骑列表[ds]
    local nsf = require("SDL.图像")(280, 420)
    if nsf["渲染开始"](nsf) then
      字体18:置颜色(__取颜色("浅黑"))
      字体18:取图像(bb.等级):显示(20+98,97+9)
      字体18:取图像(bb.初始成长+bb.额外成长):显示(20+98,97+9+33)
      if not bb.主属性 then
        self.选择主属性:置可见(true)
      else
        self.选择主属性:置可见(false)
        self.lssc:显示(20+79,96+8+33*2)
        字体18:取图像(bb.主属性):显示(20+98,97+9+33*2)
      end
      字体18:取图像(math.floor(bb.好感度)):显示(20+98,97+9+33*4+90)
      字体18:取图像(100):显示(20+98,97+9+33*5+90)
      -- 字体18:取图像("经   验"):显示(20+98,97+9+33*6+90)
      if bb.统御召唤兽~=nil  then
		    if bb.统御召唤兽[1]~=nil then
          local txmx = {}
					for i=1,#角色信息.宝宝列表 do
						if 角色信息.宝宝列表[i].认证码 == bb.统御召唤兽[1]  then
							txmx.模型 = 角色信息.宝宝列表[i].模型
              break
						end
					end
          self.头像网格1["置头像"](self.头像网格1, txmx)
        end
        if bb.统御召唤兽[2]~=nil then
          local txmx = {}
					for i=1,#角色信息.宝宝列表 do
						if 角色信息.宝宝列表[i].认证码 == bb.统御召唤兽[2]  then
							txmx.模型 = 角色信息.宝宝列表[i].模型
              break
						end
					end
          self.头像网格2["置头像"](self.头像网格2, txmx)
        end
      end
      if bb.是否骑乘 then
        字体18:置颜色(__取颜色("紫色"))
        字体18:取图像(bb.是否骑乘):显示(20+98,97+9+33*3+90)
      else
        字体18:置颜色(__取颜色("白色"))
        字体18:取图像("否"):显示(20+98,97+9+33*3+90)
      end
      字体18:置颜色(__取颜色("绿色"))
      字体18:取图像(string.format("%s/%s", bb.当前经验, bb.最大经验)):置混合(0):显示(20+98,97+9+33*6+90)
     
      nsf["渲染结束"](nsf)
      
      if 角色信息.坐骑~=nil and (角色信息.坐骑.认证码==角色信息.坐骑列表[ds].认证码 or (角色信息.坐骑~=nil and 角色信息.坐骑[1]~=nil and 角色信息.坐骑列表[ds].认证码==角色信息.坐骑[1].认证码)) then
        self:我的按钮置文字(self.骑乘按钮, __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(117, 43), "下 骑")
      else
        self:我的按钮置文字(self.骑乘按钮, __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(117, 43), "骑 乘")
      end
    end
    self.图像2 = nsf["到精灵"](nsf)
  end
end


for i, v in ipairs({
  {
    name = "骑乘按钮",
    x = 20,
    y = 436,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    font = "骑 乘"
  },
  {
    name = "放生",
    x = 20+125,
    y = 436,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    font = "放 生"
  },
  {
    name = "查看技能",
    x = 20+125*2,
    y = 436,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true),
    font = "查看技能"
  },
  {
    name = "骑乘快捷键",
    x = 20+233,
    y = 111-50-5,
    tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(127, 35),
    font = "骑乘快捷键"
  },
  {
    name = "修炼",
    x = 20+239,
    y = 436+39-136,
    tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(117, 35),
    font = "修 炼"
  },
  {
    name = "驯养",
    x = 20+239,
    y = 436+39-136+44,
    tcp = __res:getPNGCC(1, 449, 28, 174, 35, true):拉伸(117, 35),
    font = "驯 养"
  },
  {
    name = "选择主属性",
    x = 20+20+83,
    y = 96+9+33*2-5,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(85, 35),
    font = "请选择"
  },
}) do 

  local 临时函数 = 坐骑属性栏["创建我的按钮"](坐骑属性栏, v.tcp, v.name, v.x, v.y, v.font)
  function  临时函数:左键弹起(x, y)
    if 角色信息.坐骑列表[坐骑属性栏.选中] then
      if v.name == "骑乘按钮" then
        if 坐骑属性栏["选中"] then
          if 角色信息["坐骑"] and 角色信息["坐骑列表"] and 角色信息["坐骑"]["认证码"] == 角色信息["坐骑列表"][坐骑属性栏["选中"]]["认证码"] then
            发送数据(27, {
              ["序列"] = 0
            })
            坐骑属性栏:我的按钮置文字(坐骑属性栏.骑乘按钮, __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(117, 43), "骑 乘")
          else
            发送数据(26, {
              ["序列"] = 坐骑属性栏["选中"]
            })
            坐骑属性栏:我的按钮置文字(坐骑属性栏.骑乘按钮, __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(117, 43), "下 骑")
          end
        end
      elseif v.name == "骑乘快捷键" then
        __UI界面.窗口层.对话栏:打开("","","请设置你要飞行的快捷键",{"F1","F2","F3","F4","","F5","F6","F7","F8"})
      elseif v.name == "放生" then
        if 角色信息.坐骑列表[坐骑属性栏.选中] then
          local 事件 = function()
            发送数据(11.3,{角色信息.坐骑列表[坐骑属性栏.选中].认证码})
          end
          __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, "真的要放生#Y/"..角色信息.坐骑列表[坐骑属性栏.选中].等级.."级的坐骑#R/"..角色信息.坐骑列表[坐骑属性栏.选中].名称.."#W/吗?", 285, 155, 390, 200, 事件)
        end
      elseif v.name == "选择主属性" then
        if 角色信息.坐骑列表[坐骑属性栏.选中] and not 角色信息.坐骑列表[坐骑属性栏.选中].主属性 then
          __UI弹出["坐骑主属性弹出"]:打开(角色信息.坐骑列表[坐骑属性栏.选中].认证码)
        end
      elseif v.name == "驯养" then
        local 事件 = function()
          发送数据(61,{角色信息.坐骑列表[坐骑属性栏.选中].认证码})
        end
        __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, "#Y当前驯养需：#G"..math.floor(角色信息.坐骑列表[坐骑属性栏.选中].最大经验/2).." #Y点人物经验。", 285, 155, 390, 200, 事件)
      elseif v.name == "查看技能" then
        __UI界面["窗口层"].坐骑技能栏:打开(角色信息.坐骑列表[坐骑属性栏.选中])
      end
    end
  end
end



local zhu请选择 = 坐骑属性栏["创建我的按钮"](坐骑属性栏, __res:getPNGCC(3, 378, 347, 143, 37, true):拉伸(210, 37), "zhu请选择", 93-77, 111-50-5, "")
function zhu请选择:左键弹起(x, y, msg)
  if 坐骑属性栏.坐骑选择.是否可见 then
    坐骑属性栏.坐骑选择:置可见(false)
  else
    坐骑属性栏.坐骑选择:置可见(true)
    坐骑属性栏.坐骑选择.选项网格:置选项()
  end
end

local 关闭 = 坐骑属性栏["创建我的按钮"](坐骑属性栏, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 500+29-167, 0)
function 关闭:左键弹起(x, y, msg)
  坐骑属性栏["置可见"](坐骑属性栏, false)
end






local 头像网格1 = 坐骑属性栏["创建网格"](坐骑属性栏, "头像网格1", 20, 230+13-8, 50, 50)
function 头像网格1:初始化()
  self:创建格子(50, 50, 0, 0, 1, 1)
end
function 头像网格1:左键弹起(x, y, a, b, msg)
  if 角色信息.坐骑列表[坐骑属性栏.选中] then 
    if 角色信息.坐骑列表[坐骑属性栏.选中].统御召唤兽[1] then
      发送数据(59.1,{坐骑认证码=角色信息.坐骑列表[坐骑属性栏.选中].认证码,编号=1})
    else
      __UI弹出["召唤兽选择"]["打开"](__UI弹出["召唤兽选择"], 151+150, 133, "坐骑统御1")
    end
  end
end
function 头像网格1:置头像(数据)
  --print(数据,1)
  local lssj = __头像选择格子["创建"]()
  lssj["置头像"](lssj, 数据)
  头像网格1["子控件"][1]["置精灵"](头像网格1["子控件"][1], lssj)
end

local 头像网格2 = 坐骑属性栏["创建网格"](坐骑属性栏, "头像网格2", 20+75, 230+13-8, 50, 50)
function 头像网格2:初始化()
  self:创建格子(50, 50, 0, 0, 1, 1)
end
function 头像网格2:左键弹起(x, y, a, b, msg)
  if 角色信息.坐骑列表[坐骑属性栏.选中] then 
    if 角色信息.坐骑列表[坐骑属性栏.选中].统御召唤兽[2] then
      发送数据(59.1,{坐骑认证码=角色信息.坐骑列表[坐骑属性栏.选中].认证码,编号=2})
    else
      __UI弹出["召唤兽选择"]["打开"](__UI弹出["召唤兽选择"], 251+150, 133, "坐骑统御2")
    end
  end
end
function 头像网格2:置头像(数据)
 -- print(数据,2)
  local lssj = __头像选择格子["创建"]()
  lssj["置头像"](lssj, 数据)
  头像网格2["子控件"][1]["置精灵"](头像网格2["子控件"][1], lssj)
end





local 坐骑选择 = 坐骑属性栏["创建控件"](坐骑属性栏, "坐骑选择", 52+130, 91, 535, 350)
function 坐骑选择:初始化()
  self:置精灵(取黑色背景(0, 0, 162, 331))
end
local 选项网格 = 坐骑选择["创建网格"](坐骑选择, "选项网格", 0+30-10, 20-7, 227, 280)
function 选项网格:置选项()
  self:创建格子(227, 70, 0, 0, #坐骑属性栏["xuanxiang"], 1,true) --GUI网格:创建格子(宽度, 高度, 行间距, 列间距, 行数量, 列数量,auto)
  for i, v in ipairs(self.子控件) do
    if 坐骑属性栏["xuanxiang"][i]  then
      local 按钮 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(2, 368, 906, 126, 52, true):拉伸(126, 52), "按1钮" .. i, 0, 10,坐骑属性栏["xuanxiang"][i]) 
      function  按钮:左键单击(x, y, msg)
        --坐骑属性栏:对话事件处理(坐骑属性栏["xuanxiang"][i])
        local  wenz=坐骑属性栏["xuanxiang"][i]
        if 角色信息.坐骑列表[i] and 角色信息.坐骑列表[i].模型==坐骑属性栏["xuanxiang"][i] and 角色信息.坐骑列表[i].主属性 then
          wenz=wenz.."-"..角色信息.坐骑列表[i].主属性
        end
        坐骑属性栏.选中=i
        坐骑属性栏.zhu请选择:我的按钮置文字(坐骑属性栏.zhu请选择, __res:getPNGCC(3, 378, 347, 143, 37, true):拉伸(210, 37), wenz)
        坐骑属性栏.坐骑选择:置可见(false)
        坐骑属性栏:fdkgdfild(i)
      end
      v:置可见(true, true)
    end
  end
end