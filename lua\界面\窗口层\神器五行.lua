local 神器五行 = 窗口层:创建窗口("神器五行",0,0,650,480)
local 属性字段={速度="速　　度", 气血="气　　血",伤害="伤　　害",防御="防    御",封印命中="封印命中",法术伤害="法术伤害",固定伤害="固定伤害",物理暴击="物理暴击",治疗能力="治疗能力",法术暴击="法术暴击",法术防御="法术防御",抵抗封印="抵抗封印"}
local 神器属性 = {
          大唐官府 = {"伤　　害","物理暴击"},
          化生寺 = {"防　　御","治疗能力"},
          方寸山 = {"封印命中","法术伤害"},
          女儿村 = {"封印命中","固定伤害"},
          天宫 = {"法术伤害","封印命中"},
          普陀山 = {"固定伤害","治疗能力"},
          龙宫 = {"法术伤害","法术暴击"},
          五庄观 = {"伤　　害","封印命中"},
          魔王寨 = {"法术伤害","法术暴击"},
          狮驼岭 = {"伤　　害","物理暴击"},
          盘丝洞 = {"封印命中","法术防御"},
          阴曹地府 = {"伤　　害","法术防御"},
          神木林 = {"法术伤害","法术暴击"},
          凌波城 = {"伤　　害","物理暴击"},
          无底洞 = {"封印命中","治疗能力"},
          花果山 = {"伤　　害","物理暴击"},
          九黎城 = {"伤　　害","物理暴击"}
        }


    local 神器图 = { 
          天宫   = {0x861333DE,0xD3F019A9,0xD90C5658},
          龙宫   = {0xC163A3CB,0xC5A94CBC,0x64893E31},
          化生寺 = {0xBCD2FD04,0x94748447,0x23906569},
          方寸山 = {0xF4468EB3,0x7ADF0AD5,0x6AD0DD5B},
          女儿村 = {0x2FE3D5CB,0xAE659408,0x478C415A},
          普陀山 = {0xACD7AC8C,0xA26045E7,0xAF7234E1},
          五庄观 = {0x7C4D3F9D,0x0A7537A9,0xF9CE015C},
          魔王寨 = {0x01AC0038,0x01AC0039,0x01AC0040},
          狮驼岭 = {0x898E3905,0xC621DCE5,0x768B550B},
          盘丝洞 = {0x767F7971,0x72527A8B,0x9F45CA15},
          神木林 = {0x01AC0038,0x96D9A247,0x1934A199},
          凌波城 = {0xC068EA63,0x8794D94E,0xC0AA056C},
          无底洞 = {0x2034FC4A,0x0CBEA6CD,0xFFC9DADB},
          花果山 = {0x861333DE,0xD3F019A9,0xD90C5658},
          大唐官府 = {0x467F2FCB,0x732620A1,0xAF99EF70},
          阴曹地府 = {0xE4E601D2,0x04847348,0x8F90B0F7},
          九黎城 = {0xE4E601D2,0x04847348,0x8F90B0F7},
          }


local 五行图 = {
  金 = 0x01AC0010,木 = 0x01AC0011,水 = 0x01AC0012,火 = 0x01AC0013,土 = 0x01AC0014
}


function 神器五行:初始化()
          self:创建纹理精灵(function ()
            __res:取资源动画("pic/sqsc","sqbj.png","图片"):显示(0,0)
            __res:取资源动画("pic/sqsc","swbj.png","图片"):显示(225,40)
            __res:取资源动画("pic/sqsc","tbwzbj.png","图片"):显示(190,0)
            __res:取资源动画("jszy/xjjm",0X258AF3A3,"图像"):显示(290,0)
            __res:取资源动画("pic/sqsc","sqzbbj.png","图片"):显示(20,80)
          end)
          self.背景动画=__res:取资源动画("jszy/xjjm",0xC057E026,"动画") 
          self.保存动画=__res:取资源动画("jszy/xjjm",0xBCA98FEB,"动画")
          self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
          self.可初始化=true
end


function 神器五行:更新(dt)
    self.背景动画:更新(dt)
    if self.是否保存 then
          if not self.记录时间 then
              self.保存动画:更新(dt)
          elseif self.记录时间 then
                  if self.当前坐标[1]>=200 then
                      self.当前坐标[1]=self.当前坐标[1]-2
                  end
                  if self.保存坐标[1]<465 then
                      self.保存坐标[1]=self.保存坐标[1]+2
                  end
                  if self.当前坐标[1]<=200 and self.保存坐标[1]>=465 then
                      self.记录时间=nil
                      self.放弃本次:置可见(true)
                      self.保存结果:置可见(true)
                      self.更换五行:置可见(false)
                  end
          end
    end
end
function 神器五行:显示(x,y)
          self.背景动画:显示(x-90,y-70)
          if self.图像 then
              self.图像:显示(x,y)
          end
          if self.是否保存 and not self.记录时间  then
                  self.保存动画:显示(x+380,y+225)
          end
          if self.保存五行 then
              self.保存五行:显示(x+self.保存坐标[1],y+self.保存坐标[2])
          end
          if self.当前五行 then
              self.当前五行:显示(x+self.当前坐标[1],y+self.当前坐标[2])
          end

end



function 神器五行:打开(数据)
 
    if not 角色信息 or not 角色信息.门派 or 角色信息.门派=="无" or 角色信息.门派=="无门派" or not 数据  or not 数据.神器技能  then return end
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  
    
    self.图像=self:创建纹理精灵(function ()

        __res:取资源动画("jszy/xjjm",0X42DE086A,"图像"):显示(45,205)
        __res:取资源动画("jszy/xjjm",0XC840D36F,"图像"):显示(77,240)
       
        文本字体:置颜色(255,255,255)
        文本字体:取图像("金：速　　度"):显示(78,85)
        文本字体:取图像("木：气　　血"):显示(78,110)
        文本字体:取图像("水："..神器属性[角色信息.门派][1]):显示(78,135)
        文本字体:取图像("火："..神器属性[角色信息.门派][2]):显示(78,160)
        文本字体:取图像("土：抵抗封印"):显示(78,185)
        文本字体:取图像("体力消耗"):显示(60,273)
        文本字体:取图像("金钱消耗"):显示(60,303)
        __res:取资源动画("jszy/xjjm",0X01AC0008,"图像"):显示(123,270)
        __res:取资源动画("jszy/xjjm",0X01AC0008,"图像"):显示(123,300)
        文本字体:置颜色(0,0,0):取图像("50"):显示(130,274)
        文本字体:置颜色(__取颜色("绿色")):取图像("800000"):显示(130,304)
    end,1,200,360)
    self.状态=1
    self:刷新(数据)
end






function 神器五行:设置保存(数据)
        self.是否保存=true
        self.记录时间=true
        self.保存数据=数据
        self.保存五行=self:创建纹理精灵(function ()
              __res:取资源动画("jszy/xjjm",0xACEB1F9B,"图像"):显示(0,0)
              __res:取资源动画("jszy/xjjm",0xA0FB07B8,"图像"):显示(81,97)
              __res:取资源动画("jszy/xjjm",0x01AC0007,"图像"):显示(84,27)
              __res:取资源动画("jszy/xjjm",五行图[数据[1]],"图像"):显示(91,33)
              __res:取资源动画("jszy/xjjm",0x01AC0007,"图像"):显示(148,94)
              __res:取资源动画("jszy/xjjm",五行图[数据[2]],"图像"):显示(155,100)
              __res:取资源动画("jszy/xjjm",0x01AC0007,"图像"):显示(84,158)
              __res:取资源动画("jszy/xjjm",五行图[数据[3]],"图像"):显示(91,164)
              __res:取资源动画("jszy/xjjm",0x01AC0007,"图像"):显示(20,94)
              __res:取资源动画("jszy/xjjm",五行图[数据[4]],"图像"):显示(27,100)
        
        end,1,190,190)
end
function 神器五行:刷新(数据)
        self.神器属性 = {}

        for n=1,3 do
            if 数据.神器解锁[n]~=nil then
                self.神器属性[n]=数据.神器解锁[n]
            end
        end
        self.是否保存=false
        self.保存五行=nil
        self.保存数据=nil
        self.原始坐标={315,145}
        self:显示设置()
end



function 神器五行:显示设置()
         
          self.放弃本次:置可见(false)
          self.保存结果:置可见(false)
          
          if self.是否保存 then
              self.当前坐标={200,145}
              self.保存坐标={465,145}
              self.放弃本次:置可见(true)
              self.保存结果:置可见(true)
              self.更换五行:置可见(false)
          else
              self.当前坐标=table.copy(self.原始坐标)
              self.保存坐标=table.copy(self.原始坐标)
              self.更换五行:置可见(true)
              self.放弃本次:置可见(false)
              self.保存结果:置可见(false)
          end
          for i = 1, 3 do
              if self.状态 == i then
                  self["部位按钮"..i]:创建按钮精灵(__res:取资源动画("jszy/xjjm",神器图[角色信息.门派][i]):取图像(5))
                  self.按钮光环= __res:取资源动画("pic","gq.png","图片"):拉伸(self["部位按钮"..i].宽度,self["部位按钮"..i].高度):到精灵()
                  self["部位按钮"..i]:置选中(true)
              else
                  self["部位按钮"..i]:创建按钮精灵(__res:取资源动画("jszy/xjjm",神器图[角色信息.门派][i]))
                  self["部位按钮"..i]:置选中(false)
              end
          end
          if self.状态 ==1 then
              self.部位按钮1:置坐标(370,50)
              self.部位按钮2:置坐标(448,40)
              self.部位按钮3:置坐标(293,40)
          elseif self.状态 ==2 then
                self.部位按钮1:置坐标(293,40)
                self.部位按钮2:置坐标(370,50)
                self.部位按钮3:置坐标(448,40)
          else
              self.部位按钮1:置坐标(448,40)
              self.部位按钮2:置坐标(293,40)
              self.部位按钮3:置坐标(370,50)
          end 
          self.当前五行=self:创建纹理精灵(function ()
                __res:取资源动画("jszy/xjjm",0xE4C15C10,"图像"):显示(0,0)
                __res:取资源动画("jszy/xjjm",0xF3A1A9B3,"图像"):显示(81,97)
                __res:取资源动画("jszy/xjjm",0x01AC0007,"图像"):显示(84,27)
                __res:取资源动画("jszy/xjjm",五行图[self.神器属性[self.状态].神器五行[1]],"图像"):显示(91,33)
                __res:取资源动画("jszy/xjjm",0x01AC0007,"图像"):显示(148,94)
                __res:取资源动画("jszy/xjjm",五行图[self.神器属性[self.状态].神器五行[2]],"图像"):显示(155,100)
                __res:取资源动画("jszy/xjjm",0x01AC0007,"图像"):显示(84,158)
                __res:取资源动画("jszy/xjjm",五行图[self.神器属性[self.状态].神器五行[3]],"图像"):显示(91,164)
                __res:取资源动画("jszy/xjjm",0x01AC0007,"图像"):显示(20,94)
                __res:取资源动画("jszy/xjjm",五行图[self.神器属性[self.状态].神器五行[4]],"图像"):显示(27,100)
          
          end,1,190,190)
end


for i=1,3 do
  local 临时按钮=神器五行:创建单选按钮("部位按钮"..i)
  function 临时按钮:左键弹起(x, y)
            if 神器五行.状态~=i and 神器五行.神器属性[i] then
                神器五行.状态=i
                神器五行:显示设置()
            end
  end
  function 临时按钮:显示(x, y)
        if 神器五行.状态==i then
          神器五行.按钮光环:显示(x, y)
        end
  end
end
local 更换五行 = 神器五行:创建蓝色按钮( "更换五行", "更换五行", 360,360,110,40,说明字体) 

function 更换五行:左键弹起(x, y)
        请求服务(6225)
end
local 放弃本次 = 神器五行:创建蓝色按钮( "放弃本次", "放弃本次", 270,360,110,40,说明字体) 

function 放弃本次:左键弹起(x, y)
        神器五行.是否保存=false
        神器五行.保存数据=nil
        神器五行:显示设置()

end

local 保存结果 = 神器五行:创建蓝色按钮( "保存结果", "保存结果", 470,360,110,40,说明字体) 

function 保存结果:左键弹起(x, y)
    if 神器五行.是否保存 and 神器五行.保存数据 then
        请求服务(6226,{神器部件=神器五行.状态,五行=神器五行.保存数据})
    end
end
local 关闭 = 神器五行:创建按钮( "关闭", 605,2) 
function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x20FD5715),1)
end
function 关闭:左键弹起(x, y)
  神器五行:置可见(false)
end







  


















