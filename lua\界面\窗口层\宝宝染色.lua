--[[
LastEditTime: 2024-10-06 01:37:40
--]]
local 宝宝染色 = 窗口层:创建窗口("宝宝染色", 0,0, 510, 280)
function 宝宝染色:初始化()
              self:创建纹理精灵(function()
                      置窗口背景("宝宝染色", 0, 0, 510, 280, true):显示(0, 0)
                      取白色背景(0, 0, 230, 240, true):显示(10, 30)
                      __res:取资源动画("pic", "wqrs.png","图片"):拉伸(230,240):显示(10,30)
                      __res:取资源动画("pic", "rsbeij.png","图片"):显示(255,70)
                      __res:取资源动画("pic", "rstiao.png","图片"):显示(310,130)
                      __res:取资源动画("pic", "rsxbj.png","图片"):显示(335,33)
                      取输入背景(0, 0, 120, 23):显示(330,175)
          
                      文本字体:置颜色(255,255,255,255)
                      文本字体:取图像("选中颜色为:"):显示(250, 35)
                      文本字体:取图像("五彩织"):显示(250, 130)
                      文本字体:取图像("所需彩果"):显示(265, 180)
                      排行标题:置颜色(0,0,0,255)
                      排行标题:取投影图像("20"):显示(335, 180)
                    end
              )
      
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        if __手机 then
            self.关闭:置大小(25,25)
            self.关闭:置坐标(self.宽度-27, 2)
        else
            self.关闭:置大小(16,16)
            self.关闭:置坐标(self.宽度-18, 2)
        end
end
function 宝宝染色:打开(宝宝,编号)
        self:置可见(not self.是否可见)
        if not self.是否可见 then
            return
        end
        self.宝宝=table.copy(宝宝)
        self.编号 = 编号
        self.染色id = 0
        self.方向 = 1
        self:置模型()
       
        
	    --方向 = 4

end 

function 宝宝染色:更新(dt)
        if self.宝宝模型 then
            self.宝宝模型:更新(dt)
        end
        if self.饰品显示 then
          self.饰品显示:更新(dt)
      end


end
function 宝宝染色:显示(x,y)
        if self.染色显示 then
             self.染色显示:显示(x+335+(35-self.染色显示.宽度)//2,y+35)
        end
        if self.宝宝模型 then
            self.宝宝模型:显示(x+125,y+210)
        end
        if self.饰品显示 then
            self.饰品显示:显示(x+125,y+210)
        end

end


function 宝宝染色:置模型()

        if self.染色id ==0 then
            self.染色显示=文本字体:置颜色(255,255,255,255):取精灵(0)
            self.染色方案=0
            self.染色组={}
            self.滑块:置位置(0)
        else
            self.染色显示=文本字体:置颜色(255,255,255,255):取精灵(self.染色id)
            self.染色方案=__染色方案[self.染色id].id
            self.染色组={}
            self.染色组[1]=__染色方案[self.染色id].方案[1] 
            self.染色组[2]=__染色方案[self.染色id].方案[2] or 0
        end
        local 资源 = 取战斗模型(self.宝宝.模型)
        self.宝宝模型 = __res:取资源动画(资源[10],资源[6],"置动画"):置循环(true)
        if self.染色方案~=nil and self.染色方案~=0 and self.染色组~=nil and self.染色组~=0 and #self.染色组>0 then
                local 调色板  = __dewpal(self.染色方案)
                self.宝宝模型:调色(调色板,取调色数据(self.染色组))
        end
        if self.宝宝.饰品  then
            资源 = 取战斗模型(self.宝宝.模型 .. "_饰品")
            self.饰品显示 = __res:取资源动画(资源[10],资源[6],"置动画"):置循环(true)
        else 
            self.饰品显示 =nil
        end
        self:置方向(self.方向)
end






function 宝宝染色:置方向(v)
        if self.宝宝模型 then
            self.宝宝模型:置方向(v)
        end
        if self.饰品显示 then
            self.饰品显示:置方向(v)
        end

        
end





local 滑块=宝宝染色:创建滑块("滑块",258,66,235,37)
滑块.最大值=80

function 滑块:滚动事件(x,y,a)
  宝宝染色.染色id=a
  宝宝染色:置模型()
end

local 按钮= 滑块:创建滑块按钮("按钮",0,0)
function 按钮:初始化()
        self:创建按钮精灵(__res:取资源动画('jszy/dd',00000007),nil,nil,10,37)
end


local 方向按钮 = 宝宝染色:创建按钮("方向按钮",110, 240)
function 方向按钮:初始化()
      self:创建按钮精灵(__res:取资源动画("jszy/ui",0x00000072),1)

end

function 方向按钮:左键弹起()
  宝宝染色.方向 = 宝宝染色.方向 - 1
	if 宝宝染色.方向<1 then
    宝宝染色.方向=4
	end
	宝宝染色:置方向(宝宝染色.方向)
end

local 还原=宝宝染色:创建红色按钮("还原", "还原按钮", 300, 240,50,22)
function 还原:左键弹起()
  宝宝染色.染色id = 0
  宝宝染色:置模型()
end

local 染色=宝宝染色:创建红色按钮("染色", "染色按钮", 400, 240,50,22)
function 染色:左键弹起()
      if 宝宝染色.染色id ==0 then
          请求服务(5011.1,{序列=宝宝染色.宝宝.认证码})
      else
          角色信息.宝宝列表[宝宝染色.编号].染色方案= 宝宝染色.染色方案
          角色信息.宝宝列表[宝宝染色.编号].染色组={}
          角色信息.宝宝列表[宝宝染色.编号].染色组[1]=宝宝染色.染色组[1]
          角色信息.宝宝列表[宝宝染色.编号].染色组[2]=宝宝染色.染色组[2]
          
          请求服务(5011,{序列=宝宝染色.宝宝.认证码,
          序列1=宝宝染色.染色方案,
          序列2=宝宝染色.染色组[1],
          序列3=宝宝染色.染色组[2]
        })
      end
end





local 关闭 = 宝宝染色:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
      宝宝染色:置可见(false)
end