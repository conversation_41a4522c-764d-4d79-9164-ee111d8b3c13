local 子女查看 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "子女查看", 210 + abbr.py.x, 53 + abbr.py.y, 550, 424)
function 子女查看:初始化()
  local nsf = require("SDL.图像")(550, 424)
  if nsf["渲染开始"](nsf) then
    置窗口背景("我的孩子", 0, 12, 540, 412, true)["显示"](置窗口背景("我的孩子", 0, 12, 540, 412, true), 0, 0)
    取白色背景(0, 0, 270, 159, true)["显示"](取白色背景(0, 0, 270, 159, true), 17, 56)
    local lssj = 取输入背景(0, 0, 78, 23)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "气血")["显示"](字体18["取图像"](字体18, "气血"), 20, 225)
    lssj["显示"](lssj, 66, 223)
    字体18["取图像"](字体18, "魔法")["显示"](字体18["取图像"](字体18, "魔法"), 20, 252)
    lssj["显示"](lssj, 66, 250)
    字体18["取图像"](字体18, "攻击")["显示"](字体18["取图像"](字体18, "攻击"), 20, 279)
    lssj["显示"](lssj, 66, 277)
    字体18["取图像"](字体18, "防御")["显示"](字体18["取图像"](字体18, "防御"), 20, 306)
    lssj["显示"](lssj, 66, 304)
    字体18["取图像"](字体18, "速度")["显示"](字体18["取图像"](字体18, "速度"), 20, 333)
    lssj["显示"](lssj, 66, 331)
    字体18["取图像"](字体18, "法伤")["显示"](字体18["取图像"](字体18, "法伤"), 20, 360)
    lssj["显示"](lssj, 66, 358)
    字体18["取图像"](字体18, "法防")["显示"](字体18["取图像"](字体18, "法防"), 20, 387)
    lssj["显示"](lssj, 66, 385)
    字体18["取图像"](字体18, "根骨")["显示"](字体18["取图像"](字体18, "根骨"), 163, 225)
    lssj["显示"](lssj, 208, 223)
    字体18["取图像"](字体18, "智力")["显示"](字体18["取图像"](字体18, "智力"), 163, 252)
    lssj["显示"](lssj, 208, 250)
    字体18["取图像"](字体18, "武力")["显示"](字体18["取图像"](字体18, "武力"), 163, 279)
    lssj["显示"](lssj, 208, 277)
    字体18["取图像"](字体18, "定力")["显示"](字体18["取图像"](字体18, "定力"), 163, 306)
    lssj["显示"](lssj, 208, 304)
    字体18["取图像"](字体18, "念力")["显示"](字体18["取图像"](字体18, "念力"), 163, 333)
    lssj["显示"](lssj, 208, 331)
    字体18["取图像"](字体18, "灵敏")["显示"](字体18["取图像"](字体18, "灵敏"), 163, 360)
    lssj["显示"](lssj, 208, 358)
    字体18["取图像"](字体18, "亲密")["显示"](字体18["取图像"](字体18, "亲密"), 163, 387)
    lssj["显示"](lssj, 208, 385)
    lssj = 取输入背景(0, 0, 130, 23)
    字体18["取图像"](字体18, "名称")["显示"](字体18["取图像"](字体18, "名称"), 301, 64)
    lssj["显示"](lssj, 350, 62)
    字体18["取图像"](字体18, "等级")["显示"](字体18["取图像"](字体18, "等级"), 301, 91)
    lssj["显示"](lssj, 350, 89)
    字体18["取图像"](字体18, "成长")["显示"](字体18["取图像"](字体18, "成长"), 301, 118)
    lssj["显示"](lssj, 350, 116)
    字体18["取图像"](字体18, "门派")["显示"](字体18["取图像"](字体18, "门派"), 301, 145)
    lssj["显示"](lssj, 350, 143)
    字体18["取图像"](字体18, "五行")["显示"](字体18["取图像"](字体18, "五行"), 301, 172)
    lssj["显示"](lssj, 350, 170)
    字体16["取描边图像"](字体16, "结局：", 157, 160, 169)["显示"](字体16["取描边图像"](字体16, "结局：", 157, 160, 169), 301, 199)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.模型格子 = __UI模型格子["创建"]()
end
function 子女查看:打开(data)
  self:置可见(true)
  self:刷新(data)
end
function 子女查看:刷新(data)
 -- table.print(data)
  local nsf = require("SDL.图像")(550, 424)
if  data["门派"] == nil then
  data["门派"] = "无门派"
end
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, data["气血"])["显示"](字体18["取图像"](字体18, data["气血"]), 75, 225)
    字体18["取图像"](字体18, data["魔法"])["显示"](字体18["取图像"](字体18, data["魔法"]), 75, 252)
    字体18["取图像"](字体18, data["伤害"])["显示"](字体18["取图像"](字体18, data["伤害"]), 75, 279)
    字体18["取图像"](字体18, data["防御"])["显示"](字体18["取图像"](字体18, data["防御"]), 75, 306)
    字体18["取图像"](字体18, data["速度"])["显示"](字体18["取图像"](字体18, data["速度"]), 75, 333)
    字体18["取图像"](字体18, data["灵力"])["显示"](字体18["取图像"](字体18, data["灵力"]), 75, 360)
    字体18["取图像"](字体18, data["灵力"])["显示"](字体18["取图像"](字体18, data["灵力"]), 75, 387)
    字体18["取图像"](字体18, data.培养["根骨"])["显示"](字体18["取图像"](字体18, data.培养["根骨"]), 217, 225)
    字体18["取图像"](字体18, data.培养["智力"])["显示"](字体18["取图像"](字体18, data.培养["智力"]), 217, 252)
    字体18["取图像"](字体18, data.培养["武力"])["显示"](字体18["取图像"](字体18, data.培养["武力"]), 217, 279)
    字体18["取图像"](字体18, data.培养["定力"])["显示"](字体18["取图像"](字体18, data.培养["定力"]), 217, 306)
    字体18["取图像"](字体18, data.培养["定力"])["显示"](字体18["取图像"](字体18, data.培养["定力"]), 217, 333)
    字体18["取图像"](字体18, data.培养["灵敏"])["显示"](字体18["取图像"](字体18, data.培养["灵敏"]), 217, 360)
    字体18["取图像"](字体18, data["忠诚"])["显示"](字体18["取图像"](字体18, data["忠诚"]), 217, 387)
    字体18["取图像"](字体18, data["名称"])["显示"](字体18["取图像"](字体18, data["名称"]), 358, 64)
    字体18["取图像"](字体18, data["等级"])["显示"](字体18["取图像"](字体18, data["等级"]), 358, 91)
    字体18["取图像"](字体18, data["成长"])["显示"](字体18["取图像"](字体18, data["成长"]), 358, 118)
    字体18["取图像"](字体18, data["门派"])["显示"](字体18["取图像"](字体18, data["门派"]), 358, 145)
    字体18["取图像"](字体18, data["五行"])["显示"](字体18["取图像"](字体18, data["五行"]), 358, 172)
    字体16["置颜色"](字体16, __取颜色("白色"))
    if data["结果"] and data["结果"] ~= "" then
      字体16["取描边图像"](字体16, data["结果"], 98, 183, 222)["显示"](字体16["取描边图像"](字体16, data["结果"], 98, 183, 222), 353, 199)
    end
  end
  self.图像 = nsf["到精灵"](nsf)
  self.模型格子["置数据"](self.模型格子, data, "子女", 147, 169)
  self.技能网格["置数据"](self.技能网格, data["技能"])
end
local 关闭 = 子女查看["创建我的按钮"](子女查看, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 500, 0)
function 关闭:左键弹起(x, y, msg)
  子女查看["置可见"](子女查看, false)
end
local 技能网格 = 子女查看["创建网格"](子女查看, "技能网格", 297, 223, 245, 185)
function 技能网格:初始化()
  self:创建格子(55, 55, 3, 3, 3, 4, true)
end
function 技能网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["数据"] then
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, x, y)
  end
end
function 技能网格:置数据(数据)
  for i = 1, #技能网格["子控件"] do
    local lssj = __召唤兽技能格子["创建"]()
    lssj["置数据"](lssj, 数据[i], 55, 55, "子女")
    技能网格["子控件"][i]["置精灵"](技能网格["子控件"][i], lssj)
  end
end
