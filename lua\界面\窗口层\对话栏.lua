local 对话栏 = 窗口层:创建窗口("对话栏",0,0,600,450)
function 对话栏:初始化()
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  self.对话高度=20
end


local 显示控件=对话栏:创建控件("显示控件",0,260,600,190)

function 显示控件:初始化()
	self:置精灵(取九宫图像(__res:取资源动画("jszy/dd",0x00000074,"图像"),600,190,20))
	self.第一列=0
	self.第二列=0
	self.第三列=0
	self.加入=0
	self.选项={}
end

function 显示控件:置选项()
	self.选项={}
	self.加入=0
	for i=1,#对话栏.选项缓存 do
		self.选项[i]={}
		self.选项[i].内容=对话栏.选项缓存[i]
		self.选项[i].显示=选项字体:置颜色(__取颜色("红色")):取精灵(对话栏.选项缓存[i])
		self.选项[i].选中=选项字体:置颜色(__取颜色("黄色")):取精灵(对话栏.选项缓存[i])
		self.选项[i].矩形=require("SDL.矩形")(0,0,self.选项[i].显示.宽度,self.选项[i].显示.高度)
	end
	self:调整坐标()
end

function 显示控件:左键弹起(x,y)
    if self.选项 and self.选项[1] then
      for i = 1, 20 do
          if self.选项[i+self.加入] and self.选项[i+self.加入].矩形 and self.选项[i+self.加入].矩形:检查点(x, y)  then
            对话栏:对话事件处理(self.选项[i+self.加入].内容,角色信息.地图数据.编号,self.名称缓存,nil,i+self.加入)
          end
      end
    else
        对话栏:置可见(false)
    end
end

function 显示控件:获得鼠标(x,y)
	self.焦点=nil
	for i = 1, 20 do
		if self.选项[i+self.加入] and self.选项[i+self.加入].矩形 and self.选项[i+self.加入].矩形:检查点(x, y)  then
			  self.焦点=i
     end
	end
end
function 显示控件:失去鼠标(x,y)
	   self.焦点=nil
end

function 显示控件:右键弹起(x,y)
		对话栏:置可见(false)
end 

function 显示控件:显示(x, y)
	if self.选项 then
		for i = 1, 20 do
			if self.选项[i+self.加入] then
				if i>=1 and i<6  then
					self.选项[i+self.加入].矩形:置坐标(x+15,y+对话栏.对话高度+(i-1)*22-3)
					if self.焦点 and self.焦点==i then
						self.选项[i+self.加入].选中:显示(x+20,y+对话栏.对话高度+(i-1)*22)
					else
						self.选项[i+self.加入].显示:显示(x+20,y+对话栏.对话高度+(i-1)*22)
					end
				elseif 	i>=6 and i<11  then 
					self.选项[i+self.加入].矩形:置坐标(x+15+self.第一列,y+对话栏.对话高度+(i-6)*22-3)
					if self.焦点 and self.焦点==i then
						self.选项[i+self.加入].选中:显示(x+20+self.第一列,y+对话栏.对话高度+(i-6)*22)
					else
						self.选项[i+self.加入].显示:显示(x+20+self.第一列,y+对话栏.对话高度+(i-6)*22)
					end
				elseif 	i>=11 and i<16 then 
					self.选项[i+self.加入].矩形:置坐标(x+15+self.第一列+self.第二列,y+对话栏.对话高度+(i-11)*22-3)
					if self.焦点 and self.焦点==i then
						self.选项[i+self.加入].选中:显示(x+20+self.第一列+self.第二列,y+对话栏.对话高度+(i-11)*22)
					else
						self.选项[i+self.加入].显示:显示(x+20+self.第一列+self.第二列,y+对话栏.对话高度+(i-11)*22)
					end
				else
					self.选项[i+self.加入].矩形:置坐标(x+15+self.第一列+self.第二列+self.第三列,y+对话栏.对话高度+(i-16)*22-3)
					if self.焦点 and self.焦点==i then
						self.选项[i+self.加入].选中:显示(x+20+self.第一列+self.第二列+self.第三列,y+对话栏.对话高度+(i-16)*22)
					else
						self.选项[i+self.加入].显示:显示(x+20+self.第一列+self.第二列+self.第三列,y+对话栏.对话高度+(i-16)*22)
					end
				end
			end
		end
		
	end
end


local 上一页=显示控件:创建蓝色按钮("上一页", "上一页", 425, 160,74,20) 
function 上一页:左键弹起(x, y)
		if 显示控件.加入>=20 and 显示控件.选项 and #显示控件.选项>20 then
			显示控件.加入=显示控件.加入-20
			显示控件:调整坐标()
		end
end

local 下一页=显示控件:创建蓝色按钮("下一页", "下一页", 510, 160,74,20) 
function 下一页:左键弹起(x, y)
	if 显示控件.选项 and 显示控件.加入 < #显示控件.选项  then
		显示控件.加入=显示控件.加入+20
		显示控件:调整坐标()
	end

end

function 显示控件:调整坐标()
		self.焦点=nil
		self.第一列=0
		self.第二列=0
		self.第三列=0
		self.上一页:置可见(false)
		self.下一页:置可见(false)
		if self.选项 and #self.选项>5 then
			local xx = 0
			for i = 1, 20 do
				local wh = 100
				if self.选项[i+self.加入] then
					wh=self.选项[i+self.加入].显示.宽度+30
					if  i<6 and self.第一列<=wh then
						self.第一列=wh
					elseif  i<11 and self.第二列<=wh then 
						self.第二列=wh
					elseif i<16 and self.第三列<=wh then 
						self.第三列=wh 	
					end
				
				end
			end
			if #self.选项>20 then
				 if self.加入+20< #self.选项 then
					self.下一页:置可见(true)
				 end
				 if self.加入>=20 then
					self.上一页:置可见(true)
				  end
			end
		end
		
end



function 对话栏:显示(x, y)
	if self.头像 then
    if self.头像缓存=="物件_打铁炉" then
        self.头像:显示(x, y+70+(260- self.头像.高度))
    elseif self.头像缓存=="恶魔泡泡" then
          self.头像:显示(x+75, y+(260- self.头像.高度))
    elseif self.头像缓存=="超级红孩儿" then
          self.头像:显示(x-80, y+(260- self.头像.高度))
    elseif self.头像缓存=="风祖飞廉" then
          self.头像:显示(x-50, y+(260- self.头像.高度))
    elseif self.头像缓存=="刑天" then
          self.头像:显示(x-90, y+(290- self.头像.高度))     
    elseif self.头像缓存=="食铁兽" then
          self.头像:显示(x-120, y+(290- self.头像.高度))    
    else
		    self.头像:显示(x, y+(260- self.头像.高度))
    end
	end
	if self.名称背景 then
		self.名称背景:显示(x+5, y+(260-20))
	end
	if self.名称 then
		self.名称:显示(x+15, y+(260-17))
	end
end
local  内容文本 = 对话栏:丰富文本("内容文本", 10,270,580,20)
function 内容文本:初始化()
		self:置文字(文本字体)
end

function 对话栏:打开(头像,名称,内容,选项,附加事件,多重对话)
	self.可选门派=nil
	if not 内容 then
		return
	end
	self.附加事件=附加事件
	self:置可见(true)
	self:置头像名称(头像, 名称)
	self:置对话选项(内容, 选项, 多重对话)
end





function 对话栏:置对话选项(对话, 选项, 多重对话)
	self.对话高度=20
	self.选项缓存 = {}
	if 选项 and type(选项)=="table" then
		for i = 1, #选项 do
			if 选项[i] ~=nil or 选项[i]~="" then
				table.insert(self.选项缓存,选项[i])
			end
		end
	end

	self.内容文本:清空()
	self.内容文本:置高度(20)
	if 对话 and 对话~="" then
		local w,h= self.内容文本:置文本(对话)
		self.对话高度=20+h
		self.内容文本:置高度(h)
	end
   	self.显示控件:置选项()

end




local 选项网格 = 对话栏:创建网格("选项网格", 0,0, 227, 280)
function 选项网格:置选项()
  --self:置坐标(对话栏.选项坐标.x+20, 对话栏.选项坐标.y+40)
  for k, v in self:遍历控件() do
      if v.对话文本 then
            v:删除控件("对话文本")
      end
  end
  self:创建格子(227, 60, 10, 0, #对话栏.选项缓存, 1, true)
  for i, v in ipairs(self.子控件) do
    if 对话栏.选项缓存[i]  then
		local 对话文本 = self.子控件[i]:丰富文本("对话文本", 0, 0,197,50)
		local w,h = 对话文本:置文本(对话栏.选项缓存[i])
		对话文本:置可见(true,true)
		local xx = 15
		local yy = 5
		if w<197 then
			xx = xx+(197-w)//2
		end
		if h < 50 then
			yy =yy+(50-h)//2
		end
		对话文本:置坐标(xx,yy)
		self.子控件[i]:置精灵(__res:getPNGCC(2, 0, 960, 227, 60):拉伸(227, 60):到精灵())
	else
		self.子控件[i]:置精灵(nil)
    end
  end
end

function 选项网格:左键弹起(x, y, a, b, msg)
	if 对话栏.选项缓存[a] and 对话栏.选项缓存[a]~="" then
		  对话栏:对话事件处理(对话栏.选项缓存[a],角色信息.地图数据.编号,self.名称缓存,nil,a)
	end
end

function 对话栏:置头像名称(头像, 名称)
  self.头像 = nil
  self.头像缓存=nil
  self.名称 = nil
  self.名称背景=nil
  if 名称 and "" ~= 名称 then
	self.名称=标题字体:置颜色(250, 250, 255,255):取精灵(名称)
	self.名称背景=require('SDL.精灵')(0, 0, 0, self.名称.宽度+20, 20):置颜色(0, 0, 0, 150)
  end

  if 头像 and 头像~="" then
	local lssc = 取头像(头像)
	if lssc and lssc[4] then
		self.头像 =__res:取资源动画(lssc[7], lssc[4],"精灵")
    if self.头像.宽度<150 and self.头像.高度<150 then
        self.头像 =__res:取资源动画(lssc[7], lssc[4],"图像"):拉伸(150,150):到精灵()
    end
    self.头像缓存=头像
	end
end
  self.名称缓存 = 名称
end





function 对话栏:对话事件处理(事件,地图1,名称,名称1,编号)
  --local 名称=对话栏.名称缓存
  if 事件 == "不了不了" or 事件 == "算了算了" or 事件 == "怕了怕了，我绕着走还不行吗？" or 事件 == "大王，我是来膜拜你绝世的容颜的" or 事件 == "太贵了我没钱" or 事件 == "我随便逛逛 不好意思" or 事件 == "我只是来看看" or 事件 == "我只是看看" or 事件 == "只是路过" or 事件 == "我只是路过" or 事件 == "我只是随便看看" or 事件 == "我还要逛逛" or 事件 == "我点错了" or 事件 == "我什么也不想做" or 事件 == "我保留意见" or 事件 == "我什么都不想做" or 事件 == "没什么，我只是看看" or 事件 == "我们后会有期" then
	对话栏:置可见(false)
	return 0
elseif 事件 == "部分解锁" or 事件 == "全面解锁" then
  __UI界面.窗口层.物品解锁:打开()
	对话栏:置可见(false)
	return 0
elseif 事件 == "确定解锁插槽" then
	请求服务(6215,self.附加事件)
	self.附加事件=nil
	对话栏:置可见(false)
	return 0
elseif 事件 == "确定添加点数" then
	请求服务(6216)
	对话栏:置可见(false)
	return 0
elseif 事件 == "炼制灵犀之屑" then
	self:打开("物件_打铁炉","物件_打铁炉","炼制灵犀之屑需要，150级-160级的“人物装备”，即可兑换到对应数量的灵犀之屑。\n#G150级装备 = 10\n#G160级装备 = 15",{"我要炼制","我再考虑考虑"})
	return 0
elseif 事件 == "我要炼制" then
	请求服务(6202)
	对话栏:置可见(false)
	return 0
elseif 事件 == "合成灵犀玉" then
	请求服务(6218)
	对话栏:置可见(false)
	return 0
elseif 事件 == "更换神器五行" then
	请求服务(6201)
	对话栏:置可见(false)
	return 0
elseif 事件 == "帮主" or 事件 == "副帮主" or 事件 == "左护法" or 事件 == "右护法" or 事件 == "长老" or 事件 == "堂主" or 事件 == "帮众" or 事件 == "商人" then
	if 窗口层.帮派查看.选中成员 == nil or 窗口层.帮派查看.帮众数据[窗口层.帮派查看.选中成员] == nil  then
		__UI弹出.提示框:打开("#Y请选择正确的目标!")
		对话栏:置可见(false)
		return
	end
	local id1=窗口层.帮派查看.帮众数据[窗口层.帮派查看.选中成员].id
    请求服务(1502,{事件,角色信息.地图数据.编号,self.名称缓存,id1})


elseif 事件=="驯养十次" or 事件=="驯养一次" or 事件=="驯养五十次" or 事件=="驯养百次" or 事件=="驯养千次"  then
	local 驯养目标 = 窗口层.坐骑属性.选中
	if 角色信息.坐骑列表[驯养目标] == nil then
		__UI弹出.提示框:打开("#Y请选择正确的目标!")
		对话栏:置可见(false)
		return
	end
	local 次数 = 1
	if 事件=="驯养十次" then
		次数 = 10
	elseif 事件=="驯养五十次" then
		  次数 = 50
  elseif 事件=="驯养百次" then
      次数 = 100
  elseif 事件=="驯养千次" then
      次数 = 1000
	end
	请求服务(92,{编号=驯养目标,次数=次数})
elseif 事件=="坐骑洗点" then
	local 驯养 =  窗口层.坐骑属性.选中
	if 角色信息.坐骑列表[驯养] == nil then
		  __UI弹出.提示框:打开("#Y请选择正确的目标!")
  else
     请求服务(94,{编号=驯养})
	end
	
elseif 事件=="坐骑放生" then
  local 驯养 =  窗口层.坐骑属性.选中
	if 角色信息.坐骑列表[驯养] == nil then
		  __UI弹出.提示框:打开("#Y请选择正确的目标!")
  else
     请求服务(95,{编号=驯养})
	end
	







elseif 事件=="坐骑喂养" then
	-- local 驯养目标 = tp.窗口.坐骑属性栏.选中
	-- if _tp.坐骑列表[驯养目标] == nil then
	-- 	__UI弹出.提示框:打开("#Y请选择正确的目标!")
	-- 	对话栏:置可见(false)
	-- 	return
	-- end
	-- 请求服务(97,{编号=驯养目标})	-- elseif 事件 == "什么是化境" then
else
	请求服务(1502,{事件,地图1,self.名称缓存,名称1})
end

对话栏:置可见(false)
end


