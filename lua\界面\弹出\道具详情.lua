__UI弹出["道具详情"] = __UI界面["创建弹出窗口"](__UI界面, "道具详情", 0, 0, 295+68, 400)
local 道具详情 = __UI弹出["道具详情"]
function 道具详情:初始化()
end
function 道具详情:左键弹起()
  self:置可见(false)
end
function 道具详情:打开(数据, x, y, w, h, Button, Button2, Button3, Button4, bh, lx)
  self.pid = bh
  self.lx = lx
  -- print(x,引擎.宽度-295+68)
  if x>引擎.宽度-295-190+60 then
    x=引擎.宽度-295-190 +60
  end
  -- self.Button=Button
  -- self.Button2=Button2
  -- self.Button3=Button3
  -- self.Button4=Button4

  self.数据 = 数据
  if self.数据 then
    道具详情["置坐标"](道具详情, x + abbr.py.x+68, y + abbr.py.y)
    local nsf = require("SDL.图像")(295+68, 400)
    if nsf["渲染开始"](nsf) then
      -- __res:getPNGCC(3, 213, 927, 30, 30)["显示"](__res:getPNGCC(3, 213, 927, 30, 30), 0, 0)
      -- __res:getPNGCC(3, 213, 957, 30, 141)["平铺"](__res:getPNGCC(3, 213, 957, 30, 141), 30, h - 60)["显示"](__res:getPNGCC(3, 213, 957, 30, 141)["平铺"](__res:getPNGCC(3, 213, 957, 30, 141), 30, h - 60), 0, 30)
      -- __res:getPNGCC(3, 213, 1098, 30, 31)["显示"](__res:getPNGCC(3, 213, 1098, 30, 31), 0, h - 30)
      -- __res:getPNGCC(3, 243, 927, 140, 30)["平铺"](__res:getPNGCC(3, 243, 927, 140, 30), w - 60, 30)["显示"](__res:getPNGCC(3, 243, 927, 140, 30)["平铺"](__res:getPNGCC(3, 243, 927, 140, 30), w - 60, 30), 30, 0)
      -- __res:getPNGCC(3, 243, 957, 155, 141)["平铺"](__res:getPNGCC(3, 243, 957, 155, 141), w - 60, h - 60)["显示"](__res:getPNGCC(3, 243, 957, 155, 141)["平铺"](__res:getPNGCC(3, 243, 957, 155, 141), w - 60, h - 60), 30, 30)
      -- __res:getPNGCC(3, 243, 1098, 155, 31)["平铺"](__res:getPNGCC(3, 243, 1098, 155, 31), w - 60, 30)["显示"](__res:getPNGCC(3, 243, 1098, 155, 31)["平铺"](__res:getPNGCC(3, 243, 1098, 155, 31), w - 60, 30), 30, h - 30)
      -- __res:getPNGCC(3, 398, 927, 30, 30)["显示"](__res:getPNGCC(3, 398, 927, 30, 30), w - 30, 0)
      -- __res:getPNGCC(3, 398, 957, 30, 141)["平铺"](__res:getPNGCC(3, 398, 957, 30, 141), 30, h - 60)["显示"](__res:getPNGCC(3, 398, 957, 30, 141)["平铺"](__res:getPNGCC(3, 398, 957, 30, 141), 30, h - 60), w - 30, 30)
      -- __res:getPNGCC(3, 398, 1098, 30, 31)["显示"](__res:getPNGCC(3, 398, 1098, 30, 31), w - 30, h - 30)
      取黑透明背景(0, 0, 290, 390, true):置透明(230):显示(68, 0)
      self.小模型 = __res:getPNGCC(3, 132, 506, 55, 55):显示(13+68,13)--["到精灵"]((__res:getPNGCC(3, 132, 506, 55, 55)))
      local 目录="shape/dj/"
      if 锦衣文件完整 and 数据.资源 and (数据.资源=="r3d.dll" or 数据.资源=="nx3d5.dll" or 数据.资源=="nx3d6.dll") then
        目录="shape/sys/"
      end
      if 数据.颜色区分 then
        __res["取图像"](__res, __res["取地址"](__res, 目录, 数据["小模型资源"])):置颜色(检查是否有物品颜色(数据.属性)):显示(15+68, 15)
      else
        __res["取图像"](__res, __res["取地址"](__res, 目录, 数据["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, 数据["小模型资源"])), 15+68, 15)
      end
      
      字体20["置颜色"](字体20, __取颜色("黄色"))
      字体20:取图像(数据.名称):显示(15+68+67, 18)
      nsf["渲染结束"](nsf)
    end
    道具详情["置精灵"](道具详情, nsf["到精灵"](nsf))
  else
    local nsf = require("SDL.图像")(295+68, 400)
    if nsf["渲染开始"](nsf) then
      取黑透明背景(0, 0, 290, 390, true):显示(0+68, 0)
      -- __res:getPNGCC(3, 213, 927, 30, 30)["显示"](__res:getPNGCC(3, 213, 927, 30, 30), 0, 0)
      -- __res:getPNGCC(3, 213, 957, 30, 141)["平铺"](__res:getPNGCC(3, 213, 957, 30, 141), 30, h - 60)["显示"](__res:getPNGCC(3, 213, 957, 30, 141)["平铺"](__res:getPNGCC(3, 213, 957, 30, 141), 30, h - 60), 0, 30)
      -- __res:getPNGCC(3, 213, 1098, 30, 31)["显示"](__res:getPNGCC(3, 213, 1098, 30, 31), 0, h - 30)
      -- __res:getPNGCC(3, 243, 927, 140, 30)["平铺"](__res:getPNGCC(3, 243, 927, 140, 30), w - 60, 30)["显示"](__res:getPNGCC(3, 243, 927, 140, 30)["平铺"](__res:getPNGCC(3, 243, 927, 140, 30), w - 60, 30), 30, 0)
      -- __res:getPNGCC(3, 243, 957, 155, 141)["平铺"](__res:getPNGCC(3, 243, 957, 155, 141), w - 60, h - 60)["显示"](__res:getPNGCC(3, 243, 957, 155, 141)["平铺"](__res:getPNGCC(3, 243, 957, 155, 141), w - 60, h - 60), 30, 30)
      -- __res:getPNGCC(3, 243, 1098, 155, 31)["平铺"](__res:getPNGCC(3, 243, 1098, 155, 31), w - 60, 30)["显示"](__res:getPNGCC(3, 243, 1098, 155, 31)["平铺"](__res:getPNGCC(3, 243, 1098, 155, 31), w - 60, 30), 30, h - 30)
      -- __res:getPNGCC(3, 398, 927, 30, 30)["显示"](__res:getPNGCC(3, 398, 927, 30, 30), w - 30, 0)
      -- __res:getPNGCC(3, 398, 957, 30, 141)["平铺"](__res:getPNGCC(3, 398, 957, 30, 141), 30, h - 60)["显示"](__res:getPNGCC(3, 398, 957, 30, 141)["平铺"](__res:getPNGCC(3, 398, 957, 30, 141), 30, h - 60), w - 30, 30)
      -- __res:getPNGCC(3, 398, 1098, 30, 31)["显示"](__res:getPNGCC(3, 398, 1098, 30, 31), w - 30, h - 30)
      nsf["渲染结束"](nsf)
    end
    道具详情["置精灵"](道具详情, nsf["到精灵"](nsf))
  end
  __道具提示(self.数据, 道具详情["道具文本"])
  local Buttontable = {
    ["使用"] = false,
    ["卸下"] = false,
    ["移动"] = false,
    ["丢弃"] = false,
    ["取出"] = false,
    ["存入"] = false,
    ["符石"] = false
  }
  -- print(Button,Button2,Button3,Button4)
  local yy={307+30,267+30,227+30,187+30}
  -- if Button then
  --   道具详情[Button]["置坐标"](道具详情[Button], 10+68, 307)
  --   道具详情[Button]["置可见"](道具详情[Button], true)
  --   Buttontable[Button] = true
  -- end
  -- if Button2 then
  --   道具详情[Button2]["置坐标"](道具详情[Button2], 10+68, 267)
  --   道具详情[Button2]["置可见"](道具详情[Button2], true)
  --   Buttontable[Button2] = true
  -- end
  -- if Button3 then
  --   道具详情[Button3]["置坐标"](道具详情[Button3], 10+68, 227)
  --   道具详情[Button3]["置可见"](道具详情[Button3], true)
  --   Buttontable[Button3] = true
  -- end
  -- if Button4 then
  --   道具详情[Button4]["置坐标"](道具详情[Button4], 10+68, 187)
  --   道具详情[Button4]["置可见"](道具详情[Button4], true)
  --   Buttontable[Button4] = true
  -- end
  if Button then
    if Button =="丢弃" then
      道具详情[Button]["置坐标"](道具详情[Button], 3+13+8, 227+100+8)
    elseif Button =="使用" then
      道具详情[Button]["置坐标"](道具详情[Button], 10+65+155, 307+30)
    else
      道具详情[Button]["置坐标"](道具详情[Button], 10+73, yy[1])
      table.remove(yy,1)
    end
    道具详情[Button]["置可见"](道具详情[Button], true)
    Buttontable[Button] = true
  end
  if Button2 then
    if Button2 =="丢弃" then
      道具详情[Button2]["置坐标"](道具详情[Button2], 3+13+8, 227+100+8)
    elseif Button2 =="使用" then
      道具详情[Button2]["置坐标"](道具详情[Button2], 10+65+155, 307+30)
    else
      道具详情[Button2]["置坐标"](道具详情[Button2], 10+73, yy[1])
      table.remove(yy,1)
    end
    道具详情[Button2]["置可见"](道具详情[Button2], true)
    Buttontable[Button2] = true
  end
  if Button3 then
    if Button3 =="丢弃" then
      道具详情[Button3]["置坐标"](道具详情[Button3], 3+13+8, 227+100+8)
    elseif Button3 =="使用" then
      道具详情[Button3]["置坐标"](道具详情[Button3], 10+65+155, 307+30)
    else
      道具详情[Button3]["置坐标"](道具详情[Button3], 10+73, yy[1])
      table.remove(yy,1)
    end
    道具详情[Button3]["置可见"](道具详情[Button3], true)
    Buttontable[Button3] = true
  end
  if Button4 then
    if Button4 =="丢弃" then
      道具详情[Button4]["置坐标"](道具详情[Button4], 3+13+8, 227+100+8)
    elseif Button4 =="使用" then
      道具详情[Button4]["置坐标"](道具详情[Button4], 10+65+155, 307+30)
    else
      道具详情[Button4]["置坐标"](道具详情[Button4], 10+73, yy[1])
      table.remove(yy,1)
    end
    道具详情[Button4]["置可见"](道具详情[Button4], true)
    Buttontable[Button4] = true
  end
  -- for i=1,4 do
  --   if self["Button"..i] then
  --     print(self["Button"..i])
  --     if self["Button"..i] =="丢弃" then
  --       道具详情[self["Button"..i]]["置坐标"](道具详情[self["Button"..i]], 3, 227+100)
  --     elseif self["Button"..i] =="使用" then
  --       print(1111111)
  --       道具详情[self["Button"..i]]["置坐标"](道具详情[self["Button"..i]], 10+68, 267+64)
  --     else
  --       道具详情[self["Button"..i]]["置坐标"](道具详情[self["Button"..i]], 10+68, 187+40*i)
  --     end
  --     道具详情[self["Button"..i]]["置可见"](道具详情[self["Button"..i]], true)
  --     Buttontable[self["Button"..i]] = true
  --   end
  -- end
  -- local 道具文本 = 道具详情["创建文本"](道具详情, "道具文本", 15+68, 18, 256, 310)---38
  -- 道具详情.道具文本:置宽高(256, 310)
  -- print(#yy)
  --print( #yy)
  if #yy<=2 then
    道具详情.道具文本:置宽高(256, 310-38-55)
  else
    道具详情.道具文本:置宽高(256, 310-55)
  end
  for k, v in pairs(Buttontable) do
    if false == v then
      道具详情[k]["置可见"](道具详情[k], false)
    -- elseif k=="符石" then
    --   道具详情.道具文本:置宽高(256, 310-38)
    end
  end
end

local 使用 = 道具详情:创建我的按钮(__res:getPNGCC(3, 126, 563, 111, 36, true), "使用", 0, 0, "使用"):置可见(false)
function 使用:左键弹起(x, y, msg)
  local 道具分页递增序列=0
  
  local lx2
  if __UI界面["窗口层"]["道具行囊"]["是否可见"] then
    if __UI界面["窗口层"]["道具行囊"]["人物"]["是否选中"] then
      lx2 = "主人公"
    elseif __UI界面["窗口层"]["道具行囊"]["召唤兽"]["是否选中"] then
      lx2 = "召唤兽"
    elseif __UI界面["窗口层"]["道具行囊"]["坐骑"]["是否选中"] then
      lx2 = "坐骑"
    elseif __UI界面["窗口层"]["道具行囊"]["子女"]["是否选中"] then
      lx2 = "子女"
    end
    道具分页递增序列=(__UI界面["窗口层"]["道具行囊"].分页状态-1)*20
  end
  if 2 == 道具详情["数据"]["总类"] then
    if "主人公" == lx2 then
      if 道具详情["数据"].分类 < 10 then --穿戴装备
        发送数据(3703, {
          ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
          ["角色"] = "主角",
          ["道具"] = 道具详情.pid+道具分页递增序列
        })
      elseif 道具详情["数据"]["分类"] >= 10 and 道具详情["数据"]["分类"] <= 13 then --穿戴灵饰
        发送数据(3703, {
          ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
          ["角色"] = "主角",
          ["道具"] = 道具详情.pid+道具分页递增序列
        })
      elseif 道具详情["数据"].分类 >= 14 and 道具详情["数据"].分类 <= 19 then --穿戴锦衣
        __UI弹出["锦衣弹出"]["打开"](__UI弹出["锦衣弹出"], 角色信息["锦衣"])
        发送数据(3703, {
          ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
          ["角色"] = "主角",
          ["道具"] = 道具详情.pid+道具分页递增序列
        })
      end
    elseif lx2 == "召唤兽" and __UI界面["窗口层"]["道具行囊"]["选中召唤兽"] then
      发送数据(3708, {
        ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
        ["角色"] = "bb",
        ["道具"] = 道具详情.pid+道具分页递增序列,
        ["编号"] = __UI界面["窗口层"]["道具行囊"]["选中召唤兽"]
      })
    end
  else
    if 道具详情["数据"].总类=="可使用" or 道具详情["数据"].名称=="特殊兽决·碎片" or 道具详情["数据"].名称=="装备·碎片" or 道具详情["数据"].名称=="高级装备·碎片" or 道具详情["数据"].名称=="特性宝珠·碎片" 
    or ((道具详情["数据"].分类 <=12 or 道具详情["数据"].总类==144) and 道具详情["数据"].总类~=2) then
      local xl=0
      if "坐骑" == lx2 then
        xl=__UI界面["窗口层"]["道具行囊"]["选中坐骑"]
      elseif "子女" == lx2 then
        xl=__UI界面["窗口层"]["道具行囊"]["选中子女"]
      else
        xl=__UI界面["窗口层"]["道具行囊"]["选中召唤兽"]
      end
      发送数据(3705,{类型=__UI界面["窗口层"]["道具行囊"]["包裹类型"],编号=道具详情.pid+道具分页递增序列,窗口=lx2,序列=xl})
    elseif 道具详情["数据"].总类==889 or 道具详情["数据"].总类==149 then
      local xl=0
      if "坐骑" == lx2 then
        xl=__UI界面["窗口层"]["道具行囊"]["选中坐骑"]
      elseif "子女" == lx2 then
        xl=__UI界面["窗口层"]["道具行囊"]["选中子女"]
      else
        xl=__UI界面["窗口层"]["道具行囊"]["选中召唤兽"]
      end
      发送数据(3705,{类型=__UI界面["窗口层"]["道具行囊"]["包裹类型"],编号=道具详情.pid+道具分页递增序列,窗口=lx2,序列=xl})
    -- elseif 道具详情["数据"].总类=="更改鼠标" then
    --   -- tp.鼠标.置鼠标("铁锤点击")
    elseif 道具详情["数据"].总类=="自选灵饰礼包" then
      -- print(111)
      __UI界面["窗口层"]["对话栏"]:打开("",道具详情["数据"].名称,"请选择你想要的部位，开启一次可获得五套同类型的灵饰书铁。请选择部位：",{"手镯","戒指","佩饰","耳饰"})
      -- tp.窗口.对话栏:文本("",道具详情["数据"].名称,"请选择你想要的部位，开启一次可获得五套同类型的灵饰书铁。请选择部位：",{"手镯","戒指","佩饰","耳饰"})
    -- elseif 道具详情["数据"].总类=="天赋符" then
    --   if 道具详情["数据"].属性 then
    --     发送数据(3705,{类型=__UI界面["窗口层"]["道具行囊"]["包裹类型"],编号=o,窗口=self.窗口,序列=self.选中召唤兽})
    --   else
    --     tp.窗口.对话栏:文本("",道具详情["数据"].名称,"你想加强召唤兽的哪种天赋呢？",{"攻击","气血","防御","速度","灵力","躲闪","我再想想"},nil,{类型=__UI界面["窗口层"]["道具行囊"]["包裹类型"],编号=o})
    --   end
    end
  end
  -- if 2 ~= 道具详情["数据"]["总类"] and (道具详情["数据"]["分类"] <= 12 or 道具详情["数据"]["总类"] == 147 or 106 == 道具详情["数据"]["总类"]) then
  --   if "坐骑" == lx2 then
  --     发送数据(3705, {
  --       ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
  --       ["编号"] = 道具详情.pid,
  --       ["窗口"] = lx2,
  --       ["序列"] = __UI界面["窗口层"]["道具行囊"]["选中坐骑"]
  --     })
  --   elseif "子女" == lx2 then
  --     发送数据(3705, {
  --       ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
  --       ["编号"] = 道具详情.pid,
  --       ["窗口"] = lx2,
  --       ["序列"] = __UI界面["窗口层"]["道具行囊"]["选中子女"]
  --     })
  --   else
  --     发送数据(3705, {
  --       ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
  --       ["编号"] = 道具详情.pid,
  --       ["窗口"] = lx2,
  --       ["序列"] = __UI界面["窗口层"]["道具行囊"]["选中召唤兽"]
  --     })
  --   end
  -- elseif 2 == 道具详情["数据"]["总类"] then
  --   if "主人公" == lx2 and (道具详情["数据"]["分类"] <= 6 or 道具详情["数据"]["分类"] >= 10 and 道具详情["数据"]["分类"] <= 13) then
  --     发送数据(3703, {
  --       ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
  --       ["角色"] = "主角",
  --       ["道具"] = 道具详情.pid
  --     })
  --   elseif "主人公" == lx2 and 道具详情["数据"]["分类"] >= 14 and 道具详情["数据"]["分类"] <= 19 then
  --     __UI弹出["锦衣弹出"]["打开"](__UI弹出["锦衣弹出"], 角色信息["锦衣"])
  --     发送数据(3703, {
  --       ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
  --       ["角色"] = "主角",
  --       ["道具"] = 道具详情.pid
  --     })
  --   elseif "召唤兽" == lx2 and __UI界面["窗口层"]["道具行囊"]["选中召唤兽"] and 道具详情["数据"]["分类"] >= 7 and 道具详情["数据"]["分类"] <= 9 then
  --     发送数据(3708, {
  --       ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
  --       ["角色"] = "bb",
  --       ["道具"] = 道具详情.pid,
  --       ["编号"] = __UI界面["窗口层"]["道具行囊"]["选中召唤兽"]
  --     })
  --   end
  -- end
  道具详情["置可见"](道具详情, false)
end
local 卸下 = 道具详情:创建我的按钮(__res:getPNGCC(3, 126, 563, 111, 36, true), "卸下", 0, 0, "卸下"):置可见(false)
function 卸下:左键弹起(x, y, msg)
  if 道具详情.lx == "装备法宝" or 道具详情.lx == "装备灵宝" and __UI界面["窗口层"]["法宝"]["是否可见"] then
    if 道具详情.lx == "装备法宝" then
      发送数据(3734, {
        ["序列"] = 道具详情.pid
      })
    elseif 道具详情.lx == "装备灵宝" then
      发送数据(3753, {
        ["序列"] = 道具详情.pid
      })
    end
  elseif 道具详情.lx == "宝宝饰品" then
    if 角色信息.宝宝列表[__UI界面["窗口层"]["召唤兽属性"].选中召唤兽] and 角色信息.宝宝列表[__UI界面["窗口层"]["召唤兽属性"].选中召唤兽].认证码 then 
      发送数据(5019,{角色信息.宝宝列表[__UI界面["窗口层"]["召唤兽属性"].选中召唤兽].认证码})
      __UI界面["窗口层"]["召唤兽属性"].饰品网格:置物品()
    end
  
  elseif __UI界面["窗口层"]["道具行囊"]["是否可见"] then
    if __UI界面["窗口层"]["道具行囊"]["人物"]["是否选中"] then
      if 道具详情.lx == "装备" and 道具详情["数据"]["分类"] <= 6 then
        发送数据(3704, {
          ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
          ["角色"] = "主角",
          ["道具"] = 道具详情.pid
        })
      elseif 道具详情.lx == "锦衣" and 道具详情["数据"]["分类"] >= 14 and 道具详情["数据"]["分类"] <= 19 then
        发送数据(3704, {
          ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
          ["角色"] = "主角",
          ["锦衣"] = true,
          ["道具"] = 道具详情.pid
        })
      elseif 道具详情.lx == "灵饰" and 道具详情["数据"]["分类"] >= 10 and 道具详情["数据"]["分类"] <= 13 then
        发送数据(3704, {
          ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
          ["角色"] = "主角",
          ["灵饰"] = true,
          ["道具"] = 道具详情.pid
        })
      end
    elseif __UI界面["窗口层"]["道具行囊"]["召唤兽"]["是否选中"] then
      发送数据(3709, {
        ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
        ["角色"] = "bb",
        ["道具"] = 道具详情.pid,
        ["编号"] = __UI界面["窗口层"]["道具行囊"]["选中召唤兽"]
      })
    elseif __UI界面["窗口层"]["道具行囊"]["坐骑"]["是否选中"] then
      发送数据(3747, {
        ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"],
        ["角色"] = "坐骑",
        ["道具"] = 道具详情.pid,
        ["编号"] = __UI界面["窗口层"]["道具行囊"]["选中坐骑"]
      })
    elseif __UI界面["窗口层"]["道具行囊"]["子女"]["是否选中"] then
    end
  end
  道具详情["置可见"](道具详情, false)
end
local 移动 = 道具详情:创建我的按钮(__res:getPNGCC(3, 126, 563, 111, 36, true), "移动", 0, 0, "移动"):置可见(false)
function 移动:左键弹起(x, y, msg)
  if 道具详情.lx == "法宝" then
    __UI界面["窗口层"]["法宝"]["移动"] = true
  else
    __UI界面["窗口层"]["道具行囊"]["移动"] = true
  end
  道具详情["置可见"](道具详情, false)
end
local 丢弃 = 道具详情:创建我的按钮(__res:getPNGCC(3, 1085, 446, 62, 62, true):拉伸(43,43), "丢弃", 13, 0):置可见(false)
function 丢弃:左键弹起(x, y, msg)
  __UI弹出["提示弹出"]["打开"](__UI弹出["提示弹出"], "确认#R销毁#Y" .. 道具详情["数据"]["名称"] .. "#W?", 3702, {
    ["物品"] = 道具详情.pid+(__UI界面["窗口层"]["道具行囊"].分页状态-1)*20,
    ["类型"] = __UI界面["窗口层"]["道具行囊"]["包裹类型"]
  })
  道具详情["置可见"](道具详情, false)
end
local 符石 = 道具详情:创建我的按钮(__res:getPNGCC(3, 126, 563, 111, 36, true), "符石", 0, 0, "符石"):置可见(false)
function 符石:左键弹起(x, y, msg)
  道具详情["置可见"](道具详情, false)
  发送数据(3800,{装备=道具详情.pid+(__UI界面["窗口层"]["道具行囊"].分页状态-1)*20,类型=__UI界面["窗口层"]["道具行囊"]["包裹类型"]})
  -- __UI界面["窗口层"]["符石镶嵌"]["打开"](__UI界面["窗口层"]["符石镶嵌"], 道具详情["数据"], 道具详情.pid)
end

local 取出 = 道具详情:创建我的按钮(__res:getPNGCC(3, 126, 563, 111, 36, true), "取出", 0, 0, "取出"):置可见(false)
function 取出:左键弹起(x, y, msg)
  道具详情["置可见"](道具详情, false)
  -- __UI界面["窗口层"]["符石"]["打开"](__UI界面["窗口层"]["符石"], 道具详情["数据"], 道具详情.pid)
end

local 存入 = 道具详情:创建我的按钮(__res:getPNGCC(3, 126, 563, 111, 36, true), "存入", 0, 0, "存入"):置可见(false)
function 存入:左键弹起(x, y, msg)
  道具详情["置可见"](道具详情, false)
  -- __UI界面["窗口层"]["符石"]["打开"](__UI界面["窗口层"]["符石"], 道具详情["数据"], 道具详情.pid)
end


local 道具文本 = 道具详情["创建文本"](道具详情, "道具文本", 15+68, 18+55, 256, 230)---38
function 道具文本:初始化()
  self:置文字(字体17)
end
