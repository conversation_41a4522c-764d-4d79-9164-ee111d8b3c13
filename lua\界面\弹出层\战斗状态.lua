
if __手机 then
  __UI弹出.战斗状态 = 界面:创建弹出窗口("战斗状态", 0, 0)
else
  __UI弹出.战斗状态 = 界面:创建提示控件("战斗状态", 0, 0)
end
local 战斗状态 = __UI弹出.战斗状态
function 战斗状态:初始化()
end
function 战斗状态:打开(数据,x,y)
 self:置可见(true,true)
 self.状态网格:置宽高(270,50)
 local w,h = 290,50
 if 数据 then
     h = math.ceil(#数据/2)*50
     if #数据==1 then
        w=145
     end
 end
 self:置精灵(require("SDL.精灵")(0, 0, 0,w,h+10):置颜色(0, 0, 0, 210),true)
 self:置宽高(w, h + 10)
 self.状态网格:置宽高(270,h)
 self.状态网格:置技能(数据)
 local xx = 0
 local yy = 0
 if x + w <引擎.宽度 then
     xx = x
 else
     xx = 引擎.宽度-w
 end
 if y + h +10 <引擎.高度 then
     yy = y
 else
     yy = 引擎.高度-(h+10)
 end
 if xx<5 then
    xx = 5
 end
 if yy <5 then
    yy = 5
 end
 self:置坐标(xx,yy)
end

local 状态网格 = 战斗状态:创建网格("状态网格", 5, 5, 270, 50)

function 状态网格:置技能(数据)
      if  not 数据 then 数据={} end
      self:创建格子(135,50,0,0,math.ceil(#数据/2),2)
      for i, v in ipairs(self.子控件) do
            v:创建纹理精灵(function()
                if 数据 and 数据[i] then
                  __res:取资源动画("jszy/fwtb",0x00000030,"图像"):拉伸(34,34):显示(3,3)
                  __res:取资源动画(数据[i].资源,数据[i].图标,"图像"):拉伸(30,30):显示(5,5)
                  文本字体:置颜色(255,255,255,255):取图像(数据[i].说明):显示(40,5)
                end
            end)
      end
end

