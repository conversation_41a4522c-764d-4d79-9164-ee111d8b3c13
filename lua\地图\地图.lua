--[[
LastEditTime: 2024-11-05 20:29:34
--]]

local 地图寻路 = require("地图/寻路")
local 地图类 = class("地图", 地图寻路)
local 排序 = function(a, b)
      if not a or not b then return end
      return a.xy.y < b.xy.y
end
function 地图类:初始化(id)
    local map = assert(__res:取地图("scene/" .. id .. ".map"), "地图不存在")
    self.map = map
    self.id = id
    self.宽度 = map.width
    self.高度 = map.height
    self.列数 = map.colnum
    self.行数 = map.rownum
    self.块宽度 = 320
    self.块高度 = 240
    self.地图更新=os.clock()
    self:置宽高()
    self.info = {}
    local n = 0
    for y = 1, self.行数 do
        self.info[y] = {}
        for x = 1, self.列数 do
            self.info[y][x] = {
                xy = require("GGE.坐标")((x - 1) * self.块宽度, (y - 1) * self.块高度),
                id = n
            }
            n = n + 1
        end
    end
    地图寻路:地图寻路(self.列数 * 16, self.行数 * 12, map:取障碍())
    self.特效 = {}
    self.家具 = {}
    self.传送圈={}
    self.显示表=setmetatable({}, { __mode = "v" })
    self.a = require("GGE.坐标")(1, 1)
    self.b = require("GGE.坐标")(1, 1)
    self.云 =__res:取资源动画('jszy/xjiem',4046268095,"动画")
    self.云1 =__res:取资源动画('jszy/xjiem',3362353993,"动画")

end

function 地图类:加载(id)
    self.map:清空缓存()
    local map = assert(__res:取地图("scene/" .. id .. ".map"), "地图不存在")
    self.map = map
    self.id = id
    self.地图更新=os.clock()
    self.宽度 = map.width
    self.高度 = map.height
    self.列数 = map.colnum
    self.行数 = map.rownum
    self.块宽度 = 320
    self.块高度 = 240
    self:置宽高()
    self.info = {}
    local n = 0
    for y = 1, self.行数 do
        self.info[y] = {}
        for x = 1, self.列数 do
            self.info[y][x] = {
                xy = require("GGE.坐标")((x - 1) * self.块宽度, (y - 1) * self.块高度),
                id = n
            }
           
            n = n + 1
        end
    end
    地图寻路:地图寻路(self.列数 * 16, self.行数 * 12, map:取障碍())
    for k, v in pairs(self.特效) do
      self.特效[k] = nil
    end
    for k, v in pairs(self.家具) do
      self.家具[k] = nil
    end
    for k, v in pairs(self.传送圈) do
          self.传送圈[k] = nil
    end
    self.特效 = {}
    self.家具 = {}
    self.传送圈={}
    self.显示表=setmetatable({}, { __mode = "v" })
    --
    self.a = require("GGE.坐标")(1, 1)
    self.b = require("GGE.坐标")(1, 1)
    --test1()

end

function 地图类:置宽高()
    self.屏幕列数 = math.ceil(引擎.宽度 / 320)
    self.屏幕列数2 = self.列数 - self.屏幕列数
    self.屏幕列数3 = math.ceil(self.屏幕列数 / 2)
    self.屏幕行数 = math.ceil(引擎.高度 / 240)
    self.屏幕行数2 = self.行数 - self.屏幕行数
    self.屏幕行数3 = math.ceil(self.屏幕行数 / 2)

end

local _协程载入 = function(self, t)
    t.精灵, t.遮罩 = self.map:取精灵(t.id)
    t.load = nil
end
function 地图类:更新(dt, pos)
  self.显示表=setmetatable({}, { __mode = "v" })

    if pos.x < self.屏幕列数 then
        self.a.x = 1
        self.b.x = self.屏幕列数 + 1
    elseif pos.x > self.屏幕列数2 then
        self.a.x = self.列数 - self.屏幕列数 - 1
        self.b.x = self.列数
    else
        self.a.x = pos.x - self.屏幕列数3
        self.b.x = pos.x + self.屏幕列数3
    end
    if pos.y < self.屏幕行数 then
        self.a.y = 1
        self.b.y = self.屏幕行数 + 1
    elseif pos.y > self.屏幕行数2 then
        self.a.y = self.行数 - self.屏幕行数 - 1
        self.b.y = self.行数
    else
        self.a.y = pos.y - self.屏幕行数3
        self.b.y = pos.y + self.屏幕行数3
    end
    local 重复遮罩 = {}
    for y = self.a.y, self.b.y do
        for x = self.a.x, self.b.x do
            local t = self.info[y] and self.info[y][x]
            if t then
                if __主显.主角.xy:取距离(t.xy) < 1200 and  not t.精灵 and not t.load then --
                    t.load = true
                    t.精灵, t.遮罩 = self.map:取精灵2(t.id)
                end
                if t.精灵 and t.遮罩 and not __主显.主角.飞行 and not 重复遮罩[t.id] then
                    if __主显.主角.xy:取距离(t.xy) < 1200 then
                        coroutine.xpcall(_协程载入, self, t)
                        for _, v in ipairs(t.遮罩) do
                            if v then
                                重复遮罩[t.id] = true
                                table.insert(self.显示表, v)
                            end
                        end
                    else
                        t.load = nil
                        t.精灵 = nil
                        t.遮罩 = nil
                    end
                end
            end
        end
    end
    if self.地图更新 and os.clock()-self.地图更新>=0.08 then
        self.map:更新()
        self.地图更新=os.clock()
    end
    

    if not _tp.战斗中 then
        for i, v in pairs(__主显.场景人物) do
            if  __主显.场景人物[i] and __主显.主角 
            and __主显.主角.xy:取距离(v.xy) < 800 then
              v:更新(dt)
              table.insert(self.显示表, v)
            end
        end
        for i, v in pairs(self.特效) do
            if __主显.主角.xy:取距离(v.xy) < 800  then
                v:更新(dt)
              table.insert(self.显示表, v)
            end
        end
        for i, v in pairs(self.家具) do
            if __主显.主角.xy:取距离(v.xy) < 800  then
                v:更新(dt)
                table.insert(self.显示表, v)
            end
        end
        for i, v in pairs(self.传送圈) do
            if __主显.主角.xy:取距离(v.xy) < 800  then
                v:更新(dt)
                table.insert(self.显示表, v)
            end
        end
    end

  
    table.sort(self.显示表, 排序)
   
    if __主显.主角.飞行 and __res.配置.坐骑显示==1 then
        self.云:更新(dt)
        self.云1:更新(dt)
    end


 
end

function 地图类:显示(xy)
    for y = self.a.y, self.b.y do
        for x = self.a.x, self.b.x do
            local t = self.info[y] and self.info[y][x]
            if t and t.精灵 then
               t.精灵:显示(t.xy + xy)
            end
        end
    end
    for i = 1, #self.显示表 do
        self.显示表[i]:显示(xy)
    end
    if __主显.主角.飞行 and __res.配置.坐骑显示==1 then
        self.云:显示(10,math.floor(引擎.高度/3+20))
        self.云1:显示(math.floor(引擎.宽度/2+200),math.floor(引擎.高度/3+20))
    end
  
end


function 地图类:消息事件(msg)
      for k, v in pairs(self.家具) do
          if v.消息事件 then
              v:消息事件(msg)
          end
      end
      for k, v in pairs(self.传送圈) do
          if v.消息事件 then
              v:消息事件(msg)
          end
      end
end







function 地图类:添加特效(t)
    self.特效[t.id] = require("特效")(t)
end

function 地图类:删除特效(id)
    self.特效[id] = nil
end


function 地图类:添加传送圈(t)
  self.传送圈[t.编号] = require("跳转")(t)
end

function 地图类:删除传送圈(id)
  self.传送圈[id] = nil
end

function 地图类:添加家具(t)
  self.家具[t.编号] = require("家具")(t)
end

function 地图类:删除家具(id)
  self.家具[id] = nil
end








return 地图类
