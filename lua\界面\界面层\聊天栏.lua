

local 聊天控件 = 界面层:创建控件('聊天控件',0,0)


local SDL = require 'SDL'
function 聊天控件:初始化()
    self.历史= {}
    self.超链接 = {}
    self.超链接记录= {}
    self:置宽高(引擎.宽度, 引擎.高度)
    self.可初始化=true
end

function 聊天控件:重新初始化()
    for k, v in self:遍历控件() do
        v:初始化()
    end
    if self.聊天窗口 then
        self.聊天窗口:重新初始化()
    end
end

-- function 聊天控件:重置()
--       self:置可见(true,true)
--       --self.频道表情:置可见(false)
--       self.频道颜色:置可见(false)
--       self.频道控件:置可见(false)
-- end


function 聊天控件:添加文本(文本,频道)
        if not self.消息缓存 then self.消息缓存={} end
        if #self.消息缓存>=500 then table.remove(self.消息缓存,1) end
        table.insert(self.消息缓存,{频道=频道,文本=文本})
        if self.聊天窗口 then
            self.聊天窗口:添加内容(文本,频道)
        else
            self.内容区域.聊天列表:添加内容(文本,频道)
        end
    
end

local 内容区域 = 聊天控件:创建控件('内容区域', 0, 0, 410, 160)
function 内容区域:初始化()
        self:置精灵(require('SDL.精灵')(0, 0, 0, 410, 160):置颜色(20, 20, 60, 170), true)
        self:置坐标(0,引擎.高度-180)
end



function 内容区域:检查消息()
    return false
end

function 内容区域:修改高度(v)
        if self.高度 + v < 160 or self.高度 + v > 引擎.高度 - 100  or self.y-v<20 or self.y-v>引擎.高度-180 then
            return
        end
        self:置精灵(require('SDL.精灵')(0, 0, 0, 410, self.高度 + v):置颜色(20, 20, 60, 170), true)
        self.聊天列表:置高度(self.高度 - 35)
        local x, y = self:取坐标()
        self:置坐标(x, y - v)
        聊天控件.拉伸按钮.y=self.y-20
        聊天控件.喇叭按钮.y=self.y-20
end

function 内容区域:移动坐标(px,py)
    if self.x + self.宽度+px <=引擎.宽度 and  self.x+px >=0 then
        self.x = self.x + px
    end
    if self.y + self.高度+py <=引擎.高度-20 or self.y+py >=30 then
        self.y = self.y + py
    end
    if self.x<=0 then
        self.x=0
    end
    if self.x+self.宽度>=引擎.宽度 then
        self.x = 引擎.宽度-self.宽度
    end
    聊天控件.喇叭按钮.x = self.x+25
    聊天控件.拉伸按钮.x = self.x
    if self.y <=30 then
        self.y =30
    end
    if self.y+self.高度 >=引擎.高度-20 then
        self.y = 引擎.高度-self.高度-20
    end
    聊天控件.喇叭按钮.y = self.y-20
    聊天控件.拉伸按钮.y = self.y-20
end


local 聊天列表 = 内容区域:创建列表('聊天列表', 5, 5, 400, 125)
function 聊天列表:初始化()
        self.选中精灵 = nil
        self.焦点精灵 = nil
        self.行间距 = 5
        self:自动滚动(true)
end

function 聊天列表:检查消息()
    return false
end

-- function 聊天列表:鼠标滚轮(x,y,xx,yy,xxx,yyy)

--    -- self:自动滚动(v)
-- end


function 聊天列表:添加内容(文本,频道)
    if #self.子控件>=500 then
        self.子控件[1]:删除控件("文本")
        self:删除(1)
    end
    if not 频道 or not __频道表[频道] then
        频道 = "dq"
    end
    文本= 文本 or " "
    local r = self:添加():置精灵()
    local 文本内容 = r:丰富文本('文本', 0, 0, self.宽度, 500,true)
   -- 文本.获得回调 = 聊天列表.文本获得回调
            -- 文本.回调左键弹起 = 聊天列表.文本回调左键弹起
            -- 文本.回调右键弹起 = 聊天列表.文本回调右键弹起

    local 临时内容 = " #" .. __频道表[频道] .. 文本
    if string.find(文本,"消耗了") or string.find(文本,"请截图给管理员")  or  string.find(文本,"请求下载中")  then
        临时内容 =  文本
    end
    local _, h = 文本内容:置文本(临时内容)
    文本内容:置高度(h)
    r:置高度(h)
    r:置可见(true, true)
end

function 聊天列表:文本获得回调(x, y, v)
            --鼠标层:手指形状()
end

function 聊天列表:文本回调右键弹起(v)
            -- local nid = v:match('0|(.*)')
            -- if nid then
            --     聊天列表:文本右键返回(
            --         nid,
            --         self:弹出右键 {
            --             '加为好友',
            --             '设为私聊',
            --             '屏蔽发言',
            --             '查看装备',
            --             '禁止发言'
            --         }
            --     )
            -- end
end

-- function 聊天列表:文本回调左键弹起(nid)
        --     print('左键点击')
        --     if nid:match('0|(.*)') == nil then
        --         local t, d = __rpc:角色_查看对象(nid)
        --         if t == 1 then --物品
        --             local r = require('界面/数据/物品')(d)
        --             窗口层:打开物品提示(r)
        --         elseif t == 2 then --召唤
        --             窗口层:打开召唤兽查看(d)
        --         end
        --     else
        --         local v = nid
        --         local newnid = v:match('0|(.*)')
        --         if newnid then
        --             聊天列表:文本右键返回(
        --                 newnid,
        --                 self:弹出右键 {
        --                     '加为好友',
        --                     '设为私聊',
        --                     '屏蔽发言',
        --                     '查看装备',
        --                     '禁止发言'
        --                 }
        --             )
        --         end
        --     end
-- end

-- function 聊天列表:文本右键返回(nid, v)
        --     -- print(nid, v)
        --     if v == '加为好友' then
        --         __rpc:角色_申请添加好友(nid)
        --     elseif v == '设为私聊' then

        --     elseif v == '屏蔽发言' then

        --     elseif v == '查看装备' then

        --     elseif v == '禁止发言' then
        --         __rpc:快捷禁言(nid)
        --     end
 -- end


--  self.按钮_拉伸 = 按钮(资源:载入('登陆资源.wdf',"网易WDF动画",0xD209A05E),0,0,1,true,nil,true)
--  self.喇叭 = 按钮(资源:载入('登陆资源.wdf',"网易WDF动画",0xD150CE5B),0,0,1,true)

 local 拉伸按钮 = 聊天控件:创建按钮('拉伸按钮', 0, 0)
 function 拉伸按钮:初始化()
    self:创建按钮精灵( __res:取资源动画('dlzy',0xD209A05E),1)
    self:置坐标(0, 引擎.高度-200)
end
function 拉伸按钮:左键按下(x, y)
    self.按下y = y
end

function 拉伸按钮:左键按住(x, y)
    if self.按下y then
        local mx, my = 引擎:取鼠标坐标()
        local py = self.按下y-my
        self.按下y =my
        内容区域:修改高度(py)
    end
end
function 拉伸按钮:左键弹起(x, y)
    self.按下y =nil
end



local 喇叭按钮 = 聊天控件:创建按钮('喇叭按钮', 0, 0)
function 喇叭按钮:初始化()
   self:创建按钮精灵( __res:取资源动画('dlzy',0xD150CE5B),1)
   self:置坐标(25, 引擎.高度-200)
end


function 喇叭按钮:左键弹起(x, y)

end



local 左拉按钮 = 内容区域:创建按钮('左拉按钮', 50, -25)
function 左拉按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x55AB686A,"图像"),1)
end
function 左拉按钮:左键按下(x, y)
    if not 聊天控件.聊天窗口 and not __手机 then
        for i, v in ipairs(聊天列表.子控件) do
            v:删除控件("文本")
        end
        聊天列表:清空()
        聊天控件.内容区域:置可见(false)
        聊天控件.拉伸按钮:置可见(false)
        聊天控件.喇叭按钮:置可见(false)
       -- test1()
       collectgarbage()
        聊天控件.聊天窗口 = require('界面/聊天')
        聊天控件.聊天窗口.关闭 = function()
            聊天控件.内容区域:置可见(true)
            聊天控件.拉伸按钮:置可见(true)
            聊天控件.喇叭按钮:置可见(true)
            聊天控件.聊天窗口 = nil
            if not 聊天控件.消息缓存 then 聊天控件.消息缓存={} end
            for i, v in ipairs(聊天控件.消息缓存) do
                聊天列表:添加内容(v.文本,v.频道)
            end
        end
        聊天控件.聊天窗口:打开()

    end
end




   




local 上拉按钮 = 内容区域:创建按钮('上拉按钮', 72, -25)
function 上拉按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x9C6CABF6,"图像"),1)
end
function 上拉按钮:左键按下(x, y)
        聊天列表:向上滚动()
end

local 下拉按钮 = 内容区域:创建按钮('下拉按钮', 94, -25)
function 下拉按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x2BA6891C,"图像"),1)
end
function 下拉按钮:左键按下(x, y)
        聊天列表:向下滚动()
end
local 移动按钮 = 内容区域:创建按钮('移动按钮', 116, -25)
function 移动按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x32BE710D,"图像"),1)
end

function 移动按钮:左键按下(x, y)
    self.按下x = y
    self.按下y = y

end

function 移动按钮:左键按住(x, y)
    if self.按下y and self.按下x then
        local mx, my = 引擎:取鼠标坐标()
        local px,py = self.按下x-mx,self.按下y-my
        self.按下x,self.按下y =mx, my 
        内容区域:移动坐标(-px,-py)
    end
end
function 移动按钮:左键弹起(x, y)
    self.按下y =nil
    self.按下x =nil
end
local 查询按钮 = 内容区域:创建按钮('查询按钮', 138, -25)
function 查询按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x2E6E37A3,"图像"),1)
end
local 禁止按钮 = 内容区域:创建按钮('禁止按钮', 160, -25)
function 禁止按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x1122D737,"图像"),1)
end
local 锁定按钮 = 内容区域:创建按钮('锁定按钮', 182, -25)
function 锁定按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0x9B8428CC,"图像"),1)
end
function 锁定按钮:左键按下(x, y)
    if self.已锁定 then
        self.已锁定 =false
    else
        self.已锁定=true
    end
    聊天列表:自动滚动(self.已锁定)
end

local 清屏按钮 = 内容区域:创建按钮('清屏按钮', 204, -25)
function 清屏按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('dlzy',0xAEB2E8AC,"图像"),1)
end
function 清屏按钮:左键按下(x, y)
        for i, v in ipairs(聊天列表.子控件) do
            v:删除控件("文本")
        end
        聊天列表:清空()
        聊天控件.消息缓存={}
end



--==================================================================================================
 local 输入区域 = 聊天控件:创建控件('输入区域', 0, 0, 460, 32)
 function 输入区域:初始化()

  self:创建纹理精灵(function()
      __res:取资源动画('dlzy',0x4BC54173,"图像"):显示(0,9)
      __res:取资源动画("pic", "dtltk.png","图片"):显示(40,9)
    end
  )
    self:置坐标( 0, 引擎.高度-32)
end



local 频道按钮 = 输入区域:创建按钮('频道按钮', 0, 10, 40, 32)
local x频道 = {0X3EFEA3B2,0X177C94C8,0X53B4E145,0X43A27A6,0XEB0A7DDA,0X1346424F,0X1961D39F,0X1FF1384F,0X4705033A,0X74158BD0,0X9FE6A010,0x6A92C472}
function 频道按钮:初始化()
      self:创建按钮精灵(__res:取资源动画('dlzy',0X3EFEA3B2))
      self.当前频道=1
end

function 频道按钮:左键弹起(x,y)
      聊天控件.频道控件:打开()
end

local 频道背景 = 输入区域:创建控件('频道背景', 0, 0, 40, 32)
频道背景:置精灵(__res:取资源动画('dlzy',0xB39D470E,"精灵"))

local 颜色按钮 = 输入区域:创建按钮('颜色按钮', 410, 9, 10, 25)

function 颜色按钮:初始化()
      self:创建按钮精灵(__res:取资源动画('dlzy',0x9D123E79),1)

end


function 颜色按钮:左键弹起(x,y)
    聊天控件.频道颜色:打开()
end



local 表情按钮 = 聊天控件:创建按钮('表情按钮', 0, 0, 25, 25)




function 表情按钮:初始化()
    self:创建按钮精灵(__res:取资源动画('dlzy',0x590CAA9B),1)
    self:置坐标(420, 引擎.高度-25)
end

function 表情按钮:左键弹起(x,y)
       -- 聊天控件.频道表情:打开()
       if  __UI弹出.频道表情.是否可见 then
          __UI弹出.频道表情:置可见(false)
       else
          __UI弹出.频道表情:打开()
       end
        
end


function 表情按钮:获得鼠标(x,y)
    __UI弹出.自定义:打开(x-15,y-30,"表情")
end





local 内容输入 = 输入区域:创建文本输入("内容输入", 97, 12, 305, 18)
function 内容输入:初始化()
    self.历史位置 = 1
    self:取光标精灵(255, 255, 255, 255)
    self:置限制字数(120)
    self:置颜色(255, 255, 255, 255)

end

function 内容输入:键盘弹起(键码, 功能)
    if 键码 == SDL.KEY_ENTER or 键码 == SDL.KEY_KP_ENTER then
        if self:取文本()~=nil and self:取文本()~="" then
            local 发送文本 =  require("数据/敏感词库")(self:取文本())
            请求服务(6001,{频道=频道按钮.当前频道,文本=发送文本,聊天控件.超链接})
            table.insert(聊天控件.历史, 发送文本)
            table.insert(聊天控件.超链接记录, 聊天控件.超链接)
            if #聊天控件.历史 > 20 then
                table.remove(聊天控件.历史, 1)
                table.remove(聊天控件.超链接记录, 1)
            end
            self.历史位置 = #聊天控件.历史 + 1
            聊天控件.超链接 = {}
            self:清空()
        else
            __UI弹出.提示框:打开('#Y温馨提示：你还未输入内容')
        end
    elseif 键码 == SDL.KEY_UP then
        self.历史位置 = self.历史位置 - 1
        if 聊天控件.历史[self.历史位置] then
            self:置文本(聊天控件.历史[self.历史位置])
        else
            self.历史位置 = self.历史位置 + 1
        end
    elseif 键码 == SDL.KEY_DOWN then
        self.历史位置 = self.历史位置 + 1
        if 聊天控件.历史[self.历史位置] then
            self:置文本(聊天控件.历史[self.历史位置])
        else
            self.历史位置 = self.历史位置 - 1
        end
    end

end



  


local 频道颜色 =聊天控件:创建控件("频道颜色", 412, 引擎.高度-55, 145, 25)
local 颜色表 = {{"#R","红色"},{"#C","橙色"},{"#Y","黄色"},{"#G","绿色"},{"#P","青色"},{"#B","蓝色"},{"#F","紫色"},{"#W","白色"}}
function 频道颜色:初始化()
      self:置精灵(require('SDL.精灵')(0, 0, 0, 145, 25):置颜色(0, 0, 0, 200), true)
      self:置坐标(412, 引擎.高度-55)
      self:置可见(false)
end

function 频道颜色:打开()
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
end
local 颜色网格 =频道颜色:创建网格("颜色网格", 5, 6, 135, 12)
function 颜色网格:初始化()
    self:创建格子(12, 12, 5, 5, 1, 8)
    for i, v in ipairs(self.子控件) do
        v:置精灵(require('SDL.精灵')(0, 0, 0, 12, 12):置颜色(__取颜色(颜色表[i][2])), true)
    end
end

function 颜色网格:获得鼠标(x,y,a)
       if 颜色表[a] then
          __UI弹出.自定义:打开(x-15,y-30,颜色表[a][2])
      end
end
function 颜色网格:左键弹起(x,y,a)
      if 颜色表[a] then
          聊天控件.输入区域.内容输入:插入文本(颜色表[a][1])
      end
      频道颜色:置可见(false)
 end


local 频道控件 =聊天控件:创建控件("频道控件", 0, 引擎.高度-286, 40, 264)
function 频道控件:初始化()
   self:置坐标(0, 引擎.高度-286)
   self:置可见(false)
end
function 频道控件:打开()
        self:置可见(not self.是否可见)
        if not self.是否可见 then
            return
        end
end
for i = 1, #x频道 do
    local 临时按钮 = 频道控件:创建按钮("按钮"..i,0,(i-1)*22,40,22)
    function 临时按钮:初始化()
      self:创建按钮精灵(__res:取资源动画('dlzy',x频道[i]))
 
    end
    function 临时按钮:左键弹起(x,y)
        频道按钮.当前频道=i
        频道按钮:创建按钮精灵(__res:取资源动画('dlzy',x频道[i]))
        频道控件:置可见(false)
    end
    
end



