

local SDL = require("SDL")
local GUI控件 = require("GUI.控件")





function GUI控件:_基本事件(msg)
  if not msg.鼠标 then
      return
  end
  if self:发送消息('鼠标消息') ~= false then
      for _, v in ipairs(msg.鼠标) do
          if v.type == SDL.MOUSE_DOWN then
              if v.button == SDL.BUTTON_LEFT or v.button == SDL.BUTTON_RIGHT then
                  if self:检查透明(v.x, v.y) then
                      v.typed, v.type = v.type, nil
                      v.control = self
                      if __主显 and __主显.主角 then
                          __主显.主角.按下=false
                          __主显.主角.点击移动=nil
                      end
                      if not self.是否禁止 then
                          if v.button == SDL.BUTTON_LEFT then
                              self:发送消息('左键按下',v.x, v.y, msg)
                              self._LEFTDOWN = true
                          elseif v.button == SDL.BUTTON_RIGHT then
                              self:发送消息('右键按下', v.x, v.y, msg)
                              self._RIGHTDOWN = true
                          end
                      end
                  end
              end
          elseif v.type == SDL.MOUSE_UP then
              if v.button == SDL.BUTTON_LEFT then
                  if self._LEFTDOWN then
                      self._LEFTDOWN = false
                      self:发送消息('左键弹起', v.x, v.y, msg)
                      if self:检查透明(v.x, v.y) then
                          v.typed, v.type = v.type, nil
                          v.control = self
                          self:发送消息('左键单击', v.x, v.y, msg)
                          if v.clicks == 2 then
                              self:发送消息('左键双击',v.x, v.y, msg)
                          end
                      end
                  end
              elseif v.button == SDL.BUTTON_RIGHT then
                  if self._RIGHTDOWN then
                      self._RIGHTDOWN = false
                      self:发送消息('右键弹起',v.x, v.y, msg)
                      if self:检查透明(v.x, v.y) then
                          v.typed, v.type = v.type, nil
                          v.control = self
                          self:发送消息('右键单击',v.x, v.y, msg)
                          if v.clicks == 2 then
                              self:发送消息('右键双击',v.x, v.y, msg)
                          end
                      end
                  end
              end
          elseif v.type == SDL.MOUSE_MOTION then
              if gge.platform == 'Windows' then
                  if v.state == 0 then --非按下
                      if self:检查点(v.x, v.y) then
                          self.鼠标焦点 = true
                          local x, y = self:取坐标()
                          self:发送消息('获得鼠标', v.x, v.y, msg)
                      elseif self.鼠标焦点 then
                          self.鼠标焦点 = false
                          self:发送消息('失去鼠标',v.x, v.y, msg)
                      end
                  elseif self._LEFTDOWN then
                      v.typed, v.type = v.type, nil
                      if v.state & SDL.BUTTON_LMASK == SDL.BUTTON_LMASK then
                          local x, y = self:取坐标()
                          self:发送消息('左键按住', x, y, v.x, v.y, msg)
                      end
                  elseif self._RIGHTDOWN then
                      v.typed, v.type = v.type, nil
                      if v.state & SDL.BUTTON_LMASK == SDL.BUTTON_RMASK then
                          local x, y = self:取坐标()
                          self:发送消息('右键按住', x, y, v.x, v.y, msg)
                      end
                  end
              end
          end
      end
  end
end




function GUI控件:取坐标减宽()
    local xx, yy = self.取坐标(self)
    local ww, hh = self.取宽高(self)
    return xx - ww, yy
end

function GUI控件:取坐标加宽()
    local xx, yy = self.取坐标(self)
    local ww, hh = self.取宽高(self)
    return xx + ww, yy
end




-- function GUI控件:创建我的编辑(name, x, y, w, h, ms, zs, colour)
--     local 输入 = self:创建编辑(name, x, y, w, h)
--     function 输入:初始化()
--         self:取光标精灵()
--         self:置限制字数(zs)
--         self:置颜色(__取颜色(colour))
--         if fnt then
--             self:置文字(fnt)
--         end
--     end

--     return 输入
-- end




function GUI控件:创建文本输入(name, x, y, w, h)
    -- local 输入 = self:创建输入(name, x, y, w, h)
    -- function 输入:初始化()
    --     self.取光标精灵(self)
    --     self:置颜色(255, 255, 255, 255)
    -- end
    local 输入 = self:创建输入(name, x, y, w, h)
    输入:置颜色(0, 0, 0, 255)
   -- 输入:置限制字数(12)
    输入:置文字(宋体文本)

    function 输入:获得鼠标()
        __UI界面.鼠标层:输入形状()
    end
    return 输入
end



function GUI控件:创建我的进度(tcp, name, x, y, w, h)
    local 进度 = self:创建进度(name, x, y, w, h)
    function 进度:初始化()
        self:置精灵(tcp:到精灵())
    end

    return 进度
end



function GUI控件:丰富文本(name, x, y, w, h, bq)
    local 文本 = self:创建文本(name, x, y, w, h)
    文本:置文字(文本字体)
    if bq then
        for i = 1, 25 do
            文本:添加精灵(i + 899, _tp.频道[i])
        end
        for i = 1, 190 do
            文本:添加精灵(i, _tp.表情[i])
        end
        for i = 121, 190 do
            文本:添加精灵(i, _tp.靓号[i])
        end
    end
    
    return 文本
end


function GUI控件:创建纹理精灵(fun,lx,w,h)
        if not w then
            w=self.宽度
        end
        if not h then
            h=self.高度
        end
        local sf = require('SDL.图像')(w, h)
        if sf:渲染开始() and fun then
            if type(fun)=="function" then
                  fun()
            elseif type(fun)=="string" or type(fun)=="number" then
                  文本字体:置颜色(255,255,255,255):取图像(fun):显示(0,0)
            else
                fun:显示(0,0)
            end
            sf:渲染结束()
        end 
        if lx then
              if lx ==1 then
                  return sf:到精灵()
              else
                  return sf
              end
        else
            self:置精灵(sf:到精灵())
        end
end


function GUI控件:创建按钮精灵(tcp,ax,t,w,h,zt,ys,jc)
    local tpc=tcp
    local ww,hh=w,h
    if  not ww or not hh then
        ww,hh = tcp.宽度,tcp.高度
    end
    if ggetype(tcp)~="SDL图像" then
        tpc=tcp:取图像(1):拉伸(ww,hh)
    else
        tpc=tcp:复制():拉伸(ww,hh)
    end
    if self.宽度~=ww or self.高度~=hh then
        self:置宽高(ww,hh)
    end
    local tpc1,tpc2
    if tcp.frame and tcp.frame==2 then
          tpc1=tcp:取图像(2):拉伸(ww,hh)
    elseif tcp.frame and tcp.frame>=3 then
          tpc1=tcp:取图像(2):拉伸(ww,hh)
          tpc2=tcp:取图像(3):拉伸(ww,hh)
    end

    
    if t then
          local zzt = 文本字体
          if zt then
              zzt=zt
          end
          local R,G,B,A= 255,255,255,255
          if ys then
              if type(ys)=="string" then
                R,G,B,A=__取颜色(ys)
              elseif type(ys)=="table" then
                  R,G,B,A=ys[1],ys[2],ys[3],ys[4]
              end
          end
          zzt:置颜色(R,G,B,A)
          if tpc and tpc:渲染开始() then
              if type(t)=="string" or type(t)=="number" then
                    local zts = zzt:取图像(t)
                    if jc then
                        zts = zzt:取投影图像(t,R,G,B,A)
                    end
                    zts:显示((ww - zts.宽度) // 2, (hh - zts.高度) // 2)
              elseif ggetype(t)=="SDL图像" then 
                    t:显示((ww - t.宽度) // 2, (hh - t.高度) // 2)
              elseif ggetype(t)=="tpc" then 
                    t:取图像(1):显示((ww - t.宽度) // 2, (hh - t.高度) // 2)
              end
              tpc:渲染结束()
          end
          if tpc1 and tpc1:渲染开始() then
              if type(t)=="string"  then
                  local zts = zzt:取图像(t)
                  if jc then
                      zts = zzt:取投影图像(t,R,G,B,A)
                  end
                  zts:显示((ww - zts.宽度) // 2, (hh - zts.高度) // 2)
              elseif type(t)=="number" then
                    local zts = zzt:取图像(t)
                    if jc then
                        zts = zzt:取投影图像(t,R,G,B,A)
                    end
                    zts:显示((ww - zts.宽度) // 2, (hh - zts.高度) // 2)
              elseif ggetype(t)=="SDL图像" then 
                    t:显示((ww - t.宽度) // 2, (hh - t.高度) // 2)
              elseif ggetype(t)=="tpc" then 
                    t:取图像(1):显示((ww - t.宽度) // 2, (hh - t.高度) // 2)
              end
              tpc1:渲染结束()
          end
          if tpc2 and tpc2:渲染开始() then
              if type(t)=="string"  then
                  local zts = zzt:取图像(t)
                  if jc then
                      zts = zzt:取投影图像(t,R,G,B,A)
                  end
                  zts:显示((ww - zts.宽度) // 2, (hh - zts.高度) // 2)
              elseif type(t)=="number" then
                    local zts = zzt:取图像(t)
                    if jc then
                        zts = zzt:取投影图像(t,R,G,B,A)
                    end
                    zts:显示((ww - zts.宽度) // 2, (hh - zts.高度) // 2)
              elseif ggetype(t)=="SDL图像" then 
                    t:显示((ww - t.宽度) // 2, (hh - t.高度) // 2)
              elseif ggetype(t)=="tpc" then 
                    t:取图像(1):显示((ww - t.宽度) // 2, (hh - t.高度) // 2)
              end
              tpc2:渲染结束()
          end
    end
   
   if tpc1 and not tpc2 then
            self:置正常精灵(tpc:到精灵():置中心(0,0))
            if ax then
                self:置按下精灵(tpc1:到精灵():置中心(-1, -1))
            else
                self:置按下精灵(tpc1:到精灵():置中心(0, 0))
            end
            if not __手机 then
                self:置经过精灵(tpc:到精灵():置高亮(true):置中心(0,0))
            end
            self:置禁止精灵(tpc:复制():到灰度():到精灵():置中心(0,0))
            if self.置选中正常精灵 then
                self:置选中正常精灵(tpc1:到精灵():置中心(0,0))
            end
    elseif tpc2 then
            self:置正常精灵(tpc:到精灵():置中心(0, 0))
            if ax then
                self:置按下精灵(tpc1:到精灵():置中心(-1, -1))
            else
                self:置按下精灵(tpc1:到精灵():置中心(0, 0))
            end
            if not __手机 then
                self:置经过精灵(tpc2:到精灵():置中心(0, 0))
            end
            self:置禁止精灵(tpc:复制():到灰度():到精灵():置中心(0, 0))
            if self.置选中正常精灵 then
                self:置选中正常精灵(tpc1:到精灵():置中心(0, 0))
            end
    else
        self:置正常精灵(tpc:到精灵():置中心(0, 0))
        if ax then
            self:置按下精灵(tpc:到精灵():置中心(-1, -1))
        else
            self:置按下精灵(tpc:到精灵():置中心(0, 0))
        end
        if not __手机 then
            self:置经过精灵(tpc:到精灵():置高亮(true):置中心(0,0))
        end
        self:置禁止精灵(tpc:复制():到灰度():到精灵():置中心(0, 0))
        if self.置选中正常精灵 then
            self:置选中正常精灵(tpc:到精灵():置中心(0, 0))
        end
    end
    return ww,hh
  end







function GUI控件:创建圆形选中精灵(ax)
      local tcp=__res:取资源动画("jszy/fwtb",0x10000077)
      local ww,hh=tcp.宽度,tcp.高度
      if self.宽度~=ww or self.高度~=hh then
          self:置宽高(ww,hh)
      end
      self:置正常精灵(tcp:取精灵(1):置中心(0, 0))
      if ax then
        self:置按下精灵(tcp:取精灵(1):置中心(-1, -1))
      else
        self:置按下精灵(tcp:取精灵(1):置中心(0, 0))
      end
      if not __手机 then
          self:置经过精灵(tcp:取精灵(3):置中心(0, 0))
      end
      self:置禁止精灵(tcp:取灰度精灵(1):置中心(0, 0))
      if self.置选中正常精灵 then
          self:置选中正常精灵(tcp:取精灵(5):置中心(0, 0))
      end
      return ww,hh
end

function GUI控件:创建关闭按钮(name, x, y,w,h)
    if not w or not h then
        w,h=16,16
    end
    local 按钮 = self:创建按钮(name,x, y)
    function 按钮:初始化()
    end 
    function 按钮:置大小(ww,hh)
        self:创建按钮精灵(__res:取资源动画("dlzy",0xAADDCC15),1,nil,ww,hh)
        self:置状态(1)
    end
    按钮:置大小(w,h)
    return 按钮 


end




function GUI控件:创建红色单选按钮(txt, name, x, y,w,h,ztt,yss)
    if not w or not h then
        w,h=20,20
    end
    local 按钮 = self:创建单选按钮(name,x, y)
    function 按钮:初始化()
    end  
    function 按钮:置文字(ww,hh,t,zt,ss)
            self:置宽高(ww,hh)
            local tpc = __res:取资源动画("jszy/jmxf",0x00000407)
            self:置正常精灵(取九宫图像(tpc:取图像(1),ww,hh,8,nil,t,zt,ss))
            self:置按下精灵(取九宫图像(tpc:取图像(2),ww,hh,8,nil,t,zt,ss))
            if not __手机 then
                self:置经过精灵(取九宫图像(tpc:取图像(3),ww,hh,8,nil,t,zt,ss))
            end
            self:置选中正常精灵(取九宫图像(tpc:取图像(2),ww,hh,8,nil,t,zt,ss))
    end
    
    按钮:置文字(w,h,txt,ztt,yss)
    return 按钮
end



function GUI控件:创建蓝色单选按钮(txt, name, x, y,w,h,ztt,yss)
    if not w or not h then
        w,h=20,20
    end
    local 按钮 = self:创建单选按钮(name,x, y)
    function 按钮:初始化()
    end  
    function 按钮:置文字(ww,hh,t,zt,ss)
            self:置宽高(ww,hh)
            local tpc = __res:取资源动画("jszy/dd",0x00000071)
            self:置正常精灵(取九宫图像(tpc:取图像(1),ww,hh,8,nil,t,zt,ss))
            self:置按下精灵(取九宫图像(tpc:取图像(2),ww,hh,8,nil,t,zt,ss))
            if not __手机 then
              self:置经过精灵(取九宫图像(tpc:取图像(3),ww,hh,8,nil,t,zt,ss))
            end
            self:置选中正常精灵(取九宫图像(tpc:取图像(2),ww,hh,8,nil,t,zt,ss))
    end

  
    按钮:置文字(w,h,txt,ztt,yss)
    return 按钮
end






function GUI控件:创建蓝色按钮(txt, name, x, y,w,h,ztt,yss)
    if not w or not h then
        w,h=20,20
    end
    local 按钮 = self:创建按钮(name,x, y)
    function 按钮:初始化()
    end  
 
    function 按钮:置文字(ww,hh,t,zt,ss)
            self:置宽高(ww,hh)
            local tpc = __res:取资源动画("jszy/dd",0x00000071)
            self:置正常精灵(取九宫图像(tpc:取图像(1),ww,hh,8,nil,t,zt,ss))
            self:置按下精灵(取九宫图像(tpc:取图像(2),ww,hh,8,nil,t,zt,ss))
            if not __手机 then
                self:置经过精灵(取九宫图像(tpc:取图像(3),ww,hh,8,nil,t,zt,ss))
            end
            self:置禁止精灵(取九宫图像(tpc:取图像(1):到灰度(),ww,hh,8,nil,t,zt,ss))
    end
    按钮:置文字(w,h,txt,ztt,yss)
    return 按钮
end

function GUI控件:创建红色按钮(txt, name, x, y,w,h,ztt,yss)
    if not w or not h then
        w,h=20,20
    end
    local 按钮 = self:创建按钮(name,x, y)
    function 按钮:初始化()
    end  

    function 按钮:置文字(ww,hh,t,zt,ss)
            self:置宽高(ww,hh)
            local tpc = __res:取资源动画("jszy/jmxf",0x00000407)
            self:置正常精灵(取九宫图像(tpc:取图像(1),ww,hh,8,nil,t,zt,ss))
            self:置按下精灵(取九宫图像(tpc:取图像(2),ww,hh,8,nil,t,zt,ss))
            if not __手机 then
                self:置经过精灵(取九宫图像(tpc:取图像(3),ww,hh,8,nil,t,zt,ss))
            end
            self:置禁止精灵(取九宫图像(tpc:取图像(1):到灰度(),ww,hh,8,nil,t,zt,ss))
        
    end

    按钮:置文字(w,h,txt,ztt,yss)
    return 按钮
end


function GUI控件:创建商店网格(name, x, y)
    local 显示控件= self:创建控件(name, x, y, 262,282)
    显示控件.物品={}
    显示控件.背景 = __res:取资源动画("dlzy",0xE9C090A3,"精灵")     
    显示控件.物品焦点 = __res:取资源动画("dlzy",0x6F88F494,"精灵")
    显示控件.物品选中 =__res:取资源动画("dlzy",0x10921CA7,"精灵") 
    显示控件.花纹 = __res:取资源动画("jszy/fwtb",0xabcd0203,"精灵"):置区域(0,0,248,13)
    function 显示控件:显示(xx,yy)
        if self.背景 then
            self.背景:显示(xx+2,yy)
        end
        if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.物品 and self.物品[self.焦点编号]  and self.物品[self.焦点编号].编号 then
            self.物品焦点:显示(xx+3+self.道具网格.子控件[self.焦点编号].x,yy+1+self.道具网格.子控件[self.焦点编号].y)
        else
            self.焦点编号=nil
        end
        if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.物品 and self.物品[self.选中编号]  and self.物品[self.选中编号].编号 then
            self.物品选中:显示(xx+3+self.道具网格.子控件[self.选中编号].x,yy+1+self.道具网格.子控件[self.选中编号].y)
        else
            self.选中编号=nil
        end
        if self.花纹 then
            self.花纹:显示(xx+6,yy+265)
        end

    end
    function 显示控件:选中()
        if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.物品 and self.物品[self.选中编号]  and self.物品[self.选中编号].编号 then
            return  self.物品[self.选中编号].编号
        else
            return 0
        end
    end
    function 显示控件:焦点()
        if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.物品 and self.物品[self.焦点编号]  and self.物品[self.焦点编号].编号 then
            return  self.物品[self.焦点编号].编号
        else
            return 0
        end
    end

    function 显示控件:选中物品()
        if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.道具网格.子控件[self.选中编号]._spr and self.道具网格.子控件[self.选中编号]._spr.物品 then
            return  self.道具网格.子控件[self.选中编号]._spr
        else
            return nil
        end
    end
    function 显示控件:焦点物品()
        if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.道具网格.子控件[self.焦点编号]._spr and self.道具网格.子控件[self.焦点编号]._spr.物品 then
            return  self.道具网格.子控件[self.焦点编号]._spr
        else
            return nil
        end
    end

    local 道具网格 = 显示控件:创建网格("道具网格", 0, 0, 255, 255)
    道具网格:创建格子(51, 51, 0, 0, 5, 5)
    function 道具网格:置物品(数据)
        for i = 1, #self.子控件 do
            if 数据 and 数据[i] and 数据[i].小动画  then
                self.子控件[i]:置精灵(数据[i].小动画)   
            else
                self.子控件[i]:置精灵()
            end
        end
    end
    function 道具网格:获得鼠标(xx,yy,i)
            显示控件.焦点编号=nil
            if self.子控件[i] and self.子控件[i]._spr and self.子控件[i]._spr.物品 then
                显示控件.焦点编号=i
            end
            if not __手机 then
              显示控件:发送消息("获得鼠标",xx,yy,i) 
            end
    end


    function 道具网格:左键弹起(xx,yy,i)
               
            显示控件.选中编号=nil
            if self.子控件[i] and self.子控件[i]._spr and self.子控件[i]._spr.物品 then
                显示控件.选中编号 = i
            end
            显示控件:发送消息("左键弹起",xx,yy,i)
    end
    function 道具网格:失去鼠标(xx,yy)
            显示控件.焦点编号=nil
            显示控件:发送消息("失去鼠标",xx,yy) 
    end

    function 道具网格:左键按下(xx,yy,i)
        显示控件:发送消息("左键按下",xx,yy,i)
    end
    function 道具网格:右键弹起(xx,yy,i)
         显示控件:发送消息("右键弹起",xx,yy,i)
    end
    function 道具网格:右键按下(xx,yy,i)
         显示控件:发送消息("右键按下",xx,yy,i)
    end
    function 道具网格:键盘按下(键码, 功能)
        显示控件:发送消息("键盘按下",键码, 功能)
    end
    function 道具网格:键盘弹起(键码, 功能)
        显示控件:发送消息("键盘弹起",键码, 功能)
    end

    function 显示控件:置数据(数据)
            self.物品={}
            self.选中编号=nil
            self.焦点编号=nil
            self:置物品(数据)
    end

    function 显示控件:置物品(数据)
            if not self.物品 then self.物品={} end
            for i = 1, 25 do
                self.物品[i]=nil
                if 数据 and 数据[i] then
                    self.物品[i]={}
                    local lssj = __物品格子:创建()
                    if 数据[i].附加显示 then
                        lssj:置物品(数据[i],50,50,"数量",nil,true,数据[i].附加显示)
                    else
                        lssj:置物品(数据[i],50,50,"数量",nil,true)
                    end
                    if 数据[i].灰度 then
                        lssj:置灰色(true)
                    end
                    self.物品[i].小动画=lssj
                    self.物品[i].编号=i
                end
            end
            道具网格:置物品(self.物品)
    end
    function 显示控件:置禁止(总类,名称,编号)
        if 编号 then
            if type(编号)=="table" then
                for k, v in pairs(编号) do
                    if self.物品[v] and self.物品[v].小动画 and self.物品[v].小动画.物品 then
                            self.物品[v].小动画:置禁止(总类,名称)
                    end
                end
            elseif type(编号)=="number" then
                    if self.物品[编号] and self.物品[编号].小动画 and self.物品[编号].小动画.物品 then
                        self.物品[编号].小动画:置禁止(总类,名称)
                    end
            end
        else
            for i = 1, 25 do
                if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画.物品 then
                    self.物品[i].小动画:置禁止(总类,名称)
                end
            end
        end
    end
    function 显示控件:置灰色(fx,编号)
        if 编号 then
            if type(编号)=="table" then
                for k, v in pairs(编号) do
                    if self.物品[v] and self.物品[v].小动画 and self.物品[v].小动画.物品 then
                            self.物品[v].小动画:置灰色(fx)
                    end
                end
            elseif type(编号)=="number" then
                    if self.物品[编号] and self.物品[编号].小动画 and self.物品[编号].小动画.物品 then
                        self.物品[编号].小动画:置灰色(fx)
                    end
            end
        else
            for i = 1, 25 do
                if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画.物品 then
                    self.物品[i].小动画:置灰色(fx)
                end
            end
        end
    end

return 显示控件
end



function GUI控件:创建道具网格(name, x, y)
        local 显示控件= self:创建控件(name, x, y, 262,210)
        显示控件.物品={}
        显示控件.背景 = __res:取资源动画("dlzy",0xF70725E9,"精灵")
        显示控件.物品焦点 = __res:取资源动画("dlzy",0x6F88F494,"精灵")
        显示控件.物品选中 =__res:取资源动画("dlzy",0x10921CA7,"精灵") 
 
        function 显示控件:显示(xx,yy)
            if self.背景 then
                self.背景:显示(xx+2,yy)
            end
            if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.物品 and self.物品[self.焦点编号]  and self.物品[self.焦点编号].编号 then
                self.物品焦点:显示(xx+3+self.道具网格.子控件[self.焦点编号].x,yy+1+self.道具网格.子控件[self.焦点编号].y)
            else
                self.焦点编号=nil
            end
            if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.物品 and self.物品[self.选中编号]  and self.物品[self.选中编号].编号 then
                self.物品选中:显示(xx+3+self.道具网格.子控件[self.选中编号].x,yy+1+self.道具网格.子控件[self.选中编号].y)
            else
                self.选中编号=nil
            end
        end
        function 显示控件:选中()
            if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.物品 and self.物品[self.选中编号]  and self.物品[self.选中编号].编号 then
                return  self.物品[self.选中编号].编号
            else
                return 0
            end
        end
        function 显示控件:焦点()
            if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.物品 and self.物品[self.焦点编号]  and self.物品[self.焦点编号].编号 then
                return  self.物品[self.焦点编号].编号
            else
                return 0
            end
        end

        function 显示控件:选中物品()
            if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.道具网格.子控件[self.选中编号]._spr and self.道具网格.子控件[self.选中编号]._spr.物品 then
                return  self.道具网格.子控件[self.选中编号]._spr
            else
                return nil
            end
        end
        function 显示控件:焦点物品()
            if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.道具网格.子控件[self.焦点编号]._spr and self.道具网格.子控件[self.焦点编号]._spr.物品 then
                return  self.道具网格.子控件[self.焦点编号]._spr
            else
                return nil
            end
        end
    
        local 道具网格 = 显示控件:创建网格("道具网格", 0, 0, 255, 205)
        道具网格:创建格子(51, 51, 0, 0, 4, 5)
        function 道具网格:置物品(数据)
            for i = 1, #self.子控件 do
                if 数据 and 数据[i] and 数据[i].小动画  then
                    self.子控件[i]:置精灵(数据[i].小动画)   
                else
                    self.子控件[i]:置精灵()
                end
            end
        end
        function 道具网格:获得鼠标(xx,yy,i)
                显示控件.焦点编号=nil
                if self.子控件[i] and self.子控件[i]._spr and self.子控件[i]._spr.物品 then
                    显示控件.焦点编号=i
                end
                if not __手机 then
                  显示控件:发送消息("获得鼠标",xx,yy,i) 
                end
        end
    

        function 道具网格:左键弹起(xx,yy,i)
                   
                显示控件.选中编号=nil
                if self.子控件[i] and self.子控件[i]._spr and self.子控件[i]._spr.物品 then
                    显示控件.选中编号 = i
                end
                显示控件:发送消息("左键弹起",xx,yy,i)
        end
        function 道具网格:失去鼠标(xx,yy)
                显示控件.焦点编号=nil
                显示控件:发送消息("失去鼠标",xx,yy) 
        end
 
        function 道具网格:左键按下(xx,yy,i)
            显示控件:发送消息("左键按下",xx,yy,i)
        end
        function 道具网格:右键弹起(xx,yy,i)
             显示控件:发送消息("右键弹起",xx,yy,i)
        end
        function 道具网格:右键按下(xx,yy,i)
             显示控件:发送消息("右键按下",xx,yy,i)
        end
        function 道具网格:键盘按下(键码, 功能)
            显示控件:发送消息("键盘按下",键码, 功能)
        end
        function 道具网格:键盘弹起(键码, 功能)
            显示控件:发送消息("键盘弹起",键码, 功能)
        end

        function 显示控件:置数据(数据)
                self.物品={}
                self.选中编号=nil
                self.焦点编号=nil
                self:置物品(数据)
        end

        function 显示控件:置物品(数据)
                if not self.物品 then self.物品={} end
                for i = 1, 20 do
                    self.物品[i]=nil
                    if 数据 and 数据[i] then
                        self.物品[i]={}
                        local lssj = __物品格子:创建()
                        if 数据[i].附加显示 then
                            lssj:置物品(数据[i],50,50,"数量",nil,true,数据[i].附加显示)
                        else
                            lssj:置物品(数据[i],50,50,"数量",nil,true)
                        end
                        if 数据[i].灰度 then
                            lssj:置灰色(true)
                        end
                        self.物品[i].小动画=lssj
                        self.物品[i].编号=i
                    end
                end
                道具网格:置物品(self.物品)
        end
        function 显示控件:置禁止(总类,名称,编号)
            if 编号 then
                if type(编号)=="table" then
                    for k, v in pairs(编号) do
                        if self.物品[v] and self.物品[v].小动画 and self.物品[v].小动画.物品 then
                                self.物品[v].小动画:置禁止(总类,名称)
                        end
                    end
                elseif type(编号)=="number" then
                        if self.物品[编号] and self.物品[编号].小动画 and self.物品[编号].小动画.物品 then
                            self.物品[编号].小动画:置禁止(总类,名称)
                        end
                end
            else
                for i = 1, 20 do
                    if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画.物品 then
                        self.物品[i].小动画:置禁止(总类,名称)
                    end
                end
            end
        end
        function 显示控件:置灰色(fx,编号)
            if 编号 then
                if type(编号)=="table" then
                    for k, v in pairs(编号) do
                        if self.物品[v] and self.物品[v].小动画 and self.物品[v].小动画.物品 then
                                self.物品[v].小动画:置灰色(fx)
                        end
                    end
                elseif type(编号)=="number" then
                        if self.物品[编号] and self.物品[编号].小动画 and self.物品[编号].小动画.物品 then
                            self.物品[编号].小动画:置灰色(fx)
                        end
                end
            else
                for i = 1, 20 do
                    if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画.物品 then
                        self.物品[i].小动画:置灰色(fx)
                    end
                end
            end
        end

   return 显示控件
end

function GUI控件:创建背包网格(name, x, y)
    local 显示控件= self:创建控件(name, x, y, 262,240)
    显示控件.起点=0
    显示控件.物品={}
    显示控件.背景 = __res:取资源动画("dlzy",0xF70725E9,"精灵")
    显示控件.物品焦点 =__res:取资源动画("dlzy",0x6F88F494,"精灵") 
    显示控件.物品选中 =__res:取资源动画("dlzy",0x10921CA7,"精灵")

    function 显示控件:显示(xx,yy)
        if self.背景 then
            self.背景:显示(xx+2,yy)
        end
        if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.物品 and self.物品[self.焦点编号+self.起点]  and self.物品[self.焦点编号+self.起点].编号 then
            self.物品焦点:显示(xx+3+self.道具网格.子控件[self.焦点编号].x,yy+1+self.道具网格.子控件[self.焦点编号].y)
        else
            self.焦点编号=nil
        end
        if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.物品 and self.物品[self.选中编号+self.起点]  and self.物品[self.选中编号+self.起点].编号 then
            self.物品选中:显示(xx+3+self.道具网格.子控件[self.选中编号].x,yy+1+self.道具网格.子控件[self.选中编号].y)
        else
            self.选中编号=nil
        end
    end
    function 显示控件:选中()
        if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.物品 and self.物品[self.选中编号+self.起点]  and self.物品[self.选中编号+self.起点].编号 then
            return  self.物品[self.选中编号+self.起点].编号
        else
            return 0
        end
    end
    function 显示控件:焦点()
        if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.物品 and self.物品[self.焦点编号+self.起点]  and self.物品[self.焦点编号+self.起点].编号 then
            return  self.物品[self.焦点编号+self.起点].编号
        else
            return 0
        end
    end

    function 显示控件:选中物品()
        if self.选中编号 and  self.道具网格.子控件[self.选中编号] and self.道具网格.子控件[self.选中编号]._spr and self.道具网格.子控件[self.选中编号]._spr.物品 then
            return  self.道具网格.子控件[self.选中编号]._spr
        else
            return nil
        end
    end
    function 显示控件:焦点物品()
        if self.焦点编号 and  self.道具网格.子控件[self.焦点编号] and self.道具网格.子控件[self.焦点编号]._spr and self.道具网格.子控件[self.焦点编号]._spr.物品 then
            return  self.道具网格.子控件[self.焦点编号]._spr
        else
            return nil
        end
    end

    local 道具网格 = 显示控件:创建网格("道具网格", 0, 0, 255, 205)
    道具网格:创建格子(51, 51, 0, 0, 4, 5)
    function 道具网格:置物品(数据)
        for i = 1, #self.子控件 do
            if 数据 and 数据[i+显示控件.起点] and 数据[i+显示控件.起点].小动画  then
                self.子控件[i]:置精灵(数据[i+显示控件.起点].小动画)   
            else
                self.子控件[i]:置精灵()
            end
        end
    end
    function 道具网格:获得鼠标(xx,yy,i)
            显示控件.焦点编号=nil
            if self.子控件[i] and self.子控件[i]._spr and self.子控件[i]._spr.物品 then
                显示控件.焦点编号=i
            end
            if not __手机 then
                显示控件:发送消息("获得鼠标",xx,yy,i+显示控件.起点) 
            end
            
    end
  

    function 道具网格:左键弹起(xx,yy,i)
            显示控件.选中编号=nil
            if self.子控件[i] and self.子控件[i]._spr and self.子控件[i]._spr.物品 then
                显示控件.选中编号 = i
            end
            显示控件:发送消息("左键弹起",xx,yy,i+显示控件.起点)
    end
    function 道具网格:失去鼠标(xx,yy)
            显示控件.焦点编号=nil
            显示控件:发送消息("失去鼠标",xx,yy) 
    end

    function 道具网格:左键按下(xx,yy,i)
        显示控件:发送消息("左键按下",xx,yy,i+显示控件.起点)
    end
    function 道具网格:右键弹起(xx,yy,i)
         显示控件:发送消息("右键弹起",xx,yy,i+显示控件.起点)
    end
    function 道具网格:右键按下(xx,yy,i)
         显示控件:发送消息("右键按下",xx,yy,i+显示控件.起点)
    end
    function 道具网格:键盘按下(键码, 功能)
        显示控件:发送消息("键盘按下",键码, 功能)
    end
    function 道具网格:键盘弹起(键码, 功能)
        显示控件:发送消息("键盘弹起",键码, 功能)
    end

    function 显示控件:置数据(数据)
            self.起点=0
            self.物品={}
            self.选中编号=nil
            self.焦点编号=nil
            self:置物品(数据)
            self.壹:置选中(true)
    end

    function 显示控件:置物品(数据)
            if not self.物品 then self.物品={} end
            for i = 1, 100 do
                self.物品[i]=nil
                if 数据 and 数据[i] then
                    self.物品[i]={}
                    local lssj = __物品格子:创建()
                    if 数据[i].附加显示 then
                        lssj:置物品(数据[i],50,50,"数量",nil,true,数据[i].附加显示)
                    else
                        lssj:置物品(数据[i],50,50,"数量",nil,true)
                    end
                    if 数据[i].灰度 then
                      lssj:置灰色(true)
                    end
                    self.物品[i].小动画=lssj
                    self.物品[i].编号=i
                end
            end
            道具网格:置物品(self.物品)
    end

    function 显示控件:置禁止(总类,名称,编号)
        if 编号 then
            if type(编号)=="table" then
                for k, v in pairs(编号) do
                    if self.物品[v] and self.物品[v].小动画 and self.物品[v].小动画.物品 then
                            self.物品[v].小动画:置禁止(总类,名称)
                    end
                end
            elseif type(编号)=="number" then
                    if self.物品[编号] and self.物品[编号].小动画 and self.物品[编号].小动画.物品 then
                        self.物品[编号].小动画:置禁止(总类,名称)
                    end
            end
        else
            for i = 1, 100 do
                if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画.物品 then
                    self.物品[i].小动画:置禁止(总类,名称)
                end
            end
        end
    end

    function 显示控件:置灰色(fx,编号)
        if 编号 then
            if type(编号)=="table" then
                for k, v in pairs(编号) do
                    if self.物品[v] and self.物品[v].小动画 and self.物品[v].小动画.物品 then
                            self.物品[v].小动画:置灰色(fx)
                    end
                end
            elseif type(编号)=="number" then
                    if self.物品[编号] and self.物品[编号].小动画 and self.物品[编号].小动画.物品 then
                        self.物品[编号].小动画:置灰色(fx)
                    end
            end
        else
            for i = 1, 100 do
                if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画.物品 then
                    self.物品[i].小动画:置灰色(fx)
                end
            end
        end
    end


    




    local 格子设置={"壹","贰","叁","肆", "伍"}

    for i, v in ipairs(格子设置) do
        local 临时函数 = 显示控件:创建蓝色单选按钮(v,v,8+(i-1)*50,215,42,25,说明字体)
        function 临时函数:左键弹起(xx,yy)
                if v == "壹" then
                    显示控件.起点 = 0
                elseif v== "贰" then
                    显示控件.起点 = 20
                elseif v == "叁" then
                    显示控件.起点 = 40
                elseif v == "肆" then
                    显示控件.起点 = 60 
                elseif v == "伍" then
                    显示控件.起点 = 80 
                end
                显示控件.焦点编号=nil
                显示控件.选中编号=nil
                显示控件.道具网格:置物品(显示控件.物品)
                显示控件:发送消息("背包左键弹起",xx,yy,i)
            end
            function 临时函数:右键弹起(xx,yy)
                显示控件:发送消息("背包右键弹起",xx,yy,i)
            end
            function 临时函数:右键按下(xx,yy)
                显示控件:发送消息("背包右键按下",xx,yy,i)
            end
            function 临时函数:左键按下(xx,yy)
                显示控件:发送消息("背包左键按下",xx,yy,i)
            end

    end





return 显示控件
end






function GUI控件:创建横向滑块(name,x,y,w,h,zj,w1,h1)
        local 滑块控件=self:创建控件(name,x,y,w,h)
        local x1=x
        local w2=w
        if zj then
            x1=x+h
            w2=w-h*2
        end
        local 滑块=self:创建滑块(name.."滑块",x1,y,w2,h)
        滑块控件.滑块=滑块
        function 滑块:初始化()
            self:置精灵( __res:取资源动画("jszy/fwtb",0x00000438,"图像"):拉伸(w2,h):到精灵())
        end
        function 滑块:滚动事件(xx, yy,a)
            滑块控件:发送消息('滚动事件',xx, yy,a)
        end
        local 按钮= 滑块:创建滑块按钮(name.."按钮",0,1)
        function 按钮:初始化()
            self:创建按钮精灵(__res:取资源动画('dlzy',0x8D4BBC26),nil,nil,w1,h1)
        end
        function 按钮:左键弹起(xx, yy)

            滑块控件:发送消息("左键弹起",xx,yy)
        end

        function 滑块控件:置位置(a)
                滑块:置位置(a)
        end

        function 滑块:更新(dt)
            if self.是否可见 and not 滑块控件.是否可见  then
                        self:置可见(false)
            end
        end
        function 滑块控件:更新(dt)
                if self.是否可见 and not 滑块.是否可见  then
                    滑块:置可见(true)
                end
        end
        if zj then
            local 减少按钮 = 滑块控件:创建按钮(name..'减少',0,0,h,h)
            function 减少按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000063),1,nil,h,h)
            end
            function 减少按钮:左键弹起(xx, yy)
                滑块:置位置(滑块.位置 - 10)
                滑块控件:发送消息('滚动事件', xx, yy, 滑块.位置)
            end
            local 增加按钮 = 滑块控件:创建按钮(name..'增加',-h,0,h,h)
            function 增加按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000064),1,nil,h,h)
            end
            function 增加按钮:左键弹起(xx, yy)
                滑块:置位置(滑块.位置 + 10)
                滑块控件:发送消息('滚动事件',xx, yy, 滑块.位置)
            end
        end
        return 滑块控件
end


function GUI控件:创建竖向滑块(name,x,y,w,h,zj,w1,h1)  
        local 滑块控件=self:创建控件(name,x,y,w,h)
        local y1=y
        local h2=h
        if zj then
            y1=y+w
            h2=h-w*2
        end
        local 滑块=self:创建滑块(name.."滑块",x,y1,w,h2)
        滑块控件.滑块=滑块
        function 滑块:初始化()
            self:置精灵( __res:取资源动画('jszy/other1',0x00000019,"图像"):拉伸(w,h2):到精灵())
        end
        function 滑块:滚动事件(xx, yy,a)
            滑块控件:发送消息('滚动事件',xx, yy,a)
        end
        local 按钮= 滑块:创建滑块按钮(name.."按钮",1,0)
        function 按钮:初始化()
                self:创建按钮精灵(__res:取资源动画('jszy/dd',00000007),nil,nil,w1,h1)
        end
        function 按钮:左键弹起(xx, yy)
            滑块控件:发送消息("左键弹起",xx,yy)
        end

        function 滑块控件:置位置(a)
                滑块:置位置(a)
        end

        function 滑块:更新(dt)
            if self.是否可见 and not 滑块控件.是否可见  then
                        self:置可见(false)
            end
        end
        function 滑块控件:更新(dt)
                if self.是否可见 and not 滑块.是否可见  then
                    滑块:置可见(true)
                end
        end

        function 滑块控件:置高度(hh)
              self:置宽高(self.宽度,hh)
              local hh2=hh
              if self.加减按钮 then
                    hh2=hh-self.宽度*2  
              end
              滑块:置宽高(滑块.宽度,hh2)
        end



        if zj then
            local 减少按钮 = 滑块控件:创建按钮(name..'减少',0,0)
            function 减少按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画("dlzy",0x335CECBC),1,nil,w,w)
                    
            end
            function 减少按钮:左键弹起(xx, yy)
                滑块:置位置(滑块.位置 - 10)
                滑块控件:发送消息('滚动事件', xx, yy, 滑块.位置)
            end
            local 增加按钮 = 滑块控件:创建按钮(name..'增加',0,-w,w,w)
            function 增加按钮:初始化()
                    self:创建按钮精灵(__res:取资源动画("dlzy",0xB7F2FF5E),1,nil,w,w)
            end
            function 增加按钮:左键弹起(xx, yy)
                滑块:置位置(滑块.位置 + 10)
                滑块控件:发送消息('滚动事件',xx, yy, 滑块.位置)
            end
        end
        return 滑块控件
end



-- function GUI控件:创建我的滑块(tcp,tpc2,name,x,y,w,h)
--         local 滑块=self:创建滑块(name,x,y,w,h)
--         if tcp then
--             滑块:置精灵(tcp:拉伸(w,h):到精灵())
--         end
--         local 按钮= 滑块:创建滑块按钮("按钮",1,1)
--         if tpc2 then
--             按钮:置精灵(tpc2:到精灵())
--         end
--         function 按钮:左键弹起(xx, yy)
--             滑块:发送消息("左键弹起",xx,yy)
--         end
--         return 滑块
-- end




function GUI控件:创建技能内丹控件(name,x,y)
        local  技能控件=self:创建控件(name, x, y, 220,140)
        技能控件.超级图标=__res:取资源动画("pic","cjjnxtb.png","图片"):到精灵()
        function 技能控件:初始化()
            self.状态="技能"
            self.内丹动画=__res:取资源动画("dlzy",0x3FEEB486,"动画")
            self.进阶图标 ={
                        [1] = __res:取资源动画("dlzy",0x4536A03D,"精灵"),
                        [2] = __res:取资源动画("dlzy",0x714C3706,"精灵"),
                        [3] = __res:取资源动画("dlzy",0xD60014B8,"精灵"),
                        [4] = __res:取资源动画("dlzy",0xF7EBF987,"精灵"),
                        [5] = __res:取资源动画("dlzy",0x11963488,"精灵"),
                        [6] = __res:取资源动画("dlzy",0x9A4F1961,"精灵"),
                        [7] = __res:取资源动画("dlzy",0x1E7ABB94,"精灵"),
                        [8] = __res:取资源动画("dlzy",0xA6C9A76A,"精灵"),
                        [9] = __res:取资源动画("dlzy",0x2982E3F7,"精灵"),
                        [10] = __res:取资源动画("dlzy",0x1D0717D7,"精灵"),
                        [11] =  __res:取资源动画("dlzy",0xC44F0602,"精灵"),
                        [12] = __res:取资源动画("dlzy",0x9765D0B3,"精灵"),
                        [13] =  __res:取资源动画("dlzy",0x36A2C1A6,"精灵"),
                        [14] = __res:取资源动画("dlzy",0xAFC2E161,"精灵"),
                        [15] =  __res:取资源动画("dlzy",0x27E24CFA,"精灵"),
                        [16] = __res:取资源动画("dlzy",0x1094AD16,"精灵")
                    }
            self.技能页数=0
            self.技能图标 ={}
            for i = 1, 12 do
                local lssj =  __技能格子:创建()
                lssj:置数据()
                self.技能图标[i]=lssj
            end
            
        end
       
        function 技能控件:置数据(数据,状态)
                self.数据=数据
                if not 状态 then
                        self.状态="技能"
                elseif type(状态)=="string" and (状态=="技能" or 状态=="内丹"  or 状态=="进阶")  then
                        self.状态=状态
                end
                self:显示设置()
        end
        function 技能控件:显示设置()
                self[self.状态]:置选中(true)
                self.超级赐福={}
                self.内丹图标={}
                self.技能页数=0
                self.特性图标=nil
                self.焦点=nil
                self.选中=nil
                self.特性说明="无特性"
                if self.超级图标 then
                    self.赐福:置可见(true)
                else
                    self.赐福:置可见(false)
                end
                self:创建纹理精灵(function ()
                                  if self.状态=="技能" then
                                      local xx=0
                                      local yy=0
                                      for i = 1, 12 do
                                          __res:取资源动画("jszy/fwtb",0x00000030,"图像"):显示(xx*41,yy*41)
                                          xx=xx+1
                                          if xx>=4 then
                                              xx=0
                                              yy=yy+1
                                          end
                                      end
                                  elseif self.状态=="内丹" then
                                      __res:取资源动画("dlzy",0x7367031D,"图像"):显示(0,0) 
                                  elseif self.状态=="进阶" then
                                      __res:取资源动画("dlzy",0xC361C087,"图像"):显示(0,0) 
                                      __res:取资源动画("dlzy",0x1094AD16,"图像"):显示(40,7)
                                      if self.数据 then
                                          if self.数据.特性 ~="无" then
                                              self.特性图标=文本字体:置颜色(__取颜色("黄色")):取精灵(self.数据.特性)
                                              if self.数据.特性几率~=0 then
                                                  self.特性说明=取特性(self.数据.特性,self.数据.等级,self.数据.特性几率)
                                              end
                                          end
                                          文本字体:置颜色(255,255,255,255):取图像("灵性:"):显示(65,115) 
                                          文本字体:置颜色(255,255,255,255):取描边图像(self.数据.灵性,150,150,150,150):显示(65+文本字体:取宽度("灵性:"),115) 
                                      end
                                  end
                              end
                  )
                if  self.数据 then
                    self.数据技能= table.copy(self.数据.技能)
                    if self.数据.法术认证 and self.数据.法术认证[1]  then
                            local 遗留技能={}
                            if self.数据.技能[24] then
                                table.insert(遗留技能,self.数据.技能[24])
                                self.数据技能[24]=nil
                            end
                            if self.数据.技能[12] then
                                table.insert(遗留技能,self.数据.技能[12])
                                self.数据技能[12]=nil
                            end
                            if 遗留技能[1] or 遗留技能[2] then
                                local 最后编号=0
                                for i=1,11 do
                                    if self.数据技能[i]==nil and 最后编号<i then
                                        最后编号=i
                                        break
                                    end
                                end
                                if 最后编号~=0 then
                                    self.数据技能[最后编号]=遗留技能[1]
                                else
                                    local 找到编号=0
                                    for i=13,23 do
                                        if self.数据技能[i]==nil and 最后编号<i then
                                            找到编号=i
                                            break
                                        end
                                    end
                                    if 找到编号~=0 then
                                        self.数据技能[找到编号]=遗留技能[1]
                                        if 找到编号+1>=24 then
                                            self.数据技能[25]=遗留技能[2]
                                        else
                                            self.数据技能[找到编号+1]=遗留技能[2]
                                        end
                                    else  
                                        self.数据技能[25]=遗留技能[1] 
                                        self.数据技能[26]=遗留技能[2] 
                                    end
                                end
                            end
                    end
                    local yx = {{68,4},{25,28},{111,28},{24,75},{111,75},{68,97}}
                    for i=1,6 do
                        self.内丹图标[i]={}
                        if self.数据.内丹 and self.数据.内丹.内丹上限>=i and self.数据.内丹数据 then
                            if self.数据.内丹数据[i] and self.数据.内丹数据[i].技能 ~= nil then
                                    local lxxs = 取内丹数据(self.数据.内丹数据[i].技能,self.数据.内丹数据[i].等级,self.数据)
                                    if lxxs.资源 and lxxs.模型 then
                                        self.内丹图标[i].图标= __res:取资源动画(lxxs.资源, lxxs.模型,"精灵")
                                        self.内丹图标[i].名称=self.数据.内丹数据[i].技能
                                        self.内丹图标[i].等级=self.数据.内丹数据[i].等级
                                        self.内丹图标[i].资源 = lxxs.资源
                                        self.内丹图标[i].大动画 = lxxs.模型
                                        self.内丹图标[i].介绍 = "#G"..self.数据.内丹数据[i].等级.."层\\5层\n"..lxxs.说明.."\n#Y"..lxxs.效果
                                    else
                                        self.内丹图标[i].图标= __res:取资源动画("dlzy", 0xF2FC2425,"精灵")
                                        self.内丹图标[i].名称="可用的内丹技能格"
                                        self.内丹图标[i].资源 = "dlzy"
                                        self.内丹图标[i].大动画 = 0xF2FC2425
                                        self.内丹图标[i].介绍 = "可以学习的内丹技能"
                                    end
                            else
                                    self.内丹图标[i].图标= __res:取资源动画("dlzy", 0xF2FC2425,"精灵")
                                    self.内丹图标[i].名称="可用的内丹技能格"
                                    self.内丹图标[i].资源 = "dlzy"
                                    self.内丹图标[i].大动画 = 0xF2FC2425
                                    self.内丹图标[i].介绍 = "可以学习的内丹技能"
                            end
                        else
                            self.内丹图标[i].图标= __res:取资源动画("dlzy", 0x1E714129,"精灵")
                            self.内丹图标[i].名称="不可用的内丹技能格"
                            self.内丹图标[i].资源 = "dlzy"
                            self.内丹图标[i].大动画 = 0x1E714129
                            self.内丹图标[i].介绍 = "召唤兽可用内丹格数量和参战等级相关"
                        end
                        self.内丹图标[i].x=yx[i][1]
                        self.内丹图标[i].y=yx[i][2]
                    end
                end
                
            self:翻页设置()
        end

        function 技能控件:翻页设置()
                self.上一页:置可见(false)
                self.下一页:置可见(false)
                self.赐福:置可见(false)
                if self.数据 and self.状态=="技能" then
                    for i = 1, 12 do
                        self.技能图标[i]:置数据(self.数据技能[i+self.技能页数])
                    end
                    if self.数据.法术认证 and self.数据.法术认证[1] then
                        self.技能图标[12]:置数据(self.数据.法术认证[1])
                        self.技能图标[12].数据.认证=__res:取资源动画("dlzy",0x10921CA7,"图像"):拉伸(40,40):到精灵()
                    end
                    for k, v in pairs(self.数据.技能) do
                        for i = 1, 12 do
                            if self.技能图标[i].数据 and self.技能图标[i].数据.名称==v then
                                self.技能图标[i].编号=k
                            end
                        end
                    end




                    if self.超级图标 and  self.数据.超级赐福 and self.数据.超级赐福[1]  then
                        for i = 1, 4 do
                            self.超级赐福[self.数据.超级赐福[i]]=true
                        end
                        self.赐福:置可见(true)
                    end
                    if self.技能页数==0 and self.数据技能[13] then
                        self.下一页:置可见(true)
                    elseif self.技能页数==12 then
                        self.上一页:置可见(true)
                        if self.数据技能[25]  then
                            self.下一页:置可见(true)
                        end
                    elseif self.技能页数==24 then
                        self.上一页:置可见(true)
                    end
                end
        end

        function 技能控件:取焦点()
                if self.焦点 and self.数据 then
                    if self.状态=="技能" and self.技能图标[self.焦点].数据  and self.技能图标[self.焦点].编号  then
                        return self.技能图标[self.焦点].编号
                    elseif self.状态=="内丹" and  self.内丹图标 and  self.内丹图标[self.焦点] then
                        return self.焦点
                    end
                end
            return 0
        end
        function 技能控件:焦点数据()
            if self.选中 and self.数据 then
                if self.状态=="技能" and  self.技能图标[self.焦点].数据 and self.技能图标[self.焦点].编号  then
                    return self.数据.技能[self.技能图标[self.焦点].编号]
                elseif self.状态=="内丹" and  self.内丹图标 and  self.内丹图标[self.焦点] then
                        if self.内丹图标[self.焦点].名称 == "可用的内丹技能格" then
                            return "可用内丹"
                        elseif self.内丹图标[self.焦点].名称 == "不可用的内丹技能格" then
                            return "不可用内丹"
                        else
                            return "已有内丹"
                        end
                end
            end
            return nil
        end
        function 技能控件:取选中()
                if self.选中 and self.数据 then
                    if self.状态=="技能" and  self.技能图标[self.选中].数据 and self.技能图标[self.选中].编号 then
                        return self.技能图标[self.选中].编号
                    elseif self.状态=="内丹" and  self.内丹图标 and  self.内丹图标[self.选中] then
                        return self.选中
                    end
                end
            return 0
        end


        function 技能控件:选中数据()
            if self.选中 and self.数据 then
                if self.状态=="技能" and  self.技能图标[self.选中].数据 and self.技能图标[self.选中].编号  then
                    return self.数据.技能[self.技能图标[self.选中].编号]
                elseif self.状态=="内丹" and  self.内丹图标 and  self.内丹图标[self.选中] then
                        if self.内丹图标[self.选中].名称 == "可用的内丹技能格" then
                            return "可用内丹"
                        elseif self.内丹图标[self.选中].名称 == "不可用的内丹技能格" then
                            return "不可用内丹"
                        else
                            return "已有内丹"
                        end
                end
            end
            return nil
        end


        function 技能控件:取页数()
                return self.技能页数
        end

        function 技能控件:获得鼠标(xx, yy)
                    if __手机 then return end
                    self.焦点=nil
                    if self.数据 then
                        if self.状态=="技能" then
                                for i = 1, 12 do
                                    if self.技能图标[i].数据 and self.技能图标[i]:检查透明(xx, yy) then
                                        self.焦点=i
                                        self.技能图标[i].焦点=true
                                        if  self.超级赐福[self.技能图标[i].数据.名称] then
                                            __UI弹出.超级技能详情:打开(xx+20,yy+20,self.技能图标[i].数据)
                                        else
                                            __UI弹出.技能详情:打开(self.技能图标[i].数据,xx+20,yy+20)
                                        end
                                    end
                                end
                        elseif self.状态=="内丹" and  self.内丹图标 then
                                for i = 1, 6 do
                                    if self.内丹图标[i] and self.内丹图标[i].图标:检查透明(xx, yy) then
                                        self.焦点=i
                                        __UI弹出.自定义提示:打开(self.内丹图标[i],xx+20,yy+20)
                                    end
                                end
                        elseif self.状态=="进阶" and self.进阶图标 then
                                if self.进阶图标[15]:检查透明(xx, yy) and self.特性说明 then
                                    __UI弹出.自定义:打开(xx+20, yy+20,self.特性说明)
                                elseif self.进阶图标[16]:检查透明(xx, yy) then
                                    __UI弹出.自定义:打开(xx+20, yy+20,"#W使用#Y易经丹#W可以提升该召唤兽灵性,当灵性到达到50可以获得新的造型")
                                end
                        end
                    end
                    if not __手机 then
                      self:发送消息("回调获得鼠标",xx,yy,self.焦点) 
                    end
        end
        function 技能控件:失去鼠标(xx, yy)
            self.焦点=nil
            self:发送消息("回调失去鼠标",xx,yy) 
        end
        function 技能控件:右键弹起(xx, yy)
            self:发送消息("回调右键弹起",xx,yy) 
        end

        function 技能控件:左键弹起(xx, yy)
                    self.选中=nil
                    if self.数据 then
                        if self.状态=="技能" then
                                for i = 1, 12 do
                                    if  self.技能图标[i].数据 and self.技能图标[i]:检查透明(xx, yy) then
                                        self.选中=i
                                        if __手机 then
                                            if self.超级赐福[self.技能图标[i].数据.名称] then
                                                __UI弹出.超级技能详情:打开(xx+20,yy+20,self.技能图标[i].数据)
                                            else
                                                __UI弹出.技能详情:打开(self.技能图标[i].数据,xx+20,yy+20)
                                            end
                                        end
                                    end
                                end
                        elseif self.状态=="内丹" and  self.内丹图标 then
                                for i = 1, 6 do
                                    if self.内丹图标[i] and self.内丹图标[i].图标:检查透明(xx, yy) then
                                        self.选中=i
                                        if __手机 then
                                            __UI弹出.自定义提示:打开(self.内丹图标[i],xx+20,yy+20)
                                        end
                                    end
                                end
                        elseif self.状态=="进阶" and self.进阶图标 and __手机 then
                                if self.进阶图标[15]:检查透明(xx, yy) and self.特性说明 then
                                    __UI弹出.自定义:打开(xx+20, yy+20,self.特性说明)
                                elseif self.进阶图标[16]:检查透明(xx, yy) then
                                    __UI弹出.自定义:打开(xx+20, yy+20,"#W使用#Y易经丹#W可以提升该召唤兽灵性,当灵性到达到50可以获得新的造型")
                                end
                        end
                    end
                    self:发送消息("回调左键弹起",xx,yy,self.选中) 

        end




        function 技能控件:更新(dt)
                if self.状态=="内丹" and self.内丹动画 then
                    self.内丹动画:更新(dt)
                end
        end

        function 技能控件:显示(xx, yy)
                if self.数据 then
                    if self.状态=="技能" then
                        local xxx=0
                        local yyy=0
                        for i = 1, 12 do
                            if self.焦点~=i and self.技能图标[i].焦点 then
                                self.技能图标[i].焦点=nil
                            end
                            if self.技能图标[i].数据 then
                                self.技能图标[i]:显示(xx+1+xxx*41,yy+1+yyy*41)
                                if self.技能图标[i].数据.认证 then
                                        self.技能图标[i].数据.认证:显示(xx+1+xxx*41,yy+1+yyy*41)
                                end 
                                if self.超级图标 and self.超级赐福[self.技能图标[i].数据.名称] then
                                        self.超级图标:显示(xx+3+xxx*41,yy+3+yyy*41)
                                end
                            end
                            xxx=xxx+1
                            if xxx>=4 then
                                xxx=0
                                yyy=yyy+1
                            end
                        end
                    elseif self.状态=="内丹" and self.内丹图标 then
                            for i = 1, 6 do
                                if self.内丹图标[i] and self.内丹图标[i].图标 then
                                    self.内丹图标[i].图标:显示(xx+self.内丹图标[i].x, yy+self.内丹图标[i].y)
                                    if self.内丹动画 and self.选中 and self.选中==i then
                                        self.内丹动画:显示(xx+self.内丹图标[i].x, yy+self.内丹图标[i].y)
                                    end
                                end
                            end
                    elseif self.状态=="进阶" and self.进阶图标 then
                            self.进阶图标[16]:显示(xx+40,yy+7)
                            if  self.数据.灵性 then
                                if self.数据.灵性>0 and self.数据.灵性<= 10 then
                                        self.进阶图标[1]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>10 and self.数据.灵性<= 20 then
                                        self.进阶图标[2]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>20 and self.数据.灵性<= 30 then
                                        self.进阶图标[3]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>30 and self.数据.灵性<= 40 then
                                        self.进阶图标[4]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>40 and self.数据.灵性<= 50 then
                                        self.进阶图标[5]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>50 and self.数据.灵性<= 60 then
                                        self.进阶图标[6]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>60 and self.数据.灵性<= 70 then
                                        self.进阶图标[7]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>70 and self.数据.灵性<= 80 then
                                        self.进阶图标[8]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>80 and self.数据.灵性<= 90 then
                                        self.进阶图标[9]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>90 and self.数据.灵性<= 91 then
                                        self.进阶图标[10]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>91 and self.数据.灵性<= 93 then
                                        self.进阶图标[11]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>93 and self.数据.灵性<= 97 then
                                        self.进阶图标[12]:显示(xx+39,yy+6)
                                elseif self.数据.灵性>=98  then
                                        self.进阶图标[13]:显示(xx+39,yy+6)
                                end
                                if self.数据.灵性>80  then
                                    if self.数据.特性=="无" then
                                        self.进阶图标[14]:显示(xx+51,yy+30)
                                    else
                                      self.进阶图标[15]:显示(xx+67,yy+36)
                                   end
                               end
                                if self.特性图标 then
                                    self.特性图标:显示(xx+74,yy+45)
                                end
                            end
                    end
                end
        end
        local 上一页 =技能控件:创建按钮("上一页",170,25)
        function 上一页:初始化()
            self:创建按钮精灵(__res:取资源动画("dlzy",0x7AB5584C),1)
        end
        function 上一页:左键弹起(xx, yy)
            if 技能控件.状态=="技能" and 技能控件.技能页数>=12 then
                技能控件.技能页数=技能控件.技能页数-12
                技能控件:翻页设置()
            end
            技能控件:发送消息("上一页弹起",xx,yy) 
        end
        local 下一页 =技能控件:创建按钮("下一页",170,100)
        function 下一页:初始化()
            self:创建按钮精灵(__res:取资源动画("dlzy",0xCB50AB1D),1)
        end
        function 下一页:左键弹起(xx, yy)
            if 技能控件.状态=="技能" and 技能控件.技能页数<36 then
                技能控件.技能页数=技能控件.技能页数+12
                技能控件:翻页设置()
            end
            技能控件:发送消息("下一页弹起",xx,yy) 
        end
        local 赐福 =技能控件:创建按钮("赐福", 172, 5)
        function 赐福:初始化()
            self:创建按钮精灵(__res:取资源动画("pic","cjjnxtb.png","图片"),1)

        end
        function 赐福:左键弹起(xx, yy)
             技能控件:发送消息("赐福弹起",xx,yy) 
        end
        local 技能 = 技能控件:创建单选按钮("技能",190,0)
        function 技能:初始化()
                self:创建按钮精灵(__res:取资源动画("dlzy",0x1F996671))
        end
        function 技能:左键弹起(xx, yy)
            if 技能控件.状态~="技能" then
                技能控件.状态="技能"
                技能控件:显示设置()
            end
            技能控件:发送消息("技能弹起",xx,yy) 
        end
        local 内丹 = 技能控件:创建单选按钮("内丹",190,45)
        function 内丹:初始化()
                self:创建按钮精灵(__res:取资源动画("dlzy",0x9C24F376))
        end
        function 内丹:左键弹起(xx, yy)
            if 技能控件.状态~="内丹" then
                技能控件.状态="内丹"
                技能控件:显示设置()
            end
            技能控件:发送消息("内丹弹起",xx,yy) 
        end
        local 进阶 = 技能控件:创建单选按钮("进阶",190,90)
        function 进阶:初始化()
                self:创建按钮精灵(__res:取资源动画("dlzy",0xCD999F0B))
        end
        function 进阶:左键弹起(xx, yy)
            if 技能控件.状态~="进阶" then
                技能控件.状态="进阶"
                技能控件:显示设置()
            end
            技能控件:发送消息("进阶弹起",xx,yy) 
        end

    return 技能控件

end




local GGE文本 = require("GGE.文本")


local _objmeta = {
    检查点 = function(self, x, y)
        if self.rect then
            return self.rect:检查点(x, y)
        end
    end,
    置透明 = function(self, a)
        self.o:置透明(a)
    end,
    取坐标 = function(self)
        return self.o:取坐标()
    end,
    更新 = function(self, dt)
        if self.o.更新 then
            self.o:更新(dt)
        end
    end,
    显示 = function(self, x, y)
        if self.b then --闪烁
            self.b = self.b + 1
            if self.b > 60 then
                self.b = 0
            end
        end
        if not self.b or self.b > 30 then
            self.o:显示(self.x + x, y)
            if self.rect then
                self.rect:置坐标(self.o:取坐标())
                --self.rect:显示()
            end
        end
    end
}



local _obj = function(t, x, o)
    local self = setmetatable({}, { __index = _objmeta })
    self.x = x
    self.o = o --obj

    if t.m or t.url then
        self.cb = t.m or t.url
        self.rect = require('SDL.矩形')(0, 0, o.宽度, o.高度)
    end

    if t.b then --闪烁
        self.b = 0
    end
    return self
end

local _colors = {
    [82] = 16711680,
    [71] = 65280,
    [66] = 255,
    [87] = 16777215,
    [89] = 16776960,
    [75] = 0,
    [67] = 16750848,
    [72] = 4278190080,
    [76] = -16776961,
    [83] = -16466190,
    [90] = -16711681,
    [78] = -16777216,
    [80] = 65535,
    [70] = 16711934
}
local _insert = function(t)
    local i = #t + 1
    return function(data)
        if nil ~= data then
            t[i] = data
            i = i + 1
        end
    end
end


local _Parser = function(str)
   
    local style_c, style_b, style_u, style_F, style_m, style_j
    local datas = {}
    local indata = _insert(datas)
    local codes = {}
    local incode = _insert(codes)
    local u8char = utf8.char
    local unpack = table.unpack
    local iter = utf8.codes(str)
    local p, code
    while true do
        p, code = iter(str, p)
        if not p then
            break
        end
        if 35 == code and not style_j then
            p, code = iter(str, p)
            if not p then
                break
            end
            if #codes >= 1 and 35 ~= code and 114 ~= code and not style_m then
                indata({
                    c = style_c,
                    b = style_b,
                    u = style_u,
                    F = style_F,
                    s = u8char(unpack(codes))
                })
                codes = {}
                incode = _insert(codes)
            end
            if 35 == code then
                incode(code)
            elseif 98 == code then
                style_b = not style_b
            elseif 99 == code then
                style_c = tonumber(str:sub(p + 1, p + 6), 16)
                if style_c then
                    p = p + 6
                end
            elseif _colors[code] then
                style_c = _colors[code]
            elseif 109 == code then
                if style_m then
                    local m, s = string.match(u8char(unpack(codes)), "%((.+)%)(.*)")
                    indata({
                        c = style_c,
                        b = style_b,
                        u = style_u,
                        F = style_F,
                        m = m,
                        s = s
                    })
                    codes = {}
                    incode = _insert(codes)
                end
                style_m = not style_m
            elseif 91 == code then
                style_m = true
            elseif 93 == code then
                if style_m then
                    local m, s = string.match(u8char(unpack(codes)), "(.+)$(.*)")
                    indata({
                        c = style_c,
                        b = style_b,
                        u = style_u,
                        F = style_F,
                        m = m,
                        s = s
                    })
                    codes = {}
                    incode = _insert(codes)
                    style_m = false
                end
            elseif 110 == code then
              
                style_c = nil
            elseif 117 == code then
                style_u = not style_u
            elseif 114 == code then
                indata({
                    c = style_c,
                    b = style_b,
                    u = style_u,
                    F = style_F,
                    r = true,
                    s = u8char(unpack(codes))
                })
                codes = {}
                incode = _insert(codes)
            elseif 70 == code then
                style_F = true
            else
                if not (code >= 48 and code <= 57) then
                    goto lbl_386
                end
                local num = { code }
                local p_ = p
                for i = 2, 3 do
                    p_, code = iter(str, p_)
                    if not code then
                        break
                    end
                    if code >= 48 and code <= 57 then
                        p = p_
                        num[i] = code
                    end
                end
                indata({
                    s = tonumber(u8char(unpack(num)))
                })
            end
        elseif true == style_F and 58 == code then
            local name, size = string.match(u8char(unpack(codes)), "([^%d]+)(%d*)")
            style_F = {
                name,
                tonumber(size)
            }
            codes = {}
            incode = _insert(codes)
        elseif 104 == code then
            p, code = iter(str, p)
            if 116 ~= code then
                goto lbl_386
            end
            p, code = iter(str, p)
            if not p then
                break
            end
            if #codes >= 1 and 116 ~= code and 114 ~= code and not style_j then
                indata({
                    c = style_c,
                    b = style_b,
                    u = style_u,
                    F = style_F,
                    s = u8char(unpack(codes))
                })
                codes = {}
                incode = _insert(codes)
            end
            if 124 == code then
                style_j = true
            end
        elseif 35 == code and style_j then
            local m, s = string.match(u8char(unpack(codes)), "(.+)/(.*)")
            indata({
                c = style_c,
                b = style_b,
                u = style_u,
                F = style_F,
                m = m,
                s = s
            })
            codes = {}
            incode = _insert(codes)
            style_j = false
            p, code = iter(str, p)
            if not p then
                break
            end
            if _colors[code] then
                style_c = _colors[code]
            end
        elseif 47 == code and not style_j then
        else
            incode(code)
        end
        ::lbl_386::
    end
    if #codes > 0 then
        indata({
            c = style_c,
            b = style_b,
            u = style_u,
            F = style_F,
            s = u8char(unpack(codes))
        })
    end
    return datas
end

local function _split(str, width, font)

    for i, c in utf8.codes(str) do
        local w = font:取宽度(utf8.char(c))
        if w > width then
            return str:sub(1, i - 1), str:sub(i)
        else
            width = width - w
        end
    end
    return '', str
end



local function _Adjust(self)
    local width = self.宽度
    local fonts = self._文字表
    local emote = self._精灵表

    local font = fonts.默认
    local fh = font:取高度()

    local ret = {}
    local x = 0
    local line = { w = 0, h = fh }

    for _, v in ipairs(self._解析后) do
        if type(v.s) == 'string' then --文本
            --字体
            if type(v.F) == 'table' then
                local file, size = v.F[1], v.F[2]
                if fonts[file] then
                    font = fonts[file]
                end
                font:置大小(size)
                fh = size
                if not line.eh or fh > line.eh then --当没有表情时
                    line.h = fh
                end
            end
            --超链接
            local str = v.s
            if str:find('<url>') then
                str = str:match('<url>(.+)</url>')
                v.s = str
                v.url = str
                if str:find('<show>') then
                    local a, b = str:match('<show>(.+)</show>(.*)')
                    if a and b then
                        str = a
                        v.s = str
                        v.url = b
                    end
                end
            end
            --颜色
            if v.c then
                font:置颜色(v.c >> 16 & 0xFF, v.c >> 8 & 0xFF, v.c & 0xFF, 255)
            else
                font:置颜色(255, 255, 255, 255)
            end
            --下划线
            if v.u or v.url then
                font:置样式(SDL.TTF_STYLE_UNDERLINE)
            else
                font:置样式(font:取样式() & ~SDL.TTF_STYLE_UNDERLINE)
            end

            local w, h = font:取宽高(str)

            if x == 0 and w > 0 and font:取宽度(utf8.char(utf8.codepoint(str, 1))) > width then
                print('宽度过小')
            elseif x + w > width then --大于就换行
                ::loop::
                local a, b = _split(str, width - x, font)
                if a ~= '' then
                    w, h = font:取宽高(a)
                    table.insert(line, _obj(v, x, font:取精灵(a):置中心(0, h)))
                    x = x + w
                end
                line.w = x
                table.insert(ret, line)
                x = 0
                line = { w = 0, h = fh }
                --换行

                w, h = font:取宽高(b)
                if w > width then --循环换行
                    str = b
                    goto loop
                else
                    table.insert(line, _obj(v, x, font:取精灵(b):置中心(0, h)))
                    x = x + w
                    if v.r or x == width then
                        line.w = x
                        table.insert(ret, line)
                        x = 0
                        line = { w = 0, h = fh }
                    end
                end
            else
                if w > 0 then
                    table.insert(line, _obj(v, x, font:取精灵(str):置中心(0, h)))
                    x = x + w
                end
                if v.r or x == width then
                    line.w = x
                    table.insert(ret, line)
                    x = 0
                    line = { w = 0, h = fh }
                end
            end
        elseif emote[v.s] then --表情
          
            local e = emote[v.s]
            if e then
                local w, h = e:取宽高()
             
                if x + w > width then --大于就换行
                    line.w = x
                    table.insert(ret, line) --换行
                    x = 0
                    line = { w = 0, h = fh }
                end
                if x + w <= width then
                    table.insert(line, _obj(v, x, e:复制()))
                    x = x + w
                end
                if h > line.h then
                    line.h = h
                    line.eh = h
                end
                if v.r or x == width then
                    line.w = x
                    table.insert(ret, line)
                    x = 0
                    line = { w = 0, h = fh }
                end
               
                
            else --表情不存在，以文本显示
            end
        end
    end
    line.w = x
    table.insert(ret, line)
    for i, v in ipairs(ret) do
        v.eh = nil
    end
    return ret
end



function GGE文本:置文本(s, ...)

    if not self._文字表["默认"] then
        return 0, 0
    end
    
    if select("#", ...) > 0 then
        s = s:format(...)
    end

    s = s:gsub("\r\n", "#r"):gsub("\r", "#r"):gsub("\n", "#r")
   

    self._解析后 = _Parser(s)

    for _, v in ipairs(_Adjust(self)) do
        table.insert(self._数据表, v)
    end
    local w, h, y = 0, 0, 0
    for _, v in ipairs(self._数据表) do
        if w < v.w then
            w = v.w
        end
        h = h + v.h + self.行间距
        y = y + v.h
        v.y = y
        y = y + self.行间距
    end
    return w, h - self.行间距
end

local SDL文字 = require("SDL.文字")
local _set = function(self)
    if self._file then
        self:置大小(self._size)
        self:置样式(self._style)
    end
end
-- function SDL文字:取我的描边图像(t, r, g, b, a)
--     if t and "" ~= t then
--         _set(self)
--         local sfa, sfb
--         if self._anti then
--             sfa = self._font:RenderUTF8_Blended(t, self._r, self._g, self._b, self._a)
--             sfb = self._font:RenderUTF8_Blended(t, r or 0, g or 0, b or 0, a)
--         else
--             sfa = self._font:RenderUTF8_Solid(t, self._r, self._g, self._b, self._a)
--             sfb = self._font:RenderUTF8_Solid(t, r or 0, g or 0, b or 0, a)
--         end
--         local sf = SDL.CreateRGBSurfaceWithFormat(sfa.w + 2, sfa.h + 2)
--         local rs = SDL.CreateRect(1, 0, 0, 0)
--         sfb:BlitSurface(nil, sf, rs)
--         rs:SetRectXY(0, 1)
--         sfb:BlitSurface(nil, sf, rs)
--         rs:SetRectXY(2, 1)
--         sfb:BlitSurface(nil, sf, rs)
--         rs:SetRectXY(1, 2)
--         sfb:BlitSurface(nil, sf, rs)
--         rs:SetRectXY(1, 1)
--         sfa:BlitSurface(nil, sf, rs)
--         return require("SDL.图像")(sf)
--     end
-- end

-- function SDL文字:取我的描边精灵(t, r, g, b, a)
--     if t and "" ~= t then
--         return self._win:创建精灵(self:取我的描边图像(t, r, g, b, a))
--     end
--     return self._win:创建精灵()
-- end

-- function SDL文字:取我的投影图像(t, r, g, b, a)
--     if t and "" ~= t then
--         _set(self)
--         local sfa, sfb
--         if self._anti then
--             sfa = self._font:RenderUTF8_Blended(t, self._r, self._g, self._b, self._a)
--             sfb = self._font:RenderUTF8_Blended(t, r or 0, g or 0, b or 0, a)
--         else
--             sfa = self._font:RenderUTF8_Solid(t, self._r, self._g, self._b, self._a)
--             sfb = self._font:RenderUTF8_Solid(t, r or 0, g or 0, b or 0, a)
--         end
--         local sf = SDL.CreateRGBSurfaceWithFormat(sfa.w + 1, sfa.h + 1)
--         local rs = SDL.CreateRect(1, 0, 0, 0)
--         rs:SetRectXY(1, 1)
--         sfb:BlitSurface(nil, sf, rs)
--         rs:SetRectXY(0, 0)
--         sfa:BlitSurface(nil, sf, rs)
--         return require("SDL.图像")(sf)
--     end
-- end

-- function SDL文字:取我的投影精灵(t, r, g, b, a)
--     if t and "" ~= t then
--         return self._win:创建精灵(self:取我的投影图像(t, r, g, b, a))
--     end
--     return self._win:创建精灵()
-- end


function SDL文字:取描边投影图像(t, r, g, b, a, r1, g1, b1, a1)
    if t and t ~= '' then
        _set(self)
        local sfa
        local sfb
        local sfc
        if self._anti then
            sfa = self._font:RenderUTF8_Blended(t, self._r, self._g, self._b, self._a)
            sfb = self._font:RenderUTF8_Blended(t, r or 0, g or 0, b or 0, a)
        else
            sfa = self._font:RenderUTF8_Solid(t, self._r, self._g, self._b, self._a)
            sfb = self._font:RenderUTF8_Solid(t, r or 0, g or 0, b or 0, a)
            sfc = self._font:RenderUTF8_Solid(t, r1 or 0, g1 or 0, b1 or 0, a1)
        end
        local sf = SDL.CreateRGBSurfaceWithFormat(sfa.w + 2, sfa.h + 2)
        local rs = SDL.CreateRect(1, 0, 0, 0)
        sfb:BlitSurface(nil, sf, rs)
        rs:SetRectXY(0, 1)
        sfb:BlitSurface(nil, sf, rs)
        rs:SetRectXY(1, 1)
        sfb:BlitSurface(nil, sf, rs)
        rs:SetRectXY(2, 1)
        sfb:BlitSurface(nil, sf, rs)
        rs:SetRectXY(1, 2)
        sfb:BlitSurface(nil, sf, rs)
        rs:SetRectXY(1, 1)
        if sfc then
            sfc:BlitSurface(nil, sf, rs)
            rs:SetRectXY(0, -1)
            sfc:BlitSurface(nil, sf, rs)
            rs:SetRectXY(-2, -1)
            sfc:BlitSurface(nil, sf, rs)
            rs:SetRectXY(-1, -2)
            sfc:BlitSurface(nil, sf, rs)
            rs:SetRectXY(-1, -1)
            sfc:BlitSurface(nil, sf, rs)
            rs:SetRectXY(0, 0)
        end
        sfa:BlitSurface(nil, sf, rs)
        return require('SDL.图像')(sf)
    end


end

function SDL文字:取描边投影精灵(t, r, g, b, a, r1, g1, b1, a1)
    if t and "" ~= t then
        return self._win:创建精灵(self:取描边投影图像(t, r, g, b, a, r1, g1, b1, a1))
    end
    return self._win:创建精灵()
end



local SDL图像 = require("SDL.图像")
function SDL图像:更新时间()
    self._time = os.time() + 300
end

function SDL图像:检查时间(time)
    if not self._time then
        self._time = os.time()
    end
    return time > self._time
end

-- local SDL精灵 = require("SDL.精灵")
-- function SDL精灵:置坐标(x, y)
--     if not y and ggetype(x) == "GGE坐标" then
--         x, y = x:unpack()
--     end
--     if x and y then
--         x, y = math.floor(x), math.floor(y)
--         if self._hx then
--             x, y = x - self._hx, y - self._hy
--         end
--         self._x, self._y = x, y
--         self._dr:SetRectXY(x, y)
--     end
-- end

-- local GGE动画 = require("GGE.动画")
-- function GGE动画:置坐标(x, y)
--     if not y and ggetype(x) == "GGE坐标" then
--         x, y = x:unpack()
--     end
--     if x and y then
--         x, y = math.floor(x), math.floor(y)
--         if self._hx then
--             x, y = x - self._hx, y - self._hy
--         end
--         self._x, self._y = x, y
--     end
-- end

__取颜色 = function(name)
    if "黑色" == name then
        return 0, 0, 0
    elseif "浅黑" == name then
        return 39, 53, 81
    elseif "红色" == name then
        return 255, 1, 1
    elseif "青色" == name then
        return 4, 255, 255
    elseif "黄色" == name then
        return 255, 255, 8
    elseif "绿色" == name then
        return 1, 255, 1
    elseif "紫色" == name then
        return 255, 1, 255
    elseif "蓝色" == name then
        return 0,0,240
    elseif "橙色" == name then
        return 255, 154, 0
    elseif "深蓝" == name then
        return 29, 27, 238
    else
        return 255, 255, 255
    end
end

取颜色字符 = function(name)
    if "黑色" == name then
        return "#H"
    elseif "红色" == name then
        return "#R"
    elseif "青色" == name then
        return "#P"
    elseif "黄色" == name then
        return "#Y"
    elseif "绿色" == name then
        return "#G"
    elseif "紫色" == name then
        return "#F"
    elseif "蓝色" == name then
        return "#B"
    elseif "橙色" == name then
        return "#C"
    else
        return "#W"
    end
end




__取银子颜色 = function(jiag)
    local 数额=0
    if tonumber(jiag)~=nil then
        数额=tonumber(jiag)
    end
    if 数额 < 10000 then
        return 0,0,0,255
      elseif 数额 < 100000 then
        return __取颜色("蓝色")    
    elseif 数额 < 1000000 then
        return __取颜色("绿色")
    elseif 数额 < 10000000 then
        return __取颜色("红色")
    elseif 数额 < 100000000 then
        return __取颜色("黄色")
    else
        return __取颜色("紫色")
    end
end


__取商城银子颜色 = function(jiag)
  local 数额=0
  if tonumber(jiag)~=nil then
      数额=tonumber(jiag)
  end
  if 数额 < 10000 then
      return 255,255,255,255
  elseif 数额 < 100000 then
      return __取颜色("蓝色")    
  elseif 数额 < 1000000 then
      return __取颜色("绿色")
  elseif 数额 < 10000000 then
      return __取颜色("红色")
  elseif 数额 < 100000000 then
      return __取颜色("黄色")
  else
      return __取颜色("紫色")
  end
end





function __dewpal(id)

    local data = SDL.LoadFile("assets/wpal/" .. id .. ".wpal")
    local flag, num, pos = string.unpack("<c4I4", data)
    if "wpal" == flag then
        local h, n = {}
        for i = 1, num + 1 do
            n, pos = string.unpack("<I4", data, pos)
            table.insert(h, n)
        end
        local ret = {}
        for i = 1, num do
            ret[i] = {
                a = h[i],
                b = h[i + 1]
            }
            n, pos = string.unpack("<I4", data, pos)
            for j = 1, n do
                ret[i][j] = {}
                for N = 1, 9 do
                    n, pos = string.unpack("<I4", data, pos)
                    ret[i][j][N] = n
                end
            end
        end
        return ret
    end
end




-- function __dewpal(id)
--     local data = SDL.LoadFile("assets/wpal/" .. id .. ".wpal")
--     local flag, num, pos = string.unpack("<c4I4", data)
--     if "wpal" == flag then
--         local h, n = {}
--         for i = 1, num + 1 do
--             n, pos = string.unpack("<I4", data, pos)
--             table.insert(h, n)
--         end
--         if 1 == id then
--             h[2] = 57
--             h[3] = 112
--             h[4] = 172
--         elseif 201 == id then
--             h[2] = 45
--             h[3] = 81
--             h[4] = 181
--         elseif 202 == id then
--             h[2] = 50
--             h[3] = 166
--             h[4] = 206
--         elseif 203 == id then
--             h[2] = 40
--             h[3] = 126
--             h[4] = 206
--         elseif 7 == id then
--             h[2] = 40
--             h[3] = 130
--             h[4] = 191
--         elseif 3 == id then
--             h[2] = 55
--             h[3] = 104
--             h[4] = 186
--         elseif 5 == id then
--             h[2] = 62
--             h[3] = 122
--             h[4] = 184
--         elseif 6 == id then
--             h[2] = 70
--             h[3] = 140
--             h[4] = 186
--         elseif 8 == id then
--             h[2] = 60
--             h[3] = 126
--             h[4] = 186
--         elseif 9 == id then
--             h[2] = 40
--             h[3] = 115
--             h[4] = 190
--         elseif 10 == id then
--             h[2] = 50
--             h[3] = 100
--             h[4] = 196
--         elseif 11 == id then
--             h[2] = 66
--             h[3] = 146
--             h[4] = 186
--         elseif 12 == id then
--             h[2] = 50
--             h[3] = 110
--             h[4] = 190
--         elseif 204 == id then
--             h[2] = 50
--             h[3] = 130
--             h[4] = 200
--         elseif 205 == id then
--             h[2] = 50
--             h[3] = 136
--             h[4] = 201
--         elseif 206 == id then
--             h[2] = 50
--             h[3] = 130
--             h[4] = 200
--         elseif 301 == id then
--             h[2] = 0
--             h[3] = 256
--             h[4] = 2
--         elseif 701 == id then
--             h[4] = 2
--         elseif 702 == id then
--             h[4] = 2
--         elseif 703 == id then
--             h[4] = 2
--         elseif 704 == id then
--             h[4] = 2
--         elseif 705 == id then
--             h[4] = 2
--         elseif 706 == id then
--             h[4] = 2
--         elseif 707 == id then
--             h[4] = 2
--         elseif 708 == id then
--             h[4] = 2
--         elseif 709 == id then
--             h[4] = 2
--         elseif 710 == id then
--             h[4] = 2
--         elseif 711 == id then
--             h[4] = 2
--         elseif 712 == id then
--             h[4] = 2
--         elseif 713 == id then
--             h[4] = 2
--         elseif 714 == id then
--             h[4] = 2
--         elseif 715 == id then
--             h[4] = 2
--         elseif 716 == id then
--             h[4] = 2
--         elseif 717 == id then
--             h[4] = 2
--         elseif 718 == id then
--             h[4] = 2
--         elseif 719 == id then
--             h[4] = 2
--         elseif 720 == id then
--             h[4] = 2
--         elseif 721 == id then
--             h[4] = 2
--         elseif 722 == id then
--             h[4] = 2
--         elseif 722 == id then
--             h[4] = 2
--         elseif 103 == id then
--             h[2] = 100
--             h[3] = 256
--             h[4] = 2
--         end
--         local ret = {}
--         for i = 1, num do
--             ret[i] = {
--                 a = h[i],
--                 b = h[i + 1]
--             }
--             n, pos = string.unpack("<I4", data, pos)
--             for j = 1, n do
--                 ret[i][j] = {}
--                 for N = 1, 9 do
--                     n, pos = string.unpack("<I4", data, pos)
--                     ret[i][j][N] = n
--                 end
--             end
--         end
--         return ret
--     end
-- end

__染色信息 = {
    ["通用"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 301
    },
    ["护卫"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2051
    },
    ["泡泡"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 701
    },
    ["树怪"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 56
    },
    ["大海龟"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 69
    },
    ["巨蛙"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 70
    },
    ["章鱼"] = {
        ["方案"] = {
            [1] = 2
        },
        id = 119
    },
    ["海星"] = {
        ["方案"] = {
            [1] = 0
        },
        id = 119
    },
    ["野猪"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 52
    },
    ["大蝙蝠"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 66
    },
    ["海毛虫"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 67
    },
    ["狸"] = {
        ["方案"] = {
            [1] = 5
        },
        id = 2079
    },
    ["强盗"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 74
    },
    ["山贼"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 75
    },
    ["赌徒"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 76
    },
    ["狐狸精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 81
    },
    ["羊头怪"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 54
    },
    ["花妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 58
    },
    ["骷髅怪"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 60
    },
    ["蛤蟆精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 61
    },
    ["老虎"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 64
    },
    ["黑熊"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 65
    },
    ["野鬼"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 80
    },
    ["虾兵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 83
    },
    ["蟹将"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 84
    },
    ["牛妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 87
    },
    ["小龙女"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 63
    },
    ["狼"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 68
    },
    ["牛头"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 77
    },
    ["马面"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 78
    },
    ["僵尸"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 79
    },
    ["龟丞相"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 85
    },
    ["蜘蛛精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 80
    },
    ["兔子怪"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 51
    },
    ["黑熊精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 53
    },
    ["黑山老妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 93
    },
    ["蝴蝶仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 57
    },
    ["雷鸟人"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 62
    },
    ["白熊"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 72
    },
    ["古代瑞兽"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 73
    },
    ["善财童子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 703
    },
    ["哮天犬"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 701
    },
    ["天兵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 82
    },
    ["天将"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 71
    },
    ["风伯"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 89
    },
    ["地狱战神"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 94
    },
    ["花铃"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 702
    },
    ["蛟龙"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 90
    },
    ["凤凰"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 91
    },
    ["蚌精"] = {
        ["方案"] = {
            [1] = 1
        },
        id = 20306
    },
    ["鲛人"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 2057
    },
    ["碧水夜叉"] = {
        ["方案"] = {
            [1] = 1
        },
        id = 2059
    },
    ["雨师"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 114
    },
    ["月影仙"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["星灵仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 88
    },
    ["巡游天神"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 92
    },
    ["犀牛将军人形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 704
    },
    ["犀牛将军兽形"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 5
        },
        id = 20104
    },
    ["锦毛貂精"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 3
        },
        id = 20103
    },
    ["芙蓉仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 55
    },
    ["如意仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 59
    },
    ["千年蛇魅"] = {
        ["方案"] = {
            [1] = 5,
            [2] = 0
        },
        id = 20104
    },
    ["野猪精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 107
    },
    ["百足将军"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 108
    },
    ["鼠先锋"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 109
    },
    ["泪妖"] = {
        ["方案"] = {
            [1] = 6
        },
        id = 20113
    },
    ["镜妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2062
    },
    ["吸血鬼"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 96
    },
    ["幽灵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 97
    },
    ["灵符女娲"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 98
    },
    ["律法女娲"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 99
    },
    ["阴阳伞"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2070
    },
    ["鬼将"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 95
    },
    ["净瓶女娲"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 100
    },
    ["幽萤娃娃"] = {
        ["方案"] = {
            [1] = 4,
            [2] = 0
        },
        id = 20113
    },
    ["画魂"] = {
        ["方案"] = {
            [1] = 4,
            [2] = 0
        },
        id = 20113
    },
    ["云游火"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 704
    },
    ["狐不归"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 706
    },
    ["大力金刚"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 101
    },
    ["夜罗刹"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 104
    },
    ["雾中仙"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 1
        },
        id = 102
    },
    ["灵鹤"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 103
    },
    ["炎魔神"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 105
    },
    ["噬天虎"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 106
    },
    ["琴仙"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2071
    },
    ["金饶僧"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 1
        },
        id = 2069
    },
    ["月魅"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 707
    },
    ["踏云兽"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 1
        },
        id = 110
    },
    ["红萼仙子"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 1
        },
        id = 111
    },
    ["葫芦宝贝"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2071
    },
    ["蝎子精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2071
    },
    ["龙龟"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 1
        },
        id = 112
    },
    ["机关人人形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 708
    },
    ["机关人车"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2070
    },
    ["猫灵兽形"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 1
        },
        id = 2057
    },
    ["狂豹兽形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2065
    },
    ["机关兽"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2070
    },
    ["连弩车"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2070
    },
    ["机关鸟"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2070
    },
    ["巴蛇"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2070
    },
    ["长眉灵猴"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2051
    },
    ["巨力神猿"] = {
        ["方案"] = {
            [1] = 0,
            [2] = 1
        },
        id = 2079
    },
    ["修罗傀儡鬼"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2065
    },
    ["藤蔓妖花"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2042
    },
    ["蜃气妖"] = {
        ["方案"] = {
            [1] = 5,
            [2] = 0
        },
        id = 2079
    },
    ["猫灵人形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 723
    },
    ["狂豹人形"] = {
        ["方案"] = {
            [1] = 3,
            [2] = 0
        },
        id = 20230
    },
    ["混沌兽"] = {
        ["方案"] = {
            [1] = 6,
            [2] = 0
        },
        id = 2078
    },
    ["修罗傀儡妖"] = {
        ["方案"] = {
            [1] = 5,
            [2] = 0
        },
        id = 2078
    },
    ["金身罗汉"] = {
        ["方案"] = {
            [1] = 2,
            [2] = 0
        },
        id = 2000
    },
    ["曼珠沙华"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 2070
    },
    ["持国巡守"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 709
    },
    ["毗舍童子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 84
    },
    ["真陀护法"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 710
    },
    ["增长巡守"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 711
    },
    ["灵灯侍者"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["般若天女"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 713
    },
    ["涂山雪"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["谛听"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 704
    },
    ["进阶黑山老妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 706
    },
    ["进阶蝴蝶仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶雷鸟人"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 711
    },
    ["进阶白熊"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 711
    },
    ["进阶古代瑞兽"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 711
    },
    ["进阶善财童子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 710
    },
    ["进阶哮天犬"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["进阶天兵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶风伯"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 716
    },
    ["进阶地狱战神"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 705
    },
    ["进阶花铃"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶天将"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 716
    },
    ["进阶蛟龙"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 703
    },
    ["进阶凤凰"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶蚌精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶鲛人"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶碧水夜叉"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 710
    },
    ["进阶雨师"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 717
    },
    ["进阶月影仙"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶星灵仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 704
    },
    ["进阶巡游天神"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 708
    },
    ["进阶犀牛将军人形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶犀牛将军兽形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶锦毛貂精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["进阶芙蓉仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶如意仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 708
    },
    ["进阶千年蛇魅"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 718
    },
    ["进阶野猪精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 711
    },
    ["进阶百足将军"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 711
    },
    ["进阶鼠先锋"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["进阶泪妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶镜妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 710
    },
    ["进阶吸血鬼"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶幽灵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 719
    },
    ["进阶灵符女娲"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶律法女娲"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶阴阳伞"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶鬼将"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 708
    },
    ["进阶净瓶女娲"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 719
    },
    ["进阶幽萤娃娃"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 710
    },
    ["进阶画魂"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶云游火"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶狐不归"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶大力金刚"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶夜罗刹"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["进阶雾中仙"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 708
    },
    ["进阶灵鹤"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 709
    },
    ["进阶炎魔神"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶噬天虎"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶琴仙"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶金饶僧"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶月魅"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶踏云兽"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶红萼仙子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶葫芦宝贝"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶蝎子精"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶龙龟"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶机关人人形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 716
    },
    ["进阶猫灵兽形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 703
    },
    ["进阶狂豹兽形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 708
    },
    ["进阶机关兽"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶机关鸟"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶连弩车"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶巴蛇"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 720
    },
    ["进阶长眉灵猴"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶巨力神猿"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶修罗傀儡鬼"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶藤蔓妖花"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶蜃气妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶猫灵人形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 718
    },
    ["进阶狂豹人形"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶混沌兽"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶修罗傀儡妖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶金身罗汉"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶曼珠沙华"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 716
    },
    ["进阶持国巡守"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 708
    },
    ["进阶毗舍童子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 717
    },
    ["进阶真陀护法"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 716
    },
    ["进阶增长巡守"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶灵灯侍者"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶般若天女"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["进阶涂山雪"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["进阶谛听"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["小毛头"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 704
    },
    ["小丫丫"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 703
    },
    ["小魔头"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 719
    },
    ["小精灵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["小仙灵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 705
    },
    ["小仙女"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["进阶小毛头"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶小丫丫"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["进阶小魔头"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 708
    },
    ["进阶小精灵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 716
    },
    ["进阶小仙灵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 710
    },
    ["进阶小仙女"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["牛魔王"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 716
    },
    ["大大王"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 716
    },
    ["程咬金"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 711
    },
    ["观音菩萨"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["空度禅师"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 711
    },
    ["孙婆婆"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 722
    },
    ["地涌夫人"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 711
    },
    ["地藏菩萨"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 711
    },
    ["白晶晶"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["阎罗王"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 719
    },
    ["镇元子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["李天王"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 719
    },
    ["巫奎虎"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["东海龙王"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["菩提老祖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 703
    },
    ["二郎神"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["周杰伦"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 708
    },
    ["齐天大圣"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["猪八戒"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["烟花占卜师"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 714
    },
    ["春十三娘"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["有个和尚"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["郑镖头"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 715
    },
    ["天马"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["九头虫"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["九灵元圣"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["腾蛇炫卡"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["小象炫卡"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["雪人炫卡"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 712
    },
    ["蚩尤"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["知了王"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["九色鹿"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["沙和尚"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["自在天魔"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 718
    },
    ["自在天魔宝珠"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["自在天魔刀"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["自在天魔宝剑"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["自在天魔经筒"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["自在天魔弓弩"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["自在天魔法杖"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["自在天魔斧钺"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 0
        },
        id = 702
    },
    ["飞燕女"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 3
    },
    ["英女侠"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 4
    },
    ["巫蛮儿"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 201
    },
    ["逍遥生"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 1
    },
    ["剑侠客"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 2
    },
    ["狐美人"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 7
    },
    ["骨精灵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 8
    },
    ["影精灵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 8
    },
    ["杀破狼"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 202
    },
    ["巨魔王"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 5
    },
    ["虎头怪"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 6
    },
    ["舞天姬"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 11
    },
    ["玄彩娥"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 12
    },
    ["羽灵神"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 203
    },
    ["神天兵"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 9
    },
    ["龙太子"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 10
    },
    ["桃夭夭"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 204
    },
    ["偃无师"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 205
    },
    ["鬼潇潇"] = {
        ["方案"] = {
            [1] = 1,
            [2] = 1
        },
        id = 206
    }
}


__染色方案={
	[1]={方案={[1]=1,[2]=0},id=64},
	[2]={方案={[1]=1,[2]=0},id=0},
	[3]={方案={[1]=1,[2]=0},id=83},
	[4]={方案={[1]=1,[2]=0},id=106},
	[5]={方案={[1]=1,[2]=0},id=114},
	[6]={方案={[1]=1,[2]=0},id=77},
	[7]={方案={[1]=1,[2]=0},id=60},
	[8]={方案={[1]=1,[2]=0},id=2051},
	[9]={方案={[1]=1,[2]=0},id=2065},
	[10]={方案={[1]=1,[2]=1},id=56},
	[11]={方案={[1]=1,[2]=0},id=54},
	[12]={方案={[1]=1,[2]=0},id=67},
	[13]={方案={[1]=1,[2]=0},id=52},
	[14]={方案={[1]=1,[2]=0},id=82},
	[15]={方案={[1]=1,[2]=1},id=107},
	[16]={方案={[1]=1,[2]=0},id=76},
	[17]={方案={[1]=1,[2]=0},id=80},
	[18]={方案={[1]=1,[2]=0},id=2070},
	[19]={方案={[1]=0,[2]=1},id=2057},
	[20]={方案={[1]=1,[2]=0},id=53},
	[21]={方案={[1]=1,[2]=0},id=85},
	[22]={方案={[1]=1,[2]=0},id=69},
	[23]={方案={[1]=2},id=119},
	[24]={方案={[1]=0,[2]=1},id=2069},
	[25]={方案={[1]=1,[2]=0},id=101},
	[26]={方案={[1]=1,[2]=0},id=58},
	[27]={方案={[1]=2,[2]=0},id=2000},
	[28]={方案={[1]=5,[2]=0},id=2078},
	[29]={方案={[1]=5,[2]=0},id=2079},
	[30]={方案={[1]=1,[2]=0},id=2042},
	[31]={方案={[1]=1,[2]=1},id=90},
	[32]={方案={[1]=1,[2]=0},id=2071},
	[33]={方案={[1]=1,[2]=0},id=75},
	[34]={方案={[1]=1,[2]=0},id=95},
	[35]={方案={[1]=1,[2]=1},id=108},
	[36]={方案={[1]=1,[2]=0},id=81},
	[37]={方案={[1]=1,[2]=0},id=70},
	[38]={方案={[1]=1,[2]=1},id=55},
	[39]={方案={[1]=1,[2]=0},id=63},
	[40]={方案={[1]=1,[2]=0},id=89},
	[41]={方案={[1]=1,[2]=0},id=66},
	[42]={方案={[1]=4,[2]=0},id=20113},
	[43]={方案={[1]=1,[2]=0},id=74},
	[44]={方案={[1]=0,[2]=1},id=111},
	[45]={方案={[1]=1,[2]=0},id=2062},
	[46]={方案={[1]=0,[2]=1},id=112},
	[47]={方案={[1]=1,[2]=0},id=98},
	[48]={方案={[1]=1,[2]=1},id=94},
	[49]={方案={[1]=0,[2]=1},id=110},
	[50]={方案={[1]=0,[2]=1},id=102},
	[51]={方案={[1]=1,[2]=0},id=61},
	[52]={方案={[1]=1,[2]=1},id=59},
	[53]={方案={[1]=1,[2]=0},id=51},
	[54]={方案={[1]=1,[2]=0},id=68},
	[55]={方案={[1]=1,[2]=0},id=78},
	[56]={方案={[1]=1,[2]=1},id=93},
	[57]={方案={[1]=1,[2]=0},id=62},
	[58]={方案={[1]=1,[2]=0},id=103},
	[59]={方案={[1]=1,[2]=0},id=71},
	[60]={方案={[1]=5,[2]=0},id=20104},
	[61]={方案={[1]=1,[2]=1},id=92},
	[62]={方案={[1]=0,[2]=3},id=20103},
	[63]={方案={[1]=1,[2]=0},id=65},
	[64]={方案={[1]=1,[2]=1},id=91},
	[65]={方案={[1]=1,[2]=0},id=97},
	[66]={方案={[1]=1,[2]=0},id=96},
	[67]={方案={[1]=1,[2]=0},id=87},
	[68]={方案={[1]=1,[2]=0},id=105},
	[69]={方案={[1]=1,[2]=0},id=99},
	[70]={方案={[1]=1,[2]=0},id=104},
	[71]={方案={[1]=1,[2]=1},id=88},
	[72]={方案={[1]=1},id=20306},
	[73]={方案={[1]=1,[2]=0},id=100},
	[74]={方案={[1]=1,[2]=1},id=73},
	[75]={方案={[1]=1,[2]=0},id=79},
	[76]={方案={[1]=1,[2]=0},id=57},
	[77]={方案={[1]=1},id=2059},
	[78]={方案={[1]=1,[2]=0},id=84},
	[79]={方案={[1]=1,[2]=0},id=109},
	[80]={方案={[1]=3,[2]=0},id=20230},
	[81]={方案={[1]=1,[2]=2},id=3000},
}

