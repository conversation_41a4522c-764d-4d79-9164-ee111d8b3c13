--[[
LastEditTime: 2024-09-27 05:17:50
--]]

local 多开自动 = 战斗层:创建窗口("多开自动",0,0,340,200)
function 多开自动:初始化()
    self.缩放=1
    self.禁止移动= true
end 


function 多开自动:打开()
    self:置可见(true)
    self:显示重置()
    if  __手机 then
        self:置坐标(0, 界面层.高度-界面层.聊天控件.高度-self.高度+10)
    else
        if 界面层.聊天控件.聊天窗口 then
            self:置坐标(0, 界面层.高度-self.高度-40)
        else
            self:置坐标(0, 界面层.高度-205-self.高度)
        end
    end
end 



function 多开自动:显示重置()
        self.显示控件:置可见(false)
        self.自动开关:置可见(false)
        self.技能网格:置可见(false)
        self.显示控件:置可见(false)
        self.缩放按钮:置可见(true)
        self.缩放按钮:置坐标(0,0)
        self.多开数据=nil
        self.技能名称=nil
       -- self.显示控件:加载技能()
        self:置精灵()
        if __战斗主控.自动开关 then
            self.自动开关:重置文字("取消自动")
        else
            self.自动开关:重置文字("开启自动")
        end
        if self.缩放==1 then
            self:置精灵(__res:取资源动画('dlzy',0x00000001,"精灵"))
            self.显示控件:置可见(true)
            self.自动开关:置可见(true)
            self.技能网格:置可见(true)
            self.显示控件:置可见(true)
            self.缩放按钮:置坐标(307,0)
            self.多开数据=table.copy(__战斗主控.多开数据)
            self.技能名称=self:创建纹理精灵(function()
                  local 技能={}
                  local xx = 0
	                local yy = 0
                  for i=1,10 do
                      if self.多开数据[i] and self.多开数据[i].编号 and self.多开数据[i].参数 then 
                            技能[i]=self.多开数据[i].参数
                            文本字体:置颜色(255,255,255,255):取投影图像(self.多开数据[i].参数):显示(10+(50-文本字体:取宽度(self.多开数据[i].参数))//2+xx*60,85+yy*85)
                      end
                      xx = xx + 1
                      if xx >=5 then
                        yy = yy +1
                        xx = 0
                      end
                  end
                  self.技能网格:置数据(技能)
            end,1)
            





        end
end


local 技能网格 = 多开自动:创建网格("技能网格",14,40,290,135)

function 技能网格:初始化()
      self:创建格子(40, 40,45,20,2,5)
end 

function 技能网格:置数据(数据)
     for i, v in ipairs(self.子控件) do
        if 数据 and 数据[i] then
            local lssj =  __技能格子:创建()
            lssj:置数据(数据[i])
            v:置精灵(lssj)
        else
           v:置精灵(__res:取资源动画('dlzy',0x00000003,"精灵"))
        end
     end
end



function 技能网格:获得鼠标(x,y,a)
    if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
        self.子控件[self.焦点]._spr.焦点=nil
    end
    if self.子控件[a]._spr and self.子控件[a]._spr.数据 and 多开自动.多开数据 and 多开自动.多开数据[a] and 多开自动.多开数据[a].编号 then
          self.焦点=a
          self.子控件[a]._spr.焦点=true
          __UI弹出.自定义:打开(x+25,y+25,"#Y右键设置 #R"..__战斗主控.战斗单位[多开自动.多开数据[a].编号].名称.."#Y 的技能")
    end
end

function 技能网格:失去鼠标(x,y)
  for i, v in ipairs(self.子控件) do
      if v._spr.焦点 then
          v._spr.焦点=nil
      end
  end
  self.焦点=nil
end

function 技能网格:右键按下(x,y,a)

  界面层.按下1=false
end


function 技能网格:右键弹起(x,y,a)
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 and 多开自动.多开数据 and 多开自动.多开数据[a] and 多开自动.多开数据[a].编号 then
        local 战斗单位 = __战斗主控.战斗单位[多开自动.多开数据[a].编号]
        if 战斗单位.门派=="九黎城" then
           战斗层.九黎法术:打开(战斗单位,a)
        else
           战斗层.多开法术:打开(战斗单位,a)
        end
  end
  界面层.按下1=false
end


function 技能网格:左键按下(x,y,a)

  界面层.按下=false

end

function 技能网格:左键弹起(x,y,a)
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 and 多开自动.多开数据 and 多开自动.多开数据[a] and 多开自动.多开数据[a].编号 and __手机  then
        local 战斗单位 = __战斗主控.战斗单位[多开自动.多开数据[a].编号]
        if 战斗单位.门派=="九黎城" then
            战斗层.九黎法术:打开(战斗单位,a)
       else
            战斗层.多开法术:打开(战斗单位,a)
       end

  end
  界面层.按下=false

end
local 显示控件 = 多开自动:创建控件("显示控件",0,0,315,200)
function 显示控件:初始化()
    self:置精灵(__res:取资源动画('dlzy',0x00000002,"精灵"))
end
function 显示控件:显示(x,y)
        if 多开自动.技能名称 then
            多开自动.技能名称:显示(x,y)
        end
end


local 缩放按钮 = 多开自动:创建按钮("缩放按钮",307,0,40,190) 
function 缩放按钮:初始化()
        self:创建按钮精灵( __res:取资源动画('dlzy',0x00000001,"图像"):复制区域(307,0,40,190),1)
end 


function 缩放按钮:左键按下(x,y)
        if 多开自动.缩放==1 then
            多开自动.缩放=0
        else
            多开自动.缩放=1
        end
        多开自动:显示重置()
        界面层.按下=false
end

local 自动开关 = 多开自动:创建红色按钮("开启自动", "自动开关",10,0,74,22) 
function 自动开关:重置文字(txt,jz)
    self:置文字(74,22,txt)
    self:置禁止(false)
    if jz then
        self:置禁止(jz)
    end
end
function 自动开关:左键按下(x,y)
    请求服务(5507)
    界面层.按下=false
end