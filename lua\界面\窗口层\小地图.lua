--[[
LastEditTime: 2024-10-21 10:53:46
--]]
--[[
LastEditTime: 2024-10-09 20:44:12
--]]

local 小地图 = 窗口层:创建窗口("小地图",0,0, 100, 100)
local 筛选表 = {"全部","普通","商业","特殊","传送","任务","出口"}

local 取dt路径=function(mc)
	local pz={}
if mc=="长安城" then
		pz={"南瞻部洲","大唐国境",mc}
	elseif mc=="建邺城" then
		pz={"南瞻部洲","大唐国境","长安城","江南野外",mc}
	elseif mc=="东海湾" then
		pz={"南瞻部洲","建邺城",mc}
	elseif mc=="大唐官府" then
		pz={"南瞻部洲","大唐国境","长安城",mc}
	elseif mc=="大雁塔一层" or mc=="大雁塔一层" or mc=="大雁塔二层" or mc=="大雁塔三层" or mc=="大雁塔四层" or mc=="大雁塔五层" or mc=="化生寺"then
		pz={"南瞻部洲","大唐国境","长安城",mc}
	elseif mc=="长寿村" then
		pz={"西牛贺洲",mc}
	elseif mc=="长寿郊外" then
		pz={"西牛贺洲",mc}
	elseif mc=="丝绸之路" then
		pz={"西牛贺洲",mc}
	elseif mc=="西凉女国" then
		pz={"西牛贺洲",mc}
	elseif mc=="朱紫国" then
		pz={"西牛贺洲",mc}
	elseif mc=="碗子山" then
		pz={"西牛贺洲",mc}
	elseif mc=="宝象国" then
		pz={"西牛贺洲","丝绸之路",mc}
	elseif mc=="麒麟山" then
		pz={"西牛贺洲",mc}
	elseif mc=="解阳山" then
		pz={"西牛贺洲",mc}
	elseif mc=="大唐境外" then
		pz={"南瞻部洲",mc}
	elseif mc=="傲来国" then
		pz={"东胜神洲",mc}
	elseif mc=="女儿村" then
		pz={"东胜神洲",mc}
	else
		pz={"东胜神洲",mc}
	end
return pz
end




function 小地图:初始化()
    self.标记 = __res:取资源动画("dlzy",0x393947EB,"精灵")
    self.标记:置中心(9, 9)
    self.终点标识 =__res:取资源动画("dlzy",0xDEE57252,"精灵") 
    self.终点标识:置中心(3, self.终点标识.高度)
    self.路径 = __res:取资源动画("dlzy",0xF792E03C,"精灵")
    self.传送阵 =__res:取资源动画("dlzy",0x558897FF,"图像")
    self.小背景=__res:取资源动画("pic","dtxbj.png","图片")
end








  function 小地图:打开(id)
        if not __小地图资源加载(id) then
            界面层.聊天控件:添加文本("此场景无法查看小地图","xt")
          return
        end
        self:置可见(not self.是否可见)
        if not self.是否可见  then
            return
        end
        self.筛选控制 = {}
        for _, n in ipairs(筛选表) do
              self.筛选控制[n] = false
              self[n .. "按钮"]:置选中(false)
        end
        if self.记忆地图 and self.记忆地图 ~= id then
            self:显示设置(id)
        elseif not self.记忆地图 then
            self:显示设置(id)
        end
        self:筛选重置()
        if  self.终点 then
            local 路径 = __主显.主角:设置路径(self.终点)
            self.路径列表={}
            if 路径 and 路径[1] then
              for i, v in ipairs(路径) do
                  if i%8 ==0 then
                      table.insert(self.路径列表, require("GGE.坐标")(v.x*20, v.y*20))
                  end
              end
            end
        end
  end

  function 小地图:显示设置(id)
    self.记忆地图 = id
    self.图像 = nil
    self.提示字体=nil
    self.地图提示=nil
    self.偏移X = 0
    self.偏移Y = 0
    local Smap, Smapa = __小地图资源加载(id)
    if Smapa then
        self.图像 = __res:取资源动画(Smap, Smapa,"精灵")
        self.py = {
          x = self.图像.宽度 / __主显.地图.宽度,
          y = self.图像.高度 / __主显.地图.高度
        }
        self.pys = {
          x = __主显.地图.宽度 / self.图像.宽度,
          y = __主显.地图.高度 / self.图像.高度
        }
        local w,h = 100,100
        if self.图像.宽度>= 500 then
            w,h=self.图像.宽度+20,self.图像.高度+125
            for i, v in ipairs(筛选表) do
                self[v.."按钮"]:置坐标(10+(i-1)*65,h-35)
            end
            self.提示字体=self:创建纹理精灵(function()
                for i, v in ipairs(筛选表) do
                    标题字体:置颜色(255,255,255,255)
                    if v=="全部" or v=="任务" then
                      标题字体:置颜色(__取颜色("青色"))
                    elseif v=="商业" then
                      标题字体:置颜色(__取颜色("黄色"))
                    elseif v=="特殊" then
                      标题字体:置颜色(__取颜色("绿色"))
                    elseif v=="传送" then
                      标题字体:置颜色(__取颜色("红色"))
                    elseif v=="出口" then
                      标题字体:置颜色(__取颜色("紫色"))
                    end
                    标题字体:取投影图像(v):显示((i-1)*65,0)
                end
            end,1,self.图像.宽度,35)
        else
            w,h=self.图像.宽度+90,self.图像.高度+85
            for i, v in ipairs(筛选表) do
                self[v.."按钮"]:置坐标(self.图像.宽度+20,85+(i-1)*30)
            end
            self.提示字体=self:创建纹理精灵(function()
                  for i, v in ipairs(筛选表) do
                    标题字体:置颜色(255,255,255,255)
                      if v=="全部" or v=="任务" then
                        标题字体:置颜色(__取颜色("青色"))
                      elseif v=="商业" then
                        标题字体:置颜色(__取颜色("黄色"))
                      elseif v=="特殊" then
                        标题字体:置颜色(__取颜色("绿色"))
                      elseif v=="传送" then
                        标题字体:置颜色(__取颜色("红色"))
                      elseif v=="出口" then
                        标题字体:置颜色(__取颜色("紫色"))
                      end
                      标题字体:取投影图像(v):显示(0,(i-1)*30)
                  end
            end,1,35,self.图像.高度)
        end
        local 地图路径 = 取dt路径(取地图名称(id))
        self.地图提示=self:创建纹理精灵(function()
             for i, v in ipairs(地图路径) do
                self.小背景:显示((i-1)*(self.小背景.宽度+20),0)
                local zts=文本字体:置颜色(255,255,255,255):取图像(v)
                zts:显示((self.小背景.宽度-zts.宽度)//2+(i-1)*(self.小背景.宽度+20),2)
             end
             for i=1,#地图路径-1 do
                标题字体:置颜色(255,255,255,255):取描边图像(">"):显示(self.小背景.宽度+5+(i-1)*(self.小背景.宽度+20),2)
            end
        end,1,(self.小背景.宽度+25)*#地图路径,40)
        self:置宽高(w,h)
        self:置精灵(置窗口背景(取地图名称(id), 0, 15, w,h))
        self:置坐标((引擎.宽度-w)// 2,(引擎.高度-h) // 2)
        self.世界:置坐标(w-self.世界.宽度-20,h-self.世界.高度-10)
        if __手机 then
              self.关闭:置大小(25,25)
              self.关闭:置坐标(self.宽度-27, 17)
        else
              self.关闭:置大小(16,16)
              self.关闭:置坐标(self.宽度-18, 17)
        end
  
    else
       self:置可见(false)
    end

     
  end


  function 小地图:显示(x,y)
        if self.地图提示 then
          self.地图提示:显示(x+10,y+48)
        end
        if self.图像 then
          self.图像:显示(x+10,y+75)
        end
        if self.提示字体 and self.图像 then
          if self.图像.宽度>= 500 then
              self.提示字体:显示(x+35,y+95+self.图像.高度)
          else
              self.提示字体:显示(x+45+self.图像.宽度,y+90)
          end
          
        end

        if self.筛选图像 then
            self.筛选图像:显示(x + 10, y + 75)
        end
        self.标记:显示(x + math.floor(__主显.主角.xy.x * self.py.x) + 10, y + math.floor(__主显.主角.xy.y * self.py.y) + 75)
    
        if self.终点 then
          if __主显.主角.xy:取距离(self.终点) < 10 or not __主显.主角.是否移动 then
            self.终点 = nil
          else
            local xx, yy = x + math.floor(self.终点.x * self.py.x)+ 10, y + math.floor(self.终点.y * self.py.y)+ 75
            self.终点标识:显示(xx, yy)
              if self.路径列表  then
                  for i, v in ipairs(self.路径列表) do
                      local xxx, yyy = x + math.floor(v.x * self.py.x)+ 10, y + math.floor(v.y * self.py.y)+ 75
                      self.路径:显示(xxx, yyy)
                      if __主显.主角.xy:取距离(v)<10 then
                          table.remove(self.路径列表,i)
                      end
                  end
              end

          end
        end


  end

  
	

	--self.NPC查找Q= 按钮.创建(资源:载入('登陆资源.wdf',"网易WDF动画",0x0BC4D521),0,0,4,true,true,"")
  function 小地图:左键按下(x, y)
        if self.图像:检查透明(x, y) then
                  local xx, yy = self.图像:取坐标()
                  local xxx, yyy = x - xx, y - yy
                  self.终点 = require("GGE.坐标")(xxx * self.pys.x, yyy * self.pys.y):floor()
                  if  __手机 then
                      __UI弹出.自定义:打开(x-30,y-45,string.format("#Y%d，%d", self.终点.x//20,self.终点.y//20))
                  end
                  if __主显.主角 and __主显.主角:是否可移动() then
                    self:路径设置(self.终点)
                    请求服务(1001,{x=self.终点.x//20,y=self.终点.y//20})
                  end
        end
  end


  function 小地图:路径设置(目标)
          if 目标 then
              self.路径列表={}
              __主显.主角.按下=false
              __主显.主角.点击移动=nil
              local 路径 = __主显.主角:设置路径(目标) 
              if 路径 and 路径[1] then
                  for i, v in ipairs(路径) do
                      if i%8 ==0 then
                        table.insert(self.路径列表, require("GGE.坐标")(v.x*20, v.y*20))
                      end
                  end
              end
          end
  end
  
  function 小地图:获得鼠标(x, y)
          if self.图像:检查透明(x, y) and __主显.主角 then
              local xx, yy = self.图像:取坐标()
              local x1, y1 = x - xx, y - yy
              local 显示坐标 = require("GGE.坐标")(x1 * self.pys.x, y1 * self.pys.y):floor()
              __UI弹出.自定义:打开(x+30,y+20,string.format("#Y%d，%d", 显示坐标.x//20,显示坐标.y//20))
          end
  end

  function 小地图:筛选重置()
    self.筛选图像 = nil

    self.筛选表 = {假人 = {},传送 = {},场景 = {}}
    for k, v in pairs(场景取假人表(self.记忆地图) or {}) do
        table.insert(self.筛选表.假人, v)
    end
    for k, v in pairs(__传送数据[self.记忆地图]  or {}) do
        table.insert(self.筛选表.传送, v)
    end
    for k, v in pairs(场景取名称(self.记忆地图) or {}) do
          table.insert(self.筛选表.场景, v)
    end
    self.筛选图像 = self:创建纹理精灵(function()
          if  self.筛选控制.出口  then
              for _, v in ipairs(self.筛选表.传送) do
                  local x, y
                  if v.X and v.Y then
                      x, y = v.X * 20, v.Y * 20
                  elseif v.x and v.y then
                      x, y = v.x * 20, v.y * 20
                  end
                  self.传送阵:显示(math.floor(x * self.py.x - self.传送阵.宽度 / 2), math.floor(y * self.py.y - self.传送阵.高度 / 2)+15)
              end
          end
          for _, v in ipairs(self.筛选表.场景) do
              local x, y, tsf
              if v.X and v.Y then
                  x, y = v.X * 20, v.Y * 20
              elseif v.x and v.y then
                  x, y = v.x * 20, v.y * 20
              end
              if v.等级字体 then
                  if v.红字体 then
                        tsf = 标题字体:置颜色(__取颜色("红色")):取投影图像(v.名称, 255, 255, 255, 150)
                  else
                        tsf = 标题字体:置颜色(__取颜色("蓝色")):取投影图像(v.名称, 255, 255, 255, 150)
                  end
              else
                  tsf = 标题字体:置颜色(__取颜色("蓝色")):取投影图像(v.名称, 255, 255, 255, 150)
              end
              tsf:显示(math.floor(x * self.py.x - tsf.宽度 / 2), math.floor(y * self.py.y - tsf.高度 / 2))
          end
          for _, v in ipairs(self.筛选表.假人) do
              if v.地图颜色 then
                  local x, y, tsf
                  if v.X and v.Y then
                      x, y = v.X * 20, v.Y * 20
                  elseif v.x and v.y then
                      x, y = v.x * 20, v.y * 20
                  end
                  if (0 == v.地图颜色 or 1 == v.地图颜色) and self.筛选控制.普通 then
                      tsf = 文本字体:置颜色(255,255,255,255):取投影图像( v.名称, 0, 0, 0, 150)
                  elseif 2 == v.地图颜色 and self.筛选控制.商业  then
                      tsf = 文本字体:置颜色(__取颜色("黄色")):取投影图像( v.名称, 0, 0, 0, 150)
                  elseif 3 == v.地图颜色 and self.筛选控制.特殊 then
                      tsf = 文本字体:置颜色(__取颜色("绿色")):取投影图像(v.名称, 0, 0, 0, 150)
                  elseif 4 == v.地图颜色 and self.筛选控制.传送  then
                      tsf = 文本字体:置颜色(__取颜色("红色")):取投影图像(v.名称, 0, 0, 0, 150)
                  elseif 5 == v.地图颜色 and self.筛选控制.任务  then
                      tsf = 文本字体:置颜色(__取颜色("青色")):取投影图像(v.名称, 0, 0, 0, 150)
                  end
                  if tsf then
                      tsf:显示(math.floor(x * self.py.x - tsf.宽度 / 2), math.floor(y * self.py.y - tsf.高度 / 2))
                  end
              end
          end
      end,1,self.图像.宽度+10, self.图像.高度+10)

end

  
for i, v in ipairs(筛选表) do
      local 临时选项=小地图:创建多选按钮(v.."按钮")
      function 临时选项:初始化()
        self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000009))
    end
      function 临时选项:左键弹起(x, y)
            if 临时选项.是否选中 then
                  小地图.筛选控制[v] = false
                  if v =="全部" then
                      for _, n in ipairs(筛选表) do
                          if n ~= "全部" then
                            小地图.筛选控制[n] = false
                            小地图[n .. "按钮"]:置选中(false)
                          end
                      end
                  end
              else
                  小地图.筛选控制[v] = true
                  if v =="全部" then
                      for _, n in ipairs(筛选表) do
                          if n ~= "全部" then
                            小地图.筛选控制[n] = true
                            小地图[n .. "按钮"]:置选中(true)
                          end
                      end
                  end
              end

              小地图:筛选重置()
      end
end





local NPC查找 = 小地图:创建按钮("NPC查找",0,0)
function NPC查找:初始化()
  self:创建按钮精灵(__res:取资源动画("dlzy", 0x0BC4D521), 1)
end
function NPC查找:左键按下(x, y)
        窗口层.小地图NPC:打开(小地图.筛选表.假人,取地图名称(小地图.记忆地图))
end
  
local 世界 =小地图:创建红色按钮("世界", "世界", 0, 0,44,22)
function  世界:左键弹起(x, y)

    窗口层.大地图:打开()
    小地图:置可见(false)
end

local 关闭 = 小地图:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    小地图:置可见(false)
end