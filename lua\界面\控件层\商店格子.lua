local 基类 = require("界面/控件层/基类/物品基类")
local 商店格子 = class("商店格子", 基类)
function 商店格子:初始化()
  self.py = {x = 0, y = 0}
end
function 商店格子:置物品(数据, lx)
  self.模型 = nil
  self.物品 = nil
  if "商城道具" == lx then
    local nsf = require("SDL.图像")(125, 125)
    if 数据 then
      self:取数据(数据)
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 375, 388, 145, 149)["拉伸"](__res:getPNGCC(3, 375, 388, 145, 149), 125, 125)["显示"](__res:getPNGCC(3, 375, 388, 145, 149)["拉伸"](__res:getPNGCC(3, 375, 388, 145, 149), 125, 125), 0, 0)
        __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 36, 36)
        if 数据["模型"] and 数据["种类"] == "宝宝" then
          local lssj = 取头像(数据["模型"])
          __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50), 38, 38)
          字体18["置颜色"](字体18, __取颜色("黄色"))
          local tsf = 字体18["取图像"](字体18, 数据["模型"])
          tsf["显示"](tsf, (125 - tsf["宽度"]) // 2, 8)
          字体18["置颜色"](字体18, __取颜色("红色"))
          tsf = 字体18["取图像"](字体18, "价格:" .. 数据["价格"])
          tsf["显示"](tsf, (125 - tsf["宽度"]) // 2, 96)
        else
          local 目录="shape/dj/"
          if 锦衣文件完整 and self.物品.资源 and (self.物品.资源=="r3d.dll" or self.物品.资源=="nx3d5.dll" or self.物品.资源=="nx3d6.dll") then
            目录="shape/sys/"
          end
          if self.物品.颜色区分 then
            __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])):置颜色(检查是否有物品颜色(self.物品.属性)):显示(38, 38)
            else
            __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])), 38, 38)
            end
          -- __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])), 38, 38)
          字体18["置颜色"](字体18, __取颜色("黄色"))
          local tsf = 字体18["取图像"](字体18, 数据["名称"])
          tsf["显示"](tsf, (125 - tsf["宽度"]) // 2, 8)
          字体18["置颜色"](字体18, __取颜色("红色"))
          tsf = 字体18["取图像"](字体18, "价格:" .. 数据["价格"])
          tsf["显示"](tsf, (125 - tsf["宽度"]) // 2, 96)
        end
        nsf["渲染结束"](nsf)
      end
      self.精灵 = nsf["到精灵"](nsf)
      self.格子类型 = lx
    end
  elseif "NPC商店" == lx then
    local nsf = require("SDL.图像")(55, 55)
    if 数据 then
      self:取数据(数据)
      self.格子类型 = lx
      self.物品["价格"] = 数据["价格"]
      self.物品["原始商品"] = 数据["原始商品"]
      local hjdshf= {红玛瑙=1,太阳石=1,舍利子=1,黑宝石=1,月亮石=1,神秘石=1,星辉石=1,光芒石=1,精魄灵石=1}
      if 数据.子类 then
        self.物品["子类"] = 数据["子类"] + 0
        -- print(self.物品.名称,hjdshf[self.物品.名称])
        if hjdshf[self.物品.名称] then
          self.物品["临时lv"]=self.物品["子类"]
        end
        self.取重复名物品(self)
      end
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
        local 目录="shape/dj/"
          if 锦衣文件完整 and self.物品.资源 and (self.物品.资源=="r3d.dll" or self.物品.资源=="nx3d5.dll" or self.物品.资源=="nx3d6.dll") then
            目录="shape/sys/"
          end
        __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self.精灵 = nsf["到精灵"](nsf)
    else
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self.精灵 = nsf["到精灵"](nsf)
    end
  else
    local nsf = require("SDL.图像")(55, 55)
    if 数据 then
      self:取数据(数据)
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
        local 目录="shape/dj/"
          if 锦衣文件完整 and self.物品.资源 and (self.物品.资源=="r3d.dll" or self.物品.资源=="nx3d5.dll" or self.物品.资源=="nx3d6.dll") then
            目录="shape/sys/"
          end
        __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])), 0, 0)
        if 数据["数量"] then
          置轮廓文字(字体18,数据["数量"],"黑色","白色",1, -2)
        end
        nsf["渲染结束"](nsf)
      end
      self.精灵 = nsf["到精灵"](nsf)
    else
      if nsf["渲染开始"](nsf) then
        __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
        nsf["渲染结束"](nsf)
      end
      self.精灵 = nsf["到精灵"](nsf)
    end
  end
  self.数据 = 数据
end
function 商店格子:详情打开(x, y, w, h, lx, bh)
  local Button, Button2, Button3, Button4
  __UI弹出["道具详情"]["置可见"](__UI弹出["道具详情"], true, true)
  __UI弹出["道具详情"]["道具文本"]["清空"](__UI弹出["道具详情"]["道具文本"])
  __UI弹出["道具详情"]["打开"](__UI弹出["道具详情"], self.物品, x, y, 360, 360, Button, Button2, Button3, Button4, bh, lx)
end
function 商店格子:更新(dt)
end
function 商店格子:显示(x, y)
  if self.精灵 then
    self.精灵["显示"](self.精灵, x + self.py.x, y + self.py.y)
  end
  if self.确定 and self.格子类型 == "NPC商店" then
    __主控["道具选中小"]["显示"](__主控["道具选中小"], x, y)
  elseif self.确定 and self.格子类型 == "商城道具" then
    __主控["道具选中小"]["显示"](__主控["道具选中小"], x + 36, y + 36)
  end
end
return 商店格子
