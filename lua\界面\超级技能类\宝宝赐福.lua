local 宝宝赐福 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "宝宝赐福", 80+67+30 + abbr.py.x, 42-25 + abbr.py.y, 665-40-90, 470+18+18)

function 宝宝赐福:初始化()
  local nsf = require("SDL.图像")(665-90, 470+18+18)
  if nsf["渲染开始"](nsf) then
    置窗口背景("赐 福", 0, 12, 665-40-90, 460+18+18, true):显示(0, 0)
    --__res:getPNGCC(8, 838, 56, 282, 399+18):显示(352+15+15-40, 53)
    --取灰色背景(0, 0, 366-40, 399+18, true):置透明(130):显示(15, 53)
   取白色背景(0, 0, 336, 153+18, true):置透明(235):显示(190, 50)
   取白色背景(0, 0, 178, 153+18, true):置透明(235):显示(7, 50)
   取白色背景(0, 0, 178, 272, true):置透明(235):显示(7, 215-44+37+18)
  __res:getPNGCC(3, 694, 4, 338, 273):显示(190,215-44+37+18)
   __res:getPNGCC(8, 0, 208, 53, 53):拉伸(45,45):显示(339,140-5)
   local gezi=__res:getPNGCC(3, 1091, 374, 50, 50):拉伸(43,43)
   for i=1,4 do
    gezi:显示(213+(i-1)*83,57)
  end
    字体14:置颜色(__取颜色("白色"))
  --  字体14:取图像("倍率说明"):显示(359,115+61+24)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
local function 递增函数(n)
	if n==0 then
	    return 0
	end
	return qz(20 * 2^(n-1))
end

function 宝宝赐福:打开(内容)
  self.宝宝列表=内容.宝宝列表 or {}
  for i=1,道具总数量 do
    if 内容.道具列表 and 内容.道具列表.道具 and 内容.道具列表.道具[i] then
      __主控.道具列表[i] = 内容.道具列表.道具[i]
    else
      __主控.道具列表[i] = nil
    end
  end
  self.新赐福=false
  self.锁定技能={}
  self.头像组 = {}
  self.加入 = 0
  self.拽拖对象=0
  self.宝宝动画={}
  self.操作宝宝编号=0
  self.模型格子 = __UI模型格子.创建()
  self:置可见(true)
  self.赐福技能控件.丸子网格:置物品()
  self.道具网格:置物品(__主控["道具列表"])
  self.bb列表:重置头像()
  self:按钮逻辑()
  self:锁定按钮初始化()
end
function 宝宝赐福:按钮逻辑()
  self.赐福按钮:置可见(not self.新赐福)
  self.保存技能:置可见(self.新赐福)
  self.再次赐福:置可见(self.新赐福)
end
function 宝宝赐福:更新(dt)
  self.模型格子:更新(dt)
end
function 宝宝赐福:显示(x, y)
  self.模型格子:显示(x, y)
end






local 赐福技能控件 = 宝宝赐福["创建控件"](宝宝赐福, "赐福技能控件", 190, 50, 336, 153+16)

local 丸子网格 = 赐福技能控件["创建网格"](赐福技能控件, "丸子网格", 11+135, 11+81-9, 46, 46)
function 丸子网格:初始化()
  self:创建格子(46, 46, 0, 0, 1, 1)
end
function 丸子网格:置物品()
  for i = 1, #丸子网格.子控件 do
    local lssj = __物品格子.创建()
    lssj:置物品({名称="仙露丸子"}, "经脉界面")
    self.子控件[i]:置精灵(lssj)
  end
end
function 丸子网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "1、赐福消耗#G1个仙露丸子#W。 2、锁定1个技能，#G5个仙露丸子#W。 3、锁定2，#G15个仙露丸子#W。 4、锁定3，#G99个仙露丸子#W。" )
  end
end




local 赐福技能网格 = 赐福技能控件:创建网格("赐福技能网格", 25, 8, 320, 45)
function 赐福技能网格:初始化()
  self:创建格子(55, 55, 8, 8+20, 1, 4)
end

function 赐福技能网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr.数据 then
    self.子控件[a]._spr:详情打开(x, y+109)
  end
end

function 赐福技能网格:置技能对象(yiyou,linshi,技能)
  for n = 1, #赐福技能网格.子控件 do
    local lssj = __召唤兽技能格子.创建()
    if 宝宝赐福.新赐福 then
      if linshi  and  linshi[n] then
        local 是否灰度=true
        for i=1,# 技能 do
          if 技能[i]==linshi[n] then
            是否灰度=false
            break
          end
        end
        lssj:置数据(linshi[n], 40, 40, nil, nil, nil, 是否灰度) 
      end
    else --显示老的赐福技能
      if yiyou  and  yiyou[n] then
        local 是否灰度=true
        for i=1,# 技能 do
          if 技能[i]==yiyou[n] then
            是否灰度=false
            break
          end
        end
        lssj:置数据(yiyou[n], 40, 40, nil, nil, nil, 是否灰度) 
      end
    end
    赐福技能网格.子控件[n]:置精灵(lssj)
  end
end
















local 道具网格 = 宝宝赐福["创建网格"](宝宝赐福, "道具网格", 373-184, 132+100, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    if 宝宝赐福["道具选中"] and self.子控件[宝宝赐福["道具选中"]] and self.子控件[宝宝赐福["道具选中"]]._spr then
      self.子控件[宝宝赐福["道具选中"]]._spr["确定"] = nil
    end
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 120, 86, w, h, "", a)
    宝宝赐福["道具选中"] = a
    self.子控件[a]._spr["确定"] = true
    -- end
  end
end
function 道具网格:置物品(数据)
  宝宝赐福["道具选中"] = nil
  for i = 1, #self.子控件 do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "宝宝赐福")
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end





--:到灰度()

local bb列表 = 宝宝赐福:创建列表("bb列表", 8, 239, 178, 253)
function bb列表:初始化()
  self:置文字(字体20)
  self.行高度 = 50
  self.行间距 = 0
end
function bb列表:重置头像()
  self:清空()
  for _, v in ipairs(宝宝赐福.宝宝列表) do
    local nsf = require("SDL.图像")(292, 50)
    if nsf:渲染开始() then
      if v["名称"] and v["等级"] then
        local lssj = 取头像(v["模型"])
        if 0 == lssj[2] then
          lssj[2] = lssj[1]
        end
        --print(_)
        if 宝宝赐福.操作宝宝编号==_ then
          __res:getPNGCC(3, 1091, 374, 50, 50):拉伸(43,43):显示(3, 2)
          __res:取图像(__res:取地址("shape/mx/", lssj[2])):拉伸(35, 35):显示(8, 6)
          字体16:置颜色(__取颜色("红色"))
          字体16["取图像"](字体16, v["名称"])["显示"](字体16["取图像"](字体16, v["名称"]), 75-21, 4)
          字体16["取图像"](字体16, v["等级"] .. "级")["显示"](字体16["取图像"](字体16, v["等级"] .. "级"), 75-21, 25)
          __res["UI素材"][1]:复制区域(725, 211, 30, 30):拉伸(22,22):显示(32, 26)
        else
          __res:getPNGCC(3, 1091, 374, 50, 50):拉伸(43,43):到灰度():显示(3, 2)
          __res:取图像(__res:取地址("shape/mx/", lssj[2])):拉伸(35, 35):到灰度():显示(8, 6)
          字体16:置颜色(__取颜色("黑色"))
          字体16["取图像"](字体16, v["名称"])["显示"](字体16["取图像"](字体16, v["名称"]), 75-21, 4)
          字体16["取图像"](字体16, v["等级"] .. "级")["显示"](字体16["取图像"](字体16, v["等级"] .. "级"), 75-21, 25)
        end
      end
      nsf:渲染结束()
    end
    local r = self:添加()
    r:置精灵(nsf:到精灵())
  end
end

function bb列表:左键弹起(x, y, i, item, msg)
  if 宝宝赐福.新赐福 then
    if 宝宝赐福.操作宝宝编号~=0 and 宝宝赐福.操作宝宝编号 ~= i then
      local 事件 = function()
        if 宝宝赐福.宝宝列表[i] then
          宝宝赐福.操作宝宝编号 = i
          self:重置头像()
          宝宝赐福.模型格子:置数据(宝宝赐福.宝宝列表[i], "召唤兽", 150-28, 225-37)
          宝宝赐福.新赐福=false
          宝宝赐福:加载赐福技能()
          宝宝赐福:按钮逻辑()
          宝宝赐福:锁定按钮初始化()
        end
      end
      local wb = "本次赐福效果尚未保存，是否放弃本次赐福结果？"
      __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, wb, 285, 155, 390, 200, 事件)
    end
  else
    if 宝宝赐福.宝宝列表[i] then
      宝宝赐福.操作宝宝编号 = i
      self:重置头像()
      宝宝赐福.模型格子:置数据(宝宝赐福.宝宝列表[i], "召唤兽", 150-28, 225-37)
      宝宝赐福.新赐福=false
      宝宝赐福:加载赐福技能()
      宝宝赐福:按钮逻辑()
      宝宝赐福:锁定按钮初始化()
    end
  end
end



function 宝宝赐福:加载赐福技能(内容)
	self.新赐福=false
	local bb=self.宝宝列表[self.操作宝宝编号]
	if bb then
		if 内容 and 内容.认证码==bb.认证码 then
			self.新赐福=true
			self.宝宝列表[self.操作宝宝编号]=内容
      self:按钮逻辑()
		end
   -- table.print(self.宝宝列表[self.操作宝宝编号])
		self.赐福技能控件.赐福技能网格:置技能对象(self.宝宝列表[self.操作宝宝编号].赐福技能_已有,self.宝宝列表[self.操作宝宝编号].赐福技能_临时,self.宝宝列表[self.操作宝宝编号].技能)
	end
	-- if tp.窗口.赐福技能列表.可视 then
	-- 	tp.窗口.赐福技能列表:重载技能(self.宝宝列表[self.操作宝宝编号].技能)
	-- end
end


function 宝宝赐福:保存赐福(内容)
	local bb=self.宝宝列表[self.操作宝宝编号]
	if bb then
		if 内容 and 内容.认证码==bb.认证码 then
			--table.print(内容.赐福技能_已有)
			self.新赐福=false
			self.宝宝列表[self.操作宝宝编号]=内容
			self.赐福技能控件.赐福技能网格:置技能对象(self.宝宝列表[self.操作宝宝编号].赐福技能_已有,self.宝宝列表[self.操作宝宝编号].赐福技能_临时,self.宝宝列表[self.操作宝宝编号].技能)
      self:按钮逻辑()
		end
	end
end







local 赐福按钮 = 宝宝赐福["创建我的按钮18"](宝宝赐福,  __res:getPNGCC(5, 905, 276, 56, 30):拉伸(70, 35), "赐福按钮", 190+11+130,132+50, "赐 福")
function 赐福按钮:左键弹起(x, y, msg)
  if 宝宝赐福.宝宝列表[宝宝赐福.操作宝宝编号] then
    print(11111111112)
    发送数据(5020,{rzm=宝宝赐福.宝宝列表[宝宝赐福.操作宝宝编号].认证码,锁定技能=宝宝赐福.锁定技能,新赐福=宝宝赐福.新赐福})
  end
end

local 保存技能 = 宝宝赐福["创建我的按钮18"](宝宝赐福,  __res:getPNGCC(5, 905, 276, 56, 30):拉伸(90, 35), "保存技能", 190+11+130-58-18,132+50, "保存技能")
function 保存技能:左键弹起(x, y, msg)
  if 宝宝赐福.宝宝列表[宝宝赐福.操作宝宝编号] then
    发送数据(5021,{rzm=宝宝赐福.宝宝列表[宝宝赐福.操作宝宝编号].认证码})
  end
end

local 再次赐福 = 宝宝赐福["创建我的按钮18"](宝宝赐福,  __res:getPNGCC(5, 905, 276, 56, 30):拉伸(90, 35), "再次赐福", 190+11+130+70-18,132+50, "再次赐福")
function 再次赐福:左键弹起(x, y, msg)
  if 宝宝赐福.宝宝列表[宝宝赐福.操作宝宝编号] then
    发送数据(5020,{rzm=宝宝赐福.宝宝列表[宝宝赐福.操作宝宝编号].认证码,锁定技能=宝宝赐福.锁定技能,新赐福=宝宝赐福.新赐福})
  end
end

for i=1,4 do
  local 临时函数 = 宝宝赐福["创建我的按钮18"](宝宝赐福,  __res:getPNGCC(5, 905, 276, 56, 30):拉伸(50, 32), "锁定按钮"..i, 213-192+(i-1)*83+190,44+5+50, "锁定")
  function 临时函数:左键按下(消息, x, y)
    宝宝赐福.锁定技能[i]=not 宝宝赐福.锁定技能[i]
    if 宝宝赐福.锁定技能[i] then
      --临时函数[i]:置文字("解锁")
      self:我的按钮18置文字(宝宝赐福["锁定按钮"..i],__res:getPNGCC(5, 905, 276, 56, 30):拉伸(50, 32), "解锁")
    else
      self:我的按钮18置文字(宝宝赐福["锁定按钮"..i],__res:getPNGCC(5, 905, 276, 56, 30):拉伸(50, 32), "锁定")
    end
  end
end

function 宝宝赐福:锁定按钮初始化()
  self.锁定技能={}
  for i=1,4 do
    self["锁定按钮"..i]:我的按钮18置文字(宝宝赐福["锁定按钮"..i],__res:getPNGCC(5, 905, 276, 56, 30):拉伸(50, 32), "锁定")
  end
end



local 技能列表按钮 = 宝宝赐福["创建我的按钮18"](宝宝赐福,  __res:getPNGCC(4, 45, 802, 92, 29), "技能列表按钮", 63+17, 424+5-369)
function 技能列表按钮:左键弹起(x, y, msg)
  if 宝宝赐福.宝宝列表[宝宝赐福.操作宝宝编号] then
    __UI弹出["召唤兽技能弹出"]:打开(宝宝赐福.宝宝列表[宝宝赐福.操作宝宝编号].技能)
  end
end



local 关闭 = 宝宝赐福["创建我的按钮"](宝宝赐福, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 665-40-48-87, 0)
function 关闭:左键弹起(x, y, msg)
  宝宝赐福["置可见"](宝宝赐福, false)
end