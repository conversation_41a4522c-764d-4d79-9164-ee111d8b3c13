
local 每日查看 = 窗口层:创建窗口("每日查看", 0, 0, 540, 335)



function 每日查看:初始化()
      self:创建纹理精灵(function()
      置窗口背景("每日福利", 0, 0, 540, 335,true):显示(0,0)
      蓝白标题背景( 520, 290, true):显示(10, 35)
      文本字体:置颜色(__取颜色("红色")):取图像("今日福利"):显示(460, 40)
     end
   )
     self.领取时间 = "未领取奖励"
     self.月卡物品={}
     self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
     self.可初始化=true
     if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
    else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
    end
    
  
end
function 每日查看:打开(data)
      self:置可见(true)
      self.领取时间 = "未领取奖励"
      self:刷新(data)
end
function 每日查看:刷新(数据)
      if 数据.月卡 ~= nil then
          if  数据.月卡.当前领取 and 数据.月卡.当前领取 ==  os.date("%j")  then
              self.领取时间 = "已领取奖励"
          else
              self.领取时间 = "未领取奖励"
          end
      end
      self.月卡物品={}
      if 数据.物品 and 数据.物品数量 and 数据.物品数量>0 then
          for i=1,数据.物品数量 do
                self.月卡物品[i]={}
                local 资源=取物品(数据.物品[i].名称)
                self.月卡物品[i].资源 = 资源[11]
                self.月卡物品[i].小动画= 资源[12]
                self.月卡物品[i].大动画= 资源[13]
                self.月卡物品[i].名称= 数据.物品[i].名称
                self.月卡物品[i].数量=tonumber(数据.物品[i].数量)
                self.月卡物品[i].介绍= 资源[1]
            end
      end
      if 数据.经验 and 数据.经验>0 then
          local 临时编号 = #self.月卡物品+1
          self.月卡物品[临时编号]={}
          local 资源=取物品("仙丹")
          self.月卡物品[临时编号].资源= 资源[11]
          self.月卡物品[临时编号].小动画= 资源[12]
          self.月卡物品[临时编号].大动画= 资源[13]
          self.月卡物品[临时编号].名称="经验"
          self.月卡物品[临时编号].数量=数额尾数转换(数据.经验)
          self.月卡物品[临时编号].介绍="#Y领取经验:"..数额尾数转换(数据.经验)
      end

      if 数据.储备 and  数据.储备>0 then
          local 临时编号 = #self.月卡物品+1
          self.月卡物品[临时编号]={}
          local 资源=取物品("储备")
          self.月卡物品[临时编号].资源= 资源[11]
          self.月卡物品[临时编号].小动画= 资源[12]
          self.月卡物品[临时编号].大动画= 资源[13]
          self.月卡物品[临时编号].名称="储备"
          self.月卡物品[临时编号].数量=数额尾数转换(数据.储备)
          self.月卡物品[临时编号].介绍="#Y领取储备:"..数额尾数转换(数据.储备)
      end

      if 数据.银子 and 数据.银子>0 then
            local 临时编号 = #self.月卡物品+1
            self.月卡物品[临时编号]={}
            local 资源=取物品("银子")
            self.月卡物品[临时编号].资源= 资源[11]
            self.月卡物品[临时编号].小动画= 资源[12]
            self.月卡物品[临时编号].大动画= 资源[13]
            self.月卡物品[临时编号].名称="银子"
            self.月卡物品[临时编号].数量=数额尾数转换(数据.银子)
            self.月卡物品[临时编号].介绍="#Y领取银子:"..数额尾数转换(数据.银子)
      end

      if 数据.仙玉 and 数据.仙玉>0 then
          local 临时编号 = #self.月卡物品+1
          self.月卡物品[临时编号]={}
          local 资源=取物品("仙玉")
          self.月卡物品[临时编号].资源= 资源[11]
          self.月卡物品[临时编号].小动画= 资源[12]
          self.月卡物品[临时编号].大动画= 资源[13]
          self.月卡物品[临时编号].名称="仙玉"
          self.月卡物品[临时编号].数量=数额尾数转换(数据.仙玉)
          self.月卡物品[临时编号].介绍="#Y领取仙玉:"..数额尾数转换(数据.仙玉)
      end

      if 数据.点卡  and 数据.点卡>0 then
        local 临时编号 = #self.月卡物品+1
        self.月卡物品[临时编号]={}
        local 资源=取物品("仙玉")
        self.月卡物品[临时编号].资源= 资源[11]
        self.月卡物品[临时编号].小动画= 资源[12]
        self.月卡物品[临时编号].大动画= 资源[13]
        self.月卡物品[临时编号].名称="点卡"
        self.月卡物品[临时编号].数量=数额尾数转换(数据.点卡)
        self.月卡物品[临时编号].介绍="#Y领取点卡:"..数额尾数转换(数据.点卡)
      end




      if 数据.抓鬼 and 数据.抓鬼>0 then
            local 临时编号 = #self.月卡物品+1
            self.月卡物品[临时编号]={}
            local 资源=取物品("会员卡")
            self.月卡物品[临时编号].资源= 资源[11]
            self.月卡物品[临时编号].小动画= 资源[12]
            self.月卡物品[临时编号].大动画= 资源[13]
            self.月卡物品[临时编号].名称="自动抓鬼"
            self.月卡物品[临时编号].数量=数额尾数转换(数据.抓鬼)
            self.月卡物品[临时编号].介绍="#Y领取抓鬼:"..数额尾数转换(数据.抓鬼)
      end
      self:显示刷新()
end

function 每日查看:显示刷新()

  self.图像 =self:创建纹理精灵(function()
      文本字体:置颜色(0,0,0,255):取图像("今日奖励领取: "..self.领取时间):显示(15,40)
    end,1
  )
  self.道具网格:置物品(self.月卡物品)
end



local 道具网格 = 每日查看:创建网格("道具网格", 30, 65, 510, 290)
function 道具网格:初始化()

end




function 道具网格:左键弹起(x, y, a)
  if 每日查看.月卡物品[a] and __手机 then
       __UI弹出.自定义提示:打开(每日查看.月卡物品[a],x+20,y+20)
    end
end


function 道具网格:获得鼠标(x, y, a)
  if 每日查看.月卡物品[a]  then
       __UI弹出.自定义提示:打开(每日查看.月卡物品[a],x+20,y+20)
    end
end


function 道具网格:置物品(数据)
  self:创建格子(55, 55, 30, 30, math.ceil(#数据/6), 6,math.ceil(#数据/6)>3)
  for i = 1, #self.子控件 do
      if 数据[i] then
        self.子控件[i]:创建纹理精灵(function()
            -- __res:getPNGCC(3, 442, 931, 200, 200):拉伸(60, 60):显示(0, 0)
            __res:取资源动画("jszy/dd",0x00000070,"图像"):拉伸(55, 55):显示(0, 0)
            __res:取资源动画(数据[i].资源, 数据[i].小动画,"图像"):显示(5, 5)
            文本字体:置颜色(__取颜色("白色"))
            文本字体:取投影图像("x"..数据[i].数量):显示((58-文本字体:取宽度("x"..数据[i].数量))//2,35)
          end
        )
          
      else
          self.子控件[i]:置精灵()
          
      end
    end
end




function 每日查看:显示(x,y)
   if self.图像 then
      self.图像:显示(x,y)
   end
end




local 领取奖励 = 每日查看:创建红色按钮("领取奖励", "领取奖励",235, 300, 74,22)
function 领取奖励:左键按下(x, y)
     请求服务(45,{文本="领取奖励"})
end

local 关闭 = 每日查看:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  每日查看:置可见(false)
end


