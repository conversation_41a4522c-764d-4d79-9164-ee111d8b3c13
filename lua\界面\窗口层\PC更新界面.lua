--[[
PC更新界面
功能: PC端专用的更新界面，提供直观的进度显示和状态提示
作者: AI Assistant
日期: 2025-06-23
版本: v1.0
--]]

local PC更新界面 = class("PC更新界面", 界面基类)

function PC更新界面:构造函数()
    界面基类.构造函数(self)
    
    self.界面名称 = "PC更新界面"
    self.显示状态 = false
    self.更新管理器 = nil
    
    -- UI元素
    self.背景 = nil
    self.标题文本 = nil
    self.状态文本 = nil
    self.进度条 = nil
    self.进度文本 = nil
    self.当前文件文本 = nil
    self.取消按钮 = nil
    self.确定按钮 = nil
    self.详情按钮 = nil
    
    -- 状态数据
    self.当前状态 = "就绪"
    self.进度信息 = {
        百分比 = 0,
        当前文件 = "",
        已完成 = 0,
        总数量 = 0
    }
    
    self:创建界面元素()
end

-- 创建界面元素
function PC更新界面:创建界面元素()
    -- 主背景
    self.背景 = 通用背景("shape/ui/zd4.png", 400, 300)
    self.背景:设置位置((__分辨率设置[__res.配置.分辨率][1] - 400) / 2, 
                      (__分辨率设置[__res.配置.分辨率][2] - 300) / 2)
    self:加入元素(self.背景)
    
    -- 标题
    self.标题文本 = 界面文本("JNHT 版本更新", 标题字体, {255, 255, 255})
    self.标题文本:设置位置(self.背景.X + 200, self.背景.Y + 30, "center")
    self:加入元素(self.标题文本)
    
    -- 状态显示
    self.状态文本 = 界面文本("正在检查版本...", 文本字体, {200, 200, 200})
    self.状态文本:设置位置(self.背景.X + 200, self.背景.Y + 80, "center")
    self:加入元素(self.状态文本)
    
    -- 进度条背景
    self.进度条背景 = 界面矩形(self.背景.X + 50, self.背景.Y + 120, 300, 20, {100, 100, 100})
    self:加入元素(self.进度条背景)
    
    -- 进度条
    self.进度条 = 界面矩形(self.背景.X + 50, self.背景.Y + 120, 0, 20, {0, 150, 255})
    self:加入元素(self.进度条)
    
    -- 进度文本
    self.进度文本 = 界面文本("0%", 文本字体, {255, 255, 255})
    self.进度文本:设置位置(self.背景.X + 200, self.背景.Y + 130, "center")
    self:加入元素(self.进度文本)
    
    -- 当前文件显示
    self.当前文件文本 = 界面文本("", 格子字体, {180, 180, 180})
    self.当前文件文本:设置位置(self.背景.X + 200, self.背景.Y + 160, "center")
    self:加入元素(self.当前文件文本)
    
    -- 按钮区域
    self:创建按钮()
end

-- 创建按钮
function PC更新界面:创建按钮()
    -- 取消按钮
    self.取消按钮 = 界面按钮("取消", 80, 30)
    self.取消按钮:设置位置(self.背景.X + 80, self.背景.Y + 220)
    self.取消按钮.点击事件 = function()
        self:取消更新()
    end
    self:加入元素(self.取消按钮)
    
    -- 确定按钮
    self.确定按钮 = 界面按钮("确定", 80, 30)
    self.确定按钮:设置位置(self.背景.X + 200, self.背景.Y + 220)
    self.确定按钮:设置可见性(false)
    self.确定按钮.点击事件 = function()
        self:关闭界面()
    end
    self:加入元素(self.确定按钮)
    
    -- 详情按钮
    self.详情按钮 = 界面按钮("详情", 80, 30)
    self.详情按钮:设置位置(self.背景.X + 320, self.背景.Y + 220)
    self.详情按钮.点击事件 = function()
        self:显示详情()
    end
    self:加入元素(self.详情按钮)
end

-- 设置更新管理器
function PC更新界面:设置更新管理器(管理器)
    self.更新管理器 = 管理器
    
    -- 注册状态监听器
    if 管理器.状态管理器 then
        管理器.状态管理器:添加监听器(self)
    end
end

-- 状态变化回调
function PC更新界面:状态变化(旧状态, 新状态, 数据)
    self.当前状态 = 新状态
    self:更新界面显示()
end

-- 进度更新回调
function PC更新界面:进度更新(进度信息)
    self.进度信息 = 进度信息
    self:更新进度显示()
end

-- 更新界面显示
function PC更新界面:更新界面显示()
    local 状态描述表 = {
        ["检查版本"] = "正在检查版本...",
        ["无需更新"] = "版本已是最新",
        ["准备下载"] = "准备下载更新...",
        ["正在下载"] = "正在下载更新文件...",
        ["校验文件"] = "正在校验文件...",
        ["安装更新"] = "正在安装更新...",
        ["更新完成"] = "更新完成",
        ["更新失败"] = "更新失败",
        ["已取消"] = "更新已取消"
    }
    
    local 描述 = 状态描述表[self.当前状态] or self.当前状态
    self.状态文本:设置文本(描述)
    
    -- 根据状态调整按钮显示
    self:调整按钮显示()
end

-- 更新进度显示
function PC更新界面:更新进度显示()
    local 进度 = self.进度信息
    
    -- 更新进度条
    local 进度宽度 = math.floor(300 * (进度.百分比 or 0) / 100)
    self.进度条:设置大小(进度宽度, 20)
    
    -- 更新进度文本
    local 进度文本 = string.format("%d%%", 进度.百分比 or 0)
    if 进度.已完成 and 进度.总数量 then
        进度文本 = 进度文本 .. string.format(" (%d/%d)", 进度.已完成, 进度.总数量)
    end
    self.进度文本:设置文本(进度文本)
    
    -- 更新当前文件显示
    if 进度.当前文件 and 进度.当前文件 ~= "" then
        local 文件名 = string.match(进度.当前文件, "/([^/]+)$") or 进度.当前文件
        self.当前文件文本:设置文本("正在处理: " .. 文件名)
    else
        self.当前文件文本:设置文本("")
    end
end

-- 调整按钮显示
function PC更新界面:调整按钮显示()
    local 状态 = self.当前状态
    
    if 状态 == "更新完成" or 状态 == "无需更新" then
        self.取消按钮:设置可见性(false)
        self.确定按钮:设置可见性(true)
    elseif 状态 == "更新失败" or 状态 == "已取消" then
        self.取消按钮:设置文本("关闭")
        self.确定按钮:设置可见性(false)
    else
        self.取消按钮:设置可见性(true)
        self.取消按钮:设置文本("取消")
        self.确定按钮:设置可见性(false)
    end
end

-- 显示界面
function PC更新界面:显示()
    self.显示状态 = true
    self:设置可见性(true)
    
    -- 重置显示状态
    self:更新界面显示()
    self:更新进度显示()
end

-- 隐藏界面
function PC更新界面:隐藏()
    self.显示状态 = false
    self:设置可见性(false)
end

-- 关闭界面
function PC更新界面:关闭界面()
    self:隐藏()
    
    -- 通知关闭事件
    if self.关闭回调 then
        self.关闭回调()
    end
end

-- 取消更新
function PC更新界面:取消更新()
    if self.当前状态 == "更新失败" or self.当前状态 == "已取消" then
        self:关闭界面()
        return
    end
    
    -- 确认取消
    if self:确认取消() then
        if self.更新管理器 then
            self.更新管理器:取消更新()
        end
    end
end

-- 确认取消对话框
function PC更新界面:确认取消()
    -- 简单的确认逻辑，实际项目中可以使用更复杂的对话框
    return true  -- 暂时直接返回true
end

-- 显示详情
function PC更新界面:显示详情()
    if not self.更新管理器 then
        return
    end
    
    local 详情信息 = self:收集详情信息()
    self:显示详情窗口(详情信息)
end

-- 收集详情信息
function PC更新界面:收集详情信息()
    local 详情 = {}
    
    if self.更新管理器 then
        table.insert(详情, "当前版本: " .. (self.更新管理器.当前版本 or "未知"))
        table.insert(详情, "服务器版本: " .. (self.更新管理器.服务器版本 or "未知"))
        table.insert(详情, "更新状态: " .. self.当前状态)
        
        -- 进度信息
        if self.进度信息 then
            table.insert(详情, "更新进度: " .. (self.进度信息.百分比 or 0) .. "%")
            if self.进度信息.总数量 > 0 then
                table.insert(详情, "文件进度: " .. (self.进度信息.已完成 or 0) .. "/" .. self.进度信息.总数量)
            end
        end
        
        -- 错误信息
        if self.更新管理器.状态管理器 then
            local 最新错误 = self.更新管理器.状态管理器:获取最新错误()
            if 最新错误 then
                table.insert(详情, "错误信息: " .. 最新错误.信息)
            end
        end
    end
    
    return 详情
end

-- 显示详情窗口
function PC更新界面:显示详情窗口(详情信息)
    -- 创建详情显示窗口
    print("=== 更新详情 ===")
    for _, 信息 in ipairs(详情信息) do
        print(信息)
    end
    print("===============")
end

-- 设置关闭回调
function PC更新界面:设置关闭回调(回调函数)
    self.关闭回调 = 回调函数
end

-- 更新界面事件处理
function PC更新界面:事件处理(事件)
    if not self.显示状态 then
        return false
    end
    
    -- 处理按键事件
    if 事件.类型 == "按键" then
        if 事件.按键 == "Escape" then
            self:取消更新()
            return true
        elseif 事件.按键 == "Enter" then
            if self.确定按钮:是否可见() then
                self:关闭界面()
            end
            return true
        end
    end
    
    return 界面基类.事件处理(self, 事件)
end

-- 界面基类的简化实现(如果不存在)
if not 界面基类 then
    界面基类 = class("界面基类")
    
    function 界面基类:构造函数()
        self.元素列表 = {}
        self.可见性 = true
    end
    
    function 界面基类:加入元素(元素)
        table.insert(self.元素列表, 元素)
    end
    
    function 界面基类:设置可见性(可见)
        self.可见性 = 可见
        for _, 元素 in ipairs(self.元素列表) do
            if 元素.设置可见性 then
                元素:设置可见性(可见)
            end
        end
    end
    
    function 界面基类:事件处理(事件)
        return false
    end
end

-- 简化的UI元素实现(如果不存在)
if not 界面文本 then
    界面文本 = class("界面文本")
    function 界面文本:构造函数(文本, 字体, 颜色)
        self.文本 = 文本
        self.字体 = 字体
        self.颜色 = 颜色
        self.X = 0
        self.Y = 0
    end
    function 界面文本:设置位置(x, y, 对齐)
        self.X = x
        self.Y = y
        self.对齐 = 对齐
    end
    function 界面文本:设置文本(文本)
        self.文本 = 文本
    end
    function 界面文本:设置可见性(可见)
        self.可见性 = 可见
    end
end

if not 界面矩形 then
    界面矩形 = class("界面矩形")
    function 界面矩形:构造函数(x, y, 宽度, 高度, 颜色)
        self.X = x
        self.Y = y
        self.宽度 = 宽度
        self.高度 = 高度
        self.颜色 = 颜色
    end
    function 界面矩形:设置大小(宽度, 高度)
        self.宽度 = 宽度
        self.高度 = 高度
    end
    function 界面矩形:设置可见性(可见)
        self.可见性 = 可见
    end
end

if not 界面按钮 then
    界面按钮 = class("界面按钮")
    function 界面按钮:构造函数(文本, 宽度, 高度)
        self.文本 = 文本
        self.宽度 = 宽度
        self.高度 = 高度
        self.X = 0
        self.Y = 0
        self.可见性 = true
    end
    function 界面按钮:设置位置(x, y)
        self.X = x
        self.Y = y
    end
    function 界面按钮:设置可见性(可见)
        self.可见性 = 可见
    end
    function 界面按钮:设置文本(文本)
        self.文本 = 文本
    end
    function 界面按钮:是否可见()
        return self.可见性
    end
end

return PC更新界面 