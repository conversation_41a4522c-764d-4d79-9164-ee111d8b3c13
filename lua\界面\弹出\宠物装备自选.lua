local 宠物装备自选 = class()
local zts,zts1,zts2
local 宠物装备正常属性={}
local 宠物装备附加属性={}
local 额外属性={"力量","魔力","体质","敏捷","耐力"}
local 额外属性1={"力量","魔力","体质","敏捷","耐力"}
local mmm = {"伤害","灵力","敏捷","防御","耐力","速度","体质","力量","魔力","气血"}
function 宠物装备自选:初始化(根)
	self.ID = 1008614
	self.x = 310
	self.y = 69
	self.xx = 0
	self.yy = 0
	self.注释 = "宠物装备自选"
	self.可视 = false
	self.鼠标 = false
	self.焦点 = false
	self.可移动 = true
	self.窗口时间 = 0
	zts1 = tp.字体表.普通字体
	zts2 = tp.字体表.普通字体
	tp = 根
	self.介绍文本 = 根._丰富文本(130,90)

end

function 宠物装备自选:取名称(名称)
	if  名称=="宠物铠甲自选礼包" then
		宠物装备正常属性={"防御"}
		宠物装备附加属性={"气血","伤害","灵力"}

		return "冰蚕织甲"

	elseif 名称=="宠物项圈自选礼包" then
		宠物装备正常属性={"速度"}
		宠物装备附加属性={"灵力","伤害","气血"}
		return "冰蚕丝圈"

	elseif 名称=="宠物护腕自选礼包" then
		宠物装备正常属性={"伤害"}
		宠物装备附加属性={"灵力","伤害","气血"}
		return "冰蚕丝带"
	end
end


function 宠物装备自选:打开(类型,o)
	self.当前格子=o
	self.扣除类型=类型
	if self.可视 then
		self.可视 = false
		self.资源组=nil
		zts =nil
	else
		zts = require("gge文字类").创建(nil,16,true,false,true)
		local item_mc = self:取名称(类型)
		local dj = 引擎.取物品(item_mc)

		self.道具 = {名称=item_mc,介绍=dj[1],词条={[1]={},[2]={},[3]={}},幻化属性={附加={},基础={},额外={},额外1={}}}
		self.灵饰类型 = 类型
		self.道具.大模型 = tp.资源:载入(dj[11],"网易WDF动画",dj[13])

		table.insert(tp.窗口_,self)
		local 按钮 = tp._按钮
		local 自适应 = tp._自适应
		local 资源 = tp.资源
		self.资源组 = {
		  [0] = 自适应.创建(4,1,300,160,3,9),
			[1] = 自适应.创建(0,1,310,440,3,9),--pwd("zxxt"), --背景
			[2] = 按钮.创建(自适应.创建(12,4,55,22,1,1),0,0,4,true,true,"提交"),
			[3] = 按钮.创建(自适应.创建(18,4,16,16,4,3),0,0,4,true,true),
			[10] = 自适应.创建(5,1,140,134,3,9),
		}
		tp.运行时间 = tp.运行时间 + 1
		self.窗口时间 = tp.运行时间
	    self.可视 = true
	    self.介绍文本:清空()
	    self.介绍文本:添加文本(self.道具.介绍)
		self.基础属性 = 按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true)
		self.附加属性 = 按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true)
		self.额外属性 = 按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true)
		self.额外属性1 = 按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true)
		self.词条属性1 = 按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true)
		self.词条属性2 = 按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true)
		self.词条属性3 = 按钮.创建(自适应.创建(25,4,19,19,4,3),0,0,4,true,true)
		self.选中行数 =1
	end
end

function 宠物装备自选:更新(dt)end


function 宠物装备自选:宠物装备词条属性1(类型)

	self.道具.词条[1] = {类型=类型,数值=self:取词条数值(self.灵饰类型,类型) or 0}
	self.选中行数=1
end

function 宠物装备自选:宠物装备词条属性2(类型)

	self.道具.词条[2] = {类型=类型,数值=self:取词条数值(self.灵饰类型,类型) or 0}
	self.选中行数=1
end

function 宠物装备自选:宠物装备词条属性3(类型)

	self.道具.词条[3] = {类型=类型,数值=self:取词条数值(self.灵饰类型,类型) or 0}
	self.选中行数=1
end

function 宠物装备自选:设置额外属性(类型)

	self.道具.幻化属性.额外 = {类型=类型,数值=self:取数值(self.灵饰类型,类型) or 0}
	self.选中行数=1
end

function 宠物装备自选:设置额外属性1(类型)

	self.道具.幻化属性.额外1 = {类型=类型,数值=self:取数值(self.灵饰类型,类型) or 0}
	self.选中行数=1
end
function 宠物装备自选:设置附加(类型)

	self.道具.幻化属性.附加 = {类型=类型,数值=self:取数值(self.灵饰类型,类型) or 0}
	self.选中行数=1
end
function 宠物装备自选:设置基础(类型)--没用到
	self.道具.幻化属性.基础 = {类型=类型,数值=self:取数值(self.灵饰类型,类型,1) or 0}
	self.选中行数=1

end


function 宠物装备自选:取词条数值(名称,类型,基础,附加,额外,额外1)
	  if  名称=="宠物铠甲自选礼包" then
    if 类型== "气血" or 类型== "魔法" and 附加 ~= nil  then
     	return 15  --这里15 就是百分之15
    elseif 类型 == "伤害" or 类型 =="灵力" or 类型 =="敏捷" or 类型 =="防御" or 类型 =="耐力" or 类型 =="速度" or 类型 =="体质" or 类型 =="力量" or 类型 =="魔力" then
        return 10 ----这里10 就是百分之10
    else
      return 155
    end

  elseif 名称=="宠物护腕自选礼包" then
    if 类型== "气血" or 类型== "魔法" and 附加 ~= nil  then
     	return 15  --这里15 就是百分之15
    elseif 类型 == "伤害" or 类型 =="灵力" or 类型 =="敏捷" or 类型 =="防御" or 类型 =="耐力" or 类型 =="速度" or 类型 =="体质" or 类型 =="力量" or 类型 =="魔力" then
        return 10 ----这里10 就是百分之10
    else
      return  155
    end

  elseif 名称=="宠物项圈自选礼包" then
   if 类型== "气血" or 类型== "魔法" and 附加 ~= nil  then
     	return 15  --这里15 就是百分之15
    elseif 类型 == "伤害" or 类型 =="灵力" or 类型 =="敏捷" or 类型 =="防御" or 类型 =="耐力" or 类型 =="速度" or 类型 =="体质" or 类型 =="力量" or 类型 =="魔力" then
        return 10 ----这里10 就是百分之10
    else
      return  155
     end
  end
  return {0,0}
end



function 宠物装备自选:取数值(名称,类型,基础,附加,额外,额外1)
	  if  名称=="宠物铠甲自选礼包" then
    if 类型 == "防御" and 基础 ~= nil then
      return 155
     elseif 类型== "气血" or 类型== "伤害" or 类型== "灵力" and 附加 ~= nil  then--#1
     	return 155
    elseif 类型 == "力量" or 类型 =="魔力" or 类型 =="体质" or 类型 =="敏捷" or 类型 =="耐力" then
        return 80
    else
      return 155
    end

  elseif 名称=="宠物护腕自选礼包" then
    if 类型 == "伤害"and 基础 ~= nil then
      return 155
    elseif 类型== "气血" or 类型== "伤害" or 类型== "灵力" and 附加 ~= nil then
      return 155
    elseif  类型 == "力量" or 类型 =="魔力" or 类型 =="体质" or 类型 =="敏捷" or 类型 =="耐力"   then
      return 80
    else
      return  155
    end

  elseif 名称=="宠物项圈自选礼包" then
    if 类型 == "速度" and 基础 ~= nil then
      return 155
    elseif 类型== "气血" or 类型== "伤害" or 类型== "灵力" and 附加 ~= nil then
      return 155
    elseif  类型 == "力量" or 类型 =="魔力" or 类型 =="体质" or 类型 =="敏捷" or 类型 =="耐力"    then
        return 80
    else
      return  155
     end
  end
  return {0,0}
end

function 宠物装备自选:显示(dt,x,y)
	self.焦点=false
	self.资源组[2]:更新(x,y)
	self.资源组[3]:更新(x,y)
	self.基础属性:更新(x,y)
	self.附加属性:更新(x,y)
	self.额外属性:更新(x,y)
	self.额外属性1:更新(x,y)
	self.词条属性1:更新(x,y)
	self.词条属性2:更新(x,y)
	self.词条属性3:更新(x,y)

	if self.基础属性:事件判断() then--##1
		tp.窗口.对话栏:文本("女人_万圣公主","宠物装备基础","请选择宠物装备基础属性",宠物装备正常属性)
	elseif self.附加属性:事件判断() then
	  tp.窗口.对话栏:文本("女人_万圣公主","宠物装备附加","请选择宠物装备附加属性",宠物装备附加属性)
	elseif self.额外属性:事件判断() then
		tp.窗口.对话栏:文本("女人_万圣公主","宠物装备额外","请选择宠物装备双加属性【1】",额外属性)
	elseif self.额外属性1:事件判断() then
		tp.窗口.对话栏:文本("女人_万圣公主","宠物装备额外1","请选择宠物装备双加属性【2】",额外属性1)
	elseif self.词条属性1:事件判断() then
		tp.窗口.对话栏:文本("女人_万圣公主","宠物装备词条属性1","请选择宠物装备词条属性【1】",mmm)
	elseif self.词条属性2:事件判断() then
		tp.窗口.对话栏:文本("女人_万圣公主","宠物装备词条属性2","请选择宠物装备词条属性【2】",mmm)
	elseif self.词条属性3:事件判断() then
		tp.窗口.对话栏:文本("女人_万圣公主","宠物装备词条属性3","请选择宠物装备词条属性【3】",mmm)
	end
	if self.资源组[3]:事件判断() then
		self:打开()
	elseif self.资源组[2]:事件判断() then
		if self.道具.幻化属性.基础.类型 == nil then
			tp.常规提示:打开("#Y/请选择装备基础属性")
			return
		 elseif self.道具.幻化属性.附加.类型 == nil then
			tp.常规提示:打开("#Y/请选择装备附加属性")
			return
		elseif self.道具.幻化属性.额外1.类型 == nil then
			tp.常规提示:打开("#Y/请选择装备双加[1]属性")
			return
		elseif self.道具.幻化属性.额外.类型 ==nil then
			tp.常规提示:打开("#Y/请选择装备双加[2]属性")
			return
		elseif self.道具.幻化属性.额外.类型 ==self.道具.幻化属性.额外1.类型 then
			tp.常规提示:打开("#Y/双加不可以选择相同属性")
			return
		elseif self.道具.词条[1].类型 == nil then
			tp.常规提示:打开("#Y/请选择词条1的类型")
			return
		elseif self.道具.词条[2].类型 ==nil then
			tp.常规提示:打开("#Y/请选择词条2的类型")
			return
		elseif self.道具.词条[3].类型 ==nil then
			tp.常规提示:打开("#Y/请选择词条3的类型")
			return
		-- elseif self.道具.词条[2].类型 ==self.道具.词条[1].类型 then
		-- 	tp.常规提示:打开("#Y/词条2不可与词条1相同")
		-- 	return
		-- elseif self.道具.词条[3].类型 ==self.道具.词条[2].类型 then
		-- 	tp.常规提示:打开("#Y/词条3不可与词条2相同")
		-- 	return
		-- elseif self.道具.词条[3].类型 ==self.道具.词条[1].类型 then
		-- 	tp.常规提示:打开("#Y/词条3不可与词条1相同")
			--return

		end
		-- local function 事件()
			-- local 临时={}
			-- local 临时1={}
			-- local nnn = {"基础","附加","额外","额外1"}
			-- local mmm = {"伤害","灵力","敏捷","防御","耐力","速度","体质","力量","魔力","气血"}
			-- for n=1,#self.道具.幻化属性.附加 do
			-- 	临时[#临时+1]=self.道具.幻化属性.附加[n].类型
			-- end
			-- for i = 1,4 do
			-- 	if self.道具.幻化属性[nnn[i]] then
			-- 		for k,v in next , self.道具.幻化属性[nnn[i]] do
			-- 			临时[#临时+1]=v
			-- 		end
			-- 	end
			-- end
			-- for i = 1,3 do
			-- 	if self.道具.词条[i] then
			-- 		for k,v in next , self.道具.词条[i] do
			-- 			临时1[#临时1+1]=v
			-- 		end
			-- 	end
			-- end
			--table.print(临时1)
			--print(self.道具.幻化属性.基础)
			--1,2,3,4,5,6,7,8
			--{"防御=155","气血"=155,"力量=60","耐力=60"}
			--最后你就是要调整这里的数字写入，
			-- local text = self.道具.名称.."*-*"..临时[1].."*-*"..临时[2].."*-*"
			-- text = text..临时[3].."*-*"..临时[4].."*-*"
			-- text = text..临时[5].."*-*"..临时[6].."*-*"
			-- text = text..临时[7].."*-*"..临时[8].."*-*"
			-- text = text..临时1[1].."*-*"..临时1[2].."*-*"
			-- text = text..临时1[3].."*-*"..临时1[4].."*-*"
			-- text = text..临时1[5].."*-*"..临时1[6].."*-*"
			-- text = text .. self.道具.幻化属性.基础.类型.."*-*"..self.扣除类型.."*-*"..self.当前格子.."*-*"..self.灵饰类型
			--text = text .. self.道具.词条[1]
			--table.print(self.道具.幻化属性)
			发送数据(3799,{道具名称=self.扣除类型,名称=self.道具.名称,幻化属性=self.道具.幻化属性,词条=self.道具.词条})
		 --	self:打开()
		-- 	return
		--end
		--tp.窗口.文本栏:载入("属性一旦选定无法更改,你确定要提交么?",nil,true,事件)
	end
	self.资源组[1]:显示(self.x,self.y)
	self.资源组[0]:显示(self.x+5,self.y+20)
	self.资源组[10]:显示(self.x+5,self.y+46)
	self.基础属性:显示(self.x+240,self.y+185)
	self.附加属性:显示(self.x+240,self.y+215)
	self.额外属性:显示(self.x+240,self.y+245)
	self.额外属性1:显示(self.x+240,self.y+275)
	self.词条属性1:显示(self.x+240,self.y+305)
	self.词条属性2:显示(self.x+240,self.y+335)
	self.词条属性3:显示(self.x+240,self.y+365)
	self.道具.大模型:显示(self.x+15,self.y+55)
	zts:置颜色(黄色)
	zts:显示(self.x+115,self.y+23,self.道具.名称)
	self.介绍文本:显示(self.x+160,self.y+60)
	--print(self.道具.幻化属性.额外1.类型)
	if self.道具.幻化属性.基础.类型 then
		zts2:置颜色(黄色)
		zts2:显示(self.x+70,self.y+185,self.道具.幻化属性.基础.类型.." + "..self.道具.幻化属性.基础.数值)
	end
	if self.道具.幻化属性.附加.类型 then

	  zts2:置颜色(黄色)
		zts2:显示(self.x+70,self.y+215,self.道具.幻化属性.附加.类型.." + "..self.道具.幻化属性.附加.数值)
	end
	if self.道具.幻化属性.额外.类型 then
		zts2:置颜色(黄色)
		zts2:显示(self.x+70,self.y+245,self.道具.幻化属性.额外.类型.." + "..self.道具.幻化属性.额外.数值)
	end
	if self.道具.幻化属性.额外1.类型 then
		zts2:置颜色(黄色)
		zts2:显示(self.x+70,self.y+275,self.道具.幻化属性.额外1.类型.." + "..self.道具.幻化属性.额外1.数值)
	end

	if self.道具.词条[1].类型 then
		zts2:置颜色(黄色)
		zts2:显示(self.x+70,self.y+305,self.道具.词条[1].类型.." + "..self.道具.词条[1].数值.."%")
	end

	if self.道具.词条[2].类型 then
		zts2:置颜色(黄色)
		zts2:显示(self.x+70,self.y+335,self.道具.词条[2].类型.." + "..self.道具.词条[2].数值.."%")
	end

	if self.道具.词条[3].类型 then
		zts2:置颜色(黄色)
		zts2:显示(self.x+70,self.y+365,self.道具.词条[3].类型.." + "..self.道具.词条[3].数值.."%")
	end


	self.资源组[2]:显示(self.x+130,self.y+415)
	self.资源组[3]:显示(self.x+290,self.y+3)
	zts1:置颜色(0xFF00FF00)

end

function 宠物装备自选:初始移动(x,y)
	tp.运行时间 = tp.运行时间 + 1
	if not tp.消息栏焦点 then
  		self.窗口时间 = tp.运行时间
 	end
	if not self.焦点 then
		tp.移动窗口 = true
	end
	if self.鼠标 and  not self.焦点 then
		self.xx = x - self.x
		self.yy = y - self.y
	end
end

function 宠物装备自选:开始移动(x,y)
	if self.鼠标 then
		self.x = x - self.xx
		self.y = y - self.yy


	end
end
function 宠物装备自选:检查点(x,y)
	if self.可视 and self.资源组[1]:是否选中(x,y)  then
		return true
	else
		return false
	end
end
return 宠物装备自选