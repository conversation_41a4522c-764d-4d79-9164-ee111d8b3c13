local 成长礼包 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "成长礼包", 80+67 + abbr.py.x, 20+42 + abbr.py.y, 555+29, 430)

function 成长礼包:初始化()
  local nsf = require("SDL.图像")(555+29, 430)
  if nsf["渲染开始"](nsf) then
    -- __res:getPNGCC(5, 0, 0, 683, 450):显示(0,5)
    -- 字体20:置颜色(__取颜色("浅黑"))
    -- 字体20:取图像("伙 伴"):显示(683/2-20,9)
    置窗口背景("成长礼包", 0, 12, 540+29,415, true):显示(0, 0)
    local xian=__res:getPNGCC(4, 550, 334, 140, 3):拉伸(471+23, 3)
    xian:显示(20, 122)
    xian:显示(20, 122+73)
    xian:显示(20, 122+73*2)
    xian:显示(20, 122+73*3)
    字体18:置颜色(__取颜色("白色"))
    字体18:取图像("20级"):显示(20,79)
    字体18:取图像("30级"):显示(20,79+73)
    字体18:取图像("40级"):显示(20,79+73*2)
    字体18:取图像("50级"):显示(20,79+73*3)
    字体18:取图像("60级"):显示(20,79+73*4)
    --取白色背景(0, 0, 440, 300, true)["显示"](取白色背景(0, 0, 440, 300, true), 20, 56)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
-- local wuping={
--   [1]={名称="40装备礼包"},--技能书
--   [2]={名称="50W储备金"}, --特技书 新手宝宝
--   [3]={名称="新手宝宝"},
--   [4]={名称="地载阵"},-- 金砖 回梦丹
--   [5]={名称="红色合成旗"},

--   [6]={名称="种族坐骑"},
--   [7]={名称="1级宝石礼包"},
--   [8]={名称="100W储备金"},
--   [9]={名称="高品九转*2"},
--   [10]={名称="修炼果*2"},

--   [11]={名称="90装备礼包"},
--   [12]={名称="2级宝石礼包"}, --2级宝石礼包
--   [13]={名称="新手宝宝"},
--   [14]={名称="高品九转*4"},
--   [15]={名称="测试经验"},

--   [16]={名称="回梦丹"},
--   [17]={名称="3级宝石礼包"},
--   [18]={名称="黄色合成旗"},
--   [19]={名称="高品九转*6"},
--   [20]={名称="修炼果*6"},

--   [21]={名称="100装备礼包"},
--   [22]={名称="新春飞行符"},
--   [23]={名称="新手宝宝"},
--   [24]={名称="秘宝宝箱"},
--   [25]={名称="机缘宝箱"},
--  }
 local wuping={}
 wuping={
  [1]={名称="秘制红罗羹"},--技能书
  [2]={名称="50W储备金"}, --特技书 新手宝宝
  [3]={名称="秘制绿芦羹"},
  [4]={名称="地载阵"},-- 金砖 回梦丹
  [5]={名称="红色合成旗"},

  [6]={名称="种族坐骑"},
  [7]={名称="40装备礼包"},
  [8]={名称="100W储备金"},
  [9]={名称="高品九转*2"},
  [10]={名称="修炼果*2"},

  [11]={名称="修炼果*2"},
  [12]={名称="1级宝石礼包"}, --2级宝石礼包
  [13]={名称="高品九转*2"},
  [14]={名称="回梦丹"},
  [15]={名称="100W储备金"},

  [16]={名称="60装备礼包"},
  [17]={名称="2级宝石礼包"},
  [18]={名称="黄色合成旗"},
  [19]={名称="高品九转*5"},
  [20]={名称="修炼果*5"},

  [21]={名称="60灵饰礼包"},
  [22]={名称="3级宝石礼包"},
  [23]={名称="新春飞行符"},
  [24]={名称="秘宝宝箱"},
  [25]={名称="机缘宝箱"},
 }


function 成长礼包:打开(内容)
  self:置可见(true)
  self:sdfsdaa(内容)
end

function 成长礼包:sdfsdaa(内容)
  if 内容.是否领取 then
    if 内容.是否领取.礼包一 then
      self.领取按钮1:置禁止(true)
      for i=1,5 do
        wuping[i].领取=true
      end
    end
    if 内容.是否领取.礼包二 then
      self.领取按钮2:置禁止(true)
      for i=6,10 do
        wuping[i].领取=true
      end
    end
    if 内容.是否领取.礼包三 then
      self.领取按钮3:置禁止(true)
      for i=11,15 do
        wuping[i].领取=true
      end
    end
    if 内容.是否领取.礼包四 then
      self.领取按钮4:置禁止(true)
      for i=16,20 do
        wuping[i].领取=true
      end
    end
    if 内容.是否领取.礼包五 then
      self.领取按钮5:置禁止(true)
      for i=21,25 do
        wuping[i].领取=true
      end
    end
  end
  self.物品wangge:置数据()
end

function 成长礼包:更新礼包(内容)
  if 内容 then
    -- table.print(内容)
    if 内容.内容.礼包一 then
      self.领取按钮1:置禁止(true)
      for i=1,5 do
        wuping[i].领取=true
      end
    end
    if 内容.内容.礼包二 then
      print(1111111)
      self.领取按钮2:置禁止(true)
      for i=6,10 do
        wuping[i].领取=true
      end
    end
    if 内容.内容.礼包三 then
      self.领取按钮3:置禁止(true)
      for i=11,15 do
        wuping[i].领取=true
      end
    end
    if 内容.内容.礼包四 then
      self.领取按钮4:置禁止(true)
      for i=16,20 do
        wuping[i].领取=true
      end
    end
    if 内容.内容.礼包五 then
      self.领取按钮5:置禁止(true)
      for i=21,25 do
        wuping[i].领取=true
      end
    end
  end
  self.物品wangge:置数据()
end


local 物品wangge = 成长礼包["创建网格"](成长礼包, "物品wangge", 94-25, 69+16-25, 400, 350)
function 物品wangge:初始化()
  self:创建格子(55, 55, 18, 27, 5, 5)
end
function 物品wangge:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 0, 86, w, h, "选择", a)
  end
end

function 物品wangge:置数据()
  for i = 1, #self.子控件 do
    if wuping[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, wuping[i], "白格子","无需显示数量",wuping[i].领取)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i], nil)
    end
  end
end

local 领取按钮1 = 成长礼包["创建我的按钮"](成长礼包, __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(75,35), "领取按钮1", 476-9, 79-10+73*0,"领取")
function 领取按钮1:左键弹起(x, y, msg)
  if 调试模式 or 角色信息.等级>=20 then 
    发送数据(94,{礼包=20})
  else
    __UI弹出.提示框:打开("#Y/等级未达到要求！")
  end
end
local 领取按钮2 = 成长礼包["创建我的按钮"](成长礼包, __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(75,35), "领取按钮2", 476-9, 79-10+73*1,"领取")
function 领取按钮2:左键弹起(x, y, msg)
  if 调试模式 or 角色信息.等级>=20 then 
    发送数据(94,{礼包=30})
  else
    __UI弹出.提示框:打开("#Y/等级未达到要求！")
  end
end
local 领取按钮3 = 成长礼包["创建我的按钮"](成长礼包, __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(75,35), "领取按钮3", 476-9, 79-10+73*2,"领取")
function 领取按钮3:左键弹起(x, y, msg)
  if 调试模式 or 角色信息.等级>=20 then 
    发送数据(94,{礼包=40})
  else
    __UI弹出.提示框:打开("#Y/等级未达到要求！")
  end
end
local 领取按钮4 = 成长礼包["创建我的按钮"](成长礼包, __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(75,35), "领取按钮4", 476-9, 79-10+73*3,"领取")
function 领取按钮4:左键弹起(x, y, msg)
  if 调试模式 or 角色信息.等级>=20 then 
    发送数据(94,{礼包=50})
  else
    __UI弹出.提示框:打开("#Y/等级未达到要求！")
  end
end
local 领取按钮5 = 成长礼包["创建我的按钮"](成长礼包, __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(75,35), "领取按钮5", 476-9, 79-10+73*4,"领取")
function 领取按钮5:左键弹起(x, y, msg)
  if 调试模式 or 角色信息.等级>=20 then 
    发送数据(94,{礼包=60})
  else
    __UI弹出.提示框:打开("#Y/等级未达到要求！")
  end
end

local 关闭 = 成长礼包["创建我的按钮"](成长礼包, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 500+29, 0)
function 关闭:左键弹起(x, y, msg)
  成长礼包["置可见"](成长礼包, false)
end