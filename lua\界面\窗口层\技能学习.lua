--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-31 22:57:27
    @Last Modified by: GGELUA
    @Last Modified time: 2022-11-01 06:38:27
--]]
local 技能学习 = __UI界面.窗口层:创建我的窗口("技能学习", 98 + abbr.py.x, 14 + abbr.py.y, 773, 502)
local 技能消耗 = {}
技能消耗[1] = {
  经验 = 16,
  金钱 = 6
}
技能消耗[2] = {
  经验 = 32,
  金钱 = 12
}
技能消耗[3] = {
  经验 = 52,
  金钱 = 19
}
技能消耗[4] = {
  经验 = 75,
  金钱 = 28
}
技能消耗[5] = {
  经验 = 103,
  金钱 = 38
}
技能消耗[6] = {
  经验 = 136,
  金钱 = 51
}
技能消耗[7] = {
  经验 = 179,
  金钱 = 67
}
技能消耗[8] = {
  经验 = 231,
  金钱 = 86
}
技能消耗[9] = {
  经验 = 295,
  金钱 = 110
}
技能消耗[10] = {
  经验 = 372,
  金钱 = 139
}
技能消耗[11] = {
  经验 = 466,
  金钱 = 174
}
技能消耗[12] = {
  经验 = 578,
  金钱 = 216
}
技能消耗[13] = {
  经验 = 711,
  金钱 = 266
}
技能消耗[14] = {
  经验 = 867,
  金钱 = 325
}
技能消耗[15] = {
  经验 = 1049,
  金钱 = 393
}
技能消耗[16] = {
  经验 = 1260,
  金钱 = 472
}
技能消耗[17] = {
  经验 = 1503,
  金钱 = 563
}
技能消耗[18] = {
  经验 = 1780,
  金钱 = 667
}
技能消耗[19] = {
  经验 = 2096,
  金钱 = 786
}
技能消耗[20] = {
  经验 = 2452,
  金钱 = 919
}
技能消耗[21] = {
  经验 = 2854,
  金钱 = 1070
}
技能消耗[22] = {
  经验 = 3304,
  金钱 = 1238
}
技能消耗[23] = {
  经验 = 3807,
  金钱 = 1426
}
技能消耗[24] = {
  经验 = 4364,
  金钱 = 1636
}
技能消耗[25] = {
  经验 = 4983,
  金钱 = 1868
}
技能消耗[26] = {
  经验 = 5664,
  金钱 = 2124
}
技能消耗[27] = {
  经验 = 6415,
  金钱 = 2404
}
技能消耗[28] = {
  经验 = 7238,
  金钱 = 2714
}
技能消耗[29] = {
  经验 = 8138,
  金钱 = 3050
}
技能消耗[30] = {
  经验 = 9120,
  金钱 = 3420
}
技能消耗[31] = {
  经验 = 10188,
  金钱 = 3820
}
技能消耗[32] = {
  经验 = 11347,
  金钱 = 4255
}
技能消耗[33] = {
  经验 = 12602,
  金钱 = 4725
}
技能消耗[34] = {
  经验 = 13959,
  金钱 = 5234
}
技能消耗[35] = {
  经验 = 15423,
  金钱 = 5783
}
技能消耗[36] = {
  经验 = 16998,
  金钱 = 6374
}
技能消耗[37] = {
  经验 = 18692,
  金钱 = 7009
}
技能消耗[38] = {
  经验 = 20508,
  金钱 = 7690
}
技能消耗[39] = {
  经验 = 22452,
  金钱 = 8419
}
技能消耗[40] = {
  经验 = 24532,
  金钱 = 9199
}
技能消耗[41] = {
  经验 = 26753,
  金钱 = 10032
}
技能消耗[42] = {
  经验 = 29121,
  金钱 = 10920
}
技能消耗[43] = {
  经验 = 31642,
  金钱 = 11865
}
技能消耗[44] = {
  经验 = 34323,
  金钱 = 12871
}
技能消耗[45] = {
  经验 = 37169,
  金钱 = 13938
}
技能消耗[46] = {
  经验 = 40188,
  金钱 = 15070
}
技能消耗[47] = {
  经验 = 43388,
  金钱 = 16270
}
技能消耗[48] = {
  经验 = 46773,
  金钱 = 17540
}
技能消耗[49] = {
  经验 = 50352,
  金钱 = 18882
}
技能消耗[50] = {
  经验 = 54132,
  金钱 = 20299
}
技能消耗[51] = {
  经验 = 58120,
  金钱 = 21795
}
技能消耗[52] = {
  经验 = 62324,
  金钱 = 23371
}
技能消耗[53] = {
  经验 = 66750,
  金钱 = 25031
}
技能消耗[54] = {
  经验 = 71407,
  金钱 = 26777
}
技能消耗[55] = {
  经验 = 76303,
  金钱 = 28613
}
技能消耗[56] = {
  经验 = 81444,
  金钱 = 30541
}
技能消耗[57] = {
  经验 = 86840,
  金钱 = 32565
}
技能消耗[58] = {
  经验 = 92500,
  金钱 = 34687
}
技能消耗[59] = {
  经验 = 98430,
  金钱 = 36911
}
技能消耗[60] = {
  经验 = 104640,
  金钱 = 39240
}
技能消耗[61] = {
  经验 = 111136,
  金钱 = 41676
}
技能消耗[62] = {
  经验 = 117931,
  金钱 = 44224
}
技能消耗[63] = {
  经验 = 125031,
  金钱 = 46886
}
技能消耗[64] = {
  经验 = 132444,
  金钱 = 49666
}
技能消耗[65] = {
  经验 = 140183,
  金钱 = 52568
}
技能消耗[66] = {
  经验 = 148253,
  金钱 = 55595
}
技能消耗[67] = {
  经验 = 156666,
  金钱 = 58749
}
技能消耗[68] = {
  经验 = 165430,
  金钱 = 62036
}
技能消耗[69] = {
  经验 = 174556,
  金钱 = 65458
}
技能消耗[70] = {
  经验 = 184052,
  金钱 = 69019
}
技能消耗[71] = {
  经验 = 193930,
  金钱 = 72723
}
技能消耗[72] = {
  经验 = 204198,
  金钱 = 76574
}
技能消耗[73] = {
  经验 = 214868,
  金钱 = 80575
}
技能消耗[74] = {
  经验 = 225948,
  金钱 = 84730
}
技能消耗[75] = {
  经验 = 237449,
  金钱 = 89043
}
技能消耗[76] = {
  经验 = 249383,
  金钱 = 93518
}
技能消耗[77] = {
  经验 = 261760,
  金钱 = 98160
}
技能消耗[78] = {
  经验 = 274589,
  金钱 = 102971
}
技能消耗[79] = {
  经验 = 287884,
  金钱 = 107956
}
技能消耗[80] = {
  经验 = 301652,
  金钱 = 113119
}
技能消耗[81] = {
  经验 = 315908,
  金钱 = 118465
}
技能消耗[82] = {
  经验 = 330662,
  金钱 = 123998
}
技能消耗[83] = {
  经验 = 345924,
  金钱 = 129721
}
技能消耗[84] = {
  经验 = 361708,
  金钱 = 135640
}
技能消耗[85] = {
  经验 = 378023,
  金钱 = 141758
}
技能消耗[86] = {
  经验 = 394882,
  金钱 = 148080
}
技能消耗[87] = {
  经验 = 412297,
  金钱 = 154611
}
技能消耗[88] = {
  经验 = 430280,
  金钱 = 161355
}
技能消耗[89] = {
  经验 = 448844,
  金钱 = 168316
}
技能消耗[90] = {
  经验 = 468000,
  金钱 = 175500
}
技能消耗[91] = {
  经验 = 487760,
  金钱 = 182910
}
技能消耗[92] = {
  经验 = 508137,
  金钱 = 190551
}
技能消耗[93] = {
  经验 = 529145,
  金钱 = 198429
}
技能消耗[94] = {
  经验 = 550796,
  金钱 = 206548
}
技能消耗[95] = {
  经验 = 573103,
  金钱 = 214913
}
技能消耗[96] = {
  经验 = 596078,
  金钱 = 223529
}
技能消耗[97] = {
  经验 = 619735,
  金钱 = 232400
}
技能消耗[98] = {
  经验 = 644088,
  金钱 = 241533
}
技能消耗[99] = {
  经验 = 669149,
  金钱 = 250931
}
技能消耗[100] = {
  经验 = 694932,
  金钱 = 260599
}
技能消耗[101] = {
  经验 = 721452,
  金钱 = 270544
}
技能消耗[102] = {
  经验 = 748722,
  金钱 = 280770
}
技能消耗[103] = {
  经验 = 776755,
  金钱 = 291283
}
技能消耗[104] = {
  经验 = 805566,
  金钱 = 302087
}
技能消耗[105] = {
  经验 = 835169,
  金钱 = 313188
}
技能消耗[106] = {
  经验 = 865579,
  金钱 = 324592
}
技能消耗[107] = {
  经验 = 896809,
  金钱 = 336303
}
技能消耗[108] = {
  经验 = 928876,
  金钱 = 348328
}
技能消耗[109] = {
  经验 = 961792,
  金钱 = 360672
}
技能消耗[110] = {
  经验 = 995572,
  金钱 = 373339
}
技能消耗[111] = {
  经验 = 1030234,
  金钱 = 386337
}
技能消耗[112] = {
  经验 = 1065190,
  金钱 = 399671
}
技能消耗[113] = {
  经验 = 1102256,
  金钱 = 413346
}
技能消耗[114] = {
  经验 = 1139649,
  金钱 = 427368
}
技能消耗[115] = {
  经验 = 1177983,
  金钱 = 441743
}
技能消耗[116] = {
  经验 = 1217273,
  金钱 = 456477
}
技能消耗[117] = {
  经验 = 1256104,
  金钱 = 471576
}
技能消耗[118] = {
  经验 = 1298787,
  金钱 = 487045
}
技能消耗[119] = {
  经验 = 1341043,
  金钱 = 502891
}
技能消耗[120] = {
  经验 = 1384320,
  金钱 = 519120
}
技能消耗[121] = {
  经验 = 1428632,
  金钱 = 535737
}
技能消耗[122] = {
  经验 = 1473999,
  金钱 = 552749
}
技能消耗[123] = {
  经验 = 1520435,
  金钱 = 570163
}
技能消耗[124] = {
  经验 = 1567957,
  金钱 = 587984
}
技能消耗[125] = {
  经验 = 1616583,
  金钱 = 606218
}
技能消耗[126] = {
  经验 = 1666328,
  金钱 = 624873
}
技能消耗[127] = {
  经验 = 1717211,
  金钱 = 643954
}
技能消耗[128] = {
  经验 = 1769248,
  金钱 = 663468
}
技能消耗[129] = {
  经验 = 1822456,
  金钱 = 683421
}
技能消耗[130] = {
  经验 = 1876852,
  金钱 = 703819
}
技能消耗[131] = {
  经验 = 1932456,
  金钱 = 724671
}
技能消耗[132] = {
  经验 = 1989284,
  金钱 = 745981
}
技能消耗[133] = {
  经验 = 2047353,
  金钱 = 767757
}
技能消耗[134] = {
  经验 = 2106682,
  金钱 = 790005
}
技能消耗[135] = {
  经验 = 2167289,
  金钱 = 812733
}
技能消耗[136] = {
  经验 = 2229192,
  金钱 = 835947
}
技能消耗[137] = {
  经验 = 2292410,
  金钱 = 859653
}
技能消耗[138] = {
  经验 = 2356960,
  金钱 = 883860
}
技能消耗[139] = {
  经验 = 2422861,
  金钱 = 908573
}
技能消耗[140] = {
  经验 = 2490132,
  金钱 = 933799
}
技能消耗[141] = {
  经验 = 2558792,
  金钱 = 959547
}
技能消耗[142] = {
  经验 = 2628860,
  金钱 = 985822
}
技能消耗[143] = {
  经验 = 2700356,
  金钱 = 1012633
}
技能消耗[144] = {
  经验 = 2773296,
  金钱 = 1039986
}
技能消耗[145] = {
  经验 = 2847703,
  金钱 = 1067888
}
技能消耗[146] = {
  经验 = 2923593,
  金钱 = 1096347
}
技能消耗[147] = {
  经验 = 3000989,
  金钱 = 1125371
}
技能消耗[148] = {
  经验 = 3079908,
  金钱 = 1154965
}
技能消耗[149] = {
  经验 = 3160372,
  金钱 = 1185139
}
技能消耗[150] = {
  经验 = 3242400,
  金钱 = 1215900
}
技能消耗[151] = {
  经验 = 6652022,
  金钱 = 2494508
}
技能消耗[152] = {
  经验 = 6822452,
  金钱 = 2558419
}
技能消耗[153] = {
  经验 = 6996132,
  金钱 = 2623549
}
技能消耗[154] = {
  经验 = 7173104,
  金钱 = 2689914
}
技能消耗[155] = {
  经验 = 7353406,
  金钱 = 2757527
}
技能消耗[156] = {
  经验 = 11305620,
  金钱 = 4239607
}
技能消耗[157] = {
  经验 = 11586254,
  金钱 = 4344845
}
技能消耗[158] = {
  经验 = 11872072,
  金钱 = 4452027
}
技能消耗[159] = {
  经验 = 12163140,
  金钱 = 4561177
}
技能消耗[160] = {
  经验 = 12459518,
  金钱 = 4672319
}
技能消耗[161] = {
  经验 = 15033471,
  金钱 = 450041
}
技能消耗[162] = {
  经验 = 15315219,
  金钱 = 4594563
}
技能消耗[163] = {
  经验 = 15600468,
  金钱 = 4680138
}
技能消耗[164] = {
  经验 = 15889236,
  金钱 = 4766769
}
技能消耗[165] = {
  经验 = 16181550,
  金钱 = 4854465
}
技能消耗[166] = {
  经验 = 16477425,
  金钱 = 4943226
}
技能消耗[167] = {
  经验 = 16776885,
  金钱 = 5033064
}
技能消耗[168] = {
  经验 = 17079954,
  金钱 = 5123985
}
技能消耗[169] = {
  经验 = 17386650,
  金钱 = 5215995
}
技能消耗[170] = {
  经验 = 17697000,
  金钱 = 5309100
}
技能消耗[171] = {
  经验 = 24014692,
  金钱 = 7204407
}
技能消耗[172] = {
  经验 = 24438308,
  金钱 = 7331490
}
技能消耗[173] = {
  经验 = 24866880,
  金钱 = 7460064
}
技能消耗[174] = {
  经验 = 25300432,
  金钱 = 7590129
}
技能消耗[175] = {
  经验 = 25739000,
  金钱 = 7721700
}
技能消耗[176] = {
  经验 = 32728255,
  金钱 = 9818475
}
技能消耗[177] = {
  经验 = 33289095,
  金钱 = 9986727
}
技能消耗[178] = {
  经验 = 33856310,
  金钱 = 10156893
}
技能消耗[179] = {
  经验 = 34492930,
  金钱 = 10328979
}
技能消耗[180] = {
  经验 = 40842000,
  金钱 = 12252600
}
function 技能学习:初始化()
  local nsf = require("SDL.图像")(773, 502)
  if nsf:渲染开始() then
    置窗口背景("技能学习", 0, 12, 765, 490, true):显示(0, 0)
    取白色背景(0, 0, 295, 433, true):显示(20, 50)
    取白色背景(0, 0, 410, 100, true):显示(320, 50)
    local lssj = 取输入背景(0, 0, 124, 23)
    字体18:置颜色(__取颜色("黑色"))
    for _, v in ipairs({
      {
        name = "技能",
        x = 89,
        y = 58
      },
      {
        name = "等级",
        x = 238,
        y = 58
      },
      {
        name = "相关法术",
        x = 498,
        y = 159,
        colour = "白色"
      },
      {
        name = "可用经验",
        x = 325,
        y = 357,
        input = true
      },
      {
        name = "所需经验",
        x = 540,
        y = 357,
        input = true
      },
      {
        name = "可用金钱",
        x = 325,
        y = 388,
        input = true
      },
      {
        name = "所需金钱",
        x = 540,
        y = 388,
        input = true
      },
      {
        name = "存  款",
        x = 325,
        y = 419,
        input = true
      },
      {
        name = "储 备 金",
        x = 540,
        y = 419,
        input = true
      }
    }) do
      if v.colour then
        字体18:置颜色(__取颜色(v.colour))
      end
      字体18:取图像(v.name):显示(v.x, v.y)
      if v.input then
        lssj:显示(v.x + 85, v.y - 2)
      end
    end
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
end
function 技能学习:重置(level)
  local nsf = require("SDL.图像")(430, 100)
  if nsf:渲染开始() then
    字体18:置颜色(__取颜色("黑色"))
    for _, v in ipairs({
      {
        name = "可用经验",
        dy = "当前经验",
        x = 103,
        y = 22,
        input = true
      },
      {
        name = "所需经验",
        x = 318,
        y = 22,
        input = true
      },
      {
        name = "可用金钱",
        dy = "银子",
        x = 103,
        y = 52,
        input = true
      },
      {
        name = "所需金钱",
        x = 318,
        y = 52,
        input = true
      },
      {
        name = "存  款",
        dy = "存银",
        x = 103,
        y = 84,
        input = true
      },
      {
        name = "储 备 金",
        dy = "储备",
        x = 318,
        y = 84,
        input = true
      }
    }) do
      if level then
        if v.name == "所需经验" then
          字体18:取图像(技能消耗[level + 1].经验 or 0):显示(v.x, v.y)
        elseif v.name == "所需金钱" then
          字体18:取图像(技能消耗[level + 1].金钱 or 0):显示(v.x, v.y)
        else
          字体18:取图像(角色信息[v.dy]):显示(v.x, v.y)
        end
      elseif v.name == "所需经验" then
        字体18:取图像(0):显示(v.x, v.y)
      elseif v.name == "所需金钱" then
        字体18:取图像(0):显示(v.x, v.y)
      else
        字体18:取图像(角色信息[v.dy]):显示(v.x, v.y)
      end
    end
    nsf:渲染结束()
  end
  self.图像 = nsf:到精灵()
  self.图像:置中心(-317, -338)
end
function 技能学习:打开()
  if 角色信息.门派 == "无门派" then
    __UI弹出.提示框:打开("#Y/拜入门派才可以学习师门技能")
    return false
  end
  self:置可见(true)
  self.技能列表:重置()
  self:重置()
  self.选中 = nil
end
local 关闭 = 技能学习:创建我的按钮(__res:getPNGCC(1, 401, 0, 46, 46), "关闭", 723, 0)
function 关闭:左键弹起(x, y, msg)
  技能学习:置可见(false)
  技能学习.技能文本:清空()
  技能学习.包含网格:重置({})
end
local 技能文本 = 技能学习:创建我的文本("技能文本", 345, 60, 384, 88, true)
local 技能列表 = 技能学习:创建列表("技能列表", 22, 86, 292, 350)
function 技能列表:初始化()
  self:置文字(字体20)
  self.行高度 = 50
  self.行间距 = 0
end
function 技能列表:重置()
  local tbsj = {}
  self:清空()
  for _, v in ipairs(角色信息.师门技能) do
    local nsf = require("SDL.图像")(292, 50)
    if nsf:渲染开始() then
      local skill = 取技能(v.名称)
      local wenj="shape/jn/"
            if skill[10] then
                wenj="shape/xinzengsucai/"
            end
      __res:取图像(__res:取地址(wenj, skill[8])):拉伸(30, 30):显示(11, 10)
      字体16:置颜色(__取颜色("浅黑"))
      字体16:取图像(v.名称):显示(54, 15)
      字体16:取图像(v.等级 .. "/180"):显示(200, 15)
      nsf:渲染结束()
    end
    local r = self:添加()
    r:置精灵(nsf:到精灵())
  end
end
function 技能列表:左键弹起(x, y, i, item, msg)
  local skill = 取技能(角色信息.师门技能[i].名称)
  技能文本:清空()
  技能文本:置文本("#G" .. 角色信息.师门技能[i].名称)
  技能文本:置文本("#K" .. (skill[1] or "无效果"))
  技能学习.包含网格:重置(角色信息.师门技能[i].包含技能)
  技能学习:重置(角色信息.师门技能[i].等级)
  技能学习.选中 = i
end
local 学习 = 技能学习:创建我的按钮(__res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(123, 41), "学习", 475-104, 450, "学习")
function 学习:左键弹起(x, y, msg)
  if 技能学习.选中 and 角色信息.师门技能[技能学习.选中].等级 < 180 and 技能消耗[角色信息.师门技能[技能学习.选中].等级 + 1] then
    local lssj = 技能消耗[角色信息.师门技能[技能学习.选中].等级 + 1]
    local allow = false
    if 角色信息.当前经验 >= lssj.经验 then
      if 角色信息.师门技能[技能学习.选中].等级 >= 角色信息.等级 + 10 then
        __UI弹出.提示框:打开("#Y师门技能不能超过角色等级+10")
        return
      end
      if 角色信息.储备 >= lssj.金钱 then
        角色信息.储备 = 角色信息.储备 - lssj.金钱
        allow = true
      elseif 角色信息.银子 >= lssj.金钱 then
        角色信息.银子 = 角色信息.银子 - lssj.金钱
        allow = true
      elseif 角色信息.储备 + 角色信息.银子 >= lssj.金钱 then
        lssj.金钱 = lssj.金钱 - 角色信息.储备
        角色信息.储备 = 0
        角色信息.银子 = 角色信息.银子 - lssj.金钱
        allow = true
      elseif 角色信息.储备 + 角色信息.银子 + 角色信息.存银 >= lssj.金钱 then
        lssj.金钱 = lssj.金钱 - 角色信息.储备 - 角色信息.银子
        角色信息.储备 = 0
        角色信息.银子 = 0
        角色信息.存银 = 角色信息.存银 - lssj.金钱
        allow = true
      end
      if allow then
        角色信息.师门技能[技能学习.选中].等级 = 角色信息.师门技能[技能学习.选中].等级 + 1
        local nsf = require("SDL.图像")(292, 50)
        if nsf:渲染开始() then
          local skill = 取技能(角色信息.师门技能[技能学习.选中].名称)
          local wenj="shape/jn/"
            if skill[10] then
                wenj="shape/xinzengsucai/"
            end
          __res:取图像(__res:取地址(wenj, skill[8])):拉伸(30, 30):显示(11, 10)
          字体16:置颜色(__取颜色("浅黑"))
          字体16:取图像(角色信息.师门技能[技能学习.选中].名称):显示(54, 15)
          字体16:取图像(角色信息.师门技能[技能学习.选中].等级 .. "/180"):显示(200, 15)
          nsf:渲染结束()
        end
        技能学习.技能列表.子控件[技能学习.选中]:置精灵(nsf:到精灵())
        技能学习:重置(角色信息.师门技能[技能学习.选中].等级)
        发送数据(3711, {
          序列 = 技能学习.选中
        })
      end
    end
  end
end
local 学习十次 = 技能学习:创建我的按钮(__res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(123, 41), "学习十次", 475+75, 450, "学习十次")
function 学习十次:左键弹起(x, y, msg)
  if 角色信息.师门技能[技能学习.选中].等级 >= 角色信息.等级 + 10 then
    __UI弹出.提示框:打开("#Y师门技能不能超过角色等级+10")
    return
  end
  角色信息.师门技能[技能学习.选中].等级 = 角色信息.师门技能[技能学习.选中].等级 + 10
  local nsf = require("SDL.图像")(292, 50)
  if nsf:渲染开始() then
    local skill = 取技能(角色信息.师门技能[技能学习.选中].名称)
    local wenj="shape/jn/"
      if skill[10] then
          wenj="shape/xinzengsucai/"
      end
    __res:取图像(__res:取地址(wenj, skill[8])):拉伸(30, 30):显示(11, 10)
    字体16:置颜色(__取颜色("浅黑"))
    字体16:取图像(角色信息.师门技能[技能学习.选中].名称):显示(54, 15)
    字体16:取图像(角色信息.师门技能[技能学习.选中].等级 .. "/180"):显示(200, 15)
    nsf:渲染结束()
  end
  技能学习.技能列表.子控件[技能学习.选中]:置精灵(nsf:到精灵())
  技能学习:重置(角色信息.师门技能[技能学习.选中].等级)
  发送数据(3711,{序列=技能学习.选中,十次=true})
end
local 包含网格 = 技能学习:创建我的网格("包含网格", 338, 190, 403, 140, 198, 64, 8, 8, 8, 2, true)
function 包含网格:重置(data)
  for _, v in ipairs(self.子控件) do
    if data[_] then
      local nsf = require("SDL.图像")(198, 64)
      if nsf:渲染开始() then
        local skill = 取技能(data[_].名称)
        local wenj="shape/jn/"
            if skill[10] then
                wenj="shape/xinzengsucai/"
            end
        __res:getPNGCC(3, 997, 1047, 178, 64):拉伸(198, 64):显示(0, 0)
        __res:取图像(__res:取地址(wenj, skill[7])):拉伸(40, 40):显示(11, 10)
        字体16:置颜色(__取颜色("浅黑"))
        字体16:取图像(data[_].名称):显示(63, 24)
        nsf:渲染结束()
      end
      v:置精灵(nsf:到精灵())
    else
      v:置精灵(v)
    end
  end
end
