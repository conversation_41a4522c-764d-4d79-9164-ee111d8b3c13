--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-31 22:57:27
    @Last Modified by: GGELUA
    @Last Modified time: 2022-11-01 05:29:46
--]]
local 召唤兽属性 = __UI界面.窗口层:创建窗口("召唤兽属性", 50 + abbr.py.x, 17 + abbr.py.y, 780, 500)
local lsb1 = {
  "气血",
  "魔法",
  "攻击",
  "防御",
  "速度",
  "法伤",
  "法防",
  "经验",
  "体质",
  "法力",
  "力量",
  "耐力",
  "敏捷",
  "潜能",
  "忠诚",
}
local lsb2 = {
  "气血",
  "魔法",
  "伤害",
  "防御",
  "速度",
  "法伤",
  "法防",
  "忠诚",
  "体质",
  "魔力",
  "力量",
  "耐力",
  "敏捷",
  "潜力",
  "忠诚",
  "经验",
}
local lsb3 = {
  "攻击资质",
  "防御资质",
  "体力资质",
  "法术资质",
  "速度资质",
  "闪躲资质",
  "寿  命",
  "成  长",
  "五  行"
}
local lsb4 = {
  "攻击资质",
  "防御资质",
  "体力资质",
  "法力资质",
  "速度资质",
  "躲闪资质",
  "寿命",
  "成长",
  "五行"
}
function 召唤兽属性:初始化()
  local nsf = require("SDL.图像")(780, 500)
  if nsf:渲染开始() then
    置窗口背景("召唤兽", 0, 12, 770, 485, true):显示(0, 0)
    取输入背景(0, 0, 160, 23):显示(26, 77)
    __res:getPNGCC(3, 375, 388, 145, 149):显示(77, 118)
    __res:getPNGCC(3, 684, 411, 57, 174):显示(247, 116)
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
  self.选中召唤兽 = nil
  self.模型格子 = __UI模型格子.创建()
end
function 召唤兽属性:更新(dt)
  self.模型格子:更新(dt)
end
function 召唤兽属性:显示(x, y)
  if self.数据 then
    self.数据:显示(x, y)
  end
  self.模型格子:显示(x, y)
end

function 召唤兽属性:打开()
  self:置可见(true)
  self.选中召唤兽 = nil
  召唤兽属性.头像网格:置头像(角色信息.宝宝列表)
  召唤兽属性.属性按钮:置选中(true)
  召唤兽属性.属性:重置()
  召唤兽属性.装备网格:置物品({})
  召唤兽属性.饰品网格:置物品()
  召唤兽属性.名称输入:清空()
  self.模型格子:置数据()
end

function 召唤兽属性:重置()
  if not 召唤兽属性.选中召唤兽 then
    self.数据 = nil
  else
    local nsf = require("SDL.图像")(250, 300)
    if nsf:渲染开始() then
        字体18:置颜色(255, 255, 255)
        字体18:取图像("等级 " .. 角色信息.宝宝列表[召唤兽属性.选中召唤兽].等级):置混合(0):显示(100, 270)
        nsf:渲染结束()
    end
    self.数据 = nsf:到精灵()
  end
end

local 关闭 = 召唤兽属性:创建我的按钮(__res:getPNGCC(1, 401, 0, 46, 46), "关闭", 730, 0)
function 关闭:左键弹起(x, y, msg)
  __UI界面.窗口层.召唤兽属性:置可见(false)
end

local 名称输入 = 召唤兽属性:创建输入("名称输入", 35, 80, 140, 18)
function 名称输入:初始化()
  self:取光标精灵()
  self:置限制字数(8)
  self:置颜色(39, 53, 81, 255)
end

local 头像网格 = 召唤兽属性:创建网格("头像网格", 15, 307, 300, 140)
function 头像网格:初始化()
  self:创建格子(70, 70, 5, 5, 3, 4, true)
end
function 头像网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
    if 召唤兽属性.选中召唤兽 then
      self.子控件[召唤兽属性.选中召唤兽]._spr.确定 = nil
    end
    召唤兽属性.选中召唤兽 = a
    self.子控件[a]._spr.确定 = true
    召唤兽属性.属性:重置()
    召唤兽属性:重置()
    召唤兽属性.名称输入:置文本(角色信息.宝宝列表[召唤兽属性.选中召唤兽].名称)
    召唤兽属性.装备网格:置物品(角色信息.宝宝列表[召唤兽属性.选中召唤兽].装备)
    召唤兽属性.饰品网格:置物品(角色信息.宝宝列表[召唤兽属性.选中召唤兽].饰品)
    召唤兽属性.模型格子:置数据(角色信息.宝宝列表[召唤兽属性.选中召唤兽], "召唤兽", 150, 225)
  end
end
function 头像网格:置头像(数据)
  for i = 1, #头像网格.子控件 do
    local lssj = __头像格子.创建()
    lssj:置头像(数据[i], "大")
    头像网格.子控件[i]:置精灵(lssj)
  end
end

local 饰品网格 = 召唤兽属性:创建网格("饰品网格", 14, 117+55*2+9, 60, 180)
function 饰品网格:初始化()
  self:创建格子(55, 55, 1, 0, 1, 1)
end

function 饰品网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
    local x, y = self.子控件[a]:取坐标(self.子控件[a])
    local w, h = self.子控件[a]:取宽高(self.子控件[a])
    self.子控件[a]._spr:详情打开(520-440, 86, w, h, "宝宝饰品", a)
  end
end

function 饰品网格:置物品(数据)
  --print(数据)
  local  sp
  if 数据 then
    sp={名称="召唤兽通用饰品"}
  end
  for i = 1, #饰品网格.子控件 do
    local lssj = __物品格子.创建()
    lssj:置物品(sp, "白格子", "宝宝饰品")
    饰品网格.子控件[i]:置精灵(lssj)
  end
end

local 装备网格 = 召唤兽属性:创建网格("装备网格", 248, 117, 60, 180)
function 装备网格:初始化()
  self:创建格子(55, 55, 4, 0, 3, 1)
end
function 装备网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr.物品 then
    local x, y = self.子控件[a]:取坐标(self.子控件[a])
    local w, h = self.子控件[a]:取宽高(self.子控件[a])
    self.子控件[a]._spr:详情打开(520, 86, w, h, nil, a)
  end
end
function 装备网格:置物品(数据)
  for i = 1, #装备网格.子控件 do
    local lssj = __物品格子.创建()
    lssj:置物品(数据[i], "白格子", "装备")
    装备网格.子控件[i]:置精灵(lssj)
  end
end
local 属性按钮 = 召唤兽属性:创建我的单选按钮(__res:getPNGCC(1, 401, 65, 175, 43, true),__res:getPNGCC(1, 963, 495, 175, 43, true), "属性按钮", 330, 70, "属 性")
function 属性按钮:左键按下(消息, x, y)
  召唤兽属性.属性:重置()
end
local 资质按钮 = 召唤兽属性:创建我的单选按钮(__res:getPNGCC(1, 401, 65, 175, 43, true),__res:getPNGCC(1, 963, 495, 175, 43, true), "资质按钮", 580, 70, "资 质")
function 资质按钮:左键按下(消息, x, y)
  召唤兽属性.资质:重置()
end


local 改名按钮 = 召唤兽属性:创建我的按钮(__res:getPNGCC(2, 493, 765, 118, 35, true):拉伸(104, 35), "改名按钮", 198, 72, "▲ 改名")
function 改名按钮:左键弹起(x, y, msg)
  if 召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽] then
    if 召唤兽属性.名称输入:取文本() then
      发送数据(5003, {
        序列 = 角色信息.宝宝列表[召唤兽属性.选中召唤兽].认证码,
        名称 = 召唤兽属性.名称输入:取文本()
      })
    else
      __UI弹出.提示框.打开("输入名称不能是空")
    end
  end
end
local 属性 = 召唤兽属性:创建控件("属性", 339, 110, 410, 380)
function 属性:初始化()
  local nsf = require("SDL.图像")(410, 315)
  if nsf:渲染开始() then
    字体18:置颜色(255, 255, 255)
    local lssj = 取输入背景(0, 0, 140, 23)
    local lssj2 = __res:getPNGCC(1, 400, 109, 185, 17):拉伸(140, 17)
    local pyx = 0
    local pyy = 0
    for i = 1, #lsb1 do
      if i > 8 then
        pyx = 197
        pyy = -320
      end
      if "气血" ~= lsb1[i] and "魔法" ~= lsb1[i] and "经验" ~= lsb1[i] then
        lssj:显示(59 + pyx, 14 + pyy + (i - 1) * 40)
      elseif "经验" == lsb1[i] then
        __res:getPNGCC(1, 400, 127, 305, 18):拉伸(163, 18):显示(59 + pyx, 17 + pyy + (i - 1) * 40)
        -- __res:getPNGCC(1, 400, 127, 305, 18):拉伸(335, 18):显示(59 + pyx, 17 + pyy + (i - 1) * 40)
      else
        lssj2:显示(59 + pyx, 17 + pyy + (i - 1) * 40)
      end
      字体18:取图像(lsb1[i]):置混合(0):显示(15 + pyx, 15 + pyy + (i - 1) * 40)
    end
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
end
function 属性:显示(x, y)
  if self.数据 then
    self.数据:显示(x, y)
  end
end
function 属性:重置()
  self:置可见(true)
  召唤兽属性.资质:置可见(false)
  召唤兽属性.属性按钮:置选中(true)
  if not 召唤兽属性.选中召唤兽 then
    self.数据 = nil
  else
    local nsf = require("SDL.图像")(410, 315)
    if nsf:渲染开始() then
      字体18:置颜色(39, 53, 81)
      字体16:置颜色(39, 53, 81)
      local pyx = 0
      local pyy = 0
      for i = 1, #lsb2 do
        if i > 8 then
          pyx = 197
          pyy = -320
        end
        if "气血" ~= lsb2[i] and "魔法" ~= lsb2[i] and "经验" ~= lsb2[i] then
          字体18:取图像(qz(角色信息.宝宝列表[召唤兽属性.选中召唤兽][lsb2[i]])):置混合(0):显示(68 + pyx, 15 + pyy + (i - 1) * 40)
        elseif "气血" == lsb2[i] and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大气血~=0 then
          local lssj2 = 角色信息.宝宝列表[召唤兽属性.选中召唤兽].气血 / 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大气血
          if lssj2 > 1 then
            lssj2 = 1
          end
          __res:getPNGCC(1, 400, 163, 305, 16):拉伸(math.floor(138 * lssj2), 16):显示(60, 18)
          字体16:取图像(string.format("%s/%s", 角色信息.宝宝列表[召唤兽属性.选中召唤兽].气血, 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大气血)):显示(90, 18)
        elseif "魔法" == lsb2[i] and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大魔法~=0 then
          local lssj2 = 角色信息.宝宝列表[召唤兽属性.选中召唤兽].魔法 / 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大魔法
          if lssj2 > 1 then
            lssj2 = 1
          end
          __res:getPNGCC(1, 401, 145, 305, 16):拉伸(math.floor(138 * lssj2), 16):显示(60, 58)
          字体16:取图像(string.format("%s/%s", 角色信息.宝宝列表[召唤兽属性.选中召唤兽].魔法, 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大魔法)):显示(90, 58)
        elseif "经验" == lsb2[i] and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大经验~=0 then
          local lssj2 = 角色信息.宝宝列表[召唤兽属性.选中召唤兽].当前经验 / 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大经验
          if lssj2 > 1 then
            lssj2 = 1
          end
          -- __res:getPNGCC(1, 587, 108, 183, 16):拉伸(math.floor(333 * lssj2), 16):显示(60, 298) 
          __res:getPNGCC(1, 587, 108, 183, 16):拉伸(math.floor(161 * lssj2), 16):显示(60, 298) 
          字体16:置颜色(255,255,255)
          字体16:取图像(string.format("%s/%s", 角色信息.宝宝列表[召唤兽属性.选中召唤兽].当前经验, 角色信息.宝宝列表[召唤兽属性.选中召唤兽].最大经验)):显示(65, 297)
        end
      end
      字体18:置颜色(255, 255, 255)
      字体18:取图像("参战等级 " .. 角色信息.宝宝列表[召唤兽属性.选中召唤兽].参战等级):置混合(0):显示(210+34, 296)
      
      nsf:渲染结束()
    end
    self.数据 = nsf:到精灵()
    召唤兽属性.休息按钮:重置()
  end
end

local 加点按钮 = 属性:创建我的按钮(__res:getPNGCC(1, 626, 28, 58, 34, true):拉伸(69, 34), "加点按钮", 328, 209, "加点")
function 加点按钮:左键弹起(x, y, msg)
  if 召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽] then
    __UI界面.窗口层.召唤兽加点.打开(__UI界面.窗口层.召唤兽加点, 角色信息.宝宝列表[召唤兽属性.选中召唤兽])
  end
end

local 驯养按钮 = 属性:创建我的按钮(__res:getPNGCC(1, 626, 28, 58, 34, true):拉伸(69, 34), "驯养按钮", 328, 249, "驯养")
function 驯养按钮:左键弹起(x, y, msg)
end


local 更多按钮 = 召唤兽属性:创建我的按钮(__res:getPNGCC(4, 0, 537, 175, 43, true), "更多按钮", 330, 322+115, "更 多")--,__res:getPNGCC(4, 179, 537, 175, 43, true)
function 更多按钮:左键按下(消息, x, y)
  if 召唤兽属性.选中召唤兽 then
    __UI弹出.召唤兽更多弹出:打开("bb")
  end
  
end
local 休息按钮 = 召唤兽属性:创建我的按钮(__res:getPNGCC(1, 401, 65, 175, 43, true), "休息按钮", 580, 322+115, "休 息")--,__res:getPNGCC(1, 963, 495, 175, 43, true)
function 休息按钮:左键弹起(x, y, msg)
  if 召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽] then
    发送数据(5002, {
      序列 = 角色信息.宝宝列表[召唤兽属性.选中召唤兽].认证码
    })
  end
end
function 休息按钮:重置()
  if 召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].参战信息 then
    属性:我的按钮置文字(self, __res:getPNGCC(1, 401, 65, 175, 43, true), "休 息")
  else
    属性:我的按钮置文字(self, __res:getPNGCC(1, 401, 65, 175, 43, true), "参 战")
  end
end

local 资质 = 召唤兽属性:创建控件("资质",324, 110, 440, 375)
function 资质:初始化()
end
function 资质:重置()
  self:置可见(true)
  召唤兽属性.属性:置可见(false)
  self.技能按钮:置选中(true)
  self.技能:重置()
end
local 技能按钮 = 资质:创建我的单选按钮(__res:getPNGCC(3, 1041, 57, 132, 40, true), __res:getPNGCC(3, 1041, 9, 132, 42, true), "技能按钮", 15, 9, "技能")
function 技能按钮:左键按下(消息, x, y)
  资质.技能:重置()
end
local 内丹按钮 = 资质:创建我的单选按钮(__res:getPNGCC(3, 1041, 57, 132, 40, true), __res:getPNGCC(3, 1041, 9, 132, 42, true), "内丹按钮", 155, 9, "内丹")
function 内丹按钮:左键按下(消息, x, y)
  资质.内丹:重置()
end
local 进阶按钮 = 资质:创建我的单选按钮(__res:getPNGCC(3, 1041, 57, 132, 40, true), __res:getPNGCC(3, 1041, 9, 132, 42, true), "进阶按钮", 295, 9, "进阶")
function 进阶按钮:左键按下(消息, x, y)
  资质.进阶:重置()
end
local 技能 = 召唤兽属性.资质:创建控件("技能", 0, 0, 440, 375)
function 技能:初始化()
  local nsf = require("SDL.图像")(440, 375)
  if nsf:渲染开始() then
    local lssj = 取输入背景(0, 0, 90, 23)
    __res:getPNGCC(3, 438, 608, 433, 322):显示(0, 49)
    for i = 1, #lsb3 do
      lssj:显示(87, 59 + (i - 1) * 35)
      字体18:取图像(lsb3[i]):显示(7, 61 + (i - 1) * 35)
    end
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
end
function 技能:显示(x, y)
  if self.数据 then
    self.数据:显示(x, y)
  end
end
function 技能:重置()
  资质.技能:置可见(true)
  资质.内丹:置可见(false)
  资质.进阶:置可见(false)
  self.数据 = nil
  if 召唤兽属性.选中召唤兽 then
    local nsf = require("SDL.图像")(339, 370)
    if nsf:渲染开始() then
      字体18:置颜色(39, 53, 81)
      for i = 1, #lsb4 do
        if lsb4[i]=="寿命" and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].种类=="神兽" then
          字体18:取图像("★永生★"):显示(92, 62 + (i - 1) * 35)
        else
          字体18:取图像(角色信息.宝宝列表[召唤兽属性.选中召唤兽][lsb4[i]]):显示(92, 62 + (i - 1) * 35)
        end
      end

      if 角色信息.宝宝列表[召唤兽属性.选中召唤兽].赐福技能_已有 then
        __主控["赐福图标大"]:显示(230+50+14, 262+31)
      end
      nsf:渲染结束()
    end
    self.数据 = nsf:到精灵()
    资质.技能.技能网格:置数据(角色信息.宝宝列表[召唤兽属性.选中召唤兽].技能, 角色信息.宝宝列表[召唤兽属性.选中召唤兽].法术认证)
  else
    资质.技能.技能网格:置数据({})
  end
end
local 技能网格 = 技能:创建网格("技能网格", 184, 90, 245, 185)
function 技能网格:初始化()
  self:创建格子(55, 55, 8, 8, 6, 4, true)
end
function 技能网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr.数据 then
    self.子控件[a]._spr:详情打开(x, y)
  end
end
function 技能网格:置数据(数据, 认证)
  for i = 1, #技能网格.子控件 do
    local lssj = __召唤兽技能格子.创建()
    if 认证 and i == 认证 then
      认证 = 数据[i]
    end
    local 是否赐福= nil 
    if 角色信息.宝宝列表[召唤兽属性.选中召唤兽] and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].赐福技能_生效 then
      是否赐福=判断是否赐福技能(角色信息.宝宝列表[召唤兽属性.选中召唤兽].赐福技能_生效  ,  数据[i])
    end
    lssj:置数据(数据[i], 55, 55, nil, 认证,是否赐福)
    技能网格.子控件[i]:置精灵(lssj)
  end
end
function 召唤兽属性:更新内丹(序号,内丹,bb)
  if 召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽] and 召唤兽属性.选中召唤兽==序号 then
    角色信息.宝宝列表[召唤兽属性.选中召唤兽].内丹=内丹
    -- table.print(bb)
    召唤兽属性.资质.内丹:重置()
  end
end
local 内丹 = 召唤兽属性.资质:创建控件("内丹", 0, 0, 440, 375)
function 内丹:初始化()
  local nsf = require("SDL.图像")(440, 375)
  if nsf:渲染开始() then
    __res:getPNGCC(3, 0, 602, 433, 322):显示(0, 49)
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
end
function 内丹:重置()
  资质.技能:置可见(false)
  资质.内丹:置可见(true)
  资质.进阶:置可见(false)
  local zjcz = 0
  if 召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].内丹 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].内丹.格子 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].内丹.内丹上限 then
    zjcz=0
    for n = 1,#角色信息.坐骑列表 do
      if 角色信息.坐骑列表[n].统御召唤兽[1] == 角色信息.宝宝列表[召唤兽属性.选中召唤兽].认证码 or 角色信息.坐骑列表[n].统御召唤兽[2] == 角色信息.宝宝列表[召唤兽属性.选中召唤兽].认证码 then
        zjcz = 角色信息.坐骑列表[n].初始成长
        break
      end
    end
    资质.内丹.内丹网格:置数据(zjcz)
  else
    资质.内丹.内丹网格:置数据()
  end
end
local 内丹网格 = 内丹:创建网格("内丹网格", 0, 0, 440, 375)
function 内丹网格:初始化()
  self:创建格子(55, 55, 8, 8, 1, 6)
end
function 内丹网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr.数据 then
    self.子控件[a]._spr:详情打开(x, y,w,h,self.子控件[a]._spr.数据.可学习)
  end
end
function 内丹网格:置数据(zjcz)
  local 坐标 = {
    {187-7, 69},
    {81-7, 132}, --5
    {294-7, 132}, --6
    {294-7, 242}, --2
    {187-7, 305}, --3
    {81-7, 242}, --4
  }
  for i = 1, #内丹网格.子控件 do
    local lssj = __内丹格子.创建()
    lssj:置数据(zjcz,角色信息.宝宝列表[召唤兽属性.选中召唤兽],i, 55, 55) 
    内丹网格.子控件[i]:置精灵(lssj)
    内丹网格.子控件[i]:置坐标(坐标[i][1], 坐标[i][2])
  end
end
local 进阶 = 召唤兽属性.资质:创建控件("进阶", 0, 0, 440, 375)
function 进阶:初始化()
  local nsf = require("SDL.图像")(440, 375)
  if nsf:渲染开始() then
    __res:getPNGCC(3, 438, 608, 433, 322):显示(0, 49)
    __res:取图像(__res:取地址("shape/ui/zhs/", 278179094)):拉伸(196, 210):显示(100, 100)
    -- __res:取图像(__res:取地址("shape/ui/zhs/", 4159437191)):拉伸(200, 212):显示(100, 100)
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
end
function 进阶:显示(x, y)
  if self.数据 then
    self.数据:显示(x, y)
  end
end
function 进阶:重置()
  资质.技能:置可见(false)
  资质.内丹:置可见(false)
  资质.进阶:置可见(true)
  self.数据 = nil

  if 召唤兽属性.选中召唤兽 and 角色信息.宝宝列表[召唤兽属性.选中召唤兽].进阶 then
    local nsf = require("SDL.图像")(440, 375)
    local jinjie=角色信息.宝宝列表[召唤兽属性.选中召唤兽].进阶
    local lx=jinjie.灵性
    if nsf:渲染开始() then
      if lx > 0 and lx <= 10 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 1161207869)):拉伸(200, 212):显示(100, 100)
      elseif lx > 10 and lx <= 20 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 1900820230)):拉伸(200, 212):显示(100, 100)
      elseif lx > 20 and lx <= 30 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 3590329528)):拉伸(200, 212):显示(100, 100)
      elseif lx > 30 and lx <= 40 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 4159437191)):拉伸(200, 212):显示(100, 100)
      elseif lx > 40 and lx <= 50 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 295056520)):拉伸(200, 212):显示(100, 100)
      elseif lx > 50 and lx <= 60 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 2588875105)):拉伸(200, 212):显示(100, 100)
      elseif lx > 60 and lx <= 70 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 511359892)):拉伸(200, 212):显示(100, 100)
      elseif lx > 70 and lx <= 80 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 2798233450)):拉伸(200, 212):显示(100, 100)
      elseif lx > 80 and lx <= 90 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 696443895)):拉伸(200, 212):显示(100, 100)
      elseif lx > 90 and lx <= 91 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 487004119)):拉伸(200, 212):显示(100, 100)
      elseif lx > 91 and lx <= 93 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 3293513218)):拉伸(200, 212):显示(100, 100)
      elseif lx > 93 and lx <= 97 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 2540032179)):拉伸(200, 212):显示(100, 100)
      elseif lx >= 98 then
        __res:取图像(__res:取地址("shape/ui/zhs/", 916636070)):拉伸(200, 212):显示(100, 100)
      end
      字体18:置颜色(255, 255, 255)
      字体18:取图像("灵性：" .. lx):置混合(0):显示(200-44, 62+262)
          if jinjie.特性 and jinjie.特性~="无" then
            if jinjie.开启 then
              字体18["置颜色"](字体18, __取颜色("黄色"))
            else
              字体18["置颜色"](字体18, __取颜色("白色"))
            end
            字体18["取图像"](字体18, jinjie.特性):置混合(0):显示(200+117-134, 62+89+38)
          end
      nsf:渲染结束()
    end
    self.数据 = nsf:到精灵()
    资质.进阶.特性网格:置数据(角色信息.宝宝列表[召唤兽属性.选中召唤兽])
  else
    资质.进阶.特性网格:置数据({})
  end
end
local 特性网格 = 进阶:创建网格("特性网格", 121, 145, 126, 102)
function 特性网格:初始化()
  self:创建格子(126, 102, 0, 0, 1, 1)
end
function 特性网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr.数据 then
    self.子控件[a]._spr:详情打开(x, y)
  end
end
function 特性网格:置数据(数据, zjcz, bb, nsgz)
  for i = 1, #特性网格.子控件 do
    local lssj = __特性格子.创建()
    lssj:置数据(数据, 126, 102)
    特性网格.子控件[i]:置精灵(lssj)
  end
end
