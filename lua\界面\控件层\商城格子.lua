local 基类 = require("界面/控件层/基类/物品基类")
local 商城格子 = class("商城格子", 基类)
function 商城格子:初始化()
  self.py = {x = 0, y = 0}
  self.商城选中 = __res:getPNGCC(6, 266, 515, 121, 121):到精灵()--__res["UI素材"][4]:复制区域(542, 272, 159, 67):到精灵()
  self.zgdf =__res:getPNGCC(6, 131, 521, 120, 120)
end
function 商城格子:置物品(数据,总类)
  self.模型 = nil
  self.物品 = nil
  if 数据 then
    local nsf = require("SDL.图像")(120, 120)
    self:取数据(数据)
    if nsf["渲染开始"](nsf) then
      -- table.print(数据)
      -- __res:getPNGCC(3, 375, 388, 145, 149)["拉伸"](__res:getPNGCC(3, 375, 388, 145, 149), 125, 125)["显示"](__res:getPNGCC(3, 375, 388, 145, 149)["拉伸"](__res:getPNGCC(3, 375, 388, 145, 149), 125, 125), 0, 0)
      -- __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 36, 36)
      -- if 数据["模型"] and 数据["种类"] == "宝宝" then
      --   local lssj = 取头像(数据["模型"])
      --   __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50), 38, 38)
      --   字体18["置颜色"](字体18, __取颜色("黄色"))
      --   local tsf = 字体18["取图像"](字体18, 数据["模型"])
      --   tsf["显示"](tsf, (125 - tsf["宽度"]) // 2, 8)
      --   字体18["置颜色"](字体18, __取颜色("红色"))
      --   tsf = 字体18["取图像"](字体18, "价格:" .. 数据["价格"])
      --   tsf["显示"](tsf, (125 - tsf["宽度"]) // 2, 96)
      -- else
      self.zgdf:显示(0, 0)
      local 目录="shape/dj/"
      if 锦衣文件完整 and self.物品.资源 and (self.物品.资源=="r3d.dll" or self.物品.资源=="nx3d5.dll" or self.物品.资源=="nx3d6.dll") then
        目录="shape/sys/"
      end
      if self.物品.颜色区分 then
        __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])):置颜色(检查是否有物品颜色(self.物品.属性)):显示(8+29, 12+22)
      else
        __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])), 8+29, 12+22)
      end
        -- __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])), 8+29, 12+22)
        字体14["置颜色"](字体14, __取颜色("白色"))
        local tsf = 字体14["取图像"](字体14, 数据["名称"])
        tsf["显示"](tsf, (125 - tsf["宽度"]) // 2+55-58, 102)
        -- 字体14["置颜色"](字体14, __取颜色("白色"))
        if 总类=="仙玉商城" or 总类=="锦衣商城" or 总类=="特殊商城" or 总类=="祥瑞" then
          字体14["置颜色"](字体14, __取颜色("绿色"))
        else
          字体14["置颜色"](字体14, __取颜色("白色"))
        end
        tsf = 字体14["取图像"](字体14, 数据["价格"])
        tsf["显示"](tsf, (125 - tsf["宽度"]) // 2+55-58, 0)
      -- end
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
  end

  self.数据 = 数据
end
function 商城格子:详情打开(x, y, w, h, lx, bh)
  local Button, Button2, Button3, Button4
  __UI弹出["道具详情"]["置可见"](__UI弹出["道具详情"], true, true)
  __UI弹出["道具详情"]["道具文本"]["清空"](__UI弹出["道具详情"]["道具文本"])
  __UI弹出["道具详情"]["打开"](__UI弹出["道具详情"], self.物品, x, y, 360, 360, Button, Button2, Button3, Button4, bh, lx)
end
function 商城格子:更新(dt)
end
function 商城格子:显示(x, y)
  if self.精灵 then
    self.精灵["显示"](self.精灵, x + self.py.x, y + self.py.y)
  end
  if self.确定 then
    self["商城选中"]["显示"](self["商城选中"], x + 0, y + 0)
  end
end
return 商城格子
