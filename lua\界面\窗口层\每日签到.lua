

local qdwp = {
  {名称="秘制红罗羹",类型="消耗道具"},
 {名称="秘制绿芦羹",类型="消耗道具"},
 {名称="天眼通符",类型="消耗道具"},
 {名称="回梦丹",类型="消耗道具"},
 {名称="超级金柳露",类型="消耗道具"},
 {名称="高级兽诀",类型="消耗道具"},
 {名称="120无级别礼包",类型="消耗道具"}
 }
 local 每日签到 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "每日签到", 96 + abbr.py.x, 23 + abbr.py.y, 727, 484)
 function 每日签到:初始化()
   local nsf = require("SDL.图像")(727, 484)
   if nsf["渲染开始"](nsf) then
     置窗口背景("每日签到", 0, 12, 700, 356, true)["显示"](置窗口背景("每日签到", 0, 12, 700, 356, true), -15, 0)
     __res:getPNGCC(12, 0, 0, 650, 320):显示(5,43)
     字体VIP:置颜色(__取颜色("黑"))
     字体VIP:取图像("欢迎少侠来到VIP特权\n界面,提升江湖威望等级\n可获得丰厚奖励。"):显示(30,65)
   end
   self.数据 ={}
   self:置精灵(nsf["到精灵"](nsf))
 end
 
 
 function 每日签到:打开(数据)
   self:置可见(true)
 end
 function 每日签到:刷新(数据)
   self.数据 = 数据
   if  self.数据.类型 == "七日签到" then
     self.七日签到面板["置可见"](self.七日签到面板, true)
     self.签到道具:置物品(qdwp)
     self.签到道具["置可见"](self.签到道具, true)
   end
 end
 
 local 七日签到面板 = 每日签到["创建控件"](每日签到, "七日签到面板", 0, 0, 773, 483)
 function 七日签到面板:初始化()
   local nsf = require("SDL.图像")(773, 483)
   if nsf["渲染开始"](nsf) then
     __res:getPNGCC(12, 689, 0, 500, 257):显示(195,100)
     nsf["渲染结束"](nsf)
   end
   self:置精灵(nsf["到精灵"](nsf))
   self:置可见(true)
   
   
 end


 
 local 签到道具 = 每日签到["创建网格"](每日签到, "签到道具",  200, 200, 800, 800)
 function 签到道具:初始化()
   
 end
 function 签到道具:置物品(data, zl, fl)
   self:创建格子(70, 150, 0, 0, 5, 7)
   if data==nil then 
   return 
   end
   for i, v in ipairs(self.子控件) do
     if data[i] then
       local lssj = __物品格子["创建"]()
       lssj["置物品"](lssj, data[i], nil, "道具选择",huise,true) --置物品(数据, 背景, 类型,huise,子类描述)
       
       if i==1    then
       lssj["置偏移"](lssj, 8, 5) 
       elseif  i==2  then
       lssj["置偏移"](lssj, 8, 20) 
       elseif  i==3   then
       lssj["置偏移"](lssj, 6, 5)     
       elseif   i==4  then
       lssj["置偏移"](lssj, 6, 20) 
      elseif   i==7 or i==5 then
        lssj["置偏移"](lssj, 3, 5) 
      elseif   i==6 then
        lssj["置偏移"](lssj, 6, 22) 



       
       end   
       self.子控件[i]["置精灵"](self.子控件[i], lssj)
       if i==1  then
         local 签到按钮1 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(12, 689, 265, 29, 29):拉伸(29, 29), "详情" .. i, 25, 75)
         function 签到按钮1:左键单击(x, y, msg)
           发送数据(122,{文本="七日签到"})
         end
       elseif  i==2  then
         local 签到按钮2 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(12, 689, 265, 29, 29):拉伸(29, 29), "详情" .. i, 25, 95)   
         function 签到按钮2:左键单击(x, y, msg)
           发送数据(122,{文本="七日签到"})
         end
       elseif  i==3  then
         local 签到按钮3 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(12, 689, 265, 29, 29):拉伸(29, 29), "详情" .. i, 22, 75)   
         function 签到按钮3:左键单击(x, y, msg)
           发送数据(122,{文本="七日签到"})
         end
       elseif  i==4  then
         local 详情 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(12, 689, 265, 29, 29):拉伸(29, 29), "详情" .. i, 20, 95)   
         function 详情:左键单击(x, y, msg)
           发送数据(122,{文本="七日签到"})
         end
       elseif  i==5  then
         local 详情 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(12, 689, 265, 29, 29):拉伸(29, 29), "详情" .. i, 17, 75)   
         function 详情:左键单击(x, y, msg)
           发送数据(122,{文本="七日签到"})
         end
       elseif  i==6  then
         local 详情 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(12, 689, 265, 29, 29):拉伸(29, 29), "详情" .. i, 17, 95)   
         function 详情:左键单击(x, y, msg)
           发送数据(122,{文本="七日签到"})
         end
       elseif  i==7  then
         local 详情 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(12, 689, 265, 29, 29):拉伸(29, 29), "详情" .. i, 17, 75)   
         function 详情:左键单击(x, y, msg)
           发送数据(122,{文本="七日签到"})
         end
       end  
        
       
       v:置可见(true, true)
       
       
     end
   end
 end
 

 local 关闭 = 每日签到["创建我的按钮"](每日签到, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 655, -5)
 function 关闭:左键弹起(x, y, msg)
   每日签到["置可见"](每日签到, false)
 end
 for i, v in ipairs({
--    {
--      name = "七日签到",
--      x = 200,
--      y = 50,
--      tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 117, 43),
--      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 43),
--      font = "七日签到"
--    },
--    {
--      name = "VIP奖励",
--      x = 355-20-10,
--      y = 50,
--      tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 117, 43),
--      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 117, 43),
--      font = "VIP奖励"
--    },
--    {
--      name = "充值CDK",
--      x = 355+155-40-20,
--      y = 50,
--      tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 117, 43),
--      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 43),
--      font = "充值CDK"
--    },
--    {
--     name = "加速系统",
--     x = 355+155+30+30,
--     y = 50,
--     tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 117, 43),
--     tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 43),
--     font = "加速系统"
--  }
 }) do
   local 临时函数 = 每日签到["创建我的单选按钮"](每日签到, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
  
   function 临时函数:左键弹起(x, y)
     if not __主控.战斗中 then
       if v.name == "七日签到" then
         发送数据(120,{文本="七日签到"}) 
       elseif v.name == "VIP奖励" then
         发送数据(120,{文本="威望系统",参数=1})
       elseif v.name == "充值CDK" then
         发送数据(120,{文本="充值CDK",参数=1})
        elseif v.name == "加速系统" then  
          发送数据(120,{文本="加速系统",参数=1})
         
       end
     end
   end
 end
