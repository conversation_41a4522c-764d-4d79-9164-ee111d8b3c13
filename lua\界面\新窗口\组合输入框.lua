--[[
Author: GGELUA
Date: 2024-08-10 00:03:20
Last Modified by: GGELUA
Last Modified time: 2024-08-17 12:49:04
--]]
local 组合输入框 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "组合输入框", 258 + abbr.py.x, 60 + abbr.py.y, 367+10, 157+10+87)
function 组合输入框:初始化()
  local nsf = require("SDL.图像")(445, 410)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("请输入", 0, 12, 367, 157+17+58-39, true):显示( 0, 0)
    __res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35), 72-24, 60+60-29)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 组合输入框:打开(类型,附加)
  self.回调事件 = 附加
  self.类型事件 = 类型
  self:置可见(true)
  self:刷新显示()
end

function 组合输入框:刷新显示()
  local nsf = require("SDL.图像")(367, 157)
  if nsf["渲染开始"](nsf) then
    字体18:置颜色(__取颜色("白色"))
    字体18:取图像(self.回调事件[1]):置混合(0):显示(32,33+8+16)
    nsf["渲染结束"](nsf)
  end
  self.图像2 = nsf["到精灵"](nsf)
end

local 关闭 = 组合输入框["创建我的按钮"](组合输入框, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 403-75, 0)
function 关闭:左键弹起(x, y, msg)
  组合输入框["置可见"](组合输入框, false)
  组合输入框["shuru"]["清空"](组合输入框["shuru"])
end
local shuru = 组合输入框["创建我的输入"](组合输入框, "shuru", 81-24, 67+60-28, 254, 24, nil, 88, "黑色", 字体20)
local 确定 = 组合输入框["创建我的按钮"](组合输入框, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "确定", 153-26, 337-189-25+13, "确定")
function 确定:左键弹起(x, y, msg)
  if 组合输入框.shuru:取文本()~= ""  then
    if 组合输入框.类型事件 == "影蛊" then
			发送数据(73,{文本=组合输入框.shuru:取文本()})
		elseif 组合输入框.类型事件 == "勾魂索" then
			发送数据(6567,{文本=组合输入框.shuru:取文本()})
			-- 全局勾魂索=组合输入框.shuru:取文本()

  if 组合输入框 == "请抄写："  then
    self.组合输入框:置限制字数(100)
		end
		self.组合输入框:置可视(true,true)
elseif self.类型事件 == "请抄写：" then
  if self.组合输入框:取文本() == "" then
    tp.提示:写入("#Y/请输入您需要抄写的句子！")
  else
    发送数据(88,{文本=self.组合输入框:取文本()})
    self:打开()
    return false
  end
		elseif 组合输入框.类型事件 == "帮战报名"  then
			if 组合输入框.shuru:取文本()+0<500000 then
				__UI弹出["提示框"]:打开("#Y/最低报名费用不能低于50W")
				return
			end
			发送数据(6550,{文本=组合输入框.shuru:取文本()})
		elseif 组合输入框.类型事件 == "更改宝宝造型" then
			发送数据(5013,{文本=组合输入框.shuru:取文本()})
		end
    组合输入框["置可见"](组合输入框, false)
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end
