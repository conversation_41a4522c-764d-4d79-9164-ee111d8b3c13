
local 多开经脉 = 窗口层:创建窗口("多开经脉",0, 0, 610, 475)


local 取门派文字动画={
  物理输出=0x69700000,
  法术输出=0x69900000,
  法力损毁=0x69800000,
  固定伤害=0x70200000,
  治疗回复=0x70400000,
  增益强化=0x70300000,
  封印控制=0x70100000,
  追加法术=0x70500000,
  死亡禁锢=0x71800000,
  召唤物=  0x75700000,


}

local 门派神器名称 = {
    大唐官府 = "轩辕剑",化生寺 = "墨魂笔",方寸山 = "黄金甲",女儿村 = "泪痕碗",天宫 = "独弦琴",
    普陀山 = "华光玉",龙宫 = "清泽谱",五庄观 = "星斗盘",魔王寨 = "明火珠",狮驼岭 = "噬魂齿",
    盘丝洞 = "昆仑镜",阴曹地府 = "四神鼎",神木林 = "月光草",凌波城 = "天罡印",无底洞 = "玲珑结",
    花果山 = "鸿蒙石",九黎城 = "魔息角"
  }

local 取门派说明={
 大唐官府={
          浴血豪侠={[1]="物理输出"},
          无双战神={[1]="物理输出"},
          虎贲上将={[1]="物理输出"},
          },

 方寸山={
          拘灵散修={[1]="封印控制"},
          伏魔天师={[1]="法术输出"},
          五雷正宗={[1]="法术输出"},
          },

 女儿村={
         绝代妖娆={[1]="封印控制"},
         花雨伊人={[1]="封印控制",[2]="法力损毁",[3]="固定伤害"},
         妙舞佳人={[1]="物理输出"},
         },

 化生寺={
         杏林妙手={[1]="治疗回复"},
         护法金刚={[1]="治疗回复",[2]="增益强化"},
         无量尊者={[1]="法术输出"},
         },

 盘丝洞={
         风华舞圣={[1]="封印控制"},
         迷情妖姬={[1]="封印控制",[2]="法力损毁"},
         百媚魔姝={[1]="物理输出"},
         },

 阴曹地府={
         勾魂阎罗={[1]="死亡禁锢",[2]="治疗回复"},
         六道魍魉={[1]="物理输出",[2]="死亡禁锢"},
         诛刑毒师={[1]="物理输出"},
         },
 魔王寨={
         平天大圣={[1]="法术输出"},
         盖世魔君={[1]="法术输出"},
         风火妖王={[1]="法术输出"},
         },
 狮驼岭={
         嗜血狂魔={[1]="物理输出"},
         万兽之王={[1]="物理输出"},
         狂怒斗兽={[1]="物理输出"},
         },

 天宫={
       镇妖神使={[1]="封印控制"},
       踏雷天尊={[1]="法术输出"},
       霹雳真君={[1]="物理输出"},
       },

 普陀山={
         莲台仙子={[1]="治疗回复",[2]="增益强化"},
         五行咒师={[1]="固定伤害"},
         落伽神女={[1]="物理输出"},

         },
 五庄观={
         清心羽客={[1]="封印控制"},
         乾坤力士={[1]="物理输出"},
         万寿真仙={[1]="物理输出"},
         },
 龙宫={
       海中蛟虬={[1]="法术输出"},
       云龙真身={[1]="法术输出",[2]="法力损毁"},
       沧海潜龙={[1]="法术输出"},
       },
 神木林={
         通天法王={[1]="法术输出"},
         巫影祭司={[1]="法术输出"},
         灵木药宗={[1]="法术输出"},
         },


 凌波城={
         九天武圣={[1]="物理输出"},
         灵霄斗士={[1]="物理输出"},
         风云战将={[1]="物理输出"},
         },
 无底洞={
         妙谛金莲={[1]="治疗回复",[2]="增益强化"},
         摄魂迷影={[1]="封印控制"},
         幽冥巫煞={[1]="固定伤害",[2]="召唤物"},
         },
九黎城={
          铁火战魔={[1]="物理输出"},
 
          },      
}



local 取门派说明上={
 大唐官府={
          浴血豪侠="攻无不克,战无不胜",
          无双战神="一夫当关,万夫莫开",
          虎贲上将="披坚执锐,智勇双全",
          },

 方寸山={
          拘灵散修="逍遥散修,载一抱素",
          伏魔天师="伏魔除妖,正法论道",
          五雷正宗="五气朝元,天地自在",
           },

  女儿村={
         绝代妖娆="绝代妖娆,智计无双",
         花雨伊人="花雨伊人,绝代双骄",
         妙舞佳人="踏歌倩影,一舞蹁跹",
         },

 化生寺={
         杏林妙手="春满杏林,医者侠心",
         护法金刚="怒目金刚,千军无畏",
         无量尊者="无量臧言,十方普照",
         },

 盘丝洞={
         风华舞圣="迷瘴之殇,谁解情丝",
         迷情妖姬="多姿多情,引魂噬心",
         百媚魔姝="百媚婀娜，千丝笼影",
         },

 阴曹地府={
         勾魂阎罗="九幽阎罗，勾魂锢魄",
         六道魍魉="无惧黑夜，不畏轮回",
         诛刑毒师="毒刹诛刑，瘴蔽幽冥",
         },
 魔王寨={
         平天大圣="威震山河,气概平天",
         盖世魔君="魔君盖世,独霸一方",
         风火妖王="妖王怒火,势焰滔天",
         },
 狮驼岭={
         嗜血狂魔="狂兽奔杀,激突猛进",
         万兽之王="万兽之王,万兽臣服",
         狂怒斗兽="炽热兽魂，无畏战狂",
         },

 天宫={
       镇妖神使="神使执戒，镇妖压邪",
       踏雷天尊="踏电行雷,耀武天尊",
       霹雳真君="霹雳手段，百战凌风",
       },

 普陀山={
         莲台仙子="莲台端坐,普渡众生",
         五行咒师="五行制化,咒令乾坤",
         落伽神女="落伽大士,扶危济厄",
         },
 五庄观={
         清心羽客="清风望月，羽客归心",
         乾坤力士="乾坤飞剑，锋刃无形",
         万寿真仙="清净守笃，修斋行道",
         },
 龙宫={
       海中蛟虬="碧海青天,踏浪穿波",
       云龙真身="云龙现身,呼风唤雨",
       沧海潜龙="潜龙在渊,睥睨沧海",
       },
 神木林={
         通天法王="通天之灵,师法自然",
         巫影祭司="巫神幽语，蛊魅人心",
         灵木药宗="神木之侍，灵佑之恩",
         },


 凌波城={
         九天武圣="九天神力.三界光明",
         灵霄斗士="诛魔驱暗,战意凌然",
         风云战将="风云荡邪,天眼诛恶",
         },
 无底洞={
         妙谛金莲="地涌金莲，华光灵照",
         摄魂迷影="强力固定伤害",
         幽冥巫煞="幽冥诡巫,夺血为煞",
         },
九黎城={
          铁火战魔="强力物理伤害",
          },
}


local 取门派小动画={
  输出=0x01000006,
  封印=0x01000007,
  辅助=0x01000005,
}

local 取门派动画={
 大唐官府={
          浴血豪侠="输出",
          无双战神="输出",
          虎贲上将="输出",
          },

 方寸山={
          拘灵散修="封印",
          伏魔天师="封印",
          五雷正宗="输出",
          },

 女儿村={
         绝代妖娆="封印",
         花雨伊人="封印",
         妙舞佳人="输出",
         },

 化生寺={
         杏林妙手="辅助",
         护法金刚="辅助",
         无量尊者="输出",
         },

 盘丝洞={
         风华舞圣="封印",
         迷情妖姬="封印",
         百媚魔姝="输出",
         },

 阴曹地府={
         勾魂阎罗="封印",
         六道魍魉="输出",
         诛刑毒师="输出",
         },
 魔王寨={
         平天大圣="输出",
         盖世魔君="输出",
         风火妖王="输出",
         },
 狮驼岭={
         嗜血狂魔="输出",
         万兽之王="输出",
         狂怒斗兽="输出",
         },

 天宫={
       镇妖神使="封印",
       踏雷天尊="输出",
       霹雳真君="输出",
       },

 普陀山={
         莲台仙子="辅助",
         五行咒师="输出",
         落伽神女="输出",
         },
 五庄观={
         清心羽客="封印",
         乾坤力士="输出",
         万寿真仙="输出",
         },
 龙宫={
       海中蛟虬="输出",
       云龙真身="输出",
       沧海潜龙="输出",
       },
 神木林={
         通天法王="输出",
         巫影祭司="输出",
         灵木药宗="输出",
         },


 凌波城={
         九天武圣="输出",
         灵霄斗士="输出",
         风云战将="输出",
         },
 无底洞={
         妙谛金莲="辅助",
         摄魂迷影="封印",
         幽冥巫煞="输出",
         },
  九黎城={
          铁火战魔="输出",
           },
}






local 取门派法宝 ={
  方寸山 ="救命毫毛",
	女儿村 ="织女扇",
	神木林 ="月影",
	化生寺 ="慈悲",
	大唐官府 ="干将莫邪",
	阴曹地府 ="九幽",
	盘丝洞 ="迷魂灯",
	无底洞 ="宝烛",
	魔王寨 ="五火神焰印",
	狮驼岭 ="失心钹",
	天宫 ="伏魔天书",
	普陀山 ="普渡",
	凌波城 ="天煞",
	五庄观 ="奇门五行令",
	龙宫 ="镇海珠",
  九黎城 ="铸兵锤",
}


local 取门派介绍技能={
 大唐官府={
          浴血豪侠={"横扫千军","后发制人","杀气诀","翩鸿一击"},
          无双战神={"连破","横扫千军","后发制人","杀气诀","翩鸿一击"},
          虎贲上将={"披坚执锐","横扫千军","后发制人","杀气诀"}
          },
 方寸山={
          拘灵散修={"催眠符","凝神术","失心符","落魄符"},
          伏魔天师={"五雷咒","落雷符","悲恸","奔雷"},
          五雷正宗={"五雷正法","雷法崩裂","雷法震煞","雷法坤伏","咒符"}
          },

 女儿村={
         绝代妖娆={"似玉生香","莲步轻舞","如花解语","自矜"},
         花雨伊人={"雨落寒沙","子母神针","似玉生香"},
         妙舞佳人={"踏歌","乐韵","轻歌飘舞","翩跃飞舞"}
         },
 化生寺={
         杏林妙手={"活血","推气过宫","救死扶伤","我佛慈悲","佛眷"},
         护法金刚={"聚气","金刚护法","金刚护体","推气过宫","我佛慈悲"},
         无量尊者={"唧唧歪歪","谆谆教诲","金刚护体","达摩护体"}
         },
 盘丝洞={
         风华舞圣={"含情脉脉","神迷","魔音摄魂","天罗地网"},
         迷情妖姬={"含情脉脉","神迷","魔音摄魂","姐妹同心"},
         百媚魔姝={"千蛛噬魂","蛛丝缠绕","神迷","天罗地网"}
         },
 阴曹地府={
         勾魂阎罗={"锢魂术","尸腐毒","魂飞魄散","阎罗令"},
         六道魍魉={"锢魂术","尸腐毒","魂飞魄散","六道无量"},
         诛刑毒师={"血影蚀心","百鬼噬魂","魂飞魄散","幽冥鬼眼"}
         },
 魔王寨={
         平天大圣={"三昧真火","飞砂走石","牛劲","魔冥"},
         盖世魔君={"三昧真火","飞砂走石","牛劲","魔冥"},
         风火妖王={"秘传三昧真火","秘传飞砂走石","三昧真火","飞砂走石"}
         },
 狮驼岭={
         嗜血狂魔={"变身","鹰击","连环击","象形","狮搏"},
         万兽之王={"驯兽幼狮","幼狮之搏","鹰击长空","狮魂"},
         狂怒斗兽={"狂怒","变身","鹰击","连环击","象形"}
         },
 天宫={
       镇妖神使={"错乱","镇妖","掌心雷","知己知彼"},
       踏雷天尊={"雷霆万钧","天神护体","电芒"},
       霹雳真君={"风雷斩","霹雳弦惊","雷怒霆激","返璞"}
       },
 普陀山={
         莲台仙子={"普渡众生","自在心法","杨柳甘露"},
         五行咒师={"紧箍咒","日光华","莲心剑意"},
         落伽神女={"五行珠","日光华","剑意莲心"}
         },
 五庄观={
         清心羽客={"日月乾坤","生命之泉","炼气化神"},
         乾坤力士={"烟雨剑法","飘渺式","骤雨"},
         万寿真仙={"敲金击玉","还丹","金击式"}
         },
 龙宫={
       海中蛟虬={"龙卷雨击","龙腾","龙魂","龙骇"},
       云龙真身={"龙卷雨击","龙腾","龙魂","龙骇"},
       沧海潜龙={"龙卷雨击","龙腾","龙魂","龙骇"}
       },
 神木林={
         通天法王={"风灵","落叶萧萧","荆棘舞","鞭挞"},
         巫影祭司={"风灵","蛊树迷瘴","催化","雾杀"},
         灵木药宗={"百草诀","药灵","滋养","百草复苏","百草神木复苏"}
         },


 凌波城={
         九天武圣={"战意","天崩地裂","翻江搅海","吞山","饮海"},
         灵霄斗士={"战意","超级战意","天崩地裂","翻江搅海"},
         风云战将={"战意","天眼神通","天崩地裂","翻江搅海"}
         },
 无底洞={
         妙谛金莲={"金莲","地涌金莲","燃血术","由己渡人","焕生咒"},
         摄魂迷影={"夺魄令","煞气诀","惊魂掌","燃血术"},
         幽冥巫煞={"裂魂","夺命咒","追魂刺","燃血术"}
         },
 九黎城={
          铁火战魔={"黎魂","战鼓","怒哮","一斧开天","三荒尽灭"},
 
          },
}

local 取门派按键文字={
 大唐官府={
          [1]="浴血豪侠",
          [2]="无双战神",
          [3]="虎贲上将"
          },
 方寸山={
          [1]="拘灵散修",
          [2]="伏魔天师",
          [3]="五雷正宗"
          },

 女儿村={
          [1]="绝代妖娆",
          [2]="花雨伊人",
          [3]="妙舞佳人"
         },
 化生寺={
          [1]="杏林妙手",
          [2]="护法金刚",
          [3]="无量尊者"
         },
 盘丝洞={
          [1]="风华舞圣",
          [2]="迷情妖姬",
          [3]="百媚魔姝"
         },
 阴曹地府={
          [1]="勾魂阎罗",
          [2]="六道魍魉",
          [3]="诛刑毒师"
         },
 魔王寨={
          [1]="平天大圣",
          [2]="盖世魔君",
          [3]="风火妖王"
         },
 狮驼岭={
          [1]="嗜血狂魔",
          [2]="万兽之王",
          [3]="狂怒斗兽"
         },
 天宫={
          [1]="镇妖神使",
          [2]="踏雷天尊",
          [3]="霹雳真君"
       },
 普陀山={
          [1]="莲台仙子",
          [2]="五行咒师",
          [3]="落伽神女"
         },
 五庄观={
          [1]="清心羽客",
          [2]="乾坤力士",
          [3]="万寿真仙"
         },
 龙宫={
          [1]="海中蛟虬",
          [2]="云龙真身",
          [3]="沧海潜龙"
       },
 神木林={
          [1]="通天法王",
          [2]="巫影祭司",
          [3]="灵木药宗"
         },
 凌波城={
          [1]="九天武圣",
          [2]="灵霄斗士",
          [3]="风云战将"
         },
 无底洞={
          [1]="妙谛金莲",
          [2]="摄魂迷影",
          [3]="幽冥巫煞"
         },
九黎城={
          [1]="铁火战魔",
          [2]="铁火战魔",
          [3]="铁火战魔"
         },
}


local function 取重复技能介绍(名称,门派)
    if 名称 == "不灭" then
         if 门派=="魔王寨" then
          return "秘传法术造成伤害提升10%，且使目标直接阵亡的几率提升10%。"
        elseif 门派=="方寸山"  then
           return "佩戴“救命毫毛”时，造成的法术伤害结果增加（救命毫毛层数%2）%；增加“救命毫毛”在每场战斗中可以发挥的次数上限3，每次“救命毫毛”复活后法术伤害力增加40点。"
        else
           return "无介绍报错管理员"
        end
    elseif 名称=="感念"  then
       if 门派=="普陀山" then
          return "莲台仙子:本方每有一个单位带有“普渡众生”灵动九天额外作用1个单位。\n五行咒师:敌方每有一个单位带有“紧箍咒”，灵动九天额外作用1个单位。"
        elseif 门派=="化生寺"  then
           return "你的“韦陀护法”效果提高150%，但持续时间变为1回合。"
        else
           return "无介绍报错管理员"
        end
    elseif 名称=="静心"  then
       if 门派=="普陀山" then
          return "本方玩家单位抵抗封印几率增加3%(不可叠加)"
        elseif 门派=="女儿村"  then
           return "使用原始愤怒消耗≥80点以上的特技后，获得“乐韵”。"
        else
           return "无介绍报错管理员"
        end
    elseif 名称=="矫健"  then
       if 门派=="狮驼岭" then
          return "自身气血≥70%时，造成的物理伤害提高10%。"
        elseif 门派=="女儿村"  then
           return "自身气血≥90%时，造成的物理伤害提高10%"
        else
           return "无介绍报错管理员"
        end
    elseif 名称=="怒火"  then
       if 门派=="狮驼岭" then
          return "使用破血狂攻或破碎无双后，你的召唤兽本回合造成的伤害提高32%。"
        elseif 门派=="凌波城"  then
           return "增加（战意点数×战意点数）×6的伤害力。"
        elseif 门派=="天宫"  then
           return "当因气血损失而回复≥10的愤怒时，可以额外回复4点愤怒。"
        else
           return "无介绍报错管理员"
        end
    elseif 名称=="威吓"  then
       if 门派=="魔王寨" then
          return "触发“神焰”时，对召唤兽和召唤物伤害倍率增加42%。"
        elseif 门派=="天宫"  then
           return "五雷轰顶成功时造成的伤害比例在基础上增加8%并且目标本次攻击不再增加愤怒值。"
        else
           return "无介绍报错管理员"
        end
    elseif 名称=="余韵"  then
       if 门派=="女儿村" then
          return "带有你的暗器毒的单位，每回合额外减少30点魔法值。"
        elseif 门派=="天宫"  then
           return "魔兽之印、河东狮吼、圣灵之甲、碎甲术、停陷术、啸风诀的效果提高50%。"
        else
           return "无介绍报错管理员"
        end
    elseif 名称=="持戒"  then
       if 门派=="无底洞" then
          return "惊魂掌命中率变为原先的300%（无视命中率上限)；但持续回合数上限变为2。"
        elseif 门派=="化生寺"  then
           return "唧唧歪歪对首目标造成的法术伤害提高10%"
        else
           return "无介绍报错管理员"
        end
   elseif 名称=="混元"  then
       if 门派=="五庄观" then
          return "当你气血≥70%时，提高5%的伤害力。"
        elseif 门派=="凌波城"  then
           return "天崩地裂和天神怒斩攻击的目标如果在本回合死亡，有70%的几率附加“腾雷”：受到的治疗效果降低50%，持续3回合。"
        else
           return "无介绍报错管理员"
        end
   elseif 名称=="聚魂"  then
       if 门派=="阴曹地府" then
          return "摄魂的所有基础效果增加4.5%。"
        elseif 门派=="无底洞"  then
           return "与玩家战斗时：触发“裂魂”的基础几率降低50%，但你造成的固定伤害提高8%。"
        else
           return "无介绍报错管理员"
        end

  elseif 名称=="震怒"  then
        if 门派=="魔王寨" then
           return "飞砂走石增加40点法术伤害结果。"
         elseif 门派=="九黎城"  then
            return "使用一斧开天未令目标浮空时，获得“怒哮”的几率提升100%；进入战斗时自动附加“怒哮”。"
         else
            return "无介绍报错管理员"
         end

  elseif 名称=="救主"  then
      if 门派=="狮驼岭" then
          return "你阵亡后，你的召唤兽将在回合结束时尝试消耗1个九转回魂丹复活你（有50%的几率成功，失败不消耗道具)。"
        elseif 门派=="神木林"  then
           return "你阵亡后，你的召唤兽在回合结束时对你使用百草复苏，每场战斗成功一次后不再尝试。"
        else
           return "无介绍报错管理员"
        end
 elseif 名称=="灵能"  then
      if 门派=="方寸山" then
          return "造成伤害后,有几率使得本次伤害提高12%"
        elseif 门派=="大唐官府"  then
           return "造成伤害后,有几率使得本次伤害提高12%"
        else
           return "无介绍报错管理员"
        end
    else
      return "无介绍报错管理员"
    end
end




local function 取奇经八脉(门派,当前经脉)
    if  门派== "大唐官府" then ---
      if  当前经脉 == "浴血豪侠" then
        return  {"目空","风刃","扶阵","翩鸿","勇武","长驱直入","杀意","贪心","静岳","干将","勇念","神凝","执剑","不惊","傲视","破空","历战","安神","额外能力","无敌","浴血豪侠"}
        elseif 当前经脉 == "无双战神" then
        return {"目空","勇进","突刺","翩鸿","勇武","长驱直入","亢强","贪心","静岳","干将","勇念","神凝","执剑","不惊","突进","乘胜","孤勇","熟练","额外能力","破军","无双战神"}
        elseif 当前经脉 == "虎贲上将" then
        return {"潜心","笃志","昂扬","效法","追戮","烈光","摧枯拉朽","肃杀","历兵","怒伤","奉还","催迫","攻伐","暴突","诛伤","灵能","奋战","破刃","额外能力","披挂上阵","虎贲上将"}
      end
    elseif  门派== "方寸山" then----
      if  当前经脉 == "拘灵散修" then
        return {"雷动","苦缠","灵咒","黄粱","制约","必果","补缺","不倦","精炼","化身","调息","幻变","斗法","吐纳","专神","鬼念","灵威","碎甲","额外能力","顺势而为","拘灵散修"}
        elseif 当前经脉 == "伏魔天师" then
        return{"驱雷","策电","雷动","鬼恸","穿透","余悸","宝诀","妙用","不灭","化身","怒霆","批亢","顺势","炼魂","吐纳","灵能","囚笼","摧心","额外能力","钟馗论道","伏魔天师"}
        elseif 当前经脉 == "五雷正宗" then
        return{"五雷挪移","雷动","天箓","咒诀","穿透","符威","宝诀","妙用","不灭","雷法翻天","雷鸣","雷法倒海","顺势","神机","吐纳","造化","囚笼","摧心","额外能力","奇门","五雷正宗"}
      end

    elseif  门派== "女儿村" then---
      if  当前经脉 == "绝代妖娆" then
        return {"独尊","暗伤","重明","倩影","花舞","风行","傲娇","花护","空灵","叶护","国色","轻霜","抑怒","机巧","毒雾","嫣然","磐石","倾国","额外能力","碎玉弄影","绝代妖娆"}
        elseif 当前经脉 == "花雨伊人" then
        return{"涂毒","杏花","暗伤","淬芒","花舞","暗刃","傲娇","花护","天香","轻霜","花影","百花","毒雾","毒引","余韵","磐石","飞花","花殇","额外能力","鸿渐于陆","花雨伊人"}
        elseif 当前经脉 == "妙舞佳人" then
        return{"天籁","即兴","夺目","独舞","静心","嫣然曼舞","翻飞","水漾","惊鸿起舞","矫健","花雨","跃动","幽美","风姿","风回旋舞","纷舞","轻霜","映日妙舞","额外能力","余韵索心","妙舞佳人"}
      end

    elseif  门派== "化生寺" then----
      if  当前经脉 == "杏林妙手" then
        return{"销武","止戈","圣手","妙手","仁心","化瘀","佛显","心韧","归气","天照","舍利","佛佑","佛法","佛性","妙悟","慈心","虔诚","佛缘","额外能力","渡劫金身","杏林妙手"}
        elseif 当前经脉 == "护法金刚" then
        return{"施他","佛屠","销武","聚念","仁心","磅礴","佛显","心韧","归气","感念","舍利","无碍","佛法","佛性","妙悟","慈心","映法","流刚","额外能力","诸天看护","护法金刚"}
        elseif 当前经脉 == "无量尊者" then
        return {"诵律","授业","修习","诵经","悲悯","解惑","持戒","生花","悟彻","抚琴","舍利","静气","自在","无量","慧定","金刚","达摩","韦陀","额外能力","坐禅","无量尊者"}
      end
    elseif  门派== "盘丝洞" then-------
      if  当前经脉 == "风华舞圣" then
        return{"粘附","妖气","怜心","迷瘴","鼓乐","魔音","玲珑","安抚","丹香","迷梦","忘川","连绵","情劫","绝殇","绵密","结阵","媚态","绝媚","额外能力","落花成泥","风华舞圣"}
        elseif 当前经脉 == "迷情妖姬" then
        return{"粘附","妖气","怜心","迷瘴","鼓乐","忘忧","玲珑","安抚","倾情","连绵","忘川","意乱","情劫","魔瘴","迷意","结阵","绝媚","利刃","额外能力","偷龙转凤","迷情妖姬"}
        elseif 当前经脉 == "百媚魔姝" then
        return{"粘附","杀戮","罗网","天网","凌弱","制怒","狂击","千蛛","引诛","附骨","亡缚","罗刹","障眼","连绵","意乱","结阵","牵魂蛛丝","扑袭","额外能力","绝命毒牙","百媚魔姝"}
      end
    elseif  门派== "阴曹地府" then-----
      if  当前经脉 == "勾魂阎罗" then
        return{"阎罗","回旋","夜行","入骨","聚魂","拘魄","索魂","伤魂","克敌","黄泉","幽冥","冥视","幽光","泉暴","鬼火","魂飞","汲魂","扼命","额外能力","魍魉追魂","勾魂阎罗"}
        elseif 当前经脉 == "六道魍魉" then
        return{"阎罗","回旋","夜行","聚魂","狱火","六道","索魂","伤魂","百炼","追命","幽冥","百爪狂杀","咒令","泉暴","鬼火","恶焰","汲魂","噬毒","额外能力","夜之王者","六道魍魉"}
        elseif 当前经脉 == "诛刑毒师" then
        return{"毒炽","回旋","阴翳","聚魂","破毒","入魂","毒慑","破印","瘴幕","无赦咒令","幽冥","通暝","狂宴","鬼火","轮回","追命","汲魂","恶焰","额外能力","生杀予夺","诛刑毒师"}
      end

    elseif  门派== "魔王寨" then---
      if  当前经脉 == "平天大圣" then
        return{"充沛","震怒","激怒","蚀天","邪火","赤暖","火神","震天","真炎","神焰","崩摧","焚尽","咆哮","狂月","燃魂","威吓","连营","魔心","额外能力","魔焰滔天","平天大圣"}
        elseif 当前经脉 == "盖世魔君" then
        return{"充沛","震怒","炙烤","烈焰","赤暖","邪火","火神","震天","折服","焰星","崩摧","焰威","咆哮","狂月","魔焱","威吓","连营","狂劲","额外能力","升温","盖世魔君"}
        elseif 当前经脉 == "风火妖王" then
        return{"秘传三昧真火","烈火真言","秘传飞砂走石","极炙","咒言","摧山","不忿","震天","融骨","神焰","焦土","不灭","烬藏","固基","惊悟","威吓","旋阳","魔心","额外能力","燎原","风火妖王"}
      end
    elseif  门派== "狮驼岭" then---
      if  当前经脉 == "嗜血狂魔" then
        return{"爪印","迅捷","驭兽","化血","宁息","兽王","威压","鹰啸","怒象","九天","魔息","协战","怒火","狂袭","癫狂","死地","乱击","肝胆","额外能力","背水","嗜血狂魔"}
        elseif 当前经脉 == "万兽之王" then
        return{"狮王","健壮","图腾","急救","饮血","守势","护盾","狂化","矫健","协同","九天","嗜血","羁绊","獠牙","钢牙","复仇","逞凶","救主","额外能力","最佳搭档","万兽之王"}
        elseif 当前经脉 == "狂怒斗兽" then
        return{"狂躁","狂化","狂啸","攫取","屏息","不羁","狮噬","象踏","长啸","九天","魔息","协战","羁绊","狂袭","狂血","狂乱","雄风","狩猎","额外能力","困兽之斗","狂怒斗兽"}
      end

    elseif  门派== "天宫" then
      if  当前经脉 == "镇妖神使" then
        return{"威吓","疾雷","轰鸣","趁虚","余韵","天威","震慑","神念","藏招","苏醒","护佑","坚壁","月桂","怒火","套索","神律","神尊","洞察","额外能力","画地为牢","镇妖神使"}
        elseif 当前经脉 == "踏雷天尊" then
        return{"频变","威吓","惊曜","霜雷","轰鸣","驭意","电掣","神念","伏魔","雷霆汹涌","苏醒","天劫","怒电","共鸣","灵光","洞察","仙音","雷波","额外能力","风雷韵动","踏雷天尊"}
        elseif 当前经脉 == "霹雳真君" then
        return{"霆震","疾雷","激越","存雄","余韵","慨叹","电掣","伏魔","惊霆","雷吞","苏醒","电光火石","神采","劲健","啸傲","神律","气势","洞察","额外能力","威仪九霄","霹雳真君"}
      end

    elseif  门派== "普陀山" then
      if  当前经脉 == "莲台仙子" then
        return{"推衍","化戈","普照","莲花心音","静心","慈佑","劳心","普渡","度厄","甘露","清净","莲动","法华","灵动","感念","玉帛","雨润","道衍","额外能力","波澜不惊","莲台仙子"}
        elseif 当前经脉 == "五行咒师" then
        return{"庄严","借灵","推衍","默诵","静心","莲花心音","赐咒","普渡","慧眼","无怖","清净","秘术","感念","莲心剑意","灵动","道衍","缘起","法咒","额外能力","五行制化","五行咒师"}
        elseif 当前经脉 == "落伽神女" then
        return{"湛然","因缘","莲音","安忍","静心","低眉","顿悟","怒目","馀威","清净","业障","困兽","无尽","抖擞","莲华","化用","智念","执念","额外能力","万象","落伽神女"}
      end

    elseif  门派== "五庄观" then
      if  当前经脉 == "清心羽客" then
        return{"体恤","运转","行气","心浪","养生","蓄志","归本","修心","存思","修身","同辉","守中","乾坤","意境","存神","陌宝","心随意动","玄机","额外能力","清风望月","清心羽客"}
        elseif 当前经脉 == "乾坤力士" then
        return{"体恤","锤炼","神附","心浪","养生","强击","无极","修心","混元","修身","剑气","雨杀","意境","起雨","滂沱","剑势","心随意动","致命","额外能力","天命剑法","乾坤力士"}
        elseif 当前经脉 == "万寿真仙" then
        return{"木摧","道果","饮露","炼果","心浪","聚力","无极","修心","混元","刺果","修身","三元","凝神","纳气","气盛","剑势","还元","致命","额外能力","归真","万寿真仙"}
      end
    elseif  门派== "龙宫" then----
      if  当前经脉 == "海中蛟虬" then
        return{"波涛","破浪","狂浪","叱咤","踏涛","龙啸","逐浪","龙珠","龙息","龙慑","傲翔","飞龙","骇浪","月光","戏珠","汹涌","龙魄","斩浪","额外能力","亢龙归海","海中蛟虬"}
        elseif 当前经脉 == "云龙真身" then
        return{"波涛","破浪","云霄","呼风","踏涛","清吟","龙息","龙珠","唤雨","龙慑","傲翔","飞龙","戏珠","月光","云变","沐雨","龙魄","摧意","额外能力","雷浪穿云","云龙真身"}
        elseif 当前经脉 == "沧海潜龙" then
        return{"傲岸","云魂","雨魄","盘龙","踏涛","叱咤","凛然","龙珠","回灵","龙慑","傲翔","飞龙","戏珠","月光","波涛","龙钩","琴魂","惊鸿","额外能力","潜龙在渊","沧海潜龙"}
      end
    elseif  门派== "神木林" then----------------------
      if  当前经脉 == "通天法王" then
        return{"法身","风魂","灵佑","追击","咒法","狂叶","劲草","冰锥","苍埃","神木","月影","薪火","纯净","蔓延","破杀","星光","滋养","灵归","额外能力","风卷残云","通天法王"}
        elseif 当前经脉 == "巫影祭司" then
        return{"风魂","迷缚","法身","伏毒","咒法","灵木","绞藤","冰锥","寄生","神木","月影","薪火","纯净","蔓延","破杀","激活","滋养","毒萃","额外能力","凋零之歌","巫影祭司"}
        elseif 当前经脉 == "灵木药宗" then
        return{"木魂","绿茵","滋润","明心","反哺","祛除","药颂","补血","灵精","风神","月影","转化","纯净","救主","净化","润泽","木精","残余","额外能力","百草古树长青","灵木药宗"}
      end
    elseif 门派== "凌波城" then
      if  当前经脉 == "九天武圣" then
        return{"山破","战诀","无双","聚气","贯通","魂聚","神躯","冰暴","不动","力战","破击","巧变","海沸","怒火","煞气","强袭","混元","再战","额外能力","天神怒斩","九天武圣"}
        elseif 当前经脉 == "灵霄斗士" then
        return{"石摧","战诀","天泽","聚气","贯通","魂聚","神躯","涡流","不动","妙得","闪雷","惊涛","海沸","怒火","煞气","乘势","追袭","再战","额外能力","真君显灵","灵霄斗士"}
        elseif 当前经脉 == "风云战将" then
        return{"山破","战诀","拍岸","怒眼","贯通","魂聚","神躯","砥石","不动","威震","盛势","天眼","海沸","怒火","煞气","蓄势","杀罚","再战","额外能力","耳目一新","风云战将"}
      end
    elseif 门派== "无底洞" then----------------
      if  当前经脉 == "妙谛金莲" then
        return{"灵照","秉幽","护法","涌泉","绝处逢生","烛照","华光","风墙","血潮","精进","救人","疗愈","持戒","罗汉","灵通","忍辱","暗潮","噬魂","额外能力","同舟共济","妙谛金莲"}
        elseif 当前经脉 == "摄魂迷影" then
        return{"阴魅","诡印","萦魄","御兽","绝处逢生","陷阱","椎骨","风墙","血潮","妖法","精进","救人","烈煞","持戒","罗汉","忍辱","暗潮","噬魂","额外能力","妖风四起","摄魂迷影"}
        elseif 当前经脉 == "幽冥巫煞" then
        return{"弥愤","魂守","刺骨","余咒","鬼袭","羽裂","分魄","盛怒","血潮","夺血","灵变","深刻","牵动","独一","聚魂","纠缠","灵身","踏魄","额外能力","冥煞","幽冥巫煞"}
      end
    elseif 门派== "九黎城" then----------------
      return{"枫魂","怒刃","震怒","俾睨","识破","得势","飞扬","凌人","生风","蛮横","难保","乘风","擎天","族魂","魂力","狂暴","驭魔","野蛮","额外能力","魔神之刃","铁火战魔"}

    end
end


local function 取额外能力介绍(门派,当前经脉)
	 if  门派== "大唐官府" then
      if  当前经脉 == "浴血豪侠" then
        return  "增加40点伤害"
        elseif 当前经脉 == "无双战神" then
        return "增加280点气血"
        elseif 当前经脉 == "虎贲上将" then
        return "增加30点速度"
        end
    elseif  门派== "方寸山" then
      if  当前经脉 == "拘灵散修" then
        return "增加30点速度"
        elseif 当前经脉 == "伏魔天师" then
        return "增加40点法术伤害"
        elseif 当前经脉 == "五雷正宗" then
        return "增加60点法术暴击等级"
      end

    elseif  门派== "女儿村" then
      if  当前经脉 == "绝代妖娆" then
        return "增加60点封印命中等级"
        elseif 当前经脉 == "花雨伊人" then
        return "增加80点抵抗封印等级"
        elseif 当前经脉 == "妙舞佳人" then
        return "增加60点封印命中等级"
      end

    elseif  门派== "化生寺" then
      if  当前经脉 == "杏林妙手" then
        return "增加30点治疗能力"
        elseif 当前经脉 == "护法金刚" then
        return "增加80点法术防御"
        elseif 当前经脉 == "无量尊者" then
        return "增加40点法术伤害"
      end
    elseif  门派== "盘丝洞" then
      if  当前经脉 == "风华舞圣" then
        return "增加80点法术防御"
        elseif 当前经脉 == "迷情妖姬" then
        return "增加30点速度"
        elseif 当前经脉 == "百媚魔姝" then
        return "增加80点抵抗封印等级"
      end
    elseif  门派== "阴曹地府" then
      if  当前经脉 == "勾魂阎罗" then
        return "增加30点速度"
        elseif 当前经脉 == "六道魍魉" then
        return "增加40点伤害"
        elseif 当前经脉 == "诛刑毒师" then
        return "增加60点物理暴击等级"
      end

    elseif  门派== "魔王寨" then
      if  当前经脉 == "平天大圣" then
        return "增加40点法术伤害"
        elseif 当前经脉 == "盖世魔君" then
        return "增加80点防御"
        elseif 当前经脉 == "风火妖王" then
        return "增加60点法术暴击等级"
      end
    elseif  门派== "狮驼岭" then
      if  当前经脉 == "嗜血狂魔" then
        return "增加30点速度"
        elseif 当前经脉 == "万兽之王" then
        return "增加60点物理暴击等级"
        elseif 当前经脉 == "狂怒斗兽" then
        return "增加40点伤害"
      end

    elseif  门派== "天宫" then
      if  当前经脉 == "镇妖神使" then
        return "增加60点封印命中等级"
        elseif 当前经脉 == "踏雷天尊" then
        return "增加40点法术伤害"
        elseif 当前经脉 == "霹雳真君" then
        return "增加40点伤害"
      end
    elseif  门派== "普陀山" then
      if  当前经脉 == "莲台仙子" then
        return "增加80点法术防御"
        elseif 当前经脉 == "五行咒师" then
        return "增加30点速度"
        elseif 当前经脉 == "落伽神女" then
        return "增加40点伤害"
      end

    elseif  门派== "五庄观" then
      if  当前经脉 == "清心羽客" then
        return "增加30点速度"
        elseif 当前经脉 == "乾坤力士" then
        return "增加40点伤害"
        elseif 当前经脉 == "万寿真仙" then
        return "增加60点物理暴击等级"
      end
    elseif  门派== "龙宫" then
      if  当前经脉 == "海中蛟虬" then
        return "增加40点法术伤害"
        elseif 当前经脉 == "云龙真身" then
        return "增加80点防御"
        elseif 当前经脉 == "沧海潜龙" then
        return "增加60点法术暴击等级"
      end
    elseif  门派== "神木林" then
      if  当前经脉 == "通天法王" then
        return "增加40点法术伤害"
        elseif 当前经脉 == "巫影祭司" then
        return "增加280点气血"
        elseif 当前经脉 == "灵木药宗" then
        return "增加80点抵抗封印等级"
      end
    elseif 门派== "凌波城" then
      if  当前经脉 == "九天武圣" then
        return "增加40点伤害"
        elseif 当前经脉 == "灵霄斗士" then
        return "增加30点速度"
        elseif 当前经脉 == "风云战将" then
        return "增加40点伤害"
      end
    elseif 门派== "无底洞" then
      if  当前经脉 == "妙谛金莲" then
        return "增加80点法术防御"
        elseif 当前经脉 == "摄魂迷影" then
        return "增加60点封印命中等级"
        elseif 当前经脉 == "幽冥巫煞" then
        return "增加80点防御"
      end
    elseif 门派== "九黎城" then
      return "增加30点速度"
    end

end



function 多开经脉:初始化()
  self:置精灵(__res:取资源动画("jszy/jmxf", 0xabcde007,"精灵")) 
  self.经脉小图标 = __res:取资源动画("jszy/jmxf", 0xabcde006,"精灵")
  self.技能遮罩 =__res:取资源动画("jszy/jmxf", 0xabcde008,"精灵") 
  self.技能框框 = __res:取资源动画("dlzy", 0x22D22D6D,"动画")  
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  self.显示技能={}
  for m=1,21 do
      local lssj =  __技能格子:创建()
      lssj:置数据(nil)
      self.显示技能[m]=lssj
  end
  self.师门技能={}
  for i = 1, 5 do
      local lssj =  __技能格子:创建()
      lssj:置数据(nil)
      self.师门技能[i]=lssj
  end
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  

end





function 多开经脉:打开(数据,玩家id)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  
  self:刷新(数据,玩家id)
end

function 多开经脉:刷新(数据,玩家id)
    self.玩家id = 玩家id
    self.数据 = table.copy(数据)
    self.查看流派 =self.数据.经脉流派
    self.奇经八脉 =self.数据.奇经八脉[self.查看流派]
    self.法宝显示={}
    self.神器显示={}
 
    self:显示设置()
end

function 多开经脉:显示设置()
  self.启用流派:置可见(false)
  self.确定加点:置禁止(true)
  self.取消:置禁止(true)
  self.选中 = nil
  self.焦点 = nil
  self.师门焦点=nil
  self.物品焦点=nil
  self.多开经脉 = self.数据.奇经八脉[self.查看流派]
  local qj = 取奇经八脉(self.数据.门派,self.查看流派)
  for m=1,21 do
      self.显示技能[m]:置数据(qj[m],nil,nil,nil,true)
      self.显示技能[m]:置灰色(true)
      self.显示技能[m].尝试=nil
    if qj[m]=="不灭" or qj[m]=="感念" or qj[m]=="静心" or qj[m]=="矫健"
       or qj[m]=="怒火" or qj[m]=="威吓" or qj[m]=="余韵" or qj[m]=="持戒"
       or qj[m]=="混元" or qj[m]=="聚魂" or qj[m]=="救主" or qj[m]=="灵能" 
       or qj[m]=="震怒" then
      self.显示技能[m].数据.介绍=取重复技能介绍(qj[m],self.数据.门派)
    end
    if m==19 or m==21  then 
        self.显示技能[m].数据.介绍 = 取额外能力介绍(self.数据.门派,self.查看流派) 
    end
  end
  for i=1,#self.奇经八脉 do
        self.显示技能[self.奇经八脉[i]].尝试 = true
  end
  for i, v in ipairs( self.显示技能) do
      if v.尝试 then
          v:置灰色(false)
      end
  end
  if #self.奇经八脉 <= 0 then
      self.技能树 = {1,2,3}
  else
      self.技能树 = self:技能树设定(self.奇经八脉[#self.奇经八脉])
  end
 
  for i=1,5 do
        self.师门技能[i]:置数据(取门派介绍技能[self.数据.门派][self.查看流派][i])
        if self.查看流派 ~= self.数据.经脉流派 then
            self.师门技能[i]:置灰色(true)
        else
            self.师门技能[i]:置灰色(false)
        end
  end
  local 法宝  = __物品格子:创建()
  法宝:置物品(取门派法宝[self.数据.门派],50,50)
  self.法宝显示=法宝
  local 神器 = __物品格子:创建()
  神器:置物品(门派神器名称[self.数据.门派],50,50) 
  self.神器显示=神器
  self.定位建议={}
  local 门派动画图标=取门派小动画[取门派动画[self.数据.门派][self.查看流派]]
  local 定位建议图标=取门派说明[self.数据.门派][self.查看流派]
  if self.查看流派 ~= self.数据.经脉流派 then
      self.法宝显示:置灰色(true)
      self.神器显示:置灰色(true)
      self.经脉小图标 = __res:取资源动画("jszy/jmkz", 门派动画图标,"图像"):到灰度():到精灵()
      for i=1,#定位建议图标 do
          self.定位建议[i]=__res:取资源动画("jszy/jmkz", 取门派文字动画[定位建议图标[i]],"图像"):到灰度():到精灵()
      end
      self.启用流派:置可见(true)
  else
      self.法宝显示:置灰色(false)
      self.神器显示:置灰色(false)
      self.经脉小图标 = __res:取资源动画("jszy/jmkz", 门派动画图标,"精灵")
      for i=1,#定位建议图标 do
          self.定位建议[i]=__res:取资源动画("jszy/jmkz", 取门派文字动画[定位建议图标[i]],"精灵")
      end
  end
  self:字体设置()
  self.流派按钮:置按钮()
end

function 多开经脉:字体设置()
    self.图像 =  self:创建纹理精灵(function()
        说明字体:置颜色(0,0,0,255):取图像("已"):显示(577,170)
        说明字体:置颜色(0,0,0,255):取图像("用"):显示(577,188)
        说明字体:置颜色(0,0,0,255):取图像("乾"):显示(577,206)
        说明字体:置颜色(0,0,0,255):取图像("元"):显示(577,224)
        说明字体:置颜色(0,0,0,255):取图像("丹"):显示(577,240)
        说明字体:置颜色(0,0,0,255):取图像("可"):显示(577,285)
        说明字体:置颜色(0,0,0,255):取图像("用"):显示(577,303)
        说明字体:置颜色(0,0,0,255):取图像("乾"):显示(577,321)
        说明字体:置颜色(0,0,0,255):取图像("元"):显示(577,339)
        说明字体:置颜色(0,0,0,255):取图像("丹"):显示(577,357)
   
        说明字体:置颜色(255,255,255,255):取图像("可换乾元丹: "..self.数据.乾元丹.可换乾元丹):显示(5,442)
        说明字体:置颜色(255,255,255,255):取图像(self.查看流派):显示(170,442)
    
      if string.len(self.数据.乾元丹.乾元丹) == 1 then
         说明字体:置颜色(__取颜色("红色")):取图像(self.数据.乾元丹.乾元丹):显示(580,260)
      else
         说明字体:置颜色(__取颜色("红色")):取图像(self.数据.乾元丹.乾元丹):显示(576,260)
      end
      if string.len(self.数据.乾元丹.剩余乾元丹) == 1 then
         说明字体:置颜色(__取颜色("红色")):取图像(self.数据.乾元丹.剩余乾元丹):显示(580,375)
      else
         说明字体:置颜色(__取颜色("红色")):取图像(self.数据.乾元丹.剩余乾元丹):显示(576,375)
      end
      道具字体:置颜色(0,0,0,255):取图像(self.查看流派):显示(100,83)
      说明字体:置颜色(0,0,0,255):取图像(取门派说明上[self.数据.门派][self.查看流派]):显示(100,110)
       local zx = 0
       local zy = 0
         if  #取门派介绍技能[self.数据.门派][self.查看流派]>4 then
               for i=1,#取门派介绍技能[self.数据.门派][self.查看流派] do
                   if  self.师门技能[i] and self.师门技能[i].数据 then
                       if self.查看流派~=self.数据.经脉流派 then
                           说明字体:置颜色(125,125,125):取图像(取门派介绍技能[self.数据.门派][self.查看流派][i]):显示(77+zx*120,226+zy*58)
                       else
                           说明字体:置颜色(0,0,0,255):取图像(取门派介绍技能[self.数据.门派][self.查看流派][i]):显示(77+zx*120,226+zy*58)
                       end
                       zx =zx + 1
                       if zx >=3 then
                         zx= 0
                         zy =zy + 1
                       end
                   end
               end
         else
               for i=1,#取门派介绍技能[self.数据.门派][self.查看流派] do
                   if   self.师门技能[i] and self.师门技能[i].数据 then
                         if self.查看流派~=self.数据.经脉流派 then
                             说明字体:置颜色(125,125,125):取图像(取门派介绍技能[self.数据.门派][self.查看流派][i]):显示(85+zx*175,226+zy*58)
                         else
                             说明字体:置颜色(0,0,0,255):取图像(取门派介绍技能[self.数据.门派][self.查看流派][i]):显示(85+zx*175,226+zy*58)
                         end
                         zx =zx + 1
                         if zx >=2 then
                           zx= 0
                             zy =zy + 1
                         end
                     end
               end
         end
      end,1
    )  
end

function 多开经脉:更新(dt)
   self.技能框框:更新(dt)
end


function 多开经脉:显示(x,y)
  if self.图像 then
     self.图像:显示(x,y)
  end

  self.经脉小图标:显示(x+25,y+75)
  local xx = 0
	local yy = 0
	for n=1,21 do
      if self.焦点~=n and self.显示技能[n].焦点 then
          self.显示技能[n].焦点=nil
      end
      self.显示技能[n]:显示(x + xx * 60 + 402,y +70+ yy * 52)
      if n == 19 or n == 21 then
        self.技能遮罩:显示(x + xx * 60 + 400,y +64+ yy * 52)
      end
      if self.技能树 ~=nil then
          for i=1,3 do
              if self.技能树[i] ~= nil and self.技能树[i]==n  then
                self.技能框框:显示(x + xx * 60 + 392,y +60+ yy * 52)
              end
          end
      end

		xx = xx + 1
		if xx >= 3 then
			xx = 0
			yy = yy + 1
		end
end

if self.定位建议 then
   for i, v in ipairs(self.定位建议) do
        v:显示(x+18+i*74,y+150)
   end
end

if self.法宝显示 and self.法宝显示.物品  then
    if self.物品焦点~=1 and self.法宝显示.焦点 then
        self.法宝显示.焦点=nil
    end
	  self.法宝显示:显示(x + 30,y + 363)
end
if  self.神器显示 and self.神器显示.物品  then
    if self.物品焦点~=2 and self.神器显示.焦点 then
      self.神器显示.焦点=nil
    end
    self.神器显示:显示(x + 204,y + 363)
end

if  self.师门技能 then
    local zx = 0
    local zy = 0
    if  #取门派介绍技能[self.数据.门派][self.查看流派]>4 then
          for i=1,5 do
              if self.师门焦点~=i and self.师门技能[i].焦点 then
                  self.师门技能[i].焦点 =nil
              end
              self.师门技能[i]:显示(x+30+zx*120,y+216+zy*58)
              zx =zx + 1
              if zx >=3 then
                zx= 0
                zy =zy + 1
              end
          end
    else
          for i=1,4 do
              if self.师门焦点~=i and self.师门技能[i].焦点 then
                  self.师门技能[i].焦点 =nil
              end
              self.师门技能[i]:显示(x+30+zx*175,y+216+zy*58)
              zx =zx + 1
              if zx >=2 then
                  zx= 0
                  zy =zy + 1
              end
           
          end
    end

end

end
function 多开经脉:获得鼠标(x, y)
    self.焦点=nil
    self.师门焦点=nil
    self.物品焦点=nil
    for n=1,21 do
      if self.显示技能[n].数据 and self.显示技能[n]:检查透明(x, y) then
          self.焦点=n
          self.显示技能[n].焦点=true
          __UI弹出.技能详情:打开(self.显示技能[n].数据,x,y)
      end
    end
    if self.法宝显示 and self.法宝显示.物品 and self.法宝显示:检查透明(x, y) then
        self.物品焦点=1
        self.法宝显示.焦点=true
        __UI弹出.道具提示:打开(self.法宝显示.物品,x,y)
    end
    if  self.神器显示 and self.神器显示.物品 and self.神器显示:检查透明(x, y)   then
      self.物品焦点=2
      self.神器显示.焦点=true
      __UI弹出.道具提示:打开(self.神器显示.物品,x,y)
    end
    
    for i=1,5 do
        if  self.师门技能[i].数据 and self.师门技能[i]:检查透明(x, y) then
            self.师门焦点=i
            self.师门技能[i].焦点=true
             __UI弹出.技能详情:打开(self.师门技能[i].数据,x,y)   
        end
    end
 

end
function 多开经脉:失去鼠标(x, y)
    self.焦点=nil
    self.师门焦点=nil
    self.物品焦点=nil
end
function 多开经脉:左键弹起(x, y)
  self.选中 = nil
  if not __手机 then
     self.手机选中 = nil
  end
  for n=1,21 do
    if self.显示技能[n].数据 and self.显示技能[n]:检查透明(x, y) then
        if  (self.手机选中  and  self.手机选中 == n) or not __手机 then
            local 是否可选 =false
            for i = 1, 3 do
                if self.技能树~=nil and self.技能树[i] ~= nil and self.技能树[i]==n then
                  是否可选 = true
                end
            end
            if 是否可选 then
                  self.选中 = n
                  self.显示技能[n]:置灰色(false)
                  if n ==20 then
                    self.显示技能[19]:置灰色(false)
                    self.显示技能[21]:置灰色(false)
                  end
                  self.技能树 = self:技能树设定(self.选中)
                  if  self.查看流派==self.数据.经脉流派 then
                      self.确定加点:置禁止(false)
                  end
                  self.取消:置禁止(false)
            else
                  self.选中 = nil
                  self.手机选中 = nil
            end
        elseif __手机 then
              self.手机选中 = n
              __UI弹出.技能详情:打开(self.显示技能[n].数据,x,y)
            
        end
    end
  end

  if self.法宝显示 and self.法宝显示.物品 and self.法宝显示:检查透明(x, y) and  __手机  then
        __UI弹出.道具提示:打开(self.法宝显示.物品,x,y)
  end
  if  self.神器显示 and self.神器显示.物品 and self.神器显示:检查透明(x, y)  and  __手机 then
      __UI弹出.道具提示:打开(self.神器显示.物品,x,y)
  end

  
    for i=1,5 do
          if self.师门技能[i].数据 and self.师门技能[i]:检查透明(x, y) and  __手机 then
            __UI弹出.技能详情:打开(self.师门技能[i].数据,x,y)   
          end
      end


end

local 流派按钮 = 多开经脉:创建网格("流派按钮", 20, 25, 570, 30)
function 流派按钮:初始化()
      self:创建格子(170,30,0,20, 1, 3)
end

function 流派按钮:置按钮()
        local 按钮文字 =取门派按键文字[多开经脉.数据.门派]
        for i = 1, #self.子控件 do
            self.子控件[i]:创建纹理精灵(function()
              if  多开经脉.查看流派==按钮文字[i] then
                  __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(170,30):显示(0,0)
              else
                  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(170,30):显示(0,0)
              end
              说明字体:置颜色(255,255,255,255):取图像(按钮文字[i]):显示((170-说明字体:取宽度(按钮文字[i]))//2,7)
              if  多开经脉.数据.经脉流派==按钮文字[i] then
                  __res:取资源动画("jszy/jmkz", 0x01000003,"图像"):显示(2,4)
              end
            end
          )  
        end
end

function 流派按钮:左键弹起(x, y, a)
      local 按钮文字 =取门派按键文字[多开经脉.数据.门派]
      if 按钮文字[a] then
        多开经脉.查看流派=按钮文字[a] 
        多开经脉:显示设置()
      end

end

local 启用流派 = 多开经脉:创建红色按钮("启用此流派","启用流派", 248, 437,120,30)
function 启用流派:左键弹起(x, y)
      窗口层.文本栏:打开("切换该流派需消耗100点体力",63,{参数=多开经脉.玩家id,文本="切换经脉",经脉=多开经脉.查看流派})

end

local 确定加点 = 多开经脉:创建红色按钮("确定加点","确定加点", 248+125, 437,80,30)
function 确定加点:左键弹起(x, y)
    if 多开经脉.查看流派 ~= 多开经脉.数据.经脉流派 then
        __UI弹出.提示框:打开('#Y请先启用此经脉后在操作')
        多开经脉:显示设置()
    else
        if 多开经脉.选中~=nil then
            请求服务(63,{参数=多开经脉.玩家id,文本="经脉加点",序列=多开经脉.选中})
            多开经脉.选中 = nil
        end
    end
end

local 清空经脉 = 多开经脉:创建红色按钮("清空经脉","清空经脉", 248+125+85, 437,80,30)
function 清空经脉:左键弹起(x, y)
    if 多开经脉.查看流派 ~= 多开经脉.数据.经脉流派 then
        __UI弹出.提示框:打开('#Y请先启用此经脉后在操作')
        多开经脉:显示设置()
    else
        窗口层.对话栏:打开(_tp.多角色[多开经脉.玩家id].模型,_tp.多角色[多开经脉.玩家id].名称,"请选择重置经脉",{"重置该角色当前经脉","重置该角色全部经脉"})
        -- tp.窗口.对话栏:文本(tp.队伍[1].模型,tp.队伍[1].名称, "请选择重置经脉",{"重置该角色当前经脉","重置该角色全部经脉"})
    end
end
local 取消 = 多开经脉:创建红色按钮("取消","取消",248+125+85+85, 437,50,30)
function 取消:左键弹起(x, y)
  多开经脉:显示设置()
end










function 多开经脉:技能树设定(a)

	if a == 1 or a == 4 or a == 7 or a == 10 or a == 13 or a == 16 then
		if a == 16 then
			return {20}
		else
			return {a+3,a+4}
		end
	elseif a == 2 or a == 5 or a == 8 or a == 11 or a == 14 or a == 17 then
		if a == 17 then
			return {20}
		else
			return {a+2,a+3,a+4}
		end
	elseif a == 3 or a == 6 or a == 9 or a == 12 or a == 15 or a == 18 then
		if a == 18 then
			return {20}
		else
			return {a+2,a+3}
		end
	end
end


local 关闭 = 多开经脉:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  多开经脉:置可见(false)
end









