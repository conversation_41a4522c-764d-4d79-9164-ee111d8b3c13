
local 任务提示 = 窗口层:创建窗口("任务提示", 0, 0, 430, 360)

local 副本数据={
  {"乌鸡国","#R/副本难度：低\n#L/找长安国子监吴举人进入副本"},
  {"通天河","#R/副本难度：高\n#L/找长寿村的蝴蝶女进入副本"},
  {"车迟斗法","#R/副本难度：低\n#L/找长寿村慧觉和尚进入副本"},
  {"水陆大会","#R/副本难度：低\n#L/找化生寺疥癞和尚进入副本"},
  {"齐天大圣","#R/副本难度：高\n#L/找傲来国红毛猿进入副本"},
}
-- {"黑风山","#R/副本难度：高\n#L/找大唐国境小二进入副本"},
-- {"大闹天宫","#R/副本难度：高\n#L/找长寿村太白金星进入副本"},
-- {"秘境降妖","#R/副本难度：高\n#L/找长安城的御林军左统领进入副本"},

function 任务提示:初始化()
  self:创建纹理精灵(function()
                    置窗口背景("任务提示", 0, 0, 430, 360, true):显示(0, 0)
                    蓝白标题背景(160,275,true):置透明(220):显示(15,65)
                    蓝白标题背景(230,275,true):置透明(220):显示(185,65)
            end
        )
      self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
      self.可初始化=true
      if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
      else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
      end
end
function 任务提示:显示(x,y)
    if  self.标题1 then 
      self.标题1:显示(x+15+(160-self.标题1.宽度)//2,y+70)
    end 
    if  self.标题2 then 
      self.标题2:显示(x+185+(230-self.标题2.宽度)//2,y+70)
    end 
end

function 任务提示:打开(内容)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
          return
    end
    self.状态="已接任务"
    self:刷新(内容)
end


function 任务提示:刷新(内容)
   self[self.状态]:置选中(true)
   self.任务数据 ={}
    if 内容 and 内容[1] then
        for k, v in pairs(内容) do
            if v[1] and v[2] then
                local 添加内容= ""
                if v[4] and v[4].x then
                  local 断句 = 分割文本(v[2],"@")
                  local 回调 = "#["..v[4].名称.."@"..v[4].x.."@"..v[4].y.."@"..v[4].地图.."$"..v[4].名称.."#]"
                    添加内容=断句[1].."#G#u"..回调.."#u#W"..断句[2]
                else
                    添加内容=v[2]
                end
                if v[3] then
                    添加内容=添加内容.."\n\n#86\n#L"..v[3]
                end
                local 添加 = {v[1],添加内容}
                table.insert(self.任务数据,添加)
            end
        end
    end
   self:刷新状态()
end


function 任务提示:刷新状态()
  self.任务文本:清空()
  self.名称列表:清空()
  if self.状态=="已接任务" then
        self.标题1= 标题字体:置颜色(255, 255, 255, 255):取精灵("任 务 列 表")
        self.标题2= 标题字体:置颜色(255, 255, 255, 255):取精灵("任 务 详 细")
        self.名称列表:置数据(self.任务数据)
   else
        self.标题1= 标题字体:置颜色(255, 255, 255, 255):取精灵("副 本 列 表")
        self.标题2= 标题字体:置颜色(255, 255, 255, 255):取精灵("副 本 详 细")
        self.名称列表:置数据(副本数据)
   end

end

local 按钮设置={"已接任务","副本介绍"}
for i, v in ipairs(按钮设置) do
      local 临时按钮=任务提示:创建红色单选按钮(v,v,17+(i-1)*80,33,74,22) 
      function 临时按钮:左键弹起(x,y)
          if 任务提示.状态~=v then
              任务提示.状态=v
              任务提示:刷新状态()
          end
      end
end



local 任务文本 = 任务提示:丰富文本("任务文本", 190, 95, 220, 240,true)
function 任务文本:初始化()
  self:置文字(文本字体)

end


function 任务文本:获得回调(x,y,cb)
  local lssj = 分割文本(cb, "@")
  __UI弹出.自定义:打开(x+20,y+20,"#Y"..lssj[1].."在"..取地图名称(tonumber(lssj[4])).."("..lssj[2]..","..lssj[3]..")")
end

function 任务文本:回调左键弹起(cb,x,y)
      if __手机 then
          local lssj = 分割文本(cb, "@")
          __UI弹出.自定义:打开(x+20,y+20,"#Y"..lssj[1].."在"..取地图名称(tonumber(lssj[4])).."("..lssj[2]..","..lssj[3]..")")
      end
      -- local 发送内容 = {名称=lssj[1],x=lssj[2],y=lssj[3],地图=tonumber(lssj[4])}
      -- 请求服务(41,发送内容)
end



local 名称列表 = 任务提示:创建列表("名称列表", 20, 95, 150, 240)
function 名称列表:初始化()
    self:置文字(标题字体)
    self:置颜色(0,0,0,255)
    self.行间距 = 5
end
function 名称列表:置数据(数据)
    self:清空()
    self.数据 = {}
    if 数据 and 数据 then
      self.数据=数据
    end
    
    for _, v in ipairs(self.数据) do
        self:添加(v[1])
    end
end
function 名称列表:左键弹起(x, y, i)
        任务文本:清空()
        任务文本:置文本(self.数据[i][2])
end 

local 关闭 = 任务提示:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
   任务提示:置可见(false)
end