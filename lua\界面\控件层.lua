--[[
Author: GGELUA
Date: 2024-09-04 16:18:55
Last Modified by: GGELUA
Last Modified time: 2024-10-28 17:49:07
--]]
require("界面/控件层/窗口背景")
require("界面/控件层/属性背景")
require("界面/控件层/输入背景")
require("界面/控件层/通用背景")
__角色选择格子 = require("界面/控件层/角色选择格子")
__物品格子 = require("界面/控件层/物品格子")
__物品助战格子 = require("界面/助战窗口/物品助战格子")
__材料格子 = require("界面/控件层/材料格子")
__商店格子 = require("界面/控件层/商店格子")
__头像格子 = require("界面/控件层/头像格子")
__头像格子2 = require("界面/控件层/头像格子2")
__头像选择格子 = require("界面/控件层/头像选择格子")
__UI模型格子 = require("界面/控件层/UI模型格子")
__技能格子 = require("界面/控件层/技能格子")
__技能格子2 = require("界面/控件层/技能格子2")
__技能格子3 = require("界面/控件层/技能格子3")
__召唤兽技能格子 = require("界面/控件层/召唤兽技能格子")
__内丹格子 = require("界面/控件层/内丹格子")
__特性格子 = require("界面/控件层/特性格子")
__队伍格子 = require("界面/控件层/队伍格子")
__助战模型 = require("界面/助战窗口/助战模型格子")
__助战bb格子 = require("界面/助战窗口/助战bb格子")
__阵型格子 = require("界面/控件层/阵型格子")
__摊位格子 = require("界面/控件层/摊位格子")
__商会格子 = require("界面/控件层/商会格子")
__经脉格子 = require("界面/控件层/经脉格子")
__战斗预选技能格子 = require("界面/控件层/战斗预选技能格子")
__商城格子 = require("界面/控件层/商城格子")
__商城锦衣格子 = require("界面/控件层/商城锦衣格子")
__建筑格子 = require("界面/控件层/建筑格子")
__成就格子 = require("界面/控件层/成就格子")
__小技能格子 = require("界面/控件层/小技能格子")
__40格子 = require("界面/控件层/标准40技能格子")
__赐福bb格子 = require("界面/超级技能类/赐福bb格子")

