local 基类 = require("界面/控件层/基类/物品基类")
local 物品格子 = class("物品格子", 基类)
function 物品格子:初始化()
  self.py = {x = 0, y = 0}
  self.物品 = nil
  self.确定 = nil
  self.类型 = nil
  self.小模型 = nil
  self.宽度=0
  self.高度=0
  self.道具选中 = nil
  self.物品图像= nil
  self.物品格子=nil
  self.物品禁止 = false
  self.道具焦点 = nil
  self.专用背景 =__res:取资源动画("pic","zy.png","图片"):到精灵()
  self.加锁图标 =__res:取资源动画("jszy/xjiem",0X85655274,"精灵")
  self.赐福动画 =__res:取资源动画("dlzy",0x22D22D6D,"动画")

end

function 物品格子:置物品(数据,w, h,类型,格子,类别,附加)
    if not w then w =50 end
    if not h then h =50 end
    self.道具选中 = __res:取资源动画("dlzy",0x10921CA7,"图像"):拉伸(w, h):到精灵()
    self.道具焦点 = __res:取资源动画("dlzy",0x6F88F494,"图像"):拉伸(w, h):到精灵()
    self.格子背景 = __res:取资源动画("dlzy",0xB17505CF,"图像"):拉伸(w, h):到精灵()
    self.绿装边框 =__res:取资源动画("jszy/zbpjsjxt",0X00000038,"图像"):拉伸(w, h):到精灵()
    self.蓝装边框 =__res:取资源动画("jszy/zbpjsjxt",0X00000037,"图像"):拉伸(w, h):到精灵()
    self.橙装边框 =__res:取资源动画("jszy/zbpjsjxt",0X00000035,"图像"):拉伸(w, h):到精灵()
    self.红装边框 =__res:取资源动画("jszy/zbpjsjxt",0X00000036,"图像"):拉伸(w, h):到精灵()
    self.物品 = nil
    self.确定 = nil
    self.类型 = nil
    self.类别 = nil
    self.附加 = nil
    self.格子 = nil
    self.小模型=nil
    self.宽度=0
    self.高度=0
    self.物品图像= nil
    self.物品格子=nil
    self.物品禁止 = false
    self.物品灰度=nil
    self.py = {x = 3, y = 3}
    if 数据 then
        if type(数据)=="table" and 数据.名称 and 数据.名称~="" and 数据.名称~= 0  then
            self:取数据(数据)
            self.物品.数量 = nil
            if 数据.数量 and 数据.数量~="" and 数据.可叠加 then
                self.物品.数量=数据.数量
            end
        elseif type(数据)=="string" then
                self:取数据({名称=数据})
        end
        if self.物品 then
            self.物品图像 = __res:取资源动画(self.物品.资源, self.物品.小模型资源,"图像")
            self.物品格子禁止 =  __res:取资源动画('ui',1094234215,"图像"):拉伸(w-10, h-10):到精灵()
        end
    end
    self:计算显示(w,h,类型,格子,类别,附加)
end

function 物品格子:计算显示(w,h,类型,格子,类别,附加)
  self.格子 = 格子
  self.类型=类型
  self.类别=类别
  self.附加=附加
  self.宽度=w
  self.高度=h
  local nsf = require('SDL.图像')(w+2, h+2)
  if nsf:渲染开始() then
            if self.物品图像 then
                if self.物品灰度 then
                    self.物品图像:到灰度():显示(self.py.x, self.py.y)
                else
                    self.物品图像:显示(self.py.x, self.py.y)
                end
                格子字体:置颜色(255,255,255,255)
                if self.类型 then
                    if type(self.类型)=="string"then
                        if self.类型 =="数量" and self.物品.数量 then
                            格子字体:取投影图像(self.物品.数量):显示(4,2)
                        elseif self.类型 ~="数量" then
                            格子字体:取投影图像(self.类型):显示(4,2)
                        end
                    elseif type(self.类型)=="number" then
                        格子字体:取投影图像(self.类型):显示(4,2)
                    end
                end
                if self.类别 then
                    格子字体:置颜色(255,255,255,255)
                    if not self.物品.可叠加 and (self.物品.名称 == "藏宝图" or self.物品.名称 == "高级藏宝图") and  self.物品.地图名称~=nil and string.find(self.物品.地图名称,"随机")==nil then
                        格子字体:取投影图像(self.物品.地图名称):显示((w-格子字体:取宽度(self.物品.地图名称))//2,h-15)
                    elseif not self.物品.可叠加 and  (self.物品.名称 == "百炼精铁" or self.物品.名称 == "元灵晶石") and self.物品.子类~=nil  then
                        格子字体:取投影图像(self.物品.子类.."级"):显示((w-格子字体:取宽度(self.物品.子类.."级"))//2,h-15)
                    elseif not self.物品.可叠加 and  self.物品.总类 == 5 and self.物品.分类== 6 and self.物品.级别限制~=nil then
                        格子字体:取投影图像(self.物品.级别限制.."级"):显示((w-格子字体:取宽度(self.物品.级别限制.."级"))//2,h-15)
                    elseif not self.物品.可叠加 and (self.物品.名称 == "炼妖石" or self.物品.名称 == "天眼珠") and self.物品.级别限制~=nil then
                        格子字体:取投影图像(self.物品.级别限制.."级"):显示((w-格子字体:取宽度(self.物品.级别限制.."级"))//2,h-15)
                    elseif not self.物品.可叠加 and self.物品.名称 == "制造指南书"   and self.物品.特效~=nil and self.物品.子类~=nil then
                        格子字体:取投影图像(_tp:取武器子类(self.物品.特效)):显示((w-格子字体:取宽度(_tp:取武器子类(self.物品.特效)))//2,h-30)
                        格子字体:取投影图像(self.物品.子类.."级"):显示((w-格子字体:取宽度(self.物品.子类.."级"))//2,h-15)
                    elseif not self.物品.可叠加 and self.物品.名称 == "灵饰指南书" and self.物品.特效~=nil and self.物品.子类~=nil then
                        格子字体:取投影图像(self.物品.特效):显示((w-格子字体:取宽度(self.物品.特效))//2,h-32)
                        格子字体:取投影图像(self.物品.子类.."级"):显示((w-格子字体:取宽度(self.物品.子类.."级"))//2,h-15)
                    elseif not self.物品.可叠加 and self.物品.名称 == "上古锻造图策" and self.物品.级别限制~=nil and self.物品.种类~=nil then
                        格子字体:取投影图像(self.物品.种类):显示((w-格子字体:取宽度(self.物品.种类))//2,h-32)
                        格子字体:取投影图像(self.物品.级别限制.."级"):显示((w-格子字体:取宽度(self.物品.级别限制.."级"))//2,h-15)
                    elseif not self.物品.可叠加 and (self.物品.名称 == "魔兽要诀" or self.物品.名称 == "高级魔兽要诀" or self.物品.名称 == "特殊魔兽要诀" or self.物品.名称 == "超级魔兽要诀" or self.物品.名称 == "召唤兽内丹"  or self.物品.名称 == "高级召唤兽内丹") and self.物品.附带技能~=nil then
                          if self.物品.名称 == "超级魔兽要诀" then
                              local 显示技能 = 分割文本(self.物品.附带技能,"超级")
                              格子字体:取投影图像(显示技能[2]):显示((w-格子字体:取宽度(显示技能[2]))//2+2,h-15)
                          elseif self.物品.名称 == "高级魔兽要诀" then
                                if string.find(self.物品.附带技能,"高级") then
                                    local 显示技能 = 分割文本(self.物品.附带技能,"高级")
                                    格子字体:取投影图像(显示技能[2]):显示((w-格子字体:取宽度(显示技能[2]))//2+2,h-15)
                                else
                                    格子字体:取投影图像(self.物品.附带技能):显示((w-格子字体:取宽度(self.物品.附带技能))//2+2,h-15)
                                end
                          else  
                              格子字体:取投影图像(self.物品.附带技能):显示((w-格子字体:取宽度(self.物品.附带技能))//2+2,h-15)
                          end
                      
                     
                    end
                end
                if self.附加 then
                    if type(self.附加)=="string" or type(self.附加)=="number" then
                        格子字体:置颜色(255,20,147,255):取投影图像(self.附加):显示((w-格子字体:取宽度(self.附加))//2,(h-格子字体:取高度(self.附加))//2-2)
                    elseif ggetype(self.附加)=="SDL图像" then
                            self.附加:显示((w-self.附加.宽度)//2,(h-self.附加.高度)//2-2)
                    elseif ggetype(self.附加)=="tcp" then
                        self.附加:取图像(1):显示((w- self.附加.宽度) // 2, (h - self.附加.高度) // 2 - 2)
                    end
                end
            end
            nsf:渲染结束()
      end
      self.小模型 = nsf:到精灵()


end

function 物品格子:置灰色(fx)
    self.物品灰度=fx
    self:计算显示(self.宽度,self.高度,self.类型,self.物品格子,self.类别,self.附加)
end



function 物品格子:置禁止(总类,名称)
    self.物品禁止 = false
    self:置灰色()
    if self.物品 then
        if 总类 then
            if type(总类)=="table" then
                if 总类[1] and  self.物品.总类 ~= 总类[1] and (总类[2]==nil or self.物品.分类 ~= 总类[2]) and (总类[3]==nil or 总类[3] == false)  then
                self.物品禁止 =true
                self:置灰色(true)
                end
            else
                if 总类 == true  then
                    self.物品禁止 = true
                    self:置灰色(true)
                end
            end
        end
        if 名称 and self.物品.名称 ~=名称 then
            self.物品禁止 = true
            self:置灰色(true)
        end
    end
  end


function 物品格子:置偏移(x, y)
  self.py = {x = x, y = y}
  self:计算显示(self.宽度,self.高度,self.类型,self.物品格子,self.类别,self.附加)
end

function 物品格子:检查透明(x, y)
        if self.格子 then
            return self.格子背景:检查透明(x, y)
        else
            return self.小模型:检查透明(x, y)
        end
end



function 物品格子:更新(dt)
      if self.物品 and self.物品.总类==2 and self.物品.鉴定 and self.物品.赐福 and not self.物品禁止 then
          self.赐福动画:更新(dt)
      end
end



function 物品格子:显示(x, y)
    if self.格子 then
        self.格子背景:显示(x, y)
    end
    if self.物品 and self.物品.装备境界 then
          if self.物品.装备境界.品质=="优秀" then
                self.绿装边框:显示(x+1, y+1)
          elseif self.物品.装备境界.品质=="稀有" then
                  self.蓝装边框:显示(x+1, y+1)
          elseif self.物品.装备境界.品质=="传说" then
                  self.橙装边框:显示(x+1, y+1)
          elseif self.物品.装备境界.品质=="神话" then
                   self.红装边框:显示(x+1, y+1)
          end
    end
    if self.小模型 then
        self.小模型:显示(x, y)
    end
    if self.物品 then 
        if self.物品.总类==2 and self.物品.鉴定 and not self.物品禁止 then
            if self.物品.专用 then
                self.专用背景:显示(x+2, y+2)
            end
            if self.物品.赐福 then
               self.赐福动画:显示(x-2, y-2)
            end
        end
        if self.物品.加锁 then
            self.加锁图标:显示(x+self.宽度-self.加锁图标.宽度-2, y+2)
        end
    end
    if self.确定 then
        self.道具选中:显示(x, y)
    end
    if self.焦点 then
        self.道具焦点:显示(x, y)
    end
    if self.物品禁止 and self.物品格子禁止 then
        self.物品格子禁止:显示(x+5, y+5)
    end
    
end
return 物品格子
