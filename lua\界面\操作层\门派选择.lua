--[[
LastEditTime: 2024-05-06 16:11:42
--]]

local 门派选择 = 窗口层:创建窗口("门派选择",0, 0, 640, 450)
function 门派选择:初始化()
    self:创建纹理精灵(function()
      置窗口背景("门派选择", 0, 12, 640, 450, true):显示(0, 0)
      取输入背景(0, 0, 135, 23):显示(465, 50)
      __res:取资源动画("jszy/xjiem",0x00000063,"图像"):显示(580, 51)
      取输入背景(0, 0, 135, 23):显示(310, 50)
      __res:取资源动画("jszy/xjiem",0x00000050,"图像"):显示(425, 51)
      说明字体:置颜色(0, 0, 0)
      说明字体:取图像("种族"):显示(315,53)
      说明字体:取图像("性别"):显示(470,53)
      说明字体:置颜色(255,255,255):取图像("等级上限为175级，无法转入奇遇门派，更多详见_梦幻门派!"):显示(160,80)
      __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(80, 25):显示(14,50)
      __res:getPNGCC(1, 401, 65, 175, 43, true):拉伸(80, 25):显示(100,50)
      标题字体:置颜色(255,255,255,255):取图像("天命门派"):显示(20,53)
      标题字体:置颜色(255,255,255,255):取图像("奇遇门派"):显示(106,53)
    end
  )
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
    if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
    else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
    end
    



  


  self.门派类型={"大唐官府","方寸山","女儿村","神木林"}
  self.图片={
    大唐官府 = "dtgf.png", 
    化生寺 =  "hss.png", 
    方寸山 =  "fcs.png", 
    神木林 =  "sml.png", 
    天机城 =  "tjc.png", 
    女儿村 =  "nrc.png", 
    狮驼岭 =  "stl.png", 
    魔王寨 =  "mwz.png", 
    阴曹地府 =  "ycdf.png", 
    无底洞 =  "wdd.png", 
    女魃墓 =  "nbm.png", 
    盘丝洞 =  "psd.png", 
    天宫 =  "tg.png", 
    龙宫 =  "lg.png", 
    普陀山 =  "pts.png", 
    五庄观 =  "wzg.png", 
    花果山 =  "hgs.png", 
    凌波城 = "lbc.png", 
  }
  self.门派按钮={
    大唐官府 = {资源=0x00000120,进程=1},
    化生寺 =   {资源=0x00000121,进程=2},
    方寸山 =   {资源=0x00000122,进程=3},
    神木林 =   {资源=0x00000123,进程=4},
    天机城 =   {资源=0x00000124,进程=5},
    女儿村 =   {资源=0x00000125,进程=6},
    狮驼岭 =   {资源=0x00000126,进程=7},
    魔王寨 =   {资源=0x00000127,进程=8},
    阴曹地府 = {资源=0x00000128,进程=9},
    无底洞 =   {资源=0x00000129,进程=10},
    女魃墓 =   {资源=0x00000130,进程=11},
    盘丝洞 =   {资源=0x00000131,进程=12},
    天宫 =     {资源=0x00000132,进程=13},
    龙宫 =     {资源=0x00000133,进程=14},
    五庄观 =   {资源=0x00000134,进程=15},
    凌波城 =   {资源=0x00000135,进程=16},
    花果山 =   {资源=0x00000136,进程=17},
    普陀山 =   {资源=0x00000137,进程=18},

  }




 



end







local 传送按钮 = 门派选择:创建按钮( "传送按钮", 390, 415 )
function 传送按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/mpjm", 0x00000119),1)
end
function 传送按钮:左键按下(x, y)
    if 门派选择.选中门派 and 门派选择.门派按钮[门派选择.选中门派] then
      请求服务(102,{参数=门派选择.门派按钮[门派选择.选中门派].进程})
      门派选择:置可见(false)
    end
end

  function 门派选择:显示(x,y)
      -- if self.图片[self.选中门派] then
      --   self.图片[self.选中门派]:显示(x+146,y+110)
      -- end
      if self.门派图片 then
        self.门派图片:显示(x,y)
      end
  end
function 门派选择:打开()
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  if 角色信息.模型=="飞燕女" or 角色信息.模型=="英女侠" or 角色信息.模型=="巫蛮儿" then
		self.门派类型={"大唐官府","方寸山","女儿村","神木林"}
	elseif 角色信息.模型=="偃无师" or 角色信息.模型=="逍遥生" or 角色信息.模型=="剑侠客" then
		self.门派类型={"大唐官府","方寸山","化生寺","神木林"}
	elseif 角色信息.模型=="狐美人" or 角色信息.模型=="骨精灵" or 角色信息.模型=="鬼潇潇" then
		self.门派类型={"魔王寨","阴曹地府","盘丝洞","无底洞"}
	elseif 角色信息.模型=="杀破狼" or 角色信息.模型=="巨魔王" or 角色信息.模型=="虎头怪" then
		self.门派类型={"魔王寨","阴曹地府","狮驼岭","无底洞"}
	elseif 角色信息.模型=="舞天姬" or 角色信息.模型=="玄彩娥" or 角色信息.模型=="桃夭夭" then
		self.门派类型={"天宫","龙宫","普陀山","凌波城"}
	elseif 角色信息.模型=="羽灵神" or 角色信息.模型=="神天兵" or 角色信息.模型=="龙太子" then
		self.门派类型={"天宫","龙宫","五庄观","凌波城"}
	end
  self.选中门派=self.门派类型[1]
  self.门派网格:置显示()

end

function 门派选择:图片显示()
  self.门派图片=nil
  self.门派图片 =  self:创建纹理精灵(function()
    __res:取资源动画("pic/mp",self.图片[self.选中门派],"图片"):显示(146,110)
  end,1
)

end


local 门派网格 = 门派选择:创建网格("门派网格", 10, 110, 140, 350)

function 门派网格:初始化()
  self:创建格子(136, 63, 2, 2, 4, 1)
end
function 门派网格:置显示()
    for i = 1, #self.子控件 do
        if 门派选择.门派类型[i] then
            if 门派选择.门派类型[i]==门派选择.选中门派 then
                self.子控件[i]:置精灵(__res:取资源动画("jszy/mpjm",门派选择.门派按钮[门派选择.门派类型[i]].资源):取精灵(2))
            else
                self.子控件[i]:置精灵(__res:取资源动画("jszy/mpjm",门派选择.门派按钮[门派选择.门派类型[i]].资源,"图像"):到精灵())
            end
        else
          self.子控件[i]:置精灵()
        end
    end
    门派选择:图片显示()
end

function 门派网格:左键按下(x, y,a)
    if self.子控件[a] and self.子控件[a]._spr and 门派选择.门派类型[a] then
      门派选择.选中门派=门派选择.门派类型[a]
      self:置显示()
    end
end


local 关闭 = 门派选择:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  门派选择:置可见(false)
end



