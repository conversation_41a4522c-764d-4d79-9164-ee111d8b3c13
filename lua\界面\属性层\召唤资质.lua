
local 召唤资质 = 窗口层:创建窗口("召唤资质", 0,0, 250, 470)
local 资质列表={"攻击资质","防御资质","体力资质","法力资质","速度资质","躲闪资质"}
function 召唤资质:初始化()
  self:创建纹理精灵(function()
      置窗口背景("召唤兽资质", 0, 0, 250, 470, true):显示(0, 0)
      __res:取资源动画("dlzy",0x4FC09361,"图像"):复制区域(14,31,184,66):显示(30,33)
       文本字体:置颜色(255,255,255,255)
      for i, v in ipairs(资质列表) do
          取输入背景(0, 0, 145, 23):显示(85,102+(i-1)*25)
          文本字体:取图像(v):显示(20, 105+(i-1)*25)
         
      end
      文本字体:置颜色(__取颜色("橙色"))
      取输入背景(0, 0, 145, 23):显示(85,252)
      文本字体:取图像("寿    命"):显示(20, 255)
      取输入背景(0, 0, 145, 23):显示(85,277)
      文本字体:取图像("成    长"):显示(20, 280)
      取输入背景(0, 0, 145, 23):显示(85,302)
      文本字体:取图像("五    行"):显示(20, 305)
    end
  )
    self.可初始化=true
    self:置坐标(370, 30)
    if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-27, 2)
    else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-18, 2)
    end
end





function 召唤资质:更新(dt)
    if self.开始x and self.开始y then
          local xx,yy=窗口层.召唤属性:取坐标()
          if self.开始x~=xx or self.开始y~=yy then
              if xx - self.宽度>=5 then
                    self:置坐标(xx - self.宽度, yy)
              else
                    self:置坐标(xx + 窗口层.召唤属性.宽度, yy)
              end
              self.开始x,self.开始y=窗口层.召唤属性:取坐标()
          end

    end
    if not 窗口层.召唤属性.是否可见 and self.是否可见 then
        self:置可见(false)
    end
end


function 召唤资质:显示(x, y)
   if self.资质图片  then 
        self.资质图片:显示(x,y)
   end
end



function 召唤资质:打开(数据)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self:刷新(数据)
   
end
function 召唤资质:刷新(数据)
        self.赐福控件:置可见(false)
        self.属性数据=nil
        self.资质图片=nil
        if 数据 and 数据.名称 then
            self.属性数据=table.copy(数据)
        end
        self:显示设置()
       
        self.开始x,self.开始y=窗口层.召唤属性:取坐标()
        if self.开始x - self.宽度>=5 then
              self:置坐标(self.开始x - self.宽度, self.开始y)
        else
              self:置坐标(self.开始x + 窗口层.召唤属性.宽度, self.开始y)
        end
        
end


function 召唤资质:显示设置()
        self.技能控件:置数据()
        self.装备网格:置物品()
        if self.属性数据 then
            self.资质图片 =self:创建纹理精灵(function()
              文本字体:置颜色(0,0,0,255)
              local 资质={}
              for i, v in ipairs(资质列表) do
                      资质[v]=文本字体:取图像(self.属性数据[v])
                      资质[v]:显示(95, 105+(i-1)*25)
              end
              if self.属性数据.种类=="神兽" then
                文本字体:取图像("★永生★"):显示(95, 255)
              else
                文本字体:取图像(self.属性数据.寿命):显示(95, 255)
              end
              文本字体:取图像(self.属性数据.成长):显示(95, 280)
              文本字体:取图像(self.属性数据.五行):显示(95, 305)
              if self.属性数据.饰品 then
                  文本字体:置颜色(__取颜色("红色"))
                  if self.属性数据.饰品攻资 then
                      文本字体:取图像("+"..self.属性数据.饰品攻资):显示(100+资质.攻击资质.宽度, 105)  
                  end
                  if self.属性数据.饰品防资 then
                      文本字体:取图像("+"..self.属性数据.饰品防资):显示(100+资质.防御资质.宽度, 130)  
                  end
                  if self.属性数据.饰品体资 then
                      文本字体:取图像("+"..self.属性数据.饰品体资):显示(100+资质.体力资质.宽度, 155)  
                  end
                  if self.属性数据.饰品法资 then
                      文本字体:取图像("+"..self.属性数据.饰品法资):显示(100+资质.法力资质.宽度, 180)  
                  end         
                  if self.属性数据.饰品速资 then
                      文本字体:取图像("+"..self.属性数据.饰品速资):显示(100+资质.速度资质.宽度, 205)  
                  end  
                  if self.属性数据.饰品躲资 then
                      文本字体:取图像("+"..self.属性数据.饰品躲资):显示(100+资质.躲闪资质.宽度, 230)  
                  end  

              end
            end,1
          )
            self.技能控件:置数据(self.属性数据,true)
            self.装备网格:置物品(self.属性数据.装备)
        end



end

local  装备网格=召唤资质:创建网格("装备网格", 35, 38,170,52)

function 装备网格:初始化()
    self:创建格子(51, 51, 9, 9, 1, 3)
end



function 装备网格:置物品(数据)
    for i = 1, #self.子控件 do
            local lssj = __物品格子:创建()
            lssj:置物品(nil,50,50)
            if 数据 and 数据[i] then
                lssj:置物品(数据[i],50,50,nil,true)
            end
            self.子控件[i]:置精灵(lssj)   
      end
end

function 装备网格:获得鼠标(x,y,a)
        self.子控件[a]._spr.焦点=true
        if self.子控件[a]._spr and self.子控件[a]._spr.物品  then
                __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
        end
end
function 装备网格:失去鼠标(x,y)
        for i = 1, #self.子控件 do
            self.子控件[i]._spr.焦点=nil
        end
end
function 装备网格:左键弹起(x, y, a)
    if self.子控件[a]._spr and self.子控件[a]._spr.物品 and __手机 then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end




local  技能控件=召唤资质:创建技能内丹控件("技能控件", 20, 330)
-- function 技能控件:回调左键弹起(x, y,a)
--     print(x, y,a)

-- end
function 技能控件:赐福弹起(x, y)
        if 召唤资质.属性数据 then
          召唤资质.赐福控件:打开(召唤资质.属性数据.技能,召唤资质.属性数据.超级赐福)
        end
end
function 技能控件:内丹弹起(x, y)
        if 召唤资质.赐福控件.是否可见 then
          召唤资质.赐福控件:置可见(false)
        end
end
function 技能控件:进阶弹起(x, y)
        if 召唤资质.赐福控件.是否可见 then
          召唤资质.赐福控件:置可见(false)
        end
end



local 赐福控件=召唤资质:创建控件("赐福控件", 22, 260,205,70)
function 赐福控件:初始化()
    self:置精灵(require('SDL.精灵')(0, 0, 0,205, 70):置颜色(0, 0, 0, 180))
    self.超级图标=__res:取资源动画("pic","cjjnxtb.png","图片"):到精灵()
    self.技能图标={}
    for i=1,4 do
        local lssj = __技能格子:创建()
        lssj:置数据()
        self.技能图标[i]=lssj
    end

end
function 赐福控件:打开(技能,赐福)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    if not 赐福 or not 赐福[1] then
        self:置可见(false)
        return
    end
    
    local 已有技能={}
    for i=1,4 do
        self.技能图标[i]:置数据()
        if 赐福[i]~=nil then
              self.技能图标[i]:置数据(赐福[i])
              for n=1,#技能 do
                  if  self.技能图标[i].数据 and self.技能图标[i].数据.名称==技能[n] then
                      已有技能[技能[n]]=true
                  end
              end
        end
    end
    for i=1,4 do
        if  self.技能图标[i].数据 and not 已有技能[self.技能图标[i].数据.名称] then
            self.技能图标[i]:置灰色(true)
        end
    end
end

function 赐福控件:显示(x, y)
    for i=1,4 do
        if self.技能图标[i].数据 then
            self.技能图标[i]:显示(x+15+(i-1)*45,y+15)
            self.超级图标:显示(x+17+(i-1)*45,y+17)
        end
    end


end
function 赐福控件:获得鼠标(x, y)
        for i=1,4 do
            if   self.技能图标[i].数据 and self.技能图标[i]:检查透明(x, y) then
                __UI弹出.超级技能详情:打开(x+20,y+20,self.技能图标[i].数据)
            end
        end
end




function 赐福控件:左键弹起(x, y)
        if __手机 then
            for i=1,4 do
                if self.技能图标[i].数据 and self.技能图标[i]:检查透明(x, y) then
                        local 事件 =function (编号)
                            if 编号==1 then
                                self:置可见(false)
                            else
                                __UI弹出.超级技能详情:打开(x+20,y+20,self.技能图标[i].数据)
                            end
                        end
                        __UI弹出.临时按钮:打开({"关闭","查看"},事件,x,y)
                end
            end
           
            
        end
end




function 赐福控件:右键弹起(x, y)
        self:置可见(false)
end

local 关闭 = 召唤资质:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  召唤资质:置可见(false)
end