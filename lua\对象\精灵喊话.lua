
local 冒泡精灵 = class('冒泡精灵')

function 冒泡精灵:初始化(内容,  特效, 偏移, 帧率)
    if not 内容 or 内容 == '' or type(内容)~="string"  then
        return
    end
    self.文本 =require('GGE.文本')(125, 200)
    
    self.文本:置文字表({默认 = 文本字体, 宋体 = 文本字体})
    for i = 1, 25 do
        self.文本:添加精灵(i + 899, _tp.频道[i])
    end
    for i = 1, 190 do
        self.文本:添加精灵(i, _tp.表情[i])
    end
    for i = 121, 190 do
        self.文本:添加精灵(i, _tp.靓号[i])
    end


    local w, h = self.文本:置文本(内容)
    self.w = w + 10

    -- if 特效 then
    --     -- self.h = h + 40
    --     -- self._spr = {
    --     --     __res:getani('wzife/vstyle/chat/%d/0.tcp', id):置中心(60, 0):播放(true):置区域(0, 0, 200, self.h),
    --     --     __res:getani('wzife/vstyle/chat/%d/1.tcp', id):置中心(60, 0):播放(true),
    --     --     __res:getani('wzife/vstyle/chat/%d/2.tcp', id):置中心(60, -self.h):播放(true)
    --     -- }
    --     -- self.文本:置中心(50, -20)
    -- else
        self.h = h + 10
        self._spr = {require('SDL.精灵')(0, 0, 0,self.w, self.h):置颜色(0, 0, 0, 120):置中心(self.w // 2, 0)}
        self.文本:置中心((self.w - 10) // 2, -5)
    --end
    self.y = 偏移
    --self.x = 0
    self.ty = 0
    self.dt = 帧率 or 0
    self.a = 255
    self.up = 0
end

function 冒泡精灵:更新(dt)
    if self.a <= 0 then
        return
    end
    if self._spr[2] then
        for i, v in ipairs(self._spr) do
            v:更新(dt)
        end
    end

    self.文本:更新(dt)

    if self.dt ~= true then
        self.dt = self.dt + dt
        if self.dt > 9 and self.a > 0 then
            self.a = self.a - math.floor(dt * 300)
            if self.a < 0 then
                self.a = 0
            end
            for i, v in ipairs(self._spr) do
                v:置透明(self.a)
            end
            self.文本:置透明(self.a)
        end
    end

    if self.ty > self.y then
        self.up = self.up + dt * 60
        local up = math.floor(self.up)

        if up > 0 then
            self.up = self.up - up
            self.y = self.y + up
            if self.y > self.ty then
                self.y = self.ty
            end
        end
    end
end

function 冒泡精灵:显示(x, y)
    for i, v in ipairs(self._spr) do
        v:显示(x, y - self.y - self.h)
    end
    self.文本:显示(x, y - self.y - self.h)
end
--=====================================================
local 喊话精灵 = class('喊话精灵')

function 喊话精灵:初始化(h)
    self.h = h
    self.shout = {}
end

function 喊话精灵:更新(dt)
    if self.shout[1] then
        for _, v in ipairs(self.shout) do
            v:更新(dt)
        end
        if self.shout[1].a == 0 then
            table.remove(self.shout, 1)
        end
    end
end

function 喊话精灵:显示(x, y)
    if not y and ggetype(x) == 'GGE坐标' then
        x, y = x:unpack()
    end

    for _, v in ipairs(self.shout) do
        v:显示(x, y)
    end
end

function 喊话精灵:添加(内容, 特效)
    if not 内容 or 内容 == '' or type(内容)~="string"  then
          return
      end
    内容 = require("数据/敏感词库")(内容)
    table.insert(self.shout, 冒泡精灵(内容, 特效, self.h))
    local h = self.h
    for i = #self.shout, 1, -1 do
        local x = self.shout[i]
        x.ty = h+(#self.shout-i)*3
        h = h + x.h
        if h > 300 then --删除前面的
            x.dt = 9
        end
    end
end

return 喊话精灵
