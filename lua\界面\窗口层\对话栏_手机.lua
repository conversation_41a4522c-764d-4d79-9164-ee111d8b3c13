local 对话栏 = 窗口层:创建窗口("对话栏", 0, 0, 引擎.宽度, 引擎.高度)
function 对话栏:初始化()
  self:置宽高(引擎.宽度,引擎.高度)
  self:创建纹理精灵(function()
                  __res:getPNGCC(2, 0, 1022, 100, 252):显示(0, 引擎.高度-153)--左圆弧
                  __res:getPNGCC(2, 100, 1022, 892, 252):拉伸(引擎.宽度-198, 252):显示(100, 引擎.高度-153)--中间
                  __res:getPNGCC(2, 992, 1022, 100, 252):显示(引擎.宽度-99, 引擎.高度-153)--右圆弧
              end
    )

  self.选项背景 = __res:getPNGCC(2, 1, 572, 255, 355):到精灵()
  self.选项坐标={x=引擎.宽度-self.选项背景.宽度-2,y=引擎.高度-153 -self.选项背景.高度}
  self.可初始化=true
  self.禁止移动=true
end
function 对话栏:重新初始化()
    for k, v in self:遍历控件() do
        v:初始化()
    end
end 
function 对话栏:左键弹起(x, y)
    self:置可见(false)
end

function 对话栏:显示(x, y)

    if self.是否有选项 then
        self.选项背景:显示(self.选项坐标.x,self.选项坐标.y)
    end
    if self.头像 then
      if self.头像缓存=="物件_打铁炉" then
          self.头像:显示(0, 引擎.高度-100 - self.头像.高度)
      elseif self.头像缓存=="恶魔泡泡" then
          self.头像:显示(75, 引擎.高度-180- self.头像.高度)
      elseif self.头像缓存=="超级红孩儿" then
          self.头像:显示(-80, 引擎.高度-180- self.头像.高度)
      elseif self.头像缓存=="风祖飞廉" then
          self.头像:显示(-50, 引擎.高度-180 - self.头像.高度)
      elseif self.头像缓存=="刑天" then
          self.头像:显示(-90,引擎.高度-150 - self.头像.高度)    
      elseif self.头像缓存=="食铁兽" then
          self.头像:显示(-120, 引擎.高度-150 - self.头像.高度)   
      else
        self.头像:显示(0, 引擎.高度-180 - self.头像.高度)
      end
    end
    if self.名称 then
        self.名称:显示(5, 引擎.高度-180)
    end
end
local  内容文本 = 对话栏:丰富文本("内容文本", 15,-140)
function 内容文本:初始化()
    self:置文字(说明字体)
    self:置宽高(引擎.宽度-50,130)
    self.可初始化=true
end

function 对话栏:打开(头像,名称,内容,选项,附加事件,多重对话)
  self.可选门派=nil
  if not 内容 then
    return
  end
  self.附加事件=附加事件
  self:置可见(true)
  self:置头像名称(头像, 名称)
  self:置对话选项(内容, 选项, 多重对话)
end





function 对话栏:置对话选项(对话, 选项, 多重对话)
	-- 对话栏.对话文本:清空()
	-- 对话栏.对话文本:置文本(对话 or "")
	self.文本内容=""
	self.是否有选项=nil
	self.选项缓存 = {}
	if 选项 and type(选项)=="table" then
      for i = 1, #选项 do
          if 选项[i] ~=nil or 选项[i]~="" then
              table.insert(self.选项缓存,选项[i])
          end
      end
	end
	if #self.选项缓存 ~=0 then
		self.是否有选项=true
	end
	self.内容文本:清空()
	if 对话 and 对话~="" then
      self.内容文本:置文本(对话)
      self.文本内容=对话
	end

  	self.选项列表:置选项()
	--对话栏.选项网格:置选项()
end

local 选项列表 = 对话栏:创建列表('选项列表', 0,0, 227, 280)
function 选项列表:初始化()
          self.选中精灵 = nil
          self.焦点精灵 = nil
          self.行高度 = 60
          self.行间距 = 10
end

function 选项列表:置选项()
         self:清空()
         self:置坐标(对话栏.选项坐标.x+20, 对话栏.选项坐标.y+40)
         for i, v in ipairs(对话栏.选项缓存) do
              local r = self:添加()
              local 对话文本 = r:创建文本("对话文本", 0, 0,197,50)
              local w,h = 对话文本:置文本(v)
              对话文本:置可见(true,true)
              local xx = 15
              local yy = 5
              if w<197 then
                xx = xx+(197-w)//2
              end
              if h < 50 then
                yy =yy+(50-h)//2
              end
              对话文本:置坐标(xx,yy)
              r:置精灵(__res:getPNGCC(2, 0, 960, 227, 60):拉伸(227, 60):到精灵())
         end
end

function 选项列表:左键弹起(x, y, a)
        if 对话栏.选项缓存[a] and 对话栏.选项缓存[a]~="" then
            对话栏:对话事件处理(对话栏.选项缓存[a],角色信息.地图数据.编号,self.名称缓存,nil,a)
        end
end






function 对话栏:置头像名称(头像, 名称)
  self.头像 = nil
  self.头像缓存=nil
  self.名称 = nil
  if 名称 and "" ~= 名称 then
      self.名称 = self:创建纹理精灵(function()
                __res:getPNGCC(1, 448, 0, 20, 28):显示(0, 0)
                __res:getPNGCC(1, 468, 0, 45, 28):平铺(160, 28):显示(20, 0)
                __res:getPNGCC(1, 529, 0, 20, 28):显示(180, 0)
                local 宽度 = 道具字体:取宽度(名称)
                道具字体:置颜色(250, 250, 10)
                道具字体:取图像(名称):显示(100.0 - 宽度 / 2, 4)
            end,1,200,28
      )
  end
  if 头像 and 头像~="" then
	local lssc = 取头像(头像)
	if lssc and lssc[4] then
		  self.头像 =__res:取资源动画(lssc[7], lssc[4],"精灵")
      if self.头像.宽度<150 and self.头像.高度<150 then
          self.头像 =__res:取资源动画(lssc[7], lssc[4],"图像"):拉伸(150,150):到精灵()
      end
      self.头像缓存=头像
	end
end
  self.名称缓存 = 名称
end




local 关闭 = 对话栏:创建按钮("关闭", 引擎.宽度-45, 引擎.高度-151)
function 关闭:初始化()
  self:创建按钮精灵(__res:getPNGCC(2, 8, 935, 44, 25),1)
end
function 关闭:左键弹起(x, y, msg)
  对话栏:置可见(false)
end




function 对话栏:对话事件处理(事件,地图1,名称,名称1,编号)
  --local 名称=对话栏.名称缓存
  if 事件 == "不了不了" or 事件 == "算了算了" or 事件 == "怕了怕了，我绕着走还不行吗？" or 事件 == "大王，我是来膜拜你绝世的容颜的" or 事件 == "太贵了我没钱" or 事件 == "我随便逛逛 不好意思" or 事件 == "我只是来看看" or 事件 == "我只是看看" or 事件 == "只是路过" or 事件 == "我只是路过" or 事件 == "我只是随便看看" or 事件 == "我还要逛逛" or 事件 == "我点错了" or 事件 == "我什么也不想做" or 事件 == "我保留意见" or 事件 == "我什么都不想做" or 事件 == "没什么，我只是看看" or 事件 == "我们后会有期" then
	对话栏:置可见(false)
	return 0
elseif 事件 == "部分解锁" or 事件 == "全面解锁" then
  __UI界面.窗口层.物品解锁:打开()
	对话栏:置可见(false)
	return 0
elseif 事件 == "确定解锁插槽" then
	请求服务(6215,self.附加事件)
	self.附加事件=nil
	对话栏:置可见(false)
	return 0
elseif 事件 == "确定添加点数" then
	请求服务(6216)
	对话栏:置可见(false)
	return 0
elseif 事件 == "炼制灵犀之屑" then
	self:打开("物件_打铁炉","物件_打铁炉","炼制灵犀之屑需要，150级-160级的“人物装备”，即可兑换到对应数量的灵犀之屑。\n#G150级装备 = 10\n#G160级装备 = 15",{"我要炼制","我再考虑考虑"})
	return 0
elseif 事件 == "我要炼制" then
	请求服务(6202)
	对话栏:置可见(false)
	return 0
elseif 事件 == "合成灵犀玉" then
	请求服务(6218)
	对话栏:置可见(false)
	return 0
elseif 事件 == "更换神器五行" then
	请求服务(6201)
	对话栏:置可见(false)
	return 0
elseif 事件 == "帮主" or 事件 == "副帮主" or 事件 == "左护法" or 事件 == "右护法" or 事件 == "长老" or 事件 == "堂主" or 事件 == "帮众" or 事件 == "商人" then
	if 窗口层.帮派查看.选中成员 == nil or 窗口层.帮派查看.帮众数据[窗口层.帮派查看.选中成员] == nil  then
		__UI弹出.提示框:打开("#Y请选择正确的目标!")
		对话栏:置可见(false)
		return
	end
	local id1=窗口层.帮派查看.帮众数据[窗口层.帮派查看.选中成员].id
    请求服务(1502,{事件,角色信息.地图数据.编号,self.名称缓存,id1})


  elseif 事件=="驯养十次" or 事件=="驯养一次" or 事件=="驯养五十次" or 事件=="驯养百次" or 事件=="驯养千次"  then
    local 驯养目标 = 窗口层.坐骑属性.选中
    if 角色信息.坐骑列表[驯养目标] == nil then
      __UI弹出.提示框:打开("#Y请选择正确的目标!")
      对话栏:置可见(false)
      return
    end
    local 次数 = 1
    if 事件=="驯养十次" then
      次数 = 10
    elseif 事件=="驯养五十次" then
        次数 = 50
    elseif 事件=="驯养百次" then
        次数 = 100
    elseif 事件=="驯养千次" then
        次数 = 1000
    end
	请求服务(92,{编号=驯养目标,次数=次数})
elseif 事件=="坐骑洗点" then
	local 驯养 =  窗口层.坐骑属性.选中
	if 角色信息.坐骑列表[驯养] == nil then
		  __UI弹出.提示框:打开("#Y请选择正确的目标!")
  else
     请求服务(94,{编号=驯养})
	end
	
elseif 事件=="坐骑放生" then
		local 驯养 = 窗口层.坐骑属性.选中
		if 角色信息.坐骑列表[驯养] == nil then
        __UI弹出.提示框:打开("#Y请选择正确的目标!")
		else
      请求服务(95,{编号=驯养})
		end
	







elseif 事件=="坐骑喂养" then
	-- local 驯养目标 = tp.窗口.坐骑属性栏.选中
	-- if _tp.坐骑列表[驯养目标] == nil then
	-- 	__UI弹出.提示框:打开("#Y请选择正确的目标!")
	-- 	对话栏:置可见(false)
	-- 	return
	-- end
	-- 请求服务(97,{编号=驯养目标})	-- elseif 事件 == "什么是化境" then
else
	请求服务(1502,{事件,地图1,self.名称缓存,名称1})
end

对话栏:置可见(false)
end


