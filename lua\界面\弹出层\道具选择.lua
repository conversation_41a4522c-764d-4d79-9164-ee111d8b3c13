--[[
LastEditTime: 2024-10-25 19:10:14
--]]

__UI弹出.道具选择 = 界面:创建弹出窗口("道具选择",0,0, 280, 285)
local 道具选择 = __UI弹出.道具选择
function 道具选择:初始化()
      self:置精灵(置窗口背景("选择道具", 0, 0, 280, 285))
      if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
      else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
      end
      

 
end



function 道具选择:打开(数据,事件)
      self:置可见(true)
      self.事件=事件
      self.道具网格:置数据()
      self:道具刷新(数据)
      self:置坐标((引擎.宽度-self.宽度)//2, (引擎.高度-self.高度)//2)
end


function 道具选择:道具刷新(道具)
      if 道具 then
          self.道具列表 =table.copy(道具)
      else
          self.道具列表 =table.copy(_tp.道具列表)
      end
      self.道具网格:置物品(self.道具列表)
end



local 道具网格=道具选择:创建背包网格("道具网格",10,35)

function 道具网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
              __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
          end
end
function 道具网格:左键弹起(x,y,i)
        local 物品=self:选中物品() 
        if  物品 and 物品.物品 then
            if __手机 then
                __UI弹出.道具提示:打开(物品.物品,x+20,y+20,道具网格,"选择",1)
            else
                  self:选择(1)
            end
        end
end

function 道具网格:选择(编号)
        if 编号 and 编号~=0 then
            if 道具选择.事件 then
                道具选择.事件(self:选中())
            end
        end
        道具选择:置可见(false)
end




local 关闭 = 道具选择:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  道具选择:置可见(false)
end








