local 游戏传音类 = __UI界面.界面层:创建控件("游戏传音类",0,215,280,80)
function 游戏传音类:初始化()
    local nsf = require("SDL.图像")(引擎["宽度"], 80)
    self.dongtai = __res:取动画(__res:取地址("shape/smap/DBF85229/", 2888856949)):取动画(1):播放(true)
    if nsf["渲染开始"](nsf) then
        __res:getPNGCC(4,921,330,266,71):显示(4,3)
        nsf["渲染结束"](nsf)
    end
    self:置精灵(nsf["到精灵"](nsf))
end

function 游戏传音类:添加传音(内容)
    -- self.数据组[#self.数据组+1]="#Y"..内容
    -- if not self.是否可见 then
        self.计时=1200
        self.提示文本11["清空"](self.提示文本11)
        self.提示文本11["置文本"](self.提示文本11, 内容.内容)
        -- table.print(self.提示文本11)
        self:置可见(true,true)
    -- end
end
function 游戏传音类:更新(dt)
    if self.是否可见 then
        self.计时=self.计时-1
        if self.计时<=0 then
            self:置可见(false)
        end
    end
end
function 游戏传音类:显示(x,y)
    if self.是否可见 then
        self.dongtai:更新(0.025)
        self.dongtai:显示(x+2,y+0)
    end
end

local 提示文本11 = 游戏传音类["创建我的文本"](游戏传音类, "提示文本11", 10, 8, 252, 56, true)
function 提示文本11:初始化()
end
function 提示文本11:回调左键弹起(cb, msg)
    if cb then
        local lssj = 分割文本(cb, "*")
        -- table.print(lssj)
        if "玩家信息" == lssj[3] then
            __UI弹出["玩家信息弹出"]["打开"](__UI弹出["玩家信息弹出"], {名称=lssj[1],ID = lssj[4]})
        elseif  lssj[3] == "召唤兽" then
            for i,v in ipairs(chaolianjieshuju) do
                if  lssj[3] == v.索引类型 and lssj[1] == v.名称 and lssj[2] == v.认证码 then
                    __UI界面["窗口层"]["召唤兽查看"]:打开(v)
                    break
                end
            end
            -- table.print(lssj)
        elseif  lssj[3] == "道具" then
            for i,v in ipairs(chaolianjieshuju) do
                if lssj[3] == v.索引类型 and lssj[1] == v.名称 and lssj[2] == v.识别码 then
                    local wwewq = __物品格子["创建"]()
                    -- wwewq["置物品"](wwewq, v, nil, "临时背包")
                    wwewq:取数据(v)
                    wwewq["详情打开"](wwewq, 170+254-232, 86, w, h, "选择", a)
                    break
                end
            end
        end
    end

end


