local 战斗灵宝 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "战斗灵宝", 407 + abbr.py.x, 87 + abbr.py.y, 494, 327)
local 取可用道具 = function(名称)
  if "风舞心经" == 名称 or "寒霜盾戟" == 名称 or "战神宝典" == 名称 then
    return 2
  elseif "断穹巨剑" == 名称 or "相思寒针" == 名称 or "锢魂命谱" == 名称 or "青狮獠牙" == 名称 or "冥火炼炉" == 名称 or "缚仙蛛丝" == 名称 or "煞魂冥灯" == 名称 or "九霄龙锥" == 名称 or "化怨清莲" == 名称 or "天雷音鼓" == 名称 or "乾坤金卷" == 名称 or "乾坤木卷" == 名称 or "乾坤水卷" == 名称 or "乾坤火卷" == 名称 or "乾坤土卷" == 名称 then
    return 4
  elseif "静心禅珠" == 名称 or "宁心道符" == 名称 or "真阳令旗" == 名称 or "护体灵盾" == 名称 or "惊兽云尺" == 名称 or "赤炎战笛" == 名称 then
    return 3
  end
  return 0
end
function 战斗灵宝:初始化()
  local nsf = require("SDL.图像")(494, 327)
  if nsf["渲染开始"](nsf) then
    置窗口背景("选择灵宝", 0, 12, 494, 317, true)["显示"](置窗口背景("选择灵宝", 0, 12, 494, 317, true), 0, 0)
    取白色背景(0, 0, 220, 243, true)["显示"](取白色背景(0, 0, 220, 243, true), 18, 67)
    取白色背景(0, 0, 220, 243, true)["显示"](取白色背景(0, 0, 220, 243, true), 255, 67)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 战斗灵宝:打开(data)
  self:置可见(true)
  __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], false)
  self.灵宝网格["置物品"](self.灵宝网格, data["灵宝佩戴"])
  self:重置(data)
end
function 战斗灵宝:重置(data)
  local nsf = require("SDL.图像")(494, 327)
  if nsf["渲染开始"](nsf) then
    字体20["置颜色"](字体20, __取颜色("黄色"))
    字体20["取图像"](字体20, "当前剩余灵元" .. data["灵元"]["数值"] .. "(" .. data["灵元"]["回合"] .. "回合后增加1点)")["显示"](字体20["取图像"](字体20, "当前剩余灵元" .. data["灵元"]["数值"] .. "(" .. data["灵元"]["回合"] .. "回合后增加1点)"), 132, 42)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
local 关闭 = 战斗灵宝["创建我的按钮"](战斗灵宝, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 444, 0)
function 关闭:左键弹起(x, y, msg)
  __UI界面["窗口层"]["战斗灵宝"]["置可见"](__UI界面["窗口层"]["战斗灵宝"], false)
  __UI界面["界面层"]["战斗界面"]["重置"](__UI界面["界面层"]["战斗界面"])
  __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], true)
end
local 灵宝网格 = 战斗灵宝["创建网格"](战斗灵宝, "灵宝网格", 99, 85, 270, 55)
function 灵宝网格:初始化()
  self:创建格子(55, 55, 0, 164, 1, 2)
end
function 灵宝网格:置物品(data)
  战斗灵宝["灵宝介绍1"]["清空"](战斗灵宝["灵宝介绍1"])
  战斗灵宝["灵宝介绍2"]["清空"](战斗灵宝["灵宝介绍2"])
  for i = 1, #self.子控件 do
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data[i], "白格子", "战斗灵宝")
    self.子控件[i]["置精灵"](self.子控件[i], lssj)
    if self.子控件[i]._spr["物品"] then
      战斗灵宝["灵宝介绍" .. i]["置文本"](战斗灵宝["灵宝介绍" .. i], self.子控件[i]._spr["物品"]["介绍"])
    end
  end
end
local 灵宝介绍1 = 战斗灵宝["创建文本"](战斗灵宝, "灵宝介绍1", 30, 147, 200, 120)
local 灵宝介绍2 = 战斗灵宝["创建文本"](战斗灵宝, "灵宝介绍2", 267, 147, 200, 120)
for i, v in ipairs({
  {
    name = "使用灵宝1",
    x = 70,
    y = 250,
    tcp = __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41),
    font = "使用"
  },
  {
    name = "使用灵宝2",
    x = 304,
    y = 250,
    tcp = __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41),
    font = "使用"
  }
}) do
  local 临时函数 = 战斗灵宝["创建我的按钮"](战斗灵宝, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "使用灵宝1" then
      if 战斗灵宝["灵宝网格"]["子控件"][1]._spr["物品"] then
        __UI界面["界面层"]["战斗界面"]["设置灵宝参数"](__UI界面["界面层"]["战斗界面"], 1, 取可用道具(战斗灵宝["灵宝网格"]["子控件"][1]._spr["物品"]["名称"]))
      end
    elseif v.name == "使用灵宝2" and 战斗灵宝["灵宝网格"]["子控件"][2]._spr["物品"] then
      __UI界面["界面层"]["战斗界面"]["设置灵宝参数"](__UI界面["界面层"]["战斗界面"], 2, 取可用道具(战斗灵宝["灵宝网格"]["子控件"][2]._spr["物品"]["名称"]))
    end
  end
end
