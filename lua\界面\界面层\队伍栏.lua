--[[
LastEditTime: 2024-10-21 14:00:04
--]]

local 队伍栏 = 界面层:创建控件("队伍栏",0,0,250,57)
function 队伍栏:初始化()
    if __手机 then
        self:置坐标(引擎.宽度-490,0)
    else
        self:置坐标(引擎.宽度-485,0)
    end
    self.可初始化=true
end
local 队员网格 = 队伍栏:创建网格("队员网格",0,0,250,50)
function 队员网格:初始化()
    self:创建格子(50,50,0,0,1,5)
end
function 队员网格:左键弹起(x, y, a)
    if self.子控件[a] and self.子控件[a]._spr and self.子控件[a].数据 then
        if __手机 then
            local 事件 =function (编号)
                    if 编号==1 then
                        if _tp.多角色[self.子控件[a].数据.id] and self.子控件[a].数据.id~=角色信息.数字id and self.子控件[a].数据.id~=_tp.队伍数据[1].id then
                            请求服务(63,{参数=self.子控件[a].数据.id,文本="切换角色"})
                        end
                    elseif 编号==2 then
                            local 传入数据="#Y名称:#W"..self.子控件[a].数据.名称.."\n#YID :#W"..self.子控件[a].数据.id.."\n#Y等级:#W"..self.子控件[a].数据.等级.."级".."\n#Y门派:#W无门派"
                            if  self.子控件[a].数据.门派 and self.子控件[a].数据.门派~="" then
                                传入数据="#Y名称:#W"..self.子控件[a].数据.名称.."\n#YID :#W"..self.子控件[a].数据.id.."\n#Y等级:#W"..self.子控件[a].数据.等级.."级".."\n#Y门派:#W"..self.子控件[a].数据.门派
                            end
                            __UI弹出.自定义:打开(x,y+50,传入数据)
                    elseif 编号==3 then
                            if self.子控件[a] and self.子控件[a]._spr and self.子控件[a].数据 and self.子控件[a].数据.id~=角色信息.数字id then
                                __UI弹出.玩家信息:打开({模型 = self.子控件[a].数据.模型,[1] = self.子控件[a].数据.名称,[2]= self.子控件[a].数据.id,[3]=self.子控件[a].数据.门派 or "无门派"})
                            end
                    end
                end
                __UI弹出.临时按钮:打开({"切换","查看","信息"},事件,x,y+50)
        else
                if  self.选中 and self.选中==a and _tp.多角色[self.子控件[a].数据.id] and self.子控件[a].数据.id~=角色信息.数字id and self.子控件[a].数据.id~=_tp.队伍数据[1].id then
                    请求服务(63,{参数=self.子控件[a].数据.id,文本="切换角色"})
                else
                    self.选中=a
                end
        end
    end
      if __主显 and __主显.主角 then
          __主显.主角.按下=false
          __主显.主角.点击移动=nil
      end
end


function 队员网格:获得鼠标(x, y, a)
    self.选中=nil
    if self.子控件[a] and self.子控件[a]._spr and self.子控件[a].数据 then
         local 传入数据="#Y名称:#W"..self.子控件[a].数据.名称.."\n#YID :#W"..self.子控件[a].数据.id.."\n#Y等级:#W"..self.子控件[a].数据.等级.."级".."\n#Y门派:#W无门派"
         if  self.子控件[a].数据.门派 and self.子控件[a].数据.门派~="" then
            传入数据="#Y名称:#W"..self.子控件[a].数据.名称.."\n#YID :#W"..self.子控件[a].数据.id.."\n#Y等级:#W"..self.子控件[a].数据.等级.."级".."\n#Y门派:#W"..self.子控件[a].数据.门派
         end
         local xx,yy=引擎:取鼠标坐标()
        __UI弹出.自定义:打开(xx+20,yy+20,传入数据)
    end
end
function 队员网格:失去鼠标(x, y)
    self.选中=nil
end


function 队员网格:右键弹起(x, y, a)
        if self.子控件[a] and self.子控件[a]._spr and self.子控件[a].数据 and self.子控件[a].数据.id~=角色信息.数字id then
            __UI弹出.玩家信息:打开({模型 = self.子控件[a].数据.模型,[1] = self.子控件[a].数据.名称,[2]= self.子控件[a].数据.id,[3]=self.子控件[a].数据.门派 or "无门派"})
        end

end



function 队员网格:置头像(数据)
    for i = 1,#self.子控件 do
        local bh = 6-i
        if 数据 and 数据[i] then
            local lssj = __头像格子:创建()
            lssj:置头像(数据[i].模型,50,50,nil,true)
            self.子控件[bh]:置精灵(lssj)
            self.子控件[bh].数据 = 数据[i]
        else
            self.子控件[bh]:置精灵(nil)
        end
    end
end


-- 模型=self.数据.模型,
-- 染色=self.染色,
-- 等级=self.数据.等级,
-- 名称=self.数据.名称,
-- 门派=self.数据.门派,
-- 染色组=self.数据.染色组,
-- 染色方案=self.数据.染色方案,
-- 当前称谓 = self.数据.当前称谓,
-- id=self.数据.数字id,
-- 变身数据=self.数据.变身数据,
-- 变异=self.数据.变异,
-- 坐骑=self.数据.坐骑,
-- 装备={},
-- 锦衣={},