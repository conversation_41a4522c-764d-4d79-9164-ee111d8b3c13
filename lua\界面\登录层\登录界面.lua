

local  登录游戏=登录层:创建控件("登录游戏",0,0,引擎.宽度,引擎.高度)
function 登录游戏:初始化()
  self:创建纹理精灵(function()
        __res:取资源动画("ui",0x01000260,"图像"):显示(引擎.宽度2-180,引擎.高度2-120)
        __res:取资源动画("ui",0x01000158,"图像"):显示(引擎.宽度2-120,引擎.高度2-120-120)
      end
    )
end

local 账号输入 = 登录游戏:创建文本输入("账号输入", 引擎.宽度2-170//2, 引擎.高度2-32, 180, 16)
function 账号输入:初始化()
    self.取光标精灵(self)
    self:置模式(1)
    self:置限制字数(16)
    self:置模式(self.英文模式 | self.数字模式)
    self:置颜色(255, 255, 255, 255)
    self:置文本(__res.配置.账号)
end
local 密码输入 = 登录游戏:创建文本输入( "密码输入", 引擎.宽度2-170//2, 引擎.高度2+15,180, 16)
function 密码输入:初始化()
    self.取光标精灵(self)
    self:置限制字数(16)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(255, 255, 255, 255)
    self:置文本(__res.配置.密码)
end
function 登录游戏:键盘弹起(键码, 功能)
    if 键码 == 9 then
        if self.账号输入._输入焦点 then
            self.密码输入:置焦点(true)
        else
            self.账号输入:置焦点(true)
        end
    elseif 键码==13 then
            self.登录:左键弹起()
    end
end



local 注册账号 = 登录游戏:创建按钮("注册账号", 引擎.宽度-150,  引擎.高度-160)
function 注册账号:初始化()
  self:创建按钮精灵(__res:取资源动画("ui","00010104.was"),1)
end
function 注册账号:左键弹起(x, y, msg)
    登录游戏:置可见(false)
    登录层.注册账号:置可见(true, true)
end


local 登录 = 登录游戏:创建按钮("登录", 引擎.宽度-150,  引擎.高度-110)
function 登录:初始化()
  self:创建按钮精灵(__res:取资源动画("ui","01000254.was"), 1)
end
function 登录:左键弹起(x, y, msg)
    if 账号输入:取文本() ~= "" and 密码输入:取文本() ~= "" then
        __res.配置.账号 = 账号输入:取文本()
        __res.配置.密码 = 密码输入:取文本()
        if #__res.配置.账号 >= 6 and #__res.配置.密码 >= 6 then
            __res:写出文件( "config.txt", zdtostring(__res.配置))
            请求服务(1,_版本号..fgc..__res.配置.账号..fgc..__res.配置.密码)
        else
            __UI弹出.提示框:打开("#R账号密码位数不能少于6位")
        end
    else
        __UI弹出.提示框:打开("#R账号密码位数不能少于6位")
    end
end
local 退出游戏 = 登录游戏:创建按钮("退出游戏", 引擎.宽度-150,  引擎.高度-60)
function 退出游戏:初始化()
  self:创建按钮精灵(__res:取资源动画("ui","01000247.was"), 1)
end
function 退出游戏:左键弹起(x, y, msg)
    引擎:关闭()
end    --现在这个
