
local 技能格子 = class("技能格子")
function 技能格子:初始化()
    self.数据=nil
    self.模型=nil
    self.焦点=nil
    self.焦点模型=nil
    self.显示字体=nil
end
function 技能格子:置数据(数据,格子,小模型,类型,奇经八脉)
  self.数据=nil
  self.模型=nil
  self.焦点=nil
  self.焦点模型=nil
  self.显示字体=nil
  self.格子=nil
  self.宽度=40
  self.高度=40
  if 小模型 then
    self.宽度=32
    self.高度=32
  end
  if 数据 then
        local 名称=""
        local 等级=0
        if type(数据)=="table" and 数据.名称 and 数据.名称~="" and 数据.名称~= 0  then
            名称=数据.名称
            if 数据.等级 then
                等级=数据.等级
            end
        elseif type(数据)=="string" then
                名称=数据
        end
        if 名称~="" then
            self.数据={}
            local lxxs = 取技能(名称)
            if 奇经八脉 then
                lxxs = 取奇经八脉技能(名称)
            end
            self.数据.名称=名称
            self.数据.等级=等级
            self.数据.模型= lxxs[7]
            self.数据.大动画=lxxs[7]
            self.数据.资源=lxxs[6]
            self.数据.介绍=lxxs[1]
            if lxxs[4] then
                self.数据.消耗="#G消耗："..lxxs[4]
            end
            if lxxs[5] then
                self.数据.条件="#G条件："..lxxs[5]
            end
            if lxxs[12] then
                self.数据.冷却 ="#G冷却："..lxxs[12]
            end
            self.模型 = __res:取资源动画(self.数据.资源,self.数据.模型 ,"精灵")
            self.焦点模型= __res:取资源动画(self.数据.资源,self.数据.模型,"精灵"):置高亮(true)
            if 小模型 then
              self.模型 = __res:取资源动画(self.数据.资源,self.数据.模型 ,"图像"):拉伸(26,26):到精灵()
              self.焦点模型= __res:取资源动画(self.数据.资源,self.数据.模型 ,"图像"):拉伸(26,26):到精灵():置高亮(true)
            end
            
            self.宽度 =self.模型.宽度 
            self.高度 =self.模型.高度
            if 类型 then
               self:类型渲染(类型)
            end
        end
   end
   if 格子 then
        self.格子=__res:取资源动画("jszy/fwtb",0x00000030,"图像"):拉伸(self.宽度,self.高度):到精灵()
   end
   self.技能选中 = __res:取资源动画("jszy/dd",0x00000070,"图像"):拉伸(self.宽度,self.高度):到精灵()
end




function 技能格子:类型渲染(类型)
      local nsf = require('SDL.图像')(self.宽度,self.高度)
      if nsf:渲染开始() then
            格子字体:置颜色(255,255,255,255)
            if type(类型)=="table"then
                if 类型.名称 then
                    格子字体:取图像(self.数据.名称):显示((self.宽度-格子字体:取宽度(self.数据.名称))//2,self.高度-16)
                    if 类型.等级 then
                      格子字体:取投影图像(self.数据.等级,100,100,100,100):显示(2,2)
                    end
                end
                if 类型.冷却 then
                    self.模型 = __res:取资源动画(self.数据.资源,self.数据.模型,"图像"):到灰度():到精灵()
                    self.焦点模型= __res:取资源动画(self.数据.资源,self.数据.模型,"图像"):到灰度():到精灵():置高亮(true)
                    冷却字体:置颜色(255,255,255,255):取投影图像(类型.冷却,100,100,100,100):显示((self.宽度-冷却字体:取宽度(类型.冷却))//2,(self.高度-冷却字体:取高度(类型.冷却))//2)
                end
            elseif ggetype(类型)=="SDL图像"then
                    类型:显示((self.宽度-类型.宽度)//2,(self.高度-类型.高度)//2)
            elseif ggetype(类型)=="tcp"then
                    类型:取图像(1):显示((self.宽度-类型.宽度)//2,(self.高度-类型.高度)//2)
            elseif type(类型)=="string"then
                    格子字体:取图像(类型):显示((self.宽度-格子字体:取宽度(类型))//2,self.高度-16)
            elseif type(类型)=="number" then
                    self.模型 = __res:取资源动画(self.数据.资源,self.数据.模型,"图像"):到灰度():到精灵()
                    self.焦点模型= __res:取资源动画(self.数据.资源,self.数据.模型,"图像"):到灰度():到精灵():置高亮(true)
                    冷却字体:置颜色(255,255,255,255):取投影图像(类型,100,100,100,100):显示((self.宽度-冷却字体:取宽度(类型))//2,(self.高度-冷却字体:取高度(类型))//2)

            end
        nsf:渲染结束()
      end
      self.显示字体 = nsf:到精灵()
end




function 技能格子:置灰色(fx)
         if self.数据 and self.数据.名称 then
            if fx then
                self.模型 = __res:取资源动画(self.数据.资源,self.数据.模型,"图像"):到灰度():到精灵()
                self.焦点模型= __res:取资源动画(self.数据.资源,self.数据.模型,"图像"):到灰度():到精灵():置高亮(true)
            else
                self.模型 = __res:取资源动画(self.数据.资源,self.数据.模型,"精灵")
                self.焦点模型= __res:取资源动画(self.数据.资源,self.数据.模型,"精灵"):置高亮(true)
            end
        end  
end

function 技能格子:检查透明(x, y)
    if self.格子 then
        return self.格子:检查透明(x, y)
    else
        return self.模型:检查透明(x, y)
    end
end

function 技能格子:显示(x, y)
    if self.格子 then
        self.格子:显示(x, y)
    end
    if self.模型 then
        if self.焦点 then
            self.焦点模型:显示(x, y)
        else
            self.模型:显示(x, y)
        end
    end
    if self.显示字体 then
        self.显示字体:显示(x, y)
    end
    if self.确定 then
        self.技能选中:显示(x, y)
    end
end
return 技能格子
