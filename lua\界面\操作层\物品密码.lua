--[[
LastEditTime: 2024-10-22 17:34:49
--]]


local 物品密码 = 窗口层:创建窗口("物品密码", 0, 0, 260, 350)
function 物品密码:初始化()
  self:创建纹理精灵(function()
    置窗口背景("密码设置", 0, 0, 260, 350, true):显示(0, 0)
    取输入背景(0, 0, 140, 22):显示(95, 129)
    取输入背景(0, 0, 140, 22):显示(95, 161)
    文本字体:置颜色( 255, 255, 255,255)
    文本字体:取图像("请用右边的软键盘入您物品锁密\n码的前3位,剩余的位数请用键盘\n输入。\n(建议您设置的物品锁密码尽量由\n数字和英文字母搭配而成)"):显示(21, 45)
    文本字体:取图像("设置密码"):显示( 26, 133)
    文本字体:取图像("确认密码"):显示( 26, 165)
    文本字体:取图像("请输入加锁密码。 "):显示( 26, 198)
    文本字体:取图像("长"):显示(217, 213)
    文本字体:取图像("度不得少于4个或超过12个字符。\n加锁的召唤兽不允许使用系统赋予\n的名字。例如狼、 蛤蟆精,地狱战\n神之类。设置密码一次需要消耗20\n点体力。"):显示(26,227)

    文本字体:置颜色(__取颜色("黄色")):取图像("密码由a-z的小"):显示(140,198)
    文本字体:置颜色(__取颜色("黄色")):取图像("写英文字母、 0-9的数字组成, "):显示(26,213)


end
)
 
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
  else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
  end

  
end


function 物品密码:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.密码输入:置文本("")
    self.确认密码:置文本("")

end



local 密码输入 = 物品密码:创建文本输入( "密码输入", 100, 133,130, 16)
function 密码输入:初始化()
    self:取光标精灵()
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(0,0,0,255)
end


local 确认密码 = 物品密码:创建文本输入( "确认密码", 100,163,130, 16)
function 确认密码:初始化()
    self:取光标精灵()
    self:置限制字数(12)
    self:置模式(self.英文模式 | self.数字模式 | self.密码模式)
    self:置颜色(0,0,0,255)
end


function 物品密码:键盘弹起(键码, 功能)
  if 键码 == 9 then
      if self.密码输入._输入焦点 then
          self.确认密码:置焦点(true)
      else
          self.密码输入:置焦点(true)
      end
  elseif 键码==13 then
          self.确认:左键弹起()
  end
end


local 确认=物品密码:创建红色按钮("确认","确认", 50, 310,50, 22)  
function 确认:左键弹起(x, y)
          if not 物品密码.密码输入:取文本() or 物品密码.密码输入:取文本()=="" then
                __UI弹出.提示框:打开("#Y你还没有填写密码呢！")
          elseif string.len(物品密码.密码输入:取文本())>12 then
                 __UI弹出.提示框:打开("#Y密码过长，请重新填写")
          elseif not 物品密码.确认密码:取文本() or 物品密码.确认密码:取文本()=="" then
                 __UI弹出.提示框:打开("#Y你还没有填写密码呢！")
          elseif 物品密码.密码输入:取文本()~=物品密码.确认密码:取文本() then
                  __UI弹出.提示框:打开("#Y俩次密码不一样?")
          else
                请求服务(3752,{密码=物品密码.密码输入:取文本()})
                物品密码:置可见(false)
          end
end

local 取消=物品密码:创建红色按钮("取消","取消", 150, 310,50, 22)  
function 取消:左键弹起(x, y)
   物品密码:置可见(false)
end





local 关闭 = 物品密码:创建关闭按钮("关闭")

function 关闭:左键弹起(x, y)
  物品密码:置可见(false)
end

