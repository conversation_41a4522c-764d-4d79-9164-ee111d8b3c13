
local 梦幻指引 = 窗口层:创建窗口("梦幻指引", 0, 0, 700, 470)
function 梦幻指引:初始化()
      self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
      self.可初始化=true
      if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
      else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
      end
end


local 进度条 = 梦幻指引:创建我的进度(__res:取资源动画("pic", "jindu.png","图片"):拉伸(330,5),"进度条", 75, 398, 330, 5)



local 单选设置={"日常任务","限时活动","全天活动","基础攻略"}
for i, v in ipairs(单选设置) do
  local 临时函数 = 梦幻指引:创建红色单选按钮(v, v, 30+(i-1)*165,35,140,30,说明字体)
  function 临时函数:左键弹起(x, y)
     请求服务(40,{获取="获取其他数据",文本 =v})
  end
end


local 仙玉按钮 = 梦幻指引:创建按钮("仙玉按钮", 10, 378)
function 仙玉按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb", 0x00000135),1)
end
function 仙玉按钮:左键弹起(x, y)
    if 窗口层.仙玉商城.是否可见 then
        窗口层.仙玉商城:置可见(false)
    else
      请求服务(29)
    end
    梦幻指引:置可见(false)

end


local 签到按钮 = 梦幻指引:创建按钮("签到按钮", 530, 388)
function 签到按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/fwtb", 0x00000136),1)
end
function 签到按钮:左键弹起(x, y)
  请求服务(46)
  梦幻指引:置可见(false)
end




function 梦幻指引:打开(数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
        return
  end
  self.活跃度= 0
  self.显示数据={}
  self.图像=nil

  



  self:刷新(数据)

end



function 梦幻指引:刷新(数据)
    self.状态= "日常任务"
      if 数据.标题 and self.状态~= 数据.标题 then
         self.状态 = 数据.标题
      end
      self[self.状态]:置选中(true)
      self.活跃度 = 数据.活跃度
      self.显示数据={}
      for i=1,#数据.文本 do
        self.显示数据[i]=数据.文本[i]
        if 数据.文本[i].显示类型~=nil and 数据.文本[i].显示类型=="头像" then
          local 临时资源 = 取头像(数据.文本[i].显示)
          if 临时资源~=nil then
                self.显示数据[i].小动画=__res:取资源动画(临时资源[7], 临时资源[2],"图像")
            else
                self.显示数据[i].小动画=__res:取资源动画("jszy/wptb", 0x5453A471,"图像")
            end
            self.显示数据[i].偏移x = 3
            self.显示数据[i].偏移y = 3
        else
            local 临时资源=取物品(数据.文本[i].显示)
            if 临时资源~=nil then
                self.显示数据[i].小动画=__res:取资源动画(临时资源[11], 临时资源[12],"图像")
            else
                self.显示数据[i].小动画=__res:取资源动画("jszy/wptb", 0x5453A471,"图像")
            end
            self.显示数据[i].偏移x = 0
            self.显示数据[i].偏移y = 0
        end
      end


      self.进度条:置位置(self.活跃度/600*100)
      for i=1,6 do
        if  math.floor(self.活跃度/100)>= i then
           self["葫芦按钮"..i]:置禁止(false)
        else
           self["葫芦按钮"..i]:置禁止(true)
        end
      end

      self.头像网格:置网格()
      self:创建纹理精灵(function()
              置窗口背景("梦幻指引", 0, 0, 700, 470, true):显示(0, 0)
              取白色背景(0, 0, 680, 300, true):显示(10, 73)
              __res:取资源动画("pic", "hyjj.png","图片"):显示(85,388)
              文本字体:置颜色(255,255,255,255)
              for i=1, 6 do
                文本字体:取图像(i*100):显示(110+(i-1)*57, 418)
              end
              文本字体:取图像("当前活跃:"..self.活跃度):显示(530+(138-文本字体:取宽度("当前活跃:"..self.活跃度))//2, 433)
      end
      )
end




local 头像网格 = 梦幻指引:创建控件("头像网格", 20, 88, 660, 270)
function 头像网格:置网格()
    self.网格:置网格()
end

local 网格 = 头像网格:创建网格("网格", 0, 0, 660, 270)
function 网格:置网格()
      self:创建格子(325, 80, 10, 10, math.ceil(#梦幻指引.显示数据 / 2), 2)
      for i = 1, #self.子控件 do
          if 梦幻指引.显示数据[i] and 梦幻指引.显示数据[i].小动画 then
              self.子控件[i]:创建纹理精灵(function()
                    取灰色背景(0, 0, 325, 80, true):显示(0,0)
                    __res:取资源动画("pic", "fgbj.png","图片"):显示(10,12)
                    梦幻指引.显示数据[i].小动画:显示(10+梦幻指引.显示数据[i].偏移x,12+梦幻指引.显示数据[i].偏移y)
                    文本字体:置颜色(__取颜色("青色"))
                    文本字体:取图像(梦幻指引.显示数据[i].名称):显示(70, 15)

                    if 梦幻指引.显示数据[i].难度 and 梦幻指引.显示数据[i].难度~="无"  then
                      文本字体:置颜色(__取颜色("红色")):取图像("难度:"..梦幻指引.显示数据[i].难度):显示(70, 45)
                    end
              end)   
              local 按钮 = self.子控件[i]:创建红色按钮("查看攻略", "按钮" .. i,240,40,74,22) 
              function  按钮:左键弹起(x, y)
                  请求服务(40,{获取="查看攻略",文本 =梦幻指引.状态,文件=梦幻指引.显示数据[i].名称})
              end
               按钮:置可见(true)
          else
                self.子控件[i]:置精灵()
          end

      end
end



for i=1, 6 do
  local 临时函数 = 梦幻指引:创建按钮("葫芦按钮"..i, 110+(i-1)*57, 388)
  function 临时函数:初始化()
    self:创建按钮精灵(__res:getPNGCC(4, 1039, 74, 20, 27, true),1)
  end
  function 临时函数:左键弹起(x, y)
    请求服务(40,{获取="活跃度领取",文本=梦幻指引.状态,编号=i})
  end
end



local 关闭 = 梦幻指引:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  梦幻指引:置可见(false)
end