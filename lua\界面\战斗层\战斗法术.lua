local 战斗法术 = 战斗层:创建窗口("战斗法术")
function 战斗法术:初始化()
end

function 战斗法术:打开(技能组,法术类型,操作对象,操作单位)
          self:置可见(not self.是否可见)
          if not self.是否可见 then
            return
          end
          self.操作对象 = 操作对象
          local 标题="法术"
          if 操作单位 then
              标题=操作单位.名称
          end
          self.循环次数 = 15
          self:置宽高(200,280)
          self.技能网格:置宽高(174,205)
          self:置精灵(置窗口背景(标题,0,0,200,265))
          self.花纹=__res:取资源动画("jszy/fwtb",0xabcd0204,"精灵"):置区域(0,0,16,203)
          if #技能组>15 then
              self.循环次数 = 30
              self:置宽高(200,490)
              self.技能网格:置宽高(174,410)
              self:置精灵(置窗口背景(标题,0,0,200,475))
              self.花纹=__res:取资源动画("jszy/fwtb",0xabcd0204,"精灵"):置区域(0,0,16,406)
          end
          local 技能=table.copy(技能组)
          for i, v in ipairs(技能) do
              if 操作单位.技能冷却 and 操作单位.技能冷却[v.名称] then
                  v.是否冷却 = 操作单位.技能冷却[v.名称]
              end
          end
          self.技能网格:置技能(技能)
          local 默认 =  界面层.战斗界面:获取默认法术(self.操作对象)
          self.默认法术=文本字体:置颜色(__取颜色("黄色")):取精灵(默认)
          if 操作单位.类型=="角色" and 法术类型=="特技" and 操作单位.愤怒 then
              self.默认法术=文本字体:置颜色(__取颜色("黄色")):取精灵("当前愤怒:"..操作单位.愤怒)
          end
          self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
          if __手机 then
              self.关闭:置大小(25,25)
              self.关闭:置坐标(self.宽度-27, 2)
          else
              self.关闭:置大小(16,16)
              self.关闭:置坐标(self.宽度-18, 2)
          end

end
function 战斗法术:显示(x,y)
  for i = 1, 2 do
      self.花纹:显示(x+57+(i-1)*65,y+33)
  end
  if self.循环次数==15 then
      self.默认法术:显示(x+15,y+242)
  else
      self.默认法术:显示(x+15,y+450)
  end
end

local 技能网格=战斗法术:创建网格("技能网格",15,33,174,204)
function 技能网格:初始化()
      self:创建格子(40,40,1,25,10,3,true)
end

function 技能网格:置技能(数据)
      for i, v in ipairs(self.子控件) do
          local lssj =  __技能格子:创建()
          if 数据[i] and 数据[i].是否冷却 then
              lssj:置数据(数据[i],true,nil,数据[i].是否冷却)
              v.是否冷却=true
          else
              lssj:置数据(数据[i],true)
              v.是否冷却=nil
          end
          v:置精灵(lssj)
      end
end
function 技能网格:获得鼠标(x,y,a)
      if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点=nil
      end
     if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
            self.焦点=a
            self.子控件[a]._spr.焦点=true
            __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
      end
end

function 技能网格:失去鼠标(x,y)
      for i, v in ipairs(self.子控件) do
          if v._spr.焦点 then
              v._spr.焦点=nil
          end
      end
      self.焦点=nil
end


function 技能网格:左键按下(x,y,a)
    界面层.按下=false
end
function 技能网格:左键弹起(x,y,a)

  if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
      if self.子控件[a].是否冷却 then
            __UI弹出.提示框:打开("#Y该技能还未冷却")
      else
            if __手机 then
                  __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20,技能网格,"战斗法术",a)
            else
                  self:使用(a)
             end
      end
  end
  界面层.按下=false
end

function 技能网格:右键按下(x,y,a)

  界面层.按下1=false
end

function 技能网格:右键弹起(x,y,a)
      if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
              if self.子控件[a].是否冷却 then
                    __UI弹出.提示框:打开("#Y该技能还未冷却")
              else
                    self:设置默认(a)
              end
      end
      界面层.按下1=false
end

function 技能网格:使用(编号)
  if 编号 and 编号~=0 then
    界面层.战斗界面:设置法术参数(self.子控件[编号]._spr.数据)
    战斗法术:置可见(false)
  end
end
function 技能网格:设置默认(编号)
    if 编号 and 编号~=0 then
      界面层.战斗界面:设置默认法术(self.子控件[编号]._spr.数据.名称,战斗法术.操作对象)
      界面层.战斗界面:设置法术参数(self.子控件[编号]._spr.数据)
      战斗法术:置可见(false)
    end
end


local 关闭 = 战斗法术:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    战斗法术:置可见(false)
end
