local 给予NPC = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "给予NPC", 160 + abbr.py.x, 115 + abbr.py.y, 654, 408)
local lsb = {
  "现有现金",
  "给予银两"
}
function 给予NPC:初始化()
  local nsf = require("SDL.图像")(654, 408)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("向NPC给予界面", 0, 12, 642, 395, true)["显示"](xiao置窗口背景("向NPC给予界面", 0, 12, 642, 395, true), 0, 0)
    取灰色背景(0, 0, 340, 280, true)["显示"](取灰色背景(0, 0, 340, 280, true), 278, 106)
    字体18["置颜色"](字体18, 255, 255, 255)
    local lssj = 取输入背景(0, 0, 120, 23)
    for i = 1, #lsb do
      lssj["显示"](lssj, 109, 262 + (i - 1) * 37)
      字体18["取图像"](字体18, lsb[i])["显示"](字体18["取图像"](字体18, lsb[i]), 10, 262 + (i - 1) * 37)
    end
    lssj = 取输入背景(0, 0, 55, 23)
    for i = 1, 3 do
      lssj["显示"](lssj, 33 + (i - 1) * 80, 215)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 给予NPC:打开(数据, mc, lx)
  self:置可见(true)
  self:重置(数据, mc, lx)
end
function 给予NPC:重置(数据, mc, lx)
  local nsf = require("SDL.图像")(654, 408)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, 255, 255, 255)
    字体18["取图像"](字体18, "NPC:" .. mc)["显示"](字体18["取图像"](字体18, "NPC:" .. mc), 12, 55)
    字体18["取图像"](字体18, "想要给我什么？")["显示"](字体18["取图像"](字体18, "想要给我什么？"), 12, 85)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.道具网格["置物品"](self.道具网格, __主控["道具列表"])
  self.给予网格["置物品"](self.给予网格, {})
  self.道具按钮["置选中"](self.道具按钮, true)
  self.道具数量1["置数值"](self.道具数量1, 1)
  self.道具数量2["置数值"](self.道具数量2, 1)
  self.道具数量3["置数值"](self.道具数量3, 1)
  self.银两输入["置数值"](self.银两输入, 0)
  self.选中类型 = "道具"
end
local 道具网格 = 给予NPC["创建网格"](给予NPC, "道具网格", 283, 118, 326, 260)
function 道具网格:初始化()
  self:创建格子(55, 55, 14, 13, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    for i = 1, #给予NPC["给予网格"]["子控件"] do
      if not 给予NPC["给予网格"]["子控件"][i]._spr["物品"] then
        self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
        给予NPC["给予网格"]["置物品"](给予NPC["给予网格"], self.子控件[a]._spr["物品"], i)
        给予NPC["给予网格"]["子控件"][i]._spr["物品"]["原始编号"] = a
        self:置物品(nil, a)
        break
      end
    end
  end
end
function 道具网格:置物品(数据, bh)
  if not bh then
    for i = 1, #道具网格["子控件"] do
      local lssj = __商店格子["创建"]()
      lssj["置物品"](lssj, 数据[i], "给予")
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)
    end
  else
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, 数据, "给予")
    道具网格["子控件"][bh]["置精灵"](道具网格["子控件"][bh], lssj)
  end
end
local 给予网格 = 给予NPC["创建网格"](给予NPC, "给予网格", 34, 150, 214, 55)
function 给予网格:初始化()
  self:创建格子(55, 55, 0, 25, 1, 3)
end
function 给予网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
    给予NPC["道具网格"]["置物品"](给予NPC["道具网格"], self.子控件[a]._spr["物品"], self.子控件[a]._spr["物品"]["原始编号"])
    self:置物品(nil, a)
  end
end
function 给予网格:置物品(数据, bh)
  if not bh then
    for i = 1, #给予网格["子控件"] do
      local lssj = __商店格子["创建"]()
      lssj["置物品"](lssj, 数据[i], "给予")
      给予网格["子控件"][i]["置精灵"](给予网格["子控件"][i], lssj)
    end
  else
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, 数据, "给予")
    给予网格["子控件"][bh]["置精灵"](给予网格["子控件"][bh], lssj)
  end
end
local 关闭 = 给予NPC["创建我的按钮"](给予NPC, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 604, 0)
function 关闭:左键弹起(x, y, msg)
  给予NPC["置可见"](给予NPC, false)
end
local lsan = {"道具", "任务"}
for i = 1, #lsan do
  local 临时函数 = 给予NPC["创建我的单选按钮"](给予NPC, __res:getPNGCC(3, 1040, 201, 149, 37, true)["拉伸"](__res:getPNGCC(3, 1040, 201, 149, 37, true), 125, 37), __res:getPNGCC(3, 1039, 160, 148, 36, true)["拉伸"](__res:getPNGCC(3, 1039, 160, 148, 36, true), 125, 36), lsan[i] .. "按钮", 269 + (i - 1) * 139, 63, lsan[i])
 function  临时函数:左键按下(消息, x, y)
    if "道具" == lsan[i] then
      给予NPC["道具网格"]["置物品"](给予NPC["道具网格"], __主控["道具列表"])
      给予NPC["给予网格"]["置物品"](给予NPC["给予网格"], {})
      给予NPC["选中类型"] = "道具"
    elseif "任务" == lsan[i] then
      给予NPC["道具网格"]["置物品"](给予NPC["道具网格"], __主控["任务道具列表"])
      给予NPC["给予网格"]["置物品"](给予NPC["给予网格"], {})
      给予NPC["选中类型"] = "任务包裹"
    end
  end
end
for i, v in ipairs({
  {
    name = "道具数量1",
    x = 50,
    y = 216,
    w = 35,
    h = 18
  },
  {
    name = "道具数量2",
    x = 130,
    y = 216,
    w = 35,
    h = 18
  },
  {
    name = "道具数量3",
    x = 210,
    y = 216,
    w = 35,
    h = 18
  },
  {
    name = "银两输入",
    x = 120,
    y = 302,
    w = 105,
    h = 18
  }
}) do
  local 临时函数 = 给予NPC["创建输入"](给予NPC, v.name, v.x, v.y, v.w, v.h)
 function  临时函数:初始化()
    self.取光标精灵(self)
    if v.name ~= "银两输入" then
      self:置限制字数(2)
    else
      self:置限制字数(10)
    end
    self:置颜色(39, 53, 81, 255)
    self:置模式(2)
  end
end
local 确定按钮 = 给予NPC["创建我的按钮"](给予NPC, __res:getPNGCC(3, 2, 507, 124, 41)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41), 123, 41), "确定按钮", 65, 350, "确定给予")
function 确定按钮:左键弹起(x, y, msg)
  local lsgz = {}
  local lssl = {}
  for i, v in ipairs(给予NPC["给予网格"]["子控件"]) do
    if v._spr["物品"] then
      table.insert(lsgz, v._spr["物品"]["原始编号"])
      table.insert(lssl, 给予NPC["道具数量" .. i]["取数值"](给予NPC["道具数量" .. i]))
    end
  end
  发送数据(3715, {
    ["格子"] = lsgz,
    ["银子"] = 给予NPC["银两输入"]["取数值"](给予NPC["银两输入"]),
    ["数量"] = lssl,
    ["类型"] = 给予NPC["选中类型"]
  })
  给予NPC["置可见"](给予NPC, false)
end
