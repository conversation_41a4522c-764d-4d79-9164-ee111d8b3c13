__UI弹出["状态提示"] = __UI界面["创建弹出窗口"](__UI界面, "状态提示", 728 + abbr.py.x, 93 + abbr.py.y, 234, 288)
local 状态提示 = __UI弹出["状态提示"]
function 状态提示:初始化()
  self:置精灵(取黑色背景(0, 0, 234, 288))
end
function 状态提示:打开(data)
  if not self.是否可见 then
    self:置可见(true)
  end
  self.状态文本["清空"](self.状态文本)
  for i, v in ipairs(data) do
    self.状态文本["置文本"](self.状态文本, "")
    -- table.print(v)
    if "摄妖香" == v[1] and type(v[5]) == "number" and v[5] > 0 then
      self.状态文本["置文本"](self.状态文本, "#Y" .. v[1])
      self.状态文本["置文本"](self.状态文本, "#Y" .. v[3] .. v[5] .. v[4])
    elseif "红罗羹" == v[1] or "八珍玉液" == v[1] or "绿芦羹" == v[1] then
      self.状态文本["置文本"](self.状态文本, "#Y" .. v[1])
      self.状态文本["置文本"](self.状态文本, "#Y" .. v[3] .. v[5])
    elseif "变身卡" == v[1] and type(v[5]) == "number" and v[5] > 0 then
      self.状态文本["置文本"](self.状态文本, "#Y" .. v[1])
      self.状态文本["置文本"](self.状态文本, "#Y" .. v[3] .. v[5] .. v[4])
    end
  end
end
local 状态文本 = 状态提示["创建我的文本"](状态提示, "状态文本", 15, 15, 185, 255)
