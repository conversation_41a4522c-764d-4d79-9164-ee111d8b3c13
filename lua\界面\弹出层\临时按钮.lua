--[[
LastEditTime: 2024-10-29 12:10:32
--]]


__UI弹出.临时按钮 = 界面:创建弹出窗口("临时按钮")
local 临时按钮 = __UI弹出.临时按钮
function 临时按钮:初始化()

end


function 临时按钮:打开(列表,事件,x,y,zt,ys) 
        if not 列表 or not 列表[1]  then self:置可见(false) return end
        self:置可见(true)
        self.事件=nil
        if 事件 then
            self.事件=事件
        end
        self.宽度 = 70
        local 最大宽度=0
        for i, v in ipairs(列表) do
            if  zt then
                if zt:取宽度(v)>最大宽度 then
                    最大宽度=zt:取宽度(v)
                end
            else
                if 说明字体:取宽度(v)>最大宽度 then
                    最大宽度=说明字体:取宽度(v)
                end
            end
        end
        if 最大宽度~=0 then
            self.宽度=最大宽度+20
        end
        self.高度 =#列表*33+7
        y=y-(#列表*33+7)
        if x+self.宽度 >引擎.宽度 then
            x = 引擎.宽度 - self.宽度- 5
        elseif x<0 then
            x = 0
        end
        if y+self.高度 >引擎.高度 then
            y = 引擎.高度 - self.高度- 5
        elseif y<0 then
            y = 0
        end
        self:置坐标(x,y)
        self:置宽高(self.宽度,self.高度)
        self:置精灵(require('SDL.精灵')(0, 0, 0,self.宽度,self.高度):置颜色(20, 20, 60, 170), true)
        self.列表显示:置宽高(self.宽度-10,self.高度-10)
        self.列表显示:显示列表(列表,self.宽度-10,zt,ys)
end


local 列表显示 = 临时按钮:创建列表("列表显示", 5, 5, 0, 0)
function 列表显示:初始化()
        self.焦点精灵=nil
        self.选中精灵=nil
        self.行高度 = 30
        self.行间距 = 2
end

function 列表显示:显示列表(列表,w,zt,ys)
        self:清空()
        for n=1,#列表 do
                local r = self:添加()
                r:创建纹理精灵(function()
                  __res:getPNGCC(3, 880, 331, 86, 37, true):拉伸(w,30):显示(0, 0)
                  local zts =说明字体
                  if zt then
                      zts=zt
                  end
                  zts:置颜色(255,255,255,255)
                  if ys then
                      if type(ys)=="string" then
                          zts:置颜色(__取颜色(ys))
                      elseif type(ys)=="table" then
                          zts:置颜色(ys[1],ys[2],ys[3],ys[4])
                      end
                  end
                  zts:取图像(列表[n]):显示((w- zts:取宽度(列表[n])) // 2, (30 - zts:取高度(列表[n])) // 2 - 1)
                end
              )

        end
end

function 列表显示:左键弹起(x, y, i)
        if 临时按钮.事件 then
            临时按钮.事件(i)
        end
        临时按钮:置可见(false)
end
