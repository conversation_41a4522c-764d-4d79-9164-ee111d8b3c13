# 手机端热更新完整机制分析

## 1. 问题背景

用户反馈摇杆控件在调试运行时能显示，但打包成安卓客户端后不显示。经分析发现是 `main.lua` 中平台判断逻辑错误导致的。

### 1.1 原始问题代码
```lua
-- main.lua 第18-24行 (错误的平台判断)
if gge.platform == "Android" then
    __手机=false  -- 错误：Android设为false
else  
    __手机=true   -- 非Android平台设为true
end
```

### 1.2 修复后代码
```lua
-- main.lua 修复后
if gge.platform == "Android" or gge.platform == "iOS" then
    __手机=true   -- 正确：移动平台设为true
else
    __手机=false  -- PC平台设为false
end
```

## 2. 热更新系统架构

### 2.1 版本号管理系统

**关键发现**：热更新系统使用双版本号机制，但用途不同：

#### 正确的版本号（用于热更新）
- **文件**：`lua/资源/配置.lua`
- **变量**：`self.配置.版本号 = "1.00"`
- **作用**：热更新版本检测
- **存储**：保存在 `config.txt` 中

#### 其他版本号（非热更新用途）
- **文件**：`lua/UIlayer.lua`
- **变量**：`_版本号 = "1.222"`
- **作用**：登录协议、角色选择等其他功能
- **注意**：**不用于热更新逻辑**

### 2.2 热更新配置参数

```lua
-- UIlayer.lua 中的热更新配置
__热更地址="121.41.9.212"
__热更端口="18886"
__主体名称="ggelua"
```

## 3. 热更新完整流程

### 3.1 启动检测流程

1. **启动时调用**：
   ```lua
   -- UIlayer.lua 第131行
   __Http:效验版本号()
   
   -- 加载层.lua 第39行 (备用调用)
   __Http:效验版本号()
   ```

2. **版本检测核心逻辑**：
   ```lua
   -- 网站处理.lua Http:结束事件
   if __res.配置.版本号 ~= self.receivedata then
       self:更新主体(self.receivedata)
   else
       -- 版本一致，隐藏更新界面
       __UI界面.登录层.更新界面:置可见(false)
       __UI界面.登录层.公告界面:置可见(true, true)
   end
   ```

### 3.2 关键条件检查

```lua
-- 网站处理.lua 第30行
if self.更新 ==1 and not 调试模式 then
    -- 只有在非调试模式下才进行热更新检测
end
```

**重要**：调试模式会跳过热更新检查！

### 3.3 服务器通信流程

1. **连接服务器**：`http://121.41.9.212:18886`
2. **请求版本文件**：`GET /bbb.txt`
3. **版本对比**：`__res.配置.版本号` vs 服务器返回的版本号
4. **触发下载**：版本不一致时开始下载流程

### 3.4 文件下载机制

#### 手机端下载逻辑
```lua
-- 网站处理.lua Http:更新主体
if __手机 then
    self.updata[1]= {
        path = "/ggelua.com",      -- 本地保存路径
        http = "/"..下载名,        -- 服务器请求路径 (/ggelua)
        lx = "脚本"
    }
end
```

#### PC端下载逻辑
```lua
else
    self.updata[1]= {
        path = "/MHXY_new.exe",
        http = "/MHXY.exe", 
        lx = "程序"
    }
end
```

**关键发现**：
- 服务器应放置 `ggelua` 文件（无扩展名）
- 手机端下载后保存为 `ggelua.com`

## 4. 热更新生效机制

### 4.1 手机端重启机制

```lua
-- 网站处理.lua Http:保存事件
if self.savepath == "/ggelua.com" then
   __res:写出文件("config.txt", zdtostring(__res.配置))
   引擎:关闭()  -- 强制退出应用
   return
end
```

**核心发现**：手机端热更新是**冷更新**模式
- 下载完成后强制退出应用
- 用户需要重新启动应用
- 新的 `ggelua.com` 在重启时被加载
- **新配置和逻辑才真正生效**

### 4.2 PC端自更新机制

```lua
-- PC端使用PowerShell脚本自动更新
function Http:执行PC端自我更新()
    -- 生成PowerShell更新脚本
    -- 自动备份、替换、重启程序
end
```

## 5. 编译和构建机制

### 5.1 热更新文件生成

```lua
-- ggerun_hotupdate.lua
编译目录('ggelua',true)      -- 编译ggelua目录
编译目录('./lua',true)       -- 编译lua脚本目录  
写出脚本('./assets/ggelua')  -- 生成最终文件
```

### 5.2 构建任务配置

已在 `.vscode/tasks.json` 中添加：
- `generate_ggelua`：生成无扩展名文件（服务器用）
- `generate_ggelua_dot_com`：生成带扩展名文件（测试用）

## 6. 实时更新vs主体更新

### 6.1 实时更新机制

```lua
-- 实时更新.lua
function 实时更新:检查更新(ip,dk)
    if not __手机 then return end  -- 仅手机端支持
    -- 更新资源文件（WDF等）
    -- 游戏运行中即时生效
end
```

**特点**：
- 仅限手机端
- 主要用于资源文件更新
- 游戏运行中直接生效
- 不影响核心逻辑

### 6.2 主体更新机制

```lua
-- 网站处理.lua 
-- 更新核心逻辑和配置
-- 需要重启应用才能生效
```

**特点**：
- 手机端和PC端都支持
- 更新核心游戏逻辑
- 必须重启才能生效
- 真正的"冷更新"

## 7. 配置文件管理

### 7.1 config.txt结构

```lua
-- 当前config.txt内容示例
{
    账号="888888",
    音效=37,
    坐骑显示=1,
    版本号="1.06",  -- 热更新版本号
    分辨率=2,
    资源包={},
    -- 其他配置项...
}
```

### 7.2 版本号更新流程

1. **检测版本**：读取 `config.txt` 中的版本号
2. **对比服务器**：与 `/bbb.txt` 中的版本号比较
3. **下载更新**：版本不一致时下载新文件
4. **更新配置**：`__res.配置.版本号 = bbh`
5. **保存配置**：`__res:写出文件("config.txt", zdtostring(__res.配置))`
6. **重启应用**：`引擎:关闭()`

## 8. 调试和部署注意事项

### 8.1 调试模式影响

```lua
-- 调试模式会跳过热更新
调试模式=gge.isdebug or false
```

**注意**：
- 调试模式下不会进行热更新检测
- 发布版本需要确保 `调试模式=false`

### 8.2 平台判断修复

```lua
-- 确保正确的平台判断
if gge.platform == "Android" or gge.platform == "iOS" then
    __手机=true
else
    __手机=false
end
```

### 8.3 服务器部署要求

1. **版本文件**：`/bbb.txt` 包含当前版本号
2. **主体文件**：`/ggelua` （无扩展名）
3. **服务器配置**：支持HTTP GET请求
4. **端口开放**：确保18886端口可访问

## 9. 常见问题和解决方案

### 9.1 摇杆控件不显示

**原因**：平台判断错误导致 `__手机=false`
**解决**：修复 `main.lua` 中的平台判断逻辑

### 9.2 热更新不生效

**可能原因**：
1. 调试模式开启
2. 版本号使用错误（使用了UIlayer中的版本号）
3. 服务器文件名错误
4. 网络连接问题

### 9.3 更新后配置丢失

**原因**：`config.txt` 没有正确保存
**解决**：确保热更新完成后调用配置保存

## 10. 总结

手机端热更新是一个**冷更新系统**：
1. **检测版本**：使用 `__res.配置.版本号`
2. **下载文件**：从服务器下载 `ggelua` 保存为 `ggelua.com`
3. **强制重启**：调用 `引擎:关闭()` 退出应用
4. **重新加载**：用户重启应用时加载新的 `ggelua.com`
5. **配置生效**：新的游戏逻辑和配置开始工作

这种设计确保了更新的可靠性，但需要用户手动重启应用才能看到新功能。 