local 打造 = 窗口层:创建窗口("打造", 0, 0, 458,348)
function 打造:初始化()
  self.分类="打造"
  self.处理项目 = "强化人物装备"
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
end
  local 可选项目 = {"强化人物装备","普通人物装备","召唤兽装备","强化灵饰淬灵","灵饰淬灵"}
  local 打造按钮 = 打造:创建红色按钮("打造","打造按钮", 60,300, 80, 22)
  function 打造按钮:左键弹起(x, y)
        if 打造.材料物品[1]  and 打造.材料物品[1].原始编号 and ((打造.材料物品[2] and 打造.材料物品[2].原始编号) or 打造.分类=="修理" or 打造.分类=="分解" or 打造.处理项目=="还原装备") then
            local 发送序列 = nil
            local 发送序列2 = nil
            if 打造.材料物品[2] and 打造.材料物品[2].原始编号 then
                发送序列=打造.材料物品[2].原始编号
            end
            if 打造.材料物品[3] and 打造.材料物品[3].原始编号 then
                发送序列2=打造.材料物品[3].原始编号
            end
            local 发送内容 = {序列=打造.材料物品[1].原始编号,序列1=发送序列,序列2=发送序列2,类型=打造.分类,分类=打造.处理项目}
            if 打造.分类=="修理" then
                    窗口层.文本栏:打开("#Y修理装备有一定的失败风险,你确定要进行修理么?", 4501,发送内容)
            elseif 打造.分类=="镶嵌" then
                if 打造.处理项目 == "宝石转移" then
                    窗口层.文本栏:打开("#Y你确定要将副道具宝石转移至主装备么?", 4501,发送内容)
                elseif 打造.处理项目 == "星辉石转移" then
                    窗口层.文本栏:打开("#Y你确定要将副道具星辉石转移至主装备么?", 4501,发送内容)
                else
                  请求服务(4501,发送内容)
                --     窗口层.文本栏:打开("#Y你确定要将该道具宝石镶嵌该装备么?", 4501,发送内容)
                end
            elseif 打造.分类=="分解" then
                    窗口层.文本栏:打开("#Y你确定要将该装备进行分解么?一旦分解无法找回！", 4501,发送内容)
            elseif 打造.处理项目=="还原装备" then
                    窗口层.文本栏:打开("#Y你确定要将该装备重铸属性还原么?一旦分解无法取消！", 4501,发送内容)
            else
                  请求服务(4501,发送内容)
            end
        end
  end
local 类型设置 = {"打造","镶嵌","合成","修理","熔炼","分解","神器"}
for i, v in ipairs(类型设置) do
      local 临时函数 = 打造:创建红色单选按钮(v, v, 15+(i-1)*62, 35,55,22)
      function  临时函数:左键弹起(x, y)
         打造.分类 = v
         打造.打造按钮:置文字(80,22,打造.分类)
         if v =="打造" then
            打造.处理项目 = "强化人物装备"
          elseif v =="镶嵌" then
            打造.处理项目 = "宝石"
          elseif v =="合成" then
            打造.处理项目 = "宝石"
          elseif v =="修理" then
            打造.处理项目 = "人物装备"
          elseif v =="熔炼" then
            打造.处理项目 = "熔炼装备"
          elseif v =="分解" then
            打造.处理项目 = "人物装备"
          elseif v =="神器" then
            窗口层.对话栏:打开("物件_打铁炉","物件_打铁炉","你想进行哪种操作呢？",{"炼制灵犀之屑","合成灵犀玉","更换神器五行"})
            打造:置可见(false)
         end
         打造:道具刷新()
      end
end
local 分类下拉 = 打造:创建按钮( "分类下拉", 145, 73)   
function  分类下拉:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/xjiem",0x00000050),1)
end
function  分类下拉:左键弹起(x, y)
        if 打造.分类 =="打造" then
            可选项目 = {"强化人物装备","普通人物装备","召唤兽装备","强化灵饰淬灵","灵饰淬灵"}
        elseif 打造.分类 =="镶嵌" then
            可选项目 = {"宝石","星辉石","钟灵石","宝石转移","星辉石转移","珍珠","点化石","精魄灵石"}
        elseif 打造.分类 =="合成" then
            可选项目 = {"宝石","星辉石","钟灵石","精魄灵石","变身卡","百炼精铁","暗器"}
        elseif 打造.分类 =="修理" then
            可选项目 = {"人物装备","召唤兽装备"}
        elseif 打造.分类 =="熔炼" then
            可选项目 = {"熔炼装备","还原装备"}
        elseif 打造.分类 =="分解" then
            可选项目 = {"人物装备","召唤兽装备"}
        end
      local 事件 =function (a)
          if 可选项目[a] then
              打造.处理项目=可选项目[a]
              打造:道具刷新()
          end
        end
        local xx,yy=self:取坐标()
      __UI弹出.弹出列表:打开(可选项目,nil,事件,xx-92,yy+22,110,nil,标题字体)
end


function 打造:打开(道具)
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.分类="打造"
  self.处理项目 = "强化人物装备"
  可选项目 = {"强化人物装备","普通人物装备","召唤兽装备","强化灵饰淬灵","灵饰淬灵"}
  self.打造按钮:置文字(80, 22,"打造")
  self.道具列表={}
  self.道具网格:置数据()
  self:道具刷新(道具)
end


function 打造:刷新材料(道具)
      self.道具列表=table.copy(道具)
      for i=1, 3 do
          if self.材料物品[i] and self.材料物品[i].原始编号 then
              if self.道具列表[self.材料物品[i].原始编号] and self.道具列表[self.材料物品[i].原始编号].识别码==self.材料物品[i].识别码 then
                local 编号 = self.材料物品[i].原始编号
                self.材料物品[i]=self.道具列表[self.材料物品[i].原始编号]
                self.材料物品[i].原始编号 = 编号
                self.道具列表[self.材料物品[i].原始编号]=nil
              else
                  self.材料物品[i]  = nil
              end
          end
      end
      self.道具网格:置物品(self.道具列表)
      self.材料网格:置物品(self.材料物品)
      self:显示刷新()
      self:禁止物品()
      self[self.分类]:置选中(true)
end


function 打造:道具刷新(道具)
        self.材料物品={}
        if 道具 then
            self.道具列表 =table.copy(道具)
        else
            self.道具列表 =table.copy(_tp.道具列表)
        end
        self.道具网格:置物品(self.道具列表)
        self.材料网格:置物品(self.材料物品)
        self:显示刷新()
        self:禁止物品()
        self[self.分类]:置选中(true)
end






function 打造:显示刷新()
  self:打造公式()
  self:创建纹理精灵(function()
    置窗口背景("打造", 0, 0, 458,348,true):显示(0,0)
    文本字体:置颜色(255,255,255,255):取图像("现有资金:"):显示(15, 212)
    文本字体:置颜色(255,255,255,255):取图像("所需体力:"):显示(15, 236)
    文本字体:置颜色(255,255,255,255):取图像("现有体力:"):显示(15, 260)
    文本字体:置颜色(__取颜色("黄色")):取图像("所需材料:"):显示(190, 72)
    取输入背景(0, 0, 95, 22):显示(85,206)
    取输入背景(0, 0, 95, 22):显示(85,230)
    取输入背景(0, 0, 95, 22):显示(85,254)
    取输入背景(0, 0, 95, 22):显示(85,182)
    取输入背景(0, 0, 110, 22):显示(55,73)
    if self.分类=="分解" then
        文本字体:置颜色(255,255,255,255):取图像("需要分解:"):显示(15, 188)
    else
        文本字体:置颜色(255,255,255,255):取图像("所需资金:"):显示(15, 188)
    end
    标题字体:置颜色(255,255,255,255):取图像(self.分类):显示(20, 77)
    文本字体:置颜色(0,0,0,255):取图像(self.处理项目):显示(60, 77)
    文本字体:置颜色(__取颜色("黄色")):取图像(self:所需材料()):显示(255, 72)
    文本字体:置颜色(__取银子颜色(角色信息.银子)):取图像(角色信息.银子):显示(95, 211)
    文本字体:置颜色(0,0,0,255):取图像(角色信息.体力):显示(95, 259)
    文本字体:置颜色(__取银子颜色(self.总价)):取图像(self.总价):显示(95, 187)--所需体力
    文本字体:置颜色(0,0,0,255):取图像(self.消耗体力):显示(95, 235)
  end)
end
local 道具网格 = 打造:创建背包网格("道具网格", 190, 100)
function 道具网格:获得鼠标(x, y,a)
    local 物品 = self:焦点物品()
    if 物品 and 物品.物品 then
        __UI弹出.道具提示:打开(物品.物品,x+25,y+25)
    end
end

function 道具网格:左键弹起(x, y, a)
        local 物品 = self:选中物品()
        if 物品 and 物品.物品 and self:选中()~=0 then
              if __手机 then
                  __UI弹出.道具提示:打开(物品.物品,x+25,y+25,道具网格,"选择",1)
              else
                  self:选择(1)
              end
        end

        
end

function 道具网格:选择(编号)
      if 编号 and 编号~=0 then
          local 物品 = self:选中物品()
          if 物品 and not 物品.物品禁止 then
              打造:设置物品(self:选中())
          end
      end
end

function 打造:设置物品(id)
  local 编号=0
  local 循环次数 = 2
  if 打造.分类=="合成" and  打造.处理项目 == "星辉石"   then 
      循环次数 = 3
  end
  for n=1,循环次数 do
    if not self.材料物品[n] and 编号==0 then 编号=n end
  end
  if 编号 == 0 then
      if self.材料物品[1] and self.材料物品[1].原始编号 then
          self.道具列表[self.材料物品[1].原始编号]=self.材料物品[1]
          self.材料物品[1] = nil
      end
      self.材料物品[1]= self.道具列表[id]
      self.材料物品[1].原始编号=id
      self.道具列表[id]=nil
  else
      if self.材料物品[编号] and self.材料物品[编号].原始编号 then
          self.道具列表[self.材料物品[编号].原始编号]=self.材料物品[编号]
          self.材料物品[编号] = nil
      end
      self.材料物品[编号]= self.道具列表[id]
      self.材料物品[编号].原始编号=id
      self.道具列表[id]=nil
  end

  self.道具网格:置物品(self.道具列表)
  self.材料网格:置物品(self.材料物品)
  self:禁止物品()
  self:显示刷新()
end

local 材料网格 = 打造:创建网格("材料网格", 30, 115, 210, 55)

function 材料网格:获得鼠标(x, y, a)
      if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点 = nil 
      end
      self.焦点=nil
      if 打造.材料物品[a] and 打造.材料物品[a].原始编号  and self.子控件[a] and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
            self.焦点=a
            self.子控件[a]._spr.焦点=true
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25)
      end
end
function 材料网格:失去鼠标(x, y)
    for i, v in ipairs(self.子控件) do
        if v._spr and v._spr.焦点 then
            v._spr.焦点=nil
        end
    end
    self.焦点=nil
end
function 材料网格:左键弹起(x, y, a)
        if 打造.材料物品[a] and 打造.材料物品[a].原始编号 and self.子控件[a] and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
            if __手机 then
                __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25,材料网格,"取消",a)
            else
                  self:取消(a) 
            end
        end
end


function 材料网格:取消(编号)
          if 编号 and 编号~=0 then
                  if _tp.道具列表[打造.材料物品[编号].原始编号] and _tp.道具列表[打造.材料物品[编号].原始编号].名称==打造.材料物品[编号].名称 then
                        打造.道具列表[打造.材料物品[编号].原始编号]=打造.材料物品[编号]
                  end
                  打造.道具网格:置物品(打造.道具列表)
                  打造.材料物品[编号] = nil
                  self:置物品(打造.材料物品)
                  打造:禁止物品()
                  打造:显示刷新()
          end

end


function 材料网格:置物品(数据)
    self.焦点=nil
    if 打造.分类=="合成" and  打造.处理项目 == "星辉石"   then 
        self:创建格子(55, 55, 0, 5, 1, 3)
        self:置坐标(10,105)         
    else 
        self:创建格子(55, 55, 0, 20, 1, 2)
        self:置坐标(30,105) 
    end 
    for i, v in ipairs(self.子控件) do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,55,55,nil,true)
        if 数据 and 数据[i] then
          lssj:置物品(数据[i],55,55,"数量",true,true)
        end
        self.子控件[i]:置精灵(lssj)
    end 
end

function 打造:所需材料()
    if self.处理项目 == "强化人物装备" or self.处理项目 == "普通人物装备" then
         return "制造指南书、百炼精铁"
    elseif self.分类=="打造" and self.处理项目 == "召唤兽装备" then
        return "天眼珠、上古锻造图册"
    elseif self.分类=="打造" and (self.处理项目 == "强化灵饰淬灵" or self.处理项目 == "灵饰淬灵") then
        return "灵饰指南书、元灵晶石"
    elseif self.分类=="镶嵌"  then
        if self.处理项目 == "宝石" then
          return self.处理项目.."、人物装备"
        elseif  self.处理项目 == "星辉石" then
          return self.处理项目.."、人物灵饰"
        elseif self.处理项目 == "宝石转移" then
          return "相同类型人物装备"
        elseif self.处理项目 == "星辉石转移" then
          return "人物灵饰"
        end
    elseif self.处理项目 == "珍珠" then
      return self.处理项目.."、人物装备/召唤兽装备"
    elseif self.处理项目 == "点化石" then
      return "点化石、召唤兽装备"
    elseif self.处理项目 == "精魄灵石" then
      return "精魄灵石、召唤兽装备"
    end
    return self.处理项目 or ""
end

 
function 打造:打造公式()
	self.消耗体力 = 0
	self.总价 = 0
	if self.分类 == "打造" then
		if self.材料物品[1] ~= nil and self.材料物品[2] ~= nil then
			if self.材料物品[1].名称 == "上古锻造图策" or self.材料物品[1].名称 == "天眼珠" or self.材料物品[1].名称 == "三眼天珠" or self.材料物品[1].名称 == "九眼天珠" then
				local a = 0
				if self.材料物品[1].级别限制 and self.材料物品[2].级别限制 then
					if self.材料物品[1].级别限制 >= self.材料物品[2].级别限制 then
						a = self.材料物品[1].级别限制
					else
					    a = self.材料物品[2].级别限制
					end
				end
				self.消耗体力 = a - 20
				self.总价 = (a-65)*400+8950
			else
				local a = 0
				if self.材料物品[1].子类 and self.材料物品[2].子类 then
					if self.材料物品[1].子类 >= self.材料物品[2].子类 then
						a = self.材料物品[1].子类
					else
					  a = self.材料物品[2].子类
					end
				end
				self.消耗体力 = (a - 10)/10*20+50
				if a <= 20 then
				  self.总价 = 5000
				elseif a == 30 then
					self.总价 = 10000
				elseif a == 40 then
					self.总价 = 20000
				elseif a == 50 then
					self.总价 = 30000
				elseif a == 60 then
					self.总价 = 40000
				elseif a == 70 then
					self.总价 = 80000
				elseif a == 80 then
					self.总价 = 100000
				elseif a == 90 then
					self.总价 = 200000
				elseif a == 100 then
					self.总价 = 300000
				elseif a == 110 then
					self.总价 = 400000
				elseif a == 120 then
					self.总价 = 500000
				elseif a == 130 then
					self.总价 = 600000
				elseif a == 140 then
					self.总价 = 700000
				else
				  self.总价 = 800000
				end
			end
		end
	elseif self.分类 == "镶嵌" then
		if self.处理项目 == "宝石转移" then
			if self.材料物品[2] and self.材料物品[2].锻炼等级 then
				self.总价 = self.材料物品[1].级别限制*10000*self.材料物品[2].锻炼等级
			end
		elseif self.处理项目 == "星辉石转移" then
			if self.材料物品[2] and self.材料物品[2].幻化等级 then
				self.总价 = self.材料物品[1].级别限制*20000*self.材料物品[2].幻化等级
			end
		end
		self.消耗体力 = 0
		self.总价 = 0
	elseif self.分类 == "合成" then
		self.总价 = 0
		if self.材料物品[1] ~= nil and self.材料物品[2] ~= nil then
			if self.处理项目 == "宝石" and self.材料物品[1].分类 == 6 then
				self.消耗体力 = (self.材料物品[1].级别限制 or 0)  *10
			elseif self.处理项目 == "碎石锤"  and self.材料物品[1].名称 == "碎石锤" then
				self.消耗体力 = (self.材料物品[1].级别限制 or 0)  *10
			elseif self.处理项目 == "星辉石"  and self.材料物品[1].名称 == "星辉石" then
				self.消耗体力 = (self.材料物品[1].级别限制 or 0)  *10
      elseif self.处理项目 == "钟灵石"  and self.材料物品[1].名称 == "钟灵石" then
				self.消耗体力 = (self.材料物品[1].级别限制 or 0)  *10
      elseif self.处理项目 == "精魄灵石"  and self.材料物品[1].名称 == "精魄灵石" then
				self.消耗体力 = (self.材料物品[1].级别限制 or 0)  *10
			elseif self.处理项目 == "变身卡"   and self.材料物品[1].名称 == "怪物卡片" then
				self.消耗体力 = (self.材料物品[1].等级 or 0) *10
			elseif self.处理项目 == "百炼精铁"   and self.材料物品[1].名称 == "百炼精铁" then
				self.消耗体力 = self.材料物品[1].子类 or 0
			elseif self.处理项目 == "暗器" then
				if self.材料物品[1] ~= nil and self.材料物品[2] ~= nil then
					local a = 0
					local b = 0
					if self.材料物品[1].分类 and self.材料物品[2].分类 then
						if self.材料物品[1].分类 >= self.材料物品[2].分类 then
							a = self.材料物品[1].分类
							b = self.材料物品[2].分类
						else
						    a = self.材料物品[2].分类
						    b = self.材料物品[1].分类
						end
						self.消耗体力 = a
						self.总价 = a*a + b*b
					end
				end
			end
		end
	elseif self.分类 == "熔炼" then
		if self.处理项目 == "熔炼装备" then
        local a=60
        if self.材料物品[1] ~= nil then
          if self.材料物品[1].总类 == 2 then
            a = self.材料物品[1].级别限制
          end
        end
        if self.材料物品[2] ~= nil then
            if self.材料物品[2].总类 == 2 then
                a = self.材料物品[2].级别限制
            end
        end
			self.消耗体力 = math.floor(a/10)
			self.总价 = math.ceil(a/60)*20000
		elseif self.处理项目 == "还原装备" and self.材料物品[1] ~= nil then
			self.总价 = math.ceil(self.材料物品[1].级别限制*10000)
		end
	elseif self.分类 == "修理"  then
		if self.材料物品[1] ~= nil then
			if self.材料物品[1].总类 == 2 then
				self.消耗体力 = (self.材料物品[1].级别限制 or 0)  +20
			end
		end
	elseif self.分类 == "分解" then
		if self.材料物品[1] ~= nil then
			if self.材料物品[1].总类 == 2 then
				local xh = self:取分解(self.材料物品[1].级别限制,self.材料物品[1].分类)
				self.消耗体力 = xh[2]
				self.总价 = xh[1]
			end
		end
	end
  self.消耗体力 = math.ceil(self.消耗体力)
	self.总价 = math.ceil(self.消耗体力)


end

function 打造:取装备可用(物品)
	local mc = 物品.名称
	if self.分类=="打造" then
		if self.处理项目 == "召唤兽装备" and (mc == "上古锻造图策" or mc == "天眼珠" or mc == "三眼天珠" or mc == "九眼天珠") then
			return true
		elseif (self.处理项目 == "强化人物装备" or self.处理项目 == "普通人物装备") and  (mc == "制造指南书" or mc == "百炼精铁") then
			return true
		elseif (self.处理项目 == "强化灵饰淬灵" or self.处理项目 == "灵饰淬灵") and (mc == "灵饰指南书" or mc == "元灵晶石") then
			return true
		end
		return false
	elseif self.分类=="镶嵌" then
      if self.处理项目 == "宝石" and mc ~= "星辉石" and mc ~= "钟灵石" and (物品.总类==2 and 物品.分类 <=6 or 物品.总类==5 and 物品.分类==6) then
        return true
      elseif self.处理项目 == "星辉石" and (mc == "星辉石" or (物品.总类==2 and 物品.分类 >=10 and 物品.分类 <=13)) then
        return true
      elseif self.处理项目 == "钟灵石" and (mc == "钟灵石" or (物品.总类==2 and 物品.分类 >=10 and 物品.分类 <=13)) then
        return true
      elseif self.处理项目 == "珍珠"  and (mc == "珍珠" or 物品.总类==2 and 物品.分类 <=9) then
        return true
      elseif self.处理项目 == "点化石" and (mc == "点化石" or 物品.总类==2 and 物品.分类 <=9 and 物品.分类 >=7) then
        return true
      elseif self.处理项目 == "精魄灵石" and (mc == "精魄灵石" or 物品.总类==2 and 物品.分类 <=9 and 物品.分类 >=7) then
        return true
      elseif self.处理项目 == "宝石转移" and 物品.总类==2 and 物品.分类 <=6 then
        return true
      elseif self.处理项目 == "星辉石转移" and 物品.总类==2 and 物品.分类 <=13 and 物品.分类>=10 then
        return true
      end
		  return false
	elseif self.分类=="熔炼" then
		if self.处理项目 == "熔炼装备" and (物品.总类==2 and 物品.分类 <=6 or mc == "钨金") then
			return true
		elseif self.处理项目 == "还原装备" and 物品.总类==2 and 物品.分类 <=6 then
			return true
		end
		return false
	elseif self.分类=="合成" then
		if self.处理项目 == "宝石" and mc ~= "星辉石" and mc ~= "钟灵石" and 物品.总类 == 5 and 物品.分类==6 then
			return true
		elseif self.处理项目 == "星辉石" and mc == "星辉石" then
			return true
		elseif self.处理项目 == "钟灵石" and mc == "钟灵石" then
			return true
    elseif self.处理项目 == "精魄灵石" and mc == "精魄灵石" then
			return true
		elseif self.处理项目 == "变身卡" and mc == "怪物卡片" then
			return true
		elseif self.处理项目 == "碎石锤" and mc == "碎石锤" then
			return true
		elseif self.处理项目 == "百炼精铁" and mc == "百炼精铁" then
			return true
		elseif self.处理项目 == "暗器" and 物品.总类 == 2000 then
			return true
		end
		return false
	elseif self.分类=="修理" then
        if self.处理项目 == "人物装备" and 物品.总类==2 and 物品.分类 <=6  then
            return true
        elseif self.处理项目 == "召唤兽装备" and 物品.总类==2 and 物品.分类 >6 and 物品.分类 <=9  then
            return true
        end
		return false
	elseif self.分类=="分解" then
        if self.处理项目 == "人物装备" and  (物品.总类==2 and 物品.分类 <=6 or mc == "分解符" or 物品.灵饰) then
            return true
        elseif self.处理项目 == "召唤兽装备" and  (物品.总类==2 and 物品.分类 >6 and 物品.分类 <=9 or mc == "分解符")  then
            return true
        end
		return false
	end
	return false
end


function 打造:取分解(等级,类型)
  if 类型 <= 6 or (类型 >=10 and 类型 <= 13)then
    if 等级 == 60 then
      return {5,90,1}
    elseif 等级 == 70 then
      return {5,105,math.random(1,2)}
    elseif 等级 == 80 then
      return {5,120,math.random(2,3)}
    elseif 等级 == 90 then
      return {6,135,math.random(2,4)}
    elseif 等级 == 100 then
      return {6,150,math.random(3,5)}
    elseif 等级 == 110 then
      return {7,165,math.random(3,5)}
    elseif 等级 == 120 then
      return {12,180,math.random(4,6)}
    elseif 等级 == 130 then
      return {16,195,math.random(5,6)}
    elseif 等级 == 140 then
      return {23,210,math.random(5,6)}
    else
      return {999,9999,math.random(5,6)}
    end
  else
    if 等级 == 65 then
      return {4,90,1}
    elseif 等级 == 75 then
      return {4,105,1}
    elseif 等级 == 85 then
      return {4,120,math.random(1,2)}
    elseif 等级 == 95 then
      return {4,135,math.random(1,2)}
    elseif 等级 == 105 then
      return {5,150,math.random(2,3)}
    elseif 等级 == 115 then
      return {5,165,math.random(2,3)}
    elseif 等级 == 125 then
      return {8,180,math.random(4,6)}
    elseif 等级 == 135 then
      return {16,195,math.random(5,6)}
    elseif 等级 == 145 then
      return {23,210,math.random(5,6)}
     else
      return {999,9999,math.random(5,6)}
    end
  end
end


function 打造:禁止物品()
  local 编号={}
  for k,v in pairs(self.道具列表) do
         if not self:取装备可用(v) then
              table.insert(编号,k)
          end
   end
   self.道具网格:置禁止(true,nil,编号)
end





local 关闭 = 打造:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  打造:置可见(false)
end
