local 符石合成 = 窗口层:创建窗口("符石合成", 0, 0, 285,420)
function 符石合成:初始化()

  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
end

  local 合成 = 符石合成:创建红色按钮("合成","合成", 160,143, 80, 22)
  function 合成:左键弹起(x, y)
         local 材料 ={}
         for i, v in ipairs(符石合成.材料物品) do
              材料[i]=v.原始编号
         end
         if #材料<3 then
              __UI弹出.提示框:打开("#Y你放置的材料不够")
         else
              请求服务(3804,{材料=材料})
         end
  end



function 符石合成:打开(道具)
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.道具列表={}
  self.道具网格:置数据()
  self:道具刷新(道具)
end


function 符石合成:刷新材料(道具)
      self.道具列表=table.copy(道具)
      for i=1, 4 do
          if self.材料物品[i] and self.材料物品[i].原始编号 then
              if self.道具列表[self.材料物品[i].原始编号] and self.道具列表[self.材料物品[i].原始编号].识别码==self.材料物品[i].识别码 then
                local 编号 = self.材料物品[i].原始编号
                self.材料物品[i]=self.道具列表[self.材料物品[i].原始编号]
                self.材料物品[i].原始编号 = 编号
                self.道具列表[self.材料物品[i].原始编号]=nil
              else
                  self.材料物品[i]  = nil
              end
          end
      end
      self.道具网格:置物品(self.道具列表)
      self.材料网格:置物品(self.材料物品)
      self:显示刷新()
      self:禁止物品()
end


function 符石合成:道具刷新(道具)
        self.材料物品={}
        if 道具 then
            self.道具列表 =table.copy(道具)
        else
            self.道具列表 =table.copy(_tp.道具列表)
        end
        self.道具网格:置物品(self.道具列表)
        self.材料网格:置物品(self.材料物品)
        self:显示刷新()
        self:禁止物品()

end






function 符石合成:显示刷新()

  self:创建纹理精灵(function()
    置窗口背景("符石合成", 0, 0, 285,420,true):显示(0,0)
    文本字体:置颜色(255,255,255,255)
    取输入背景(0, 0, 95, 22):显示(180,35)
    取输入背景(0, 0, 95, 22):显示(180,60)
    取输入背景(0, 0, 95, 22):显示(180,85)
    取输入背景(0, 0, 95, 22):显示(180,110)
    文本字体:取图像("- 合成材料 -"):显示(28,33)
    文本字体:取图像("所需现金:"):显示(120, 38)
    文本字体:取图像("现有现金:"):显示(120, 63)
    文本字体:取图像("所需体力:"):显示(120, 88)
    文本字体:取图像("现有体力:"):显示(120, 113)
    文本字体:置颜色(__取银子颜色(角色信息.银子)):取图像(角色信息.银子):显示(185,63)
    文本字体:置颜色(0,0,0,255)
    文本字体:取图像(角色信息.体力):显示(185, 113)
    文本字体:取图像(0):显示(185, 38)
    local 数量 =0
    for i, v in ipairs(self.材料物品) do
        数量=数量+1
    end
    if 数量>=3 then
        文本字体:取图像(40):显示(185, 88)
    else
        文本字体:取图像(0):显示(185, 88)
    end
  end)
end
local 道具网格 = 符石合成:创建背包网格("道具网格", 13, 170)
function 道具网格:获得鼠标(x, y,a)
    local 物品 = self:焦点物品()
    if 物品 and 物品.物品 then
        __UI弹出.道具提示:打开(物品.物品,x+25,y+25)
    end
end

function 道具网格:左键弹起(x, y, a)
        local 物品 = self:选中物品()
        if 物品 and 物品.物品 and self:选中()~=0 then
              if __手机 then
                  __UI弹出.道具提示:打开(物品.物品,x+25,y+25,道具网格,"选择",1)
              else
                  self:选择(1)
              end
        end

end

function 道具网格:选择(编号)
      if 编号 and 编号~=0 then
            local 物品 = self:选中物品()
            if 物品 and not 物品.物品禁止 then
              符石合成:设置物品(self:选中())
            end
      end
end



function 符石合成:设置物品(id)
  local 编号=0
  for n=1,4 do
    if not self.材料物品[n] and 编号==0 then 编号=n end
  end
  if 编号 == 0 then
      if self.材料物品[1] and self.材料物品[1].原始编号 then
          self.道具列表[self.材料物品[1].原始编号]=self.材料物品[1]
          self.材料物品[1] = nil
      end
      self.材料物品[1]= self.道具列表[id]
      self.材料物品[1].原始编号=id
      self.道具列表[id]=nil
  else
      if self.材料物品[编号] and self.材料物品[编号].原始编号 then
          self.道具列表[self.材料物品[编号].原始编号]=self.材料物品[编号]
          self.材料物品[编号] = nil
      end
      self.材料物品[编号]= self.道具列表[id]
      self.材料物品[编号].原始编号=id
      self.道具列表[id]=nil
  end

  self.道具网格:置物品(self.道具列表)
  self.材料网格:置物品(self.材料物品)
  self:禁止物品()
  self:显示刷新()
end

local 材料网格 = 符石合成:创建网格("材料网格", 10, 55, 105, 105)

function 材料网格:获得鼠标(x, y, a)
      if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点 = nil 
      end
      self.焦点=nil
      if 符石合成.材料物品[a] and 符石合成.材料物品[a].原始编号  and self.子控件[a] and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
            self.焦点=a
            self.子控件[a]._spr.焦点=true
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25)
      end
end
function 材料网格:失去鼠标(x, y)
    for i, v in ipairs(self.子控件) do
        if v._spr and v._spr.焦点 then
            v._spr.焦点=nil
        end
    end
    self.焦点=nil
end
function 材料网格:左键弹起(x, y, a)
        if 符石合成.材料物品[a] and 符石合成.材料物品[a].原始编号 and self.子控件[a] and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
            if __手机 then
                __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25,材料网格,"取消",a)
            else
                  self:取消(a)
            end
        end
end

function 材料网格:取消(编号)
        if 编号 and 编号~=0 then
            if 符石合成.材料物品[编号] and 符石合成.材料物品[编号].原始编号 then
                  符石合成.道具列表[符石合成.材料物品[编号].原始编号]=符石合成.材料物品[编号]
            end
            符石合成.道具网格:置物品(符石合成.道具列表)
            符石合成.材料物品[编号] = nil
            self:置物品(符石合成.材料物品)
            符石合成:禁止物品()
            符石合成:显示刷新()

        end

end

function 材料网格:置物品(数据)
    self.焦点=nil

    self:创建格子(50, 50, 5, 5, 2, 2)
      
    for i, v in ipairs(self.子控件) do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,50,50,nil,true)
        if 数据 and 数据[i] then
          lssj:置物品(数据[i],50,50,"数量",true,true)
        end
        self.子控件[i]:置精灵(lssj)
    end 
end



function 符石合成:禁止物品()
  local 编号={}
  for k,v in pairs(self.道具列表) do
         if v.总类 ~= 889 and v.分类 ~= 2 and v.名称~="符石卷轴" then
              table.insert(编号,k)
         end
   end
   self.道具网格:置禁止(true,nil,编号)
end





local 关闭 = 符石合成:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  符石合成:置可见(false)
end
