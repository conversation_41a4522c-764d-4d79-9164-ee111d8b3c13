
function 登录层:初始化()
    self:置宽高(引擎.宽度,引擎.高度)
    self.背景= __res:取资源动画('dlzy',0xabcde003,"图像"):拉伸(引擎.宽度,引擎.高度):到精灵()
    self.场景=__res:取资源动画('dlzy',0xEB3FD8C3,"精灵")  
    self.场景计次 = 0
    self.场景2=__res:取资源动画('dlzy',0xEB3FD8C3,"精灵")  --------------------按照这样改 
    self.场景2计次 = -self.场景2.宽度
    self.特效=__res:取资源动画('dlzy',0xEC1A0419,"图像"):拉伸(引擎.宽度,引擎.高度):到精灵()
    self.特效计次 = 0
    self.特效2=__res:取资源动画('dlzy',0xEC1A0419,"图像"):拉伸(引擎.宽度,引擎.高度):到精灵()
    self.特效2计次 = -self.特效2.宽度
    self.猴子 = __res:取资源动画('dlzy',0xDC739617,"动画")
    self.八戒 = __res:取资源动画('dlzy',0x22E6E35C,"动画")
    self.唐僧 = __res:取资源动画('dlzy',0x16CC1B46,"动画")
    self.沙僧 = __res:取资源动画('dlzy',0xD8632D20,"动画")
end

function 登录层:更新(dt)
    self.猴子:更新(dt)
    self.八戒:更新(dt)
    self.唐僧:更新(dt)
    self.沙僧:更新(dt)
    self.场景计次 = self.场景计次 + 1
    self.场景2计次=self.场景2计次 + 1
    self.特效计次 = self.特效计次 + 4
    self.特效2计次=self.特效2计次 + 4 
    if self.场景计次 >= self.场景.宽度 then
        self.场景计次 = -self.场景.宽度
    end
    if self.场景2计次 >= self.场景2.宽度 then
        self.场景2计次 = -self.场景2.宽度
    end
     
    if self.特效计次 >= self.特效.宽度 then
        self.特效计次 = -self.特效.宽度
    end
    if self.特效2计次 >= self.特效2.宽度 then
        self.特效2计次 = -self.特效2.宽度
    end
    
 

end

function 登录层:显示(x, y)

   self.背景:显示(0, -150)
   self.场景:显示(0+self.场景计次, 引擎.高度)
   self.场景2:显示(0+self.场景2计次, 引擎.高度)
   self.猴子:显示(引擎.宽度2-180, 引擎.高度-70)
   self.八戒:显示(引擎.宽度2-70, 引擎.高度-70)
   self.唐僧:显示(引擎.宽度2+40, 引擎.高度-70)
   self.沙僧:显示(引擎.宽度2+150, 引擎.高度-70)
   self.特效:显示(0+self.特效计次, 50)
   self.特效2:显示(0+self.特效2计次,50)


end

