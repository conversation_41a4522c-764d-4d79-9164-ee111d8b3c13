local 门派按钮组={
  大唐官府= __res:取资源动画("pic/zmp", "dtgf.png","图片"),
  方寸山= __res:取资源动画("pic/zmp", "fcs.png","图片"),
  女儿村= __res:取资源动画("pic/zmp", "nrc.png","图片"),
  神木林= __res:取资源动画("pic/zmp", "sml.png","图片"),
  化生寺=__res:取资源动画("pic/zmp", "hss.png","图片"),
  魔王寨=__res:取资源动画("pic/zmp", "mwz.png","图片"),
  阴曹地府= __res:取资源动画("pic/zmp", "ycdf.png","图片"),
  盘丝洞=__res:取资源动画("pic/zmp", "psd.png","图片"),
  无底洞= __res:取资源动画("pic/zmp", "wdd.png","图片"),
  狮驼岭=__res:取资源动画("pic/zmp", "stl.png","图片"),
  天宫= __res:取资源动画("pic/zmp", "tg.png","图片"),
  龙宫=__res:取资源动画("pic/zmp", "lg.png","图片"),
  普陀山= __res:取资源动画("pic/zmp", "pts.png","图片"),
  凌波城= __res:取资源动画("pic/zmp", "lbc.png","图片"),
  花果山=__res:取资源动画("pic/zmp", "hgs.png","图片"),
  五庄观= __res:取资源动画("pic/zmp", "wzg.png","图片"),
}
local 多开创建 = 窗口层:创建窗口("多开创建", 0, 0, 545, 345)
function 多开创建:初始化()
  self:置精灵(置窗口背景("创建角色", 0, 0, 545, 345)) 
  self:置宽高(545,345)

  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
end

function 多开创建:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.角色控件:置可见(true)
  self.门派选择:置可见(false)
end





local 模型 ={"逍遥生","剑侠客","偃无师","飞燕女","英女侠","巫蛮儿","巨魔王","虎头怪","杀破狼","狐美人","骨精灵","鬼潇潇","羽灵神","神天兵","龙太子","舞天姬","玄彩娥","桃夭夭"}
local 角色控件= 多开创建:创建控件("角色控件", 10, 45, 535, 295)
function 角色控件:初始化()
      self.图像2 =  self:创建纹理精灵(function()
        取白色背景(0, 0, 525, 295, true):显示(0, 0)
        __res:取资源动画("pic", "rmx.png","图片"):显示(20, 30)
        local xx = 0
        local yy = 0
        for i =1,18 do
          __res:取资源动画("pic", "fgbj.png","图片"):显示(70+xx*70, 30+yy*90)
          xx = xx + 1
          if xx>=6  then
            xx = 0
             yy = yy +1
          end
        end
      end,1
    )
end
function 角色控件:显示(x,y)
      if  self.图像2 then
          self.图像2:显示(x,y)
      end
end

local 门派选择= 多开创建:创建控件("门派选择", 10, 30, 525, 305)
function 门派选择:初始化()
      self.图像 =  self:创建纹理精灵(function()
        取白色背景(0, 0, 225, 310, true):显示(0, 2)
        取白色背景(0, 0, 300, 210, true):显示(230, 95)
        __res:取资源动画("pic", "jsdt.png","图片"):显示(230, 5)
        文本字体:置颜色(0,0,0,255):取图像("未选择"):显示(380,55)
        文本字体:置颜色(0,0,0,255):取图像("输入名称:"):显示(240,120)
        取输入背景(0, 0, 200, 20):显示(284,168)
      end,1
    )
end

function 门派选择:更新(dt)
  if self.模型格子 then
      self.模型格子:更新(dt)
  end
end


function 门派选择:显示(x,y)
      if  self.图像 then
          self.图像:显示(x,y)
      end
      if  self.图像3 then
        self.图像3:显示(x,y)
      end
      if  self.模型格子 then
        self.模型格子:显示(x-90,y-110)
      end
end
function 门派选择:重置(角色)
   self.图像3= nil
   self.角色 = 角色
   local 模型1={模型=角色,装备={}}
   self.模型格子= __UI模型格子:创建()  
   self.模型格子:清空()
   self.模型格子:置数据(模型1, "角色", 200,300)  
   self.名称输入:置文本("")
   self.图像3 =  self:创建纹理精灵(function()
    文本字体:置颜色(0,0,0,255):取图像(角色):显示(380,25)
  end,1
)
end


local 名称输入 = 门派选择:创建文本输入("名称输入", 290, 171, 140, 18)     
function 名称输入:初始化()
  self:取光标精灵()
  self:置限制字数(11)
  self:置颜色(39, 53, 81, 255)
end


local 重选按钮 = 门派选择:创建红色按钮( "重选","重选",430, 230,74, 22) 
function 重选按钮:左键弹起(x, y)
      角色控件:置可见(true)
      门派选择:置可见(false)
end


local 创建按钮 = 门派选择:创建红色按钮( "创建","创建",260, 230,74, 22) 
function 创建按钮:左键弹起(x, y)
   if 门派选择.角色 then
      if 门派选择.名称输入:取文本()~=nil and 门派选择.名称输入:取文本()~="" then
          请求服务(63,{参数=角色信息.数字id,文本="创建角色",模型=门派选择.角色,名称=门派选择.名称输入:取文本()})
          多开创建:置可见(false)
      else
        __UI弹出.提示框:打开("#Y请输入正确的名称")
      end
   else
      __UI弹出.提示框:打开("#Y请重新选择角色")
    end

end
local xx = 0
local yy = 0
for i = 1, 18 do
    local 临时函数名 = 角色控件:创建按钮(模型[i],75+xx*70,35+yy*90)
    function 临时函数名:初始化()
       local lssj = 取头像(模型[i])
       self:创建按钮精灵( __res:取资源动画(lssj[7],lssj[2],"图像"))
    end
    function 临时函数名:左键弹起(x, y)
         角色控件:置可见(false)
         门派选择:置可见(true)
         门派选择:重置(模型[i])
    end
    xx = xx + 1
    if xx>=6  then
       xx = 0
       yy = yy +1
    end
end



local 关闭 = 多开创建:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  多开创建:置可见(false)
end







