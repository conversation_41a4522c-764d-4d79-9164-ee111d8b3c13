
local 超级赐福 = 窗口层:创建窗口("超级赐福", 0,0, 500, 450)



function 超级赐福:初始化()
  self:创建纹理精灵(function()
                      置窗口背景("超级赐福", 0, 0, 500, 450, true):显示(0, 0)
                      取白色背景(0, 0, 220, 200, true):显示(10, 30)
                      取白色背景(0, 0, 260, 200, true):显示(235, 30)
                      取白色背景(0, 0, 220, 205, true):显示(10, 236)
                    __res:取资源动画("pic","fgbj.png","图片"):显示(336, 137)
                     local 丸子资源=取物品("仙露丸子")
                     __res:取资源动画(丸子资源[11],丸子资源[12],"图像"):显示(343, 143)
                     for n=1,4 do
                        __res:取资源动画("jszy/dd",0x00000070,"图像"):拉伸(45, 45):显示(248+(n-1)*60,43)
                    end
                    end
              )

  self.模型格子 = __UI模型格子.创建()
  self.加锁图标 = __res:取资源动画("jszy/xjiem",0X85655274,"精灵")
  self.赐福技能={}
  for i=1,4 do
        local lssj = __技能格子:创建()
        lssj:置数据(nil,true)
        self.赐福技能[i]=lssj
  end




  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end
end

function 超级赐福:打开()
    self:置可见(true)
    self.模型格子:清空()
    self.选中=nil
    
    self:刷新()
end


function 超级赐福:刷新()
    self.物品数量=0
    for n=1,100 do
        if _tp.道具列表[n]~=nil and _tp.道具列表[n].名称=="仙露丸子" then
            self.物品数量=self.物品数量+_tp.道具列表[n].数量
        end
    end
  
    self:显示刷新()
end


function 超级赐福:显示刷新()
    self.焦点=nil
    self.锁定状态={}
    self.需求数量 = 1
    self.保存技能:置可见(false)
    self.超级保存:置可见(false)
    self.查看技能:置可见(false)
    self.开启赐福:置坐标(320, 198)
    self.开启赐福:重置文字("赐 福")
    self.道具网格:置数据(_tp.道具列表)
    self.名称选择:置数据()
    for i=1,4 do
       self["锁定"..i]:重置文字("锁定",true)
       self.赐福技能[i]:置数据(nil,true)
    end
    local 锁定数量=0
    if self.选中~=nil and self.选中~=0 and  角色信息.宝宝列表[self.选中]~=nil  then
        self.模型格子:置数据(角色信息.宝宝列表[self.选中], "召唤兽", 125,190)
        self.名称选择:置选中(self.选中)
        if 角色信息.宝宝列表[self.选中].临时赐福~=nil then
              local 已有技能={}
              for i=1,4 do
                  self.锁定状态[i]=false
                  self.赐福技能[i]:置数据(角色信息.宝宝列表[self.选中].临时赐福[i],true)
                  for n=1,#角色信息.宝宝列表[self.选中].技能 do
                        if  self.赐福技能[i].数据 and self.赐福技能[i].数据.名称==角色信息.宝宝列表[self.选中].技能[n] then
                            已有技能[角色信息.宝宝列表[self.选中].技能[n]]=true
                        end
                  end
                  if 角色信息.宝宝列表[self.选中].赐福锁定~=nil  and  角色信息.宝宝列表[self.选中].赐福锁定[i] then
                        锁定数量=锁定数量+1
                        self["锁定"..i]:重置文字("解锁")
                        self.锁定状态[i]=true
                  else
                        self["锁定"..i]:重置文字("锁定")
                  end
                  self["锁定"..i]:置禁止(false)
              end
              for i=1,4 do
                  if  self.赐福技能[i].数据 and not 已有技能[self.赐福技能[i].数据.名称] then
                      self.赐福技能[i]:置灰色(true)
                  end
              end
              if self.赐福技能[1].数据 then
                  self.保存技能:置可见(true)
                  self.开启赐福:置坐标(380,198)
                  self.开启赐福:重置文字("再次赐福")
              end
        end    
    end
    if 锁定数量==1 then
        self.需求数量=5
    elseif 锁定数量>=2 then
        self.需求数量=25
    else
        self.需求数量=1
    end 
    if self.物品数量<self.需求数量 then
        self.开启赐福:置禁止(true)
    else
        self.开启赐福:置禁止(false)
    end

    self.物品字体=文本字体:置颜色(255,255,255,255):取投影精灵(self.物品数量.."/"..self.需求数量)
end


function 超级赐福:更新(dt)
    self.模型格子:更新(dt)
    if self.赐福显示~=nil then
      self:赐福动画(dt)
    end
end


function 超级赐福:显示(x, y)
    if self.赐福技能~=nil then
        for i=1,4 do
          if self.赐福技能[i] then
                if self.焦点~=i and self.赐福技能[i].焦点 then
                  self.赐福技能[i].焦点=nil
                end
                self.赐福技能[i]:显示(x+250+(i-1)*60,y+45)  
                if self.锁定状态[i] then
                  self.加锁图标:显示(x+265+(i-1)*60,y+70)
                end
          end
        end
    end
    if self.物品字体~=nil then
        self.物品字体:显示(x+338+(50-self.物品字体.宽度)//2,y+170)
    end
    self.模型格子:显示(x, y)
end



function 超级赐福:左键弹起(x, y)
        if __手机  then
            for i=1,4 do
                if  self.赐福技能[i].数据  and self.赐福技能[i]:检查透明(x, y) then
                    __UI弹出.超级技能详情:打开(x+20,y+20,self.赐福技能[i].数据)
                end
            end
        end
end

function 超级赐福:获得鼠标(x, y)
        self.焦点=nil
        for i=1,4 do
            if  self.赐福技能[i].数据  and self.赐福技能[i]:检查透明(x, y) then
                self.焦点=i
                self.赐福技能[i].焦点=true
                __UI弹出.超级技能详情:打开(x+20,y+20,self.赐福技能[i].数据)
            end
        end
end
function 超级赐福:失去鼠标(x, y)
    self.焦点=nil
end


  local 名称选择 = 超级赐福:创建列表("名称选择", 15, 241, 198, 195)   
  function 名称选择:初始化()
      self.行高度= 37
      self.行间距 = 2
  end
  local 滑块=超级赐福:创建竖向滑块("名称滑块",218,236,10,205,true)
  名称选择:绑定滑块(滑块.滑块)
  
  
  function 名称选择:置数据()
        self:清空()
        for i, v in ipairs(角色信息.宝宝列表) do
            local r = self:添加()
            r:创建纹理精灵(function()
                        __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
                        local lssj = 取头像(v.模型)
                        __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
                        文本字体:置颜色(0,0,0,255)
                        文本字体:取图像(v.名称):显示(40,4)
                        文本字体:取图像(v.等级.."级"):显示(40,20)
                      end
                )

        end
  end
  
  function 名称选择:左键弹起(x, y, i)
      if 角色信息.宝宝列表[i] then
            超级赐福.选中 = i
            超级赐福:显示刷新()
       
      end
  end
  

local 道具网格=超级赐福:创建道具网格("道具网格",235,235)

function 道具网格:获得鼠标(x,y,i)
    local 物品 = self:焦点物品()
    if 物品 and 物品.物品  then
          local xx,yy=引擎:取鼠标坐标()
        __UI弹出.道具提示:打开(物品.物品,xx+20,yy+20)
    end
end
function 道具网格:左键弹起(x,y,i)
  local 物品=self:选中物品() 
  if __手机 and 物品 and 物品.物品 then
    __UI弹出.道具提示:打开(物品.物品,x+40,y+40)
  end

end




local 技能列表 = 超级赐福:创建按钮("技能列表",125, 35)
function 技能列表:初始化()
    self:创建按钮精灵(__res:取资源动画("pic","jinengliebiao.png","图片"),1)

end

function 技能列表:左键弹起(x, y)
    超级赐福.查看技能:打开(超级赐福.选中)
    超级赐福.超级保存:置可见(false)
end




local 开启赐福 = 超级赐福:创建红色按钮("赐福", "开启赐福", 320, 198,80,22)
function 开启赐福:重置文字(txt,jz)
    self:置文字(80,22,txt)
    self:置禁止(false)

    if jz then
        self:置禁止(true)
    end
end
function 开启赐福:左键弹起(x, y)
    if 超级赐福.选中==nil or 超级赐福.选中==0 then
          __UI弹出.提示框:打开('#Y你还没有选择宝宝,请选择宝宝后在操作')
    else
        请求服务(5021,{类型="随机赐福",序列=角色信息.宝宝列表[超级赐福.选中].认证码})
    end
end

local 保存技能 = 超级赐福:创建红色按钮("保存技能", "保存技能", 260, 198,80,20)
function 保存技能:左键弹起(x, y)
      超级赐福.超级保存:打开(超级赐福.选中,超级赐福.赐福技能)
      超级赐福.查看技能:置可见(false)
end



local 锁定按钮={"锁定1","锁定2","锁定3","锁定4"}

for i, v in ipairs(锁定按钮) do
    local 临时函数 = 超级赐福:创建红色按钮(v, v, 248+(i-1)*60, 95,45,22)  
    function 临时函数:重置文字(txt,jz)
        self:置文字(45,22,txt)
        self:置禁止(false)
    
        if jz then
            self:置禁止(true)
        end
    end
    function 临时函数:左键弹起(x, y)
            if 超级赐福.选中==nil or 超级赐福.选中==0 then
              __UI弹出.提示框:打开('#Y你还没有选择宝宝,请选择宝宝后在操作')
            elseif not 超级赐福.赐福技能[i].数据 then
              __UI弹出.提示框:打开('#Y请先赐福技能')
            else
                if 超级赐福.锁定状态[i] then
                  请求服务(5021,{类型="解锁技能",编号=i,序列=角色信息.宝宝列表[超级赐福.选中].认证码})
                else
                  请求服务(5021,{类型="锁定技能",编号=i,序列=角色信息.宝宝列表[超级赐福.选中].认证码})
                   
                end
            end
    end

end



function 超级赐福:刷新赐福(数据)
    self.物品数量=0
    for n=1,100 do
        if _tp.道具列表[n]~=nil and _tp.道具列表[n].名称=="仙露丸子" then
          self.物品数量=self.物品数量+_tp.道具列表[n].数量
        end
    end
    if self.选中~=nil and self.选中~=0 then
        角色信息.宝宝列表[超级赐福.选中].临时赐福=数据
        local  是否显示 = true
        if 角色信息.宝宝列表[超级赐福.选中].临时赐福==nil  then
            是否显示 = false
        else
            for i=1,4 do
                if 角色信息.宝宝列表[超级赐福.选中].临时赐福[i]==nil then
                  是否显示 = false
                end
            end
        end
        if 是否显示 then
            self.赐福显示=0
            for i=1,4 do
               self["锁定"..i]:重置文字("锁定",true)
               self.赐福技能[i]:置数据(nil,true)
            end
            self.超级保存:置可见(false)
            self.查看技能:置可见(false)
            self.保存技能:置可见(false)
            self.开启赐福:置坐标(320, 198) 
            self.开启赐福:重置文字("赐 福",true)
        else
            self.赐福显示=nil
        end
    end
end


function 超级赐福:赐福动画(dt)
	if self.赐福显示~=nil then
		 -- self.赐福特效:更新(dt)
		 -- self.赐福特效:显示(self.x+321-80,self.y+30)
      	self.赐福显示=self.赐福显示+1
	    if self.赐福显示==20 then
            self.赐福技能[1]:置数据(角色信息.宝宝列表[self.选中].临时赐福[1])
	    elseif self.赐福显示==40 then
            self.赐福技能[2]:置数据(角色信息.宝宝列表[self.选中].临时赐福[2])
		elseif self.赐福显示==60 then
            self.赐福技能[3]:置数据(角色信息.宝宝列表[self.选中].临时赐福[3])  
		elseif self.赐福显示==80  then
            self.赐福技能[4]:置数据(角色信息.宝宝列表[self.选中].临时赐福[4])
		elseif self.赐福显示==100 then
		        self:显示刷新()
			    self.赐福显示=nil
      	end
	end
end




local 超级保存 = 超级赐福:创建控件("超级保存", 135,30, 320, 225)
function 超级保存:初始化()
  self:创建纹理精灵(function()
                      置窗口背景("保存技能", 0, 0, 320, 225, true):显示(0, 0)
                      文本字体:置颜色(255,255,255,255):取图像("原赐福效果"):显示(120, 35)
                      文本字体:置颜色(255,255,255,255):取图像("本次赐福效果"):显示(115, 110)
                    end
              )
     self.原有赐福={}
    for i = 1, 4 do
        local lssj = __技能格子:创建()
        lssj:置数据(nil,true)
        self.原有赐福[i]=lssj
    end
    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end
end

function 超级保存:打开(编号,当前赐福)
        if not 编号 or 编号==0 then
            return
        end
        self:置可见(not self.是否可见)
        if not self.是否可见 then
            return
        end
        self.编号=编号
        self.赐福技能=当前赐福
        self.焦点=nil
        self.焦点1=nil  
        local 已有技能={}
        for i=1,4 do
            self.原有赐福[i]:置数据(nil,true)
            if 角色信息.宝宝列表[编号] then
                if 角色信息.宝宝列表[编号].超级赐福 then
                    self.原有赐福[i]:置数据(角色信息.宝宝列表[编号].超级赐福[i],true)
                end 
                for n=1,#角色信息.宝宝列表[编号].技能 do
                    if  self.原有赐福[i].数据 and self.原有赐福[i].数据.名称==角色信息.宝宝列表[编号].技能[n] then
                        已有技能[角色信息.宝宝列表[编号].技能[n]]=true
                    end
                end
            end
        end
        for i=1,4 do
            if self.原有赐福[i] and self.原有赐福[i].数据  and not 已有技能[self.原有赐福[i].数据.名称] then
                self.原有赐福[i]:置灰色(true)
            end
        end
          
end

function 超级保存:显示(x, y)
    for i=1,4 do
        if self.原有赐福[i] then
            if self.焦点1~=i and self.原有赐福[i].焦点 then
                self.原有赐福[i].焦点=nil
            end
            self.原有赐福[i]:显示(x+20+(i-1)*80,y+60)
        end
        if self.赐福技能 and self.赐福技能[i]  then
            if self.焦点~=i and self.赐福技能[i].焦点 then
                self.赐福技能[i].焦点=nil
            end
            self.赐福技能[i]:显示(x+20+(i-1)*80,y+135)
        end
    end


end

function 超级保存:右键弹起(x, y)
        self:置可见(false)
end

function 超级保存:左键弹起(x, y)
    if __手机 then
        for i=1,4 do
            if self.赐福技能 and self.赐福技能[i] and  self.赐福技能[i].数据 and self.赐福技能[i]:检查透明(x, y) then
                __UI弹出.超级技能详情:打开(x+20,y+20,self.赐福技能[i].数据)
            end
            if self.原有赐福[i].数据 and self.原有赐福[i]:检查透明(x, y) then
                __UI弹出.超级技能详情:打开(x+20,y+20,self.原有赐福[i].数据)
            end
        end
    end
end

function 超级保存:获得鼠标(x, y)
    self.焦点=nil
    self.焦点1=nil
        for i=1,4 do
            if  self.赐福技能 and self.赐福技能[i] and self.赐福技能[i].数据 and self.赐福技能[i]:检查透明(x, y) then
                self.焦点=i
                self.赐福技能[i].焦点=true
                __UI弹出.超级技能详情:打开(x+20,y+20,self.赐福技能[i].数据)
            end
            if  self.原有赐福[i].数据 and self.原有赐福[i]:检查透明(x, y) then
                self.焦点1=i
                self.原有赐福[i].焦点=true
                __UI弹出.超级技能详情:打开(x+20,y+20,self.原有赐福[i].数据)
            end
        end
end
function 超级保存:失去鼠标(x, y)
    self.焦点=nil
    self.焦点1=nil
end

local 保存技能1 = 超级保存:创建红色按钮( "保存技能","保存技能", 123, 190,74,22 ) 
function 保存技能1:左键弹起(x, y)
    请求服务(5021,{类型="保存技能",序列=角色信息.宝宝列表[超级保存.编号].认证码})
    超级保存:置可见(false)
end



local 关闭2 = 超级保存:创建关闭按钮("关闭")
  function 关闭2:左键弹起(x, y)
    超级保存:置可见(false)
  end






  
local 查看技能 = 超级赐福:创建控件("查看技能", 15,60, 320, 285)
function 查看技能:初始化()
  self:创建纹理精灵(function()
                      置窗口背景("保存技能", 0, 0, 320, 285, true):显示(0, 0)
                      文本字体:置颜色(255,255,255,255):取图像("可赐福技能"):显示(120, 35)
                      文本字体:置颜色(255,255,255,255):取图像("不可赐福技能"):显示(115, 160)
                    end
              )
     self.可赐福={}
     self.不可赐福={}
    for i = 1, 14 do
        local lssj = __技能格子:创建()
        lssj:置数据(nil,true)
        self.可赐福[i]=lssj
        local lssj1 = __技能格子:创建()
        lssj1:置数据(nil,true)
        self.不可赐福[i]=lssj1
    end
    self.高级技能={高级毒=true,高级夜战=true,高级反震=true,高级吸血=true,高级连击=true,高级飞行=true,高级隐身=true,
                         高级感知=true,高级再生=true,高级冥思=true,高级驱鬼=true,高级慧根=true,高级必杀=true,高级幸运=true,
                          高级神迹=true,高级招架=true,高级永恒=true,高级敏捷=true,高级偷袭=true,高级强力=true,高级防御=true,
                         高级盾气=true,高级合纵=true,高级魔之心=true,奔雷咒=true,泰山压顶=true,水漫金山=true,高级驱怪=true,
                         地狱烈火=true,高级进击必杀=true,高级进击法暴=true,高级法术连击=true,高级法术暴击=true,
                         高级法术波动=true,壁垒击破=true,高级法术抵抗=true,高级精神集中=true,高级否定信仰=true,
                         高级雷属性吸收=true,高级土属性吸收=true,高级水属性吸收=true,高级火属性吸收=true}
    if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end
end

function 查看技能:打开(编号)
        if not 编号 or 编号==0 then
            return
        end
        self:置可见(not self.是否可见)
        if not self.是否可见 then
            return
        end
        self.焦点=nil
        self.焦点1=nil  
        local 可赐福={}
        local 不可赐福={}
        if 角色信息.宝宝列表[编号] then
            for k, v in pairs(角色信息.宝宝列表[编号].技能) do
                if self.高级技能[v] then
                    table.insert(可赐福,v)
                else
                    table.insert(不可赐福,v)
                end
            end
        end
        for i = 1, 14 do
            self.可赐福[i]:置数据(可赐福[i],true)
            self.不可赐福[i]:置数据(不可赐福[i],true)
        end      
end

function 查看技能:显示(x, y)
    local xx,yy = 0,0
    for i=1,14 do
        if self.可赐福[i] then
            if self.焦点1~=i and self.可赐福[i].焦点 then
                self.可赐福[i].焦点=nil
            end
            self.可赐福[i]:显示(x+18+xx*41,y+60+yy*45)
        end
        if  self.不可赐福[i]  then
            if self.焦点~=i and self.不可赐福[i].焦点 then
                self.不可赐福[i].焦点=nil
            end
            self.不可赐福[i]:显示(x+18+xx*41,y+185+yy*45)
        end

        xx=xx+1
        if xx>=7 then
            xx=0
            yy=yy+1
        end


    end


end

function 查看技能:右键弹起(x, y)
        self:置可见(false)
end

function 查看技能:左键弹起(x, y)
    if __手机 then
        for i=1,14 do
            if  self.可赐福[i].数据 and self.可赐福[i]:检查透明(x, y) then
                __UI弹出.超级技能详情:打开(x+20,y+20,self.可赐福[i].数据)
            end
            if self.不可赐福[i].数据 and self.不可赐福[i]:检查透明(x, y) then
                __UI弹出.技能详情:打开(self.不可赐福[i].数据,x+20,y+20)
            end
        end
    end
end

function 查看技能:获得鼠标(x, y)
    self.焦点=nil
    self.焦点1=nil
        for i=1,14 do
            if  self.可赐福[i].数据 and self.可赐福[i]:检查透明(x, y) then
                self.焦点=i
                self.可赐福[i].焦点=true
                __UI弹出.超级技能详情:打开(x+20,y+20,self.可赐福[i].数据)
            end
            if  self.不可赐福[i].数据 and self.不可赐福[i]:检查透明(x, y) then
                self.焦点1=i
                self.不可赐福[i].焦点=true
                __UI弹出.技能详情:打开(self.不可赐福[i].数据,x+20,y+20)
            end
        end
end
function 查看技能:失去鼠标(x, y)
    self.焦点=nil
    self.焦点1=nil
end


local 关闭2 = 查看技能:创建关闭按钮("关闭")
  function 关闭2:左键弹起(x, y)
    查看技能:置可见(false)
  end









local 关闭 = 超级赐福:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    超级赐福:置可见(false)
  end














