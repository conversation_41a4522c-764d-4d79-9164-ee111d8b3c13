

local 打书内丹 = 窗口层:创建窗口("打书内丹", 0, 10, 560, 450)

local 材料说明={
    洗练="金柳露、超级金柳露",
    炼化="吸附石、圣兽丹、炼妖石",
    打书="高级、特殊、超级、魔兽要诀",
    内丹="召唤兽内丹、高级召唤兽内丹",
}
local 说明事项={
    洗练="#W说明:\n#Y洗练操作会重置召唤兽\n#Y全部资质和技能且等级变成0级\n#Y洗练材料包括:金柳露和超级金柳露",
    炼化="#Y炼化天眼珠需要炼妖石:\n炼化宠物饰品需要圣兽丹\n炼化点化石需要吸附石\n★炼化的宠物失败成功都会消失★",
    打书="#W说明: #Y魔兽要诀能赋予召唤兽某种技能,有概率会消失该召唤兽的一项技能,也有概率直接在原有的技能基础上,增加魔兽要诀的这个技能",
    内丹="#W说明: #Y新学习的内丹技能自动占据空余的内丹格子，如果没有空余内丹格子，则替换掉指定内丹（原内丹消失）。",
}
function 打书内丹:初始化()
    self:创建纹理精灵(function ()
                  置窗口背景("无", 0, 0, 560, 450, true):显示(0, 0)
                  取白色背景(0, 0, 230, 120, true):显示(15, 30)
                  取输入背景(0, 0, 170, 23):显示(15, 155)
                  __res:取资源动画("jszy/fwtb",0xabcd0204,"图像"):平铺(16,450):显示(255,30)
                  文本字体:置颜色(255, 255, 255)
                  文本字体:取图像("攻击资质:"):显示(15,191)
                  文本字体:取图像("法力资质:"):显示(135,191)
                  文本字体:取图像("防御资质:"):显示(15,211)
                  文本字体:取图像("速度资质:"):显示(135,211)
                  文本字体:取图像("体力资质:"):显示(15,231)
                  文本字体:取图像("躲闪资质:"):显示(135,231)
                  文本字体:取图像("寿 命:"):显示(15,251)
                  文本字体:取图像("成 长:"):显示(135,251)
                  文本字体:取图像("五 行:"):显示(15,271)
                  文本字体:取图像("请放入材料:"):显示(295,58)
              end
            )
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        self.类型="打书"
        self.模型格子 = __UI模型格子:创建()
        if __手机 then
            self.关闭:置大小(25,25)
            self.关闭:置坐标(self.宽度-27, 2)
        else
            self.关闭:置大小(16,16)
            self.关闭:置坐标(self.宽度-18, 2)
        end
end






  function 打书内丹:更新(dt)
    self.模型格子:更新(dt)
end

function 打书内丹:显示(x, y)
    if self.图像 then
        self.图像:显示(x, y)
    end
    self.模型格子:显示(x+140, y+140)
end


function 打书内丹:打开(数据,lx)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.选中=nil
    self.类型=lx
    if self[lx] then
        self[lx]:置选中(true)
    end
    self.材料物品=nil
    self.材料网格:置物品()
    self.技能控件:置数据()
    self.道具网格:置数据()
    self:道具刷新(数据)
end


function 打书内丹:清除()
        刷新宝宝窗口(true)
        if self.类型 =="洗练" then
            self.选中=nil
            self.技能控件:置数据()
        end
        self:道具刷新()
end

function 打书内丹:刷新道具(数据)
        self.道具列表 = table.copy(数据)
        if self.材料物品 and self.材料物品.原始编号 then
              if self.道具列表[self.材料物品.原始编号] and self.道具列表[self.材料物品.原始编号].识别码==self.材料物品.识别码  then
                  local 编号 = self.材料物品.原始编号
                  self.材料物品=self.道具列表[self.材料物品.原始编号]
                  self.材料物品.原始编号 = 编号
                  self.道具列表[self.材料物品.原始编号]=nil
              else
                  self.材料物品  = nil
              end
        end
        self.道具网格:置物品(self.道具列表)
        self.材料网格:置物品(self.材料物品)
        self:显示刷新()
        self:禁止物品()
end

  function 打书内丹:道具刷新(道具)
      self.材料物品=nil
      self.选择内丹=nil
      if 道具 then
          self.道具列表 =table.copy(道具)
      else
          self.道具列表 =table.copy(_tp.道具列表)
      end
      self.道具网格:置物品(self.道具列表)
      self.材料网格:置物品(self.材料物品)
      self:显示刷新()
      self:禁止物品()
  end


function 打书内丹:显示刷新()
        self.洗练:置可见(false)
        self.炼化:置可见(false)
        self.打书:置可见(false)
        self.内丹:置可见(false)
        if self.类型=="洗练" or self.类型=="炼化" then
            self.洗练:置可见(true)
            self.炼化:置可见(true)
            self.选择内丹=nil
        else 
            self.打书:置可见(true)
            self.内丹:置可见(true)
        end
        self.模型格子:清空()
        self.说明文本:清空()
        self.说明文本:置文本(说明事项[self.类型])
        self.洗练发送:重置文字(self.类型)
        if self.选中  and 角色信息.宝宝列表[self.选中] then
                self.模型格子:置数据(角色信息.宝宝列表[self.选中], "召唤兽",-10, -10)
                if self.类型 == "内丹" then
                    self.技能控件:置数据(角色信息.宝宝列表[self.选中],self.类型)
                    if self.选择内丹 then
                        self.技能控件.选中=self.选择内丹
                    end
                else
                    self.技能控件:置数据(角色信息.宝宝列表[self.选中])
                end
        end
  self.图像 = self:创建纹理精灵(function()
                    文本字体:置颜色(255,255,255,255):取图像(self.类型):显示((540-文本字体:取宽度(self.类型))//2, 4)
                    if self.选中  and 角色信息.宝宝列表[self.选中] then
                            local 宝宝数据 = 角色信息.宝宝列表[self.选中]
                            文本字体:置颜色(255,255,255,255)
                            文本字体:取描边图像(宝宝数据.攻击资质):显示(80,191)
                            文本字体:取描边图像(宝宝数据.法力资质):显示(200,191)
                            文本字体:取描边图像(宝宝数据.防御资质):显示(80,211)
                            文本字体:取描边图像(宝宝数据.速度资质):显示(200,211)
                            文本字体:取描边图像(宝宝数据.体力资质):显示(80,231)
                            文本字体:取描边图像(宝宝数据.躲闪资质):显示(200,231)
                            if 宝宝数据.种类=="神兽" then
                                文本字体:取图像("★永生★"):显示(60,251)
                            else
                                文本字体:取描边图像(宝宝数据.寿命):显示(60,251)
                            end
                            文本字体:取描边图像(宝宝数据.成长):显示(180,251)
                            文本字体:取描边图像(宝宝数据.五行):显示(60,271)
                            文本字体:置颜色(0,0,0,255):取图像(宝宝数据.名称):显示(20, 158)
                    else
                            文本字体:置颜色(255,255,255,255)
                            文本字体:取描边图像("--"):显示(80,191)
                            文本字体:取描边图像("--"):显示(200,191)
                            文本字体:取描边图像("--"):显示(80,211)
                            文本字体:取描边图像("--"):显示(200,211)
                            文本字体:取描边图像("--"):显示(80,231)
                            文本字体:取描边图像("--"):显示(200,231)
                            文本字体:取描边图像("--"):显示(60,251)
                            文本字体:取描边图像("--"):显示(180,251)
                            文本字体:取描边图像("--"):显示(60,271)
                            文本字体:置颜色(0,0,0,255):取图像("请选择召唤兽->"):显示(20, 158)
                    end
                  文本字体:置颜色(255,255,255,255):取图像(材料说明[self.类型]):显示(350,80)
                end,1,560,300
              )

end

function 打书内丹:禁止物品()
    local 编号={}
    for k,v in pairs(self.道具列表) do
            if self.类型=="洗练" and v.名称~="金柳露" and v.名称~="超级金柳露" then
                    table.insert(编号,k)
            elseif self.类型=="炼化" and v.名称~="吸附石" and v.名称~="圣兽丹" and v.名称~="炼妖石" then
                    table.insert(编号,k)
            elseif self.类型=="打书" and v.名称~="魔兽要诀" and v.名称~="高级魔兽要诀" and v.名称~="超级魔兽要诀" and v.名称~="特殊魔兽要诀" then
                    table.insert(编号,k)
            elseif self.类型=="内丹" and v.名称~="召唤兽内丹" and v.名称~="高级召唤兽内丹" then
                    table.insert(编号,k)
            end
     end
     self.道具网格:置禁止(true,nil,编号)
end






function 打书内丹:放入材料(编号)
        if self.选中 and 角色信息.宝宝列表[self.选中]  then
            if self.类型=="洗练" and self.道具列表[编号].名称~="金柳露" and self.道具列表[编号].名称~="超级金柳露" then
                return
            elseif self.类型=="炼化" and self.道具列表[编号].名称~="吸附石" and self.道具列表[编号].名称~="圣兽丹" and self.道具列表[编号].名称~="炼妖石" then
                return
            elseif self.类型=="打书" and self.道具列表[编号].名称~="魔兽要诀" and self.道具列表[编号].名称~="高级魔兽要诀" and self.道具列表[编号].名称~="超级魔兽要诀" and self.道具列表[编号].名称~="特殊魔兽要诀" then
                return
            elseif self.类型=="内丹" and self.道具列表[编号].名称~="召唤兽内丹" and self.道具列表[编号].名称~="高级召唤兽内丹" then
                return
            else
                if self.材料物品 and self.材料物品.原始编号 then
                        self.道具列表[self.材料物品.原始编号]=table.copy(self.材料物品) 
                end
                self.材料物品=self.道具列表[编号]
                self.材料物品.原始编号 = 编号
                self.道具列表[编号]=nil
                self.道具网格:置物品(self.道具列表)
                self.材料网格:置物品(self.材料物品)
                self:禁止物品()
            end
       end
end


function 打书内丹:清除材料()
    if self.材料物品 and self.材料物品.原始编号 then
            self.道具列表[self.材料物品.原始编号]=table.copy(self.材料物品) 
            self.材料物品=nil
            self.材料网格:置物品(self.材料物品)
            self.道具网格:置物品(self.道具列表)
            self:禁止物品()
    end
end







local 选择 =打书内丹:创建红色按钮("选择", "选择",192,155,55,22) 
function 选择:左键弹起(x, y) 
  打书内丹.选中=nil
            local 列表={}
            for i, v in ipairs(角色信息.宝宝列表) do
                列表[i] = self:创建纹理精灵(function()
                                    __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
                                    local lssj = 取头像(v.模型)
                                    __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
                                    文本字体:置颜色(0,0,0,255)
                                    文本字体:取图像(v.名称):显示(40,4)
                                    文本字体:取图像(v.等级.."级"):显示(40,20) 
                              end,1,170,37
                          )          
            end        
            local 事件 =function (a)
                 if 角色信息.宝宝列表[a] then
                  打书内丹.选中 = a 
                  打书内丹:显示刷新()
                end
            end
            local xx,yy=self:取坐标()
          __UI弹出.弹出列表:打开(列表,取白色背景(0,0,170,200),事件,xx-177,yy+24)

end



local 洗练发送 =打书内丹:创建红色按钮("洗练", "洗练发送",350,103,42,22)   
function 洗练发送:重置文字(txt,jz)
    self:置文字(55,22,txt)
    self:置禁止(false)
    if jz then
        self:置禁止(jz)
    end
end
function 洗练发送:左键弹起(x, y)
      if 打书内丹.材料物品 and 打书内丹.材料物品.原始编号 and _tp.道具列表[打书内丹.材料物品.原始编号]  and 打书内丹.选中 and 角色信息.宝宝列表[打书内丹.选中]  then
          if 打书内丹.类型 =="炼化" then
            请求服务(5010,{序列=打书内丹.材料物品.原始编号,序列1=角色信息.宝宝列表[打书内丹.选中].认证码})
          elseif 打书内丹.类型 =="内丹" then
              请求服务(5008,{序列=打书内丹.材料物品.原始编号,序列1=角色信息.宝宝列表[打书内丹.选中].认证码,序列2=打书内丹.选择内丹 or 0})
          else
             请求服务(5008,{序列=打书内丹.材料物品.原始编号,序列1=角色信息.宝宝列表[打书内丹.选中].认证码})
          end
      end
end





  
local 打书=打书内丹:创建红色单选按钮("打书", "打书",285,32,80,22) 
function 打书:左键弹起(x,y)
       if 打书内丹.类型 ~= "打书" then
        打书内丹.类型 = "打书"
        打书内丹:道具刷新()
       end
end
  
local 内丹=打书内丹:创建红色单选按钮("内丹", "内丹",370,32,80,22) 
function 内丹:左键弹起(x,y)
        if 打书内丹.类型 ~= "内丹" then
          打书内丹.类型 = "内丹"
          打书内丹:道具刷新()
        end
end
  
local 洗练=打书内丹:创建红色单选按钮("洗练", "洗练",285,32,80,22) 
function 洗练:左键弹起(x,y)
        if 打书内丹.类型 ~= "洗练" then
          打书内丹.类型 = "洗练"
          打书内丹:道具刷新()
        end
end
  
local 炼化=打书内丹:创建红色单选按钮("炼化", "炼化",370,32,80,22) 
function 炼化:左键弹起(x,y)
        if 打书内丹.类型 ~= "炼化" then
          打书内丹.类型 = "炼化"
          打书内丹:道具刷新()
        end
end




local 道具网格=打书内丹:创建背包网格("道具网格",285,130)

function 道具网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
              __UI弹出.道具提示:打开(物品.物品,x+20,y+20)
          end
end
function 道具网格:左键弹起(x,y,i)
        local 物品=self:选中物品() 
        if  物品 and 物品.物品 then
            if __手机 then
                __UI弹出.道具提示:打开(物品.物品,x+20,y+20,道具网格,"放入",1)
            else
                self:放入(1)
            end
        end

end
function 道具网格:放入(编号)
        print(self:选中())
      if 编号 and 编号~=0 then
            打书内丹:放入材料(self:选中())
      end
end






local 材料网格 = 打书内丹:创建网格("材料网格", 295, 75, 50, 50)
function 材料网格:初始化()
    self:创建格子(50, 50, 0, 45, 1, 1)
end
function 材料网格:左键弹起(x, y, a)
  if 打书内丹.材料物品 and 打书内丹.材料物品.原始编号 and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
        if __手机 then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,材料网格,"取消",1)
        else
            self:取消(1)
        end
  end
end

function 材料网格:取消(编号)
    if 编号 and 编号~=0 then
          打书内丹:清除材料()
    end
end

function 材料网格:获得鼠标(x, y, a)
        self.子控件[a]._spr.焦点=true
        if 打书内丹.材料物品 and 打书内丹.材料物品.原始编号 and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
        end
end
function 材料网格:失去鼠标(x, y)
    self.子控件[1]._spr.焦点=nil
 
end

function 材料网格:置物品(数据)
        local lssj = __物品格子:创建()
        if 数据 then
            lssj:置物品(数据,50,50,"数量",true,true)
        else
            lssj:置物品(nil,50,50,nil,true)
        end
        self.子控件[1]:置精灵(lssj)
end


local  技能控件=打书内丹:创建技能内丹控件("技能控件",25,300)
技能控件.进阶:置禁止(true)
技能控件.超级图标=nil

function 技能控件:回调左键弹起(x, y)
  打书内丹.选择内丹=nil 
        local 选中 = self:取选中()
        if 选中~=0 and self.状态=="内丹" then
          打书内丹.选择内丹=选中
        end
end




local  说明文本=  打书内丹:创建文本("说明文本",285,375,260,70)
function 说明文本:初始化()
        self:置文字(文本字体)
        self.行间距=3
end 


local 关闭 = 打书内丹:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    打书内丹:置可见(false)
  end
