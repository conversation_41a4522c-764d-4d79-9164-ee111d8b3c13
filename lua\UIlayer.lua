--[[
LastEditTime: 2025-02-08 20:53:02
--]]
--[[
LastEditTime: 2024-09-14 08:59:55
--]]

调试模式=gge.isdebug or false
--调试模式= false
__关注摊位={}
-- 配置信息 (原授权信息)
__热更地址="************"
__热更端口="18886"
__主体名称="ggelua"
__会员显示= true
__区名列表={[1]="公测区"}
__分区列表={
    [1]={
        [1]={名称="如梦",IP="************",端口="61888"},
    },
}
_版本号 ="1.222"
__收到流程=0
__发送流程=0
__分辨率设置={
    {800,600},
    {1024,768},
    {1280,720},
    {1280,768},
    {1280,800},
    
}
__频道表 = {
    xt = 900,
    sj = 901,
    dq = 902,
    sl = 903,
    gm = 904,
    cw = 905,
    bp = 906,
    dw = 907,
    hd = 908,
    fq = 909,
    dt = 910, --大唐
    hs = 911,--化生
    lg = 912,--龙宫
    tg = 913,--天宫
    pts = 914, --普陀
    fc = 915, --方寸
    st = 916, --狮驼
    mw = 917,--魔王
    ne = 918, --女儿
    ps = 919, --盘丝
    wz = 920, --五庄
    df = 921, --地府
    lb = 922,--凌波
    wd = 923, --无底
    sm = 924, --神木
}
__连接信息 = {停止连接 = true,服务状态 = false}--那个是WAV我怕解压出问题 等下你下载看看 
__res = require("资源")()
__CLT = require("网络/网络处理")
__Http = require("网络/网站处理")
__实时更新 = require("网络/实时更新")
-- __PC热更新 = require("网络/PC热更新最简版") -- 暂时禁用，使用统一的Http更新逻辑
__CLT:重载()
__Http:重载()
__实时更新:重载()
__res:公共资源("shape/ui/zd1.png")
__res:公共资源("shape/ui/zd2.png")
__res:公共资源("shape/ui/zd3.png")
__res:公共资源("shape/ui/zd4.png")
格子字体 = require("SDl.文字")("assets/hkyt.ttc", 12, true,0)
文本字体 = require("SDl.文字")("assets/hkyt.ttc", 14, true,0)
选项字体 = require("SDl.文字")("assets/hkyt.ttc", 15, true,0)
标题字体 = require("SDl.文字")("assets/hkyt.ttc", 16, true,0)
说明字体 =  require("SDl.文字")("assets/hkyt.ttc", 18, true,0)
道具字体 = require("SDl.文字")("assets/hkyt.ttc", 22, true,0)
冷却字体 = require("SDl.文字")("assets/hkyt.ttc", 32, true,0)
宋体文本 = require("SDl.文字")("assets/simsun.ttc", 14, true,0)
宋体标题 = require("SDl.文字")("assets/simsun.ttc", 16, true,0)
排行字体=require("SDl.文字")("assets/hyf2gjm.ttf", 14, true,0)
排行标题=require("SDl.文字")("assets/hyf2gjm.ttf", 16, true,0)
任务字体 = require("SDl.文字")("assets/ztfg.ttf", 14, true,0)
任务字体1 = require("SDl.文字")("assets/ztfg.ttf", 16, true,0)
九黎连击 = require("SDl.文字")("assets/ljzt.ttf", 46, true,0) 

require("数据/模型数据")
require("数据/特效数据")
require("数据/技能数据")
require("数据/物品数据")
require("数据/音效数据")
require("数据/场景数据")
require("数据/场景特效")
require("数据/符石数据")
require("数据/自定义数据")
require("数据/内丹数据")
require("数据/特性数据")
require("数据/图标数据")
_tp = require("对象/主控")()
require("界面/控件层/通用背景")
require("界面/自定义")

-- 加载摇杆控件（仅移动端）
if __手机 then
    require("界面/控件层/摇杆控件")
end
__NPC列表={}
__传送数据={}
__UI弹出 = {}
__UI界面 = require('界面')
__Greedy = require("Greedy")()
__战斗主控 = require("战斗主控")()
__战斗单位 = require("战斗单位")
__战斗动画 = require("战斗动画")
__物品格子 = require("物品格子")
__家具格子 = require("家具格子")
__技能格子 = require("技能格子")
__头像格子 = require("头像格子")
__队伍格子 = require("队伍格子")
__玩家类 = require("玩家")
__假人类 =require("NPC")
__UI模型格子 = require("UI模型格子")
__角色选择格子 = require("角色选择格子")
-- 统一的启动流程，PC端和移动端都使用相同的版本检查逻辑
    __UI界面.登录层:置可见(true)
    __UI界面.战斗层:置可见(true)
    __UI界面.窗口层:置可见(true)
    _tp:播放音乐(1070)

-- 都调用原有的版本检查逻辑
    __Http:效验版本号() 



场景取假人表 = function(地图ID)
    if not 地图ID  then
      return {}
    elseif  not __NPC列表[地图ID] then
      return {}

    else
        return __NPC列表[地图ID]
    end
end


function 场景取名称(地图ID)---  +40
    local 传送名称 = {}
    if 地图ID == 1003 then--桃源村
      传送名称[1] = {x=13,y=128,名称="0一9级",等级字体=true,红字体=false}
    elseif 地图ID == 1126 then--东海岩洞
      传送名称[1] = {x=5,y=103,名称="0一9级",等级字体=true,红字体=false}
    --  传送名称[2] = {x=185,y=35,名称="魁拔墓",等级字体=false,红字体=true}
    elseif 地图ID == 1508 then--沉船
      传送名称[1] = {x=26,y=10,名称="2一12级",等级字体=true,红字体=false}
      传送名称[2] = {x=150,y=160,名称="沉船内室",等级字体=false,红字体=false}
    elseif 地图ID == 1506 then--东海湾
      传送名称[1] = {x=100,y=102,名称="2一12级",等级字体=true,红字体=false}
      传送名称[2] = {x=100,y=120,名称="2一12级",等级字体=true,红字体=false}
    elseif 地图ID == 1501 then--建邺城
      传送名称[1] = {x=27,y=119,名称="6一16级",等级字体=true,红字体=false}
      传送名称[2] = {x=252,y=120,名称="0一9级",等级字体=true,红字体=false}
      传送名称[3] = {x=206,y=-4,名称="0一9级",等级字体=true,红字体=false}
    elseif 地图ID == 1193 then--江南野外
      传送名称[1] = {x=104,y=9,名称="南岭山",等级字体=false,红字体=false}
    elseif 地图ID == 1876 then--南岭山
      传送名称[1] = {x=180,y=100,名称="江南野外",等级字体=false,红字体=false}
      传送名称[2] = {x=1,y=135,名称="四方城",等级字体=false,红字体=false}
    elseif 地图ID == 16050 then--天鸣洞天
      传送名称[1] = {x=1,y=130,名称="潮音洞",等级字体=false,红字体=false}
    elseif 地图ID == 1135 then--方寸山
      传送名称[1] = {x=122,y=120,名称="观星台",等级字体=false,红字体=false}
    elseif 地图ID == 1237  then--四方城
      传送名称[1] = {x=250,y=10,名称="南岭山",等级字体=false,红字体=false}
    elseif 地图ID == 1208  then--朱紫国
      传送名称[1] = {x=76,y=105,名称="仙源洞天",等级字体=false,红字体=false}
    elseif 地图ID == 2008 then--九黎城
      传送名称[1] = {x=160  ,y=228,名称="大唐境外",等级字体=false,红字体=false}
    end
    return 传送名称
end




