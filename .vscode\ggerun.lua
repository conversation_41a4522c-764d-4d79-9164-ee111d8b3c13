
print('ggerun',arg[1])
if arg[1]=='Android' or arg[1]=='Windows'  then
--if arg[1]=='android' then
    编译目录('ggelua',true)
    编译目录('./lua',true)
    -- for path,rel in 遍历目录('./data') do
    --     local hash = gge.hash(path:sub(#rel+6))
    --     if 复制文件(path, string.format('./assets/%08x', hash), false) then
    --         print(string.format('assets/%08x', hash), path)
    --     end
    -- end
    写出脚本('./assets/ggelua')
end