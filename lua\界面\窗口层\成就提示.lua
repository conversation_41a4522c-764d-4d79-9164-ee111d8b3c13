
local 成就提示 = 窗口层:创建窗口("成就提示")
function 成就提示:初始化()
        local tcp=__res:取资源动画("dlzy", 0xAAAB0013,"精灵"):置中心(0,0)
        self:置精灵(tcp)
        self:置宽高(tcp.宽度,tcp.高度)
        self:置坐标((引擎.宽度 - self.宽度) // 2, 引擎.高度 - 160)
        self.表情=__res:取资源动画("dlzy", 0xAAAB0015,"精灵")
        self.动画=__res:取资源动画("dlzy", 0xAAAB0016,"动画")
        self.表情背景=__res:取资源动画("dlzy", 0xAAAB0014,"精灵")
        self.可初始化=true
 
end
local 关闭控件=成就提示:创建控件("关闭控件")
function 关闭控件:初始化()
        self:置坐标(0,0)
        self:置宽高(成就提示.宽度,成就提示.高度)
end
function 关闭控件:左键弹起() 
     
     if __手机  then
        成就提示:置可见(false)
     end 
end
function 关闭控件:右键弹起() 
      成就提示:置可见(false)
end



function 成就提示:更新(dt) 
          if self.出现时间 and os.time()>self.出现时间  then
              self:置可见(false)
          end
          self.动画:更新(dt)  
end
function 成就提示:显示(x, y)  
        self.表情背景:显示(x+20, y+32)      
        self.表情:显示(x+20, y+32)   
        self.动画:显示(x-15, y)  
        if self.字符  then
          self.字符:显示(x+110, y+35)  
        end
        if self.字符1  then
          self.字符1:显示(x+80, y+60)  
        end

       


end
function 成就提示:打开(内容)
    self:置可见(true)
    self.字符=nil
    self.字符1=nil
    self:刷新(内容)
end
function 成就提示:刷新(内容)
      self.出现时间=os.time()+5
      self.字符=标题字体:置颜色(0,0,0,255):取精灵(内容.内容)
      self.字符1=文本字体:置颜色(0,0,0,255):取精灵(内容.内容1)
      
end



