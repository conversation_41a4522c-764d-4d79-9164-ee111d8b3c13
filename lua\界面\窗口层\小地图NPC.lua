--[[
LastEditTime: 2024-09-17 22:54:07
--]]

local 小地图NPC = 窗口层:创建窗口("小地图NPC", 0,0, 425, 333)
local 筛选表 = {"全部","普通","商业","特殊","传送","任务","出口"}
function 小地图NPC:初始化()
 self.背景= self:创建纹理精灵(function()
        说明字体:置颜色(255,255,255,255)
        蓝白标题背景(160,250,true):显示(15,60)
        蓝白标题背景(190,250,true):显示(205,60)
        说明字体:取描边图像("NPC列表"):显示(70,62)
        说明字体:取描边图像("NPC详细"):显示(265,62)
  end,1)
 self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
 self.可初始化=true 
 if __手机 then
  self.关闭:置大小(25,25)
  self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end

function 小地图NPC:显示(x,y)
         if self.背景  then
              self.背景:显示(x,y)
         end
end

function 小地图NPC:打开(列表,标题)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
     return
  end
  self.列表= 列表 or {}
  if 标题 then
        self:置精灵(置窗口背景(标题, 0, 0, 425, 333))
  end
  self.NPC列表:置数据(self.列表)
  self.选中=nil


 
end






local NPC列表 = 小地图NPC:创建列表("NPC列表", 20, 90, 150, 210)
function NPC列表:初始化()
    self:置文字(文本字体)
    self:置颜色(0, 0, 0, 255)
    self.行间距 = 2
end
function NPC列表:置数据(data)
        小地图NPC.说明文本:清空()
        小地图NPC.选中=nil
        self:清空()

        if data and data[1] and data[1].名称 then
            for i, v in ipairs(data) do
                self:添加(v.名称)
              
            end
        end



end


function NPC列表:左键弹起(x, y, i)
        小地图NPC.说明文本:清空()
        小地图NPC.选中=nil
        if 小地图NPC.列表 and 小地图NPC.列表[i] then
            local  添加内容="#L/◆ "..小地图NPC.列表[i].名称.. "\n#R◆X坐标: "
            local 坐标x,坐标y= 0,0
            if 小地图NPC.列表[i].X and 小地图NPC.列表[i].Y then
                    坐标x=小地图NPC.列表[i].X
                    坐标y=小地图NPC.列表[i].Y
            elseif 小地图NPC.列表[i].x and 小地图NPC.列表[i].y then
                    坐标x=小地图NPC.列表[i].x
                    坐标y=小地图NPC.列表[i].y
            end
            添加内容=添加内容..坐标x.."\n#Z/◆Y坐标: "..坐标y.."\n#N/◆NPC介绍: ".."暂无!"
            小地图NPC.说明文本:置文本(添加内容)
            小地图NPC.选中=i
        end
end

local 说明文本 = 小地图NPC:创建文本("说明文本", 210, 90, 180, 210)
function 说明文本:初始化()
      self:置文字(文本字体)
      self.行间距=5
end

local 自动寻路=小地图NPC:创建红色按钮("自动寻路", "自动寻路", 330, 33,74,22)
function 自动寻路:左键弹起(x, y)
          if 小地图NPC.选中 and 小地图NPC.列表 and 小地图NPC.列表[小地图NPC.选中] then
                local 终点
                if 小地图NPC.列表[小地图NPC.选中].X and 小地图NPC.列表[小地图NPC.选中].Y then
                    终点 = require("GGE.坐标")(小地图NPC.列表[小地图NPC.选中].X * 20, 小地图NPC.列表[小地图NPC.选中].Y * 20)
                elseif 小地图NPC.列表[小地图NPC.选中].x and 小地图NPC.列表[小地图NPC.选中].y then
                    终点= require("GGE.坐标")(小地图NPC.列表[小地图NPC.选中].x * 20, 小地图NPC.列表[小地图NPC.选中].y * 20)
                end
                if 终点 and __主显.主角 and __主显.主角:是否可移动() then
                      窗口层.小地图.终点 = 终点
                      窗口层.小地图:路径设置(终点)
                      请求服务(1001,{x=终点.x//20,y=终点.y//20})
                end


          end
end





local 关闭 = 小地图NPC:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    小地图NPC:置可见(false)
    
  end





