local 宠物 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "宠物", 200 + abbr.py.x, 74 + abbr.py.y, 490, 380)
local lsb = {
  "名称",
  "等级",
  "耐力",
  "经验"
}
local lsb2 = {
  "洗炼",
  "炼化",
  "合宠",
  "打书",
  "内丹",
  "牧场"
}
function 宠物:初始化()
  local nsf = require("SDL.图像")(482, 378)
  if nsf["渲染开始"](nsf) then
    置窗口背景("宠物", 0, 12, 482, 363, true)["显示"](置窗口背景("宠物", 0, 12, 482, 363, true), 0, 0)
    __res:getPNGCC(3, 375, 388, 145, 149)["显示"](__res:getPNGCC(3, 375, 388, 145, 149), 30, 72)
    local lssj = 取输入背景(0, 0, 152, 23)
    字体18["置颜色"](字体18, 255, 255, 255)
    for i = 1, #lsb do
      lssj["显示"](lssj, 242, 72 + (i - 1) * 47)
      字体18["取图像"](字体18, lsb[i])["显示"](字体18["取图像"](字体18, lsb[i]), 194, 74 + (i - 1) * 47)
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.模型格子 = __UI模型格子["创建"]()
end
function 宠物:打开()
  self:置可见(true)
  local nsf = require("SDL.图像")(400, 240)
  if nsf["渲染开始"](nsf) then
    for i = 1, #lsb do
      字体18["置颜色"](字体18, 39, 53, 81)
      if "耐力" == lsb[i] then
        字体18["取图像"](字体18, 角色信息["宠物"][lsb[i]] .. "/" .. 角色信息["宠物"]["最大耐力"])["显示"](字体18["取图像"](字体18, 角色信息["宠物"][lsb[i]] .. "/" .. 角色信息["宠物"]["最大耐力"]), 252, 74 + (i - 1) * 47)
      elseif "经验" == lsb[i] then
        字体18["取图像"](字体18, 角色信息["宠物"][lsb[i]] .. "/" .. 角色信息["宠物"]["最大经验"])["显示"](字体18["取图像"](字体18, 角色信息["宠物"][lsb[i]] .. "/" .. 角色信息["宠物"]["最大经验"]), 252, 74 + (i - 1) * 47)
      else
        字体18["取图像"](字体18, 角色信息["宠物"][lsb[i]])["显示"](字体18["取图像"](字体18, 角色信息["宠物"][lsb[i]]), 252, 74 + (i - 1) * 47)
      end
    end
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  宠物["模型格子"]["置数据"](宠物["模型格子"], 角色信息["宠物"]["模型"], "宠物", 100, 188)
end
local 关闭 = 宠物["创建我的按钮"](宠物, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 440, 0)
function 关闭:左键弹起(x, y, msg)
  宠物["置可见"](宠物, false)
end
local pyx = 0
local pyy = 0
for i = 1, #lsb2 do
  if i > 3 then
    pyx = -450
    pyy = 60
  end
  local 临时函数 = 宠物["创建我的按钮"](宠物, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), lsb2[i] .. "按钮", 30 + (i - 1) * 150 + pyx, 255 + pyy, lsb2[i])
 function  临时函数:左键弹起(x, y, msg)
  发送数据(5007, {
    ["类型"] = lsb2[i]
  })
  end
end
