
local 召唤合宠 = 窗口层:创建窗口("召唤合宠", 0, 0, 570, 470)
local lsb = {
  "攻击资质:",
  "防御资质:",
  "体力资质:",
  "法力资质:",
  "速度资质:",
  "躲闪资质:",
  "寿      命:",
  "五      行:",
  "成      长:"
}
local lsb2 = {
  "攻击资质",
  "防御资质",
  "体力资质",
  "法力资质",
  "速度资质",
  "躲闪资质",
  "寿命",
  "五行",
  "成长"
}
function 召唤合宠:初始化()
  self:创建纹理精灵(function()
              置窗口背景("合 宠", 0, 0, 570, 470,true):显示(0, 0)
              __res:取资源动画("dlzy",0xB17505CF,"图像"):显示(258, 60)
              for i = 1, 2 do
                  取白色背景(0, 0, 210, 120, true):显示(15+(i-1)*330, 35)
                  取输入背景(0, 0, 210, 23):显示(15+(i-1)*330, 165)
              end
              local xx = 0
              local yy = 0
              for i=1, 9 do
                    文本字体:置颜色(255,255,255,255)
                    文本字体:取图像(lsb[i]):显示(15 + xx*110, 195 + yy* 25)
                    文本字体:取图像(lsb[i]):显示(345 + xx*110, 195 + yy* 25)
                    xx =xx +1
                    if xx>=2 then
                        xx=0
                        yy=yy+1
                    end
              end
              文本字体:取图像("合宠材料"):显示(257, 40)
              __res:取资源动画("jszy/fwtb",0xabcd0204,"图像"):置区域(0,0,16,300):显示(266,160)
              __res:取资源动画("jszy/fwtb",0xabcd0204,"图像"):置区域(0,0,16,300):显示(285,160)

    end
  )
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  self.选中 = nil
  self.选中1 = nil
  self.模型格子2=__UI模型格子:创建()
  self.模型格子1=__UI模型格子:创建()
  if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
  else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
  end
end





function 召唤合宠:更新(dt)
      if self.模型格子2 then
          self.模型格子2:更新(dt)
      end
      if self.模型格子1 then
          self.模型格子1:更新(dt)
      end
end

function 召唤合宠:显示(x,y)
      if self.模型格子1 then
          self.模型格子1:显示(x,y)
      end
      if self.模型格子2 then
          self.模型格子2:显示(x,y)
      end
      if self.图片 then
          self.图片:显示(x,y)
      end
end


function 召唤合宠:打开(数据)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.选中1=nil
    self.选中2 = nil
    self.宝宝数据=table.copy(角色信息.宝宝列表)
    self:显示刷新()
end




function 召唤合宠:清除()
      刷新宝宝窗口(true)
      self.选中1=nil
      self.选中2 = nil
      self.宝宝数据=table.copy(角色信息.宝宝列表)
      self:显示刷新()
end




function 召唤合宠:显示刷新()
        self.模型格子1:清空()
        self.模型格子2:清空()
        self.图片=self:创建纹理精灵(function()
                        for i = 1, 2 do
                            if self["选中"..i] then
                                文本字体:置颜色(0,0,0,255):取图像(self.宝宝数据[self["选中"..i]].名称):显示(20+(i-1)*330, 168)
                                self["模型格子"..i]:置数据(self.宝宝数据[self["选中"..i]],"召唤兽",120+(i-1)*330, 140)
                                self["技能控件"..i]:置数据(self.宝宝数据[self["选中"..i]])
                            else
                                文本字体:置颜色(0,0,0,255):取图像("请选择召唤兽->"):显示(20+(i-1)*330, 168)
                                self["技能控件"..i]:置数据()
                            end
                        end
                        local xx = 0
                        local yy = 0
                        for i=1, 9 do
                              文本字体:置颜色(255,255,255,255)
                              if self.选中1 and self.宝宝数据[self.选中1] then
                                  if  self.宝宝数据[self.选中1].种类=="神兽" and lsb2[i]=="寿命" then
                                      文本字体:取投影图像("永生"):显示(80 + xx*110, 195 + yy* 25)
                                  else
                                      文本字体:取投影图像(self.宝宝数据[self.选中1][lsb2[i]]):显示(80 + xx*110, 195 + yy* 25)
                                  end
                              else
                                    文本字体:取投影图像("-"):显示(80 + xx*110, 195 + yy* 25)
                              end
                              if self.选中2 and self.宝宝数据[self.选中2] then
                                  if  self.宝宝数据[self.选中2].种类=="神兽" and lsb2[i]=="寿命" then
                                        文本字体:取投影图像("永生"):显示(410 + xx*110, 195 + yy* 25)
                                  else
                                        文本字体:取投影图像(self.宝宝数据[self.选中2][lsb2[i]]):显示(410 + xx*110, 195 + yy* 25)
                                  end
                              else
                                    文本字体:取投影图像("-"):显示(410 + xx*110, 195 + yy* 25)
                              end
                              xx =xx +1
                              if xx>=2 then
                                  xx=0
                                  yy=yy+1
                              end
                          end 
                  end,1
                )

end
local 选择设置={"选中1","选中2"}

for i, v in ipairs(选择设置) do

  local 临时按钮=召唤合宠:创建按钮(v.."按钮",203+(i-1)*330,166)
  function 临时按钮:初始化()
      self:创建按钮精灵(__res:取资源动画("jszy/xjiem",0x00000050),1)
  end
  function 临时按钮:左键弹起(x, y)
          local 列表={}
          for k, z in ipairs(角色信息.宝宝列表) do
              列表[k] = self:创建纹理精灵(function()
                                  __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
                                  local lssj = 取头像(z.模型)
                                  __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
                                  文本字体:置颜色(0,0,0,255)
                                  文本字体:取图像(z.名称):显示(40,4)
                                  文本字体:取图像(z.等级.."级"):显示(40,20) 
                            end,1,190,37
                        )    
          end     
          local 事件 =function (a)
                  if i==1 and 召唤合宠.选中2 and 召唤合宠.选中2==a then
                  elseif  i==2 and 召唤合宠.选中1 and 召唤合宠.选中1==a then
                  else
                    召唤合宠[v] = a 
                    召唤合宠:显示刷新()
                  end
            end
            local xx,yy=self:取坐标()
            __UI弹出.弹出列表:打开(列表,取白色背景(0,0,208,200),事件,xx-188,yy+24)
  end
end



local 技能控件1=召唤合宠:创建技能内丹控件("技能控件1",25,320)
技能控件1.超级图标=nil
local 技能控件2=召唤合宠:创建技能内丹控件("技能控件2",355,320)
技能控件2.超级图标=nil

local 合成按钮 = 召唤合宠:创建红色按钮("合成","合成",260,120,50,22)
function 合成按钮:左键弹起(x, y, msg)
      if 召唤合宠.选中1 and 召唤合宠.选中2  and 召唤合宠.宝宝数据[召唤合宠.选中1] and 召唤合宠.宝宝数据[召唤合宠.选中2] then
            请求服务(5009, {
                  序列 = 召唤合宠.宝宝数据[召唤合宠.选中1].认证码,
                  序列1= 召唤合宠.宝宝数据[召唤合宠.选中2].认证码
            })
      end
end



local 关闭 = 召唤合宠:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  召唤合宠:置可见(false)
end

