
local 主显层 = class("主显层")




function 主显层:初始化()
    self.屏幕坐标 = 生成XY()
    self.地图id = 0
    self.玩家 = {}
    self.场景人物 = {}
end

function 主显层:加载(id)
    self.地图id =id
    if not self.地图 then
      self.地图=require("地图/地图")(取地图id(self.地图id))
    else
      self.地图:加载(取地图id(self.地图id))
    end
    _tp:播放音乐(取地图id(self.地图id))
    if not self.主角 then
        self.主角 = require("主角")(角色信息)
    end
    self.屏幕坐标 = 角色信息.坐标:取地图偏移(self.地图.宽度,self.地图.高度)
    for k, v in pairs(self.玩家) do
      self.玩家[k] = nil
    end
    for k, v in pairs(self.场景人物) do
      self.场景人物[k] = nil
    end
    self.玩家 = {}
    self.场景人物 = {}
    table.insert(self.场景人物, self.主角)
    local 地图等级 = {}
    地图等级[1], 地图等级[2] = 取场景等级(角色信息.地图数据.编号)
    if 地图等级[1] and 地图等级[2] then
        self.场景最低等级 = 地图等级[1]
        self.场景最高等级 = 地图等级[2]
        __UI弹出.提示框:打开("#Y本场景等级为" ..地图等级[1] .. "-" .. 地图等级[2] .. "级", "xt")
    else
        self.场景最低等级 = nil
        self.场景最高等级 = nil
    end
   -- test1()
    collectgarbage()
end


function 主显层:添加玩家(内容)
      if not 内容 or not 内容.id then return end
      self.玩家[内容.id]= __玩家类(内容)
      table.insert(self.场景人物, self.玩家[内容.id])
end


function 主显层:删除玩家(角色ID)
        if not 角色ID then return end
        self.玩家[角色ID]=nil
        for i=#self.场景人物,1,-1 do
            if self.场景人物[i] and ggetype(self.场景人物[i])=="玩家" and  self.场景人物[i].玩家ID == 角色ID then
              table.remove(self.场景人物, i)
              break
            end
        end
end


 function 主显层:设置传送(内容)
        if not 内容 then return end
        for k,v in pairs(内容) do
            if not v.编号 then
                v.编号 = k
            end
          self.地图:添加传送圈(v)
        end
        if self.地图id > 100000 then
          self:加载房屋特效()
        end
        self:加载场景特效()
        if not __传送数据[self.地图id] then
            __传送数据[self.地图id] =内容
        end
end





function 主显层:清除场景传送()
          self.地图.传送圈={}
end


function 主显层:设置假人(内容)
        if not 内容 then return end
        for i, v in pairs(内容) do
            if not v.编号 then
                v.编号 = i
            end
            table.insert(self.场景人物, __假人类(v))
        end
        self:更新假人头顶图标()
        if not __NPC列表[self.地图id] then
            __NPC列表[self.地图id] =内容
        end
end

function 主显层:添加单位(内容)
        if 内容 and 内容.id and 内容.编号 then
            table.insert(self.场景人物, __假人类(内容))
        end
end


function 主显层:删除单位(编号,序列)
        for i=#self.场景人物,1,-1 do
            if self.场景人物[i].编号 == 编号 and self.场景人物[i].id == 序列 then
              table.remove(self.场景人物, i)
              break
            end
        end
end

function 主显层:更改单位(数据)
        for i=#self.场景人物,1,-1 do
            if self.场景人物[i].编号 == 数据.编号 and self.场景人物[i].名称 == 数据.名称 then
                table.remove(self.场景人物, i)
                break
            end
        end
        if 数据.变异 and 数据.变异 and __染色信息[数据.模型] then
            数据.染色方案 = __染色信息[数据.模型].id
            数据.染色组 = __染色信息[数据.模型].方案
        end
        table.insert(self.场景人物, __假人类(数据))
end



function 主显层:加载场景特效()
        local txb = 取传特效表(self.地图id)
        if self.地图id > 100000 then
            if _tp.房屋数据.庭院ID == self.地图id then
                  txb = 取传特效表(_tp.房屋数据.庭院地图)
            elseif _tp.房屋数据.房屋ID == self.地图id then
                  txb = 取传特效表(_tp.房屋数据.房屋地图)
            elseif _tp.房屋数据.阁楼ID == self.地图id then
                txb = 取传特效表(_tp.房屋数据.阁楼地图)
            elseif _tp.房屋数据.牧场ID == self.地图id then
                txb = 取传特效表(_tp.房屋数据.牧场地图)
            end
        end
        if txb then
            if self.地图id > 100000 or __res.配置.地图特效==1  then
                for i, v in pairs(txb) do
                    v.id = i
                    self.地图:添加特效(v)
                end
            end
        end
        
        
end

function 主显层:清除场景特效()
         if self.地图id > 100000 then return end 
          self.地图.特效={}
end

function 主显层:加载房屋特效()
      if self.地图id > 100000 then
          local jj
          if _tp.房屋数据.庭院ID == self.地图id then
              jj =_tp.房屋数据.庭院装饰
          elseif _tp.房屋数据.房屋ID == self.地图id then
              jj = _tp.房屋数据.室内装饰
          elseif _tp.房屋数据.阁楼ID == self.地图id then
              jj = _tp.房屋数据.阁楼装饰
          elseif _tp.房屋数据.牧场ID == self.地图id then
              jj = _tp.房屋数据.牧场装饰
          end
          if jj then
              for i, v in pairs(jj) do
                  if not v.编号 then
                     v.编号 = i
                  end
                  local zx = 取房屋特效(v.名称..v.方向)
                  if zx.切换 and zx.资源 then
                      v.切换=zx.切换 
                      v.资源=zx.资源
                      self.地图:添加家具(v)
                  end
              end
          end
      end
end

function 主显层:家具旋转特效(数据)
        self.地图:删除家具(数据.编号)
        local zx = 取房屋特效(数据.名称..数据.方向)
        if zx.切换 and zx.资源 then
            数据.切换=zx.切换 
            数据.资源=zx.资源
            self.地图:添加家具(数据)
        end
end



function 主显层:清除房屋特效()
        self.地图.家具={}
      
end




function 主显层:房屋特效(数据)
      if self.地图id > 100000 and 数据 then
          if _tp.房屋数据.庭院ID == self.地图id then
              if not 数据.编号 then
                数据.编号=#_tp.房屋数据.庭院装饰+1
              end
              _tp.房屋数据.庭院装饰[数据.编号] = 数据
          elseif _tp.房屋数据.房屋ID == self.地图id then
                  if not 数据.编号 then
                      数据.编号=#_tp.房屋数据.室内装饰+1
                  end
                  _tp.房屋数据.室内装饰[数据.编号] = 数据
          elseif _tp.房屋数据.阁楼ID == self.地图id then
              if not 数据.编号 then
                  数据.编号=#_tp.房屋数据.阁楼装饰+1
              end
              _tp.房屋数据.阁楼装饰[数据.编号] = 数据
          elseif _tp.房屋数据.牧场ID == self.地图id then
                  if not 数据.编号 then
                      数据.编号=#_tp.房屋数据.牧场装饰+1
                  end
                  _tp.房屋数据.牧场装饰[数据.编号] = 数据
          end
          if 数据.编号 then
                local xxxj =__家具格子.创建() 
                xxxj:置数据(数据.名称,数据.方向,数据.编号)
                __UI界面.鼠标层.附加=xxxj
          end
      end
end







function 主显层:更新(dt, x, y)
    if not _tp.战斗中 then
        self.地图:更新(dt,self.主角.xy:取地图位置(self.地图.块宽度, self.地图.块高度))
    else
        __战斗主控:更新( dt * 1.2, x, y)
    end
end


function 主显层:消息事件(msg)
          self.地图:消息事件(msg)
          for k, v in pairs(self.场景人物) do
              if v.消息事件 then
                  v:消息事件(msg)
              end
          end
end


function 主显层:显示(x, y)
    self.地图:显示(self.屏幕坐标)
    if _tp.战斗中 then
        __战斗主控:显示(x, y)
    end

end


function 主显层:更新假人头顶图标()
    for k, v in pairs(self.场景人物) do
        if v.名称 == "商人的鬼魂" or v.名称 == "白鹿精"
            or v.名称 == "酒肉和尚" or v.名称 == "执法天兵"
            or v.名称 == "白琉璃"or v.名称 == "幽冥鬼"
            or v.名称 == "刘洪" or v.名称 == "野猪" or v.名称 == "蟹将军" then
                v:置任战(true)
        elseif   v.名称 == "新手接待师"  or v.名称 == "杜少海" or v.名称 == "皇宫护卫" or v.名称 == "钟馗"  or v.名称 == "赵捕头" then
            v:置任务(true)
		    end
    end
end

return 主显层
