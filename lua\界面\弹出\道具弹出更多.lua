--[[
Author: GGELUA
Date: 2024-09-04 16:18:56
Last Modified by: GGELUA
Last Modified time: 2024-10-13 02:03:53
--]]
--[[
Author: GGELUA
Date: 2024-09-04 16:18:56
Last Modified by: GGELUA
Last Modified time: 2024-10-09 21:43:10
--]]
--[[
Author: GGELUA
Date: 2024-08-10 00:03:20
Last Modified by: GGELUA
Last Modified time: 2024-09-04 06:26:00
--]]
__UI弹出["道具弹出更多"] = __UI界面["创建弹出窗口"](__UI界面, "道具弹出更多", 339+50 + abbr.py.x, 284-30 + abbr.py.y, 150, 192+64+64)
local 道具弹出更多 = __UI弹出["道具弹出更多"]
function 道具弹出更多:初始化()
  self:置精灵(取黑色背景(0, 0, 150, 192))
end
function 道具弹出更多:打开(lx)
  if self.是否可见 then
    self:置可见(false)
  else
    self:置可见(true)
  end
end
local lsan = {
  -- "临时包裹",
  "仓库操作",
  "整理道具",
 -- "本 命",
  "道具回收",
}

for i = 1, #lsan do
  local 临时函数 = 道具弹出更多["创建我的按钮"](道具弹出更多, __res:getPNGCC(2, 368, 906, 126, 52, true), lsan[i], 12, 11 + (i - 1) * 60, lsan[i])
  function  临时函数:左键弹起(x, y, msg)
    if "临时包裹" == lsan[i] then
      发送数据(3749, {
        ["方式"] = "索取"
      })
    elseif "仓库操作" == lsan[i] then
      发送数据(6701)
 --   elseif "本 命" == lsan[i] then
     -- 发送数据(193,{序号1=1})
    elseif "道具回收" == lsan[i] then
      发送数据(179,{数据序列=10})
    elseif "整理道具" == lsan[i] then
      if __UI界面["窗口层"]["道具行囊"]["是否可见"] then 
        if __UI界面["窗口层"]["道具行囊"]["包裹类型"]=="行囊" then 
          发送数据(3815,{类型="整理行囊"})
        else
          发送数据(3815,{类型="整理道具"})
        end
      end
    end
    道具弹出更多["置可见"](道具弹出更多, false)
  end
end