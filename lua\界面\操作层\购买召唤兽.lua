

local 购买召唤兽 = 窗口层:创建窗口("购买召唤兽", 0,0, 350, 380)

function 购买召唤兽:初始化()
    self:创建纹理精灵(function()
    置窗口背景("商会召唤兽", 0, 0, 350, 380, true):显示(0, 0)
    蓝白标题背景(340, 300, true):显示(5, 32) 
    标题字体:置颜色(255,255,255,255)
    标题字体:取图像("召唤兽名称"):显示(12,37)
    标题字体:取图像("召唤兽类型"):显示((350-标题字体:取宽度("召唤兽类型"))//2,37)
    标题字体:取图像("价格"):显示(340- 标题字体:取宽度("价格"),37)
   end
 )
   self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
   self.可初始化=true
   if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end

end

  local 购买 = 购买召唤兽:创建红色按钮("购买","购买", 35, 345,50, 22)                           
  function 购买:左键弹起(x, y)
    if  购买召唤兽.选中 then
      if  购买召唤兽.类型=="野怪" then
          请求服务(53,{名称= 购买召唤兽.选中})
        else
          请求服务(53.1,{名称= 购买召唤兽.选中})
       end
    end
  end


  local 刷新 = 购买召唤兽:创建红色按钮("刷新","刷新", 265, 345,50,22)  
  function 刷新:左键弹起(x, y)
    if  购买召唤兽.类型=="野怪" then
        请求服务(54,{名称= 购买召唤兽.选中})
      else
        请求服务(54.1,{名称= 购买召唤兽.选中})
    end
  end


function 购买召唤兽:打开(数据,类型)
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.选中=nil
  self.召唤兽={}
  self.类型="野怪"
  self.购买:置禁止(true)
  self:刷新(数据,类型)
end

function 购买召唤兽:刷新(数据,类型)
  self.类型=类型
  self.召唤兽={}
  for i=1,#数据 do
    self.召唤兽[#self.召唤兽+1]={名称=数据[i].模型,价格=数据[i].价格}
  end
  self.宠物选择:重置()
  if self.选中 then
      if not self.召唤兽[self.选中] then
          self.选中=nil
          self.购买:置禁止(true)
      else
          self.宠物选择:置选中(self.选中)
      end
  end
  
end 

local 宠物选择 = 购买召唤兽:创建列表("宠物选择", 5, 62, 340, 270)
function 宠物选择:初始化()
  self:置文字(文本字体)
  self:置颜色(0,0,0,255)
  self.行间距 = 0
  self.行高度 = 22
end


  function 宠物选择:重置()
        self:清空()
        for i, v in ipairs(购买召唤兽.召唤兽) do
            self:添加():创建纹理精灵(function()
                    文本字体:置颜色(0,0,0,255)
                    文本字体:取图像(v.名称):显示(12,4)
                    文本字体:取图像(购买召唤兽.类型):显示((340-文本字体:取宽度(购买召唤兽.类型))//2,4)
                    文本字体:取图像(v.价格):显示(335- 文本字体:取宽度(v.价格),4)
              end)
        end






    
  end
  
  function 宠物选择:左键弹起(x, y, i)
        购买召唤兽.选中=i
        购买召唤兽.购买:置禁止(false)
  end
  local 关闭 = 购买召唤兽:创建关闭按钮("关闭")
  function 关闭:左键弹起(x, y)
    购买召唤兽:置可见(false)
  end

  