local 基类 = require("界面/控件层/基类/物品基类")
local 商会格子 = class("商会格子", 基类)
function 商会格子:初始化()
end
function 商会格子:置物品(数据, lx)
  if "商会" == lx then
    local nsf = require("SDL.图像")(270, 68)
    if nsf["渲染开始"](nsf) then
      __res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 270, 68)["显示"](__res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 270, 68), 0, 0)
      字体18["置颜色"](字体18, __取颜色("黑色"))
      字体18["取图像"](字体18, 数据["编号"] .. "/" .. 数据["名称"])["显示"](字体18["取图像"](字体18, 数据["编号"] .. "/" .. 数据["名称"]), 15, 27)
      字体18["取图像"](字体18, 10)["显示"](字体18["取图像"](字体18, 10), 175, 27)
      字体18["取图像"](字体18, "综合店")["显示"](字体18["取图像"](字体18, "综合店"), 207, 27)
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
    self.格子类型 = lx
  elseif "商会召唤兽" == lx then
    local nsf = require("SDL.图像")(173, 64)
    if nsf["渲染开始"](nsf) then
      __res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 173, 64)["显示"](__res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 173, 64), 0, 0)
      字体18["置颜色"](字体18, __取颜色("黑色"))
      字体18["取图像"](字体18, 数据["模型"])["显示"](字体18["取图像"](字体18, 数据["模型"]), 46, 22)
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
    self.格子类型 = lx
  end
  self.数据 = 数据
end
function 商会格子:显示(x, y)
  if self.精灵 then
    self.精灵["显示"](self.精灵, x, y)
  end
  if self.确定 and self.格子类型 == "商会" then
    __主控["商会选中"]["显示"](__主控["商会选中"], x, y)
  elseif self.确定 and self.格子类型 == "商会召唤兽" then
    __主控["商会召唤兽选中"]["显示"](__主控["商会召唤兽选中"], x, y)
  end
end
return 商会格子
