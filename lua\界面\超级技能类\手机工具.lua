local 手机工具 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "手机工具", 138 + abbr.py.x, 17 + abbr.py.y, 710, 550)
function 手机工具:初始化()
  local nsf = require("SDL.图像")(695, 550)
  if nsf["渲染开始"](nsf) then
    置窗口背景("手机工具", 0, 12, 710, 500, true)["显示"](置窗口背景("手机工具", 0, 12, 710, 500, true), 0, 0)
   -- 取白色背景(0, 0, 400, 230, true)["显示"](取白色背景(0, 0, 400, 230, true), 17, 150)
  --  字体18["置颜色"](字体18, __取颜色("黑色"))
   -- 字体18["取图像"](字体18, "充值记录：")["显示"](字体18["取图像"](字体18, "充值记录："), 25, 160)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "玩家ID：")["显示"](字体18["取图像"](字体18, "玩家的ID："), 13, 120)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25), 100, 117)
 
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "玩家称谓：")["显示"](字体18["取图像"](字体18, "玩家称谓："), 340, 120)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25), 430, 117)
 
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "银子额数：")["显示"](字体18["取图像"](字体18, "仙玉额数："), 13, 182)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25), 100, 179)
 
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "储备额数：")["显示"](字体18["取图像"](字体18, "银子额数："), 340, 182)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25), 430, 180)
 
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "仙玉额数：")["显示"](字体18["取图像"](字体18, "储备额数："), 13, 244)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25), 100, 242)
 
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "经验额数：")["显示"](字体18["取图像"](字体18, "经验额数："), 340, 244)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25), 430, 242)
 
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "积分额数：")["显示"](字体18["取图像"](字体18, "积分额数："), 13, 310)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25), 100, 308)
 
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "充值会员：")["显示"](字体18["取图像"](字体18, "充值会员："), 340, 310)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 100, 25), 430, 308)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "经验倍数：")["显示"](字体18["取图像"](字体18, "经验倍数："), 13, 376)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 30, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 30, 25), 100, 372)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "修改难度：")["显示"](字体18["取图像"](字体18, "修改难度："), 243, 375)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 30, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 30, 25), 330, 373)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "等级上限：")["显示"](字体18["取图像"](字体18, "等级上限："), 470, 375)
    __res:getPNGCC(2, 795, 885, 100, 25)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 30, 25)["显示"](__res:getPNGCC(2, 795, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 272, 115), 30, 25), 557, 373)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, " ")["显示"](字体18["取图像"](字体18, " "), 0, 0)
    __res:getPNGCC(2, 810, 885, 550, 25)["拉伸"](__res:getPNGCC(2, 810, 885, 272, 115), 450, 25)["显示"](__res:getPNGCC(2, 810, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 810, 885, 272, 115), 550, 25), 13, 470)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "道具名称：")["显示"](字体18["取图像"](字体18, "道具名称："), 13, 425)
    __res:getPNGCC(2, 810, 885, 550, 25)["拉伸"](__res:getPNGCC(2, 810, 885, 272, 115), 90, 25)["显示"](__res:getPNGCC(2, 810, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 810, 885, 272, 115), 90, 25), 100, 423)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "道具数量：")["显示"](字体18["取图像"](字体18, "道具数量："), 200, 425)
    __res:getPNGCC(2, 810, 885, 550, 25)["拉伸"](__res:getPNGCC(2, 810, 885, 272, 115), 90, 25)["显示"](__res:getPNGCC(2, 810, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 810, 885, 272, 115), 90, 25), 287, 423)

    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "道具参数：")["显示"](字体18["取图像"](字体18, "道具参数："), 393, 425)
    __res:getPNGCC(2, 810, 885, 550, 25)["拉伸"](__res:getPNGCC(2, 810, 885, 272, 115), 90, 25)["显示"](__res:getPNGCC(2, 810, 885, 272, 115)["拉伸"](__res:getPNGCC(2, 810, 885, 272, 115), 90, 25), 480, 423)

    if nsf["渲染结束"](nsf) then
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
end
local data
local vip数据
function 手机工具:打开(数据)
  self:置可见(true)
  data= 数据
  self.vip=1
end




local 赞助界面 = 手机工具["创建我的控件"](手机工具, "赞助界面", 0, 0, 650, 486)
function 赞助界面:初始化()

  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end



local 自动抓鬼 = 手机工具["创建我的控件"](手机工具, "自动抓鬼", 0, 0, 650, 486)
function 自动抓鬼:初始化()
  local nsf = require("SDL.图像")(650, 550)
  self:置精灵(nsf["到精灵"](nsf))
end


function 自动抓鬼:显示(x,y)
    if self.图像 then
    self.图像["显示"](self.图像, x, y)
    end
end



local 关闭 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 645, 0)
function 关闭:左键弹起(x, y, msg)
  手机工具["置可见"](手机工具, false)
end

for i, v in ipairs({
  {
    name = "充值系统",
    x = 22-5,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 100, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 100, 43),
    font = "充值系统"
  },
  {
    name = "角色系统",
    x = 132-5,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 100, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 100, 43),
    font = "角色系统"
  },

  {
    name = "物品系统",
    x = 132+110-5,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 100, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 100, 43),
    font = "物品系统"
  },
  {
    name = "装备系统",
    x = 132+220-5,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 100, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 100, 43),
    font = "装备系统"
  },

  {
    name = "宠物系统",
    x = 132+330-5,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 100, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 100, 43),
    font = "宠物系统"
  },
  {
    name = "其他系统",
    x = 132+440-5,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 100, 43),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 100, 43),
    font = "其他系统"
  },

  {
    name = "锁定信息",
    x = 227,
    y = 109,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 100, 40),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 100, 40),
    font = "锁定信息"
  },



 
}) do
  local 临时函数 = 手机工具["创建我的单选按钮"](手机工具, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "确认充值"  then


    elseif    v.name == "锁定信息"  then
      __UI弹出.提示框:打开("#Y/锁定确认成功！")

      end
  end
end

local yinzi = 手机工具["创建我的输入"](手机工具, "yinzi", 435, 185, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 40), "充值银子", 560, 174, "充值银子")
function 确定:左键弹起(x, y, msg)
  if 手机工具.yinzi:取文本()~= ""  then
    if os.time()  then
      发送数据(7201,{数值=手机工具.yinzi:取文本(),id=手机工具.shuru:取文本(),类型="银子数额"})
      手机工具.发送时间=os.time()
      手机工具.yinzi:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end


local chubei = 手机工具["创建我的输入"](手机工具, "chubei", 104, 249, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 40), "充值储备", 230,  237, "充值储备")
function 确定:左键弹起(x, y, msg)
  if 手机工具.chubei:取文本()~= ""  then
    if os.time()  then
      发送数据(7201,{数值=手机工具.chubei:取文本(),id=手机工具.shuru:取文本(),类型="储备数额"})
      手机工具.发送时间=os.time()
      手机工具.chubei:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local jingyan = 手机工具["创建我的输入"](手机工具, "jingyan", 434, 249, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 40), "充值经验", 560,  237, "充值经验")
function 确定:左键弹起(x, y, msg)
  if 手机工具.jingyan:取文本()~= ""  then
    if os.time()  then
      发送数据(7201,{数值=手机工具.jingyan:取文本(),id=手机工具.shuru:取文本(),类型="经验数额"})
      手机工具.发送时间=os.time()
      手机工具.jingyan:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local jifen = 手机工具["创建我的输入"](手机工具, "jifen", 104, 314, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 40), "充值积分", 230,  304, "充值积分")
function 确定:左键弹起(x, y, msg)
  if 手机工具.jifen:取文本()~= ""  then
    if os.time()  then
      发送数据(7201,{数值=手机工具.jifen:取文本(),id=手机工具.shuru:取文本(),类型="充值积分"})
      手机工具.发送时间=os.time()
      手机工具.jifen:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local dianka = 手机工具["创建我的输入"](手机工具, "dianka", 434, 314, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 40), "充值会员", 560,  304, "充值会员")
function 确定:左键弹起(x, y, msg)
  if 手机工具.dianka:取文本()~= ""  then
    if os.time()  then
      发送数据(7201,{数值=手机工具.dianka:取文本(),id=手机工具.shuru:取文本(),类型="充值点卡"})
      手机工具.发送时间=os.time()
      手机工具.dianka:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local chengwei = 手机工具["创建我的输入"](手机工具, "chengwei", 434,  122, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 40), "充值称谓", 560,  109, "充值称谓")
function 确定:左键弹起(x, y, msg)
  if 手机工具.chengwei:取文本()~= ""  then
    if os.time()  then
      发送数据(7201,{数值=手机工具.chengwei:取文本(),id=手机工具.shuru:取文本(),类型="充值称谓"})
      手机工具.发送时间=os.time()
      手机工具.chengwei:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local beishu = 手机工具["创建我的输入"](手机工具, "beishu", 104,  379, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 90, 40), "经验倍数", 137,  366, "经验倍数")
function 确定:左键弹起(x, y, msg)
  if 手机工具.beishu:取文本()~= ""  then
    if os.time()  then
      发送数据(7215,{内容=手机工具.beishu:取文本(),id=手机工具.shuru:取文本(),类型="修改经验"})
      手机工具.发送时间=os.time()
      手机工具.beishu:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local nandu = 手机工具["创建我的输入"](手机工具, "nandu", 332,  379, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 90, 40), "修改难度", 370,  366, "修改难度")
function 确定:左键弹起(x, y, msg)
  if 手机工具.nandu:取文本()~= ""  then
    if os.time()  then
      发送数据(7215,{内容=手机工具.nandu:取文本(),id=手机工具.shuru:取文本(),类型="修改难度"})
      手机工具.发送时间=os.time()
      手机工具.nandu:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local dengji = 手机工具["创建我的输入"](手机工具, "dengji", 561,  379, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 90, 40), "等级上限", 590,  366, "等级上限")
function 确定:左键弹起(x, y, msg)
  if 手机工具.dengji:取文本()~= ""  then
    if os.time()  then
      发送数据(7215,{内容=手机工具.dengji:取文本(),id=手机工具.shuru:取文本(),类型="等级上限"})
      手机工具.发送时间=os.time()
      手机工具.dengji:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local gonggao = 手机工具["创建我的输入"](手机工具, "gonggao", 16,  476, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 90, 40), "发送公告", 590,  465, "发送公告")
function 确定:左键弹起(x, y, msg)
  if 手机工具.gonggao:取文本()~= ""  then
    if os.time()  then
      发送数据(7215,{内容=手机工具.gonggao:取文本(),类型="发送公告",id=手机工具.shuru:取文本()})
      手机工具.发送时间=os.time()
      手机工具.gonggao:置文本("")
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local xianyu = 手机工具["创建我的输入"](手机工具, "xianyu", 104, 185, 145, 30, nil, 88, "黑色", 字体20)
local shuru = 手机工具["创建我的输入"](手机工具, "shuru", 103, 121, 145, 30, nil, 88, "黑色", 字体20)
local 确定 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 40), "充值仙玉", 230, 174, "充值仙玉")
function 确定:左键弹起(x, y, msg)
  if 手机工具.shuru:取文本()~= ""  then
    if os.time()  then
      发送数据(7201,{数值=手机工具.xianyu:取文本(),id=手机工具.shuru:取文本(),类型="仙玉数额"})
      手机工具.发送时间=os.time()
  --    手机工具.shuru:置文本("")
      手机工具.xianyu:置文本("")

    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end

local mingcheng = 手机工具["创建我的输入"](手机工具, "mingcheng", 101, 427, 145, 30, nil, 88, "黑色", 字体20)
local shuliang = 手机工具["创建我的输入"](手机工具, "shuliang", 288, 427, 145, 30, nil, 88, "黑色", 字体20)
local canshu = 手机工具["创建我的输入"](手机工具, "canshu", 481, 427, 145, 30, nil, 88, "黑色", 字体20)

local 给予道具 = 手机工具["创建我的按钮"](手机工具, __res:getPNGCC(3, 2, 507, 117, 43, true)["拉伸"](__res:getPNGCC(3, 2, 507, 117, 43, true), 100, 40), "给予道具", 580, 418, "给予道具")
function 给予道具:左键弹起(x, y, msg)
  if 手机工具.mingcheng:取文本()~= ""  then
    if os.time()  then
      发送数据(7217,{id=手机工具.shuru:取文本(),道具名称=手机工具.mingcheng:取文本(),道具数量=手机工具.shuliang:取文本(),道具参数=手机工具.canshu:取文本(),道具附加="",道具专用=""})
      手机工具.发送时间=os.time()
      手机工具.xianyu:置文本("")
      手机工具.mingcheng:置文本("")     
      手机工具.shuliang:置文本("")
      手机工具.canshu:置文本("")     
    end
  else
    __UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  end
end