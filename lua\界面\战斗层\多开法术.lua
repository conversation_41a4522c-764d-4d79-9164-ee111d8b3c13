
local 多开法术 = 战斗层:创建窗口("多开法术")

local 按钮设置={"攻击","防御"}

function 多开法术:初始化()
 
end


function 多开法术:打开(战斗单位,参战位置)
  战斗层.九黎法术:置可见(false)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
    return
  end
  self.参战位置 = 参战位置
  self.已修改={}
  self.已修改.id =战斗单位.数据.id
  self.已修改.类型 = 战斗单位.类型
  self.已修改.认证码 = 战斗单位.数据.认证码
  self.循环次数 = 15
  self:置宽高(200,275)
  self.技能网格:置宽高(174,205)
  self:置精灵(置窗口背景(战斗单位.名称,0,0,200,275))
  self.花纹=__res:取资源动画("jszy/fwtb",0xabcd0204,"精灵"):置区域(0,0,16,203)
  if #战斗单位.主动技能>15 then
      self.循环次数 = 30
      self:置宽高(200,480)
      self.技能网格:置宽高(174,410)
      self:置精灵(置窗口背景(战斗单位.名称,0,0,200,480))
      self.花纹=__res:取资源动画("jszy/fwtb",0xabcd0204,"精灵"):置区域(0,0,16,406)
  end
  for i, v in ipairs(按钮设置) do
      self[v]:置坐标(15+(i-1)*131,self.高度-30)
  end

  local 技能=table.copy(战斗单位.主动技能)
  for i, v in ipairs(技能) do
      if 战斗单位.技能冷却 and 战斗单位.技能冷却[v.名称] then
          v.是否冷却 = 战斗单位.技能冷却[v.名称]
      end
  end
  self.技能网格:置技能(技能)
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
  else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
  end

end

function 多开法术:显示(x,y)
          for i = 1, 2 do
              self.花纹:显示(x+57+(i-1)*65,y+33)
          end
end



local 技能网格=多开法术:创建网格("技能网格",15,33,174,204)
function 技能网格:初始化()
      self:创建格子(40,40,1,25,10,3,true)
end

function 技能网格:置技能(数据)
      for i, v in ipairs(self.子控件) do
          local lssj =  __技能格子:创建()
          if 数据[i] and 数据[i].是否冷却 then
              lssj:置数据(数据[i],true,nil,数据[i].是否冷却)
              v.是否冷却=true
          else
              lssj:置数据(数据[i],true)
              v.是否冷却=nil
          end
          v:置精灵(lssj)
      end
end

function 技能网格:获得鼠标(x,y,a)
  if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
      self.子控件[self.焦点]._spr.焦点=nil
  end
 if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
        self.焦点=a
        self.子控件[a]._spr.焦点=true
        __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
  end
end

function 技能网格:失去鼠标(x,y)
  for i, v in ipairs(self.子控件) do
      if v._spr.焦点 then
          v._spr.焦点=nil
      end
  end
  self.焦点=nil
end


function 技能网格:左键弹起(x,y,a)
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
      if self.子控件[a].是否冷却 then
            __UI弹出.提示框:打开("#Y该技能还未冷却")
      else
            if __手机 then
                    __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20,技能网格,"使用",a)
              else
                    self:使用(a)
              end
      end
  end
  界面层.按下=false

end

function 技能网格:使用(编号)
        if 编号 and 编号~=0 then
              多开法术:设置技能(self.子控件[编号]._spr.数据.名称)
        end

end


function 技能网格:左键按下(x,y,a)
  
  界面层.按下=false

end

function 技能网格:右键弹起(x,y,a)
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
          if self.子控件[a].是否冷却 then
                __UI弹出.提示框:打开("#Y该技能还未冷却")
          else
                多开法术:设置技能(self.子控件[a]._spr.数据.名称)
          end
  end
  界面层.按下1=false
end
function 技能网格:右键按下(x,y,a)
 
  界面层.按下1=false
end


function 多开法术:设置技能(名称)
          self.已修改.参数 = 名称
          请求服务(5511,{修改=self.已修改})
          self:置可见(false)
end



for i, v in ipairs(按钮设置) do
      local 临时按钮=多开法术:创建红色按钮(v,v,0,0,40,22) 
      function 临时按钮:左键弹起(x,y)
            多开法术.已修改.参数 =  v
            请求服务(5511,{修改=多开法术.已修改})
            多开法术:置可见(false)
      end
end




local 关闭 = 多开法术:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    多开法术:置可见(false)
end
