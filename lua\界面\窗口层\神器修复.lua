
local 神器修复 = 窗口层:创建窗口("神器修复")
local 属性字段={速度="速　　度", 气血="气　　血",伤害="伤　　害",防御="防    御",封印命中="封印命中",法术伤害="法术伤害",固定伤害="固定伤害",物理暴击="物理暴击",治疗能力="治疗能力",法术暴击="法术暴击",法术防御="法术防御",抵抗封印="抵抗封印"}
local 技能介绍 = {
    藏锋敛锐 = {[1]= "横扫千军消耗的气血有50%的\n几率转化为等量护盾。",[2]="横扫千军消耗的气血有100%的\n几率转化为等量护盾。"},
    惊锋     = {[1]= "每次攻击提升自身10点伤害，\n最多叠加12层，死亡后清零。",[2]="每次攻击提升自身20点伤害\n，最多叠加12层，死亡后清\n零。"},
    披坚执锐 = {[1]= "遭受攻击时，有4%的几率免受\n90%的伤害。",[2]="遭受攻击时，有8%的几率免受\n90%的伤害。"},
    金汤之固 = {[1]= "气血小于30%时，提升240点抗\n封等级。",[2]="气血小于30%时，提升480点抗\n封等级。"},
    风起云墨 = {[1]= "受到你治疗的首目标本回合内\n受到的所有伤害降低4%。",[2]="受到你治疗的首目标本回合内\n受到的所有伤害降低8%。"},
    挥毫     = {[1]= "受到你的治疗时，目标每带有\n一个增益状态，额外恢复25点气血。",[2]="受到你的治疗时，目标每带有\n一个增益状态，额外恢复50点气血。"},
    盏中晴雪 = {[1]= "若你的速度高于施法者，提升\n速度差×0.5的抗封等级。",[2]="若你的速度高于施法者，提升\n速度差×1的抗封等级。"},
    泪光盈盈 = {[1]= "笑里藏刀额外减少目标6点愤\n怒。",[2]="笑里藏刀额外减少目标12点\n愤怒。"},
    凭虚御风 = {[1]= "每点被消耗的风灵增加40点法\n术伤害结果，最多叠加三层，\n死亡后清零。",[2]="每点被消耗的风灵增加80点法\n术伤害结果，最多叠加三层，\n死亡后清零。"},
    钟灵     = {[1]= "被使用3级药是有一定几率获\n得1层风灵。",[2]="被使用3级药是有较大几率获\n得1层风灵。"},
    亡灵泣语 = {[1]= "你的锢魂术会使得目标额外受\n到5%的物法伤害。",[2]="你的锢魂术会使得目标额外受\n到10%的物法伤害。"},
    魂魇     = {[1]= "被你的物理伤害攻击的单位在\n当回合内的法术伤害结果减少\n100点。",[2]="被你的物理伤害攻击的单位在\n当回合内的法术伤害结果减少\n200点。"},
    业焰明光 = {[1]= "你的法术有25%的几率造\n成额外25%的伤害。",[2]="你的法术有25%的几率造\n成额外50%的伤害。"},
    流火     = {[1]= "攻击气血百分比小于你的单位\n时，增加8%的伤害。",[2]="攻击气血百分比小于你的单位\n时，增加16%的伤害。"},
    蛮血     = {[1]= "增加（1-自身气血/气血上限）\n×8%的狂暴几率。",[2]="增加（1-自身气血/气血上限）\n×16%的狂暴几率。"},
    狂战     = {[1]= "每有一个己方召唤兽被击飞\n，增加30点伤害力，可叠加\n4层，死亡后消失。",[2]="每有一个己方召唤兽被击飞\n，增加60点伤害力，可叠加\n4层，死亡后消失。"},
    镜花水月 = {[1]= "受到治疗时，有8%的几率获\n得一个等额度的护盾。",[2]="受到治疗时，有16%的几率获\n得一个等额度的护盾。"},
    澄明     = {[1]= "每回合结束时，增加3点抵\n抗封印等级。",[2]="每回合结束时，增加6点抵抗\n封印等级。"},
    情思悠悠 = {[1]= "地涌金莲的目标获得治疗量\n10%的护盾。",[2]="地涌金莲的目标获得治疗量\n20%的护盾。"},
    相思     = {[1]= "偶数回合结束时，增加3点\n速度。",[2]="每个回合结束时，增加3点\n速度。"},
    弦外之音 = {[1]= "回合结束时，每个主动法宝\n效果会增加你3点愤怒。",[2]="回合结束时，每个主动法宝\n效果会增加你6点愤怒。"},
    裂帛     = {[1]= "  伤害性法术首目标伤害增加\n  8%。",[2]="  伤害性法术首目标伤害增加\n  16%。"},
    定风波   = {[1]= "受到的法术暴击伤害降低30%。",[2]="受到的法术暴击伤害降低60%。"},
    沧浪赋   = {[1]= "攻击气血小于30%的目标时，\n额外提升120点的法术伤害。",[2]="攻击气血小于30%的目标时，\n额外提升240点的法术伤害。"},
    斗转参横 = {[1]= "带有状态生命之泉时，日月\n乾坤命中率增加3%。",[2]="带有状态生命之泉时，日月\n乾坤命中率增加6%。"},
    静笃     = {[1]= "每次击杀敌方单位，增加60点\n伤害。",[2]= "每次击杀敌方单位，增加120\n点伤害。"},
    玉魄     = {[1]= "消耗愤怒的100%转化为下一次\n释放恢复性技能时的治疗能力。",[2]="消耗愤怒的200%转化为下一次\n释放恢复性技能时的治疗能力。"},
    璇华     = {[1]= "使用五行法术时，增\n加10%的伤害。",[2]="五使用五行法术时，增\n加20%的伤害。"},
    威服天下 = {[1]= "暴击伤害增加12%。",[2]="暴击伤害增加24%。"},
    酣战     = {[1]= "每点消耗的战意，会提升30点\n物理暴击等级，可叠加6次，\n死亡后清零。",[2]="每点消耗的战意，会提升60\n点物理暴击等级，可叠加6次，\n死亡后清零。"},
    万物滋长 = {[1]= "使用特技时将会获得（消耗愤\n怒值×等级×5%）的护盾和\n气血回复。",[2]="使用特技时将会获得（消耗愤\n怒值×等级×10%）的护盾和\n气血回复。"},
    开辟     = {[1]= "每次使用如意神通，提升20点\n自身伤害，最多叠加6层，死\n亡后清零。",[2]="每次使用如意神通，提升40点\n自身伤害，最多叠加6层，死\n亡后清零。"},
    鸣空     = {[1]= "每当令目标浮空时，你获得12点狂暴等级并且造成的物理伤害结果提高2%，最多叠加6层，阵亡后清零",[2]="每当令目标浮空时，你获得24点狂暴等级并且造成的物理伤害结果提高2%，最多叠加6层，阵亡后清零"},
    骇神     = {[1]= "受到物理伤害时，若攻击者物理伤害低于你，伤害结果降低10%",[2]="受到物理伤害时，若攻击者物理伤害低于你，伤害结果降低20%"},

}

local 名称图 = {
    大唐官府 = 0x130113D0,化生寺 = 0x9EE14769,方寸山 = 0xC160C703,女儿村 = 0xA0498994,
    天宫 = 0xD840B4EA,普陀山 = 0x721B7187,龙宫 =0xA8758DA8,五庄观 = 0xEA50FC9B,
    魔王寨 = 0x87798740,狮驼岭 = 0x50CCD3E0,盘丝洞 = 0xDCF3158D,阴曹地府 = 0xD07C437B,
    神木林 = 0xC0CD7B01,凌波城 = 0x29CCB8D3,无底洞 = 0xAF274C97
}





local 神器模型1={
    普陀山={[1]={"pt1.png","pt2.png","pt3.png"},[2]={"pt11.png","pt22.png","pt33.png"}},
    魔王寨={[1]={"mw1.png","mw2.png","mw3.png"},[2]={"mw11.png","mw22.png","mw33.png"}},
    方寸山={[1]={"fc1.png","fc2.png","fc3.png"},[2]={"fc11.png","fc22.png","fc33.png"}},
    花果山={[1]={"hg1.png","hg2.png","hg3.png"},[2]={"hg11.png","hg22.png","hg33.png"}},

}
local 五行图 = {
  金 = 0x01AC0010,木 = 0x01AC0011,水 = 0x01AC0012,火 = 0x01AC0013,土 = 0x01AC0014
}
local 神器图 = { 
  天宫   = {0x861333DE,0xD3F019A9,0xD90C5658},
  龙宫   = {0xC163A3CB,0xC5A94CBC,0x64893E31},
  化生寺 = {0xBCD2FD04,0x94748447,0x23906569},
  方寸山 = {0xF4468EB3,0x7ADF0AD5,0x6AD0DD5B},
  女儿村 = {0x2FE3D5CB,0xAE659408,0x478C415A},
  普陀山 = {0xACD7AC8C,0xA26045E7,0xAF7234E1},
  五庄观 = {0x7C4D3F9D,0x0A7537A9,0xF9CE015C},
  魔王寨 = {0x01AC0038,0x01AC0039,0x01AC0040},
  狮驼岭 = {0x898E3905,0xC621DCE5,0x768B550B},
  盘丝洞 = {0x767F7971,0x72527A8B,0x9F45CA15},
  神木林 = {0x01AC0038,0x96D9A247,0x1934A199},
  凌波城 = {0xC068EA63,0x8794D94E,0xC0AA056C},
  无底洞 = {0x2034FC4A,0x0CBEA6CD,0xFFC9DADB},
  花果山 = {0x861333DE,0xD3F019A9,0xD90C5658},
  大唐官府 = {0x467F2FCB,0x732620A1,0xAF99EF70},
  阴曹地府 = {0xE4E601D2,0x04847348,0x8F90B0F7},
  九黎城 = {0xE4E601D2,0x04847348,0x8F90B0F7},
  }

function 神器修复:初始化()
        local tcp =__res:取资源动画("pic/sqsc","sqbj.png","图片")
        self.标题背景= __res:取资源动画("pic/sqsc","tbwzbj.png","图片"):到精灵()
        self.标题= 标题字体:置颜色(__取颜色("黄色")):取精灵("神器修复")
        self:置宽高(tcp.宽度,tcp.高度)
        self:置精灵(tcp:到精灵())
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        self.背景动画=__res:取资源动画("jszy/xjjm",0xC057E026,"动画") 
        self.技能背景= __res:取资源动画("pic/sqsc","321.png","图片"):到精灵()
        self.属性框= __res:取资源动画("pic/sqsc","123.png","图片"):到精灵()
        self.按钮背景= __res:取资源动画("pic/sqsc","swbj.png","图片"):到精灵() 
         
    
    

end
function 神器修复:更新(dt)
    self.背景动画:更新(dt)
    if self.解锁动画 then
        self.解锁动画:更新(dt)
        if not self.解锁动画:是否播放() or self.解锁动画:取当前帧()>= self.解锁动画:取帧数()   then
            self:显示设置()
            self.解锁动画=nil
        end
    end
end
function 神器修复:显示(x,y)
            self.标题背景:显示(x+(self.宽度-self.标题背景.宽度)//2,y)
            self.标题:显示(x+(self.宽度-self.标题.宽度)//2,y+8)
            self.背景动画:显示(x-90,y-70)
            self.按钮背景:显示(x+(self.宽度-self.按钮背景.宽度)//2,y+40)
   

            if self.背景光源 then
                self.背景光源:显示(x+160,y + 127)
            end
            if self.背景图片 then
                if self.神器属性[self.状态] then
                    self.背景图片:显示(x+185,y + 140)
                else
                    self.背景图片:显示(x+190,y + 147)
                end
            end

            if self.属性图标 then
                if self.属性图标[1] and self.属性图标[1][1] then
                    self.属性图标[1][1]:显示(x+321,y+216)
                    if self.属性图标[1][2] then
                        self.属性图标[1][2]:显示(x+328,y+222)
                    end
                end
                if self.属性图标[2] and self.属性图标[2][1] then
                    self.属性图标[2][1]:显示(x+388,y+286)
                    if self.属性图标[2][2] then
                        self.属性图标[2][2]:显示(x+395,y+292)
                    end
                end
                if self.属性图标[3] and self.属性图标[3][1] then
                    self.属性图标[3][1]:显示(x+321,y+343)
                    if self.属性图标[3][2] then
                        self.属性图标[3][2]:显示(x+328,y+349)
                    end
                end
                if self.属性图标[4] and self.属性图标[4][1] then
                    self.属性图标[4][1]:显示(x+256,y+286)
                    if self.属性图标[4][2] then
                        self.属性图标[4][2]:显示(x+263,y+292)
                    end
                end
            end
            if self.文字图片 then
                self.文字图片:显示(x+297,y+248)
            end
            if self.锁住图片 then
               self.锁住图片:显示(x+325,y+380)
            end
            if self.解锁动画 then
                self.解锁动画:显示(x+325,y+380)
            end
            if self.解锁点图片 and self.神器属性[self.状态] then
                self.解锁点图片:显示(x+15,y+130)
            end
            self.属性框:显示(x-20,y +258)
            self.技能背景:显示(x +25 ,y + 290)
            if self.技能名称 then
                self.技能名称:显示(x +25+(self.技能背景.宽度-self.技能名称.宽度)//2,y + 296)
            end
           
         


end

-- function 神器修复:获得鼠标(x,y)
--         if self.物品 then
--             for i = 1, 8 do
--                 if self.物品[i] and self.物品[i].小动画 and self.物品[i].小动画:检查透明(x, y)  then
--                     __UI弹出.自定义提示:打开(self.物品[i],x+20,y+20)
--                 end
--             end
--         end
-- end





function 神器修复:打开(数据)
    if not 角色信息 or not 角色信息.门派 or 角色信息.门派=="无" or 角色信息.门派=="无门派" or not 数据 or not 数据.神器 or not 数据.神器.神器技能  then return end
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.解锁点=0
    self.状态=1
    self:刷新(数据)
    


   
end

function 神器修复:刷新解锁点(数据)
  self.解锁点=数据
  self.解锁点图片=self:创建纹理精灵(function()
                          取输入背景(0, 0, 125, 22):显示(55,0)
                          标题字体:置颜色(255,255,255,255):取图像("解锁点"):显示(0,3)
                          文本字体:置颜色(0,0,0,0):取图像(self.解锁点):显示(60,3) 
                      end,1,180,30
                      )
end

function 神器修复:激活插槽更新(数据)
        self.神器属性 = {}
        for n=1,3 do
            if 数据.神器解锁[n]~=nil then
                self.神器属性[n]=数据.神器解锁[n]
            end
        end
        for i = 1, 4 do
            if self.神器属性[self.状态].神器卡槽解锁[i] and not self["镶嵌按钮"..i].物品  and not self["镶嵌按钮"..i].解锁 then
                  self["镶嵌按钮"..i]:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x01AC0109))
                  self["镶嵌按钮"..i].解锁=true
            end
        end
        self:刷新解锁点(数据.神器解锁点)
        self:计算属性()
end



function 神器修复:区域解锁(数据)
       self.锁住图片=nil
       self.解锁点=数据.神器解锁点
       self.神器属性 = {}
        for n=1,3 do
            if 数据.神器解锁[n]~=nil then
                self.神器属性[n]=数据.神器解锁[n]
            end
        end
        self.解锁动画 = __res:取资源动画("jszy/xjjm",0xC3F66FE8,"置动画")



end


function 神器修复:刷新(数据)

        self.原始灵犀玉=数据.灵犀玉
        self.灵犀玉={}
        self.镶嵌灵犀玉={}
        if 数据.灵犀玉 then
           for k, v in pairs(数据.灵犀玉) do
              v.背包编号=k
              table.insert(self.灵犀玉,v)
           end
        end
        self.解锁点=数据.神器.神器解锁点
        self.神器属性 = {}
        self.技能 = 数据.神器.神器技能.名称
        self.等级 = 数据.神器.神器技能.等级
        for n=1,3 do
            if 数据.神器.神器解锁[n]~=nil then
                self.神器属性[n]=数据.神器.神器解锁[n]
            end
        end
        self.技能名称=文本字体:置颜色(255,255,255):取精灵(self.技能)
        self.标题= 标题字体:置颜色(__取颜色("黄色")):取精灵("神器修复")
        if 角色信息.门派~="无" and 角色信息.门派~="无门派" then
            if 角色信息.门派=="花果山" then
                self.标题= 标题字体:置颜色(__取颜色("黄色")):取精灵("鸿蒙石")
            elseif 角色信息.门派=="九黎城" then
                self.标题= 标题字体:置颜色(__取颜色("黄色")):取精灵("魔息角")
            elseif 名称图[角色信息.门派] then
                self.标题=__res:取资源动画("jszy/xjjm",名称图[角色信息.门派],"精灵")
            end
        end 
        self.灵犀控件.展开=nil
        self.技能描述:清空()
        self.技能描述:置文本(技能介绍[self.技能][self.等级])
        self:显示设置()
end



function 神器修复:刷新灵犀玉()
      self.灵犀玉={}
      if self.原始灵犀玉 then
        for k, v in pairs( self.原始灵犀玉) do
            if not self.镶嵌灵犀玉[k] then
                v.背包编号=k
                table.insert(self.灵犀玉,v)
            end
        end
      end
      鼠标层.附加=nil
      self.选中编号=nil
      self.灵犀控件:显示设置()
      self:计算属性()
end

function 神器修复:显示设置()
          鼠标层.附加=nil
          self.还原属性:置可见(false)
          for i = 1, 3 do
                if self.状态 == i then
                    self["部位按钮"..i]:创建按钮精灵(__res:取资源动画("jszy/xjjm",神器图[角色信息.门派][i]):取图像(5))
                    self.按钮光环= __res:取资源动画("pic","gq.png","图片"):拉伸(self["部位按钮"..i].宽度,self["部位按钮"..i].高度):到精灵()
                    self["部位按钮"..i]:置选中(true)
                else
                    self["部位按钮"..i]:创建按钮精灵(__res:取资源动画("jszy/xjjm",神器图[角色信息.门派][i]))
                    self["部位按钮"..i]:置选中(false)
                end
          end
          if self.状态 ==1 then
              self.部位按钮1:置坐标(282,50)
              self.部位按钮2:置坐标(360,40)
              self.部位按钮3:置坐标(205,40)
          elseif self.状态 ==2 then
                self.部位按钮1:置坐标(205,40)
                self.部位按钮2:置坐标(282,50)
                self.部位按钮3:置坐标(360,40)
          else
              self.部位按钮1:置坐标(360,40)
              self.部位按钮2:置坐标(205,40)
              self.部位按钮3:置坐标(282,50)
          end
          self.属性图标={}
          if self.神器属性[self.状态] then
                self.解锁区域:置可见(false)
                self.解锁点按钮:置可见(true)
                self.保存属性:置可见(true)
                self.保存属性:置禁止(true)
                self.保存属性:置坐标(300,455)
                self.背景光源 = __res:取资源动画("pic/sqsc","gh.png","图片"):到精灵()
                self.背景图片 = __res:取资源动画("pic/sqsc","sqyjs.png","图片"):到精灵()
                self.锁住图片=nil
                self.解锁点图片= self:创建纹理精灵(function()
                            取输入背景(0, 0, 125, 22):显示(55,0)
                            标题字体:置颜色(255,255,255,255):取图像("解锁点"):显示(0,3)
                            文本字体:置颜色(0,0,0,0):取图像(self.解锁点):显示(60,3) 
                          end,1,180,30
                          )
                for i = 1, 4 do
                  self["镶嵌按钮"..i].物品=nil
                    self.属性图标[i]={__res:取资源动画("jszy/xjjm",0x01AC0007,"精灵"),__res:取资源动画("jszy/xjjm",五行图[self.神器属性[self.状态].神器五行[i]],"精灵")} 
                    if self.神器属性[self.状态].神器卡槽解锁[i] then
                          self["镶嵌按钮"..i]:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x01AC0109))
                          self["镶嵌按钮"..i].解锁=true
                          if self.神器属性[self.状态].镶嵌灵犀玉[i] and self.神器属性[self.状态].镶嵌灵犀玉[i].子类 and self.神器属性[self.状态].镶嵌灵犀玉[i].特性 then
                                local lssj = __物品格子:创建()
                                local 物品={名称="灵犀玉",子类=self.神器属性[self.状态].镶嵌灵犀玉[i].子类,特性=self.神器属性[self.状态].镶嵌灵犀玉[i].特性}
                                lssj:置物品(物品,nil,nil,nil,nil,nil,self.神器属性[self.状态].镶嵌灵犀玉[i].特性)
                                self["镶嵌按钮"..i].物品=lssj
                                self["镶嵌按钮"..i].物品.背包编号=nil
                          end
                    else
                        self["镶嵌按钮"..i]:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x01AC0110))
                        self["镶嵌按钮"..i].解锁=false
                    end
                    self["镶嵌按钮"..i]:置禁止(false)
                end
                self:计算属性()
          else
              self.背景光源 =nil
              self.文字图片=nil
              self.解锁点图片=nil
              self.解锁点按钮:置可见(false)
              self.解锁区域:置可见(true)
              self.保存属性:置可见(false)
              self.背景图片 = __res:取资源动画("pic/sqsc","sqjs.png","图片"):到精灵()
              self.锁住图片=__res:取资源动画("jszy/xjjm",0xC3F66FE8,"精灵")
              for i = 1, 4 do
                  self.属性图标[i]={__res:取资源动画("jszy/xjjm",0x01AC0007):取灰度精灵(1)} 
                  self["镶嵌按钮"..i]:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x01AC0108))
                  self["镶嵌按钮"..i]:置禁止(true)
                  self["镶嵌按钮"..i].解锁=nil
                  self["镶嵌按钮"..i].物品=nil
              end


          end
          self.灵犀控件:显示设置()
         

         

end

local 灵犀控件=神器修复:创建控件("灵犀控件", 515,130, 100,320)


function 灵犀控件:显示设置()
        self.物品网格:置物品(神器修复.灵犀玉)
        self:展开设置()
end



function 灵犀控件:展开设置()

  if self.展开 then
        self:置精灵() 
        self.物品网格:置可见(false)
        self.展开按钮:置坐标(10,0)
        self.展开按钮:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x01AC0021)) 
        鼠标层.附加=nil
  else
      
      self.物品网格:置可见(true)
      self:置精灵(__res:取资源动画("jszy/xjjm",0x01AC0006,"精灵")) 
      self.展开按钮:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x01AC0019))
      self.展开按钮:置坐标(8,290)
  end

end


local 展开按钮=灵犀控件:创建按钮("展开按钮")
function 展开按钮:左键弹起(x, y)
      if 灵犀控件.展开 then
          灵犀控件.展开=nil
      else
          灵犀控件.展开=1
      end
      灵犀控件:展开设置()
end



local 物品网格=灵犀控件:创建网格("物品网格",15,10,70,290)
function 物品网格:置物品(数据)
    local 格子 = 0
    if 数据 then
        格子=#数据
    end
    self:创建格子(70, 60, 1, 1, 格子, 1)
    for i, v in ipairs(self.子控件) do
          local lssj = __物品格子:创建()
          lssj:置物品(数据[i],70,60)
          lssj:置偏移(11,5)
          lssj.格子=true
          lssj.格子背景= __res:取资源动画("jszy/xjjm",0x01AC0111,"精灵")
          v:置精灵(lssj)
    end
end

function 物品网格:获得鼠标(x,y,a)
        if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr and self.子控件[self.焦点]._spr.焦点 then
            self.子控件[self.焦点]._spr.焦点=nil
            self.焦点=nil
        end
        if self.子控件[a] and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
            self.子控件[a]._spr.焦点=true
            self.焦点=a
            local xx,yy=引擎:取鼠标坐标()
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,xx+20,yy+20)
        end
end
function 物品网格:失去鼠标(x,y)
          if self.焦点 and self.子控件[self.焦点] and self.子控件[self.焦点]._spr and self.子控件[self.焦点]._spr.焦点 then
              self.子控件[self.焦点]._spr.焦点=nil
          end
          self.焦点=nil

end

function 物品网格:左键弹起(x,y,a)

        if self.子控件[a] and self.子控件[a]._spr and self.子控件[a]._spr.物品 then
                if __手机 then
                        __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+25,y+25,物品网格,"修复神器",a)
                else
                      self:拿起(a)
                end
        end
end

function 物品网格:拿起(编号)
        if 编号 and 编号~=0 then
            鼠标层.附加=self.子控件[编号]._spr
            鼠标层.附加.窗口 = "神器修复"
            鼠标层.附加.编号=编号
            神器修复.选中编号=编号
        end
end

for i=1,3 do
      local 临时按钮=神器修复:创建单选按钮("部位按钮"..i)
      function 临时按钮:左键弹起(x, y)
                if 神器修复.状态~=i then
                    神器修复.状态=i
                    神器修复:显示设置()
                end
      end
      function 临时按钮:显示(x, y)
            if 神器修复.状态==i then
               神器修复.按钮光环:显示(x, y)
            end
      end
end

for i=1,4 do
    local 临时按钮=神器修复:创建按钮("镶嵌按钮"..i)
    if i==1 then
        临时按钮:置坐标(303,140)
    elseif i==2 then
        临时按钮:置坐标(190,265)
    elseif i==3 then
        临时按钮:置坐标(303,385)
    else
        临时按钮:置坐标(420,265)
    end

    function 临时按钮:显示(x, y)
          if 临时按钮.物品  then
              临时按钮.物品:显示(x+5, y+7)
          end
    end
    function 临时按钮:获得鼠标(x, y)
          if 临时按钮.物品 and 临时按钮.物品.物品 then
              local xx,yy=引擎:取鼠标坐标()
             __UI弹出.道具提示:打开(临时按钮.物品.物品,xx+20,yy+20)
          end
    end
 


   
    
    function 临时按钮:左键弹起(x, y)
            if self.解锁 then
               if 神器修复.选中编号 then
                    if 临时按钮.物品 and 临时按钮.物品.背包编号 then
                        神器修复.镶嵌灵犀玉[临时按钮.物品.背包编号]=nil
                        临时按钮.物品=nil
                    end
                      local 物品数据=神器修复.灵犀玉[神器修复.选中编号]
                      
                      local lssj = __物品格子:创建()
                      lssj:置物品(物品数据,nil,nil,nil,nil,nil,物品数据.特性)
                      临时按钮.物品=lssj
                      临时按钮.物品.背包编号=物品数据.背包编号
                      神器修复.镶嵌灵犀玉[物品数据.背包编号]=true
                      神器修复:刷新灵犀玉()
               elseif 临时按钮.物品 and 临时按钮.物品.物品 and __手机 then
                    __UI弹出.道具提示:打开(临时按钮.物品.物品,x+20,y+20)
               end
            elseif self.解锁==false then
                  窗口层.对话栏:打开(角色信息.模型,角色信息.名称,"神器被某种力量封印，需要100点解锁点和50000000经验激活，现有解锁点："..神器修复.解锁点.."点",{"确定解锁插槽","取消"},{部件=神器修复.状态,插槽位=i})
            end
         
    end

end










local 解锁点按钮=神器修复:创建按钮("解锁点按钮",177,125) 
function 解锁点按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x01AC0020),1)
end
--self:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x01AC0109))--可镶嵌
--tp._按钮.创建(tp.资源:载入("新加界面.npk","网易WDF动画","01AC0110"),0,0,3,true,true) --有锁
function 解锁点按钮:左键弹起(x, y)

  窗口层.对话栏:打开(角色信息.模型,角色信息.名称,"您是否需要5000000两梦幻币购买50点神器解锁点？",{"确定添加点数","取消"})
end


local 技能描述=神器修复:丰富文本("技能描述",32,325,165,120)


local 保存属性=神器修复:创建红色按钮("保存","保存属性",247,455,74,22) 
function 保存属性:左键弹起(x, y)
    local 灵犀玉 ={}
    for i=1,4 do
        if 神器修复["镶嵌按钮"..i].物品 and 神器修复["镶嵌按钮"..i].物品.背包编号 and 神器修复.原始灵犀玉[神器修复["镶嵌按钮"..i].物品.背包编号] then
           灵犀玉[i]={识别码=神器修复.原始灵犀玉[神器修复["镶嵌按钮"..i].物品.背包编号].识别码}
        end
    end
    请求服务(3774,{灵犀玉=灵犀玉,部件=神器修复.状态})

end


local 还原属性=神器修复:创建红色按钮("还原","还原属性",350,455,74,22) 

function 还原属性:左键弹起(x, y)
      神器修复.镶嵌灵犀玉={}
      神器修复:刷新灵犀玉()
      神器修复:显示设置()
end



local 解锁区域=神器修复:创建红色按钮("解锁","解锁区域",300,455,74,22) 


function 解锁区域:左键弹起(x, y)
    
    if 神器修复.神器属性[神器修复.状态-1] then
        local 数量= 0
        for i, v in ipairs(神器修复.神器属性[神器修复.状态-1].神器卡槽解锁) do
          数量=数量+1
        end
        if 数量>=4 then
           窗口层.文本栏:打开("这个部件被未知的力量封印，解锁这个部件需消耗#R5000000#W两梦幻币。", 6204, {区域 = 神器修复.状态})
        else
            __UI弹出.提示框:打开("#Y请先将已解锁区域的卡槽全部修复后，才能继续解锁该区域。")
        end
    else
        __UI弹出.提示框:打开("#Y请先解锁上一个部件。")
    end
end

function 神器修复:计算属性()
  if self.神器属性[self.状态] then
        local 预览开关={}
        local 特性激活={}
        local 基础属性={}
        for i = 1, 4 do
            特性激活[i] = {蔓延=false,天平=false,相生=false,相克=false,耀=false}
            预览开关[i] = false
            基础属性[i]=0
        end
        local 神器五行 =self.神器属性[self.状态].神器五行
        local 神器属性=self.神器属性[self.状态].神器五行属性
        for i = 1, 4 do
              if self["镶嵌按钮"..i].物品 and self["镶嵌按钮"..i].物品.物品 then
                  local 子类 = self["镶嵌按钮"..i].物品.物品.子类
                  local 特性 = self["镶嵌按钮"..i].物品.物品.特性
                  if 神器属性[i]=="速度" or 神器属性[i]=="防御" or 神器属性[i]=="法术防御"  or 神器属性[i]=="封印命中" or 神器属性[i]=="抵抗封印" then
                          if 子类==1 then
                                基础属性[i]=6
                          elseif 子类==2 then
                                基础属性[i]=12
                          else
                                基础属性[i]=24
                          end
                  elseif 神器属性[i]=="气血" then
                        if 子类==1 then
                            基础属性[i]=9
                        elseif 子类==2 then
                            基础属性[i]=18
                        else
                            基础属性[i]=36
                        end
                  else
                      if 子类==1 then
                          基础属性[i]=3
                      elseif 子类==2 then
                          基础属性[i]=6
                      else
                          基础属性[i]=12
                      end
                  end
                  for n = 1, 4 do
                      if 特性==神器五行[n].."耀" then
                          特性激活[n].耀=true
                          预览开关[n]=true
                      end
                  end
                  if 特性=="蔓延" and (i==1 or i==3) then
                        特性激活[2].蔓延=true
                        特性激活[4].蔓延=true
                        预览开关[2]=true
                        预览开关[4]=true
                  elseif 特性=="天平" and (i==2 or i==4) then
                        特性激活[2].天平=true
                        特性激活[4].天平=true
                        预览开关[2]=true
                        预览开关[4]=true
                  elseif 特性=="相生" then
                        if i==1 or i==3 then
                            if self:取五行相生(神器五行[i])==神器五行[2] then
                                特性激活[2].相生=true
                                预览开关[2]=true
                            end
                            if self:取五行相生(神器五行[i])==神器五行[4] then
                              特性激活[4].相生=true
                              预览开关[4]=true
                            end
                        elseif i==2 or i==4 then
                              if self:取五行相生(神器五行[i])==神器五行[1] then
                                特性激活[1].相生=true
                                预览开关[1]=true
                              end
                              if self:取五行相生(神器五行[i])==神器五行[3] then
                                特性激活[3].相生=true
                                预览开关[3]=true
                              end
                        end
                  elseif 特性=="相克" then
                        if i==1 or i==3 then
                            if self:取五行相克(神器五行[i])==神器五行[2] then
                                特性激活[2].相克=true
                                预览开关[2]=true
                            end
                            if self:取五行相克(神器五行[i])==神器五行[4] then
                                特性激活[4].相克=true
                                预览开关[4]=true
                            end
                        elseif i==2 or i==4 then
                              if self:取五行相克(神器五行[i])==神器五行[1] then
                                  特性激活[1].相克=true
                                  预览开关[1]=true
                              end
                              if self:取五行相克(神器五行[i])==神器五行[3] then
                                  特性激活[3].相克=true
                                  预览开关[3]=true
                              end
                        end
                  elseif 特性=="利"..神器五行[i] then
                        特性激活[i].利=true
                        预览开关[i]=true
                  end
            end
        end
        for i = 1, 4 do
          if self["镶嵌按钮"..i].物品 and self["镶嵌按钮"..i].物品.物品 then
              if 特性激活[i].耀 then --受影响的全部插槽的属性全部加
                  if 神器属性[i]=="伤害" or 神器属性[i]=="法术伤害" or 神器属性[i]=="固定伤害"  then
                        基础属性[i]=基础属性[i]+8
                  elseif 神器属性[i]=="气血" then
                        基础属性[i]=基础属性[i]+10
                  else
                        基础属性[i]=基础属性[i]+4
                  end
              elseif 特性激活[i].蔓延 then
                  if 神器属性[i]=="伤害" or 神器属性[i]=="法术伤害" or 神器属性[i]=="固定伤害"  then
                        基础属性[i]=基础属性[i]+8
                  elseif 神器属性[i]=="气血" then
                        基础属性[i]=基础属性[i]+10
                  else
                        基础属性[i]=基础属性[i]+4
                  end
              elseif 特性激活[i].天平 then
                  if 神器属性[i]=="伤害" or 神器属性[i]=="法术伤害" or 神器属性[i]=="固定伤害"  then
                        基础属性[i]=基础属性[i]+8
                  elseif 神器属性[i]=="气血" then
                        基础属性[i]=基础属性[i]+10
                  else
                        基础属性[i]=基础属性[i]+4
                  end
              elseif 特性激活[i].相生 then
                  if 神器属性[i]=="伤害" or 神器属性[i]=="法术伤害" or 神器属性[i]=="固定伤害"  then
                    基础属性[i]=基础属性[i]+8
                  elseif 神器属性[i]=="气血" then
                    基础属性[i]=基础属性[i]+10
                  else
                    基础属性[i]=基础属性[i]+4
                  end
              elseif 特性激活[i].相克 then
                    if 神器属性[i]=="伤害" or 神器属性[i]=="法术伤害" or 神器属性[i]=="固定伤害" then
                      基础属性[i]=基础属性[i]+8
                    elseif 神器属性[i]=="气血" then
                      基础属性[i]=基础属性[i]+10
                    else
                      基础属性[i]=基础属性[i]+4
                    end
              elseif 特性激活[i].利 then
                    if 神器属性[i]=="伤害" or 神器属性[i]=="法术伤害" or 神器属性[i]=="固定伤害"  then
                      基础属性[i]=基础属性[i]+8
                    elseif 神器属性[i]=="气血" then
                      基础属性[i]=基础属性[i]+10
                    else
                      基础属性[i]=基础属性[i]+4
                    end
              end
              if 基础属性[i]~=self.神器属性[self.状态].神器五行数值[i] then
                  预览开关[i]=true
                  self.保存属性:置禁止(false)
                  self.保存属性:置坐标(247,455)
                  self.还原属性:置可见(true)
              elseif  基础属性[i]==self.神器属性[self.状态].神器五行数值[i] then
                    预览开关[i]=false
              end
          end
        end
      self.文字图片= self:创建纹理精灵(function()
          for i = 1, 4 do
            if 预览开关[i] and 基础属性[i]>0 then
                文本字体:置颜色(__取颜色("黄色")):取图像(属性字段[神器属性[i]].." +"..基础属性[i]):显示(0,(i-1)*25)
                文本字体:置颜色(__取颜色("红色")):取图像("↑"):显示(文本字体:取宽度(属性字段[神器属性[i]].." +"..基础属性[i]),(i-1)*25)
            else
                文本字体:置颜色(255,255,255,255):取图像(属性字段[神器属性[i]].." +"..self.神器属性[self.状态].神器五行数值[i]):显示(0,(i-1)*25)
            end
          end
      end,1,100,100
      )
end



end



function 神器修复:取五行相生(五行)
  local 相生="水"
  if 五行=="金" then
	 相生="水"
  elseif 五行=="水" then
	 相生="木"
  elseif 五行=="木" then
	 相生="水"
  elseif 五行=="火" then
	 相生="土"
  elseif 五行=="土" then
	 相生="金"
  end
  return 相生
end

function 神器修复:取五行相克(五行)
  local 相克="木"
  if 五行=="金" then
	 相克="木"
  elseif 五行=="木" then
	 相克="土"
  elseif 五行=="土" then
	 相克="水"
  elseif 五行=="水" then
	 相克="火"
  elseif 五行=="火" then
	 相克="金"
  end
  return 相克
end


local 关闭 = 神器修复:创建按钮( "关闭", 605,2) 
function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x20FD5715),1)
end
function 关闭:左键弹起(x, y)
  神器修复:置可见(false)
end







  


















