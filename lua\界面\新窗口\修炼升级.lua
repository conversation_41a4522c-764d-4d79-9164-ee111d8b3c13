local 修炼升级 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "修炼升级", 312 + abbr.py.x, 67 + abbr.py.y, 485, 365+5)
local 计算修炼等级经验 = function(等级, 上限)
  local 临时经验 = 110
  if 0 == 等级 then
    return 110
  end
  for n = 1, 上限 + 1 do
    临时经验 = 临时经验 + 20 + n * 20
    if n == 等级 then
      return 临时经验
    end
  end
end
function 修炼升级:初始化()
  local nsf = require("SDL.图像")(485, 365+5)
  if nsf["渲染开始"](nsf) then
    置窗口背景(nil, 0, 12, 477, 356, true)["显示"](置窗口背景(nil, 0, 12, 477, 356, true), 0, 0)
    取白色背景(0, 0, 450, 175, true)["显示"](取白色背景(0, 0, 450, 175, true), 13, 43)
    local lssj = 取输入背景(0, 0, 123, 23)
    -- 字体18["置颜色"](字体18, __取颜色("白色"))
    -- 字体18["取图像"](字体18, "帮派现有资金")["显示"](字体18["取图像"](字体18, "帮派现有资金"), 15, 237)
    lssj["显示"](lssj, 133, 235)
    -- 字体18["取图像"](字体18, "现金")["显示"](字体18["取图像"](字体18, "现金"), 274, 237)
    lssj["显示"](lssj, 320+20, 235)
    -- 字体18["取图像"](字体18, "修炼所需金钱")["显示"](字体18["取图像"](字体18, "修炼所需金钱"), 15, 273)
    lssj["显示"](lssj, 133, 271)
    -- 字体18["取图像"](字体18, "帮贡")["显示"](字体18["取图像"](字体18, "帮贡"), 274, 273)
    lssj["显示"](lssj, 320+20, 271)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 修炼升级:打开(zc,yz,xl,cb,lx,mzc)
  self.选中= 0
  self.资材 =zc
  self.银子 =yz
  self.储备 =cb
  self.数据=xl
  self.sl=5
  self.免资材=mzc
  self.类型="人物修炼列表"
  self:置可见(true)
  self.人物修炼列表:置可见(false)
  self.召唤兽控制列表:置可见(false)
  self.选中 = nil
  if lx=="人物" then
      self.类型="人物修炼列表"
      self.标题="人物修炼"
      self.leix="人物修炼"
      if self.免资材 then
          self.标题="人物修炼(免资材)"
      end
      self[self.类型]:置可见(true)
  else
      self.sl=4
      self.类型="召唤兽控制列表"
      self.标题="宝宝修炼"
      self.leix="宝宝修炼"
      if self.免资材 then
          self.标题="宝宝修炼(免资材)"
      end
      self[self.类型]:置可见(true)
  end
  self:cz()
end

function 修炼升级:刷新数据(sj)
	self.资材=sj.帮派资材
	self.银子=sj.银子
	self.储备=sj.储备
	self.数据=sj.修炼
	self.免资材=sj.免资材
  self:cz()
end
function 修炼升级:cz()
  local nsf = require("SDL.图像")(477, 303)
  if nsf["渲染开始"](nsf) then
    local lssj = 取输入背景(0, 0, 123, 23)
    local 宽度 = 字体20:取宽度(self.标题)
    字体20:置颜色(255,255,255)
    字体20:取图像(self.标题):置混合(0):显示(477/2-宽度/2,17)
    if self.免资材 then
      字体18["置颜色"](字体18, __取颜色("白色"))
      字体18:取图像("修炼一次金钱"):置混合(0):显示(15, 237)
      字体18:取图像("现 金"):置混合(0):显示(274, 237)
      字体18:取图像("今日剩余次数"):置混合(0):显示(15, 273)
      字体18:取图像("储备金"):置混合(0):显示(274, 273)

      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18:取图像("80000"):置混合(0):显示(15+124, 237)
      字体18:取图像(self.银子):置混合(0):显示(274+74, 237)
      字体18:取图像(self.免资材):置混合(0):显示(15+124, 273)
      字体18:取图像(self.储备):置混合(0):显示(274+74, 273)
    else
      字体18["置颜色"](字体18, __取颜色("白色"))
      字体18:取图像("帮派现有资材"):置混合(0):显示(15, 237)
      字体18:取图像("现 金"):置混合(0):显示(274, 237)
      字体18:取图像("修炼所需金钱"):置混合(0):显示(15, 273)
      字体18:取图像("储备金"):置混合(0):显示(274, 273)

      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18:取图像(self.资材):置混合(0):显示(15+124, 237)
      字体18:取图像(self.银子):置混合(0):显示(274+74, 237)
      字体18:取图像("30000"):置混合(0):显示(15+124, 273)
      字体18:取图像(self.储备):置混合(0):显示(274+74, 273)
    end
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  -- self.图像["置中心"](self.图像, 0, -227)
  self[self.类型]["重置"](self[self.类型], self.数据)
end
local 关闭 = 修炼升级["创建我的按钮"](修炼升级, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 435, 0)
function 关闭:左键弹起(x, y, msg)
  修炼升级["置可见"](修炼升级, false)
end
local 人物修炼列表 = 修炼升级["创建列表"](修炼升级, "人物修炼列表", 22, 50, 436, 161)
function 人物修炼列表:初始化()
  self:置文字(字体20)
  self.行高度 = 32
  self.行间距 = 0
  self.选中精灵 = require('SDL.精灵')(0, 0, 0, self.宽度, 0):置颜色(255, 0, 240, 128)
  self.焦点精灵 = nil
end

function 人物修炼列表:重置(data)
  local lsb = {
    "攻击修炼",
    "防御修炼",
    "法术修炼",
    "抗法修炼",
    "猎术修炼"
  }
  self.清空(self)
  for _, v in ipairs(lsb) do
    local nsf = require("SDL.图像")(436, 32)
    if nsf["渲染开始"](nsf) then
      if 1 == _ % 2 then
      end
      字体16["置颜色"](字体16, __取颜色("浅黑"))
      字体16["取图像"](字体16, v .. "      等级：" .. data[v][1] .. "/" .. data[v][3] .. "      修炼经验" .. data[v][2] .. "/" .. 计算修炼等级经验(data[v][1], data[v][3]))["显示"](字体16["取图像"](字体16, v .. "      等级：" .. data[v][1] .. "/" .. data[v][3] .. "      修炼经验" .. data[v][2] .. "/" .. 计算修炼等级经验(data[v][1], data[v][3])), 44, 7)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
  -- 人物修炼列表.选中行=1
  -- self.选中行=1
end
function 人物修炼列表:左键弹起(x, y, i, item, msg)
  修炼升级["选中"] = i
  -- print(人物修炼列表.选中行)
end




local 召唤兽控制列表 = 修炼升级["创建列表"](修炼升级, "召唤兽控制列表", 20, 50, 436, 161)
function 召唤兽控制列表:初始化()
  self:置文字(字体20)
  self.行高度 = 32
  self.行间距 = 10
  self.选中精灵 = require('SDL.精灵')(0, 0, 0, self.宽度, 0):置颜色(255, 0, 240, 128)
  self.焦点精灵 = nil
end

function 召唤兽控制列表:重置(data)
  local lsb = {
    "攻击控制力",
    "防御控制力",
    "法术控制力",
    "抗法控制力"
  }
  self.清空(self)
  for _, v in ipairs(lsb) do
    local nsf = require("SDL.图像")(436, 32)
    if nsf["渲染开始"](nsf) then
      if 1 == _ % 2 then
      end
      字体16["置颜色"](字体16, __取颜色("浅黑"))
      字体16["取图像"](字体16, v .. "      等级：" .. data[v][1] .. "/" .. data[v][3] .. "      修炼经验" .. data[v][2] .. "/" .. 计算修炼等级经验(data[v][1], data[v][3]))["显示"](字体16["取图像"](字体16, v .. "      等级：" .. data[v][1] .. "/" .. data[v][3] .. "      修炼经验" .. data[v][2] .. "/" .. 计算修炼等级经验(data[v][1], data[v][3])), 34, 7)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 召唤兽控制列表:左键弹起(x, y, i, item, msg)
  修炼升级["选中"] = i
end














local as = {"攻击修炼","防御修炼","法术修炼","抗法修炼","猎术修炼"}
local bbx = {"攻击控制力","防御控制力","法术控制力","抗法控制力"}
local 修炼an = 修炼升级["创建我的按钮"](修炼升级, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 123, 41), "修炼", 195, 305, "修炼")
function 修炼an:左键弹起(x, y, msg)
  if 修炼升级["选中"] then
    if 修炼升级.leix=="人物修炼" then
        发送数据(44,{类型=修炼升级.leix,免资材=修炼升级.免资材,修炼项目=as[修炼升级.选中]})
    else
      发送数据(44,{类型=修炼升级.leix,免资材=修炼升级.免资材,修炼项目=bbx[修炼升级.选中]})
    end
  end
  --   发送数据(44, {
  --     ["类别"] = lsb[修炼升级["选中"]]
  --   })
  -- else
  --   __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请选择你要修炼的项目")
  -- end
end
