__UI弹出["队伍弹出"] = __UI界面["创建弹出窗口"](__UI界面, "队伍弹出", 623-50+63 + abbr.py.x, 284+45 + abbr.py.y, 150, 192)
local 队伍弹出 = __UI弹出["队伍弹出"]
function 队伍弹出:初始化()
  self:置精灵(取黑色背景(0, 0, 150, 136))
end
function 队伍弹出:打开()
  if self.是否可见 then
    self:置可见(false)
  else
    self:置可见(true)
  end
end
local lsan = {
  "创建队伍",
  "加入队伍",
  -- "组队平台"
}
for i = 1, #lsan do
  local 临时函数 = 队伍弹出["创建我的按钮"](队伍弹出, __res:getPNGCC(2, 368, 906, 126, 52, true), lsan[i] .. "按钮", 12, 11 + (i - 1) * 60, lsan[i])
 function  临时函数:左键弹起(x, y, msg)
    if "创建队伍" == lsan[i] then
      发送数据(4002, {
        id = 角色信息["数字id"]
      })
    elseif "加入队伍" == lsan[i] then
      __UI界面["界面层"]["重置"](__UI界面["界面层"], "组队")
    elseif "组队平台" == lsan[i] then
    end
    队伍弹出["置可见"](队伍弹出, false)
  end
end
