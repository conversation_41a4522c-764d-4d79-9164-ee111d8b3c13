local SDL = require("SDL")
local 助战法术 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "助战法术", 292, 110 + abbr.py.y, 470+56, 404)
function 助战法术:初始化()
  local nsf = require("SDL.图像")(470, 406)
  if nsf["渲染开始"](nsf, 0, 0, 0, 0) then
    __res:getPNGCC(2, 0, 63, 354, 396)["显示"](__res:getPNGCC(2, 0, 63, 354, 396), 0, 10)
    local 宽度 = 字体20["取宽度"](字体20, "法术")
    字体20["置颜色"](字体20, 255, 255, 255)
    字体20["取图像"](字体20, "法术")["显示"](字体20["取图像"](字体20, "法术"), 177.0 - 宽度 / 2+0, 16)
    -- 字体18["置颜色"](字体18, __取颜色("黄色"))
    -- 字体18["取图像"](字体18, "提示：点击技能按钮两次使用技能")["显示"](字体18["取图像"](字体18, "提示：点击技能按钮两次使用技能"), 32, 57)
    nsf["渲染结束"](nsf)
  end
  self.hbk= __res:getPNGCC(5, 904, 215, 198, 57):到精灵()
  self:置精灵(nsf["到精灵"](nsf))
end
local ryst={}
ryst["当头一棒"]=1
ryst["神针撼海"]=1
ryst["杀威铁棒"]=1
ryst["泼天乱棒"]=1
ryst["九幽除名"]=1
ryst["云暗天昏"]=1
ryst["铜头铁臂"]=1
ryst["无所遁形"]=1
ryst["棒掀北斗"]=1
ryst["兴风作浪"]=1
ryst["棍打诸神"]=1
ryst["意马心猿"]=1
ryst["棒打雄风"]=1
function 助战法术:打开(技能组, lx, silllx,追加法术)
  self:置可见(true)
--   __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], false)
  if "角色" == lx then
    self.类型 = "人物"
  elseif "bb" == lx then
    self.类型 = "召唤兽"
  elseif "子女" == lx then
    self.类型 = "子女"
  end
  self.法术类型 = silllx
  self.选中 = nil
  self.图像2=nil
  local jinengzu={}
  if 追加法术 then
    self.大唐技能:置可见(true)
    self.大唐技能.大唐jn网格:置技能(追加法术)
  else
    self.大唐技能:置可见(false)
    local ruyishentong={}
    local go=false
    for w=1,20 do
      if 技能组[w] then
        if ryst[技能组[w].名称] then
          go=true
          ruyishentong[#ruyishentong+1]=技能组[w]
        else
          jinengzu[#jinengzu+1]=技能组[w]
        end
      end
    end
    if go and #ruyishentong>1 then
      self.花果山技能:置可见(true)
      self.花果山技能.花果山jn网格:置技能(ruyishentong)
    else
      self.花果山技能:置可见(false)
    end
  end
  self:置技能(jinengzu)
end
local 关闭 = 助战法术["创建我的按钮"](助战法术, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 0, 0)
function 关闭:左键弹起(x, y, msg)
  __UI界面["窗口层"]["助战法术"]:置可见(false)
--   __UI界面["界面层"]["战斗界面"]["重置"](__UI界面["界面层"]["战斗界面"])
--   __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], true)
end
local 技能网格 = 助战法术["创建网格"](助战法术, "技能网格", 13, 70, 325, 320)
function 技能网格:初始化()
  self:创建格子(100, 100, 15, 12, 9, 3, true)
end
function 技能网格:左键单击(x, y, a, b, msg)
  -- print(111)
  if self.子控件[a]._spr and not self.子控件[a]["技能信息"]["剩余冷却回合"] then
    if 调试模式 then
      print("调试模式输出：",self.子控件[a]["技能信息"].名称)
    end
    -- if not 助战法术["选中"] or 助战法术["选中"] ~= a then
      -- 助战法术["选中"] = a
    -- else
      __UI界面["窗口层"]["助战法术"]:置可见(false)
    --   __UI界面["界面层"]["战斗界面"]:置可见(true)
      __UI界面["界面层"]["助战操作界面"]:设置法术参数(self.子控件[a]["技能信息"], 助战法术["法术类型"])
    -- end
  end
end
function 助战法术:置技能(data)
  self.技能数据 = data
  for i = 1, #技能网格["子控件"] do
    if self.技能数据[i] then
      local nsf = require("SDL.图像")(100, 100)
      local lssc = 取技能(self.技能数据[i]["名称"], self.类型)
      local wenj="shape/jn/"
            if lssc[10] then
                wenj="shape/xinzengsucai/"
            end
      if nsf["渲染开始"](nsf) then
        nsf["渲染清除"](nsf, 0, 0, 0, 255)
        if self.技能数据[i]["剩余冷却回合"] then
          __res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7])), 55, 55)["到灰度"]((__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7])), 55, 55)))["显示"](__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7])), 55, 55)["到灰度"]((__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7])), 55, 55))), 20, 20)
        else
          __res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7])), 55, 55)["显示"](__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, wenj, lssc[7])), 55, 55), 20, 20)
        end
        __主控["公用蒙版2"]["显示"](__主控["公用蒙版2"], -4, -3)
        local 宽度 = 字体20["取宽度"](字体20, self.技能数据[i]["名称"])
        字体18["置颜色"](字体18, 255, 255, 255)
        字体18:取图像(self.技能数据[i]["名称"]):显示(50.0 - 宽度 / 2, 80)
        nsf["渲染结束"](nsf)
      end
      local sc = nsf["到精灵"](nsf)
      -- sc["置混合"](sc, 2)
      技能网格["子控件"][i]["置精灵"](技能网格["子控件"][i], sc)
      技能网格["子控件"][i]["技能信息"] = self.技能数据[i]
    else
      技能网格["子控件"][i]["置精灵"](技能网格["子控件"][i])
    end
  end
end


local 大唐技能 = 助战法术["创建控件"](助战法术, "大唐技能", 360, 111, 200, 265)

function 大唐技能:初始化()
  local nsf = require("SDL.图像")(200, 265)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(4, 1019, 407, 168, 257):显示(0, 0)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 助战法术:qusadwr(jn)
  self.图像2=nil
  local nsf = require("SDL.图像")(50, 50)
  if nsf["渲染开始"](nsf) then
    local lssc = 取技能(jn)
    -- table.print(lssc)
    local wenj="shape/jn/"
            if lssc[10] then
                wenj="shape/xinzengsucai/"
            end
    __res:取图像(__res["取地址"](__res, wenj, lssc[7])):拉伸(40, 40):显示(-3, -2)
    __res:getPNGCC(4, 1078, 603, 50, 50):显示(-3, -2)
    nsf["渲染结束"](nsf)
  end
  self.图像2 = nsf["到精灵"](nsf)
  self.图像2:置中心(-73+15-4-360,-304-6)
end

local 大唐jn网格 = 大唐技能["创建网格"](大唐技能, "大唐jn网格", 30, 33, 200, 265)
function 大唐jn网格:初始化()
  self:创建格子(40, 40, 4,  69-40, 3, 2, false)
end
function 大唐jn网格:左键单击(x, y, a, b, msg)
  -- print(111)
  if self.子控件[a]._spr and self.子控件[a]["技能信息"]["可选"] then
    if 调试模式 then
   --   print(self.子控件[a]["技能信息"].名称)
    end
      __UI界面["窗口层"]["战斗法术"]["置可见"](__UI界面["窗口层"]["战斗法术"], false)
      __UI界面["界面层"]["战斗界面"]["置可见"](__UI界面["界面层"]["战斗界面"], true)
      __UI界面["界面层"]["战斗界面"]:设置披坚执锐(self.子控件[a]["技能信息"], a)
  end
end
function 大唐jn网格:置技能(data)
  self.大唐jn数据 = data
  助战法术:qusadwr(data[data.可用编号].名称)
  -- table.print(data)
  for i = 1, #self["子控件"] do
    if self.大唐jn数据[i] then
      local nsf = require("SDL.图像")(40, 40)
      local lssc = 取技能(self.大唐jn数据[i]["名称"], self.类型)
      local wenj="shape/jn/"
            if lssc[10] then
                wenj="shape/xinzengsucai/"
            end
      if nsf["渲染开始"](nsf) then
        if self.大唐jn数据.可选编号~=i then
          __res:取图像(__res["取地址"](__res, wenj, lssc[7])):到灰度():拉伸(40, 40):显示(0, 0)
        else
          __res:取图像(__res["取地址"](__res, wenj, lssc[7])):拉伸(40, 40):显示(0, 0)
          self.大唐jn数据[i].可选=true
        end
        nsf["渲染结束"](nsf)
      end
      local sc = nsf["到精灵"](nsf)
      self["子控件"][i]["置精灵"](self["子控件"][i], sc)
      self["子控件"][i]["技能信息"] = self.大唐jn数据[i]
    else
      self["子控件"][i]["置精灵"](self["子控件"][i])
    end
  end
end



local 花果山技能 = 助战法术["创建控件"](助战法术, "花果山技能", 360, 100, 180, 320)

function 花果山技能:初始化()
  local nsf = require("SDL.图像")(180, 320)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(4, 1019, 668, 166, 311):显示(0, 0)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
local 花果山jn网格 = 花果山技能["创建网格"](花果山技能, "花果山jn网格", 26, 33, 130, 230)
function 花果山jn网格:初始化()
  self:创建格子(40, 40, 3,  69-40, 5, 2, false)
end
function 花果山jn网格:左键单击(x, y, a, b, msg)
  if self.子控件[a]._spr and not self.子控件[a]["技能信息"]["剩余冷却回合"] then
    if 调试模式 then
    --  print(self.子控件[a]["技能信息"].名称)
    end
    -- if not 助战法术["选中"] or 助战法术["选中"] ~= a then
      -- 助战法术["选中"] = a
    -- else
    __UI界面["窗口层"]["助战法术"]:置可见(false)
    --   __UI界面["界面层"]["战斗界面"]:置可见(true)
      __UI界面["界面层"]["助战操作界面"]:设置法术参数(self.子控件[a]["技能信息"], 助战法术["法术类型"])
    -- end
  end
end
function 花果山jn网格:置技能(data)
  self.花果山jn数据 = data
  -- table.print(data)
  for i = 1, #self["子控件"] do
    if self.花果山jn数据[i] then
      local nsf = require("SDL.图像")(40, 40)
      local lssc = 取技能(self.花果山jn数据[i]["名称"], self.类型)
      local wenj="shape/jn/"
      if lssc[10] then
          wenj="shape/xinzengsucai/"
      end
      if nsf["渲染开始"](nsf) then
        if self.花果山jn数据[i]["剩余冷却回合"] then
          __res:取图像(__res["取地址"](__res,wenj, lssc[7])):到灰度():拉伸(40, 40):显示(0, 0)
        else
          __res:取图像(__res["取地址"](__res,wenj, lssc[7])):拉伸(40, 40):显示(0, 0)
          self.花果山jn数据[i].可选=true
        end
        nsf["渲染结束"](nsf)
      end
      local sc = nsf["到精灵"](nsf)
      self["子控件"][i]["置精灵"](self["子控件"][i], sc)
      self["子控件"][i]["技能信息"] = self.花果山jn数据[i]
    else
      self["子控件"][i]["置精灵"](self["子控件"][i])
    end
  end
end