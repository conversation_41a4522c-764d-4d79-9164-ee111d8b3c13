--[[
移动端虚拟摇杆控件
LastEditTime: 2025-01-24
--]]

local SDL = require 'SDL'

local 摇杆控件 = {}
摇杆控件.__index = 摇杆控件

function 摇杆控件:new(父控件, x, y, 大小)
    local obj = {}
    setmetatable(obj, 摇杆控件)
    
    obj.父控件 = 父控件
    obj.x = x or 50
    obj.y = y or (引擎.高度 - 150)
    obj.大小 = 大小 or 80
    obj.摇杆大小 = obj.大小 // 3
    
    -- 摇杆状态
    obj.是否按下 = false
    obj.中心点 = {x = obj.x + obj.大小//2, y = obj.y + obj.大小//2}
    obj.摇杆位置 = {x = obj.中心点.x, y = obj.中心点.y}
    obj.最大范围 = obj.大小//2 - obj.摇杆大小//2
    
    -- 移动状态
    obj.移动方向 = {x = 0, y = 0}
    obj.移动强度 = 0
    obj.移动阈值 = 0.2 -- 最小移动阈值
    obj.上次移动方向 = {x = 0, y = 0} -- 记录上次移动方向，避免重复设置相同目标
    
    -- 持续移动状态
    obj.是否持续移动 = false
    obj.持续移动目标 = nil
    
    -- 视觉相关
    obj.透明度 = 120
    -- 读取配置决定是否显示（默认显示）- 安全检查
    if __res and __res.配置 then
        if not __res.配置.显示摇杆 then
            __res.配置.显示摇杆 = 1
        end
        obj.是否可见 = __手机 and (__res.配置.显示摇杆 == 1) -- 只在移动端且配置开启时显示
    else
        obj.是否可见 = __手机 -- 如果配置未加载，默认显示（仅移动端）
    end
    
    -- 摇杆隐藏功能
    obj.是否隐藏 = false
    obj.标题点击区域 = {x = obj.x + obj.大小//2 - 20, y = obj.y - 20, w = 40, h = 20}
    
    -- 移动计时器
    obj.移动计时 = 0
    obj.移动间隔 = 0.15 -- 每150ms执行一次移动，与触摸移动保持一致
    
    return obj
end

function 摇杆控件:初始化()
    -- 尝试使用现有UI素材创建摇杆
    if __res and __res.取资源动画 then
        -- 尝试多种素材资源
        local 成功 = false
        
        -- 尝试使用技能框素材
        if not 成功 then
            local success, 背景 = pcall(function()
                return __res:取资源动画("jszy/ui", 0x00000089, "图像"):拉伸(self.大小, self.大小):到灰度():到精灵()
            end)
            if success and 背景 then
                self.背景精灵 = 背景
                
                local success2, 摇杆 = pcall(function()
                    return __res:取资源动画("jszy/ui", 0x00000089, "图像"):拉伸(self.摇杆大小, self.摇杆大小):到精灵()
                end)
                if success2 and 摇杆 then
                    self.摇杆精灵 = 摇杆
                    成功 = true
                end
            end
        end
        
        -- 如果上面失败，尝试使用按钮素材
        if not 成功 then
            local success, 背景 = pcall(function()
                return __res:取资源动画("jszy/fwtb", 0xBAF6A95D, "图像"):拉伸(self.大小, self.大小):到灰度():到精灵()
            end)
            if success and 背景 then
                self.背景精灵 = 背景
                
                local success2, 摇杆 = pcall(function()
                    return __res:取资源动画("jszy/fwtb", 0xBAF6A95D, "图像"):拉伸(self.摇杆大小, self.摇杆大小):到精灵()
                end)
                if success2 and 摇杆 then
                    self.摇杆精灵 = 摇杆
                    成功 = true
                end
            end
        end
        
        self.使用精灵 = 成功
    else
        self.使用精灵 = false
    end
    
    -- 备用方案：计算圆形像素点用于绘制
    if not self.使用精灵 then
        self.背景圆形 = {}
        self.摇杆圆形 = {}
        
        for i = 0, 360, 5 do
            local 角度 = math.rad(i)
            local 背景半径 = self.大小 // 2
            local 摇杆半径 = self.摇杆大小 // 2
            
            table.insert(self.背景圆形, {
                x = math.cos(角度) * 背景半径,
                y = math.sin(角度) * 背景半径
            })
            
            table.insert(self.摇杆圆形, {
                x = math.cos(角度) * 摇杆半径,
                y = math.sin(角度) * 摇杆半径
            })
        end
    end
end

function 摇杆控件:切换隐藏状态()
    self.是否隐藏 = not self.是否隐藏
    if self.是否隐藏 then
        -- 隐藏时停止移动
        self.是否按下 = false
        self.摇杆位置.x = self.中心点.x
        self.摇杆位置.y = self.中心点.y
        self.移动方向.x = 0
        self.移动方向.y = 0
        self.移动强度 = 0
    end
end

function 摇杆控件:检查标题点击(x, y)
    if not self.是否可见 then
        return false
    end
    
    local 区域 = self.标题点击区域
    return x >= 区域.x and x <= 区域.x + 区域.w and y >= 区域.y and y <= 区域.y + 区域.h
end

function 摇杆控件:更新(dt)
    if not self.是否可见 or (_tp and _tp.战斗中) then
        return
    end
    
    -- 更新移动计时
    self.移动计时 = self.移动计时 + dt
    
    -- 摇杆持续移动逻辑
    if self.是否按下 and self.移动强度 > self.移动阈值 then
        -- 检查方向是否改变
        local 方向改变 = math.abs(self.移动方向.x - self.上次移动方向.x) > 0.2 or 
                        math.abs(self.移动方向.y - self.上次移动方向.y) > 0.2
        
        -- 方向改变或者还没开始持续移动，则设置新的持续移动目标
        if 方向改变 or not self.是否持续移动 then
            self:开始持续移动()
            -- 记录当前移动方向
            self.上次移动方向.x = self.移动方向.x
            self.上次移动方向.y = self.移动方向.y
        end
    elseif self.是否持续移动 then
        -- 摇杆松开，立即停止移动
        self:停止持续移动()
    end
end

function 摇杆控件:开始持续移动()
    if not __主显 or not __主显.主角 or not __主显.主角.xy then
        return
    end
    
    -- 检查是否可移动（使用原有的移动条件检查）
    if not __主显.主角:是否可移动() then
        return
    end
    
    -- 获取当前位置
    local 当前x = __主显.主角.xy.x
    local 当前y = __主显.主角.xy.y
    
    -- 计算最远的目标点，让角色持续移动
    local 移动距离 = 2000 -- 设置最远的目标距离，确保持续移动
    local 目标x = 当前x + self.移动方向.x * 移动距离
    local 目标y = 当前y + self.移动方向.y * 移动距离
    
    -- 创建目标坐标
    local 目标坐标 = require("GGE.坐标")(目标x, 目标y)
    
    -- 基本边界检查
    local 目标格子x = math.floor(目标x / 20)
    local 目标格子y = math.floor(目标y / 20)
    if 目标格子x < 0 then 目标格子x = 0 end
    if 目标格子y < 0 then 目标格子y = 0 end
    if 目标格子x > 1000 then 目标格子x = 1000 end
    if 目标格子y > 1000 then 目标格子y = 1000 end
    
    -- 重新计算边界内的目标坐标
    目标坐标 = require("GGE.坐标")(目标格子x * 20, 目标格子y * 20)
    
    -- 使用主角原有的移动方法开始持续移动
    local 移动成功 = false
    
    -- 方式1: 使用主角的设置路径方法（最标准的方式）
    if __主显.主角.设置路径 and type(__主显.主角.设置路径) == "function" then
        __主显.主角:设置路径(目标坐标)
        移动成功 = true
    -- 方式2: 直接使用开始移动方法
    elseif __主显.主角.开始移动 and type(__主显.主角.开始移动) == "function" then
        __主显.主角:开始移动(目标坐标)
        移动成功 = true
    -- 方式3: 使用网络请求（备用方案）
    elseif 请求服务 and type(请求服务) == "function" then
        请求服务(1001, {x = 目标格子x, y = 目标格子y, 距离 = 0})
        移动成功 = true
    end
    
    if 移动成功 then
        self.是否持续移动 = true
        self.持续移动目标 = 目标坐标
    end
end

function 摇杆控件:停止持续移动()
    if self.是否持续移动 then
        self.是否持续移动 = false
        self.持续移动目标 = nil
        
        -- 立即停止主角移动
        if __主显 and __主显.主角 and __主显.主角.停止移动 then
            __主显.主角:停止移动()
        end
    end
end

function 摇杆控件:显示(x, y)
    if not self.是否可见 or (_tp and _tp.战斗中) then
        return
    end
    
    -- 摇杆使用固定的屏幕位置，不受传入的x,y影响
    local 显示x = self.x
    local 显示y = self.y
    
    -- 摇杆标识 - 支持隐藏状态
    if 文本字体 and 文本字体.置颜色 then
        if self.是否隐藏 then
            -- 隐藏状态：在左边缘中间显示"摇杆"按钮
            文本字体:置颜色(255, 255, 0, 255):显示(5, 引擎.高度 // 2 - 10, "摇杆")
        else
            -- 正常状态：在摇杆上方显示"摇杆"
            文本字体:置颜色(255, 255, 255, 200):显示(显示x + self.大小//2 - 10, 显示y - 15, "摇杆")
        end
    end
    
    -- 如果隐藏状态，不显示摇杆本体
    if self.是否隐藏 then
        return
    end
    
    if self.使用精灵 and self.背景精灵 and self.摇杆精灵 then
        -- 使用精灵显示摇杆
        -- 显示背景
        self.背景精灵:显示(显示x, 显示y)
        
        -- 显示摇杆球
        local 摇杆显示x = 显示x + (self.摇杆位置.x - self.中心点.x) + (self.大小 - self.摇杆大小)//2
        local 摇杆显示y = 显示y + (self.摇杆位置.y - self.中心点.y) + (self.大小 - self.摇杆大小)//2
        self.摇杆精灵:显示(摇杆显示x, 摇杆显示y)
    else
        -- 使用备用显示方案
        -- 绘制背景圆形（半透明灰色）
        self:绘制圆形(显示x + self.大小//2, 显示y + self.大小//2, self.大小//2, {128, 128, 128, self.透明度})
        
        -- 绘制摇杆圆形（半透明白色）
        local 摇杆显示x = 显示x + (self.摇杆位置.x - self.中心点.x) + self.大小//2
        local 摇杆显示y = 显示y + (self.摇杆位置.y - self.中心点.y) + self.大小//2
        self:绘制圆形(摇杆显示x, 摇杆显示y, self.摇杆大小//2, {255, 255, 255, self.透明度 + 50})
        
        -- 添加更明显的边框
        if 文本字体 and 文本字体.置颜色 then
            文本字体:置颜色(255, 255, 0, 255):显示(显示x, 显示y, "┌")
            文本字体:置颜色(255, 255, 0, 255):显示(显示x + self.大小 - 10, 显示y, "┐")
            文本字体:置颜色(255, 255, 0, 255):显示(显示x, 显示y + self.大小 - 10, "└")
            文本字体:置颜色(255, 255, 0, 255):显示(显示x + self.大小 - 10, 显示y + self.大小 - 10, "┘")
        end
    end
end

function 摇杆控件:绘制圆形(中心x, 中心y, 半径, 颜色)
    -- 简化的圆形绘制 - 使用矩形模拟
    local 矩形大小 = 半径 * 2
    
    -- 如果有绘制API，使用绘制API
    if 引擎 and 引擎.绘制矩形 and type(引擎.绘制矩形) == "function" then
        引擎:绘制矩形(中心x - 半径, 中心y - 半径, 矩形大小, 矩形大小, 颜色)
    else
        -- 使用文字作为替代显示 - 检查字体是否存在
        if 半径 > 20 then
            -- 背景圆形用更大的"●"
            if 说明字体 and 说明字体.置颜色 then
                说明字体:置颜色(颜色[1], 颜色[2], 颜色[3], 颜色[4] or 255):显示(中心x - 15, 中心y - 15, "●●●")
                说明字体:置颜色(颜色[1], 颜色[2], 颜色[3], 颜色[4] or 255):显示(中心x - 15, 中心y - 5, "●●●")
                说明字体:置颜色(颜色[1], 颜色[2], 颜色[3], 颜色[4] or 255):显示(中心x - 15, 中心y + 5, "●●●")
            elseif 文本字体 and 文本字体.置颜色 then
                文本字体:置颜色(颜色[1], 颜色[2], 颜色[3], 颜色[4] or 255):显示(中心x - 15, 中心y - 15, "●●●")
                文本字体:置颜色(颜色[1], 颜色[2], 颜色[3], 颜色[4] or 255):显示(中心x - 15, 中心y - 5, "●●●")
                文本字体:置颜色(颜色[1], 颜色[2], 颜色[3], 颜色[4] or 255):显示(中心x - 15, 中心y + 5, "●●●")
            end
        else
            -- 摇杆用"◎"
            if 文本字体 and 文本字体.置颜色 then
                文本字体:置颜色(颜色[1], 颜色[2], 颜色[3], 颜色[4] or 255):显示(中心x - 8, 中心y - 8, "◎")
            end
        end
    end
end

function 摇杆控件:检查点击(点击x, 点击y)
    if not self.是否可见 or (_tp and _tp.战斗中) then
        return false
    end
    
    -- 检查是否点击了标题区域（用于切换隐藏状态）
    if self:检查标题点击(点击x, 点击y) then
        return "标题"
    end
    
    -- 如果摇杆隐藏，检查是否点击边缘的"摇杆"按钮
    if self.是否隐藏 then
        if 点击x >= 5 and 点击x <= 50 and 点击y >= 引擎.高度 // 2 - 15 and 点击y <= 引擎.高度 // 2 + 15 then
            return "边缘按钮"
        end
        return false
    end
    
    -- 使用绝对屏幕坐标检查点击 - 点击坐标与摇杆中心的距离
    local 相对x = 点击x - self.中心点.x
    local 相对y = 点击y - self.中心点.y
    
    -- 检查是否在摇杆范围内
    local 距离平方 = 相对x * 相对x + 相对y * 相对y
    local 半径平方 = (self.大小//2) * (self.大小//2)
    
    return 距离平方 <= 半径平方
end

-- 添加检查是否在摇杆区域内的方法，用于事件拦截
function 摇杆控件:是否在摇杆区域内(x, y)
    if not self.是否可见 or (_tp and _tp.战斗中) then
        return false
    end
    
    -- 如果隐藏状态，检查边缘按钮区域
    if self.是否隐藏 then
        return x >= 5 and x <= 50 and y >= 引擎.高度 // 2 - 15 and y <= 引擎.高度 // 2 + 15
    end
    
    -- 检查摇杆区域（包括标题区域）
    local 摇杆区域 = (x - self.中心点.x)^2 + (y - self.中心点.y)^2 <= (self.大小//2)^2
    local 标题区域 = self:检查标题点击(x, y)
    
    return 摇杆区域 or 标题区域
end

function 摇杆控件:鼠标按下(x, y)
    local 点击结果 = self:检查点击(x, y)
    if not 点击结果 then
        return false
    end
    
    -- 处理标题点击 - 切换隐藏状态
    if 点击结果 == "标题" then
        self:切换隐藏状态()
        return true -- 拦截事件，防止穿透
    end
    
    -- 处理边缘按钮点击 - 显示摇杆
    if 点击结果 == "边缘按钮" then
        self:切换隐藏状态()
        return true -- 拦截事件，防止穿透
    end
    
    -- 正常摇杆操作
    if 点击结果 == true then
        self.是否按下 = true
        self:更新摇杆位置(x, y)
        return true -- 拦截事件，防止穿透到聊天界面
    end
    
    return false
end

function 摇杆控件:鼠标移动(x, y)
    if not self.是否按下 then
        return false
    end
    
    self:更新摇杆位置(x, y)
    return true -- 拦截事件，防止穿透
end

function 摇杆控件:鼠标释放(x, y)
    if not self.是否按下 then
        return false
    end
    
    self.是否按下 = false
    
    -- 停止持续移动
    self:停止持续移动()
    
    -- 摇杆回到中心
    self.摇杆位置.x = self.中心点.x
    self.摇杆位置.y = self.中心点.y
    self.移动方向.x = 0
    self.移动方向.y = 0
    self.移动强度 = 0
    
    -- 重置上次移动方向
    self.上次移动方向.x = 0
    self.上次移动方向.y = 0
    
    return true -- 拦截事件，防止穿透
end

function 摇杆控件:更新摇杆位置(x, y)
    -- 使用绝对屏幕坐标计算相对位置
    local 相对x = x - self.中心点.x
    local 相对y = y - self.中心点.y
    
    -- 计算距离
    local 距离 = math.sqrt(相对x * 相对x + 相对y * 相对y)
    
    if 距离 > self.最大范围 then
        -- 限制在最大范围内
        local 角度 = math.atan2(相对y, 相对x)
        相对x = math.cos(角度) * self.最大范围
        相对y = math.sin(角度) * self.最大范围
        距离 = self.最大范围
    end
    
    -- 更新摇杆位置
    self.摇杆位置.x = self.中心点.x + 相对x
    self.摇杆位置.y = self.中心点.y + 相对y
    
    -- 计算移动方向和强度
    if 距离 > 0 then
        self.移动方向.x = 相对x / 距离
        self.移动方向.y = 相对y / 距离
        self.移动强度 = 距离 / self.最大范围
    else
        self.移动方向.x = 0
        self.移动方向.y = 0
        self.移动强度 = 0
    end
end

function 摇杆控件:设置可见(可见)
    self.是否可见 = 可见 and __手机
    
    -- 如果摇杆被隐藏，确保释放按下状态
    if not self.是否可见 and self.是否按下 then
        self.是否按下 = false
        self.摇杆位置.x = self.中心点.x
        self.摇杆位置.y = self.中心点.y
        self.移动方向.x = 0
        self.移动方向.y = 0
        self.移动强度 = 0
    end
end

function 摇杆控件:设置位置(x, y)
    self.x = x
    self.y = y
    self.中心点.x = x + self.大小//2
    self.中心点.y = y + self.大小//2
    
    -- 重置摇杆位置
    self.摇杆位置.x = self.中心点.x
    self.摇杆位置.y = self.中心点.y
end

return 摇杆控件 