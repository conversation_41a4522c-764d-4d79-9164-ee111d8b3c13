
if __手机 then
  __UI弹出.状态提示 = 界面:创建弹出窗口("状态提示", 0, 0)
else
  __UI弹出.状态提示 = 界面:创建提示控件("状态提示", 0, 0)
end

local 状态提示 = __UI弹出.状态提示
function 状态提示:初始化()
end
function 状态提示:打开(数据,x,y)
    self:置可见(true,true)
    self.显示名称 = nil
    self.状态文本:清空()
    self.状态文本:置文字(文本字体)
    local w, h = 0,0
    if 数据.名称 then
      self.显示名称=数据.名称
    end
    if 数据.介绍 then
        self.状态文本:置文本(数据.介绍)
       w = 200
       h = self.状态文本:取行数()*18
       self.状态文本:置宽高(200,h)
    end
   -- local nsf = 取九宫图像(__res:getPNGCC(2, 230, 964, 401, 52),w+20, h+35,10,true)
    local nsf = 取九宫图像(__res:取资源动画("dlzy", 0xB5FDF1AC,"图像"),w+20,h+35,20,true)
    if nsf:渲染开始() then
      if self.显示名称 then
          标题字体:置颜色(__取颜色("黄色"))
          标题字体:取图像(self.显示名称):显示(10,5)
      end
      nsf:渲染结束()
    end
    self:置精灵(nsf:到精灵())
    self:置宽高(w + 20, h + 35)
    local xx = 0
    local yy = 0
    if x + w+25 <引擎.宽度 then
        xx = x
    else
        xx = 引擎.宽度-(w+25)
    end
    if y + h +35 <引擎.高度 then
        yy = y
    else
        yy = 引擎.高度-(h+35)
    end
    if xx<5 then
       xx = 5
    end
    if yy <5 then
       yy = 5
    end
    self:置坐标(xx,yy)
end


local 状态文本 = 状态提示:创建文本("状态文本", 10, 30, 200, 40)
function 状态文本:初始化()
  self:置文字(文本字体)
end

