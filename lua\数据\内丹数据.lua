function 取内丹数据(wd,s,bb)
  local wds = {}
  if wd == "迅敏" then
    wds.说明 = "提升召唤兽伤害力与速度，提升效果受召唤兽自身等级影响。"
    wds.效果 = "增加"..math.floor((bb.等级*0.08)*s).."点伤害与"..math.floor((bb.等级*0.05)*s).."点速度"
    wds.资源 = "dlzy"
    wds.模型 = 0x6FA0B3A8
  elseif wd == "狂怒" then
      wds.说明 = "提升必杀时造成的伤害，受到水和土二系法术攻击时将承受额外的伤害"
      wds.效果 = "必杀时增加"..(60+s*20).."点伤害，#Y额外受到15%水和土系#Y法术伤害"
      wds.资源 = "dlzy"
      wds.模型 = 0x956BD457
  elseif wd == "阴伤" then
      wds.说明 = "提升连击第二次造成的伤害，受到火和雷二系法术攻击时将承受额外的伤害"
      wds.效果 = "连击时增加"..(40+s*10).."点伤害，#Y额外受到15%火和雷系#Y法术伤害"
      wds.资源 = "dlzy"
      wds.模型 = 0x3EF0A9BF
  elseif wd == "静岳" then
      wds.说明 = "提升召唤兽灵力与气血，提升效果受召唤兽自身等级影响。"
      wds.效果 = "增加"..math.floor((bb.等级*0.04)*s).."点灵力与"..math.floor((bb.等级*0.4)*s).."点气血"
      wds.资源 = "dlzy"
      wds.模型 = 0xF58C6B1D
  elseif wd == "擅咒" then
      wds.说明 = "提升召唤兽对目标的法术伤害。"
      wds.效果 = "法术伤害结果增加"..math.floor(12*s).."点。"
      wds.资源 = "dlzy"
      wds.模型 = 0x4D74B795
  elseif wd == "灵身" then
      wds.说明 = "提升法术暴击时的伤害，但是受到强力技能攻击时承受额外50%物理伤害"
      wds.效果 = "法术暴击伤害增加"..(7*s).."%".."，#Y承受额外50%物理伤害"
      wds.资源 = "dlzy"
      wds.模型 = 0x55124270
   elseif wd == "矫健" then
      wds.说明 = "提升召唤兽气血与速度，提升效果受召唤兽自身等级影响。"
      wds.效果 = "增加"..math.floor((bb.等级*0.5)*s).."点气血与"..math.floor((bb.等级*0.05)*s).."点速度。"
      wds.资源 = "dlzy"
      wds.模型 = 0x3877515B
  elseif wd == "深思" then
      wds.说明 = "提升高冥思技能效果。"
      wds.效果 = "高冥思效果增加"..(s*5).."%"
      wds.资源 = "dlzy"
      wds.模型 = 0x9912B979
  elseif wd == "钢化" then
      wds.说明 = "拥有防御或高级防御技能的召唤兽能提升防御效果，但在受到除固定伤害外的其他法术攻击时，受到的伤害增加。"
      wds.效果 = "增加召唤兽"..math.floor((bb.等级*0.2)*s).."的防御（该召唤兽必须携带防御/高级防御技能） 所受法术伤害增加10%。"
      wds.资源 = "dlzy"
      wds.模型 = 0x89C1F027
  elseif wd == "坚甲" then
      wds.说明 = "拥有反震技能的召唤兽能提升反震伤害。"
      wds.效果 = "所造成的反震伤害增加"..(100*s).."点。"
      wds.资源 = "dlzy"
      wds.模型 = 0x2788B6E8
  elseif wd == "慧心" then
      wds.说明 = "提升召唤兽抵抗封印几率。"
      wds.效果 = "增加"..(s*6).."%的抗封印几率。"
      wds.资源 = "dlzy"
      wds.模型 = 0xB6A27748
  elseif wd == "撞击" then
      wds.说明 = "提升召唤兽物理攻击命中几率，提升效果受召唤兽体质点影响，同时提升一定的伤害结果。"
      wds.效果 = "物理攻击时增加"..math.floor(0.006*bb.体质*s).."%命中几率，同时增加"..math.floor(5*s).."点伤害结果。"
      wds.资源 = "dlzy"
      wds.模型 = 0xE837F9B1
  elseif wd == "无畏" then
      wds.说明 = "提升对拥有反震技能目标的物理伤害"
      wds.效果 = "对待有反震技能的目标造成的物理伤害增加"..(2*s).."%"
      wds.资源 = "dlzy"
      wds.模型 = 0xE8FDD3F4
  elseif wd == "愤恨" then
      wds.说明 = "提升对拥有幸运技能目标的物理伤害"
      wds.效果 = "对待有幸运技能的目标造成的物理伤害增加"..(2*s).."%"
      wds.资源 = "dlzy"
      wds.模型 = 0x9F97DB7F
  elseif wd == "淬毒" then
      wds.说明 = "提升毒技能的中毒触发几率"
      wds.效果 = "命中后致毒的几率增加"..(5*s).."%"
      wds.资源 = "dlzy"
      wds.模型 = 0x4A491950
  elseif wd == "狙刺" then
      wds.说明 = "提升召唤兽对施法选定目标的法术伤害，提升效果受召唤兽等级影响"
      wds.效果 = "对施法选定目标法术伤害增加等级"..math.floor(s*0.15*bb.等级).."点"
      wds.资源 = "dlzy"
      wds.模型 = 0x9762CCF9
  elseif wd == "连环" then
      wds.说明 = "提升连击技能触发连击的几率"
      wds.效果 = "连击的几率增加"..(s*2).."%。"
      wds.资源 = "dlzy"
      wds.模型 = 0xE2D19F8F
  elseif wd == "圣洁" then
      wds.说明 = "提升驱鬼对魂技能召唤兽的伤害"
      wds.效果 = "驱鬼的效果增加"..(s*10).."%"
      wds.资源 = "dlzy"
      wds.模型 = 0x809E53A3
  elseif wd == "灵光" then
      wds.说明 = "提升召唤兽法术伤害，提升效果受召唤兽自身法力点数影响。"
      wds.效果 = "增加"..math.floor((bb.魔力*0.02)*s).."点法术伤害"
      wds.资源 = "dlzy"
      wds.模型 = 0xC0EAFCA3
  elseif wd == "神机步" then
      wds.说明 = "进入战斗后三回合内提升召唤兽躲避力"
      wds.效果 = "进入战斗时3回合内的躲避值增加"..(s*20).."%"
      wds.资源 = "dlzy"
      wds.模型 = 0x19D18973
  elseif wd == "腾挪劲" then
      wds.说明 = "召唤兽受到物理攻击时有一定几率化解部分伤害"
      wds.效果 = "受到物理攻击时有"..(s*4).."%的几率抵挡50%的伤害"
      wds.资源 = "dlzy"
      wds.模型 = 0x06EF9E7B
  elseif wd == "玄武躯" then
      wds.说明 = "提升召唤兽气血，提升效果受召唤兽等级影响，但是将减少召唤兽所有攻击方式造成的伤害。"
      wds.效果 = "增加"..math.floor((bb.等级*2)*s).."点气血，对目标造成伤害减少50%"
      wds.资源 = "dlzy"
      wds.模型 = 0xC842B66C
  elseif wd == "龙胄铠" then
      wds.说明 = "提升召唤兽防御，提升效果受召唤兽等级影响，但是将减少召唤兽所有攻击方式造成的伤害。"
      wds.效果 = "增加"..math.floor((bb.等级*0.5)*s).."点防御，对目标造成伤害减少50%"
      wds.资源 = "dlzy"
      wds.模型 = 0x4E9FBEE0
  elseif wd == "玉砥柱" then
      wds.说明 = "降低受到宠物物理技能攻击时所承受伤害，将减少所有攻击造成的伤害"
      wds.效果 = "受召唤兽物理技能伤害减少"..(s*7).."%，对其他目标造成伤害减少20%"
      wds.资源 = "dlzy"
      wds.模型 = 0x5DAD21DF
   elseif wd == "碎甲刃" then
      wds.说明 = "召唤兽普通物理攻击时将有一定几率降低攻击目标的物理防御，效果持续两回合"
      wds.效果 = "普通攻击时30%几率降低攻击目标"..math.floor((5+(bb.力量-bb.等级)*0.15)*s).."点防御,效果持续两回合"
      wds.资源 = "dlzy"
      wds.模型 = 0xCDDAE9FF
  elseif wd == "阴阳护" then
      wds.说明 = "激发潜力增加召唤兽的魔法值"
      wds.效果 = "增加召唤兽的魔法值"..math.floor((bb.等级*0.5)*s).."点"
      wds.资源 = "dlzy"
      wds.模型 = 0x9041DD3F
  elseif wd == "凛冽气" then
      wds.说明 = "激发潜力增加召唤兽的速度"
      wds.效果 = "激发潜力增加召唤兽的速度"..math.floor((bb.等级*0.08)*s).."点"
      wds.资源 = "dlzy"
      wds.模型 = 0x38808205
  elseif wd == "舍身击" then
      wds.说明 = "提升物理攻击造成的伤害，提升效果受召唤兽自身力量点数影响。"
      wds.效果 = "增加物理攻击伤害结果"..math.floor((bb.力量-bb.等级)*0.05*s).."点"
      wds.资源 = "dlzy"
      wds.模型 = 0x10E2B4A7
  elseif wd == "电魂闪" then
      wds.说明 = "使用单体法术命中目标时将有一定几率驱散目标的某种增益状态。"
      wds.效果 = "单体法术命中目标时有"..math.floor(s*9).."%几率驱散目标随机一种状态"
      wds.资源 = "dlzy"
      wds.模型 = 0x624D3F68
  elseif wd == "通灵法" then
      wds.说明 = "提升召唤兽忽视目标法术减免效果的能力。"
      wds.效果 = "忽视目标"..s.."%的法防"
      wds.资源 = "dlzy"
      wds.模型 = 0xBD739B98
  elseif wd == "双星暴" then
      wds.说明 = "提升法术连击触发第二次法术攻击造成的伤害"
      wds.效果 = "法术连击伤害增加"..(s*10).."%"
      wds.资源 = "dlzy"
      wds.模型 = 0x8AD71FAF
  elseif wd == "催心浪" then
      wds.说明 = "提升术波动技能触发时的伤害波动下限"
      wds.效果 = "法术波动下限提升了"
      wds.资源 = "dlzy"
      wds.模型 = 0x5CEDA8EC
  elseif wd == "隐匿击" then
      wds.说明 = "拥有隐身技能的召唤兽伤害值提高"
      wds.效果 = "隐身状态下降低的伤害降低"..(2*s).."%，同时隐身结束后额外消耗100%的魔法值。"
      wds.资源 = "dlzy"
      wds.模型 = 0x0A21302B
  elseif wd == "生死决" then
      wds.说明 = "提升召唤兽将自身防御的一部分转化为伤害力的几率，提升效果持续到本回合结束。"
      wds.效果 = "增加"..(3+0.75*s).."%的狂暴几率"
      wds.资源 = "dlzy"
      wds.模型 = 0x2AA03A67
  elseif wd == "血债偿" then
      wds.说明 = "本方不带本技能召唤兽每被击飞一次，提升一次拥有本技能召唤兽对目标造成的法术伤害，持续到战斗结束。提升效果最多叠加5次，且受召唤兽自身魔力点数影响，不能与鬼魂和高级鬼魂技能共存。"
      wds.效果 = "本方每被击飞一个不带有此技能的召唤兽，自身对目标造成的法术伤害增加"..math.floor((bb.魔力-bb.等级)*0.04*s).."点，效果持续到战斗结束，最多可叠加5次，此技能与鬼魂术、高级鬼魂术冲突。"
      wds.资源 = "dlzy"
      wds.模型 = 0x97117DEE
  end
  return  wds
end
function 取内丹介绍(a)
	if a =="迅敏" then
		return "#Y拥有此技能会提高自己的伤害力与速度。"
	elseif a =="狂怒" then
		return "#Y在此狂乱状态下必杀的伤害更高，但是受到水、土系法术攻击时承受额外的伤害。"
	elseif a =="阴伤" then
		return "#Y疯狂攻击的第二次伤害更高，但是受到火、雷系法术攻击时承受额外的伤害。"
	elseif a =="静岳" then
	   return "#Y拥有此技能会提高自己的灵力与气血。"
	elseif a =="擅咒" then
	   return "#Y你对目标的法术伤害得到提升。"
	elseif a =="灵身" then
	   return "#Y法术带来的暴发性更强烈，承受额外50%物理伤害"
	elseif a =="矫健" then
	   return "#Y拥有此技能会提高自身的气血与速度。"
	elseif a =="深思" then
	   return "#Y高冥思的效果得到加强。"
	elseif a =="坚甲" then
	   return "#Y拥有此技能后对敌人造成的反震伤害得到加强。"
	elseif a =="钢化" then
	   return "#Y带有高级防御、防御技能时的防御值增加，但是所带来的代价是受到法术伤害额外打击。"
	elseif a =="慧心" then
	   return "#Y只要集中精神，抵御封印的能力就会加强。"
	elseif a =="撞击" then
	   return "#Y物理攻击时增加一定的伤害结果，效果与体质点成正比"
	elseif a =="无畏" then
	   return "#Y拥有此技能能够更好的突破反震、高级反震技能，对目标造成更大的物理伤害。"
	elseif a =="愤恨" then
	   return "#Y拥有此技能能够更好的突破幸运、高级幸运技能，对目标造成更大的物理伤害。"
	elseif a =="淬毒" then
	   return "#Y满淬毒汁的毒牙使对手更加胆寒。"
	elseif a =="狙刺" then
	   return "#Y该召唤兽对第一目标造成的法术伤害更大。"
	elseif a =="连环" then
		return "#Y拥有此技能时，召唤兽触发连击的几率增加。"
	elseif a =="圣洁" then
	    return "#Y拥有此技能后，你的召唤兽使用法术攻击时驱鬼和高驱鬼效果得到提升。"
	elseif a =="灵光" then
	    return "#Y法术的精修必然会为你带来更多好处。"
	elseif a =="神机步" then
	    return "#Y入场时你总是活力无限，3回合内极大的增加你的躲避力。"
	elseif a =="腾挪劲" then
	    return "#Y神奇的护盾，有一定几率能够将你所受的一部分物理伤害腾挪到另一个世界。"
	elseif a =="玄武躯" then
	    return "#Y你放弃了伤害，得到的是气血的大幅度提升。"
	elseif a =="龙胄铠" then
	    return "#Y你放弃了伤害，得到的是防御的大幅度提升。"
	elseif a =="玉砥柱" then
	    return "#Y最尖锐的矛也遇到了一点麻烦。"
	elseif a =="碎甲刃" then
	    return "#Y以千钧之力击碎目标的护甲，2回合内降低目标一定防御值，效果与自身力量点相关。"
	elseif a =="阴阳护" then
	     return "#Y激发潜力，增加召唤兽的魔法值"
	elseif a =="凛冽气" then
	     return "#Y激发潜力，增加召唤兽的敏捷"
	elseif a =="舍身击" then
	     return "#Y提升召唤兽的力量,随召唤兽等级提升而增加"
	elseif a =="电魂闪" then
	     return "#Y神奇的法术，总在不经意间给你惊喜，有可能将目标携带的增益状态驱散。"
	elseif a =="通灵法" then
	     return "#Y敏锐的洞察力！能够找出目标法术防御的漏洞，可以扣除目标一定的法术防御。"
	elseif a =="双星暴" then
	     return "#Y强大的法术攻击接踵而来，法术连击的威力更强大。"
	elseif a =="催心浪" then
	     return "#Y虽然带有法术波动技能时法术力量无法控制，但是总会向更好的方向发展。"
	elseif a =="隐匿击" then
	     return "#Y拥有隐身技能的召唤兽伤害值提高"
	elseif a =="生死决" then
	     return "#Y提升召唤兽将自身防御的一部分转化为伤害力的几率，提升效果持续到本回合结束。"
    elseif a =="血债偿" then
	     return "#Y本方不带本技能召唤兽每被击飞一次，提升一次拥有本技能召唤兽对目标造成的法术伤害，持续到战斗结束。提升效果最多叠加5次，且受召唤兽自身魔力点数影响，不能与鬼魂和高级鬼魂技能共存。"

	end

end
