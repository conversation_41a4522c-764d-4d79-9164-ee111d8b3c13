-- <AUTHOR> GGELUA
-- @Last Modified by    : GGELUA2
-- @Date                : 2024-08-14 00:02:21
-- @Last Modified time  : 2024-08-15 03:49:21

local 角色转换 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "角色转换", 80+67 + abbr.py.x, 20+42 + abbr.py.y, 683+20, 455)
local tioy={"逍遥生","剑侠客","偃无师","飞燕女","英女侠","巫蛮儿","巨魔王","虎头怪","杀破狼","狐美人","影精灵","鬼潇潇","羽灵神","神天兵","龙太子","舞天姬","玄彩娥","桃夭夭"}

local xxzb={}
xxzb["大唐官府"]=0
xxzb["方寸山"]=49
xxzb["花果山"]=98
xxzb["化生寺"]=147
xxzb["凌波城"]=196
xxzb["龙宫"]=245
xxzb["魔王寨"]=294
xxzb["女儿村"]=343
xxzb["盘丝洞"]=392
xxzb["普陀山"]=441
xxzb["神木林"]=490
xxzb["狮驼岭"]=539
xxzb["天宫"]=588
xxzb["无底洞"]=637
xxzb["五庄观"]=686
xxzb["阴曹地府"]=735
xxzb["九黎城"]=0

function 角色转换:初始化()
  local nsf = require("SDL.图像")(683+20, 455)
  if nsf["渲染开始"](nsf) then
    -- __res:getPNGCC(5, 0, 0, 683, 450):显示(0,5)
    xiao置窗口背景("角色转换", 0, 12, 540,432-70, true):显示(0, 0)
    字体20:置颜色(__取颜色("白色"))
    字体20:取图像("角色转换"):显示(230,15)

    --取白色背景(0, 0, 440, 300, true)["显示"](取白色背景(0, 0, 440, 300, true), 20, 56)
  end
  self:置精灵(nsf["到精灵"](nsf))
end



function 角色转换:打开(伙伴id,主角)
  -- print(11111111)
  self:置可见(true)
  self.新角色=nil
  self.新门派=nil
  self.支付方式=nil
  self.伙伴id=nil
  self.主角={}
  if 伙伴id then
    self.伙伴id=伙伴id
    self.主角=主角
  else
    self.主角=角色信息
  end
  self.免费="是"
  if self.主角.转门派 then
      self.免费="否"
  end
  -- self.门派控件["置可见"](self.门派控件, false)
  self.角色控件:重置()
end

-- __res:getPNGCC(3, 757, 291, 57, 57)["拉伸"](__res:getPNGCC(3, 757, 291, 57, 57), 60, 60)["显示"](__res:getPNGCC(3, 757, 291, 57, 57)["拉伸"](__res:getPNGCC(3, 757, 291, 57, 57), 60, 60), 0, 0)
--       local lssj = 取头像(v["造型"])
--       if 0 == lssj[2] then
--         lssj[2] = lssj[1]
--       end
--       __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), 54, 54)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), 54, 54), 3, 3)


local 角色控件 = 角色转换["创建控件"](角色转换, "角色控件", 0, 0, 535, 350)
function 角色控件:重置()
  角色转换.第三控件["置可见"](角色转换.第三控件, false)
  角色转换.门派控件["置可见"](角色转换.门派控件, false)
  角色转换.角色控件["置可见"](角色转换.角色控件, true)
  角色转换["图像"] = nil
  local nsf = require("SDL.图像")( 535, 350)
  if nsf["渲染开始"](nsf) then
    local lssj = 取白色背景(0, 0, 521,302, true)
    lssj["显示"](lssj, 7, 62-11)
    __res:getPNGCC(5, 1116, 80, 46, 224):显示(9+18,121-89+51)
    local bj=__res:getPNGCC(3, 132, 506, 55, 55)
    for i=1,6 do
      for b=1,3 do
        bj:显示(95+(i-1)*74,52+28+(b-1)*88)
      end
    end
    nsf["渲染结束"](nsf)
  end
  角色转换["图像"] = nsf["到精灵"](nsf)
  self.头像网格:置数据()
end



local 头像网格 = 角色控件["创建网格"](角色控件, "头像网格", 94, 69+16, 449, 274)
function 头像网格:初始化()
  self:创建格子(54, 54, 34, 20, 3, 6)
end
function 头像网格:左键弹起(x, y, a, b, msg)
  -- if self.子控件[a]._spr["数据"] then
  --   角色转换["选中"] = a
  --   self.子控件[a]._spr["确定"] = true
  -- end
end
function 头像网格:置数据()
  self:创建格子(54, 54, 34, 20, 3, 6)
  for i, v in ipairs(self.子控件) do
    local lssj = 取头像(tioy[i])
    local 按钮 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])), "详情" .. i, 6, 0) 
    function 按钮:左键单击(x, y, msg)
      角色转换.门派控件:重置(tioy[i])
    end
    v:置可见(true, true)
    头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i], lssj)
  end
end


local 门派控件 = 角色转换["创建控件"](角色转换, "门派控件", 0, 0, 535, 350)
function 门派控件:重置(toux)
  角色转换.门派控件["置可见"](角色转换.门派控件, true)
  角色转换.角色控件["置可见"](角色转换.角色控件, false)
  角色转换.第三控件["置可见"](角色转换.第三控件, false)
  local menpai=角色转换:载入门派(toux)
  角色转换["图像"] = nil
  local nsf = require("SDL.图像")( 535, 350)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 521,84, true):显示( 7, 62-11)
    取白色背景(0, 0, 521,203, true):显示( 7, 62-11+88)
    __res:getPNGCC(3, 132, 506, 55, 55):显示(9+18,121-89+51-17)
    local lssj = 取头像(toux)
    __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])):显示(9+18+6,121-89+51-17+4)
    字体16:置颜色(__取颜色("浅黑"))
    字体16:取图像("选定角色："):显示(9+18+60,121-89+51)
    __res:getPNGCC(3, 735, 1154, 202, 39):显示(9+18+60+80,121-89+51-12)
    字体16:取图像(toux):显示(9+18+60+80+16,121-89+51)
    字体16:取图像("您想选择下面哪个门派呢？"):显示(9+18,121-89+51+65)
    for i=1,#menpai do
      字体16:取图像(menpai[i]):显示(36+(i-1)*94,250)
    end
    -- __res:getPNGCC(5, 1116, 80, 46, 224):显示(9+18,121-89+51)
    nsf["渲染结束"](nsf)
  end
  角色转换["图像"] = nsf["到精灵"](nsf)
  self.menpwangge:置数据(menpai,toux)
end
local menpwangge = 门派控件["创建网格"](门派控件, "menpwangge", 33, 200, 497, 91)
function menpwangge:初始化()
  --self:创建格子(54, 54, 34, 20, 3, 6)
end
function menpwangge:左键弹起(x, y, a, b, msg)
end
function menpwangge:置数据(menpai,toux)
  self:创建格子(54, 54, 34, 40, 1, #menpai)
  for i, v in ipairs(self.子控件) do
    local 按钮 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(5, xxzb[menpai[i]], 530, 42, 42):拉伸(42, 42), "zudui" .. i, 6, 0)  
    function 按钮:左键单击(x, y, msg)
      -- 发送数据(6973,{门派=menpai[i],角色=toux})
      -- 角色转换["置可见"](角色转换, false)
      角色转换.第三控件:重置(toux,menpai[i])
    end
    v:置可见(true, true)
    头像网格["子控件"][i]["置精灵"](头像网格["子控件"][i], lssj)
  end
end

local 重选 = 门派控件["创建我的按钮18"](门派控件, __res:getPNGCC(5, 903, 144, 121, 29):拉伸(121, 29), "重选", 388,121-89+51-8,"重选角色")
function 重选:左键弹起(x, y, msg)
  角色转换.角色控件:重置()
end
function 角色转换:载入门派(toux)
	if toux=="飞燕女" or toux=="英女侠" or toux=="巫蛮儿" then
		local mp={"大唐官府","方寸山","女儿村","神木林"}
		return mp
	elseif toux=="偃无师" or toux=="逍遥生" or toux=="剑侠客" then
		local mp={"大唐官府","方寸山","化生寺","神木林"}
		return mp
	elseif toux=="狐美人" or toux=="影精灵" or toux=="鬼潇潇" then
    local mp={"魔王寨","阴曹地府","盘丝洞","无底洞"}
    if toux=="影精灵" then
      mp={"魔王寨","阴曹地府","盘丝洞","无底洞",'九黎城'}
    end
		return mp
	elseif toux=="杀破狼" or toux=="巨魔王" or toux=="虎头怪" then
		local mp={"魔王寨","阴曹地府","狮驼岭","无底洞"}
		return mp
	elseif toux=="舞天姬" or toux=="玄彩娥" or toux=="桃夭夭" then
		local mp={"天宫","龙宫","普陀山","凌波城","花果山"}
		return mp
	elseif toux=="羽灵神" or toux=="神天兵" or toux=="龙太子" then
		local mp={"天宫","龙宫","五庄观","凌波城","花果山"}
		return mp
	end
end



local 第三控件 = 角色转换["创建控件"](角色转换, "第三控件", 0, 0, 535, 380)
function 第三控件:重置(toux,mp)
  角色转换.门派控件["置可见"](角色转换.门派控件, false)
  角色转换.角色控件["置可见"](角色转换.角色控件, false)
  角色转换.第三控件["置可见"](角色转换.第三控件, true)
  角色转换["图像"] = nil
  角色转换.新角色=toux
  角色转换.新门派=mp
  local nsf = require("SDL.图像")( 535, 380)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 521,84+13, true):显示( 7, 62-11)
    取白色背景(0, 0, 521,203-13-14-5, true):显示( 7, 62-11+88+13)
    local lssj = 取输入背景(0, 0, 124, 23)
    lssj:显示(9+18+80, 66-3)
    lssj:显示(9+18+80, 66-3+27*1)
    lssj:显示(9+18+80, 66-3+27*2)
    lssj:显示(9+18+80+263, 66-3)
    lssj:显示(9+18+80+263, 66-3+27*1)
    -- __res:getPNGCC(3, 132, 506, 55, 55):显示(9+18,66)
    -- local lssj = 取头像(toux)
    -- __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])):显示(9+18+6,66+4)
    字体16:置颜色(__取颜色("浅黑"))
    字体16:取图像("原始角色"):显示(9+18,66)
    字体16:取图像("原始门派"):显示(9+18,66+27*1)
    字体16:取图像("预转角色"):显示(9+18+263,66)
    字体16:取图像("预转门派"):显示(9+18+263,66+27*1)
    字体16:取图像("原始种族"):显示(9+18,66+27*2)
    -- __res:getPNGCC(3, 735, 1154, 202, 39):显示(9+18+80,121-89+51-12)
    字体16:取图像(角色转换.主角.模型):显示(123,66)
    字体16:取图像(角色转换.主角.门派):显示(123,66+27*1)
    
    字体16:取图像(角色转换.主角.种族):显示(123,66+27*2)
    字体16:取图像("消费方式："):显示(9+18,121-89+51+65+13)
    字体16:取图像("方式一：消耗经验银两"):显示(9+18,66+124)
    字体16:取图像("方式二：仅消耗银两"):显示(9+18,66-14+218)
    
    字体16:置颜色(__取颜色("紫色"))
    字体16:取图像(toux):显示(123+263,66)
    字体16:取图像(mp):显示(123+263,66+27*1)
    字体16:取图像(角色转换.免费):置混合(0):显示(9+18+158,66-14+218+39+28)
    字体16:置颜色(__取颜色("红色"))
    if 角色转换.新角色==角色转换.主角.模型 then
      字体16:取图像("经验：3亿"):显示(9+18+62,66+124+27*1)
      字体16:取图像("银两：200万"):显示(9+18+62,66+124+27*2)
      字体16:取图像("银两：500万"):显示(9+18+62,66-14+218+27*1)
		else
      字体16:取图像("经验：6亿"):显示(9+18+62,66+124+27*1)
      字体16:取图像("银两：500万"):显示(9+18+62,66+124+27*2)
      字体16:取图像("银两：1000万"):显示(9+18+62,66-14+218+27*1)
		end 
    字体16:置颜色(__取颜色("白色"))
    字体16:取图像("是否含免费洗点机会："):置混合(0):显示(9+18,66-14+218+39+28)
    -- __res:getPNGCC(5, 1116, 80, 46, 224):显示(9+18,121-89+51)
    nsf["渲染结束"](nsf)
  end
  角色转换["图像"] = nsf["到精灵"](nsf)
  self.经验银子:置选中(true)
  角色转换.支付方式="经验银子"
  -- self.menpwangge:置数据(menpai,toux)
end
local sdsawe= {"经验银子","银子"}
local aa=0
local bb=0
for i = 1, #sdsawe do
  local 临时函数 = 第三控件["创建我的单选按钮"](第三控件, __res:getPNGCC(2, 1172, 106, 27, 27, true), __res:getPNGCC(2, 1171, 74, 27, 27, true), sdsawe[i], 280-35+128-115-42, 70+115 + aa * 80)
  aa=aa+1
  function  临时函数:左键弹起(x, y)
    角色转换.支付方式=sdsawe[i]
  end
end
local 我要转换门派 = 第三控件["创建我的按钮"](第三控件, __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(135, 33), "我要转换门派", 9+18+190, 66-14+218+39+28-8, "我要转换门派")
function 我要转换门派:左键弹起(x, y, msg)
  if not 角色转换.新角色 or not 角色转换.新门派 or not 角色转换.支付方式 then
    __UI弹出.提示框:打开("#Y/请选择一个门派")
  else
    if 角色转换.伙伴id then
      发送数据(6987,{门派=角色转换.新门派,角色=角色转换.新角色,支付方式=角色转换.支付方式,伙伴id=角色转换.伙伴id})
    else
      发送数据(91,{门派=角色转换.新门派,角色=角色转换.新角色,支付方式=角色转换.支付方式})
    end
    角色转换["置可见"](角色转换, false)
  end
end

local 关闭 = 角色转换["创建我的按钮"](角色转换, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 500, 0)
function 关闭:左键弹起(x, y, msg)
  角色转换["置可见"](角色转换, false)
end