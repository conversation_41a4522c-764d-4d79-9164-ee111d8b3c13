local 摊位购买 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "摊位购买", 112 + abbr.py.x, 5 + abbr.py.y, 776, 485)
function 摊位购买:初始化()
  local nsf = require("SDL.图像")(776, 485)
  if nsf["渲染开始"](nsf) then
    置窗口背景("摊位购买", 0, 12, 769, 472, true)["显示"](置窗口背景("摊位购买", 0, 12, 769, 472, true), 0, 0)
    local lssj = 取输入背景(0, 0, 205, 23)
    lssj["显示"](lssj, 70, 408)
    lssj["显示"](lssj, 70, 447)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "摊主")["显示"](字体18["取图像"](字体18, "摊主"), 24, 410)
    字体18["取图像"](字体18, "ID")["显示"](字体18["取图像"](字体18, "ID"), 45, 449)
    lssj = 取输入背景(0, 0, 152, 23)
    lssj["显示"](lssj, 373, 408)
    lssj["显示"](lssj, 373, 447)
    字体18["取图像"](字体18, "总价")["显示"](字体18["取图像"](字体18, "总价"), 332, 410)
    字体18["取图像"](字体18, "现金")["显示"](字体18["取图像"](字体18, "现金"), 332, 449)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 摊位购买:打开(名称,角色名称,id,物品数据,bb数据,制造数据)
  self:置可见(true)
  self.显示类型="物品"
  self.打造类组 = {}
	self.修理类组 = {}
	self.其他类组 = {}
  if not 判断是否为空表(物品数据) then
    self.显示类型="物品"
    -- self.物品类按钮:置选中(true)
    -- self.物品类按钮["左键弹起"](self.物品类按钮)
  elseif 判断是否为空表(物品数据) and not 判断是否为空表(bb数据) then
    self.显示类型="bb"
    -- self.召唤兽类按钮:置选中(true)
    -- self.召唤兽类按钮["左键弹起"](self.召唤兽类按钮)
  elseif 判断是否为空表(物品数据) and 判断是否为空表(bb数据) and not 判断是否为空表(制造数据) then
    self.显示类型="制造"
    -- self.制造类按钮:置选中(true)
    -- self.制造类按钮["左键弹起"](self.制造类按钮)
  else
    self.显示类型="物品"
    -- self.物品类按钮:置选中(true)
    -- self.物品类按钮["左键弹起"](self.物品类按钮)
  end
  self:刷新(名称,角色名称,id,物品数据,bb数据,制造数据)
end

function 摊位购买:shuaxinzhizhao()
  self.打造类组 = {}
	self.修理类组 = {}
	self.其他类组 = {}
  self.选中制造信息={}
	if self.上架制造.制造组~=nil then
		for n=1,#self.上架制造.制造组 do
			if self.上架制造[n]~=nil then
				local 排序组 = {}
				for k,v in pairs(self.上架制造[n]) do
					table.insert(排序组,k)
					table.sort(排序组,function(a,b) return a < b end)
				end
				for h,j in pairs(排序组) do
					if self.上架制造.制造组[n].类别=="打造类" then
						table.insert(self.打造类组,{名称=self.上架制造.制造组[n].名称,类别=self.上架制造.制造组[n].类别,序号=self.上架制造[n][j]["序号"],等级=math.floor(self.上架制造.制造组[n].等级/10)*10 - (j-1)*10,价格=self.上架制造[n][j]["价格"]})
					elseif self.上架制造.制造组[n].类别=="修理类" then
						table.insert(self.修理类组,{名称=self.上架制造.制造组[n].名称,类别=self.上架制造.制造组[n].类别,序号=self.上架制造[n][j]["序号"],等级=math.floor(self.上架制造.制造组[n].等级/10)*10 - (j-1)*10,价格=self.上架制造[n][j]["价格"]})
					elseif self.上架制造.制造组[n].类别=="其他类" then
						table.insert(self.其他类组,{名称=self.上架制造.制造组[n].名称,类别=self.上架制造.制造组[n].类别,序号=self.上架制造[n][j]["序号"],等级=self.上架制造.制造组[n].等级,价格=self.上架制造[n][j]["价格"]})
					end
				end
			end
		end
  end
  self.制作lei控件.制造列表1:重置(self.打造类组)
  self.制作lei控件.制造列表2:重置(self.修理类组)
  self.制作lei控件.制造列表3:重置(self.其他类组)
  -- table.print(self.其他类组)
end

function 摊位购买:刷新(名称,角色名称,id,物品数据,bb数据,制造数据)
  self.对象名称=名称
  self.摊主名称=角色名称
  self.摊主id=id
  self.摊位名称=名称
  self.销售物品 = {}
  for i, v in pairs(物品数据) do
    self.销售物品[#self.销售物品+1]=v
    self.销售物品[#self.销售物品].原始编号=i
  end
  self.上架bb={}
  for i, v in pairs(bb数据) do
    -- print(i)
    -- table.print(v)
    self.上架bb[#self.上架bb+1]=v
    self.上架bb[#self.上架bb].原始编号=i
  end
  self.上架制造=制造数据

  if self.显示类型=="物品" then
    self.显示类型="物品"
    self.物品类按钮:置选中(true)
    self.物品类按钮["左键弹起"](self.物品类按钮)
  elseif self.显示类型=="bb" then
    self.显示类型="bb"
    self.召唤兽类按钮:置选中(true)
    self.召唤兽类按钮["左键弹起"](self.召唤兽类按钮)
  elseif self.显示类型=="制造" then
    self.显示类型="制造"
    self.制造类按钮:置选中(true)
    self.制造类按钮["左键弹起"](self.制造类按钮)
  else
    self.显示类型="物品"
    self.物品类按钮:置选中(true)
    self.物品类按钮["左键弹起"](self.物品类按钮)
  end
  self.重置(self)
end
function 摊位购买:重置()
  local nsf = require("SDL.图像")(768, 95)
  if nsf["渲染开始"](nsf) then
    取灰色背景(0, 0, 524, 288, true)["显示"](取灰色背景(0, 0, 524, 288, true), 19, 110)
    取灰色背景(0, 0, 200, 245, true)["显示"](取灰色背景(0, 0, 200, 245, true), 548, 110)
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18["取图像"](字体18, self.摊主名称)["显示"](字体18["取图像"](字体18, self.摊主名称), 78, 17)
    字体18["取图像"](字体18, self.摊主id)["显示"](字体18["取图像"](字体18, self.摊主id), 78, 56)
    字体18["置颜色"](字体18, __取银子颜色(角色信息["银子"]))
    字体18["取图像"](字体18, 角色信息["银子"])["显示"](字体18["取图像"](字体18, 角色信息["银子"]), 381, 56)
    nsf["渲染结束"](nsf)
  end
  摊位购买["图像2"] = nsf["到精灵"](nsf)
  摊位购买["图像2"]["置中心"](摊位购买["图像2"], 0, -394)
end
















local 物品lei控件 = 摊位购买["创建控件"](摊位购买, "物品lei控件", 0, 110, 768, 290)
function 物品lei控件:重置()
  local nsf = require("SDL.图像")(768, 485)
  if nsf["渲染开始"](nsf) then
    取灰色背景(0, 0, 524, 288, true)["显示"](取灰色背景(0, 0, 524, 288, true), 19, 110)
    取灰色背景(0, 0, 200, 245, true)["显示"](取灰色背景(0, 0, 200, 245, true), 548, 110)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "数量")["显示"](字体18["取图像"](字体18, "数量"), 554, 370)
    取输入背景(0, 0, 124, 23)["显示"](取输入背景(0, 0, 124, 23), 614, 368)
    nsf["渲染结束"](nsf)
  end
  摊位购买["图像"] = nsf["到精灵"](nsf)
end
local 道具网格 = 物品lei控件["创建网格"](物品lei控件, "道具网格", 32, 11, 496, 275)
function 道具网格:左键弹起(x, y, a, b, msg)
  -- print(1111)
  if self.子控件[a]._spr and 摊位购买["选中商品"] ~= a then
    if 摊位购买["选中商品"] then
      self.子控件[摊位购买["选中商品"]]._spr["确定"] = nil
    end
    摊位购买["选中商品"] = a
    self.子控件[摊位购买["选中商品"]]._spr["确定"] = true
    物品lei控件["道具文本"]["清空"](物品lei控件["道具文本"])
    __道具提示(self.子控件[摊位购买["选中商品"]]._spr["物品"], 物品lei控件["道具文本"])
  end
end
function 道具网格:置物品()
  -- table.print(摊位购买.销售物品)
  -- print(math.ceil(##摊位购买.销售物品 / 2)).
  -- print(math.ceil(#摊位购买.销售物品 / 2))
  -- print(#摊位购买.销售物品)
  self:创建格子(244, 67, 10, 10, #摊位购买.销售物品, 2, true)
  for i = 1, #self.子控件 do
    if 摊位购买.销售物品[i] then
      local lssj = __摊位格子["创建"]()
      lssj["置物品"](lssj, 摊位购买.销售物品[i], "摊位物品")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
local 道具文本 = 物品lei控件["创建文本"](物品lei控件, "道具文本", 552, 7, 190, 230)
local 道具输入 = 物品lei控件["创建我的输入"](物品lei控件, "道具输入", 670, 262, 44, 18, 2, 2, "黑色")
local 数量减 = 物品lei控件["创建我的按钮"](物品lei控件, __res:getPNGCC(1, 601, 319, 29, 29), "数量减", 598, 255)
function 数量减:左键弹起(x, y, msg)
  if 道具输入["取数值"](道具输入) > 1 then
    道具输入["置数值"](道具输入, 道具输入["取数值"](道具输入) - 1)
  end
end
local 数量加 = 物品lei控件["创建我的按钮"](物品lei控件, __res:getPNGCC(1, 641, 320, 29, 29), "数量加", 721, 255)
function 数量加:左键弹起(x, y, msg)
  if 道具输入["取数值"](道具输入) < 99 then
    道具输入["置数值"](道具输入, 道具输入["取数值"](道具输入) + 1)
  end
end




local 召唤兽lei控件 = 摊位购买["创建控件"](摊位购买, "召唤兽lei控件", 0, 111, 768, 290)
function 召唤兽lei控件:重置()
  local nsf = require("SDL.图像")(768, 485)
  if nsf["渲染开始"](nsf) then
    取灰色背景(0, 0, 733, 288, true)["显示"](取灰色背景(0, 0, 733, 288, true), 19, 110)
    nsf["渲染结束"](nsf)
  end
  摊位购买["图像"] = nsf["到精灵"](nsf)
end
local 召唤兽网格 = 召唤兽lei控件["创建网格"](召唤兽lei控件, "召唤兽网格", 30, 10, 711, 272)
function 召唤兽网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and 摊位购买["选中商品"] ~= a then
    if 摊位购买["选中商品"] then
      self.子控件[摊位购买["选中商品"]]._spr["确定"] = nil
    end
    摊位购买["选中商品"] = a
    self.子控件[摊位购买["选中商品"]]._spr["确定"] = true
  end
end
function 召唤兽网格:置物品()
  -- table.print(摊位购买.上架bb)
  self:创建格子(350, 67, 10, 10, math.ceil(#摊位购买.上架bb / 2), 2, true)
  for i = 1, #self.子控件 do
    if 摊位购买.上架bb[i] then
      local lssj = __摊位格子["创建"]()
      lssj["置物品"](lssj, 摊位购买.上架bb[i], "摊位召唤兽")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
      local 按钮 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(4, 822, 9, 39, 38), "按钮" .. i, 296, 15)
     function  按钮:左键弹起(x, y, msg)
        __UI界面["窗口层"]["召唤兽查看"]["打开"](__UI界面["窗口层"]["召唤兽查看"], 摊位购买.上架bb[i])
      end
      按钮["置可见"](按钮, true, true)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end














local 制作lei控件 = 摊位购买["创建控件"](摊位购买, "制作lei控件", 0, 95, 766, 295)
function 制作lei控件:重置()
  local nsf = require("SDL.图像")(766, 485)
  if nsf["渲染开始"](nsf) then
    local lssj = 取白色背景(0, 0, 240, 256, true)
    lssj["显示"](lssj, 18, 133)
    lssj["显示"](lssj, 266, 133)
    lssj["显示"](lssj, 512, 133)
    字体20["置颜色"](字体20, __取颜色("白色"))
    字体20["取图像"](字体20, "装备制造")["显示"](字体20["取图像"](字体20, "装备制造"), 102, 105)
    字体20["取图像"](字体20, "装备修理")["显示"](字体20["取图像"](字体20, "装备修理"), 350, 105)
    字体20["取图像"](字体20, "其他制造")["显示"](字体20["取图像"](字体20, "其他制造"), 595, 105)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, "类型    等级    价格")["显示"](字体18["取图像"](字体18, "类型    等级    价格"), 50, 141)
    字体18["取图像"](字体18, "类型    等级    价格")["显示"](字体18["取图像"](字体18, "类型    等级    价格"), 297, 141)
    字体18["取图像"](字体18, "类型    等级    价格")["显示"](字体18["取图像"](字体18, "类型    等级    价格"), 545, 141)
    字体18["取图像"](字体18, "普通打造")["显示"](字体18["取图像"](字体18, "普通打造"), 56, 366)
    字体18["取图像"](字体18, "强化打造")["显示"](字体18["取图像"](字体18, "强化打造"), 172, 366)
    nsf["渲染结束"](nsf)
  end
  摊位购买["图像"] = nsf["到精灵"](nsf)
end
-- self.打造类组 = {}
-- self.修理类组 = {}
-- self.其他类组 = {}
local 制造列表1 = 制作lei控件["创建列表"](制作lei控件, "制造列表1", 20, 72, 238, 200-12) --打造类组
function 制造列表1:初始化()
  self:置文字(字体18)
  self.行高度 = 44
  self.行间距 = 0
end
function 制造列表1:重置(data)
  self.清空(self)
  for _, v in ipairs(data) do
    local nsf = require("SDL.图像")(238, 50)
    -- print(_)
    -- table.print(v)
    if nsf["渲染开始"](nsf) then
      字体16["置颜色"](字体16, __取颜色("深蓝"))
      字体16["取图像"](字体16, v["名称"]):显示(27, 12)
      字体16["取图像"](字体16, v["等级"]):显示(103, 12)
      字体16["取图像"](字体16, v["价格"]):显示(165, 12)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 制造列表1:左键弹起(x, y, i, item, msg)
  制作lei控件["制造列表3"]["置选中"](制作lei控件["制造列表3"], 0)
  制作lei控件["制造列表2"]["置选中"](制作lei控件["制造列表2"], 0)
  摊位购买.选中制造信息={}
  if 摊位购买.打造类组[i] then
    摊位购买.选中制造信息={
      商品单价=摊位购买.打造类组[i].价格,
      制造选择类别=摊位购买.打造类组[i].类别,
      打造等级=摊位购买.打造类组[i].等级,
      技能名称=摊位购买.打造类组[i].名称,
      选中序号=摊位购买.打造类组[i].序号,
      }
  end
  -- 摊位购买["选中商品"] = 摊位购买["数据"]["装备制造表"][i]["原始编号"]
end
local 制造列表2 = 制作lei控件["创建列表"](制作lei控件, "制造列表2", 513-240, 72, 238, 200)
function 制造列表2:初始化()
  self:置文字(字体18)
  self.行高度 = 44
  self.行间距 = 0
end
function 制造列表2:重置(data)
  self.清空(self)
  for _, v in ipairs(data) do
    local nsf = require("SDL.图像")(238, 50)
    if nsf["渲染开始"](nsf) then
      字体16["置颜色"](字体16, __取颜色("深蓝"))
      字体16["取图像"](字体16, v["名称"]):显示(18, 12)
      字体16["取图像"](字体16, v["等级"]):显示(108, 12)
      字体16["取图像"](字体16, v["价格"]):显示(166, 12)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 制造列表2:左键弹起(x, y, i, item, msg)
  制作lei控件["制造列表1"]["置选中"](制作lei控件["制造列表1"], 0)
  制作lei控件["制造列表3"]["置选中"](制作lei控件["制造列表3"], 0)
  摊位购买.选中制造信息={}
  -- if 摊位购买.修理类组[i] then
  --   摊位购买.选中制造信息={
  --     商品单价=摊位购买.修理类组[i].价格,
  --     制造选择类别=摊位购买.修理类组[i].类别,
  --     打造等级=摊位购买.修理类组[i].等级,
  --     技能名称=摊位购买.修理类组[i].名称,
  --     选中序号=摊位购买.修理类组[i].序号,
  --     }
  -- end
  -- 摊位购买["选中商品"] = 摊位购买["数据"]["其他制造表"][i]["原始编号"]
end



local 制造列表3 = 制作lei控件["创建列表"](制作lei控件, "制造列表3", 513, 72, 238, 200)
function 制造列表3:初始化()
  self:置文字(字体18)
  self.行高度 = 44
  self.行间距 = 0
end
function 制造列表3:重置(data)
  self.清空(self)
  for _, v in ipairs(data) do
    local nsf = require("SDL.图像")(238, 50)
    if nsf["渲染开始"](nsf) then
      字体16["置颜色"](字体16, __取颜色("深蓝"))
      字体16["取图像"](字体16, v["名称"]):显示(18, 12)
      字体16["取图像"](字体16, v["等级"]):显示(108, 12)
      字体16["取图像"](字体16, v["价格"]):显示(166, 12)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 制造列表3:左键弹起(x, y, i, item, msg)
  制作lei控件["制造列表1"]["置选中"](制作lei控件["制造列表1"], 0)
  制作lei控件["制造列表2"]["置选中"](制作lei控件["制造列表2"], 0)
  摊位购买.选中制造信息={}
  if 摊位购买.其他类组[i] then
    摊位购买.选中制造信息={
      商品单价=摊位购买.其他类组[i].价格,
      制造选择类别=摊位购买.其他类组[i].类别,
      打造等级=摊位购买.其他类组[i].等级,
      技能名称=摊位购买.其他类组[i].名称,
      选中序号=摊位购买.其他类组[i].序号,
      }
  end
  -- 摊位购买["选中商品"] = 摊位购买["数据"]["其他制造表"][i]["原始编号"]
end


for i, v in ipairs({
  {
    name = "普通打造",
    x = 27,
    y = 266,
    tcp = __res:getPNGCC(2, 1172, 107, 26, 26, true),
    tcp2 = __res:getPNGCC(2, 1171, 73, 26, 26, true)
  },
  {
    name = "强化打造",
    x = 145,
    y = 266,
    tcp = __res:getPNGCC(2, 1172, 107, 26, 26, true),
    tcp2 = __res:getPNGCC(2, 1171, 73, 26, 26, true)
  }
}) do
  local 临时函数 = 制作lei控件["创建我的单选按钮"](制作lei控件, v.tcp, v.tcp2, v.name, v.x, v.y)
 function  临时函数:左键弹起(x, y)
  end
end















for i, v in ipairs({
  {
    name = "物品类按钮",
    x = 19,
    y = 57,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 118, 41),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 41),
    font = "物品类"
  },
  {
    name = "召唤兽类按钮",
    x = 149,
    y = 57,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 118, 41),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 41),
    font = "召唤兽类"
  },
  {
    name = "制造类按钮",
    x = 277,
    y = 57,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 118, 41),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 41),
    font = "制造类"
  }
}) do
  local 临时函数 = 摊位购买["创建我的单选按钮"](摊位购买, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "物品类按钮" then
      摊位购买["物品lei控件"]:置可见(true)
      摊位购买["召唤兽lei控件"]:置可见(false)
      摊位购买["制作lei控件"]:置可见(false)
      摊位购买.显示类型="物品"
      摊位购买["物品lei控件"]["重置"](摊位购买["物品lei控件"])
      摊位购买["物品lei控件"]["道具网格"]["置物品"](摊位购买["物品lei控件"]["道具网格"])
      摊位购买["物品lei控件"]["道具输入"]["置数值"](摊位购买["物品lei控件"]["道具输入"], 1)
    elseif v.name == "召唤兽类按钮" then
      摊位购买["物品lei控件"]:置可见(false)
      摊位购买["召唤兽lei控件"]:置可见(true)
      摊位购买["制作lei控件"]:置可见(false)
      摊位购买.显示类型="bb"
      摊位购买["召唤兽lei控件"]["重置"](摊位购买["召唤兽lei控件"])
      摊位购买["召唤兽lei控件"]["召唤兽网格"]["置物品"](摊位购买["召唤兽lei控件"]["召唤兽网格"])
    elseif v.name == "制造类按钮" then
      摊位购买["物品lei控件"]:置可见(false)
      摊位购买["召唤兽lei控件"]:置可见(false)
      摊位购买["制作lei控件"]:置可见(true)
      摊位购买.显示类型="制造"
      摊位购买["制作lei控件"]["重置"](摊位购买["制作lei控件"])
      摊位购买:shuaxinzhizhao()
    end
    摊位购买["选中商品"] = nil
  end
end


local 关闭 = 摊位购买["创建我的按钮"](摊位购买, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 726, 0)
function 关闭:左键弹起(x, y, msg)
  摊位购买["置可见"](摊位购买, false)
end

local 购买 = 摊位购买["创建我的按钮"](摊位购买, __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 138, 41), "购买", 586, 422, "购买")
function 购买:左键弹起(x, y)
  if 摊位购买["选中商品"] then
    if 摊位购买.显示类型=="物品" and 摊位购买.销售物品[摊位购买["选中商品"]] then
      if 摊位购买["物品lei控件"]["道具输入"]["取数值"](摊位购买["物品lei控件"]["道具输入"]) > 0 then
        发送数据(3726,{道具=摊位购买.销售物品[摊位购买["选中商品"]].原始编号,数量=摊位购买["物品lei控件"]["道具输入"]["取数值"](摊位购买["物品lei控件"]["道具输入"]),摊主id=摊位购买.摊主id})
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请重新输入数量")
      end
    elseif 摊位购买.显示类型=="bb" and 摊位购买.上架bb[摊位购买["选中商品"]] then
      发送数据(3726,{bb=摊位购买.上架bb[摊位购买["选中商品"]].原始编号,摊主id=摊位购买.摊主id})
    -- elseif  摊位购买.显示类型=="制造" then
    --   -- 摊位购买.选中制造信息={
    --   --   商品单价=摊位购买.打造类组[i].价格,
    --   --   制造选择类别=摊位购买.打造类组[i].类别,
    --   --   打造等级=摊位购买.打造类组[i].等级,
    --   --   技能名称=摊位购买.打造类组[i].名称,
    --   --   选中序号=摊位购买.打造类组[i].序号,
    --   --   }
    end
  elseif  摊位购买.显示类型=="制造" then
    -- print(1111)
      if 摊位购买.选中制造信息.商品单价 then
        -- print(222)
        发送数据(3726.1,{制造=摊位购买.上架制造,打造模式=2,制造类别=摊位购买.选中制造信息.制造选择类别,等级=摊位购买.选中制造信息.打造等级,技能名称=摊位购买.选中制造信息.技能名称,选中序号=摊位购买.选中制造信息.选中序号,价格=摊位购买.选中制造信息.商品单价,摊主id=摊位购买.摊主id})
      end
  end
end