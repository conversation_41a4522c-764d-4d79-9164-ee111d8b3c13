local 引擎关闭提示 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "引擎关闭提示", 0, 0, 960 + abbr.py.x, 540 + abbr.py.y)
function 引擎关闭提示:初始化()
end
function 引擎关闭提示:打开(数据, x, y, w, h, sj)
  引擎关闭开始=true
  self:置可见(true, true)
  self.引擎关闭提示文本["清空"](self.引擎关闭提示文本)
  self:置坐标(x + abbr.py.x, y + abbr.py.y)
  if sj then
    self.事件 = sj
  else
    self.事件 = nil
  end
  local nsf = require("SDL.图像")(w, h)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 213, 927, 30, 30)["显示"](__res:getPNGCC(3, 213, 927, 30, 30), 0, 0)
    __res:getPNGCC(3, 213, 957, 30, 141)["平铺"](__res:getPNGCC(3, 213, 957, 30, 141), 30, h - 60)["显示"](__res:getPNGCC(3, 213, 957, 30, 141)["平铺"](__res:getPNGCC(3, 213, 957, 30, 141), 30, h - 60), 0, 30)
    __res:getPNGCC(3, 213, 1098, 30, 31)["显示"](__res:getPNGCC(3, 213, 1098, 30, 31), 0, h - 30)
    __res:getPNGCC(3, 243, 927, 140, 30)["平铺"](__res:getPNGCC(3, 243, 927, 140, 30), w - 60, 30)["显示"](__res:getPNGCC(3, 243, 927, 140, 30)["平铺"](__res:getPNGCC(3, 243, 927, 140, 30), w - 60, 30), 30, 0)
    __res:getPNGCC(3, 243, 957, 155, 141)["平铺"](__res:getPNGCC(3, 243, 957, 155, 141), w - 60, h - 60)["显示"](__res:getPNGCC(3, 243, 957, 155, 141)["平铺"](__res:getPNGCC(3, 243, 957, 155, 141), w - 60, h - 60), 30, 30)
    __res:getPNGCC(3, 243, 1098, 155, 31)["平铺"](__res:getPNGCC(3, 243, 1098, 155, 31), w - 60, 30)["显示"](__res:getPNGCC(3, 243, 1098, 155, 31)["平铺"](__res:getPNGCC(3, 243, 1098, 155, 31), w - 60, 30), 30, h - 30)
    __res:getPNGCC(3, 398, 927, 30, 30)["显示"](__res:getPNGCC(3, 398, 927, 30, 30), w - 30, 0)
    __res:getPNGCC(3, 398, 957, 30, 141)["平铺"](__res:getPNGCC(3, 398, 957, 30, 141), 30, h - 60)["显示"](__res:getPNGCC(3, 398, 957, 30, 141)["平铺"](__res:getPNGCC(3, 398, 957, 30, 141), 30, h - 60), w - 30, 30)
    __res:getPNGCC(3, 398, 1098, 30, 31)["显示"](__res:getPNGCC(3, 398, 1098, 30, 31), w - 30, h - 30)
    nsf["渲染结束"](nsf)
  end
  引擎关闭提示["置精灵"](引擎关闭提示, nsf["到精灵"](nsf))
  self.引擎关闭提示文本["置文本"](self.引擎关闭提示文本, 数据)
end
local 确定 = 引擎关闭提示["创建我的按钮"](引擎关闭提示, __res:getPNGCC(1, 401, 65, 175, 43, true)["拉伸"](__res:getPNGCC(1, 401, 65, 175, 43, true), 124, 43), "确定", 30+101, 140, "确定")
function 确定:左键弹起(x, y, msg)
  引擎关闭提示["置可见"](引擎关闭提示, false)
  __CLT["断开"](__CLT)
  引擎["关闭"](引擎)
end

local 引擎关闭提示文本 = 引擎关闭提示["创建我的文本"](引擎关闭提示, "引擎关闭提示文本", 18, 18, 359, 106)
function 引擎关闭提示文本:初始化()
end
