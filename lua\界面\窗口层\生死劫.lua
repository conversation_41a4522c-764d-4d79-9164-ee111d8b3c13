local 文字 = "    丹青生有一宝画，乃是上古流传下来的珍品，名曰《乾\n坤九曜图》。此画中有雾楼云阁，亭台水榭，青山秀水，蕊\n珠宫阙，只要轻轻将画卷展开，便可身临其境。各路神魔仙\n怪因为厌倦尘世杀戮而隐居在此，再也不问时世。天帝命丹\n青生将此画卷谨慎收藏，不再沾染红尘血腥。只可惜近日魔\n神将要现世，画中诸雄无不感到了他的怨念与仇恨而杀意萌\n动。他们忘却了修身养性的要诀，却没有忘记运用盖世的神\n功手段来杀戮他们看到的一切……于是，丹青生手执画卷，\n在此等待有缘的侠义之士，来化解他们的戾气。\n"
local 生死劫 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "生死劫", 228 + abbr.py.x, 23 + abbr.py.y, 511, 520)
function 生死劫:初始化()
  local nsf = require("SDL.图像")(511, 520)
  if nsf["渲染开始"](nsf) then
    置窗口背景("生死劫", 0, 12, 503, 502, true)["显示"](置窗口背景("生死劫", 0, 12, 503, 502, true), 0, 0)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, 文字)["显示"](字体18["取图像"](字体18, 文字), 21, 60)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 生死劫:打开()
  self:置可见(true)
end
local 关闭 = 生死劫["创建我的按钮"](生死劫, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 461, 0)
function 关闭:左键弹起(x, y, msg)
  生死劫["置可见"](生死劫, false)
end
for i, v in ipairs({
  {
    name = "止戈",
    x = 20,
    y = 359,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "止戈"
  },
  {
    name = "清心",
    x = 195,
    y = 359,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "清心"
  },
  {
    name = "雷霆",
    x = 367,
    y = 359,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "雷霆"
  },
  {
    name = "惜花",
    x = 20,
    y = 410,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "惜花"
  },
  {
    name = "忘情",
    x = 195,
    y = 410,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "忘情"
  },
  {
    name = "卧龙",
    x = 367,
    y = 410,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "卧龙"
  },
  {
    name = "天象",
    x = 20,
    y = 461,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "天象"
  },
  {
    name = "轮回",
    x = 195,
    y = 461,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "轮回"
  },
  {
    name = "娑罗",
    x = 367,
    y = 461,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "娑罗"
  }
}) do
  local 临时函数 = 生死劫["创建我的按钮"](生死劫, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "止戈" then
      发送数据(1511)
    elseif v.name == "清心" then
      发送数据(1512)
    elseif v.name == "雷霆" then
      发送数据(1513)
    elseif v.name == "惜花" then
      发送数据(1514)
    elseif v.name == "忘情" then
      发送数据(1515)
    elseif v.name == "卧龙" then
      发送数据(1516)
    elseif v.name == "天象" then
      发送数据(1517)
    elseif v.name == "轮回" then
      发送数据(1518)
    elseif v.name == "娑罗" then
      发送数据(1519)
    end
    生死劫["关闭"]["左键弹起"](生死劫["关闭"])
  end
end
