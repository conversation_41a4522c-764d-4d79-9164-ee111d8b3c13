local SDL = require("SDL")
取游戏时间 = function()
    return SDL.GetTicks()
end

-- function zdtostring(t)
--   if t then
--       local mark = {}
--       local assign = {}
--       local function ser_table(tbl, parent)
--           mark[tbl] = parent
--           local tmp = {}
--           for k, v in pairs(tbl) do
--               local key = type(k) == "number" and string.format("[%s]", k) or k
--               if type(v) == "table" then
--                   local dotkey = parent .. key
--                   if mark[v] then
--                       table.insert(assign, string.format("%s='%s'", dotkey, mark[v]))
--                   else
--                       table.insert(tmp, string.format("%s=%s", key, ser_table(v, dotkey)))
--                   end
--               elseif type(v) == "string" then
--                   table.insert(tmp, string.format("%s=%q", key, v))
--               elseif type(v) == "number" or type(v) == "boolean" then
--                   table.insert(tmp, string.format("%s=%s", key, v))
--               end
--           end
--           return string.format("{%s}", table.concat(tmp, ","))
--       end

--       return string.format("do local ret=%s%s return ret end", ser_table(t, "ret"), table.concat(assign, " "))
--   end
--   return "do local ret={} return ret end"
-- end



function zdtostring(t)
  if not t then return "do local ret={} return ret end" end
  local seen = {}
  local function serialize(val, indent)
      if type(val) == "table" then
          if seen[val] then
              return nil
          end
          seen[val] = true

          local parts = {}
          local next_indent = indent .. ""
          for k, v in pairs(val) do
              local key_str = type(k)=="number" and string.format("[%s]", k) or k
              local val_str = serialize(v, next_indent)
              if val_str ~= nil then 
                  table.insert(parts, next_indent .. key_str .. "=" .. val_str)
              end
          end

          if #parts == 0 then
              return "{}"
          else
              return "{" .. table.concat(parts, ",") .. indent .. "}"
          end
      elseif type(val) == "string" then
          return string.format("%q", val)
      elseif type(val) == "boolean" or type(val) == "number" then
          return tostring(val)
      else
          return nil
      end
  end

  local serialized = serialize(t, "")
  return "do local ret=" .. (serialized or "{}") .. " return ret end"
end



function zdloadstring(t)
  
    if t then
        local f = load(t)
        if f then
            return f()
        end
    end
end

-- function table.print(root)
--     local print = print
--     local tconcat = table.concat
--     local tinsert = table.insert
--     local srep = string.rep
--     local type = type
--     local pairs = pairs
--     local tostring = tostring
--     local next = next
--     local cache = {
--         [root] = "."
--     }
--     local function _dump(t, space, name)
--         local temp = {}
--         for k, v in pairs(t) do
--             local key = tostring(k)
--             if cache[v] then
--                 tinsert(temp, "." .. key .. " {" .. cache[v] .. "}")
--             elseif "table" == type(v) then
--                 local new_key = name .. "." .. key
--                 cache[v] = new_key
--                 tinsert(temp, "." .. key .. _dump(v, space .. (next(t, k) and "|" or " ") .. srep(" ", #key), new_key))
--             else
--                 tinsert(temp, "." .. key .. " [" .. tostring(v) .. "]")
--             end
--         end
--         return tconcat(temp, "\n" .. space)
--     end

--     print(_dump(root, "", ""))
--     print("-------------------------------------")
-- end


-- function table.copy(t) --FIXME loop
--     local r = {}
--     if type(t) == 'table' then
--         for k, v in pairs(t) do
--             local tp = type(v)
--             if tp == 'table' then
--                 r[k] = table.copy(v)
--             elseif tp == 'string' or tp == 'number' or tp == 'boolean' then
--                 r[k] = v
--             end
--         end
--     end
--     return r
-- end


function 分割字符(str, tv)
  local t = tv or {}
  for p, c in utf8.codes(str) do
      table.insert(t,utf8.char(c))
  end
  return t
end

-- function 分割字符(str,tv)
-- 	local t = tv or {}
-- 	local i = 1
-- 	local ascii = 0
-- 	while true do
-- 		ascii = string.byte(str, i)
-- 		if ascii then
-- 			if ascii < 127 then
-- 				table.insert(t,string.sub(str, i, i))
-- 				i = i+1
-- 			else
-- 				table.insert(t,string.sub(str, i, i+1))
-- 			    i = i+2
-- 			end
-- 		else
-- 		    break
-- 		end
-- 	end
-- 	return t
-- end


分割文本 = function(szFullString, szSeparator)
    local nFindStartIndex = 1
    local nSplitIndex = 1
    local nSplitArray = {}
    while true do
        local nFindLastIndex = string.find(szFullString, szSeparator, nFindStartIndex)
        if not nFindLastIndex then
            nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, string.len(szFullString))
            break
        end
        nSplitArray[nSplitIndex] = string.sub(szFullString, nFindStartIndex, nFindLastIndex - 1)
        nFindStartIndex = nFindLastIndex + string.len(szSeparator)
        nSplitIndex = nSplitIndex + 1
    end
    return nSplitArray
end






生成XY = function(x, y)
    local f = {}
    f.x = tonumber(x) or 0
    f.y = tonumber(y) or 0
    setmetatable(f, {
        __add = function(a, b)
            return 生成XY(a.x + b.x, a.y + b.y)
        end,
        __sub = function(a, b)
            return 生成XY(a.x - b.x, a.y - b.y)
        end
    })
    return f
end


function direction4(角)
    local 方向 = 0
    if 角 > 0 and 角 < 91 then
        方向 = 0
    elseif 角 > 90 and 角 < 181 then
        方向 = 1
    elseif 角 > 180 and 角 < 271 then
        方向 = 2
    elseif 角 > 270 or 0 == 角 then
        方向 = 3
    end
    return 方向 + 1
end


function direction8(角, t)
    local 方向 = 0
    if 角 > 157 and 角 < 203 then
        方向 = 5
    elseif 角 > 202 and 角 < 248 then
        方向 = 2
    elseif 角 > 247 and 角 < 293 then
        方向 = 6
    elseif 角 > 292 and 角 < 338 then
        方向 = 3
    elseif 角 > 337 or 角 < 24 then
        方向 = 7
    elseif 角 > 23 and 角 < 69 then
        方向 = 0
    elseif 角 > 68 and 角 < 114 then
        方向 = 4
    elseif 角 > 113 then
        方向 = 1
    end
    if t then
        方向 = direction48(方向)
    end
    return 方向 + 1
end



function direction48(d)
    n = 0
    if 0 == d or 4 == d then
        n = 0
    elseif 1 == d or 5 == d then
        n = 1
    elseif 2 == d or 6 == d then
        n = 2
    elseif 3 == d or 7 == d then
        n = 3
    end
    return n
end

function test1(...)
    local c1 = collectgarbage("count")
    print("最开始,Lua的内存为", c1)
    collectgarbage()
    local c2 = collectgarbage("count")
    print("现在内存为:", c2)
    collectgarbage("incremental") 
end

复制assets到内部 = function(str)
    local GGEF = require("GGE.函数")
    local SDLF = require("SDL.函数")
    for path, rel in GGEF["遍历目录"]("assets/" .. str) do
        GGEF["复制文件"](path, SDLF["取内部存储路径"]() .. "/" .. str .. "/" .. rel)
    end
    __res["配置"]["assets包"][str] = 1
    __res["写出文件"](__res, "config.txt", zdtostring(__res["配置"]))
end









-- function 字符串分析(txt)
--     if txt and  string.find(txt,"/") then
--         local 临时文本=""
--         local 临时列表={}
--         if  string.find(txt,"#/") then
--             临时列表= 分割文本(txt,"#/")
--         end
--         if #临时列表>0 then
--                  if string.find(临时列表[1],"/") then
--                         local 分解字符 = 分割文本(临时列表[1],"/")
--                         for n = 1, #分解字符 do
--                             if 分解字符[n]~=nil then
--                                 临时文本=临时文本..分解字符[n]
--                             end
--                         end
--                  else
--                     临时文本=临时文本..临时列表[1]
--                  end
--                 for i = 2, #临时列表 do
--                         if 临时列表[i]~=nil then
--                                 临时文本=临时文本.."/"
--                                 if  string.find(临时列表[i],"/") then
--                                     local 分解字符 = 分割文本(临时列表[i],"/")
--                                     local 分解内容=""
--                                     for n = 1, #分解字符 do
--                                         if 分解字符[n]~=nil then
--                                             分解内容=分解内容..分解字符[n]
--                                         end
--                                     end
--                                     临时文本=临时文本..分解内容
--                                 else
--                                     临时文本=临时文本..临时列表[i]
--                                 end
--                         end
--                 end
--         else
--                 local 分解字符 = 分割文本(txt,"/")
--                 for n = 1, #分解字符 do
--                     if 分解字符[n]~=nil then
--                         临时文本=临时文本..分解字符[n]
--                     end
--                 end
--         end
--         return 临时文本
--     end
--     return txt
-- end
