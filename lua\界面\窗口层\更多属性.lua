
local 更多属性 = 窗口层:创建窗口("更多属性")
local wbxa = {"穿刺等级","狂暴等级","物理暴击等级"}
local wbxb = {"灵力","固定伤害","法术伤害结果","法术暴击等级"}
local wbxc = {"治疗能力","封印命中等级"}
local sxxx = {"灵力","固定伤害","治疗能力","穿刺等级","狂暴等级","法术伤害结果","物理暴击等级","法术暴击等级","封印命中等级"}

function 更多属性:初始化()
      local  tcp =__res:取资源动画("jszy/fwtb", 0x00000122,"图像")
      self:置精灵(tcp:到精灵()) 
      self:置宽高(tcp.宽度,tcp.高度)
      self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
      self.可初始化=true
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
end

function 更多属性:更新(dt)
  if self.开始x and self.开始y then
        local xx,yy=窗口层.人物属性:取坐标()
        if self.开始x~=xx or self.开始y~=yy then
            if xx - self.宽度>=5 then
                  self:置坐标(xx - self.宽度, yy+15)
            else
                  self:置坐标(xx + 窗口层.人物属性.宽度, yy+15)
            end
            self.开始x,self.开始y=窗口层.人物属性:取坐标()
        end

  end
end

function 更多属性:显示(x,y)
 
    if self.标题背景 then
        self.标题背景:显示(x+90,y+40)
    end
    if  self.下边 then
        self.下边:显示(x,y)
    end


end

function 更多属性:打开(数据)
        self:置可见(not self.是否可见)
        if not self.是否可见 then
          return
        end
        self.标题背景=  文本字体:置颜色(255, 255, 255, 255):取精灵("全部")
        self:显示设置()
        self.属性列表:置数据(sxxx)
        self.开始x,self.开始y=窗口层.人物属性:取坐标()
        if self.开始x - self.宽度>=5 then
              self:置坐标(self.开始x - self.宽度, self.开始y+15)
        else
              self:置坐标(self.开始x + 窗口层.人物属性.宽度, self.开始y+15)
        end
      
end




local 属性列表 = 更多属性:创建列表("属性列表", 15,65, 200, 120)
local 滑块=属性列表:创建竖向滑块("属性滑块",210,60,8,130,nil,6,20)
function 属性列表:初始化()
    self:置文字(文本字体)
    self:置颜色(0, 0, 0, 255)
    self.焦点精灵=nil
    self.选中精灵=nil
    self.行间距 = 0
    self.行高度 = 26
    self:绑定滑块(滑块.滑块)
end
function 属性列表:置数据(data)
          self:清空()
          if not data then return end
          for i, v in ipairs(data) do
              if 角色信息 and 角色信息[v] then
                   self:添加():创建纹理精灵(function()
                          文本字体:置颜色(255, 255, 255, 255)
                          文本字体:取图像(v):显示(0,0)
                          if v == "物理暴击等级" or v == "法术暴击等级" then
                              文本字体:取描边图像(角色信息[v].."(+"..string.format("%.2f",角色信息[v]/30).."%)"):显示(130,0)
                          elseif  v == "封印命中等级"  then
                                文本字体:取描边图像(角色信息[v].."(+"..string.format("%.2f",角色信息[v]/60).."%)"):显示(130,0)
                          else
                                文本字体:取描边图像(角色信息[v]):显示(130,0)
                          end
                    end)
                  
              end
          end
end
    

local 选项按钮= 更多属性:创建按钮( "选项按钮", 199, 39)
function 选项按钮:初始化()
  self:创建按钮精灵(__res:取资源动画('jszy/ui',0x00000061), 1)
end
function 选项按钮:左键弹起(x, y, i)
    local 列表={"全部","物理","法术","辅助"}
   

    local 事件 =function (编号)
      if 列表[编号] then
            更多属性.标题背景=  文本字体:置颜色(255, 255, 255, 255):取精灵(列表[编号])
            if 列表[编号]=="全部" then
                  更多属性.属性列表:置数据(sxxx)
            elseif 列表[编号]=="物理" then
                  更多属性.属性列表:置数据(wbxa)
            elseif 列表[编号]=="法术" then
                  更多属性.属性列表:置数据(wbxb)
            elseif 列表[编号]=="辅助" then
                  更多属性.属性列表:置数据(wbxc)
            end
      end
    end
    local xx,yy=self:取坐标()
    __UI弹出.弹出列表:打开(列表,nil,事件,xx-190,yy+20,200)




end       




function 更多属性:显示设置()
   self.下边=self:创建纹理精灵(function()
            for i, v in ipairs({"躲避","格挡值","气血回复效果","抵抗封印等级","抗物理暴击等级","抗法术暴击等级"}) do
                  文本字体:置颜色(255, 255, 255, 255)
                  文本字体:取图像(v):显示(15,247+(i-1)*26)
                  if v== "抗物理暴击等级" or v == "抗法术暴击等级"  then
                      文本字体:取描边图像(角色信息[v].."(+"..string.format("%.2f",角色信息[v]/30).."%)"):显示(140,247+(i-1)*26)
                  elseif v == "抵抗封印等级" then
                        文本字体:取描边图像(角色信息[v].."(+"..string.format("%.2f",角色信息[v]/60).."%)"):显示(140,247+(i-1)*26)
                  else
                      文本字体:取描边图像(角色信息[v]):显示(140,247+(i-1)*26)
                  end
            end
      end,1)


end








local 关闭 = 更多属性:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
      更多属性:置可见(false)
end





