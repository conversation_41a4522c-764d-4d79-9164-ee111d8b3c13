local 门派选择 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "门派选择", 304 + abbr.py.x, 108 + abbr.py.y, 571, 334)
function 门派选择:初始化()
  self:置精灵(置窗口背景("门派选择", 0, 12, 314, 319):置透明(230))
end
function 门派选择:打开()
  self:置可见(true)
  self.可选门派 = __主控["队伍角色"](__主控, 角色信息["模型"])
  self.数量=#self.可选门派["门派"]
  for i=1,5 do
    self["传送1"..i]:置可见(false,false)
  end
  for i=1, self.数量 do
    self["传送1"..i]:置可见(true)
    self:我的按钮置文字(self["传送1"..i],__res:getPNGCC(7, 0, 447, 296, 38, true):拉伸(239, 38), 门派选择["可选门派"]["门派"][i])
  end
end
local 关闭 = 门派选择["创建我的按钮"](门派选择, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 521-254, 0)
function 关闭:左键弹起(x, y, msg)
  门派选择["置可见"](门派选择, false)
end
for i=1,5 do
  local 临时函数 = 门派选择["创建我的按钮"](门派选择, __res:getPNGCC(7, 0, 447, 296, 38, true):拉伸(239, 38), "传送1"..i, 25+14, 50+(i-1)*55)
  function  临时函数:左键弹起(x, y)
    发送数据(57, {
      ["门派"] = 门派选择["可选门派"]["门派"][i]
    })
    门派选择["置可见"](门派选择, false)
  end
end


