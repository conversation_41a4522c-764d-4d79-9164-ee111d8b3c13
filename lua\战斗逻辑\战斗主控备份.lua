local 战斗主控 = class("战斗主控")
local ggf = require("GGE.函数")
local 排序 = function(a, b)
    return a.显示xy.y < b.显示xy.y
end
function 战斗主控:初始化()
    self.背景 = require("SDL.精灵")(0, 0, 0, 引擎.宽度, 引擎.高度):置颜色(20, 20, 80, 180) 
    self.背景圆=__res:取资源动画("dlzy",0x00D17553,"精灵")   -- 0x10092123
    self.背景状态 = 0
    self.进程 = "加载"
    self.加载数量 = 0
    self.战斗单位 = {}
    self.数字图片 = {}-- _tp.战斗文字[5]
    for i=1,10 do
        self.数字图片[i] = __res:取资源动画("dlzy", 0x4EE0010A):取精灵(i)
    end

    self.血条背景 =__res:取资源动画("dlzy", 0x4D0A334C,"精灵")

    if not _tp.观战中 then
        self.请等待 =  __res:取资源动画("ui", 3994489772,"精灵")
    else
        self.请等待 = __res:取资源动画("ui", 4254597813,"精灵")
    end
    self.拼接偏移 = require("GGE.坐标")()
    self.战斗信息提示 = {
        开关 = false,
        内容 = "",
        起始时间 = 0,
        停止时间 = 0,
        文字 = 标题字体:置颜色(232, 140, 32):取精灵("战斗文字")
    }
    self.战斗快捷键法术 = nil
    self.宠物战斗快捷键法术 = nil
    self.拼接特效 = {}
    self.间隔等待 = 20
    self.显示排序 = {}
    self.状态显示 = false
    self.状态显示2 = false
    self.回合数 = 0
    self.特殊状态 = {}
    self.显示表 = {}
    self.自动开关=false
    self.连击背景=__res:取资源动画("jszy/jljnsc", 0x00000024,"精灵")
   


end

function 战斗主控:是否开启自动()
    for n = 1, #self.战斗单位 do
        if self.战斗单位[n].数据.自动战斗 and self.战斗单位[n].类型 == "角色" and
                self.战斗单位[n].数字id == 角色信息.数字id then
                self.自动开关=true
                self.多开数据={}
        end
    end
end

function 战斗主控:加载单位(数据)
    
    self.战斗单位[#self.战斗单位 + 1] = __战斗单位()
    self.战斗单位[#self.战斗单位]:创建单位(数据, self.队伍id,#self.战斗单位)
    if #self.战斗单位 == self.单位总数 then
        if not _tp.观战中 then
            请求服务(5501,{名称=角色信息.名称})
        end
        self:是否开启自动()
    end
end

function 战斗主控:设置命令回合(数据,回合)
        界面层.战斗界面:更新类型(数据)
        self.进程 = "命令"
        self.命令数据 = {
            计时 = os.time(),
            分 = 9,
            秒 = 9
        }
        self.回合数 = 回合
        self:设置自动()
        self:聊天外框战斗()
end


function 战斗主控:设置重连命令回合(数据,分,秒)
        界面层.战斗界面:更新类型(数据)
        self.进程="命令"
        self.命令数据={计时=os.time(),分=分,秒=秒}
        self:设置自动()

end

function 战斗主控:设置战斗流程(内容)
    self.战斗流程 = 内容
    for n = 1, #self.战斗流程 do
        if self.战斗流程 ~= nil and self.战斗流程[n] ~= nil then
            self.战斗流程[n].执行 = false
            self.战斗流程[n].允许 = false
        end
    end
    if 0 == #self.战斗流程 then
        self.进程 = "等待"
        if false == _tp.观战中 then
            请求服务(__发送流程)
        end
        return
    end
    self.进程 = "计算"
end


function 战斗主控:设置自动()
    if __多开操作  then
         self.多开数据={}
          for k,v in pairs(self.战斗单位) do
                if v.敌我 == 1 then
                    self.多开数据[v.数据.位置] = {}
                    if v.数据.自动指令 then
                        if v.数据.自动指令.类型 == "法术" then
                            self.多开数据[v.数据.位置].参数 = v.数据.自动指令.参数
                        else
                            self.多开数据[v.数据.位置].参数 = v.数据.自动指令.类型
                        end
                    else
                        self.多开数据[v.数据.位置].参数 = "攻击"
                    end
                    self.多开数据[v.数据.位置].编号 = v.编号
                end
        end
        战斗层.多开自动:显示重置()
    elseif 战斗层.战斗自动.是否可见  then
            战斗层.战斗自动:显示重置()
    end
   
end


function 战斗主控:鼠标右键状态(编号)
      if not 编号 or 编号==0 then return end
      local 返回数据={}
      if self.战斗单位[编号] and self.战斗单位[编号].敌我== 1 then
          local 单位数据 =self.战斗单位[编号]
          if 单位数据.类型=="角色" and 单位数据.门派 and 单位数据.门派 ~="无" then
              local 门派=单位数据.门派
              if 门派=="凌波城" then
                    if 单位数据.战意 and 单位数据.战意>0 then
                      local 资源= 取状态图标("战意")
                      local 添加数据={
                                图标 = 资源[1],
                                资源 = 资源[2],
                                说明="战意:\n当前层数:"..单位数据.战意
                              }
                        table.insert(返回数据, 添加数据)
                    end
                    if 单位数据.超级战意 and 单位数据.超级战意>0 then
                          local 资源= 取状态图标("超级战意")
                          local 添加数据={
                                  图标 = 资源[1],
                                  资源 = 资源[2],
                                  说明="超级战意:\n当前层数:"..单位数据.超级战意
                                }
                          table.insert(返回数据, 添加数据)
                    end
              elseif 门派=="神木林"  then
                    if 单位数据.经脉流派 =="灵木药宗"  then
                        if (单位数据.灵药红 and 单位数据.灵药红>0) or (单位数据.灵药蓝 and 单位数据.灵药蓝>0)  or (单位数据.灵药黄 and 单位数据.灵药黄>0)  then
                              local 资源= 取状态图标("灵药")
                              local 添加数据={
                                        图标 = 资源[1],
                                        资源 = 资源[2],
                                        }
                              添加数据.说明 ="灵药:\n"
                              if v.灵药红>0 then
                                      添加数据.说明=添加数据.说明.."灵药·红:"..单位数据.灵药红.."\n"
                              end
                              if v.灵药蓝>0 then
                                      添加数据.说明=添加数据.说明.."灵药·蓝:"..单位数据.灵药蓝.."\n"
                              end
                              if v.灵药黄>0 then
                                  添加数据.说明=添加数据.说明.."灵药·黄:"..单位数据.灵药黄.."\n"
                              end
                              table.insert(返回数据, 添加数据)
                          end
                        elseif 单位数据.风灵 and 单位数据.风灵>0   then
                                local 资源= 取状态图标("风灵")
                                local 添加数据={
                                        图标 = 资源[1],
                                        资源 = 资源[2],
                                        说明="风灵:\n当前层数:"..单位数据.风灵,
                                    }
                                table.insert(返回数据,添加数据)
                        end       
              elseif 门派=="方寸山"  and 单位数据.经脉流派 =="五雷正宗" then
                        if 单位数据.符咒 and 单位数据.符咒>0  then
                            local 资源= 取状态图标("符咒")
                            local 添加数据={
                                    图标 = 资源[1],
                                    资源 = 资源[2],
                                    说明="符咒:\n当前层数:"..单位数据.符咒,
                                }
                            table.insert(返回数据, 添加数据)
                        end
                        if (单位数据.雷法崩裂 and 单位数据.雷法崩裂>0) or  (单位数据.雷法震煞 and 单位数据.雷法震煞>0) or 
                            (单位数据.雷法坤伏 and 单位数据.雷法坤伏>0) or  (单位数据.雷法翻天 and 单位数据.雷法翻天>0) or  
                            (单位数据.雷法倒海 and 单位数据.雷法倒海>0 ) then
                                  local 资源= 取状态图标("雷法")
                                  local 添加数据={
                                          图标 = 资源[1],
                                          资源 = 资源[2],
                                      }
                                  添加数据.说明 ="雷法:\n"
                                  if 单位数据.雷法崩裂>0 then
                                    添加数据.说明=添加数据.说明.."崩裂:"..单位数据.雷法崩裂.."  "
                                  end
                                  if 单位数据.雷法震煞>0 then
                                    添加数据.说明=添加数据.说明.."震煞:"..单位数据.雷法震煞.."\n"
                                  end
                                  if 单位数据.雷法坤伏>0 then
                                    添加数据.说明=添加数据.说明.."坤伏:"..单位数据.雷法坤伏.."  "
                                  end
                                  if 单位数据.雷法翻天>0 then
                                    添加数据.说明=添加数据.说明.."翻天:"..单位数据.雷法翻天
                                  end
                                  if 单位数据.雷法倒海>0 then
                                    添加数据.说明=添加数据.说明.."倒海:"..单位数据.雷法倒海
                                  end
                                  table.insert(返回数据, 添加数据)
                        end
              elseif 门派=="普陀山" and 单位数据.经脉流派 =="落伽神女"  then
                            if (单位数据.五行珠金 and 单位数据.五行珠金>0) or (单位数据.五行珠木 and 单位数据.五行珠木>0) or 
                              (单位数据.五行珠水 and 单位数据.五行珠水>0) or (单位数据.五行珠火 and 单位数据.五行珠火>0) or  
                              (单位数据.五行珠土 and 单位数据.五行珠土>0 ) then
                                          local 资源= 取状态图标("五行珠")
                                          local 添加数据={
                                                图标 = 资源[1],
                                                资源 = 资源[2],
                                              }
                                          添加数据.说明 ="当前五行珠:\n"
                                          if 单位数据.五行珠金>0 then
                                              添加数据.说明=添加数据.说明.."金 "
                                          end
                                          if 单位数据.五行珠木>0 then
                                              添加数据.说明=添加数据.说明.."木 "
                                          end
                                          if 单位数据.五行珠水>0 then
                                              添加数据.说明=添加数据.说明.."水 "
                                          end
                                          if 单位数据.五行珠火>0 then
                                              添加数据.说明=添加数据.说明.."火 "
                                          end
                                          if 单位数据.五行珠土>0 then
                                              添加数据.说明=添加数据.说明.."土 "
                                          end
                                          table.insert(返回数据, 添加数据)
                            end
              elseif 门派=="五庄观" and 单位数据.经脉流派 =="万寿真仙" and 单位数据.人参娃娃 and 单位数据.人参娃娃.回合 and 单位数据.人参娃娃.回合>0 then
                                local 资源= 取状态图标("轮回")
                                local 添加数据={
                                      图标 = 资源[1],
                                      资源 = 资源[2],
                                      说明="人参娃娃:\n当前层数:"..单位数据.人参娃娃.层数.."\n剩余回合:"..单位数据.人参娃娃.回合,
                                    }
                                table.insert(返回数据, 添加数据)
              elseif 门派=="大唐官府"and 单位数据.经脉流派 =="无双战神" and 单位数据.剑意  and 单位数据.剑意>0   then
                                local 资源= 取状态图标("剑意")
                                local 添加数据={
                                      图标 = 资源[1],
                                      资源 = 资源[2],
                                      说明="剑意:\n当前层数:"..单位数据.剑意,
                                }
                                table.insert(返回数据, 添加数据)
              end
          end
          if 单位数据.状态特效  then
              for i, n in pairs(单位数据.状态特效) do
                  local 资源= 取状态图标(i)
                  if 资源 and 资源[2] and 资源[1] then
                        local 添加数据={
                              图标 = 资源[1],
                              资源 = 资源[2],
                        }
                        添加数据.说明=i
                        if n.护盾值 and n.护盾值~=0 then
                            添加数据.说明=添加数据.说明.."\n剩余护盾:"..n.护盾值
                        end
                        if n.回合 then
                            添加数据.说明=添加数据.说明.."\n剩余回合:"..n.回合
                        end
                        table.insert(返回数据,添加数据)
                  end
              end
          end
      end

      return 返回数据
end

function 战斗主控:聊天外框战斗()
    if  界面层.聊天控件.聊天窗口 then
          local 返回数据 ={}
          返回数据.回合=self.回合数
          for k,v in pairs(self.战斗单位) do
            if v.敌我 == 1 and v.类型=="角色" then
                  返回数据[v.数据.位置] = {}
                  返回数据[v.数据.位置].名称=v.名称
                  返回数据[v.数据.位置].状态数据={}
                  if v.门派 and v.门派~="无" then
                      if v.门派=="凌波城" then
                          if v.战意 and v.战意>0 then
                              local 资源= 取状态图标("战意")
                              local 添加数据={
                                        图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                        说明="战意:\n当前层数:"..v.战意,
                                        选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                        回合=文本字体:置颜色(255,255,255,255):取精灵(v.战意)
                                      }
                              table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                            end
                            if v.超级战意 and v.超级战意>0 then
                                  local 资源= 取状态图标("超级战意")
                                  local 添加数据={
                                          图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                          说明="超级战意:\n当前层数:"..v.超级战意,
                                          选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                          回合=文本字体:置颜色(255,255,255,255):取精灵(v.超级战意)
                                        }
                                  table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                            end
                      elseif v.门派=="神木林"  then
                              if v.经脉流派 =="灵木药宗"  then
                                    if (v.灵药红 and v.灵药红>0) or (v.灵药蓝 and v.灵药蓝>0)  or (v.灵药黄 and v.灵药黄>0)  then
                                          local 资源= 取状态图标("灵药")
                                          local 添加数据={
                                              图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                              选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                             
                                          }
                                          添加数据.说明 ="灵药:\n"
                                          local 回合 = 0
                                          if v.灵药红>0 then
                                            添加数据.说明=添加数据.说明.."灵药·红:"..v.灵药红.."\n"
                                            回合=回合+1
                                          end
                                          if v.灵药蓝>0 then
                                            添加数据.说明=添加数据.说明.."灵药·蓝:"..v.灵药蓝.."\n"
                                            回合=回合+1
                                          end
                                          if v.灵药黄>0 then
                                              添加数据.说明=添加数据.说明.."灵药·黄:"..v.灵药黄.."\n"
                                              回合=回合+1
                                          end
                                          添加数据.回合=文本字体:置颜色(255,255,255,255):取精灵(回合)
                                          table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                                    end
                              elseif v.风灵  and v.风灵>0   then
                                      local 资源= 取状态图标("风灵")
                                      local 添加数据={
                                              图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                              说明="风灵:\n当前层数:"..v.风灵,
                                              选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                              回合=文本字体:置颜色(255,255,255,255):取精灵(v.风灵)
                                          }
                                      table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                              end       
                      elseif v.门派=="方寸山"  and v.经脉流派 =="五雷正宗" then
                              if v.符咒 and v.符咒>0  then
                                  local 资源= 取状态图标("符咒")
                                  local 添加数据={
                                          图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                          说明="符咒:\n当前层数:"..v.符咒,
                                          选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                          回合=文本字体:置颜色(255,255,255,255):取精灵(v.符咒)
                                      }
                                  table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                              end
                              if (v.雷法崩裂 and v.雷法崩裂>0) or  (v.雷法震煞 and v.雷法震煞>0) or (v.雷法坤伏 and v.雷法坤伏>0) or  (v.雷法翻天 and v.雷法翻天>0) or  (v.雷法倒海 and v.雷法倒海>0 ) then
                                        local 资源= 取状态图标("雷法")
                                        local 添加数据={
                                              图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                              选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                            
                        
                                            }
                                        添加数据.说明 ="雷法:\n"
                                        local 回合 = 0
                                        if v.雷法崩裂>0 then
                                          添加数据.说明=添加数据.说明.."雷法崩裂:"..v.雷法崩裂.."\n"
                                          回合=回合+1
                                        end
                                        if v.雷法震煞>0 then
                                          添加数据.说明=添加数据.说明.."雷法震煞:"..v.雷法震煞.."\n"
                                          回合=回合+1
                                        end
                                        if v.雷法坤伏>0 then
                                          添加数据.说明=添加数据.说明.."雷法坤伏:"..v.雷法坤伏.."\n"
                                          回合=回合+1
                                        end
                                        if v.雷法翻天>0 then
                                          添加数据.说明=添加数据.说明.."雷法翻天:"..v.雷法翻天.."\n"
                                          回合=回合+1
                                        end
                                        if v.雷法倒海>0 then
                                          添加数据.说明=添加数据.说明.."雷法倒海:"..v.雷法倒海.."\n"
                                          回合=回合+1
                                        end
                                        添加数据.回合=文本字体:置颜色(255,255,255,255):取精灵(回合)
                                        table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                              end
                      elseif v.门派=="普陀山" and v.经脉流派 =="落伽神女"  then
                              if (v.五行珠金 and v.五行珠金>0) or  (v.五行珠木 and v.五行珠木>0) or (v.五行珠水 and v.五行珠水>0) or  (v.五行珠火 and v.五行珠火>0) or  (v.五行珠土 and v.五行珠土>0 ) then
                                            local 资源= 取状态图标("五行珠")
                                            local 添加数据={
                                                  图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                                  选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                          
                                                }
                                            添加数据.说明 ="当前五行珠:\n"
                                            local 回合 = 0
                                            if v.五行珠金>0 then
                                              添加数据.说明=添加数据.说明.."金 "
                                              回合=回合+1
                                            end
                                            if v.五行珠木>0 then
                                              添加数据.说明=添加数据.说明.."木 "
                                              回合=回合+1
                                            end
                                            if v.五行珠水>0 then
                                              添加数据.说明=添加数据.说明.."水 "
                                              回合=回合+1
                                            end
                                            if v.五行珠火>0 then
                                              添加数据.说明=添加数据.说明.."火 "
                                              回合=回合+1
                                            end
                                            if v.五行珠土>0 then
                                              添加数据.说明=添加数据.说明.."土 "
                                              回合=回合+1
                                            end
                                            添加数据.回合=文本字体:置颜色(255,255,255,255):取精灵(回合)
                                            table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                              end
                      elseif v.门派=="五庄观" and v.经脉流派 =="万寿真仙" and  v.人参娃娃  and v.人参娃娃.回合 and  v.人参娃娃.回合>0 then
                                      local 资源= 取状态图标("轮回")
                                      local 添加数据={
                                            图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                            说明="人参娃娃:\n当前层数:"..v.人参娃娃.层数.."\n剩余回合:"..v.人参娃娃.回合,
                                            选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                            回合=文本字体:置颜色(255,255,255,255):取精灵(v.人参娃娃.回合)
                                          }
                                      table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                      elseif v.门派=="大唐官府"and v.经脉流派 =="无双战神" and v.剑意  and v.剑意>0   then
                                      local 资源= 取状态图标("剑意")
                                      local 添加数据={
                                        图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                        说明="剑意:\n当前层数:"..v.剑意,
                                        选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                        回合=文本字体:置颜色(255,255,255,255):取精灵(v.剑意)
                                      }
                                      table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                      end
                  end
                  if v.状态特效  then
                      for i, n in pairs(v.状态特效) do
                          local 资源= 取状态图标(i)
                          if 资源 and 资源[2] and 资源[1] then
                                local 添加数据={
                                      图标=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵(),
                                      选中=__res:取资源动画(资源[2],资源[1],"图像"):拉伸(30,30):到精灵():置高亮(true),
                                }
                                添加数据.说明=i
                                if n.护盾值 and n.护盾值~=0 then
                                    添加数据.说明=添加数据.说明.."\n剩余护盾:"..n.护盾值
                                end
                                if n.回合 then
                                    添加数据.说明=添加数据.说明.."\n剩余回合:"..n.回合
                                    添加数据.回合=文本字体:置颜色(255,255,255,255):取精灵(n.回合)
                                end
                                table.insert(返回数据[v.数据.位置].状态数据, 添加数据)
                          end
                      end
                  end
              end
          end
          界面层.聊天控件.聊天窗口:置状态数据(返回数据)
      end

end

function 战斗主控:流程状态处理(编号,流程)
        if not 编号 or not self.战斗单位[编号] or not 流程 then
            return
        end
        if 流程.添加状态 then
            if type(流程.添加状态)=="string" then
                self.战斗单位[编号]:增加状态(流程.添加状态)
            elseif type(流程.添加状态)=="table" then
                    for k,v in pairs(流程.添加状态) do
                        if type(k) =="string" and type(v) =="table" then
                            self.战斗单位[编号]:增加状态(k,v)
                        elseif type(k) =="number" and type(v) =="string" then
                              self.战斗单位[编号]:增加状态(v)
                        end
                    end
            end
        end
        
        if 流程.取消状态 then
            if type(流程.取消状态)=="string" then
                self.战斗单位[编号]:取消状态(流程.取消状态)
            elseif type(流程.取消状态)=="table" then
                  for k,v in pairs(流程.取消状态) do
                      if type(v)=="string" then
                          self.战斗单位[编号]:取消状态(v)
                      end
                  end
            end
            
        end
        if 流程.吸血伤害 then
            self.战斗单位[编号]:设置掉血(流程.吸血伤害,2)
        end
        if 流程.多人增加状态 then
            for k,v in pairs(流程.多人增加状态) do
                if v.特效 and  self.战斗单位[v.挨打方] and not self.战斗单位[v.挨打方].状态特效[v.特效] then
                  if v.动作 then
                      self.战斗单位[v.挨打方]:添加攻击特效(v.动作)
                  end
                    self.战斗单位[v.挨打方]:增加状态(v.特效)
                end

            end
        end
        if 流程.添加状态 or 流程.取消状态 then
            self:聊天外框战斗()
        end
        流程.吸血伤害 = nil
        流程.添加状态 = nil
        流程.取消状态 = nil
        流程.多人增加状态 = nil
end





function 战斗主控:流程更新()
    if self.战斗流程[1] == nil then
       return
    end
    if not self.执行流程 then return  end
    if 1 == self.执行流程 and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取状态() and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
      if self.战斗单位[self.战斗流程[1].攻击方].动画.攻击方式==1 then  
             self.战斗单位[self.战斗流程[1].攻击方]:换动作("攻击",true)
             self.执行流程=21
      elseif self.战斗单位[self.战斗流程[1].攻击方].模型=="蜈蚣精" then
        self.战斗单位[self.战斗流程[1].攻击方].移动开关=false
        self.执行流程=2
      elseif self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取状态() and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then --保持原有旧的操作
        self.战斗单位[self.战斗流程[1].攻击方]:移动计算(self.战斗流程[1], self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
        self.执行流程=2
        end
    elseif 2 == self.执行流程 and self.战斗单位[self.战斗流程[1].攻击方].移动开关 == false then
            if nil == self.战斗流程[1].保护数据 then
                local 攻击动作 = "攻击"
                if self.战斗流程[1].挨打方[1] and self.战斗流程[1].挨打方[1].特效 then
                    for i,v in ipairs(self.战斗流程[1].挨打方[1].特效) do
                        if v=="暴击" then
                          攻击动作 ="暴击" 
                        end
                    end
                end
              
                self.战斗单位[self.战斗流程[1].攻击方]:换动作(攻击动作, true,self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
              
                if nil ~= self.战斗流程[1].挨打方[1].死亡 then
                    self.战斗单位[self.战斗流程[1].攻击方].动画:置帧率(攻击动作, 0.11)
                end
                self.执行流程 = 3
            else
                self.执行流程 = 10
            end
            




            
    elseif self.执行流程==21 then   --远程攻击特效加入
            if self.战斗单位[self.战斗流程[1].攻击方].动作=="攻击" and self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间()+self.战斗单位[self.战斗流程[1].攻击方].攻击帧 then
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:设置弓弩(self.战斗单位[self.战斗流程[1].攻击方].显示xy, self.战斗单位[self.战斗流程[1].攻击方].初始方向)
                self.战斗单位[self.战斗流程[1].攻击方].移动开关=false
                if self.战斗流程[1].保护数据==nil then
                  self.执行流程=22
                else
                  self.执行流程=24
                end
            end
        elseif self.执行流程==22 then --远程攻击伤害计算
            if  self.战斗单位[self.战斗流程[1].攻击方].移动开关==false and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" and  not self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].弓弩开关 then
            if self.战斗流程[1].躲避 then
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:开启躲避()
                self.执行流程=7
            else
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作(self.战斗流程[1].挨打方[1].动作)
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡)
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].伤害类型)
                self:流程状态处理(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].挨打方[1])
                if self.战斗流程[1].挨打方[1].特效[1]=="力劈华山" then
                   self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("被击中")
                elseif self.战斗流程[1].挨打方[1].特效[3]=="力劈华山" then
                   self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("暴击")
                elseif self.战斗流程[1].挨打方[1].特效[1]=="防御" and self.战斗流程[1].挨打方[1].特效[2]=="暴击" and self.战斗流程[1].挨打方[1].特效[3]=="暴击"  then
                  self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("防御")
                  self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("暴击")
                elseif self.战斗流程[1].挨打方[1].特效[1]=="暴击" and self.战斗流程[1].挨打方[1].特效[2]=="暴击" and self.战斗流程[1].挨打方[1].特效[3]~=nil then
                  self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[3])
                  self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[1])
                  else
                  self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[1])
                 end
              self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
              if self.战斗流程[1].反震伤害~=nil then
                self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("反震")
                self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打")
                self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
                self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡)
                self.执行流程=8
              else
                self.执行流程=17
              end
      
            end
          end
        elseif self.执行流程==24 then  --远程攻击下保护移动
          self.战斗单位[self.战斗流程[1].保护数据.编号]:添加法术特效("保护")
          self.战斗单位[self.战斗流程[1].保护数据.编号]:移动计算(self.战斗流程[1], self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
          self.执行流程=25
        elseif self.执行流程==25 then  --远程攻击下保护状态下掉血流程
          if self.战斗单位[self.战斗流程[1].攻击方].移动开关==false  and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" and not self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].弓弩开关  then
            self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作(self.战斗流程[1].挨打方[1].动作)
            self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡)
            self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].伤害类型)
            self.战斗单位[self.战斗流程[1].保护数据.编号]:换动作("挨打")
            self.战斗单位[self.战斗流程[1].保护数据.编号]:开启击退(self.战斗流程[1].保护数据.死亡)
            self.战斗单位[self.战斗流程[1].保护数据.编号]:设置掉血(self.战斗流程[1].保护数据.伤害,self.战斗流程[1].挨打方[1].伤害类型)
            for n=1,#self.战斗流程[1].挨打方[1].特效 do
              self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[n])
            end
            if self.战斗流程[1].吸血伤害~=nil then
              self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].吸血伤害,2)
            end
            self.执行流程=13
          end
    elseif 3 == self.执行流程 then
          
            if  self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间()+self.战斗单位[self.战斗流程[1].攻击方].攻击帧  then
                if self.战斗流程[1].躲避 then
                    self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:开启躲避()
                    self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作("待战")
                    self.执行流程=7
                else
                    self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作(self.战斗流程[1].挨打方[1].动作,nil,true)
                    if self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].模型=="蜈蚣精" then
                        self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作("待战")
                    else
                        self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡)
                    end
                    self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].伤害类型,self.战斗流程[1].挨打方[1].护盾值)
                    self:流程状态处理(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].挨打方[1])
                    if self.战斗流程[1].挨打方[1].特效[1]=="力劈华山" then
                       self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("被击中")
                    elseif self.战斗流程[1].挨打方[1].特效[1]=="惊涛怒" then
                       self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("被击中")
                    elseif self.战斗流程[1].挨打方[1].特效[3]=="力劈华山" then
                       self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("暴击")
                    elseif self.战斗流程[1].挨打方[1].特效[1]=="防御" and self.战斗流程[1].挨打方[1].特效[2]=="暴击" and self.战斗流程[1].挨打方[1].特效[3]=="暴击"  then
                      self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("防御")
                      self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("暴击")
                    elseif self.战斗流程[1].挨打方[1].特效[1]=="暴击" and self.战斗流程[1].挨打方[1].特效[2]=="暴击" and self.战斗流程[1].挨打方[1].特效[3]~=nil then
                      self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[3])
                      self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[1])
                    else
                      self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[1])
                    end
                    if self.战斗流程[1].挨打方[1].额外特效~=nil then
                        self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].额外特效)
                    end
                    self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
                    if self.战斗流程[1].反震伤害~=nil then
                        self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("反震")
                        self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打")
                        self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
                        self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡)
                        self.执行流程=8
                    else
                        self.执行流程=17
                    end
                end
              end
    elseif 4 == self.执行流程 and (nil == self.战斗流程[1].挨打方[1] or nil == self.战斗流程[1].挨打方[1].挨打方 or 
          nil == self.战斗单位[self.战斗流程[1].挨打方[1].挨打方] or self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取状态()) then

        if nil ~= self.战斗流程[1].反击伤害 and nil ~= self.战斗流程[1].挨打方[1] and nil ~= self.战斗流程[1].挨打方[1].挨打方 and
                 nil ~= self.战斗单位[self.战斗流程[1].挨打方[1].挨打方] then
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作("攻击", true)
            if nil ~= self.战斗流程[1].反击死亡 then
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].动画:置帧率("攻击", 0.11)
            end
            self.执行流程 = 16
        elseif self.战斗流程[1].返回  then
            self.战斗单位[self.战斗流程[1].攻击方].返回开关 = true
            self.战斗单位[self.战斗流程[1].攻击方].移动坐标 = self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("返回")
            self.执行流程 = 5
        else
            self.执行流程 = 6
        end
    elseif self.执行流程==4.1 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
        if self.战斗流程[1].反击伤害~=nil then
            if self.战斗流程[1].反击死亡~=nil then
              self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].动画:置帧率("攻击",0.11)
            end
            self.执行流程=16
          elseif self.战斗流程[1].返回  then
            self.战斗单位[self.战斗流程[1].攻击方].返回开关=true
            self.战斗单位[self.战斗流程[1].攻击方].移动坐标 = self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("返回")
            self.执行流程=5
          else
            self.执行流程=6
          end
      
    elseif self.执行流程==5 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
        -- print(self.战斗单位[self.战斗流程[1].攻击方].初始方向)
		self.战斗单位[self.战斗流程[1].攻击方]:换方向(self.战斗单位[self.战斗流程[1].攻击方].初始方向)
		self.执行流程=6
   
    elseif self.执行流程 == 6  and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
            self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
            if self.战斗流程[1].结尾气血 and self.战斗流程[1].结尾气血>0 then
                self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].结尾气血,1)
            end
            self.执行流程=6.11
    elseif 6.11 == self.执行流程 then
        for n=1,#self.战斗单位 do
          if self.战斗单位[n]:取状态()==false or self.战斗单位[n]:取法术状态()==false then
              return
          end
           -- self.战斗单位[n]:回合结束重置()
        end
         table.remove(self.战斗流程,1)
        self.执行流程=0
        self.进程="计算"
        if #self.战斗流程==0 then
            self.拼接特效 = nil
            self.背景状态 = nil
            self.进程 = "等待"
            if false == _tp.观战中 then
                请求服务(__发送流程)
            end
        end
    -- elseif self.执行流程 == "施法流程" then
    --     self:施法流程更新()

    elseif self.执行流程==7 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
        self.执行流程=4
    elseif self.执行流程==8 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取状态() then
        if self.战斗流程[1].反震死亡==nil  then
          self.执行流程=4
        else
          self.执行流程=6
        end

    elseif self.执行流程==9 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取状态() then
        if self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取间隔() >= self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取中间()+self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].攻击帧 then
          self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打")
          self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反击伤害,1)
          self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反击死亡)
          self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("被击中")
          self.执行流程=9.1
        end
    elseif self.执行流程==9.1  then
        if self.战斗流程[1].反击死亡==nil  then
            self.战斗流程[1].反击伤害=nil
            self.执行流程=4.1
        else
            self.执行流程=6
        end
    elseif self.执行流程==10 then -- 攻击
        self.战斗单位[self.战斗流程[1].保护数据.编号].保护 = true
        self.战斗单位[self.战斗流程[1].保护数据.编号].移动开关 = true
        self.战斗单位[self.战斗流程[1].保护数据.编号].移动坐标 = self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取移动坐标("保护")
        self.战斗单位[self.战斗流程[1].保护数据.编号]:添加攻击特效("保护")
        self.执行流程 = 11
    elseif self.执行流程==11 and self.战斗单位[self.战斗流程[1].保护数据.编号].移动开关==false then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("攻击",true)
        if self.战斗流程[1].挨打方[1].死亡~=nil then
          self.战斗单位[self.战斗流程[1].攻击方].动画:置帧率("攻击",0.11)
        end
        self.执行流程=12
    elseif self.执行流程==12 then
        if  self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间()+self.战斗单位[self.战斗流程[1].攻击方].攻击帧  then
          self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作(self.战斗流程[1].挨打方[1].动作)
          self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡)
          self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,self.战斗流程[1].挨打方[1].伤害类型)
          self.战斗单位[self.战斗流程[1].保护数据.编号]:换动作("挨打")
          self.战斗单位[self.战斗流程[1].保护数据.编号]:开启击退(self.战斗流程[1].保护数据.死亡)
          self.战斗单位[self.战斗流程[1].保护数据.编号]:设置掉血(self.战斗流程[1].保护数据.伤害,self.战斗流程[1].挨打方[1].伤害类型)
          for n=1,#self.战斗流程[1].挨打方[1].特效 do
            self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[1].特效[n])
          end
          if self.战斗流程[1].吸血伤害~=nil then
            self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].吸血伤害,2)
          end
          self.执行流程=13
        end
    elseif self.执行流程==13 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取状态() and self.战斗单位[self.战斗流程[1].保护数据.编号]:取状态() then
            self:流程状态处理(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].挨打方[1])
            if self.战斗流程[1].保护数据.死亡~=nil then
              self.执行流程=4
            else
              self.执行流程=14
            end
    elseif self.执行流程==14 then
     
        self.战斗单位[self.战斗流程[1].保护数据.编号].返回开关=true
        self.战斗单位[self.战斗流程[1].保护数据.编号].移动坐标= self.战斗单位[self.战斗流程[1].保护数据.编号]:取移动坐标("返回")
        self.执行流程=15
    elseif self.执行流程==15 then
        self.执行流程=4
    elseif self.执行流程==16 then
        if self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间()+self.战斗单位[self.战斗流程[1].攻击方].攻击帧  then
          self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作("攻击",true)
          self.执行流程=9
        end
    elseif self.执行流程==17 and self.战斗单位[self.战斗流程[1].攻击方]:取状态()  and 
            self.战斗单位[self.战斗流程[1].攻击方].动作~="攻击" and self.战斗单位[self.战斗流程[1].攻击方].动作~="暴击" then
            if self.战斗流程[1].反击伤害~=nil then
                    self.执行流程=4
            elseif self.战斗流程[1].返回 then
                self.战斗单位[self.战斗流程[1].攻击方].返回开关=true
                self.战斗单位[self.战斗流程[1].攻击方].移动坐标= self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("返回")
                self.执行流程=5
            else
                self.执行流程=6
            end
            if self.战斗流程[1].气血恢复~=nil  then
                self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].气血恢复,2)
            end
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n].恢复气血~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,2)
                end
            end
    elseif self.执行流程==50 then
           self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
           self.执行流程=50.1
    elseif self.执行流程==50.1 then
            if self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间() then
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加法术特效(self.战斗流程[1].挨打方[1].特效[1])
                self.执行流程=51
            end

    elseif self.执行流程==51 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
        self:流程状态处理(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].挨打方[1])
        self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
        self.执行流程=52
    elseif self.执行流程==52 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
        self.执行流程=6
    elseif self.执行流程==53 and nil == self.拼接特效 then
        if self.战斗流程[1].挨打方[1].特效[1]~="盾气" then
          self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        end
      for n=1,#self.战斗流程[1].挨打方 do
          self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[1])
      end
      self.执行流程=54



    elseif self.执行流程==54 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
        for n=1,#self.战斗流程[1].挨打方 do
          self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:增加状态(self.战斗流程[1].挨打方[n].特效[1])
          self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
          if self.战斗流程[1].挨打方[n].恢复气血~=nil then
            if self.战斗流程[1].挨打方[n].特效[2]~=nil then
              self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[2])
            end
            self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,self.战斗流程[1].挨打方[n].伤害类型)
          end
          if self.战斗流程[1].挨打方[n].恢复伤势~=nil then
            if self.战斗流程[1].挨打方[n].特效[2]~=nil then
              self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[2])
            end
            self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置伤势(self.战斗流程[1].挨打方[n].恢复伤势,self.战斗流程[1].挨打方[n].伤势类型)
          end
        end
        if self.战斗流程[1].挨打方[1].特效[1]=="天魔解体" then
          self.战斗单位[self.战斗流程[1].攻击方]:换动作("攻击",true)
        end
        self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
        self.执行流程=55
    elseif self.执行流程==55 and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].掉血开关==false and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战"  then
        if self.战斗流程[1].挨打方[1].特效[1]=="炼气化神" then
           self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].扣除气血,1)
        end
        self.执行流程=6
    elseif self.执行流程==153 then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        self:置全屏技能(self.战斗流程[1].挨打方[1].特效[1],self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
        
       --- self:施法流程(154, self.战斗流程[1].攻击方, self.战斗流程[1].挨打方, self.战斗流程[1].挨打方[1].特效[1], true)
        self.执行流程=154
    elseif self.执行流程==154 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
        for n=1,#self.战斗流程[1].挨打方 do
           self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:增加状态(self.战斗流程[1].挨打方[n].特效[1])
           self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
          if self.战斗流程[1].挨打方[n].恢复气血~=nil then
           self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,2)
          end
        end
        self.执行流程=155
    elseif self.执行流程==155 and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].掉血开关==false and self.拼接特效==nil  then
        self.执行流程=6
    elseif self.执行流程==56 then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        self.执行流程=56.1
    elseif self.执行流程==56.1 then
      if self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间() then
            for n=1,#self.战斗流程[1].挨打方 do
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[1])
            end
            self.执行流程=57
      end


      --  self:施法流程(57, self.战斗流程[1].攻击方, self.战斗流程[1].挨打方, self.战斗流程[1].挨打方[1].特效[1], false)


    elseif self.执行流程==57 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
            for k,v in pairs(self.战斗流程[1].挨打方) do
                if v.气血 then
                    self.战斗单位[v.挨打方]:设置掉血(v.气血,1)
                    self.战斗单位[v.挨打方]:设置伤势(v.伤势,1)
                    self.战斗单位[v.挨打方]:换动作("挨打",nil,true)
                    self.战斗单位[v.挨打方]:开启击退(v.死亡)
                elseif v.击退 then
                    self.战斗单位[v.挨打方]:换动作("挨打",nil,true)
                    self.战斗单位[v.挨打方]:开启击退(nil)
                end
                self:流程状态处理(v.挨打方,v)
            end
            if self.战斗流程[1].受益方 then
                for k,v in pairs(self.战斗流程[1].受益方) do
                    self.战斗单位[v.收益方]:设置掉血(v.伤害,2)
                end
            end
            self.执行流程=58
    elseif self.执行流程==58 and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取状态() then
      
        if self.战斗流程[1].增加气血~=nil then
          self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].增加气血,2)
        end
        self.执行流程=59
    elseif self.执行流程==59 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
        self.执行流程=6
    elseif self.执行流程==60 then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        self.执行流程=60.1
    elseif self.执行流程==60.1 then
            if self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间() then
                    for n=1,#self.战斗流程[1].挨打方 do
                        self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[1])
                    end
                    if self.战斗流程[1].扣除气血~=nil then
                        self.执行流程=63
                    else
                        self.执行流程=61
                    end
            end
    elseif self.执行流程==61 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 and  self.掉血流程==nil  then
            for n=1,#self.战斗流程[1].挨打方 do
                self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n]) 
                if self.战斗流程[1].挨打方[n].恢复气血~=nil then
                    if self.战斗流程[1].挨打方[n].特效[2]~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[2])
                    end
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,self.战斗流程[1].挨打方[n].伤害类型)
                end
                if self.战斗流程[1].挨打方[n].恢复伤势~=nil then
                    if self.战斗流程[1].挨打方[n].特效[2]~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[2])
                    end
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置伤势(self.战斗流程[1].挨打方[n].恢复伤势,self.战斗流程[1].挨打方[n].伤势类型)
                end
                if self.战斗流程[1].挨打方[n].复活~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("待战")
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方].停止更新=false
                end
            end
            self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
            if self.战斗流程[1].增加气血 then
                self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].增加气血,2)
            end
            self.执行流程=62
    elseif  self.执行流程==62 then
        local 条件通过=#self.战斗流程[1].挨打方
        for n=1,#self.战斗流程[1].挨打方 do
          if #self.战斗单位[self.战斗流程[1].挨打方[n].挨打方].法术特效==0 and self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:取状态()  then
            条件通过=条件通过-1
        end
      end
        if 条件通过<=0 then
          self.执行流程=6
        end
    elseif  self.执行流程==63 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战"  and  self.掉血流程==nil then
        self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].扣除气血,1)
        self.执行流程=64
    elseif self.执行流程==64 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
        self.执行流程=61
    elseif self.执行流程==160 then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        self:置全屏技能(self.战斗流程[1].挨打方[1].特效[1],self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
        if self.战斗流程[1].扣除气血~=nil then
           -- self:施法流程(163, self.战斗流程[1].攻击方, self.战斗流程[1].挨打方, self.战斗流程[1].挨打方[1].特效[1], true)
           self.执行流程=163
        else
          --  self:施法流程(161, self.战斗流程[1].攻击方, self.战斗流程[1].挨打方, self.战斗流程[1].挨打方[1].特效[1], true)
          self.执行流程=161
        end

    elseif self.执行流程==161 and self.拼接特效==nil  then
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n].恢复气血 then
                        self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,2)
                end
                if self.战斗流程[1].挨打方[n].复活 then
                        self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("待战")
                        self.战斗单位[self.战斗流程[1].挨打方[n].挨打方].停止更新=false
                end
                self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
            end
            self.执行流程=162
    elseif  self.执行流程==162 then
            local 条件通过=#self.战斗流程[1].挨打方
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:取状态() then
                    条件通过=条件通过-1
                end
            end
            if 条件通过<=0 then
                self.执行流程=6
            end
    elseif  self.执行流程==163 and self.拼接特效==nil then
            self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].扣除气血,1)
            self.执行流程=164
    elseif self.执行流程==164 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
            self.执行流程=161

    elseif self.执行流程==100 then
            self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].气血,2)
            self.执行流程=101
    elseif self.执行流程==110 then
            self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].气血,2)
            self.执行流程=101
    elseif self.执行流程==111 then
           self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
           self:添加战斗提醒文字(self.战斗流程[1].名称.."使用了"..self.战斗流程[1].技能)
           self.执行流程=112
    elseif self.执行流程==112 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() and self.战斗信息提示.开关==false then
            self.执行流程=6
    elseif self.执行流程==1000 then
            for n=1,#self.战斗流程[1].挨打方 do
              self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].气血,2)
              if self.战斗流程[1].挨打方[n].伤势 ~= nil then
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置伤势(self.战斗流程[1].挨打方[n].伤势,2)
              end
              self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
            end
            self.执行流程=101
    elseif self.执行流程==101 and self.战斗单位[self.战斗流程[1].攻击方].掉血开关==false then
            self.执行流程=6
    elseif self.执行流程==102 then
            self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].气血,1)
            self.战斗单位[self.战斗流程[1].攻击方]:设置伤势(self.战斗流程[1].伤势,1)
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打")
            self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].死亡)
            if self.战斗流程[1].受益方 ~= nil and #self.战斗流程[1].受益方 >0 then
              for i=1,#self.战斗流程[1].受益方 do
                if self.战斗流程[1].受益方[i] ~= nil then
                  self.战斗单位[self.战斗流程[1].受益方[i].受益方]:设置掉血(self.战斗流程[1].受益方[i].伤害,2)
                end
              end
            end
            self.执行流程=103
    elseif self.执行流程==1020 then
            for n=1,#self.战斗流程[1].挨打方 do
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].气血,1)
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打")
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].死亡)
                self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
                if self.战斗流程[1].挨打方[n].伤势~=nil then
                  self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置伤势(self.战斗流程[1].挨打方[n].伤势,1)
                end
            end
            self.执行流程=103
    elseif self.执行流程==103 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
            self.执行流程=6
    elseif self.执行流程==104 then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:添加攻击特效("混元伞")
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:换动作("待战")
            self.执行流程=116
    elseif self.执行流程==124 then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:添加攻击特效("修罗咒")
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:换动作("待战")
            self.执行流程=116
    elseif self.执行流程==134 then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:添加攻击特效("天衣无缝")
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:换动作("待战")
            self.执行流程=116
    elseif self.执行流程==135 then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:添加攻击特效("幻镜术")
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:换动作("待战")
            self.执行流程=116
    elseif self.执行流程==114 then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:添加法术特效(self.战斗流程[1].挨打方.特效[1])
            self.战斗单位[self.战斗流程[1].挨打方.挨打方]:换动作("待战")
            self.执行流程=116
    elseif self.执行流程==116 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
           self.战斗单位[self.战斗流程[1].挨打方.挨打方]:换动作("挨打")
           self.战斗单位[self.战斗流程[1].挨打方.挨打方]:设置掉血(self.战斗流程[1].气血,1)
           self.战斗单位[self.战斗流程[1].挨打方.挨打方]:开启击退(self.战斗流程[1].挨打方.死亡)
           self.执行流程=6
    elseif self.执行流程==200 then
        for n=1,#self.战斗流程[1].挨打方 do
          if self.战斗流程[1].挨打方[n].挨打方==nil or self.战斗流程[1].挨打方[n].挨打方==0 or self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]==nil then
           table.remove(self.战斗流程[1].挨打方,n)
          end
        end
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
    
    --   if #self.战斗流程[1].挨打方 <= 0 then
    --         self.执行流程=6
    --   else

    --     self:施法流程(201, self.战斗流程[1].攻击方, self.战斗流程[1].挨打方, self.战斗流程[1].挨打方[1].特效[1], false)
    --   end
      self.执行流程=200.1
    elseif self.执行流程==200.1 then
      if self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间() then
            self.掉血流程=self.战斗流程[1].挨打方[1].特效[1]
            for n=1,#self.战斗流程[1].挨打方 do
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("待战")
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[1])
            end
            if #self.战斗流程[1].挨打方 <= 0 then
               self.执行流程=6
            else
                self.执行流程=201
            end
      end

    elseif self.执行流程==201 and  (#self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0  or  self.掉血流程==nil) then
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n].特效[2]~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
                end
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型,self.战斗流程[1].挨打方[n].护盾值)
                if self.战斗流程[1].挨打方[n].伤势~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置伤势(self.战斗流程[1].挨打方[n].伤势,self.战斗流程[1].挨打方[n].伤势类型)
                end
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打")
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡)
                
            end
            if self.战斗流程[1].气血恢复~=nil  then
                self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].气血恢复,2)
            end
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n].恢复气血~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,2)
                end
            end
            self.执行流程=202
    elseif self.执行流程==202 then
        local 条件通过=#self.战斗流程[1].挨打方
        for n=1,#self.战斗流程[1].挨打方 do
            if self.战斗流程[1].挨打方[n] ~= nil and self.战斗流程[1].挨打方[n].挨打方 ~= nil and 
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方] ~= nil and self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:取状态() then
                条件通过=条件通过-1
            end
        end
        if 条件通过<=0 then
            self.执行流程=203
        end
    elseif self.执行流程==203 and self.拼接特效==nil then
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n].状态~=nil  then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:增加状态(self.战斗流程[1].挨打方[n].状态)
                end
                self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
            end
            self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
            self.执行流程=204
    elseif self.执行流程==204 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
        self.执行流程=6
    elseif self.执行流程 == 205 and self.拼接特效 == nil then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法", true)
       --- self:施法流程(206, self.战斗流程[1].攻击方, self.战斗流程[1].挨打方, self.战斗流程[1].挨打方[1].特效[1], true)
         self:置全屏技能(self.战斗流程[1].挨打方[1].特效[1],self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
         self.执行流程=206

    elseif self.执行流程==206  and self.掉血流程==nil then
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n].特效[2]~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
                end
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型,self.战斗流程[1].挨打方[n].护盾值)
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打")
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡)
                if self.战斗流程[1].挨打方[n].恢复气血 then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,2)
                end
                self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
            end
            if self.战斗流程[1].气血恢复~=nil  then
                self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].气血恢复,2)
            end
           
            self.执行流程=202

    elseif self.执行流程==207 then
            self:流程状态处理(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].挨打方[1])
            self.执行流程=6
    elseif self.执行流程==209 then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            
           -- self:施法流程(210, self.战斗流程[1].攻击方, self.战斗流程[1].挨打方, self.战斗流程[1].挨打方[1].特效[1], false)

     
            for n=1,#self.战斗流程[1].挨打方 do
              self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[1])
              if self.战斗流程[1].挨打方[n].特效[2]~=nil then
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
              end
            end
            self.执行流程=210


    elseif self.执行流程==211 then
            local 条件通过=#self.战斗流程[1].挨打方
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n] ~= nil and self.战斗流程[1].挨打方[n].挨打方 ~= nil and 
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方] ~= nil and self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:取状态() then
                    条件通过=条件通过-1
                end
            end
            if 条件通过<=0 then
                self.执行流程=210
            end
    elseif self.执行流程==210 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
            for n=1,#self.战斗流程[1].挨打方 do
                self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
            end
            self.执行流程=212
    elseif self.执行流程==212 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战"  then
        self.执行流程=6
    elseif self.执行流程==213 then
          self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
          self:置全屏技能(self.战斗流程[1].挨打方[1].特效[1],self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
          self.执行流程=214
    elseif self.执行流程==215 then
           self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
           self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加法术特效(self.战斗流程[1].挨打方[1].特效[1])
           for n=1,#self.战斗流程[1].挨打方 do
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("待战")
           end
         self.执行流程=214
    elseif self.执行流程==214 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n].特效[2]~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
                end
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打")
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡)
            end
            self.执行流程=211
    elseif self.执行流程 == 300 then
        self.战斗单位[self.战斗流程[1].攻击方]:设置抓捕动画(self.战斗流程[1].挨打方[1].挨打方, self.战斗流程[1].宝宝, self.战斗流程[1].捕捉成功, self.战斗流程[1].名称)
        self.执行流程 = 301
    elseif self.执行流程 == 301 and not self.战斗单位[self.战斗流程[1].攻击方].抓捕开关 then
        self.执行流程 = 6
    elseif self.执行流程==400 then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        self:置全屏技能("武神怒击",self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
        self.执行流程=402
    elseif self.执行流程==400.1 then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        self:置全屏技能("破釜沉舟",self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
        self.执行流程=402
     elseif self.执行流程==400.2 then
       self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
       self:置全屏技能("翻江搅海",self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
       self.执行流程=402
     elseif self.执行流程==400.3 then
       self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        for n=1,#self.战斗流程[1].挨打方 do
            self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效("惊涛怒")
        end
        self.执行流程=402
    elseif self.执行流程==401 then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
        self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效("力劈华山")
        self.执行流程=402
    elseif self.执行流程==402 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
        self.执行流程=1
    elseif self.执行流程 == 500 then
        self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].伤害, 1)
        self.执行流程 = 501

    elseif self.执行流程 == 501 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法", true)
        for n=1,#self.战斗流程[1].挨打方 do
             self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加法术特效(self.战斗流程[1].挨打方[n].特效[1])
             if self.战斗流程[1].挨打方[n].特效[2]~=nil then
               self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
             end
        end
          self.执行流程=502
    elseif self.执行流程==502 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效 == 0  then
        for n=1,#self.战斗流程[1].挨打方 do
            self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].类型)
            self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打")
            self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡)
            self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
        end
        self.执行流程=503

    elseif self.执行流程 == 503 then
            local 条件通过 = #self.战斗流程[1].挨打方
            for n = 1, #self.战斗流程[1].挨打方 do
                if self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:取状态() then
                    条件通过 = 条件通过 - 1
                end
            end
            if 条件通过 <= 0 then
                self.战斗单位[self.战斗流程[1].攻击方].是否显示 = false
                self.执行流程 = 6
            end
    elseif self.执行流程==504 then
            for n=1,#self.战斗流程[1].挨打方 do
               if self.战斗流程[1].挨打方[n].状态~=nil  then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:增加状态(self.战斗流程[1].挨打方[n].状态)
                end
            end
            self.执行流程=6

    elseif self.执行流程 == 900 then
            if self.战斗流程[1].id == 角色信息.数字id or 0 == self.战斗流程[1].id then
                __UI弹出.提示框:打开(self.战斗流程[1].内容)
            end
            self.执行流程 = 6   
    elseif self.执行流程 == 901 then
            if self.战斗流程[1].id == 角色信息.数字id or 0 == self.战斗流程[1].id then
                界面层.聊天控件:添加文本(self.战斗流程[1].内容,"xt")


            end
            self.执行流程 = 6
    elseif self.执行流程 == 600 then
            self.战斗单位[self.战斗流程[1].挨打方[1].挨打方] = __战斗单位()
            self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:创建单位(self.战斗流程[1].挨打方[1].数据, self.战斗流程[1].挨打方[1].队伍, self.战斗流程[1].挨打方[1].挨打方)
            self.执行流程 = 6
    

    elseif self.执行流程 == 601 then
            self.战斗单位[self.战斗流程[1].攻击方].方向 = self.战斗单位[self.战斗流程[1].攻击方].逃跑方向
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("返回")
            self.战斗流程[1].等待计时 = os.time()
            self.执行流程 = 602
    elseif self.执行流程 == 602 and os.time() - self.战斗流程[1].等待计时 >= 3 then
            if false == self.战斗流程[1].成功 then
                _tp:播放特效音乐(取音效("逃跑失败"))
                self.执行流程 = 603
            else
                _tp:播放特效音乐(取音效("逃跑成功"))
                self.执行流程 = 605
                self.战斗单位[self.战斗流程[1].攻击方].逃跑开关 = true
                if nil ~= self.战斗流程[1].追加 then
                    self.战斗单位[self.战斗流程[1].追加].方向 = self.战斗单位[self.战斗流程[1].追加].逃跑方向
                    self.战斗单位[self.战斗流程[1].追加]:换动作("返回")
                    self.战斗单位[self.战斗流程[1].追加].逃跑开关 = true
                end
            end
    elseif self.执行流程 == 603 then
        self.战斗流程[1].等待计时 = os.time()
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("待战")
        self.战斗流程[1].初始方向=self.战斗单位[self.战斗流程[1].攻击方].初始方向
        self.执行流程 = 604
    elseif self.执行流程 == 604 then
        self.战斗流程[1].初始方向 = self.战斗流程[1].初始方向 + 1
        if self.战斗流程[1].初始方向 >= 4 then
            self.战斗流程[1].初始方向 = 0
        end
        self.战斗单位[self.战斗流程[1].攻击方].方向 = self.战斗流程[1].初始方向
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("待战")
        if os.time() - self.战斗流程[1].等待计时 >= 1 then
            self.战斗单位[self.战斗流程[1].攻击方].方向 = self.战斗单位[self.战斗流程[1].攻击方].初始方向
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("待战",true)
            self.执行流程 = 6
        end
    elseif self.执行流程 == 605 then
        self.战斗单位[self.战斗流程[1].攻击方].显示xy.x = self.战斗单位[self.战斗流程[1].攻击方].显示xy.x + self.战斗单位[self.战斗流程[1].攻击方].逃跑坐标
        self.战斗单位[self.战斗流程[1].攻击方].显示xy.y = self.战斗单位[self.战斗流程[1].攻击方].显示xy.y + self.战斗单位[self.战斗流程[1].攻击方].逃跑坐标
        if nil == self.战斗流程[1].追加 then
            if self.战斗单位[self.战斗流程[1].攻击方].显示xy:取距离(self.战斗单位[self.战斗流程[1].攻击方].初始xy.x, self.战斗单位[self.战斗流程[1].攻击方].初始xy.y) >= 500 then
                self.执行流程 = 606
            end
        else
            self.战斗单位[self.战斗流程[1].追加].显示xy.x = self.战斗单位[self.战斗流程[1].追加].显示xy.x + self.战斗单位[self.战斗流程[1].追加].逃跑坐标
            self.战斗单位[self.战斗流程[1].追加].显示xy.y = self.战斗单位[self.战斗流程[1].追加].显示xy.y + self.战斗单位[self.战斗流程[1].追加].逃跑坐标
            if self.战斗单位[self.战斗流程[1].攻击方].显示xy:取距离(self.战斗单位[self.战斗流程[1].攻击方].初始xy.x, self.战斗单位[self.战斗流程[1].攻击方].初始xy.y) >= 500 and
                self.战斗单位[self.战斗流程[1].追加].显示xy:取距离(self.战斗单位[self.战斗流程[1].追加].初始xy.x, self.战斗单位[self.战斗流程[1].追加].初始xy.y) >= 500 then
                self.执行流程 = 606
            end
        end
    elseif self.执行流程 == 606 then
        self.战斗单位[self.战斗流程[1].攻击方].是否显示 = false
        if self.战斗流程[1].结束 and 角色信息.数字id == self.战斗流程[1].id then
            请求服务(5506)
            self.执行流程 = 999999
        else
            self.执行流程 = 6
        end
    elseif self.执行流程 == 607 then
        self.战斗单位[self.战斗流程[1].挨打方[1].挨打方] = __战斗单位()
        self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:创建单位(self.战斗流程[1].挨打方[1].数据, self.战斗流程[1].挨打方[1].队伍, self.战斗流程[1].挨打方[1].挨打方)
        self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].是否显示 = false
        self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法", true)
        self:添加战斗提醒文字(self.战斗单位[self.战斗流程[1].攻击方].名称 .. "使用了召唤")
        self.执行流程 = 608
    elseif self.执行流程 == 608 then
        if self.战斗单位[self.战斗流程[1].攻击方].动作 == "待战" then
            self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].是否显示 = true
            self.执行流程 = 609
            self.战斗流程[1].延时等待 = os.time()
        end
    elseif self.执行流程 == 609 and os.time() - self.战斗流程[1].延时等待 >= 1 then
        self.执行流程 = 6
    elseif self.执行流程 == 610 then
        self.战斗单位[self.战斗流程[1].攻击方].状态特效[self.战斗流程[1].状态] = nil
        self.执行流程 = 6
    elseif self.执行流程 == 611 then
        if #self.战斗流程[1].挨打方==0 then
            self.执行流程=6
        else
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            for n=1,#self.战斗流程[1].挨打方 do
                 self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置飞镖(self.战斗单位[self.战斗流程[1].攻击方].显示xy,self.战斗单位[self.战斗流程[1].攻击方].初始方向,self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].死亡)
             end
             self.执行流程=612
         end
    elseif self.执行流程==612 then
            local 结束=true
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:取状态()==false then
                    结束=false
                end
            end
            if 结束 then
                for n=1,#self.战斗流程[1].挨打方 do
                    self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
                end
                self.执行流程=6
            end
    elseif self.执行流程 == 613 then
            if 0 == #self.战斗流程[1].挨打方 then
                self.执行流程 = 6
            else
                for n = 1, #self.战斗流程[1].挨打方 do
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置溅射(self.战斗单位[self.战斗流程[1].攻击方].显示xy, self.战斗单位[self.战斗流程[1].攻击方].初始方向, self.战斗流程[1].挨打方[n].伤害, self.战斗流程[1].挨打方[n].死亡)
                end
                self.执行流程 = 612
            end
    elseif self.执行流程==623 then --狂豹变形
            self:添加战斗提醒文字(self.战斗单位[self.战斗流程[1].攻击方].名称.."使用了变形")
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self.执行流程=624
    elseif self.执行流程==624 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
            self.战斗单位[self.战斗流程[1].攻击方]:更改模型(self.战斗流程[1].参数)
            self.战斗单位[self.战斗流程[1].攻击方].名称=self.战斗流程[1].参数
            self.执行流程=6
    ---------------------------------------------------------------------------------------------------------------------
    
    elseif self.执行流程==666 then
            if self.战斗流程[1].编号 and type(self.战斗流程[1].编号)=="table" then
                for k,v in pairs(self.战斗流程[1].编号) do
                    if  self.战斗单位[v] then
                        self.战斗单位[v]:取消状态("浮空")
                    end
                end
            end
            self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
            self.执行流程=6

---------------------------------------------------------------------,物理群攻
    elseif self.执行流程==667 then
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n].挨打方==nil or self.战斗流程[1].挨打方[n].挨打方==0 or self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]==nil then
                    table.remove(self.战斗流程[1].挨打方,n)
                end
            end
            self.战斗单位[self.战斗流程[1].攻击方].移动开关=true
             if self.战斗单位[self.战斗流程[1].攻击方].敌我==1 then
                    self.战斗单位[self.战斗流程[1].攻击方].移动坐标=require("GGE.坐标")(引擎.宽度2, 引擎.高度2) 
             else
                    self.战斗单位[self.战斗流程[1].攻击方].移动坐标=require("GGE.坐标")(引擎.宽度2-60, 引擎.高度2) 
            end
            self.执行流程=668
    elseif self.执行流程==668 and self.战斗单位[self.战斗流程[1].攻击方].移动开关==false then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self.执行流程=669
    elseif self.执行流程==669 and self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间() then
            for n=1,#self.战斗流程[1].挨打方 do
     
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("待战")
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[1])
            end
            if #self.战斗流程[1].挨打方 <= 0 then
                self.执行流程=6
            else
                self.执行流程=670
            end

    elseif self.执行流程==670  then
            for n=1,#self.战斗流程[1].挨打方 do
                    if self.战斗流程[1].挨打方[n].特效[2]~=nil then
                        self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[2])
                    end
             
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].伤害,self.战斗流程[1].挨打方[n].伤害类型,self.战斗流程[1].挨打方[n].护盾值)
                    if self.战斗流程[1].挨打方[n].伤势~=nil then
                        self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置伤势(self.战斗流程[1].挨打方[n].伤势,self.战斗流程[1].挨打方[n].伤势类型)
                    end
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("挨打")
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:开启击退(self.战斗流程[1].挨打方[n].死亡)

            end
            self.执行流程=671

    elseif self.执行流程==671  then
            local 条件通过=#self.战斗流程[1].挨打方
            for n=1,#self.战斗流程[1].挨打方 do
                if self.战斗流程[1].挨打方[n] ~= nil and self.战斗流程[1].挨打方[n].挨打方 ~= nil and self.战斗单位[self.战斗流程[1].挨打方[n].挨打方] ~= nil and self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:取状态() then
                    条件通过=条件通过-1
                end
            end
            if 条件通过<=0 then
                self.执行流程=672
            end
    elseif self.执行流程==672 and self.拼接特效==nil then
            for n=1,#self.战斗流程[1].挨打方 do
                self:流程状态处理(self.战斗流程[1].挨打方[n].挨打方,self.战斗流程[1].挨打方[n])
                if self.战斗流程[1].挨打方[n].额外特效~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].额外特效)
                end
                if self.战斗流程[1].挨打方[n].恢复气血~=nil then
                    self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:设置掉血(self.战斗流程[1].挨打方[n].恢复气血,2)
                end
            end

            if self.战斗流程[1].攻击方~=nil then
                if self.战斗流程[1].气血恢复~=nil  then
                    self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].气血恢复,2)
                end
            end
            self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
            if self.战斗流程[1].保护数据~=nil then
                self.战斗单位[self.战斗流程[1].保护数据.编号]:换动作("挨打")
                self.战斗单位[self.战斗流程[1].保护数据.编号]:开启击退(self.战斗流程[1].保护数据.死亡)
                self.战斗单位[self.战斗流程[1].保护数据.编号]:设置掉血(self.战斗流程[1].保护数据.伤害,self.战斗流程[1].挨打方[1].伤害类型)
            end
            if self.战斗单位[self.战斗流程[1].攻击方].影子开关 and self.战斗单位[self.战斗流程[1].攻击方].影子显示 and not self.战斗单位[self.战斗流程[1].攻击方].影子返回 then
                self.执行流程=678

            else
                if self.战斗流程[1].反震伤害~=nil then
                        self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("反震")
                        self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打")
                        self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
                        self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡)
                        self.执行流程=8
                else
                    self.执行流程=673
                end
            end
    elseif self.执行流程==673 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 then
            if self.战斗流程[1].反击伤害~=nil then
                self.执行流程=4
            elseif self.战斗流程[1].返回 then
                    self.战斗单位[self.战斗流程[1].攻击方].返回开关=true
                    self.战斗单位[self.战斗流程[1].攻击方].移动坐标= self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("返回")
                    
                    self.执行流程=5
            else
                self.执行流程=6
            end
---------------------------------------------------------------------,物理群攻
    elseif self.执行流程==674 then
            self.战斗单位[self.战斗流程[1].攻击方]:关闭影子()
            if self.战斗流程[1].挨打方 and self.战斗流程[1].挨打方[1] then
                for n=1,#self.战斗流程[1].挨打方 do
                    if self.战斗流程[1].挨打方[n].挨打方==nil or self.战斗流程[1].挨打方[n].挨打方==0 or self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]==nil then
                        table.remove(self.战斗流程[1].挨打方,n)
                    else
                        if 1 == self.敌我 then
                            距离2 = 40
                            距离 = 20
                        else
                            距离2 = -40
                            距离 = -10
                        end
                        local 坐标= self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:取移动坐标("挨打")
                        local 移动表={移动坐标=坐标}
                        self.战斗单位[self.战斗流程[1].攻击方]:设置影子动画(移动表,n)
                    end
                end
                self.战斗单位[self.战斗流程[1].攻击方]:开启影子(#self.战斗流程[1].挨打方)
                self.执行流程=675
            else
                self.执行流程=6
            end
    elseif self.执行流程==675 and self.战斗单位[self.战斗流程[1].攻击方].影子开关  then
            self.战斗单位[self.战斗流程[1].攻击方].影子移动=true
            self.执行流程=676
            ---------------------------------------------
    -- elseif self.执行流程==676 then
    --         self.战斗单位[self.战斗流程[1].攻击方].影子返回=true
    --         self.执行流程=680
    --         ----------------------------------------------------------
    elseif self.执行流程==676 and  self.战斗单位[self.战斗流程[1].攻击方].影子移动==false then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("攻击",true,self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
            self.执行流程=677
    elseif self.执行流程==677 and  self.战斗单位[self.战斗流程[1].攻击方]:取间隔() >= self.战斗单位[self.战斗流程[1].攻击方]:取中间()+self.战斗单位[self.战斗流程[1].攻击方].攻击帧  then
            for n=1,#self.战斗流程[1].挨打方 do
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:换动作("待战")
                self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]:添加攻击特效(self.战斗流程[1].挨打方[n].特效[1])
            end
            if #self.战斗流程[1].挨打方 <= 0 then
                self.战斗单位[self.战斗流程[1].攻击方]:关闭影子()
                self.执行流程=6
            else
                self.执行流程=670
            end
    elseif self.执行流程==678 and #self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].法术特效==0 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then

            if self.战斗流程[1].反震伤害~=nil or self.战斗流程[1].反击伤害~=nil then
                self.执行流程=681

            elseif self.战斗流程[1].返回 then
          
                    self.执行流程=679
            elseif self.战斗流程[2] and self.战斗流程[2].流程==674 then
        
                    table.remove(self.战斗流程,1)
                    for n=1,#self.战斗流程[1].挨打方 do
                        if self.战斗流程[1].挨打方[n].挨打方==nil or self.战斗流程[1].挨打方[n].挨打方==0 or self.战斗单位[self.战斗流程[1].挨打方[n].挨打方]==nil then
                            table.remove(self.战斗流程[1].挨打方,n)
                        end
                    end
                    if self.战斗流程[1].挨打方 and self.战斗流程[1].挨打方[1] and self.战斗流程[1].挨打方[1].挨打方 and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方] then
                        self.战斗单位[self.战斗流程[1].攻击方].影子移动= false
                        self.执行流程=676
         
                    else
                         self.执行流程=679

                    end
            else
                self.执行流程=6
            end
    elseif self.执行流程==679 then
            self.战斗单位[self.战斗流程[1].攻击方].影子返回=true
            self.执行流程=680 
    elseif self.执行流程==680 and not self.战斗单位[self.战斗流程[1].攻击方].影子返回 then
            self.战斗单位[self.战斗流程[1].攻击方]:关闭影子()
            self.执行流程=6
    elseif self.执行流程==681 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战"  and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
            if self.战斗流程[1].反震伤害~=nil then
                self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("反震")
                self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打")
                self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
                self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡)
            end
            
            if self.战斗流程[1].反震死亡 then
                self.执行流程=679
            else
                 self.战斗流程[1].反震伤害=nil
                 self.执行流程=682
            end

     
    elseif self.执行流程==682 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战"  and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
            if self.战斗流程[1].反击伤害 then
                self.战斗流程[1].群体反击 = {}
                for n=1,#self.战斗流程[1].挨打方 do
                    if self.战斗流程[1].挨打方[n] and self.战斗单位[self.战斗流程[1].挨打方[n].挨打方] then
                        table.insert(self.战斗流程[1].群体反击,n)
                    end
                end
                if self.战斗流程[1].群体反击 and self.战斗流程[1].群体反击[1] then
                        for k,v in pairs(self.战斗流程[1].群体反击) do
                            local 编号 =self.战斗流程[1].挨打方[v].挨打方
                            -- self.战斗单位[编号].移动开关=true
                            -- self.战斗单位[编号].移动坐标= self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("挨打")
                            self.战斗单位[编号]:换动作("攻击",true)
                        end
                        self.执行流程=683
                else
                      self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("反震")
                      self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打")
                      self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反击伤害,1)
                      self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反击死亡)
                      if self.战斗流程[1].反击死亡 then
                          self.执行流程=679
                      else
                          self.战斗流程[1].反击伤害=nil
                          self.执行流程=678
                      end
                end
            else
                self.战斗流程[1].反击伤害=nil
                self.执行流程=678
            end
    -- elseif self.执行流程==683 then
    --         local 检测 =true
    --         for k,v in pairs(self.战斗流程[1].群体反击) do
    --             local 编号 =self.战斗流程[1].挨打方[v].挨打方
    --             if self.战斗单位[编号].移动开关==false then
    --                 self.战斗单位[编号]:换动作("攻击",true)
    --             else
    --                 检测=false
    --             end
    --         end
    --         if 检测 then
    --             self.执行流程=684
    --         end
    elseif self.执行流程==683 then
            local 检测 =true
            for k,v in pairs(self.战斗流程[1].群体反击) do
                local 编号 =self.战斗流程[1].挨打方[v].挨打方
                if  self.战斗单位[编号]:取间隔() < self.战斗单位[编号]:取中间()+self.战斗单位[编号].攻击帧  then
                    检测=false
                end
            end
            if 检测 then
                self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打")
                self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反击伤害,1)
                self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反击死亡)
                self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("被击中")
                self.执行流程=684
            end
    elseif self.执行流程==684 then
            local 检测 =true
            for k,v in pairs(self.战斗流程[1].群体反击) do
                local 编号 =self.战斗流程[1].挨打方[v].挨打方
                if  self.战斗单位[编号].动作 ~="待战" then
                      检测=false
                end
            end
            if 检测 then
                if self.战斗流程[1].反击死亡 or self.战斗流程[1].返回 then
                    self.执行流程=679
                else
                    self.战斗流程[1].反击伤害=nil
                    self.执行流程=678
                end
            end
    -- elseif self.执行流程==685 then
    --         local 检测 =true
    --         for k,v in pairs(self.战斗流程[1].群体反击) do
    --             local 编号 =self.战斗流程[1].挨打方[v].挨打方
    --             if  self.战斗单位[编号].动作 =="待战" then
    --                  self.战斗单位[编号].返回开关=true
    --                  self.战斗单位[编号].移动坐标= self.战斗单位[编号]:取移动坐标("返回")
    --             else
    --                   检测=false
    --             end
    --         end
    --         if 检测 then
    --             self.执行流程=686
    --         end
    -- elseif self.执行流程==686 then
    --         local 检测 =true
    --         for k,v in pairs(self.战斗流程[1].群体反击) do
    --             local 编号 =self.战斗流程[1].挨打方[v].挨打方
    --             if  self.战斗单位[编号].返回开关==false then
    --                 self.战斗单位[编号]:换方向(self.战斗单位[编号].初始方向)
    --             else
    --                 检测=false
    --             end
    --         end
    --         if 检测 then
    --             if self.战斗流程[1].反击死亡 or self.战斗流程[1].返回 then
    --                 self.执行流程=679
    --             else
    --                 self.战斗流程[1].反击伤害=nil
    --                 self.执行流程=678
    --             end
    --         end
   
  




            
    elseif self.执行流程==700 then --合击
        self.战斗单位[self.战斗流程[1].攻击方]:移动计算(self.战斗流程[1], self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
        self.战斗单位[self.战斗流程[1].队友]:移动计算(self.战斗流程[1], self.战斗单位[self.战斗流程[1].挨打方[1].挨打方])
        self.执行流程=701
    elseif self.执行流程==701 then
            if self.战斗单位[self.战斗流程[1].攻击方].移动开关==false and self.战斗单位[self.战斗流程[1].队友].移动开关==false then
                self.战斗单位[self.战斗流程[1].攻击方]:换动作("攻击",true)
                self.战斗单位[self.战斗流程[1].队友]:换动作("攻击",true)
                self.执行流程=702
            end
    elseif self.执行流程==702 then
            if self.战斗单位[self.战斗流程[1].队友]:取间隔() >= self.战斗单位[self.战斗流程[1].队友]:取中间()+self.战斗单位[self.战斗流程[1].队友].攻击帧  then
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作("挨打",false,true)
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].抖动数据.开关=true
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:开启击退(self.战斗流程[1].挨打方[1].死亡)
                self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:设置掉血(self.战斗流程[1].挨打方[1].伤害,1)
                self:流程状态处理(self.战斗流程[1].挨打方[1].挨打方,self.战斗流程[1].挨打方[1])
                self:流程状态处理(self.战斗流程[1].攻击方,self.战斗流程[1])
                self.执行流程=702.1
            end
      elseif self.执行流程==702.1 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
            if self.战斗流程[1].反震伤害 then
                self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("反震")
                self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
                if not self.战斗流程[1].反震死亡 then
                    self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
                    self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡)
                end
                self.执行流程=703
            elseif self.战斗流程[1].反击伤害 then
                  if self.战斗流程[1].挨打方[1].死亡 then
                       self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("被击中")
                       self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
                       if not self.战斗流程[1].反击死亡 then
                            self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反击伤害,1)
                            self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反击死亡)
                       end
                      self.执行流程=703
                  else
                      self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:换动作("攻击",true)
                      self.执行流程=702.2
                  end
            else
                self.执行流程 = 703
            end
      elseif self.执行流程==702.2 and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取间隔() >= self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取中间()+self.战斗单位[self.战斗流程[1].挨打方[1].挨打方].攻击帧 then
            self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("被击中")
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("挨打",nil,true)
            if not self.战斗流程[1].反击死亡 then
                 self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反击伤害,1)
                 self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反击死亡)
            end
            self.执行流程=703

    elseif self.执行流程==703 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() then
                self.战斗单位[self.战斗流程[1].攻击方].返回开关 = true
                self.战斗单位[self.战斗流程[1].攻击方].移动坐标 = self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("返回")
                self.战斗单位[self.战斗流程[1].队友].返回开关 = true
                self.战斗单位[self.战斗流程[1].队友].移动坐标 = self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("返回")
                if self.战斗流程[1].反震死亡 or self.战斗流程[1].反击死亡  then
                    self.执行流程=704.1
                else
                    self.执行流程=705
                end
    elseif self.执行流程==703 and self.战斗单位[self.战斗流程[1].攻击方]:取状态() and self.战斗单位[self.战斗流程[1].队友]:取状态()  then
                self.战斗单位[self.战斗流程[1].队友].返回开关=true
                self.战斗单位[self.战斗流程[1].攻击方].移动坐标 = self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("返回")
                self.战斗单位[self.战斗流程[1].队友].返回开关 = true
                self.战斗单位[self.战斗流程[1].队友].移动坐标 = self.战斗单位[self.战斗流程[1].攻击方]:取移动坐标("返回")
                self.执行流程=704
    elseif self.执行流程==704.1 and self.战斗单位[self.战斗流程[1].攻击方].返回开关==false then
                if self.战斗流程[1].反震死亡 then
                     self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反震伤害,1)
                     self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反震死亡)
                 elseif self.战斗流程[1].反击死亡 then
                     self.战斗单位[self.战斗流程[1].攻击方]:设置掉血(self.战斗流程[1].反击伤害,1)
                     self.战斗单位[self.战斗流程[1].攻击方]:开启击退(self.战斗流程[1].反击死亡)
                 end
                 self.执行流程=705
    elseif self.执行流程==705 and self.战斗单位[self.战斗流程[1].挨打方[1].挨打方]:取状态() then
        
            self.执行流程=6

   



         













    elseif self.执行流程==707 then --动物套变身
        if __res.配置.变身造型~=1 then
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self:添加战斗提醒文字(self.战斗单位[self.战斗流程[1].攻击方].名称.."触发了动物套效果")
            self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("动物套效果")
            self.执行流程=708
        else
            if self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
                self.执行流程=6
            end
        end
      elseif self.执行流程==708 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
            self.战斗单位[self.战斗流程[1].攻击方]:更改模型(self.战斗流程[1].参数,1)
            self.执行流程=6
      elseif self.执行流程==709 then --动物套变身
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self:添加战斗提醒文字(self.战斗单位[self.战斗流程[1].攻击方].名称.."触发了天机城套装效果")
            self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("动物套效果")
            self.执行流程=710
      elseif self.执行流程==710 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
            self.战斗单位[self.战斗流程[1].攻击方]:更改模型(self.战斗流程[1].参数,1)
            self.执行流程=6
      elseif self.执行流程==711 then --动物套变身
            self.战斗单位[self.战斗流程[1].攻击方]:换动作("施法",true)
            self:添加战斗提醒文字(self.战斗单位[self.战斗流程[1].攻击方].名称.."触发了天机城套装效果")
            self.战斗单位[self.战斗流程[1].攻击方]:添加攻击特效("动物套效果")
            self.执行流程=712
      elseif self.执行流程==712 and self.战斗单位[self.战斗流程[1].攻击方].动作=="待战" then
            self.战斗单位[self.战斗流程[1].攻击方]:更改模型(self.战斗流程[1].参数,1)
            self.战斗单位[self.战斗流程[1].攻击方]:死亡处理()
            self.执行流程=6



    end





end








function 战斗主控:取消状态(内容)
    if 内容.id and 内容.名称 and self.战斗单位[内容.id] then
        self.战斗单位[内容.id]:取消状态(内容.名称) 
    end
end

  function 战斗主控:鬼魂复活(内容)
        if 内容.id and  self.战斗单位[内容.id] then
                self.战斗单位[内容.id].停止更新=false
                self.战斗单位[内容.id]:换动作("待战")
                self.战斗单位[内容.id]:设置掉血(内容.气血,2)
        end
  end

  function 战斗主控:刷新技能(内容)
    if 内容.id and  self.战斗单位[内容.id] then
        self.战斗单位[内容.id].主动技能=内容.主动技能
    end

  end
  
-- function 战斗主控:状态同步(内容)
--     local 不是状态 ={ 战意=true,超级战意=true,风灵=true,剑意=true,灵药红=true,灵药蓝=true,灵药黄=true,
--                      符咒=true,雷法崩裂=true,雷法震煞=true,雷法坤伏=true,雷法翻天=true,雷法倒海=true,
--                      五行珠金=true,五行珠木=true,五行珠水=true,五行珠火=true,五行珠土=true,人参娃娃=true,
--                      护盾=true
--     }


--     for k,v in pairs(内容) do
--         if type(v) =="table"  and v and self.战斗单位[k] then
--             for z,n in pairs(v) do
--                 if not 不是状态[z] and self.战斗单位[k].状态特效  then
--                     -- if not self.战斗单位[k].状态特效[z] then
--                     --     self.战斗单位[k]:增加状态(z)
--                     --     self.战斗单位[k].状态特效[z].回合=n.回合
--                     -- else
--                     --     self.战斗单位[k].状态特效[z].回合=n.回合
--                     -- end
--                     if self.战斗单位[k].状态特效[z] and  self.战斗单位[k].状态特效[z].回合 and n.回合 then
--                         self.战斗单位[k].状态特效[z].回合=n.回合
--                     end
--                 else
--                     if z=="护盾" then
--                         self.战斗单位[k].护盾=n.护盾值
--                     else
--                         if self.战斗单位[k][z] then
--                             self.战斗单位[k][z]  = n
--                         end
--                     end
                   
--                 end
--             end
--         end
--     end
-- end


function 战斗主控:状态同步(内容,序号)
    local 不是状态 ={ 战意=true,超级战意=true,风灵=true,剑意=true,灵药红=true,灵药蓝=true,灵药黄=true,
                     符咒=true,雷法崩裂=true,雷法震煞=true,雷法坤伏=true,雷法翻天=true,雷法倒海=true,
                     五行珠金=true,五行珠木=true,五行珠水=true,五行珠火=true,五行珠土=true,人参娃娃=true,
                     护盾=true,护体灵盾=true
    }


    for k,v in pairs(内容) do
        if type(v) =="table"  and v and self.战斗单位[k] then
            for z,n in pairs(v) do
                if not 不是状态[z] and self.战斗单位[k].状态特效  then
                    if self.战斗单位[k].状态特效[z] and n.回合 then
                        self.战斗单位[k].状态特效[z].回合=n.回合
                    end
                else
                    if z=="护盾" or z=="护体灵盾" then
                        if self.战斗单位[k].状态特效.护盾 then
                            if n.回合 then
                                self.战斗单位[k].状态特效.护盾.回合=n.回合
                            end
                            if n.护盾值 and n.护盾值~=0 then
                                self.战斗单位[k].状态特效.护盾.护盾值 = n.护盾值
                            else
                                self.战斗单位[k]:取消状态("护盾") 
                            end
                        end
                    elseif 不是状态[z] and  self.战斗单位[k][z] then
                            self.战斗单位[k][z]  = n
                    end
                   
                end
            end
            if 序号 and 序号==5519.1 then
                if self.战斗单位[k].气血<=0 then
                    if self.战斗单位[k].状态特效 and self.战斗单位[k].状态特效.复活  then
                      self.战斗单位[k]:开启击退(2)
                    else
                      self.战斗单位[k]:开启击退(1)
                    end
                else
                    for z,n in pairs(v) do
                      if not 不是状态[z] then
                          self.战斗单位[k]:增加状态(z)
                      end
                    end
                end
            end
        end
    end

    self:聊天外框战斗()
end


function 战斗主控:设置血量同步(数据)
    for k=1,#数据 do
        if self.战斗单位[k] then
            self.战斗单位[k]:结束同步(数据[k].气血, 数据[k].最大气血,数据[k].气血上限, 数据[k].魔法,数据[k].最大魔法,数据[k].愤怒)
        end
    end
  end

  function 战斗主控:设置魔法愤怒同步(数据)
        for k, v in pairs(数据) do
            if self.战斗单位[k] then
                self.战斗单位[k]:结束同步(nil, nil,nil, v.魔法,v.最大魔法,v.愤怒)
                if v.自动指令 then
                  self.战斗单位[k].数据.自动指令=v.自动指令
                end
                if v.自动战斗 then
                    self.战斗单位[k].数据.自动战斗=v.自动战斗
                    if self.战斗单位[k].类型 == "角色" and self.战斗单位[k].数字id == 角色信息.数字id then
                        self.自动开关 = v.自动战斗
                    end
                end
            end
        end
        self:设置自动()


  end




-- function 战斗主控:加载生成怪物(sj)
--     for i = 1, #sj do
--         self.战斗单位[sj[i].挨打方] = __战斗单位()
--         self.战斗单位[sj[i].挨打方]:创建单位(sj[i].数据, sj[i].队伍, sj[i].挨打方)
--         self.战斗单位[sj[i].挨打方].是否显示 = true
--     end
-- end

-- function 战斗主控:置全屏技能(jnm, 单位)


--     local qp = nil
--     self.全屏加速=1.5
--     self.掉血流程=0
--     self.掉血帧=nil
--     self.是否延迟=false
--     if jnm == "龙卷雨击" then
--     self.拼接特效 = {}
--     for s=1,7 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("龙卷雨击"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-50,y=-100}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=-50,y=-100}
--     elseif s == 3 then
--     self.拼接特效[s].偏移 = {x=-200,y=-100}
--     elseif s == 4 then
--     self.拼接特效[s].偏移 = {x=-50,y=0}
--     elseif s == 5 then
--     self.拼接特效[s].偏移 = {x=-200,y=0}
--     elseif s == 6 then
--     self.拼接特效[s].偏移 = {x=-50,y=50}
--     self.拼接特效[s].延迟 =1
--     elseif s == 7 then
--     self.拼接特效[s].偏移 = {x=-100,y=-50}
--     end
--     end
--     self.是否延迟=true
--     self.背景状态 = 1
--     self.掉血流程=6
--     self.掉血帧=42
--     self.全屏加速=2.5
--     qp = true
--     elseif jnm == "龙吟" then
--     self.拼接特效 = {}
--     for s=1,5 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("龙吟"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-80,y=-40}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=-80,y=-80}
--     elseif s == 3 then
--     self.拼接特效[s].延迟 =0.1
--     self.拼接特效[s].偏移 = {x=-80,y=-30}
--     elseif s == 4 then
--     self.拼接特效[s].偏移 = {x=-80,y=-80}
--     elseif s == 5 then
--     self.拼接特效[s].偏移 = {x=-80,y=-80}
--     end
--     end
--     self.是否延迟=true
--     self.背景状态 = 1
--     self.掉血流程=5
--     self.掉血帧=6
--     self.全屏加速=2
--     qp = true
  
--     elseif jnm == "雨落寒沙" or jnm == "子母神针" then
--     if 单位.敌我==1 then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("雨落寒沙_我方")
--     self.拼接特效[1].偏移 = {x=-130,y=-30}
--     qp = true
--     else
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("雨落寒沙_敌方")
--     self.拼接特效[1].偏移 = {x=-100,y=-30}
--     end
--     self.是否延迟=true
--     self.掉血流程=1
--     self.掉血帧=10
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "破釜沉舟" then
--     if 单位.敌我==1 then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("破釜沉舟_我方")
--     self.拼接特效[1].偏移 = {x=-300,y=-250}
--     self.全屏加速=1
--     qp = true
--     else
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("破釜沉舟_敌方")
--     self.拼接特效[1].偏移 = {x=100,y=100}
--     self.全屏加速=1
--     qp = true
--     end
--     elseif jnm == "翻江搅海" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("翻江搅海1")
--     self.拼接特效[1].偏移 = {x=-100,y=0}  --  -40 -50
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "武神怒击" then
--     self.拼接特效 = {}
--     for s=1,2 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("武神怒击"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-255,y=-200}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=-200,y=-150}
--     end
--     end
--     self.背景状态 = 3
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "飞砂走石" or jnm == "秘传飞砂走石" then
--     self.拼接特效 = {}
--     for s=1,5 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("飞砂走石"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-90,y=50}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=-90,y=10}
--     elseif s == 3 then
--     self.拼接特效[s].偏移 = {x=30,y=-50}
--     self.拼接特效[s].延迟 =0.3
--     elseif s == 4 then
--     self.拼接特效[s].偏移 = {x=-180,y=30}
--     self.拼接特效[s].延迟 =0.2
--     elseif s == 5 then
--     self.拼接特效[s].偏移 = {x=-180,y=30}
--     end
--     end
--     self.是否延迟=true
--     self.掉血流程=4
--     self.掉血帧=28
--     self.全屏加速=2
--     qp = true
--     elseif jnm == "摇头摆尾" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("摇头摆尾")
--     self.拼接特效[1].偏移 = {x=-50,y=20}
--     self.是否延迟=true
--     self.掉血流程=1
--     self.掉血帧=15
--     self.全屏加速=1.5
--     qp = true
--     elseif jnm == "绝幻魔音" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("绝幻魔音")
--     self.拼接特效[1].偏移 = {x=-100,y=-50}
--     self.是否延迟=true
--     self.掉血流程=1
--     self.掉血帧=12
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "碎甲术" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("碎甲术")
--     self.拼接特效[1].偏移 = {x=-200,y=-200}
--     self.是否延迟=true
--     self.掉血流程=1
--     self.掉血帧=17
--     self.全屏加速=0.8
--     qp = true
--     -- elseif jnm == "亢龙归海" then
--     -- self.拼接特效 = {}
--     -- self.拼接特效[1] = {}
--     -- self.拼接特效[1].特效 = 单位:加载特效("亢龙归海")
--     -- self.拼接特效[1].偏移 = {x=-90,y=10}
--     -- self.是否延迟=true
--     -- self.背景状态 = 1
--     -- self.掉血流程=1
--     -- self.掉血帧=28
--     -- self.全屏加速=2
--     -- qp = true
--     elseif jnm == "雷浪穿云" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("雷浪穿云")
--     self.拼接特效[1].偏移 = {x=-50,y=-50}
--     self.是否延迟=true
--     self.背景状态 = 1
--     self.掉血流程=1
--     self.掉血帧=10
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "刀光剑影" then
--     self.拼接特效 = {}
--     for s=1,3 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("刀光剑影"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-255,y=-200}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=-200,y=-150}
--     elseif s == 3 then
--     self.拼接特效[s].偏移 = {x=-50,y=20}
--     self.拼接特效[s].延迟 = 0.5
--     end
--     end
--     self.是否延迟=true
--     self.背景状态 = 3
--     self.掉血流程=3
--     self.掉血帧=15
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "毁灭之光" then
--     self.拼接特效 = {}
--     for s=1,3 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("毁灭之光"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-255,y=-200}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=-200,y=-150}
--     elseif s == 3 then
--     self.拼接特效[s].偏移 = {x=-50,y=20}
--     self.拼接特效[s].延迟 = 0.5
--     end
--     end
--     self.是否延迟=true
--     self.背景状态 = 3
--     self.掉血流程=3
--     self.掉血帧=8
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "叱咤风云" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("叱咤风云")
--     self.拼接特效[1].偏移 = {x=-50,y=-50}
--     self.是否延迟=true
--     self.背景状态 = 1
--     self.掉血流程=1
--     self.掉血帧=23
--     self.全屏加速=1.5
--     qp = true
--     elseif jnm == "八凶法阵" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("八凶法阵")
--     self.拼接特效[1].偏移 = {x=-80,y=-50}
--     self.是否延迟=true
--     self.背景状态 = 3
--     self.掉血流程=1
--     self.掉血帧=10
--     self.全屏加速=1.2
--     qp = true
--     elseif jnm == "天降灵葫" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("天降灵葫")
--     self.拼接特效[1].偏移 = {x=-80,y=-20}
--     self.是否延迟=true
--     self.掉血流程=1
--     self.掉血帧=10
--     self.全屏加速=1.2
--     qp = true
--     elseif jnm == "河东狮吼" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("河东狮吼")
--     self.拼接特效[1].偏移 = {x=-50,y=20}
--     self.掉血流程=1
--     self.掉血帧=10
--     self.全屏加速=0.8
--     qp = true
--     elseif jnm == "侵掠如火" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("侵掠如火")
--     self.拼接特效[1].偏移 = {x=-50,y=20}
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "其徐如林" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("其徐如林")
--     self.拼接特效[1].偏移 = {x=-50,y=20}
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "不动如山 " then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("不动如山 ")
--     self.拼接特效[1].偏移 = {x=-50,y=20}
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "其疾如风" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("其疾如风")
--     self.拼接特效[1].偏移 = {x=-50,y=20}
--     self.全屏加速=1
--     qp = true
--     elseif jnm == "停陷术" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("停陷术")
--     self.拼接特效[1].偏移 = {x=-80,y=-20}
--     self.掉血流程=1
--     self.掉血帧=18
--     qp = true
--     elseif jnm == "巨岩破" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效(jnm)
--     self.拼接特效[1].偏移 = {x=0,y=0}
--     qp = true
--     elseif jnm == "日光华" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效(jnm)
--     self.拼接特效[1].偏移 = {x=0,y=0}
--     qp = true
--     elseif jnm == "靛沧海" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效(jnm)
--     self.拼接特效[1].偏移 = {x=0,y=0}
--     qp = true
--     elseif jnm == "苍茫树" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效(jnm)
--     self.拼接特效[1].偏移 = {x=0,y=0}
--     qp = true
--     elseif jnm == "地裂火" then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效(jnm)
--     self.拼接特效[1].偏移 = {x=0,y=0}
--     qp = true
--     -- elseif jnm == "奔雷咒"   or jnm == "超级奔雷咒" then
--     -- self.拼接特效 = {}
--     -- for s=1,9 do
--     -- self.拼接特效[s] = {}
--     -- self.拼接特效[s].特效 = 单位:加载特效("奔雷咒"..s)
--     -- self.拼接特效[s].偏移 = {x=0,y=0}
--     -- if s == 1 then
--     -- self.拼接特效[s].偏移 = {x=-150,y=20}
--     -- elseif s == 2 then
--     -- self.拼接特效[s].偏移 = {x=10,y=-50}
--     -- elseif s == 3 then
--     -- self.拼接特效[s].偏移 = {x=-60,y=-20}
--     -- self.拼接特效[s].延迟 = 0.4
--     -- elseif s == 4 then
--     -- self.拼接特效[s].偏移 = {x=-120,y=10}
--     -- self.拼接特效[s].延迟 = 0.2
--     -- elseif s == 5 then
--     -- self.拼接特效[s].偏移 = {x=30,y=-50}
--     -- self.拼接特效[s].延迟 = 0.3
--     -- elseif s == 6 then
--     -- self.拼接特效[s].偏移 = {x=-60,y=-20}
--     -- self.拼接特效[s].延迟 = 0.1
--     -- elseif s == 7 then
--     -- self.拼接特效[s].偏移 = {x=-150,y=20}
--     -- elseif s == 8 then
--     -- self.拼接特效[s].偏移 = {x=10,y=-50}
--     -- elseif s == 9 then
--     -- self.拼接特效[s].偏移 = {x=-60,y=-20}
--     -- end
--     -- end
--     -- self.是否延迟=true
--     -- self.背景状态 = 1
--     -- self.掉血流程=1
--     -- self.掉血帧=10
--     -- self.全屏加速=1.2
--     -- qp = true
--   elseif jnm == "奔雷咒"  or jnm == "超级奔雷咒" then
--     self.拼接特效 = {}
--     for s=1,9 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("奔雷咒"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-150,y=20}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=10,y=-50}
--     elseif s == 3 then
--     self.拼接特效[s].偏移 = {x=-60,y=-20}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 4 then
--     self.拼接特效[s].偏移 = {x=-120,y=10}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 5 then
--     self.拼接特效[s].偏移 = {x=30,y=-50}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 6 then
--     self.拼接特效[s].偏移 = {x=-60,y=-20}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 7 then
--     self.拼接特效[s].偏移 = {x=-150,y=20}
--     elseif s == 8 then
--     self.拼接特效[s].偏移 = {x=10,y=-50}
--     elseif s == 9 then
--     self.拼接特效[s].偏移 = {x=-60,y=-20}
--     end
--     end
--     self.是否延迟=true
--     self.背景状态 = 1
--     self.掉血流程=6
--     self.掉血帧=1
--     self.全屏加速=2
--     qp = true
--     elseif jnm == "泰山压顶" or jnm == "超级泰山压顶" then
--     self.拼接特效 = {}
--     for s=1,2 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("泰山压顶"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-50,y=10}
--     elseif s == 2 then
--     self.拼接特效[s].延迟 = 0.3
--     self.拼接特效[s].偏移 = {x=-50,y=10}
--     end
--     end
--     self.是否延迟=true
--     self.背景状态 = 2
--     self.掉血流程=2  --这边是你判断有几个特效 比方说泰山有2个特效  一个特效结束 他会删除一个特效  如果掉血流程写的是2的话 就是说明 倒数第二个特效开始进入
--     self.掉血帧=3
--     self.全屏加速=1.3  --如果掉血流程写的是2 掉血帧写的是12的额话 代表 倒数第二个特效跑到第12帧开始掉血
--     qp = true
--     elseif jnm == "水漫金山" or jnm == "扶摇万里" or jnm == "超级水漫金山" then
--       self.拼接特效 = {}
--       for s=1,8 do
--         self.拼接特效[s] = {}
--         self.拼接特效[s].特效 = 单位:加载特效("水漫金山"..s)
--         self.拼接特效[s].偏移 = {x=0,y=0}
--         if s == 1 then
--         self.拼接特效[s].偏移 = {x=-150,y=-150}
--         elseif s == 2 then
--         self.拼接特效[s].偏移 = {x=-200,y=-150}
--         elseif s == 3 then
--         self.拼接特效[s].偏移 = {x=0,y=-100}
--         self.拼接特效[s].延迟 = 0.1
--         elseif s == 4 then
--         self.拼接特效[s].偏移 = {x=50,y=-100}
--         elseif s == 5 then
--         self.拼接特效[s].偏移 = {x=-150,y=0}
--         self.拼接特效[s].延迟 = 0.1
--         elseif s == 6 then
--         self.拼接特效[s].偏移 = {x=-200,y=0}
--         elseif s == 7 then
--         self.拼接特效[s].偏移 = {x=-50,y=-100}
--         elseif s == 8 then
--         self.拼接特效[s].偏移 = {x=-100,y=-100}
--         end
--       end
--     self.是否延迟=true
--     self.背景状态 = 2
--     self.掉血流程=6
--     self.掉血帧=1
--     self.全屏加速=2
--     qp = true
--     -- elseif jnm == "扶摇万里" then
--     -- -- self.掉血流程=1
--     -- -- self.掉血帧=12
--     -- -- self.背景状态 = 2
--     -- self.拼接特效 = {}
--     -- self.拼接特效[1] = {}
--     -- self.拼接特效[1].特效 = 单位:加载特效("扶摇万里")
--     -- self.拼接特效[1].偏移 = {x=-50,y=-50}
--     -- self.是否延迟=true
--     -- self.掉血流程=1
--     -- self.掉血帧=12
--     -- self.全屏加速=1.1
--     -- qp = true
--     elseif jnm == "风卷残云"  or jnm == "枯木逢春"  then
--     self.拼接特效 = {}
--     self.拼接特效[1] = {}
--     self.拼接特效[1].特效 = 单位:加载特效("风卷残云")
--     self.拼接特效[1].偏移 = {x=-50,y=20}
--     self.是否延迟=true
--     self.掉血流程=1
--     self.掉血帧=12
--     self.全屏加速=1.1
--     qp = true
--     elseif jnm == "落叶萧萧" then
--     self.拼接特效 = {}
--     for s=1,10 do
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("落叶萧萧"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-70,y=-30}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=-150,y=0}
--     self.拼接特效[s].延迟 = 0.5
--     elseif s == 3 then
--     self.拼接特效[s].偏移 = {x=-90,y=-10}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 4 then
--     self.拼接特效[s].偏移 = {x=-90,y=50}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 5 then
--     self.拼接特效[s].偏移 = {x=-150,y=40}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 6 then
--     self.拼接特效[s].偏移 = {x=-70,y=0}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 7 then
--     self.拼接特效[s].偏移 = {x=-20,y=0}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 8 then
--     self.拼接特效[s].偏移 = {x=-20,y=30}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 9 then
--     self.拼接特效[s].偏移 = {x=20,y=-30}
--     self.拼接特效[s].延迟 = 0.1
--     elseif s == 10 then
--     self.拼接特效[s].偏移 = {x=-70,y=-30}
--     self.拼接特效[s].延迟 = 0.1
--     end
--     end
--     self.是否延迟=true
--     self.掉血流程=6
--     self.掉血帧=1
--     self.全屏加速=0.8
--     qp = true
--     elseif jnm == "地狱烈火" or jnm == "超级地狱烈火" then
--     self.拼接特效 = {}
--     for s=1,31 do  ----1,31  摩托修改地狱烈火卡顿
--     self.拼接特效[s] = {}
--     self.拼接特效[s].特效 = 单位:加载特效("地狱烈火"..s)
--     self.拼接特效[s].偏移 = {x=0,y=0}
--     if s == 1 then
--     self.拼接特效[s].偏移 = {x=-300,y=50}
--     elseif s == 2 then
--     self.拼接特效[s].偏移 = {x=-300,y=-60}
--     self.拼接特效[s].延迟 = 0.2
--     elseif s == 3 then
--     self.拼接特效[s].偏移 = {x=-300,y=-60}
--     elseif s == 4 then
--     self.拼接特效[s].偏移 = {x=-300,y=50}
--     elseif s == 5 then
--     self.拼接特效[s].偏移 = {x=-300,y=50}
--     elseif s == 6 then
--     self.拼接特效[s].偏移 = {x=-300,y=150}
--     elseif s == 7 then
--     self.拼接特效[s].偏移 = {x=-300,y=150}
--     self.拼接特效[s].延迟 = 0.2
--     elseif s == 8 then
--     self.拼接特效[s].偏移 = {x=-150,y=-110}
--     elseif s == 9 then
--     self.拼接特效[s].偏移 = {x=-150,y=-110}
--     elseif s == 10 then
--     self.拼接特效[s].偏移 = {x=-150,y=0}
--     elseif s == 11 then
--     self.拼接特效[s].偏移 = {x=-150,y=0}
--     elseif s == 12 then
--     self.拼接特效[s].偏移 = {x=-150,y=80}
--     elseif s == 13 then
--     self.拼接特效[s].偏移 = {x=-150,y=80}
--     self.拼接特效[s].延迟 = 0.2
--     elseif s == 14 then
--     self.拼接特效[s].偏移 = {x=0,y=-140}
--     elseif s == 15 then
--     self.拼接特效[s].偏移 = {x=0,y=-140}
--     elseif s == 16 then
--     self.拼接特效[s].偏移 = {x=0,y=-60}
--     elseif s == 17 then
--     self.拼接特效[s].偏移 = {x=0,y=-60}
--     elseif s == 18 then
--     self.拼接特效[s].偏移 = {x=0,y=20}
--     elseif s == 19 then
--     self.拼接特效[s].偏移 = {x=0,y=20}
--     self.拼接特效[s].延迟 = 0.2
--     elseif s == 20 then
--     self.拼接特效[s].偏移 = {x=150,y=-180}
--     elseif s == 21 then
--     self.拼接特效[s].偏移 = {x=150,y=-180}
--     elseif s == 22 then
--     self.拼接特效[s].偏移 = {x=150,y=-100}
--     elseif s == 23 then
--     self.拼接特效[s].偏移 = {x=150,y=-100}
--     elseif s == 24 then
--     self.拼接特效[s].偏移 = {x=150,y=-20}
--     elseif s == 25 then
--     self.拼接特效[s].偏移 = {x=150,y=-20}
--      self.拼接特效[s].延迟 = 0.3
--     elseif s == 26 then
--     self.拼接特效[s].偏移 = {x=300,y=-230}
--     elseif s == 27 then
--     self.拼接特效[s].偏移 = {x=300,y=-230}
--     elseif s == 28 then
--     self.拼接特效[s].偏移 = {x=300,y=-150}
--     elseif s == 29 then
--     self.拼接特效[s].偏移 = {x=300,y=-150}
--     elseif s == 30 then
--     self.拼接特效[s].偏移 = {x=300,y=-70}
--     elseif s == 31 then
--     self.拼接特效[s].偏移 = {x=300,y=-70}
--     end
--     end
--     self.背景状态 = 3
--     self.是否延迟=true
--     self.掉血流程=25
--     self.掉血帧=20
--     self.全屏加速=3
--   --这边是你判断有几个特效 比方说泰山有2个特效  一个特效结束 他会删除一个特效  如果掉血流程写的是2的话 就是说明 倒数第二个特效开始进入
--   --如果掉血流程写的是2 掉血帧写的是12的额话 代表 倒数第二个特效跑到第12帧开始掉血
  
--     qp = true
  
--     end
--     if qp then
--       if 单位.敌我 == 1 then
--         self.拼接偏移.x,self.拼接偏移.y = 引擎.宽度2+220,引擎.高度2+130
--       else
--         self.拼接偏移.x,self.拼接偏移.y = 引擎.宽度2-75,引擎.高度2-75
--       end
--     end
--   end


  
function 战斗主控:置全屏技能(技能, 单位)
 -- self.受击法术名称 = 技能
  -- local 临时音乐 = 引擎.取音效(技能)
  -- if 游戏音效 > 0 and 临时音乐 ~= nil then
  --     self:音效类(临时音乐.文件, 临时音乐.资源, '1')
  -- end

  -- 初始化默认值
  self.全屏加速 = 1.5
  self.掉血流程 = 0
  self.掉血帧 = nil
  self.是否延迟 = false
  self.拼接特效 = {}
  local qp = false

  -- 定义特效配置表
  local 特效配置 = {
      龙卷雨击 = {
          数量 = 7,
          特效前缀 = "龙卷雨击",
          偏移 = {
              {x=-50,y=-100}, {x=-50,y=-100}, {x=-200,y=-100},
              {x=-50,y=0}, {x=-200,y=0}, {x=-50,y=50,延迟=1},
              {x=-100,y=-50}
          },
          背景状态 = 1,
          掉血流程 = 6,
          掉血帧 = 42,
          全屏加速 = 2.5,
          延迟 = true
      },
      龙吟 = {
          数量 = 5,
          特效前缀 = "龙吟",
          偏移 = {
              {x=-80,y=-40}, {x=-80,y=-80}, {x=-80,y=-30,延迟=0.1},
              {x=-80,y=-80}, {x=-80,y=-80}
          },
          背景状态 = 1,
          掉血流程 = 5,
          掉血帧 = 6,
          全屏加速 = 2,
          延迟 = true
      },
      雨落寒沙 = {
          数量 = 1,
          特效前缀 = function() return 单位.敌我 == 1 and "雨落寒沙_我方" or "雨落寒沙_敌方" end,
          偏移 = {
              function() return {x=单位.敌我 == 1 and -130 or -100, y=-30} end
          },
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1,
          延迟 = true
      },
      子母神针 = {
          数量 = 1,
          特效前缀 = function() return 单位.敌我 == 1 and "雨落寒沙_我方" or "雨落寒沙_敌方" end,
          偏移 = {
              function() return {x=单位.敌我 == 1 and -130 or -100, y=-30} end
          },
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1,
          延迟 = true
      },
      破釜沉舟 = {
          数量 = 1,
          特效前缀 = function() return 单位.敌我 == 1 and "破釜沉舟_我方" or "破釜沉舟_敌方" end,
          偏移 = {
              function() return {x=单位.敌我 == 1 and -300 or 100, y=单位.敌我 == 1 and -250 or 100} end
          },
          全屏加速 = 1
      },
      翻江搅海 = {
          数量 = 1,
          特效前缀 = "翻江搅海1",
          偏移 = {{x=-100,y=0}},
          全屏加速 = 1
      },
      武神怒击 = {
          数量 = 2,
          特效前缀 = "武神怒击",
          偏移 = {
              {x=-255,y=-200}, {x=-200,y=-150}
          },
          背景状态 = 3,
          全屏加速 = 1
      },
      飞砂走石 = {
          数量 = 5,
          特效前缀 = "飞砂走石",
          偏移 = {
              {x=-90,y=50}, {x=-90,y=10}, {x=30,y=-50,延迟=0.3},
              {x=-180,y=30,延迟=0.2}, {x=-180,y=30}
          },
          掉血流程 = 4,
          掉血帧 = 28,
          全屏加速 = 2,
          延迟 = true
      },
      秘传飞砂走石 = {
          数量 = 5,
          特效前缀 = "飞砂走石",
          偏移 = {
              {x=-90,y=50}, {x=-90,y=10}, {x=30,y=-50,延迟=0.3},
              {x=-180,y=30,延迟=0.2}, {x=-180,y=30}
          },
          掉血流程 = 4,
          掉血帧 = 28,
          全屏加速 = 2,
          延迟 = true
      },
      摇头摆尾 = {
          数量 = 1,
          特效前缀 = "摇头摆尾",
          偏移 = {{x=-50,y=20}},
          掉血流程 = 1,
          掉血帧 = 15,
          全屏加速 = 1.5,
          延迟 = true
      },
      绝幻魔音 = {
          数量 = 1,
          特效前缀 = "绝幻魔音",
          偏移 = {{x=-100,y=-50}},
          掉血流程 = 1,
          掉血帧 = 12,
          全屏加速 = 1,
          延迟 = true
      },
      碎甲术 = {
          数量 = 1,
          特效前缀 = "碎甲术",
          偏移 = {{x=-200,y=-200}},
          掉血流程 = 1,
          掉血帧 = 17,
          全屏加速 = 0.8,
          延迟 = true
      },
      雷浪穿云 = {
          数量 = 1,
          特效前缀 = "雷浪穿云",
          偏移 = {{x=-50,y=-50}},
          背景状态 = 1,
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1,
          延迟 = true
      },
      刀光剑影 = {
          数量 = 3,
          特效前缀 = "刀光剑影",
          偏移 = {
              {x=-255,y=-200}, {x=-200,y=-150}, {x=-50,y=20,延迟=0.5}
          },
          背景状态 = 3,
          掉血流程 = 3,
          掉血帧 = 15,
          全屏加速 = 1,
          延迟 = true
      },
      毁灭之光 = {
          数量 = 3,
          特效前缀 = "毁灭之光",
          偏移 = {
              {x=-255,y=-200}, {x=-200,y=-150}, {x=-50,y=20,延迟=0.5}
          },
          背景状态 = 3,
          掉血流程 = 3,
          掉血帧 = 8,
          全屏加速 = 1,
          延迟 = true
      },
      叱咤风云 = {
          数量 = 1,
          特效前缀 = "叱咤风云",
          偏移 = {{x=-50,y=-50}},
          背景状态 = 1,
          掉血流程 = 1,
          掉血帧 = 23,
          全屏加速 = 1.5,
          延迟 = true
      },
      八凶法阵 = {
          数量 = 1,
          特效前缀 = "八凶法阵",
          偏移 = {{x=-80,y=-50}},
          背景状态 = 3,
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1.2,
          延迟 = true
      },
      天降灵葫 = {
          数量 = 1,
          特效前缀 = "天降灵葫",
          偏移 = {{x=-80,y=-20}},
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 1.2,
          延迟 = true
      },
      河东狮吼 = {
          数量 = 1,
          特效前缀 = "河东狮吼",
          偏移 = {{x=-50,y=20}},
          掉血流程 = 1,
          掉血帧 = 10,
          全屏加速 = 0.8
      },
      侵掠如火 = {
          数量 = 1,
          特效前缀 = "侵掠如火",
          偏移 = {{x=-50,y=20}},
          全屏加速 = 1
      },
      其徐如林 = {
          数量 = 1,
          特效前缀 = "其徐如林",
          偏移 = {{x=-50,y=20}},
          全屏加速 = 1
      },
      不动如山 = {
          数量 = 1,
          特效前缀 = "不动如山",
          偏移 = {{x=-50,y=20}},
          全屏加速 = 1
      },
      其疾如风 = {
          数量 = 1,
          特效前缀 = "其疾如风",
          偏移 = {{x=-50,y=20}},
          全屏加速 = 1
      },
      停陷术 = {
          数量 = 1,
          特效前缀 = "停陷术",
          偏移 = {{x=-80,y=-20}},
          掉血流程 = 1,
          掉血帧 = 18
      },
      巨岩破 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      日光华 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      靛沧海 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      苍茫树 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      地裂火 = {
          数量 = 1,
          特效前缀 = 技能,
          偏移 = {{x=0,y=0}}
      },
      奔雷咒 = {
          数量 = 9,
          特效前缀 = "奔雷咒",
          偏移 = {
              {x=-150,y=20}, {x=10,y=-50}, {x=-60,y=-20,延迟=0.1},
              {x=-120,y=10,延迟=0.1}, {x=30,y=-50,延迟=0.1},
              {x=-60,y=-20,延迟=0.1}, {x=-150,y=20}, {x=10,y=-50},
              {x=-60,y=-20}
          },
          背景状态 = 1,
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 2,
          延迟 = true
      },
      超级奔雷咒 = {
          数量 = 9,
          特效前缀 = "奔雷咒",
          偏移 = {
              {x=-150,y=20}, {x=10,y=-50}, {x=-60,y=-20,延迟=0.1},
              {x=-120,y=10,延迟=0.1}, {x=30,y=-50,延迟=0.1},
              {x=-60,y=-20,延迟=0.1}, {x=-150,y=20}, {x=10,y=-50},
              {x=-60,y=-20}
          },
          背景状态 = 1,
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 2,
          延迟 = true
      },
      泰山压顶 = {
          数量 = 2,
          特效前缀 = "泰山压顶",
          偏移 = {
              {x=-50,y=10}, {x=-50,y=10,延迟=0.3}
          },
          背景状态 = 2,
          掉血流程 = 2,
          掉血帧 = 3,
          全屏加速 = 1.3,
          延迟 = true
      },
      超级泰山压顶 = {
          数量 = 2,
          特效前缀 = "泰山压顶",
          偏移 = {
              {x=-50,y=10}, {x=-50,y=10,延迟=0.3}
          },
          背景状态 = 2,
          掉血流程 = 2,
          掉血帧 = 3,
          全屏加速 = 1.3,
          延迟 = true
      },
      水漫金山 = {
          数量 = 8,
          特效前缀 = "水漫金山",
          偏移 = {
              {x=-150,y=-150}, {x=-200,y=-150}, {x=0,y=-100,延迟=0.1},
              {x=50,y=-100}, {x=-150,y=0,延迟=0.1}, {x=-200,y=0},
              {x=-50,y=-100}, {x=-100,y=-100}
          },
          背景状态 = 2,
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 2,
          延迟 = true
      },
      超级水漫金山 = {
          数量 = 8,
          特效前缀 = "水漫金山",
          偏移 = {
              {x=-150,y=-150}, {x=-200,y=-150}, {x=0,y=-100,延迟=0.1},
              {x=50,y=-100}, {x=-150,y=0,延迟=0.1}, {x=-200,y=0},
              {x=-50,y=-100}, {x=-100,y=-100}
          },
          背景状态 = 2,
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 2,
          延迟 = true
      },
      风卷残云 = {
          数量 = 1,
          特效前缀 = "风卷残云",
          偏移 = {{x=-50,y=20}},
          掉血流程 = 1,
          掉血帧 = 12,
          全屏加速 = 1.1,
          延迟 = true
      },
      枯木逢春 = {
          数量 = 1,
          特效前缀 = "风卷残云",
          偏移 = {{x=-50,y=20}},
          掉血流程 = 1,
          掉血帧 = 12,
          全屏加速 = 1.1,
          延迟 = true
      },
      落叶萧萧 = {
          数量 = 10,
          特效前缀 = "落叶萧萧",
          偏移 = {
              {x=-70,y=-30}, {x=-150,y=0,延迟=0.5}, {x=-90,y=-10,延迟=0.1},
              {x=-90,y=50,延迟=0.1}, {x=-150,y=40,延迟=0.1}, {x=-70,y=0,延迟=0.1},
              {x=-20,y=0,延迟=0.1}, {x=-20,y=30,延迟=0.1}, {x=20,y=-30,延迟=0.1},
              {x=-70,y=-30,延迟=0.1}
          },
          掉血流程 = 6,
          掉血帧 = 1,
          全屏加速 = 0.8,
          延迟 = true
      },
      地狱烈火 = {
          数量 = 31,
          特效前缀 = "地狱烈火",
          偏移 = {
              {x=-300,y=50}, {x=-300,y=-60,延迟=0.2}, {x=-300,y=-60},
              {x=-300,y=50}, {x=-300,y=50}, {x=-300,y=150},
              {x=-300,y=150,延迟=0.2}, {x=-150,y=-110}, {x=-150,y=-110},
              {x=-150,y=0}, {x=-150,y=0}, {x=-150,y=80},
              {x=-150,y=80,延迟=0.2}, {x=0,y=-140}, {x=0,y=-140},
              {x=0,y=-60}, {x=0,y=-60}, {x=0,y=20},
              {x=0,y=20,延迟=0.2}, {x=150,y=-180}, {x=150,y=-180},
              {x=150,y=-100}, {x=150,y=-100}, {x=150,y=-20},
              {x=150,y=-20,延迟=0.3}, {x=300,y=-230}, {x=300,y=-230},
              {x=300,y=-150}, {x=300,y=-150}, {x=300,y=-70},
              {x=300,y=-70}
          },
          背景状态 = 3,
          掉血流程 = 25,
          掉血帧 = 20,
          全屏加速 = 3,
          延迟 = true
      },
      超级地狱烈火 = {
          数量 = 31,
          特效前缀 = "地狱烈火",
          偏移 = {
              {x=-300,y=50}, {x=-300,y=-60,延迟=0.2}, {x=-300,y=-60},
              {x=-300,y=50}, {x=-300,y=50}, {x=-300,y=150},
              {x=-300,y=150,延迟=0.2}, {x=-150,y=-110}, {x=-150,y=-110},
              {x=-150,y=0}, {x=-150,y=0}, {x=-150,y=80},
              {x=-150,y=80,延迟=0.2}, {x=0,y=-140}, {x=0,y=-140},
              {x=0,y=-60}, {x=0,y=-60}, {x=0,y=20},
              {x=0,y=20,延迟=0.2}, {x=150,y=-180}, {x=150,y=-180},
              {x=150,y=-100}, {x=150,y=-100}, {x=150,y=-20},
              {x=150,y=-20,延迟=0.3}, {x=300,y=-230}, {x=300,y=-230},
              {x=300,y=-150}, {x=300,y=-150}, {x=300,y=-70},
              {x=300,y=-70}
          },
          背景状态 = 3,
          掉血流程 = 25,
          掉血帧 = 20,
          全屏加速 = 3,
          延迟 = true
      }
  }

  -- 从配置表中获取当前技能配置
  local 当前配置 = 特效配置[技能]

  if 当前配置 then
      -- 创建特效
      for s = 1, 当前配置.数量 do
          local 特效名称 = type(当前配置.特效前缀) == "function" and 当前配置.特效前缀() or
                        (当前配置.数量 > 1 and 当前配置.特效前缀..s or 当前配置.特效前缀)

          local 偏移数据 = 当前配置.偏移[s]
          if type(偏移数据) == "function" then
              偏移数据 = 偏移数据()
          end

          self.拼接特效[s] = {
              特效 = 单位:加载特效(特效名称),
              偏移 = 偏移数据 or {x=0,y=0}
          }

          if 偏移数据 and 偏移数据.延迟 then
              self.拼接特效[s].延迟 = 偏移数据.延迟
          end
      end

      -- 设置属性
      self.是否延迟 = 当前配置.延迟 or false
      self.背景状态 = 当前配置.背景状态 or nil
      self.掉血流程 = 当前配置.掉血流程 or 0
      self.掉血帧 = 当前配置.掉血帧 or nil
      self.全屏加速 = 当前配置.全屏加速 or 1.5
      qp = true
  end

  -- 设置拼接偏移
  if qp then
      if 单位.敌我 == 1 then
          self.拼接偏移.x, self.拼接偏移.y = 引擎.宽度2+220, 引擎.高度2+130
      else
          self.拼接偏移.x, self.拼接偏移.y = 引擎.宽度2-75, 引擎.高度2-75
      end
  end

end




function 战斗主控:释放()
    self.进程 = "加载"
    self.加载数量 = 0
    self.战斗单位 = {}
    self.战斗流程 = {}
    self.显示排序 = {}
    self.拼接特效 = {}
    self.状态显示 = false
    self.状态显示2 = false
    self.回合数 = 0
    self.背景状态 = 0
end


function 战斗主控:添加战斗提醒文字(q)
    self.战斗信息提示.开关 = true
    self.战斗信息提示.文字 = 标题字体:置颜色(232, 140, 32):取精灵(q)
    self.战斗信息提示.起始时间 = os.time()
end



function 战斗主控:更新(dt,x,y)
        self.显示表 = {}
        for n = 1, #self.战斗单位 do
            -- if self.进程 == "执行" then
            --     self.战斗单位[n]:更新(dt)
            -- elseif 1 == self.战斗单位[n].敌我 then
            --     self.战斗单位[n]:更新(dt * 0.3)
            -- else
                self.战斗单位[n]:更新(dt)
            --end
            table.insert(self.显示表,self.战斗单位[n])
        end
        if __多开操作 and not 战斗层.多开自动.是否可见 then
            战斗层.多开自动:打开()
        elseif self.自动开关 and not __多开操作 and not 战斗层.战斗自动.是否可见  then
            战斗层.战斗自动:打开()
            if 战斗层.多开自动.是否可见 then
              战斗层.多开自动:置可见(false)
            end
        end
    table.sort(self.显示表, 排序)
    if self.进程 == "计算" and self.战斗流程 ~= nil and self.战斗流程[1] ~= nil then
        if self.战斗流程[1].允许 == false then
            if nil == self.战斗流程[1].提示 or false == self.战斗流程[1].提示.允许 then
                self.战斗流程[1].可以执行 = true
            elseif self.战斗流程[1].提示.类型 == "法术" and nil == self.战斗流程[1].可以执行 then
                if nil == self.战斗流程[1].特技名称 then
                    if self.战斗流程[1].提示.允许 then
                        self:添加战斗提醒文字(self.战斗流程[1].提示.名称)
                        local lsmc = 分割文本(self.战斗流程[1].提示.名称, "使用了")
                         _tp:播放特效音乐(取音效(lsmc[2]))
                    end
                elseif self.战斗流程[1].提示.允许 then
                    self.战斗单位[self.战斗流程[1].攻击方]:添加特技内容(self.战斗流程[1].特技名称)
                end
                self.战斗流程[1].可以执行 = true
            end
        end
        -- if self.战斗流程[1].战斗提示 and self.战斗流程[1].战斗提示.内容 and
        --     self.战斗流程[1].战斗提示.编号 then
        --     self.战斗单位[self.战斗流程[1].战斗提示.编号]:设置提示(self.战斗流程[1].战斗提示.内容)
        --     self.战斗流程[1].战斗提示 = nil
        -- end
        if self.战斗流程[1].可以执行 and self.战斗单位[self.战斗流程[1].攻击方] ~= nil  then
            self.战斗流程[1].允许 = true
            self.执行流程 = self.战斗流程[1].流程
            self.进程 = "执行"
        end
        if nil ~= self.背景状态 then
            self.背景状态 = 1
        end
    elseif self.进程 == "执行" then
        self:流程更新()
        self:拼接动画(dt)
     
    end
end

function 战斗主控:拼接动画(dt)
    if not self.拼接特效 then return end
    for n=1,#self.拼接特效 do
      if self.拼接特效[n] then
        if self.是否延迟 then
          if self.拼接特效[n].延迟  then
            self:拼接延迟计算(n)
            break
          else
            self:拼接动画处理(n,dt)
          end
        else
          self:拼接动画处理(n,dt)
          if #self.拼接特效==self.掉血流程 then self.掉血流程=nil end
        end
      end
    end
    if #self.拼接特效==0 then self.拼接特效=nil self.背景状态=nil  end
end

function 战斗主控:拼接延迟计算(n)
  if self.拼接特效[n].延迟时间==nil then
      self.拼接特效[n].延迟时间=os.clock()+self.拼接特效[n].延迟
  elseif self.拼接特效[n].延迟时间-os.clock()<=0 then
      self.拼接特效[n].延迟=nil
      self.拼接特效[n].延迟时间=nil
  end
end

function 战斗主控:拼接动画处理(n,dt)
  self.拼接特效[n].特效:更新(dt*self.全屏加速)
  table.insert(self.显示表,self.拼接特效[n])
  if #self.拼接特效==self.掉血流程 then
    if self.掉血帧 and self.拼接特效[n].特效:取当前帧() >= self.掉血帧 then
      self.掉血流程=nil
    elseif not self.掉血帧 and self.拼接特效[n].特效:取当前帧()>=self.拼接特效[n].特效:取帧数() then
      self.掉血流程=nil
    end
  end
  if self.拼接特效[n].特效:取当前帧()>=self.拼接特效[n].特效:取帧数() then
      table.remove(self.拼接特效,n)
  end
end

function 战斗主控:显示(x, y)
    self.背景:显示(0, 0)
    self.背景圆:显示(引擎.宽度2 - 235,引擎.高度2 - 125)
    for i = 1, #self.显示表 do
        if self.显示表[i].特效 then
            self.显示表[i].特效:显示(
                math.floor(self.拼接偏移.x + self.显示表[i].偏移.x),
                math.floor(self.拼接偏移.y + self.显示表[i].偏移.y)
            )
        else
            self.显示表[i]:显示(x, y)
        end
    end
    if self.进程 == "命令" then
        self.秒显示 = 0
        self.分显示 = 0
        self.结果 = os.time() - self.命令数据.计时
        self.显示时间 = 0
        if self.结果 >= 1 then
            self.命令数据.计时 = os.time()
            self.命令数据.秒 = self.命令数据.秒 - 1
            if self.命令数据.秒 < 0 then
                if self.命令数据.分 <= 0 and self.命令数据.秒 <= 0 then
                    __UI界面.界面层.战斗界面:退出()
                    self.进程 = "等待"
                    self.显示时间 = 1
                elseif self.命令数据.秒 <= 0 then
                    self.命令数据.秒 = 9
                    self.命令数据.分 = self.命令数据.分 - 1
                end
            end
        end
        if 0 == self.显示时间 then
            self.分显示 = self.命令数据.分 + 1
            if self.分显示 > 10 then
                self.分显示 = 1
            end
            self.秒显示 = self.命令数据.秒 + 1
            if self.秒显示 > 10 then
                self.秒显示 = 1
            end
            self.数字图片[self.分显示]:显示(引擎.宽度2- 75, 60)
            self.数字图片[self.秒显示]:显示(引擎.宽度2 - 25, 60)
        end
    elseif self.进程 == "等待" then
          if  __UI界面.界面层.战斗界面.是否可见 then
              __UI界面.界面层.战斗界面:退出()
          end
          self.请等待:显示(引擎.宽度2- 75, 60)

    elseif self.进程 == "执行" then
            if self.战斗信息提示.开关 then
                if __手机 then
                    self.战斗信息提示.文字:显示(引擎.宽度2 - self.战斗信息提示.文字.宽度//2,引擎.高度 - 30)
                else
                    self.战斗信息提示.文字:显示(引擎.宽度2 - self.战斗信息提示.文字.宽度//2,引擎.高度 - 100)
                end
                
                if os.time() - self.战斗信息提示.起始时间 >= 2 then
                    self.战斗信息提示.开关 = false
                end
            end

        if self.战斗流程[1].九黎连击  then
            self.连击背景:显示(引擎.宽度-180,140)
            if self.战斗流程[1].九黎连击<10 then
                九黎连击:置颜色(255,255,255,255):取精灵(self.战斗流程[1].九黎连击):显示(引擎.宽度-160,87)
            else
                九黎连击:置颜色(255,255,255,255):取精灵(self.战斗流程[1].九黎连击):显示(引擎.宽度-177,87)
            end
        end





    end
    for n = 1, #self.战斗单位 do
        if self.战斗单位[n].掉血开关 then
            if 1 == self.战斗单位[n].伤害类型 then
                self.战斗单位[n]:掉血显示()
            elseif 3 == self.战斗单位[n].伤害类型 or 4 == self.战斗单位[n].伤害类型 then
                self.战斗单位[n]:暴击显示()
            elseif self.战斗单位[n].伤害类型==5 then
                self.战斗单位[n]:回血暴击()
            else
                self.战斗单位[n]:加血显示()
            end
        end
    end
end

return 战斗主控



   -- if self.拼接特效 then
        --     for n = 1, #self.拼接特效 do
        --         if self.拼接特效[n] then
        --             if self.拼接特效[n].延时 then
        --                 self.拼接特效[n].延时 = self.拼接特效[n].延时 - 1
        --                 if 1 == self.拼接特效[n].延时 then
        --                     self.拼接特效[n].延时 = nil
        --                 end
        --             else
        --                 self.拼接特效[n].特效:更新(dt * self.全屏加速)
        --                 if not self.拼接特效[n].延时 then
        --                     _加入显示(self.拼接特效[n])
        --                 end
        --                 if nil ~= self.拼接特效[n].结束时间 then
        --                     if not self.拼接特效[n].结束时间原始 then
        --                         self.拼接特效[n].结束时间原始 = self.拼接特效[n].结束时间
        --                     end
        --                     self.拼接特效[n].结束时间 = self.拼接特效[n].结束时间 - 1
        --                     if self.拼接特效[n].结束时间 < 30 then
        --                         self.拼接特效[n].偏移.y = self.拼接特效[n].偏移.y + 4.5
        --                     end
        --                     if self.拼接特效[n].结束时间 <= self.拼接特效[n].结束时间原始 // 3
        --                         and not self.拼接特效[n].特效:是否播放() then
        --                         self.掉血流程 = nil
        --                     end
        --                     if self.拼接特效[n].结束时间 <= self.拼接特效[n].结束时间原始 // 2
        --                         and not self.拼接特效[n].特效:是否播放() then
        --                         self.击退流程 = nil
        --                     end
        --                     if 0 == self.拼接特效[n].结束时间 and
        --                         not self.拼接特效[n].特效:是否播放() then
        --                         table.remove(self.拼接特效, n)
        --                     end
        --                 else
        --                     if math.ceil(#self.拼接特效 / 3) == n and
        --                         self.拼接特效[n].特效:取当前帧() >=
        --                         math.ceil(self.拼接特效[n].特效:取帧数() /1.9) then
        --                         self.掉血流程 = nil
        --                     end
        --                     if #self.拼接特效 == n and
        --                         self.拼接特效[n].特效:取当前帧() >=
        --                         math.ceil(self.拼接特效[n].特效:取帧数() /1.2) then
        --                         self.掉血流程 = nil
        --                     end
        --                     if not self.拼接特效[n].特效:是否播放() then
        --                         table.remove(self.拼接特效, n)
        --                     end
        --                 end
        --             end
        --         end
        --     end
        --     if 0 == #self.拼接特效 then
        --         self.拼接特效 = nil
        --         self.背景状态 = nil
        --     end
        -- end