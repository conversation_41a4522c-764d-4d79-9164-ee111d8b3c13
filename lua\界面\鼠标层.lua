-- <AUTHOR> GGELUA
-- @Last Modified by    : GGELUA2
-- @Date                : 2022-04-10 14:32:28
-- @Last Modified time  : 2024-02-23 21:05:02
local SDL = require("SDL")
function 鼠标层:初始化()
    self:置宽高(引擎.宽度, 引擎.高度)
    self.cursor = {}
    self.cursor[1]=__res:取资源动画('dlzy',0x81DD40DC,"动画")--'是否正常',--a
    self.cursor[2]=__res:取资源动画('dlzy',0xC0247799,"动画")--'是否输入',--b
    self.cursor[3]=__res:取资源动画('dlzy',0xB3662702,"动画")--'是否手指',--c
    self.cursor[4]=__res:取资源动画('dlzy',0x1733E33B,"动画")--'是否禁止',--d
    self.cursor[5]=__res:取资源动画('dlzy',0x183DC759,"动画")--'是否组队',--e
    self.cursor[6]=__res:取资源动画('dlzy',0x81DD40DC,"动画")--'是否好友',
    self.cursor[7]=__res:取资源动画('dlzy',0xB87E0F0C,"动画")--'是否交易',--r
    self.cursor[8]=__res:取资源动画('dlzy',0xB48A9B3D,"动画")--'是否法术',--g
    self.cursor[9]=__res:取资源动画('dlzy',0xCF1D211E,"动画")--'是否给予',--h
    self.cursor[10]=__res:取资源动画('dlzy',0x1FBC5273,"动画")--'是否攻击',--h
    self.cursor[11]=__res:取资源动画('dlzy',0xB352AE45,"动画")--'是否保护',--h
    self.cursor[12]=__res:取资源动画('dlzy',0xB48A9B3D,"动画")--'是否道具',--h
    self.cursor[13]=__res:取资源动画('dlzy',0xC5750B15,"动画")--'是否捕捉',--h
    self.cursor[14]=__res:取资源动画('dlzy',0x1733E33B,"动画")--'是否拉伸',--h
    self.cursor.上下箭头 =__res:取资源动画('dlzy',0x1733E33B,"动画")
    self.是否正常 = true
    self.cur = self.cursor[1]
end




function 鼠标层:更新(dt)
    if self.附加 and self.附加.更新 then
        self.附加:更新(dt)
    end
    if self.cur and self.cur.更新 then
        self.cur:更新(dt)
    end
end

function 鼠标层:显示(x, y)
    if gge.platform == 'Windows' then
        if self.附加 then
            if ggetype(self.附加)=="物品格子" then
                self.附加.小模型:显示(x+10, y+10)
            elseif ggetype(self.附加)=="技能格子" then
                self.附加.模型:显示(x+10, y+10)
            elseif ggetype(self.附加)=="家具格子" then
                self.附加:显示(x, y)
            else
                self.附加:显示(x+10, y+10)
            end
        end
        if self.cur then
            self.cur:显示(x, y)
        end
    elseif not self.是否正常 then
        if self.附加 then
            self.附加:显示(x, y)
        end
        if self.cur then
            self.cur:显示(x, y)
        end
    end
end

function 鼠标层:消息事件(msg)
    if msg.鼠标 then
        for _, v in ipairs(msg.鼠标) do
            if v.type == SDL.MOUSE_MOTION then --鼠标移动时清空
                if self.附加 and self.附加.移动 then
                    self.附加:移动()
                elseif self.是否手指 or self.是否输入  or self.是否拉伸 then
                    self:正常形状()
                elseif not self.是否正常 and  _tp.战斗中 and   (__战斗主控.进程~="命令"  or self.是否攻击) then
                      self:正常形状()
                end
            elseif v.type == SDL.MOUSE_UP then
                if v.button == SDL.BUTTON_RIGHT then
                    if self.附加 and ggetype(self.附加) ~="家具格子" then
                            self.附加 = nil
                            v.type = nil
                            if 窗口层.道具行囊.是否可见 then
                                窗口层.道具行囊:重置抓取()
                            end
                            if 窗口层.新行囊.是否可见 then
                                窗口层.新行囊:重置抓取()
                            end
                    elseif not self.是否正常 and not  _tp.战斗中 then
                            self:正常形状()
                            if 界面层.类型 then
                                界面层:重置()
                                界面层.按下=false
                                v.type = nil
                            end
                    end
                    break
                end
            end
        end
    end
end

local function _def(self, id)
    for _, k in ipairs {
        '是否正常',--a
        '是否输入',--b
        '是否手指',--c
        '是否禁止',--d
        '是否组队',--e
        '是否好友',--f
        '是否交易',--r
        '是否法术',--g
        '是否给予',--h
        '是否攻击',--i
        '是否保护',--k
        '是否道具',--l
        '是否捕捉',--m
        '是否拉伸'--n
    } do
        self[k] = false
    end
    self.cur = self.cursor[id]

end

function 鼠标层:正常形状()
    _def(self, 1)
    self.是否正常 = true
end

function 鼠标层:输入形状()
    if self.是否正常 ~= true or self.附加 then
        return
    end
    _def(self, 2)
    self.是否正常 = true
    self.是否输入 = true
end

function 鼠标层:手指形状()
    if (self.是否正常 ~= true and self.是否拉伸 ~= true) or self.附加 then
        return
    end
    _def(self, 3)
    self.是否正常 = true --因为在按钮上
    self.是否手指 = true
end

function 鼠标层:禁止形状()
    _def(self, 4)
    self.是否禁止 = true
end

function 鼠标层:组队形状()
    if not self.是否组队 and (self.是否正常 ~= true or self.附加) then
        return
    end
    _def(self, 5)
    self.是否组队 = true
end



function 鼠标层:好友形状()
    if not self.是否好友 and (self.是否正常 ~= true or self.附加) then
        return
    end
     _def(self, 6)
    self.是否好友 = true
end

function 鼠标层:交易形状()
    if not self.是否交易 and (self.是否正常 ~= true or self.附加) then
        return
    end
    _def(self, 7)
    self.是否交易 = true
end



function 鼠标层:法术形状(v)
    if not self.是否法术 and (self.是否正常 ~= true or self.附加) then
        return
    end
    _def(self,8)
    self.是否禁止 = not v
    self.是否法术 = true
end




function 鼠标层:给予形状()
    if not self.是否给予 and (self.是否正常 ~= true or self.附加) then
        return
    end
    _def(self, 9)
    self.是否给予 = true
end




function 鼠标层:攻击形状()
    if (self.是否正常 ~= true or self.附加) then
        return
    end
    _def(self, 10)
    self.是否攻击 = true
end

function 鼠标层:保护形状()
    if not self.是否保护 and (self.是否正常 ~= true or self.附加) then
        return
    end
    _def(self,11)
    self.是否保护 = true
end




function 鼠标层:道具形状()
    if not self.是否道具 and (self.是否正常 ~= true or self.附加) then
        return
    end
    _def(self,12)
    self.是否道具 = true
end


function 鼠标层:捕捉形状()
    if not self.是否捕捉 and (self.是否正常 ~= true or self.附加) then
        return
    end
    _def(self, 12)
    self.是否捕捉 = true
end

function 鼠标层:拉伸形状()
    if self.是否正常 ~= true or self.附加 then
        return
    end
    _def(self, '上下箭头')
    self.是否拉伸 = true
end

return 鼠标层:置可见(true)
