--[[
LastEditTime: 2024-04-16 11:27:54
--]]
local 属性加点 = 窗口层:创建窗口("属性加点", 0, 0, 450, 400)


local lsb = {
  "体质",
  "魔力",
  "力量",
  "耐力",
  "敏捷"
}




function 属性加点:初始化()
  self.数据=nil
  self:创建纹理精灵(function()
            置窗口背景("加点", 0, 12, 450, 400, true):显示(0, 0)
            说明字体:置颜色( 255, 255, 255)
            for i = 1, #lsb do
              __res:getPNGCC(1, 239, 339, 252, 17):显示(120, 60 + (i - 1) * 45)
              说明字体:取图像(lsb[i]):显示(33, 60 + (i - 1) * 45)
            end
            
            说明字体:取图像("剩余潜力:"):显示(33, 285)
          end
        )
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end


function 属性加点:打开(数据,类型)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.数据=数据
  self.临时潜力 = {体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
  self.潜力=数据.潜力
  self.总潜力 = 数据.潜力
  self.图像=nil 
  self.类型=类型
  self:重置显示()
end

function 属性加点:显示(x,y)
  if self.图像 then
    self.图像:显示(x,y)
  end
end


function 属性加点:重置显示()
  for i = 1, #lsb do
      self[lsb[i].."条"]:置位置(self.临时潜力[lsb[i]]/self.总潜力*100)
  end
  self.图像 = self:创建纹理精灵(function()
            说明字体:置颜色( 255, 255, 255)
            说明字体:取图像(self.潜力):显示(135, 285)
          end,1
        )
end



local 推荐加点 = 属性加点:创建蓝色按钮("推荐加点","推荐加点", 20, 330,124,41,说明字体)
function 推荐加点:左键弹起(x, y)
   if 属性加点.类型 then
        local 是否推荐 =true
        local 记录加点 = {}
        local 已加属性 = 属性加点.数据.等级*5+5-属性加点.数据.潜力
        if 属性加点.数据.潜能果~=nil then
          已加属性=已加属性+属性加点.数据.潜能果
        end
        if 属性加点.数据.月饼~=nil then
          已加属性=已加属性+属性加点.数据.月饼*2
        end
        if 属性加点.数据.五虎上将 ~= nil then
            if 属性加点.数据.五虎上将 == 1 then
              已加属性 = 已加属性 + 10
            elseif 属性加点.数据.五虎上将 == 2 then
              已加属性 = 已加属性 + 30
            elseif 属性加点.数据.五虎上将 == 3 then
              已加属性 = 已加属性 + 60
            elseif 属性加点.数据.五虎上将 >= 4 then
              已加属性 = 已加属性 + 100
            elseif 属性加点.数据.五虎上将 >= 5 then
              已加属性 = 已加属性 + 150
            end
        end
        if 属性加点.数据.飞升 then
            已加属性 = 已加属性 +100
        end
        if  属性加点.数据.加点记录 then
            for i=1,5 do
              记录加点[lsb[i]]=0
              if 属性加点.数据.加点记录[lsb[i]]~=0 and 已加属性>=5  then
                  记录加点[lsb[i]]=math.floor(属性加点.数据.加点记录[lsb[i]]/已加属性*5)
                  是否推荐 = false
              end
            end
        end
        if 是否推荐 then
            属性加点.临时潜力.力量 = 属性加点.临时潜力.力量 + 属性加点.潜力
            属性加点.潜力 = 0
		   else
            local 循环次数 = 属性加点.潜力
            for i=1,循环次数 do
                if 属性加点.潜力>0 then
                    for k,v in pairs(记录加点) do
                        if 属性加点.潜力>= v then
                            属性加点.潜力 = 属性加点.潜力-v
                            属性加点.临时潜力[k]=属性加点.临时潜力[k]+v
                        end
                    end
                end
            end
		   end
   end
  属性加点:重置显示()
end




local 重置加点 = 属性加点:创建蓝色按钮("重置","重置加点", 165, 330,124,41,说明字体)
function 重置加点:左键弹起(x, y)
      属性加点.临时潜力 = {体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
      属性加点.潜力=属性加点.总潜力
      属性加点:重置显示()
end


local 确认加点 = 属性加点:创建蓝色按钮("确认加点","确认加点", 305, 330,124,41,说明字体)
function 确认加点:左键弹起(x, y)
  if 属性加点.类型 and 属性加点.数据 then
      if 属性加点.类型== "角色" and 窗口层.人物属性.是否可见 then
          请求服务(8, 属性加点.临时潜力)
      elseif 属性加点.类型== "召唤兽"  and 属性加点.数据.认证码 and 窗口层.召唤属性.是否可见 then 
          属性加点.临时潜力.序列 =属性加点.数据.认证码
          请求服务(5004,属性加点.临时潜力)
        elseif 属性加点.类型== "坐骑" and 窗口层.坐骑属性.是否可见 then 
              if 窗口层.坐骑属性.选中 then 
                  请求服务(93,{编号=窗口层.坐骑属性.选中,加点=属性加点.临时潜力})
              else
                  __UI弹出.提示框:打开("#Y请选择正确的目标!")
              end
      end
  end
  属性加点:置可见(false)
end







for i = 1, #lsb do

local 临时函数 = 属性加点:创建我的进度(__res:getPNGCC(1, 401, 145, 305, 16):拉伸(250, 16),lsb[i].."条", 121, 61+ (i - 1) * 45, 250, 16)
function 临时函数:显示(x, y)
        说明字体:显示(x+125-说明字体:取宽度("+"..属性加点.临时潜力[lsb[i]])//2,y-3,"+"..属性加点.临时潜力[lsb[i]])
end


  local 临时函数2 = 属性加点:创建按钮(lsb[i] .. "加",380,55 + (i - 1) * 45)
  function 临时函数2:初始化()
     self:创建按钮精灵(__res:getPNGCC(1, 641, 320, 29, 29),1)
  end

  function  临时函数2:左键弹起(x, y)
        if 属性加点.潜力>0 then
            属性加点.潜力 = 属性加点.潜力-1
            属性加点.临时潜力[lsb[i]] = 属性加点.临时潜力[lsb[i]] +1
        end

        属性加点:重置显示()
    end
    local 临时函数3 = 属性加点:创建按钮(lsb[i] .. "减",85,55 + (i - 1) * 45)
    function 临时函数3:初始化()
       self:创建按钮精灵(__res:getPNGCC(1, 601, 319, 29, 29),1)
    end
    function  临时函数3:左键弹起(x, y)
        if 属性加点.临时潜力[lsb[i]]> 0 then
          属性加点.潜力 = 属性加点.潜力+1
          属性加点.临时潜力[lsb[i]] = 属性加点.临时潜力[lsb[i]] - 1
        end
        属性加点:重置显示()
    end
end

local 关闭 = 属性加点:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
      属性加点.数据=nil
      属性加点:置可见(false)
end










