--[[
LastEditTime: 2024-12-24 05:26:05
--]]

local 好友消息 = 窗口层:创建窗口("好友消息", 0, 0, 380, 515)
function 好友消息:初始化()
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  self.禁止右键=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end




function 好友消息:打开(内容)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      请求服务(25.3)
      return
  end
  self.记录=nil
  self.好友名=nil
  self.数字id=nil
  self.等级=nil
  self.模型 =nil
  self.友好=0
  self.是否好友=nil
  self.内容输入:清空()
  self:更新数据(内容)
  
end

function 好友消息:右键弹起(x,y)
      请求服务(25.3)
      self:置可见(false)

end
function 好友消息:更新数据(内容)
      if 内容 and 内容.名称 and 内容.记录 then
          self.记录 = 内容.记录
          self.好友名=内容.名称
          self.数字id=内容.id
          self.等级=内容.等级
          self.模型 = 内容.模型
          self.友好=内容.好友度 or 0
          self.是否好友=内容.是否好友
          self.聊天列表:添加内容(内容.记录,内容.模型)
          请求服务(25.2,{id=self.数字id})
      end

      if self.是否好友 then
          self.加为好友:置禁止(true)
      else
          self.加为好友:置禁止(false)
      end

     
      self:创建纹理精灵(function()
            置窗口背景("好友聊天", 0, 0, 380, 515, true):显示(0, 0)
            取白色背景(0, 0, 360, 280, true):显示(10, 85)
            取白色背景(0, 0, 360, 73, true):显示(10, 405)
            文本字体:置颜色(255,255,255,255)
            文本字体:取图像("昵 称"):显示(10,33)
            文本字体:取图像("I   D"):显示(10,61)
            文本字体:取图像("等 级"):显示(195,33)
            文本字体:取图像("友 好"):显示(195,61)
            取输入背景(0, 0, 130, 22):显示(50,30)
            取输入背景(0, 0, 130, 22):显示(50,58)
            取输入背景(0, 0, 130, 22):显示(240,58)
            取输入背景(0, 0, 130, 22):显示(240,30)
            文本字体:置颜色(0,0,0,255)
            if self.好友名 then
                文本字体:取图像(self.好友名):显示(55,33)
            end
            if self.数字id then
                文本字体:取图像(self.数字id):显示(55,61)
            end
            if self.等级 then
                  文本字体:取图像(self.等级):显示(245,33)
            end
            if self.友好 then
                文本字体:取图像(self.友好):显示(245,61)
            end
      end)



end





local 聊天列表 = 好友消息:创建列表('聊天列表', 12, 87, 356, 276)
function 聊天列表:初始化()
        self.选中精灵 = nil
        self.焦点精灵 = nil
        self.行间距 = 5
        self:自动滚动(true)
end
    
function 聊天列表:检查消息()
    return false
end
    
-- function 聊天列表:鼠标滚轮(v)
--     self:自动滚动(v)
-- end
    
function 聊天列表:添加内容(文本,模型)
       if not 文本 or type(文本)~="table" then return end
        for k, v in self:遍历控件() do
            if v.文本 then
                v:删除控件("文本")
            end
        end
        self:清空()
        for i, v in ipairs(文本) do
              local 临时列表 = self:添加()
                临时列表:创建纹理精灵(function()
                          local 时间显示=os.date("%H", v.时间)..":"..os.date("%M", v.时间)
                          if os.date("%Y", os.time())==os.date("%Y", v.时间) and os.date("%m", os.time())==os.date("%m", v.时间) and os.date("%d", os.time())~=os.date("%d", v.时间) then
                                时间显示=os.date("%d", v.时间).."("..os.date("%H", v.时间)..":"..os.date("%M", v.时间)..")"
                          elseif os.date("%Y", os.time())==os.date("%Y", v.时间) and os.date("%m", os.time())~=os.date("%m", v.时间) then
                                时间显示=os.date("%m", v.时间).."-"..os.date("%d", v.时间).."("..os.date("%H", v.时间)..":"..os.date("%M", v.时间)..")"
                          elseif os.date("%Y", os.time())~=os.date("%Y", v.时间) then
                                时间显示=os.date("%Y", v.时间).."-"..os.date("%m", v.时间).."-"..os.date("%d", v.时间).."("..os.date("%H", v.时间)..":"..os.date("%M", v.时间)..")"
                          end
                          文本字体:置颜色(__取颜色("红色")):取图像(时间显示):显示((356-文本字体:取宽度(时间显示))//2,0)
                          if v.id == 角色信息.数字id then
                              __res:取资源动画("pic/ltk", 取人物模型代码(角色信息.模型)..".png","图片"):显示(322,16)
                          elseif 模型 then
                              __res:取资源动画("pic/ltk", 取人物模型代码(模型)..".png","图片"):显示(3,16)
                          end
                end,nil,356,50)
              
                local 文本内容 = 临时列表:丰富文本('文本', 0, 16, 320, 200,true)
                local  w,h = 0,0
                for k, n in ipairs(v.内容) do
                    if n and n~="" then
                        w, h = 文本内容:置文本("#H"..n)
                    end
                end
                if w>320 then
                    w=320
                end
                文本内容:置宽高(w,h)
                if v.id == 角色信息.数字id then
                    文本内容:置坐标(321-w,16)
                else
                    文本内容:置坐标(35,16)
                end
                if h <34 then
                    h = 34
                end
                临时列表:置高度(h+16)
                临时列表:置可见(true, true)
        end
        self:自动滚动(true)
       


        

        
 end





local 内容输入 = 好友消息:创建编辑("内容输入", 12,407, 356,70)     
function 内容输入:初始化()
    self:取光标精灵()
    self:置限制字数(200)
    self:置颜色(0, 0, 0, 255)
   -- self:置模式(self.多行模式)
end





 local 加为好友= 好友消息:创建红色按钮("加为好友", "加为好友",10,375,74,22)
 function 加为好友:左键弹起(x,y)
      if 好友消息.数字id then
          请求服务(19,{id=好友消息.数字id})
      end
  end


 local 历史按钮= 好友消息:创建红色按钮("历史消息", "历史按钮",90,375,74,22)

 local 举报按钮= 好友消息:创建红色按钮("举报", "举报按钮",270,375,42,22)
 local 祝福按钮= 好友消息:创建红色按钮("梦幻祝福", "祝福按钮",10,485,74,22)
 local 说话按钮= 好友消息:创建红色按钮("按住说话(Ctrl+Z)", "说话按钮",145,485,140,22)
 local 发送按钮= 好友消息:创建红色按钮("发  送", "发送按钮",290,485,80,22)

function 发送按钮:左键弹起(x,y)
    if 好友消息.数字id and 好友消息.内容输入:取文本() and 好友消息.内容输入:取文本()~="" then
        local 发送内容={}
        local 获取内容 = 好友消息.内容输入:取文本()
        if 获取内容:find("\n") or  获取内容:find("\r\n")  then
            获取内容 = 获取内容:gsub('\r\n', '\n')
            获取内容 = 获取内容:gsub('\r', '')
            发送内容=分割文本(获取内容, "\n")
        else
            table.insert(发送内容,好友消息.内容输入:取文本())
        end
        请求服务(24,{id=好友消息.数字id,数据=发送内容})
        好友消息.内容输入:清空()
    end
end




 local 颜色按钮 = 好友消息:创建按钮('颜色按钮', 320, 375, 10, 25)

function 颜色按钮:初始化()
      self:创建按钮精灵(__res:取资源动画('dlzy',0x9D123E79),1)

end


function 颜色按钮:左键弹起(x,y)
      好友消息.频道颜色:打开()
end



local 频道颜色 =好友消息:创建控件("频道颜色", 230, 400, 145, 25)
local 颜色表 = {{"#R","红色"},{"#C","橙色"},{"#Y","黄色"},{"#G","绿色"},{"#P","青色"},{"#B","蓝色"},{"#F","紫色"},{"#W","白色"}}
function 频道颜色:初始化()
      self:置精灵(require('SDL.精灵')(0, 0, 0, 145, 25):置颜色(0, 0, 0, 200), true)
      self:置可见(false)
end

function 频道颜色:打开()
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
end
local 颜色网格 =频道颜色:创建网格("颜色网格", 5, 6, 135, 12)
function 颜色网格:初始化()
    self:创建格子(12, 12, 5, 5, 1, 8)
    for i, v in ipairs(self.子控件) do
        v:置精灵(require('SDL.精灵')(0, 0, 0, 12, 12):置颜色(__取颜色(颜色表[i][2])), true)
    end
end

function 颜色网格:获得鼠标(x,y,a)
       if 颜色表[a] then
          __UI弹出.自定义:打开(x-15,y-30,颜色表[a][2])
      end
end
function 颜色网格:左键弹起(x,y,a)
      if 颜色表[a] then
          好友消息.内容输入:插入文本(颜色表[a][1])
      end
      频道颜色:置可见(false)
 end







local 表情按钮 = 好友消息:创建按钮('表情按钮', 335, 375, 25, 25)

function 表情按钮:初始化()
    self:创建按钮精灵(__res:取资源动画('dlzy',0x590CAA9B),1)

end

function 表情按钮:左键弹起(x,y)
     -- 界面层.聊天控件.频道表情:打开(好友消息.内容输入)
      __UI弹出.频道表情:打开(好友消息.内容输入)
end


function 表情按钮:获得鼠标(x,y)
    __UI弹出.自定义:打开(x-15,y-30,"表情")
end



local 关闭 = 好友消息:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    好友消息:右键弹起()
end

