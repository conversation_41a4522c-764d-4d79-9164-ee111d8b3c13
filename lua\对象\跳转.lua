--[[
LastEditTime: 2025-05-13 20:21:15
--]]
--[[
LastEditTime: 2024-04-07 12:27:21
--]]
--[[
Author: GGELUA
Date: 2024-03-05 05:16:05
Last Modified by: GGELUA
Last Modified time: 2024-03-05 23:18:56
--]]

local 跳转 = class("跳转")
function 跳转:初始化(t)
    self.xy = require("GGE.坐标")(t.x * 20, t.y * 20):floor()
    self.精灵 =__res:取资源动画('gy',2135735436,"动画")
    self.编号 = t.编号
    self.跳转 = false
    self.矩形 = require("SDL.矩形")(self.xy.x - 50, self.xy.y - 5, 100, 50)
    self.矩形:置颜色(255, 255, 0)
    self.已发送 = true
end

function 跳转:更新(dt)
    if self.精灵 then
        self.精灵:更新(dt)
    end
    if self.矩形:检查点(__主显.主角.xy) then
        if not self.已发送 and (not 角色信息.队伍 or 角色信息.队伍==0 or (角色信息.队伍~=0 and __主显.主角.是否队长))  and
            not __全局自动走路开关 then
            __主显.主角:停止移动()
            self.已发送 = true
            coroutine.xpcall(function()
                请求服务(1003, {
                    编号 = self.编号,
                })
            end)
        end
    elseif self.已发送 then
        self.已发送 = false
    end
end

function 跳转:显示(pys)
    if self.精灵 then
        self.精灵:显示(self.xy + pys)
    end
end

return 跳转
