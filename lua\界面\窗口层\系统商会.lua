local 系统商会 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "系统商会", 0 + abbr.py.x, 5 + abbr.py.y, 424, 519)
function 系统商会:初始化()
  local nsf = require("SDL.图像")(424, 519)
  if nsf["渲染开始"](nsf) then
   -- xiao置窗口背景("系统商会", 0, 12, 417, 505, true)["显示"](xiao置窗口背景("系统商会", 0, 12, 417, 505, true), 0, 0)
    置窗口背景("系统商会", 0, 12, 417, 505, true)["显示"](置窗口背景("系统商会", 0, 12, 417, 505, true), 0, 0)

    取灰色背景(0, 0, 375, 285, true)["显示"](取灰色背景(0, 0, 375, 285, true), 22, 90)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "商业指数")["显示"](字体18["取图像"](字体18, "商业指数"), 213, 57)
    local lssj = 取输入背景(0, 0, 135, 23)
    字体18["取图像"](字体18, "单价")["显示"](字体18["取图像"](字体18, "单价"), 18, 390)
    字体18["取图像"](字体18, "数量")["显示"](字体18["取图像"](字体18, "数量"), 214, 390)
    字体18["取图像"](字体18, "总价")["显示"](字体18["取图像"](字体18, "总价"), 18, 424)
    字体18["取图像"](字体18, "现金")["显示"](字体18["取图像"](字体18, "现金"), 214, 424)
    lssj["显示"](lssj, 70, 388)
    lssj["显示"](lssj, 264, 388)
    lssj["显示"](lssj, 70, 422)
    lssj["显示"](lssj, 264, 422)
    取输入背景(0, 0, 104, 23)["显示"](取输入背景(0, 0, 104, 23), 64, 461)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 系统商会:打开(data)
  self:置可见(true)
  self.数据 = data.数据
  self.类型 = "物品店"
  -- table.print(data)
  self.环装=false
  if data.类型=="环装店" then
      self.环装=true
  end
  self.物品店["置选中"](self.物品店, true)
  local nsf = require("SDL.图像")(424, 92)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18["取图像"](字体18, "系统商会")["显示"](字体18["取图像"](字体18, "系统商会"), 20, 57)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.重置(self)
  self.刷新(self)
end
local 关闭 = 系统商会["创建我的按钮"](系统商会, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 374, 0)
function 关闭:左键弹起(x, y, msg)
  系统商会["置可见"](系统商会, false)
end
function 系统商会:宝宝店重置()
	for i=1,#self.数据  do
		if self.数据[i] then
			self.数据[i].模型=self.数据[i].名称
			self.数据[i].类型="宝宝"
			self.数据[i].类型="宝宝"
			self.数据[i].等级=0
			self.数据[i].技能={"水攻"}
			self.数据[i].资质={888,888,888,888,888,888,8888}
			self.数据[i].成长=1
			self.数据[i].属性={10,10,10,10,10,10,10}
			self.数据[i].内丹={可用内丹=6,内丹上限=6}
			self.数据[i].装备={}
			self.数据[i].参战等级 = 0
			self.数据[i].等级 = 0
			self.数据[i].气血 = 0
			self.数据[i].伤害 = 0
			self.数据[i].魔法 = 0
			self.数据[i].攻击 = 0
			self.数据[i].防御 = 0
			self.数据[i].速度 = 0
			self.数据[i].灵力 = 0
			self.数据[i].体质 = 0
			self.数据[i].魔力 = 0
			self.数据[i].力量 = 0
			self.数据[i].耐力 = 0
			self.数据[i].敏捷 = 0
			self.数据[i].潜力 = 0
			self.数据[i].忠诚 = 100
			self.数据[i].成长 = 0
			self.数据[i].装备 = {}
			self.数据[i].种类 = ""
			self.数据[i].五行 = 0
			self.数据[i].饰品 = nil
			-- self.数据[i].双五行 = 0
			self.数据[i].染色组 = 0
			self.数据[i].染色方案 = nil
			self.数据[i].参战信息 = nil
			-- self.数据[i].战斗技能 = {}
			self.数据[i].装备属性 = {
				气血 = 0,
				魔法 = 0,
				命中 = 0,
				伤害 = 0,
				防御 = 0,
				速度 = 0,
				躲避 = 0,
				灵力 = 0,
				体质 = 0,
				魔力 = 0,
				力量 = 0,
				耐力 = 0,
				敏捷 = 0,
			}
			self.数据[i].特性五维={
				力量 = 0,
				敏捷 = 0,
				耐力 = 0,
				魔力 = 0,
				体质 = 0,
			}
			self.数据[i].临时资质={
				攻击资质 = 0,
				防御资质 = 0,
				体力资质 = 0,
				法力资质 = 0,
				速度资质 = 0,
				躲闪资质 = 0,
				时间=0,
			}
			self.数据[i].饰品资质={攻击资质 = 0,
					防御资质 = 0,
					体力资质 = 0,
					法力资质 = 0,
					速度资质 = 0,
					躲闪资质 = 0,
				}
			self.数据[i].当前经验 = 0
			self.数据[i].最大经验 = 0
			self.数据[i].最大气血 = 0
			self.数据[i].最大魔法 = 0
			self.数据[i].攻击资质 = 0
			self.数据[i].防御资质 = 0
			self.数据[i].体力资质 = 0
			self.数据[i].法力资质 = 0
			self.数据[i].速度资质 = 0
			self.数据[i].躲闪资质 = 0
			self.数据[i].元宵={可用=0,炼兽真经=0,水晶糕=0,如意丹=1000,千金露=0,千金露使用=0,元宵时间=0,真经时间=0}
			self.数据[i].打书次数=0
		end
	end
end
function 系统商会:刷新数据(data)
  self.数据={}
  self.数据 = data["数据"]
  self.环装=false
  self.类型 = "物品店"
  if data.类型=="环装店" then
      self.环装=true
  end
  if data.类型 == "召唤兽店" then
    self.类型 = "召唤兽店"
    self:宝宝店重置()
  end

  self.重置(self)
  self.刷新(self)
end
function 系统商会:重置()
  self.道具网格["置物品"](self.道具网格, self.数据)
  self.选中 = nil
end
function 系统商会:刷新()
  local nsf = require("SDL.图像")(424, 130)
  if nsf["渲染开始"](nsf) then
    if 系统商会["选中"] then
      字体18["置颜色"](字体18, __取银子颜色(系统商会["数据"][系统商会["选中"]]["价格"]))
      字体18:取图像(系统商会["数据"][系统商会["选中"]]["价格"]):置混合(0):显示(80, 20-5)
      字体18["置颜色"](字体18, __取银子颜色(系统商会["数据"][系统商会["选中"]]["价格"]))
      字体18:取图像(系统商会["数据"][系统商会["选中"]]["价格"]):置混合(0):显示(80, 50-3)
    end
    字体18["置颜色"](字体18, __取银子颜色(角色信息["银子"]))
    字体18:取图像(角色信息["银子"]):置混合(0):显示(272, 50-2)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18:取图像("1 / 1"):置混合(0):显示(98, 90)
    nsf["渲染结束"](nsf)
  end
  系统商会["图像2"] = nsf["到精灵"](nsf)
  系统商会["图像2"]["置中心"](系统商会["图像2"], 0, -376)
end
local 道具网格 = 系统商会["创建网格"](系统商会, "道具网格", 45, 115, 353, 256)
function 道具网格:左键弹起(x, y, a, b, msg)
  if 系统商会["类型"] == "物品店" then
    if self.子控件[a]._spr["物品"] then
      if 系统商会["选中"] then
        self.子控件[系统商会["选中"]]._spr["确定"] = nil
      end
      系统商会["选中"] = a
      self.子控件[系统商会["选中"]]._spr["确定"] = true
      self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 430, 86, w, h, "选择", a)
      系统商会["刷新"](系统商会)
    end
  elseif self.子控件[a]._spr then
    if 系统商会["选中"] then
      if 系统商会["选中"] == a then
        __UI界面["窗口层"]["召唤兽查看"]["打开"](__UI界面["窗口层"]["召唤兽查看"], 系统商会["数据"][系统商会["选中"]])
      else
        self.子控件[系统商会["选中"]]._spr["确定"] = nil
      end
    end
    系统商会["选中"] = a
    self.子控件[系统商会["选中"]]._spr["确定"] = true
    系统商会["刷新"](系统商会)
  end
end
function 道具网格:置物品(data)
  if 系统商会["类型"] == "物品店" then
    self:置坐标(45, 115)
    self:创建格子(55, 55, 13, 13, math.ceil(#data / 5), 5, math.ceil(#data / 5) > 4)
    for i = 1, #self.子控件 do
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], "白格子", "战斗道具")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    self:创建格子(173, 64, 8, 7, math.ceil(#data / 2), 2, math.ceil(#data / 5) > 4)
    self:置坐标(33, 115)
    for i = 1, #self.子控件 do
      if data[i] then
        local lssj = __商会格子["创建"]()
        lssj["置物品"](lssj, data[i], "商会召唤兽")
        self.子控件[i]["置精灵"](self.子控件[i], lssj)
      else
        self.子控件[i]["置精灵"](self.子控件[i])
      end
    end
  end
end
for i, v in ipairs({
  {
    name = "物品店",
    x = 20,
    y = 51,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 80, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 80, 35),
    font = "物品店"
  },
  {
    name = "宠物店",
    x = 110,
    y = 51,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 80, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 80, 35),
    font = "宠物店"
  }
}) do
  local 临时函数 = 系统商会["创建我的单选按钮"](系统商会, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "物品店" then
      系统商会["类型"] = "物品店"
      发送数据(80, {
        ["类型"] = "物品店"
      })
    elseif v.name == "宠物店" then
      系统商会["类型"] = "召唤兽店"
      发送数据(80, {
        ["类型"] = "召唤兽店"
      })
    end
  end
end
for i, v in ipairs({
  {
    name = "购买",
    x = 262,
    y = 458,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 41),
    font = "购 买"
  }
}) do
  local 临时函数 = 系统商会["创建我的按钮"](系统商会, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "购买" then
      if 系统商会["选中"] then
        发送数据(80, {
          ["类型"] = "购买",
          ["数据"] = {
            ["数量"] = 1,
            ["环装"] = 系统商会.环装,
            ["类型"] = 系统商会["类型"],
            ["选中"] = 系统商会["选中"]
          }
        })
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#R请选中一个物品")
      end
    end
  end
end
