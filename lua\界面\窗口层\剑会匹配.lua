local 剑会匹配 = 窗口层:创建窗口("剑会匹配", 0, 0, 引擎.宽度, 引擎.高度)

local rx = {
  飞燕女 = 1,
  骨精灵 = 2,
  鬼潇潇 = 3,
  狐美人 = 4,
  虎头怪 = 5,
  剑侠客 = 6,
  巨魔王 = 7,
  龙太子 = 8,
  杀破狼 = 9,
  神天兵 = 10,
  巫蛮儿 = 11,
  舞天姬 = 12,
  逍遥生 = 13,
  玄彩娥 = 14,
  偃无师 = 15,
  英女侠 = 16,
  羽灵神 = 17,
  桃夭夭 = 18,
  影精灵 = 2,
}

function 剑会匹配:初始化()
  local rxzy = {0x1A23FA19,0x1A23FA20,0x1A23FA21,0x1A23FA22,0x1A23FA23,0x1A23FA24,0x1A23FA25,0x1A23FA26,0x1A23FA27,0x1A23FA28,0x1A23FA29,0x1A23FA30,0x1A23FA31,0x1A23FA32,0x1A23FA33,0x1A23FA34,0x1A23FA35,0x1A23FA36}
  self.人像组={}
	for n=1,18 do
	    self.人像组[n] = __res:取资源动画("dlzy", rxzy[n],"精灵")
	end
  self:置宽高(引擎.宽度,引擎.高度)
  self.可初始化=true

end

  
local 匹配中 = 剑会匹配:创建红色按钮("匹配中","匹配中",引擎.宽度2-165,引擎.高度2+70,120,40,说明字体)
function 匹配中:重置文字(txt,jz)
  self:置文字(120,40,txt,说明字体)
  self:置禁止(false)
  if jz then
      self:置禁止(jz)
  end
end




local 取消匹配 = 剑会匹配:创建红色按钮("取消匹配","取消匹配",引擎.宽度2+95,引擎.高度2+70,120,40,说明字体)

function 取消匹配:左键弹起(x, y)
    请求服务(67,{模式=剑会匹配.模式})
end







function 剑会匹配:打开(数据)
  self:置可见(true)
  self.人数 = 数据.人数
	self.模式 = 数据.模式
	self.时间 = 0
end


function 剑会匹配:更新(dt)
  self.时间 = self.时间 + 1
	if self.时间 ==20 then
    self.匹配中:重置文字("匹配中.")
	elseif self.时间 ==40 then
    self.匹配中:重置文字("匹配中..")
	elseif self.时间 ==60 then
    self.匹配中:重置文字("匹配中...")
	elseif self.时间 == 80 then
    self.匹配中:重置文字("匹配中")
		self.时间 = 0
	end

end

function 剑会匹配:显示(x,y)

  if self.人数 == 1 then
    self.人像组[rx[角色信息.模型]]:显示(引擎.宽度2-45,引擎.高度2-200)
  elseif self.人数 == 3 then
    for n=1,3 do
        self.人像组[rx[_tp.队伍数据[n].模型]]:显示(引擎.宽度2-185+(n-1)*138,引擎.高度2-200)
      end
  elseif self.人数 == 5 then
    for n=1,5 do
        self.人像组[rx[_tp.队伍数据[n].模型]]:显示(引擎.宽度2-340+(n-1)*138,引擎.高度2-200)
      end
  end


end










