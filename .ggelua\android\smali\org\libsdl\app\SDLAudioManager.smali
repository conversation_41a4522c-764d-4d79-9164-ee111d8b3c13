.class public Lorg/libsdl/app/SDLAudioManager;
.super Ljava/lang/Object;
.source "SDLAudioManager.java"


# static fields
.field private static final NO_DEVICES:[I

.field protected static final TAG:Ljava/lang/String; = "SDLAudio"

.field private static mAudioDeviceCallback:Landroid/media/AudioDeviceCallback;

.field protected static mAudioRecord:Landroid/media/AudioRecord;

.field protected static mAudioTrack:Landroid/media/AudioTrack;

.field protected static mContext:Landroid/content/Context;


# direct methods
.method static constructor <clinit>()V
    .locals 1

    .line 23
    const/4 v0, 0x0

    new-array v0, v0, [I

    sput-object v0, Lorg/libsdl/app/SDLAudioManager;->NO_DEVICES:[I

    return-void
.end method

.method public constructor <init>()V
    .locals 0

    .line 16
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static native addAudioDevice(ZI)V
.end method

.method public static audioClose()V
    .locals 1

    .line 473
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    if-eqz v0, :cond_0

    .line 474
    invoke-virtual {v0}, Landroid/media/AudioTrack;->stop()V

    .line 475
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    invoke-virtual {v0}, Landroid/media/AudioTrack;->release()V

    .line 476
    const/4 v0, 0x0

    sput-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    .line 478
    :cond_0
    return-void
.end method

.method public static audioOpen(IIIII)[I
    .locals 6
    .param p0, "sampleRate"    # I
    .param p1, "audioFormat"    # I
    .param p2, "desiredChannels"    # I
    .param p3, "desiredFrames"    # I
    .param p4, "deviceId"    # I

    .line 351
    const/4 v0, 0x0

    move v1, p0

    move v2, p1

    move v3, p2

    move v4, p3

    move v5, p4

    invoke-static/range {v0 .. v5}, Lorg/libsdl/app/SDLAudioManager;->open(ZIIIII)[I

    move-result-object v0

    return-object v0
.end method

.method public static audioSetThreadPriority(ZI)V
    .locals 3
    .param p0, "iscapture"    # Z
    .param p1, "device_id"    # I

    .line 494
    if-eqz p0, :cond_0

    .line 495
    :try_start_0
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "SDLAudioC"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/Thread;->setName(Ljava/lang/String;)V

    goto :goto_0

    .line 497
    :cond_0
    invoke-static {}, Ljava/lang/Thread;->currentThread()Ljava/lang/Thread;

    move-result-object v0

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "SDLAudioP"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v0, v1}, Ljava/lang/Thread;->setName(Ljava/lang/String;)V

    .line 501
    :goto_0
    const/16 v0, -0x10

    invoke-static {v0}, Landroid/os/Process;->setThreadPriority(I)V
    :try_end_0
    .catch Ljava/lang/Exception; {:try_start_0 .. :try_end_0} :catch_0

    .line 505
    goto :goto_1

    .line 503
    :catch_0
    move-exception v0

    .line 504
    .local v0, "e":Ljava/lang/Exception;
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "modify thread properties failed "

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v0}, Ljava/lang/Exception;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    const-string v2, "SDLAudio"

    invoke-static {v2, v1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 506
    .end local v0    # "e":Ljava/lang/Exception;
    :goto_1
    return-void
.end method

.method public static audioWriteByteBuffer([B)V
    .locals 5
    .param p0, "buffer"    # [B

    .line 415
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    const-string v1, "SDLAudio"

    if-nez v0, :cond_0

    .line 416
    const-string v0, "Attempted to make audio call with uninitialized audio!"

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 417
    return-void

    .line 420
    :cond_0
    const/4 v0, 0x0

    .local v0, "i":I
    :goto_0
    array-length v2, p0

    if-ge v0, v2, :cond_3

    .line 421
    sget-object v2, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    array-length v3, p0

    sub-int/2addr v3, v0

    invoke-virtual {v2, p0, v0, v3}, Landroid/media/AudioTrack;->write([BII)I

    move-result v2

    .line 422
    .local v2, "result":I
    if-lez v2, :cond_1

    .line 423
    add-int/2addr v0, v2

    goto :goto_2

    .line 424
    :cond_1
    if-nez v2, :cond_2

    .line 426
    const-wide/16 v3, 0x1

    :try_start_0
    invoke-static {v3, v4}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 429
    :goto_1
    goto :goto_2

    .line 427
    :catch_0
    move-exception v3

    goto :goto_1

    .line 434
    .end local v2    # "result":I
    :goto_2
    goto :goto_0

    .line 431
    .restart local v2    # "result":I
    :cond_2
    const-string v3, "SDL audio: error return from write(byte)"

    invoke-static {v1, v3}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 432
    return-void

    .line 435
    .end local v0    # "i":I
    .end local v2    # "result":I
    :cond_3
    return-void
.end method

.method public static audioWriteFloatBuffer([F)V
    .locals 5
    .param p0, "buffer"    # [F

    .line 358
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    const-string v1, "SDLAudio"

    if-nez v0, :cond_0

    .line 359
    const-string v0, "Attempted to make audio call with uninitialized audio!"

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 360
    return-void

    .line 363
    :cond_0
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v2, 0x15

    if-ge v0, v2, :cond_1

    .line 364
    const-string v0, "Attempted to make an incompatible audio call with uninitialized audio! (floating-point output is supported since Android 5.0 Lollipop)"

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 365
    return-void

    .line 368
    :cond_1
    const/4 v0, 0x0

    .local v0, "i":I
    :goto_0
    array-length v2, p0

    if-ge v0, v2, :cond_4

    .line 369
    sget-object v2, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    array-length v3, p0

    sub-int/2addr v3, v0

    const/4 v4, 0x0

    invoke-virtual {v2, p0, v0, v3, v4}, Landroid/media/AudioTrack;->write([FIII)I

    move-result v2

    .line 370
    .local v2, "result":I
    if-lez v2, :cond_2

    .line 371
    add-int/2addr v0, v2

    goto :goto_2

    .line 372
    :cond_2
    if-nez v2, :cond_3

    .line 374
    const-wide/16 v3, 0x1

    :try_start_0
    invoke-static {v3, v4}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 377
    :goto_1
    goto :goto_2

    .line 375
    :catch_0
    move-exception v3

    goto :goto_1

    .line 382
    .end local v2    # "result":I
    :goto_2
    goto :goto_0

    .line 379
    .restart local v2    # "result":I
    :cond_3
    const-string v3, "SDL audio: error return from write(float)"

    invoke-static {v1, v3}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 380
    return-void

    .line 383
    .end local v0    # "i":I
    .end local v2    # "result":I
    :cond_4
    return-void
.end method

.method public static audioWriteShortBuffer([S)V
    .locals 5
    .param p0, "buffer"    # [S

    .line 389
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    const-string v1, "SDLAudio"

    if-nez v0, :cond_0

    .line 390
    const-string v0, "Attempted to make audio call with uninitialized audio!"

    invoke-static {v1, v0}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 391
    return-void

    .line 394
    :cond_0
    const/4 v0, 0x0

    .local v0, "i":I
    :goto_0
    array-length v2, p0

    if-ge v0, v2, :cond_3

    .line 395
    sget-object v2, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    array-length v3, p0

    sub-int/2addr v3, v0

    invoke-virtual {v2, p0, v0, v3}, Landroid/media/AudioTrack;->write([SII)I

    move-result v2

    .line 396
    .local v2, "result":I
    if-lez v2, :cond_1

    .line 397
    add-int/2addr v0, v2

    goto :goto_2

    .line 398
    :cond_1
    if-nez v2, :cond_2

    .line 400
    const-wide/16 v3, 0x1

    :try_start_0
    invoke-static {v3, v4}, Ljava/lang/Thread;->sleep(J)V
    :try_end_0
    .catch Ljava/lang/InterruptedException; {:try_start_0 .. :try_end_0} :catch_0

    .line 403
    :goto_1
    goto :goto_2

    .line 401
    :catch_0
    move-exception v3

    goto :goto_1

    .line 408
    .end local v2    # "result":I
    :goto_2
    goto :goto_0

    .line 405
    .restart local v2    # "result":I
    :cond_2
    const-string v3, "SDL audio: error return from write(short)"

    invoke-static {v1, v3}, Landroid/util/Log;->w(Ljava/lang/String;Ljava/lang/String;)I

    .line 406
    return-void

    .line 409
    .end local v0    # "i":I
    .end local v2    # "result":I
    :cond_3
    return-void
.end method

.method public static captureClose()V
    .locals 1

    .line 482
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    if-eqz v0, :cond_0

    .line 483
    invoke-virtual {v0}, Landroid/media/AudioRecord;->stop()V

    .line 484
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    invoke-virtual {v0}, Landroid/media/AudioRecord;->release()V

    .line 485
    const/4 v0, 0x0

    sput-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    .line 487
    :cond_0
    return-void
.end method

.method public static captureOpen(IIIII)[I
    .locals 6
    .param p0, "sampleRate"    # I
    .param p1, "audioFormat"    # I
    .param p2, "desiredChannels"    # I
    .param p3, "desiredFrames"    # I
    .param p4, "deviceId"    # I

    .line 441
    const/4 v0, 0x1

    move v1, p0

    move v2, p1

    move v3, p2

    move v4, p3

    move v5, p4

    invoke-static/range {v0 .. v5}, Lorg/libsdl/app/SDLAudioManager;->open(ZIIIII)[I

    move-result-object v0

    return-object v0
.end method

.method public static captureReadByteBuffer([BZ)I
    .locals 4
    .param p0, "buffer"    # [B
    .param p1, "blocking"    # Z

    .line 464
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/4 v1, 0x0

    const/16 v2, 0x17

    if-ge v0, v2, :cond_0

    .line 465
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    array-length v2, p0

    invoke-virtual {v0, p0, v1, v2}, Landroid/media/AudioRecord;->read([BII)I

    move-result v0

    return v0

    .line 467
    :cond_0
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    array-length v2, p0

    xor-int/lit8 v3, p1, 0x1

    invoke-virtual {v0, p0, v1, v2, v3}, Landroid/media/AudioRecord;->read([BIII)I

    move-result v0

    return v0
.end method

.method public static captureReadFloatBuffer([FZ)I
    .locals 4
    .param p0, "buffer"    # [F
    .param p1, "blocking"    # Z

    .line 446
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/4 v1, 0x0

    const/16 v2, 0x17

    if-ge v0, v2, :cond_0

    .line 447
    return v1

    .line 449
    :cond_0
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    array-length v2, p0

    xor-int/lit8 v3, p1, 0x1

    invoke-virtual {v0, p0, v1, v2, v3}, Landroid/media/AudioRecord;->read([FIII)I

    move-result v0

    return v0
.end method

.method public static captureReadShortBuffer([SZ)I
    .locals 4
    .param p0, "buffer"    # [S
    .param p1, "blocking"    # Z

    .line 455
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/4 v1, 0x0

    const/16 v2, 0x17

    if-ge v0, v2, :cond_0

    .line 456
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    array-length v2, p0

    invoke-virtual {v0, p0, v1, v2}, Landroid/media/AudioRecord;->read([SII)I

    move-result v0

    return v0

    .line 458
    :cond_0
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    array-length v2, p0

    xor-int/lit8 v3, p1, 0x1

    invoke-virtual {v0, p0, v1, v2, v3}, Landroid/media/AudioRecord;->read([SIII)I

    move-result v0

    return v0
.end method

.method protected static getAudioFormatString(I)Ljava/lang/String;
    .locals 1
    .param p0, "audioFormat"    # I

    .line 62
    packed-switch p0, :pswitch_data_0

    .line 70
    invoke-static {p0}, Ljava/lang/Integer;->toString(I)Ljava/lang/String;

    move-result-object v0

    return-object v0

    .line 68
    :pswitch_0
    const-string v0, "float"

    return-object v0

    .line 64
    :pswitch_1
    const-string v0, "8-bit"

    return-object v0

    .line 66
    :pswitch_2
    const-string v0, "16-bit"

    return-object v0

    nop

    :pswitch_data_0
    .packed-switch 0x2
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch
.end method

.method public static getAudioInputDevices()[I
    .locals 3

    .line 339
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-lt v0, v1, :cond_0

    .line 340
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mContext:Landroid/content/Context;

    const-string v1, "audio"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 341
    .local v0, "audioManager":Landroid/media/AudioManager;
    const/4 v1, 0x1

    invoke-virtual {v0, v1}, Landroid/media/AudioManager;->getDevices(I)[Landroid/media/AudioDeviceInfo;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v1

    sget-object v2, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;->INSTANCE:Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;

    invoke-interface {v1, v2}, Ljava/util/stream/Stream;->mapToInt(Ljava/util/function/ToIntFunction;)Ljava/util/stream/IntStream;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/stream/IntStream;->toArray()[I

    move-result-object v1

    return-object v1

    .line 343
    .end local v0    # "audioManager":Landroid/media/AudioManager;
    :cond_0
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->NO_DEVICES:[I

    return-object v0
.end method

.method public static getAudioOutputDevices()[I
    .locals 3

    .line 327
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-lt v0, v1, :cond_0

    .line 328
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mContext:Landroid/content/Context;

    const-string v1, "audio"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 329
    .local v0, "audioManager":Landroid/media/AudioManager;
    const/4 v1, 0x2

    invoke-virtual {v0, v1}, Landroid/media/AudioManager;->getDevices(I)[Landroid/media/AudioDeviceInfo;

    move-result-object v1

    invoke-static {v1}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v1

    sget-object v2, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;->INSTANCE:Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda2;

    invoke-interface {v1, v2}, Ljava/util/stream/Stream;->mapToInt(Ljava/util/function/ToIntFunction;)Ljava/util/stream/IntStream;

    move-result-object v1

    invoke-interface {v1}, Ljava/util/stream/IntStream;->toArray()[I

    move-result-object v1

    return-object v1

    .line 331
    .end local v0    # "audioManager":Landroid/media/AudioManager;
    :cond_0
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->NO_DEVICES:[I

    return-object v0
.end method

.method private static getInputAudioDeviceInfo(I)Landroid/media/AudioDeviceInfo;
    .locals 4
    .param p0, "deviceId"    # I

    .line 286
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/4 v1, 0x0

    const/16 v2, 0x18

    if-lt v0, v2, :cond_0

    .line 287
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mContext:Landroid/content/Context;

    const-string v2, "audio"

    invoke-virtual {v0, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 288
    .local v0, "audioManager":Landroid/media/AudioManager;
    const/4 v2, 0x1

    invoke-virtual {v0, v2}, Landroid/media/AudioManager;->getDevices(I)[Landroid/media/AudioDeviceInfo;

    move-result-object v2

    invoke-static {v2}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v2

    new-instance v3, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda0;

    invoke-direct {v3, p0}, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda0;-><init>(I)V

    .line 289
    invoke-interface {v2, v3}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object v2

    .line 290
    invoke-interface {v2}, Ljava/util/stream/Stream;->findFirst()Ljava/util/Optional;

    move-result-object v2

    .line 291
    invoke-virtual {v2, v1}, Ljava/util/Optional;->orElse(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/media/AudioDeviceInfo;

    .line 288
    return-object v1

    .line 293
    .end local v0    # "audioManager":Landroid/media/AudioManager;
    :cond_0
    return-object v1
.end method

.method private static getOutputAudioDeviceInfo(I)Landroid/media/AudioDeviceInfo;
    .locals 4
    .param p0, "deviceId"    # I

    .line 298
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/4 v1, 0x0

    const/16 v2, 0x18

    if-lt v0, v2, :cond_0

    .line 299
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mContext:Landroid/content/Context;

    const-string v2, "audio"

    invoke-virtual {v0, v2}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 300
    .local v0, "audioManager":Landroid/media/AudioManager;
    const/4 v2, 0x2

    invoke-virtual {v0, v2}, Landroid/media/AudioManager;->getDevices(I)[Landroid/media/AudioDeviceInfo;

    move-result-object v2

    invoke-static {v2}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v2

    new-instance v3, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda1;

    invoke-direct {v3, p0}, Lorg/libsdl/app/SDLAudioManager$$ExternalSyntheticLambda1;-><init>(I)V

    .line 301
    invoke-interface {v2, v3}, Ljava/util/stream/Stream;->filter(Ljava/util/function/Predicate;)Ljava/util/stream/Stream;

    move-result-object v2

    .line 302
    invoke-interface {v2}, Ljava/util/stream/Stream;->findFirst()Ljava/util/Optional;

    move-result-object v2

    .line 303
    invoke-virtual {v2, v1}, Ljava/util/Optional;->orElse(Ljava/lang/Object;)Ljava/lang/Object;

    move-result-object v1

    check-cast v1, Landroid/media/AudioDeviceInfo;

    .line 300
    return-object v1

    .line 305
    .end local v0    # "audioManager":Landroid/media/AudioManager;
    :cond_0
    return-object v1
.end method

.method public static initialize()V
    .locals 2

    .line 28
    const/4 v0, 0x0

    sput-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    .line 29
    sput-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    .line 30
    sput-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioDeviceCallback:Landroid/media/AudioDeviceCallback;

    .line 32
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-lt v0, v1, :cond_0

    .line 34
    new-instance v0, Lorg/libsdl/app/SDLAudioManager$1;

    invoke-direct {v0}, Lorg/libsdl/app/SDLAudioManager$1;-><init>()V

    sput-object v0, Lorg/libsdl/app/SDLAudioManager;->mAudioDeviceCallback:Landroid/media/AudioDeviceCallback;

    .line 46
    :cond_0
    return-void
.end method

.method static synthetic lambda$getInputAudioDeviceInfo$0(ILandroid/media/AudioDeviceInfo;)Z
    .locals 1
    .param p0, "deviceId"    # I
    .param p1, "deviceInfo"    # Landroid/media/AudioDeviceInfo;

    .line 289
    invoke-virtual {p1}, Landroid/media/AudioDeviceInfo;->getId()I

    move-result v0

    if-ne v0, p0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method static synthetic lambda$getOutputAudioDeviceInfo$1(ILandroid/media/AudioDeviceInfo;)Z
    .locals 1
    .param p0, "deviceId"    # I
    .param p1, "deviceInfo"    # Landroid/media/AudioDeviceInfo;

    .line 301
    invoke-virtual {p1}, Landroid/media/AudioDeviceInfo;->getId()I

    move-result v0

    if-ne v0, p0, :cond_0

    const/4 v0, 0x1

    goto :goto_0

    :cond_0
    const/4 v0, 0x0

    :goto_0
    return v0
.end method

.method public static native nativeSetupJNI()I
.end method

.method protected static open(ZIIIII)[I
    .locals 27
    .param p0, "isCapture"    # Z
    .param p1, "sampleRate"    # I
    .param p2, "audioFormat"    # I
    .param p3, "desiredChannels"    # I
    .param p4, "desiredFrames"    # I
    .param p5, "deviceId"    # I

    .line 79
    move/from16 v0, p1

    move/from16 v1, p3

    move/from16 v2, p4

    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    const-string v4, "Opening "

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v5, "capture"

    const-string v6, "playback"

    if-eqz p0, :cond_0

    move-object v7, v5

    goto :goto_0

    :cond_0
    move-object v7, v6

    :goto_0
    invoke-virtual {v3, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v7, ", requested "

    invoke-virtual {v3, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v7, " frames of "

    invoke-virtual {v3, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v8, " channel "

    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-static/range {p2 .. p2}, Lorg/libsdl/app/SDLAudioManager;->getAudioFormatString(I)Ljava/lang/String;

    move-result-object v9

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v9, " audio at "

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v10, " Hz"

    invoke-virtual {v3, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    const-string v11, "SDLAudio"

    invoke-static {v11, v3}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 82
    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v12, 0x15

    const/4 v13, 0x2

    if-ge v3, v12, :cond_1

    .line 83
    if-le v1, v13, :cond_1

    .line 84
    const/4 v1, 0x2

    .line 89
    .end local p3    # "desiredChannels":I
    .local v1, "desiredChannels":I
    :cond_1
    sget v3, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v14, 0x16

    if-ge v3, v14, :cond_3

    .line 90
    const/16 v3, 0x1f40

    if-ge v0, v3, :cond_2

    .line 91
    const/16 v0, 0x1f40

    .end local p1    # "sampleRate":I
    .local v0, "sampleRate":I
    goto :goto_1

    .line 92
    .end local v0    # "sampleRate":I
    .restart local p1    # "sampleRate":I
    :cond_2
    const v3, 0xbb80

    if-le v0, v3, :cond_3

    .line 93
    const v0, 0xbb80

    .line 97
    .end local p1    # "sampleRate":I
    .restart local v0    # "sampleRate":I
    :cond_3
    :goto_1
    const/16 v3, 0x17

    const/4 v14, 0x4

    move/from16 v15, p2

    if-ne v15, v14, :cond_5

    .line 98
    if-eqz p0, :cond_4

    const/16 v12, 0x17

    .line 99
    .local v12, "minSDKVersion":I
    :cond_4
    sget v13, Landroid/os/Build$VERSION;->SDK_INT:I

    if-ge v13, v12, :cond_5

    .line 100
    const/4 v13, 0x2

    .end local p2    # "audioFormat":I
    .local v13, "audioFormat":I
    goto :goto_2

    .line 103
    .end local v12    # "minSDKVersion":I
    .end local v13    # "audioFormat":I
    .restart local p2    # "audioFormat":I
    :cond_5
    move v13, v15

    .end local p2    # "audioFormat":I
    .restart local v13    # "audioFormat":I
    :goto_2
    packed-switch v13, :pswitch_data_0

    .line 115
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    const-string v15, "Requested format "

    invoke-virtual {v12, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v15, ", getting ENCODING_PCM_16BIT"

    invoke-virtual {v12, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-static {v11, v12}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 116
    const/4 v13, 0x2

    .line 117
    const/4 v12, 0x2

    .local v12, "sampleSize":I
    goto :goto_3

    .line 112
    .end local v12    # "sampleSize":I
    :pswitch_0
    const/4 v12, 0x4

    .line 113
    .restart local v12    # "sampleSize":I
    goto :goto_3

    .line 106
    .end local v12    # "sampleSize":I
    :pswitch_1
    const/4 v12, 0x1

    .line 107
    .restart local v12    # "sampleSize":I
    goto :goto_3

    .line 109
    .end local v12    # "sampleSize":I
    :pswitch_2
    const/4 v12, 0x2

    .line 110
    .restart local v12    # "sampleSize":I
    nop

    .line 121
    :goto_3
    const-string v15, " channels, getting stereo"

    const-string v14, "Requested "

    if-eqz p0, :cond_6

    .line 122
    packed-switch v1, :pswitch_data_1

    .line 130
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v11, v3}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 131
    const/4 v1, 0x2

    .line 132
    const/16 v3, 0xc

    .line 133
    .local v3, "channelConfig":I
    goto :goto_4

    .line 127
    .end local v3    # "channelConfig":I
    :pswitch_3
    const/16 v3, 0xc

    .line 128
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 124
    .end local v3    # "channelConfig":I
    :pswitch_4
    const/16 v3, 0x10

    .line 125
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 136
    .end local v3    # "channelConfig":I
    :cond_6
    packed-switch v1, :pswitch_data_2

    .line 168
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v11, v3}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 169
    const/4 v1, 0x2

    .line 170
    const/16 v3, 0xc

    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 159
    .end local v3    # "channelConfig":I
    :pswitch_5
    sget v15, Landroid/os/Build$VERSION;->SDK_INT:I

    if-lt v15, v3, :cond_7

    .line 160
    const/16 v3, 0x18fc

    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 162
    .end local v3    # "channelConfig":I
    :cond_7
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v14, " channels, getting 5.1 surround"

    invoke-virtual {v3, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-static {v11, v3}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 163
    const/4 v1, 0x6

    .line 164
    const/16 v3, 0xfc

    .line 166
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 156
    .end local v3    # "channelConfig":I
    :pswitch_6
    const/16 v3, 0x4fc

    .line 157
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 153
    .end local v3    # "channelConfig":I
    :pswitch_7
    const/16 v3, 0xfc

    .line 154
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 150
    .end local v3    # "channelConfig":I
    :pswitch_8
    const/16 v3, 0xdc

    .line 151
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 147
    .end local v3    # "channelConfig":I
    :pswitch_9
    const/16 v3, 0xcc

    .line 148
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 144
    .end local v3    # "channelConfig":I
    :pswitch_a
    const/16 v3, 0x1c

    .line 145
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 141
    .end local v3    # "channelConfig":I
    :pswitch_b
    const/16 v3, 0xc

    .line 142
    .restart local v3    # "channelConfig":I
    goto :goto_4

    .line 138
    .end local v3    # "channelConfig":I
    :pswitch_c
    const/4 v3, 0x4

    .line 139
    .restart local v3    # "channelConfig":I
    nop

    .line 212
    :goto_4
    mul-int v21, v12, v1

    .line 218
    .local v21, "frameSize":I
    if-eqz p0, :cond_8

    .line 219
    invoke-static {v0, v3, v13}, Landroid/media/AudioRecord;->getMinBufferSize(III)I

    move-result v14

    move/from16 v22, v14

    .local v14, "minBufferSize":I
    goto :goto_5

    .line 221
    .end local v14    # "minBufferSize":I
    :cond_8
    invoke-static {v0, v3, v13}, Landroid/media/AudioTrack;->getMinBufferSize(III)I

    move-result v14

    move/from16 v22, v14

    .line 223
    .local v22, "minBufferSize":I
    :goto_5
    add-int v14, v22, v21

    const/4 v15, 0x1

    sub-int/2addr v14, v15

    div-int v14, v14, v21

    invoke-static {v2, v14}, Ljava/lang/Math;->max(II)I

    move-result v2

    .line 225
    .end local p4    # "desiredFrames":I
    .local v2, "desiredFrames":I
    const/4 v14, 0x4

    new-array v14, v14, [I

    .line 227
    .local v14, "results":[I
    const/16 v15, 0x18

    const/16 v23, 0x0

    const/16 v24, 0x0

    if-eqz p0, :cond_c

    .line 228
    sget-object v16, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    if-nez v16, :cond_b

    .line 229
    new-instance v20, Landroid/media/AudioRecord;

    const/16 v16, 0x0

    mul-int v19, v2, v21

    move-object/from16 v25, v14

    .end local v14    # "results":[I
    .local v25, "results":[I
    move-object/from16 v14, v20

    move/from16 p2, v1

    const/4 v1, 0x1

    .end local v1    # "desiredChannels":I
    .local p2, "desiredChannels":I
    move/from16 v15, v16

    move/from16 v16, v0

    move/from16 v17, v3

    move/from16 v18, v13

    invoke-direct/range {v14 .. v19}, Landroid/media/AudioRecord;-><init>(IIIII)V

    sput-object v20, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    .line 233
    invoke-virtual/range {v20 .. v20}, Landroid/media/AudioRecord;->getState()I

    move-result v14

    if-eq v14, v1, :cond_9

    .line 234
    const-string v1, "Failed during initialization of AudioRecord"

    invoke-static {v11, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 235
    sget-object v1, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    invoke-virtual {v1}, Landroid/media/AudioRecord;->release()V

    .line 236
    sput-object v24, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    .line 237
    return-object v24

    .line 240
    :cond_9
    sget v14, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v15, 0x18

    if-lt v14, v15, :cond_a

    if-eqz p5, :cond_a

    .line 241
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    invoke-static/range {p5 .. p5}, Lorg/libsdl/app/SDLAudioManager;->getOutputAudioDeviceInfo(I)Landroid/media/AudioDeviceInfo;

    move-result-object v15

    invoke-virtual {v14, v15}, Landroid/media/AudioRecord;->setPreferredDevice(Landroid/media/AudioDeviceInfo;)Z

    .line 244
    :cond_a
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    invoke-virtual {v14}, Landroid/media/AudioRecord;->startRecording()V

    goto :goto_6

    .line 228
    .end local v25    # "results":[I
    .end local p2    # "desiredChannels":I
    .restart local v1    # "desiredChannels":I
    .restart local v14    # "results":[I
    :cond_b
    move/from16 p2, v1

    move-object/from16 v25, v14

    const/4 v1, 0x1

    .line 247
    .end local v1    # "desiredChannels":I
    .end local v14    # "results":[I
    .restart local v25    # "results":[I
    .restart local p2    # "desiredChannels":I
    :goto_6
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    invoke-virtual {v14}, Landroid/media/AudioRecord;->getSampleRate()I

    move-result v14

    aput v14, v25, v23

    .line 248
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    invoke-virtual {v14}, Landroid/media/AudioRecord;->getAudioFormat()I

    move-result v14

    aput v14, v25, v1

    .line 249
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioRecord:Landroid/media/AudioRecord;

    invoke-virtual {v14}, Landroid/media/AudioRecord;->getChannelCount()I

    move-result v14

    const/4 v15, 0x2

    aput v14, v25, v15

    goto :goto_7

    .line 252
    .end local v25    # "results":[I
    .end local p2    # "desiredChannels":I
    .restart local v1    # "desiredChannels":I
    .restart local v14    # "results":[I
    :cond_c
    move/from16 p2, v1

    move-object/from16 v25, v14

    const/4 v1, 0x1

    .end local v1    # "desiredChannels":I
    .end local v14    # "results":[I
    .restart local v25    # "results":[I
    .restart local p2    # "desiredChannels":I
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    if-nez v14, :cond_f

    .line 253
    new-instance v26, Landroid/media/AudioTrack;

    const/4 v15, 0x3

    mul-int v19, v2, v21

    const/16 v20, 0x1

    move-object/from16 v14, v26

    move/from16 v16, v0

    move/from16 v17, v3

    move/from16 v18, v13

    invoke-direct/range {v14 .. v20}, Landroid/media/AudioTrack;-><init>(IIIIII)V

    sput-object v26, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    .line 258
    invoke-virtual/range {v26 .. v26}, Landroid/media/AudioTrack;->getState()I

    move-result v14

    if-eq v14, v1, :cond_d

    .line 261
    const-string v1, "Failed during initialization of Audio Track"

    invoke-static {v11, v1}, Landroid/util/Log;->e(Ljava/lang/String;Ljava/lang/String;)I

    .line 262
    sget-object v1, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    invoke-virtual {v1}, Landroid/media/AudioTrack;->release()V

    .line 263
    sput-object v24, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    .line 264
    return-object v24

    .line 267
    :cond_d
    sget v14, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v15, 0x18

    if-lt v14, v15, :cond_e

    if-eqz p5, :cond_e

    .line 268
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    invoke-static/range {p5 .. p5}, Lorg/libsdl/app/SDLAudioManager;->getInputAudioDeviceInfo(I)Landroid/media/AudioDeviceInfo;

    move-result-object v15

    invoke-virtual {v14, v15}, Landroid/media/AudioTrack;->setPreferredDevice(Landroid/media/AudioDeviceInfo;)Z

    .line 271
    :cond_e
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    invoke-virtual {v14}, Landroid/media/AudioTrack;->play()V

    .line 274
    :cond_f
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    invoke-virtual {v14}, Landroid/media/AudioTrack;->getSampleRate()I

    move-result v14

    aput v14, v25, v23

    .line 275
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    invoke-virtual {v14}, Landroid/media/AudioTrack;->getAudioFormat()I

    move-result v14

    aput v14, v25, v1

    .line 276
    sget-object v14, Lorg/libsdl/app/SDLAudioManager;->mAudioTrack:Landroid/media/AudioTrack;

    invoke-virtual {v14}, Landroid/media/AudioTrack;->getChannelCount()I

    move-result v14

    const/4 v15, 0x2

    aput v14, v25, v15

    .line 278
    :goto_7
    const/4 v14, 0x3

    aput v2, v25, v14

    .line 280
    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v15, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    if-eqz p0, :cond_10

    goto :goto_8

    :cond_10
    move-object v5, v6

    :goto_8
    invoke-virtual {v15, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v4, ", got "

    invoke-virtual {v15, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    aget v4, v25, v14

    invoke-virtual {v15, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v15, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const/4 v4, 0x2

    aget v4, v25, v4

    invoke-virtual {v15, v4}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v15, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    aget v1, v25, v1

    invoke-static {v1}, Lorg/libsdl/app/SDLAudioManager;->getAudioFormatString(I)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v15, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v15, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    aget v1, v25, v23

    invoke-virtual {v15, v1}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v15, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v15}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v11, v1}, Landroid/util/Log;->v(Ljava/lang/String;Ljava/lang/String;)I

    .line 282
    return-object v25

    :pswitch_data_0
    .packed-switch 0x2
        :pswitch_2
        :pswitch_1
        :pswitch_0
    .end packed-switch

    :pswitch_data_1
    .packed-switch 0x1
        :pswitch_4
        :pswitch_3
    .end packed-switch

    :pswitch_data_2
    .packed-switch 0x1
        :pswitch_c
        :pswitch_b
        :pswitch_a
        :pswitch_9
        :pswitch_8
        :pswitch_7
        :pswitch_6
        :pswitch_5
    .end packed-switch
.end method

.method private static registerAudioDeviceCallback()V
    .locals 3

    .line 310
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-lt v0, v1, :cond_0

    .line 311
    sget-object v0, Lorg/libsdl/app/SDLAudioManager;->mContext:Landroid/content/Context;

    const-string v1, "audio"

    invoke-virtual {v0, v1}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 312
    .local v0, "audioManager":Landroid/media/AudioManager;
    sget-object v1, Lorg/libsdl/app/SDLAudioManager;->mAudioDeviceCallback:Landroid/media/AudioDeviceCallback;

    const/4 v2, 0x0

    invoke-virtual {v0, v1, v2}, Landroid/media/AudioManager;->registerAudioDeviceCallback(Landroid/media/AudioDeviceCallback;Landroid/os/Handler;)V

    .line 314
    .end local v0    # "audioManager":Landroid/media/AudioManager;
    :cond_0
    return-void
.end method

.method public static release(Landroid/content/Context;)V
    .locals 0
    .param p0, "context"    # Landroid/content/Context;

    .line 56
    invoke-static {p0}, Lorg/libsdl/app/SDLAudioManager;->unregisterAudioDeviceCallback(Landroid/content/Context;)V

    .line 57
    return-void
.end method

.method public static native removeAudioDevice(ZI)V
.end method

.method public static setContext(Landroid/content/Context;)V
    .locals 0
    .param p0, "context"    # Landroid/content/Context;

    .line 49
    sput-object p0, Lorg/libsdl/app/SDLAudioManager;->mContext:Landroid/content/Context;

    .line 50
    if-eqz p0, :cond_0

    .line 51
    invoke-static {}, Lorg/libsdl/app/SDLAudioManager;->registerAudioDeviceCallback()V

    .line 53
    :cond_0
    return-void
.end method

.method private static unregisterAudioDeviceCallback(Landroid/content/Context;)V
    .locals 2
    .param p0, "context"    # Landroid/content/Context;

    .line 317
    sget v0, Landroid/os/Build$VERSION;->SDK_INT:I

    const/16 v1, 0x18

    if-lt v0, v1, :cond_0

    .line 318
    const-string v0, "audio"

    invoke-virtual {p0, v0}, Landroid/content/Context;->getSystemService(Ljava/lang/String;)Ljava/lang/Object;

    move-result-object v0

    check-cast v0, Landroid/media/AudioManager;

    .line 319
    .local v0, "audioManager":Landroid/media/AudioManager;
    sget-object v1, Lorg/libsdl/app/SDLAudioManager;->mAudioDeviceCallback:Landroid/media/AudioDeviceCallback;

    invoke-virtual {v0, v1}, Landroid/media/AudioManager;->unregisterAudioDeviceCallback(Landroid/media/AudioDeviceCallback;)V

    .line 321
    .end local v0    # "audioManager":Landroid/media/AudioManager;
    :cond_0
    return-void
.end method
