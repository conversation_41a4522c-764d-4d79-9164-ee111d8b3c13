

local 附魔宝珠= 窗口层:创建窗口("附魔宝珠", 0, 0, 320, 530)
local 控件配置={"变身术","追加法术","变化咒","附加状态"}
local 说明配置={"消耗经验","消耗金钱","消耗石头","祈福值"}
function 附魔宝珠:初始化()


 self:创建纹理精灵(function()
          __res:取资源动画("dlzy", 0x1A23FA1A,"图像"):显示(0,0)
          
          local xx =0
          local yy =0
          for i, v in ipairs(控件配置) do
            文本字体:置颜色(0,0,0,255):取图像(v):显示(60+xx*130,293+yy*35)
              xx=xx+1
              if xx>=2 then
                  yy=yy+1
                  xx =0
              end
          end

          for i, v in ipairs(说明配置) do
              文本字体:置颜色(255,255,255,255):取图像(v):显示(125,365+(i-1)*32)
          end
  
  
  end)




    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true

end



  


function 附魔宝珠:打开(数据)
      self:置可见(not self.是否可见)
      if not self.是否可见 then
          return
      end
      self.宝珠数据 = 数据.宝珠数据
      self:刷新(数据)
end

function 附魔宝珠:刷新(数据)
  self.图像=nil
  self.选择套装=0
  self.道具列表 = 数据.道具.道具
  self.道具网格:置物品(self.道具列表)
  self.材料网格:置物品()
  for i, v in ipairs(控件配置) do
      self[v]:置选中(false)
  end
  self:显示刷新()
end







function 附魔宝珠:显示(x, y)
    if self.图像 then
        self.图像:显示(x+195, y+365)
    end
end

local 石头名称={"青龙石","朱雀石","玄武石","白虎石"}
 function 附魔宝珠:显示刷新()
        self.图像 = self:创建纹理精灵(function()
              if self.材料网格.子控件[1]._spr and self.材料网格.子控件[1]._spr.物品 then
                  local 分类=self.材料网格.子控件[1]._spr.物品.分类
                  local 级别限制 = self.材料网格.子控件[1]._spr.物品.级别限制
                  local 消耗材料=3
                  if 分类~=5 and 分类~=6 then
                      消耗材料=分类
                  end
                  文本字体:置颜色(0,0,0,255):取图像(级别限制*3000):显示(0, 0)
                  文本字体:置颜色(__取银子颜色(级别限制*5000)):取图像(级别限制*5000):显示(0, 32)
                  local 消耗数量 = math.floor(级别限制/10)
                  文本字体:置颜色(0,0,0,255):取图像(消耗数量.."个"):显示(0, 64)
                  local zts =文本字体:置颜色(0,0,0,255):取图像(石头名称[消耗材料])
                  if 石头名称[消耗材料]=="青龙石" then
                    zts=文本字体:置颜色(__取颜色("蓝色")):取图像(石头名称[消耗材料])
                  elseif 石头名称[消耗材料]=="朱雀石" then
                    zts=文本字体:置颜色(__取颜色("红色")):取图像(石头名称[消耗材料])
                  elseif 石头名称[消耗材料]=="玄武石" then
                    zts=文本字体:置颜色(__取颜色("黄色")):取图像(石头名称[消耗材料])
                  elseif 石头名称[消耗材料]=="白虎石" then
                    zts=文本字体:置颜色(255,255,255,255):取投影图像(石头名称[消耗材料])
                  end
                  zts:显示(文本字体:取宽度(消耗数量.."个"), 64)
                  if self.材料网格.子控件[1]._spr.物品.祈福值~=nil then
                    文本字体:置颜色(0,0,0,255):取图像(self.材料网格.子控件[1]._spr.物品.祈福值.."/30"):显示(0, 96)
                  else
                    文本字体:置颜色(0,0,0,255):取图像("0/30"):显示(0, 96)
                  end 
              end
        end,1,100,120
      )



end












local 道具网格 = 附魔宝珠:创建网格("道具网格", 23, 36, 280, 225)
function 道具网格:初始化()
    self:创建格子(53, 53,3, 3, 4, 5)
end

function 道具网格:获得鼠标(x, y, a)
        if self.子控件[a]._spr and self.子控件[a]._spr.物品   then
            __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
        end
end
function 道具网格:添加物品(a)
          if self.子控件[a]._spr.物品.鉴定 and not self.子控件[a]._spr.物品禁止 and self.子控件[a]._spr.物品.分类 <=6 then
                if 附魔宝珠.材料网格.子控件[1]._spr and 附魔宝珠.材料网格.子控件[1]._spr.物品 then
                  附魔宝珠.道具列表[附魔宝珠.材料网格.子控件[1].原始编号]=附魔宝珠.材料网格.子控件[1]._spr.物品
                end
                附魔宝珠.材料网格:置物品(self.子控件[a]._spr.物品)
                附魔宝珠.材料网格.子控件[1].原始编号 = a
                附魔宝珠.道具列表[a]=nil
                self:置物品(附魔宝珠.道具列表)
                附魔宝珠:显示刷新()
          end
end

function 道具网格:左键弹起(x, y, a)
   if self.子控件[a]._spr and self.子控件[a]._spr.物品   then
        if __手机 then
             __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,道具网格,"选择",a)
        else
            self:选择(a) 
        end
    end
end

function 道具网格:选择(编号)
      if 编号 and 编号~=0 then
            self:添加物品(编号) 
      end
end




function 道具网格:置物品(数据)
  for i = 1, #self.子控件 do
      if 数据[i] then
          local lssj = __物品格子:创建()
          lssj:置物品(数据[i], 53,53,"数量")
          lssj:置禁止({2})
          self.子控件[i]:置精灵(lssj)   
      else
          self.子控件[i]:置精灵()
      end
    end
end


local 材料网格 = 附魔宝珠:创建网格("材料网格", 30, 396, 53, 53)
function 材料网格:初始化()
  self:创建格子(53,53,0,0,1,1)
end
function 材料网格:左键弹起(x, y, a)
    if self.子控件[a]._spr and self.子控件[a]._spr.物品 and self.子控件[a].原始编号 then
        附魔宝珠.道具列表[self.子控件[a].原始编号]=self.子控件[a]._spr.物品
        附魔宝珠.道具网格:置物品(附魔宝珠.道具列表)
        self:置物品(nil)
        附魔宝珠:显示刷新()
    end
end

function 材料网格:获得鼠标(x, y, a)
    if self.子控件[a]._spr and self.子控件[a]._spr.物品   then
        __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
    end
end
function 材料网格:置物品(数据)
      local lssj = __物品格子:创建()
      lssj:置物品(数据,53,53,nil,true)
      self.子控件[1]:置精灵(lssj)
end

local xx =0
local yy =0
for i, v in ipairs(控件配置) do
      local 临时函数=附魔宝珠:创建单选按钮(v,30+xx*130,290+yy*35)  
      function 临时函数:初始化()
          self:创建圆形选中精灵()
      end
      xx=xx+1
      if xx>=2 then
          yy=yy+1
          xx =0
      end
      function  临时函数:左键弹起(x, y)
          if v=="变化咒" then
              __UI弹出.提示框:打开('#Y暂未开放变化咒套装。')
              附魔宝珠.选择套装=0
          else
              附魔宝珠.选择套装=i
              附魔宝珠:显示刷新()
          end
      end
end



local 附魔 = 附魔宝珠:创建红色按钮("点化","附魔", 125,490,80,22)
function 附魔:左键弹起(x, y)
          if not 附魔宝珠.宝珠数据 or 附魔宝珠.宝珠数据==0 then
               __UI弹出.提示框:打开('#Y宝珠数据异常，请重新打开界面操作。')
          elseif not 附魔宝珠.选择套装 or 附魔宝珠.选择套装==0 then
                __UI弹出.提示框:打开('#Y您未选择点化的套装类型。')
          elseif 附魔宝珠.材料网格.子控件[1]._spr and 附魔宝珠.材料网格.子控件[1]._spr.物品 and 附魔宝珠.材料网格.子控件[1].原始编号 then
                  请求服务(3768,{装备=附魔宝珠.材料网格.子控件[1].原始编号,套装=附魔宝珠.选择套装,宝珠数据 = 附魔宝珠.宝珠数据})
          else
              __UI弹出.提示框:打开('#Y请先选择需要点化套装的装备')
          end
end

local 关闭 = 附魔宝珠:创建按钮("关闭",295,2)
function 关闭:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/jmtb", 0x81DD40D3))
end
function 关闭:左键弹起(x, y)
    附魔宝珠:置可见(false)
end


