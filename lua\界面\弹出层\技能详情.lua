--[[
LastEditTime: 2024-09-25 09:54:51
--]]

if __手机 then
  __UI弹出.技能详情 = 界面:创建弹出窗口("技能详情", 0, 0, 0, 0)
else
  __UI弹出.技能详情 = 界面:创建提示控件("技能详情", 0, 0)
end


local 技能详情 = __UI弹出.技能详情 
function 技能详情:初始化()
end
function 技能详情:左键弹起()
  self:置可见(false)
end

local 按钮设置={"使用","拿起"}
function 技能详情:打开(数据,x,y,回调,类型,编号)
  self:置可见(true)
  self.数据 = 数据
  self.图片=nil
  self.名称=nil 
  self.类型=nil
  self.回调=nil
  self.编号=nil
  self.宽度 = 300
  self.高度 = 90
  self.介绍文本:置文字(文本字体):清空()
  local _,h=0,0
  if self.数据 and self.数据.名称 then
      self.名称=self.数据.名称
      if self.数据.资源 and self.数据.大动画 then
          self.图片= __res:取资源动画(self.数据.资源, self.数据.大动画,"图像"):拉伸(60,60)
          _,h=self.介绍文本:置文本("")
          _,h=self.介绍文本:置文本(self.数据.介绍)
          if self.数据.备注 then
            _,h=self.介绍文本:置文本(self.数据.备注)
          end
          if self.数据.消耗 then
            _,h=self.介绍文本:置文本(self.数据.消耗)
          end
          if self.数据.条件 then
            _,h=self.介绍文本:置文本(self.数据.条件)
          end
          if self.数据.冷却 then
            _,h=self.介绍文本:置文本(self.数据.冷却)
          end
      else
            local lxxs = 取技能(self.名称)
            self.图片= __res:取资源动画(lxxs[6],lxxs[7],"图像"):拉伸(60,60)
            _,h=self.介绍文本:置文本("")
            _,h=self.介绍文本:置文本(lxxs[1])
            if lxxs[4] then
              _,h=self.介绍文本:置文本("#G消耗："..lxxs[4])
            end
            if lxxs[5] then
              _,h=self.介绍文本:置文本("#G条件："..lxxs[5])
            end
            if lxxs[12] then
              _,h=self.介绍文本:置文本("#G冷却："..lxxs[12])
            end
      end
      self.介绍文本:置高度(h)
  end
  if self.高度<h+50 then
      self.高度= h+50 
  end
  for i, v in ipairs(按钮设置) do
      self[v]:置可见(false)
  end
  self.设置默认:置可见(false)
  if __手机 then
      self:按钮配置(回调,类型,编号)
  end
  if x+self.宽度 >引擎.宽度 then
      x = 引擎.宽度 - self.宽度- 5
  elseif x<0 then
      x = 0
  end
  if y+self.高度 >引擎.高度 then
      y = 引擎.高度 - self.高度- 5
  elseif y<0 then
      y = 0
  end
  self:提示显示(x,y,self.宽度, self.高度)
end





function 技能详情:提示显示(x,y,w,h)
  local nsf = 取九宫图像(__res:取资源动画("dlzy", 0xB5FDF1AC,"图像"),w,h,20,true)
  --local nsf =  取九宫图像(__res:getPNGCC(2, 230, 964, 401, 52),w,h,10,true)
  if nsf:渲染开始() then
      if self.图片 then
          self.图片:显示(20,20)
      end
      if self.名称 then
          道具字体:置颜色(252,252,8)
          道具字体:取图像(self.名称,252, 252, 8,180):显示(90,20)
      end
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
  self:置坐标(x, y)
  self:置宽高(w,h)

end


local 介绍文本 = 技能详情:创建文本("介绍文本", 90, 40, 200, 0)
function 介绍文本:初始化()
end


for i, v in ipairs(按钮设置) do
    local 临时按钮 = 技能详情:创建蓝色按钮(v,v, 0, 0,100,35,标题字体)
    function 临时按钮:左键弹起(x,y)
              if 技能详情.回调 and 技能详情.回调[v] and 技能详情.编号 and 技能详情.编号~=0 then
                技能详情.回调[v](技能详情.回调,技能详情.编号)
              end
              技能详情:置可见(false)
    end        
end

local 设置默认 = 技能详情:创建蓝色按钮("设置默认","设置默认", 0, 0,140,35,标题字体)
function 设置默认:左键弹起(x,y)
          if 技能详情.回调 and 技能详情.回调.设置默认 and 技能详情.编号 and 技能详情.编号~=0 then
            技能详情.回调:设置默认(技能详情.编号)
          end
          技能详情:置可见(false)
end  


function 技能详情:按钮配置(回调,类型,编号)
  if self.数据 and 类型 then
        self.回调 = 回调
        self.类型 = 类型
        self.编号 = 编号
        if self.类型=="拿起" then
            self.拿起:置可见(true)
            self.拿起:置坐标(190,self.高度)
        elseif self.类型=="使用" then
              self.使用:置可见(true)
              self.使用:置坐标(190,self.高度)
        elseif self.类型=="战斗法术" then
                self.使用:置可见(true)
                self.设置默认:置可见(true)
                self.使用:置坐标(190,self.高度)
                self.设置默认:置坐标(40,self.高度)


        elseif self.类型=="全部" then
              self.拿起:置可见(true)
              self.使用:置可见(true)
              self.拿起:置坐标(80,self.高度)
              self.使用:置坐标(190,self.高度)
        end
        self.高度 = self.高度 + 45
  end

end

