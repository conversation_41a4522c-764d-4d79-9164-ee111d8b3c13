local 符石合成 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "符石合成", 80+87 + abbr.py.x, 50 + abbr.py.y, 361+45+290-51, 492-102)

function 符石合成:初始化()
  local nsf = require("SDL.图像")(361+4+290-51, 492-102)
  if nsf["渲染开始"](nsf) then
    置窗口背景("符石合成", 0, 12, 355+9+290-71,480-102, true):显示(0, 0)
    __res:getPNGCC(3, 694, 4, 336, 273):显示(13, 44+3)
    字体18:置颜色(139,33,31) --
    
    local gez =__res:getPNGCC(3, 132, 506, 55, 55)
    -- gez:显示(397,144)
    -- gez:显示(397,77)
    -- gez:显示(487,77)
    -- gez:显示(487,144)
    
    local lssj = 取输入背景(0, 0, 120, 23)
    lssj:显示(366+158-71,121-89+51+131)
    lssj:显示(366+158-71,121-89+51+131+27*1)
    lssj:显示(366+158-71,121-89+51+131+27*2)
    lssj:显示(366+158-71,121-89+51+131+27*3)
    字体18:置颜色(255, 255, 255)
    字体18:取图像("- 合成材料 -"):显示(366+71-55+30,49)
    字体18:取图像("所需体力"):显示(366,121-89+51+134)
    字体18:取图像("所需现金"):显示(366,121-89+51+134+27*1)
    字体18:取图像("现有现金"):显示(366,121-89+51+134+27*2)
    字体18:取图像("现有体力"):显示(366,121-89+51+134+27*3)
    字体18:置颜色(__取颜色("黄色"))
    字体18:取图像("请选择要合成的符石："):显示(31,347)
    --取白色背景(0, 0, 440, 300, true)["显示"](取白色背景(0, 0, 440, 300, true), 20, 56)
  end
  self:置精灵(nsf["到精灵"](nsf))
end

function 符石合成:打开()
  self:置可见(true)
  self:刷新()
end
function 符石合成:刷新()
  self.道具网格:指导局(__主控.道具列表)
  self.材料网格:置物品({})
  self.材料位置={}
  self:计算消耗()
end
function 符石合成:计算消耗()
  local nsf = require("SDL.图像")(163, 130)
  local 材料数量=0
  for n=1,4 do
    if 符石合成.材料位置[n]  then
      材料数量=材料数量+1
    end
  end
  if nsf["渲染开始"](nsf) then
    字体18:置颜色(__取颜色("紫色"))
    if 材料数量>=2 then
      字体18:取图像(40):显示(0,0)
      字体18:取图像(0):显示(0,0+27*1)
      字体18:取图像(角色信息.银子):显示(0,0+27*2)
      字体18:取图像(角色信息.体力):显示(0,0+27*3)
    else
      字体18:取图像(0):显示(0,0)
      字体18:取图像(0):显示(0,0+27*1)
      字体18:取图像(角色信息.银子):显示(0,0+27*2)
      字体18:取图像(角色信息.体力):显示(0,0+27*3)
    end
    nsf["渲染结束"](nsf)
  end

  self.图像 = nsf["到精灵"](nsf)
  self.图像:置中心(-446-96+73,-117-100)
end
local 道具网格 = 符石合成["创建网格"](符石合成, "道具网格", 18-5, 47+4, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] and not self.子控件[a]._spr["物品"].物品禁止 then
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170+255, 86, w, h, "选择", a)
    for n=1,4 do
      if 符石合成.材料位置[n] == nil then
        符石合成.材料位置[n] = a
        符石合成.材料网格:置物品(self.子控件[a]._spr["物品"],n)
        self:指导局(data, a)
        break
      end
    end
    符石合成:计算消耗()
  end
end
function 道具网格:指导局(data, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], nil, "战斗道具")
      lssj["置偏移"](lssj, 10, 10)
      lssj["总类禁止111"](lssj, 889)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data, nil, "战斗道具")
    lssj["置偏移"](lssj, 10, 10)
    lssj["总类禁止111"](lssj,  889)
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end


local 材料网格 = 符石合成["创建网格"](符石合成, "材料网格", 481-82, 182-104, 180, 130)
function 材料网格:初始化()
  self:创建格子(67, 67, 0, 23, 2, 2)
end

function 材料网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170-54, 86, w, h, "选择", a)
    符石合成.材料位置[a] = nil
    符石合成["道具网格"]["指导局"](符石合成["道具网格"], self.子控件[a]._spr["物品"], a)
    self:置物品(nil, a)
    符石合成:计算消耗()
  end
end

function 材料网格:置物品(数据, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __商店格子["创建"]()
      lssj["置物品"](lssj, 数据[i], "制造")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, 数据, "制造")
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end

local 合成按钮 = 符石合成:创建我的按钮(__res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(143, 41), "合成按钮", 423, 337, "合成")
function  合成按钮:左键弹起(x, y)
  发送数据(3803,{材料=符石合成.材料位置})
end
local 关闭 = 符石合成["创建我的按钮"](符石合成, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 355+29-53+290-71, 0)
function 关闭:左键弹起(x, y, msg)
  符石合成["置可见"](符石合成, false)
end