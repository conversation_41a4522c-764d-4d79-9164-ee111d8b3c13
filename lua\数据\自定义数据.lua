
取境界 = function(j, w)
  if 1 == w then
    if 0 == j then
      return "#C/略晓变化"
    elseif 1 == j then
      return "#W/略晓变化"
    elseif 2 == j then
      return "#S/驾轻就熟"
    elseif 3 == j then
      return "#S/心领神会"
    elseif 4 == j then
      return "#S/出类拔萃"
    elseif 5 == j then
      return "#S/腾云驾雾"
    elseif 6 == j then
      return "#S/降龙伏虎"
    elseif 7 == j then
      return "#S/神乎其技"
    elseif 8 == j then
      return "#S/纵横三界"
    elseif 9 == j then
      return "#S/不堕轮回"
    end
  elseif 2 == w then
    if 0 == j or 1 == j then
      return "#C/了然于胸"
    elseif 2 == j then
      return "#W/妙领天机"
    elseif 3 == j then
      return "#S/渐入佳境"
    elseif 4 == j then
      return "#S/预知福祸"
    elseif 5 == j then
      return "#S/脱胎换骨"
    elseif 6 == j then
      return "#S/出神入化"
    elseif 7 == j then
      return "#S/呼风唤雨"
    elseif 8 == j then
      return "#S/随心所欲"
    elseif 9 == j then
      return "#S/登峰造极"
    elseif 10 == j then
      return "#S/道满根归"
    elseif 11 == j then
      return "#S/不堕轮回"
    elseif 12 == j then
      return "#S/法力无边"
    end
  elseif 3 == w or 4 == w then
    if 0 == j or 1 == j then
      return "#C/一日千里"
    elseif 2 == j then
      return "#W/脱胎换骨"
    elseif 3 == j then
      return "#S/负海担山"
    elseif 4 == j then
      return "#S/霞举飞升"
    elseif 5 == j then
      return "#S/移星换斗"
    elseif 6 == j then
      return "#S/变幻莫测"
    elseif 7 == j then
      return "#S/擎日挽月"
    elseif 8 == j then
      return "#S/道满根归"
    elseif 9 == j then
      return "#S/不堕轮回"
    elseif 10 == j then
      return "#S/举世无双"
    elseif 11 == j then
      return "#S/纵横三界"
    elseif 12 == j then
      return "#S/笑傲西游"
    elseif 13 == j then
      return "#S/法力无边"
    elseif 14 == j then
      return "#S/反璞归真"
    elseif 15 == j then
      return "#S/天人合一"
    elseif 16 == j then
      return "#S/物我两忘"
    elseif 17 == j then
      return "#S/再厉尘劫"
    elseif 18 == j then
      return "#S/浴火涅磐"
    end
  end
end
数字转大写 = function(a)
  if 1 == a then
    return "一"
  elseif 2 == a then
    return "二"
  elseif 3 == a then
    return "三"
  elseif 4 == a then
    return "四"
  elseif 5 == a then
    return "五"
  elseif 6 == a then
    return "六"
  elseif 7 == a then
    return "七"
  elseif 8 == a then
    return "八"
  elseif 9 == a then
    return "九"
  elseif 10 == a then
    return "十"
  end
end
function 恢复技能(名称)
	local 临时名称={"无穷妙道","地涌金莲","星月之惠","玉清诀","晶清诀","冰清诀","水清诀","四海升平","命归术","气归术","凝神诀","凝气诀","命疗术","心疗术","气疗术","归元咒","乾天罡气","我佛慈悲","杨柳甘露","推拿","推气过宫","解毒","百毒不侵","宁心","解封","清心","驱魔","驱尸","寡欲令","复苏"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 法攻技能(名称)
	local 临时名称={"夺命咒","落叶萧萧","荆棘舞","逍遥游","尘土刃","叱咤风云","天降灵葫","冰川怒","自爆","唧唧歪歪","五雷咒","落雷符","雨落寒沙","五雷轰顶","雷霆万钧","龙卷雨击","龙吟","二龙戏珠","龙腾","苍茫树","靛沧海","日光华","地裂火","巨岩破","三昧真火","飞砂走石","判官令","阎罗令","水攻","烈火","落岩","雷击","泰山压顶","水漫金山","地狱烈火","奔雷咒","超级泰山压顶","超级水漫金山","超级地狱烈火","超级奔雷咒"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 物攻技能(名称)
	local 临时名称={"翻江搅海","惊涛怒","浪涌","天崩地裂","断岳势","裂石","满天花雨","破血狂攻","破碎无双","弱点击破","善恶有报","惊心一剑","壁垒击破","横扫千军","狮搏","象形","连环击","鹰击","烟雨剑法","飘渺式","天雷斩","裂石","断岳势","天崩地裂","浪涌","惊涛怒"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 封印技能(名称)
	local 临时名称={"摧心术","惊魂掌","煞气诀","夺魄令","反间之计","催眠符","失心符","落魄符","失忆符","追魂符","离魂符","失魂符","定身符","莲步轻舞","如花解语","似玉生香","娉婷嬝娜","镇妖","错乱","百万神兵","日月乾坤","威慑","含情脉脉","魔音摄魂","夺魄令","惊魂掌","煞气诀"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 减益技能(名称)
	local 临时名称={"尸腐毒","紧箍咒","勾魂","摄魄","雾杀","偷龙转凤"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end
function 增益技能(名称)
	local 临时名称={"变身","碎星诀","不动如山","明光宝烛","移魂化骨","蜜润","炎护","后发制人","罗汉金钟","杀气诀","安神诀","分身术","达摩护体","金刚护法","金刚护体","韦陀护法","一苇渡江","佛法无边","楚楚可怜","天神护法","乘风破浪","神龙摆尾","生命之泉","炼气化神","天地同寿","乾坤妙法","普渡众生","灵动九天","幽冥鬼眼","修罗隐身","火甲术","魔王回首","定心术","极度疯狂","魔息术","天魔解体","盘丝阵","幻境术","不动如山","碎星诀","镇魂诀","明光宝烛","金身舍利","炎护","蜜润"}
	for n=1,#临时名称 do
		if 临时名称[n]==名称 then return true end
	end
	return false
end



刷新宝宝列表 = function(内容)
    角色信息.宝宝列表 = 内容
    if __UI界面.窗口层.召唤属性.是否可见 then
      __UI界面.窗口层.召唤属性.名称选择:置数据()
      __UI界面.窗口层.召唤属性:显示设置()
    end
    if __res.配置.行囊==1 and __UI界面.窗口层.道具行囊.是否可见 and __UI界面.窗口层.道具行囊.窗口类型 == "召唤兽" then
        __UI界面.窗口层.道具行囊:重置窗口()
    elseif  __UI界面.窗口层.新行囊.是否可见 and __UI界面.窗口层.新行囊.窗口类型 == "召唤兽" then
        __UI界面.窗口层.新行囊:重置窗口()
    end
end

刷新宝宝窗口 = function(选中)
      if __UI界面.窗口层.召唤属性.是否可见 then
            if 选中 then
              __UI界面.窗口层.召唤属性.选中=nil
            end
            __UI界面.窗口层.召唤属性.名称选择:置数据()
            __UI界面.窗口层.召唤属性:显示设置()
      end
      if __res.配置.行囊==1 and __UI界面.窗口层.道具行囊.是否可见 and __UI界面.窗口层.道具行囊.窗口类型 == "召唤兽" then
          __UI界面.窗口层.道具行囊:重置窗口()
      elseif  __UI界面.窗口层.新行囊.是否可见 and __UI界面.窗口层.新行囊.窗口类型 == "召唤兽" then
          __UI界面.窗口层.新行囊:重置窗口()
      end






end


__变身卡 = function(模型)
	if 模型 == "大海龟" then
		return "大海龟","item.wdf",0x4A028BEE,0x3C7B89E8,nil,nil,20,1,3
	elseif 模型 == "巨蛙" then
		return "巨蛙","item.wdf",0x4A028BEE,0x98E3377F,nil,nil,20,1,3
	elseif 模型 == "海星" then
		return "海星","item.wd1",1241680878,3876179373,nil,nil,20,1,3,"item.wdf"
	elseif 模型 == "章鱼" then
		return "章鱼","item.wd1",1241680878,2880866697,nil,nil,20,1,3,"item.wdf"
	elseif 模型 == "浣熊" then
		return "浣熊","item.wd1",1241680878,2785980633,nil,nil,20,1,3,"item.wdf"
	elseif 模型 == "大蝙蝠" then
		return "大蝙蝠","item.wdf",1241680878,0x2481DFCC,nil,nil,20,1,3
	elseif 模型 == "赌徒" then
		return "赌徒","item.wdf",1241680878,0x6BE81A68,nil,nil,20,1,3
	elseif 模型 == "海毛虫" then
		return "海毛虫","item.wdf",1241680878,0x3BD0B554,nil,nil,20,1,3
	elseif 模型 == "护卫" then
		return "护卫","item.wdf",1241680878,0x7003F174,nil,nil,20,1,3
	elseif 模型 == "强盗" then
		return "强盗","item.wdf",1241680878,0xD5C2566E,nil,nil,20,1,3
	elseif 模型 == "山贼" then
		return "山贼","item.wdf",1241680878,0x5F7346A8,nil,nil,20,1,3
	elseif 模型 == "树怪" then
		return "树怪","item.wdf",1241680878,0x4ED5C9C4,nil,nil,20,1,3
	elseif 模型 == "野猪" then
		return "野猪","item.wdf",1241680878,0xEF3A830D,nil,nil,20,1,3
	end
end


function 生活技能消耗(目标技能等级,类型)
  local cc = 0
  local vv = 0
  local 等级 = 目标技能等级
  if 等级 > 0 and 等级 <= 2 then
    cc = 8*(目标技能等级+1)*目标技能等级
  elseif 等级 > 2 and 等级 <= 5 then
    cc = 8*(目标技能等级+1)*目标技能等级+2^目标技能等级
  elseif 等级 > 5 and 等级 <= 11 then
    cc = 目标技能等级*目标技能等级*(目标技能等级+5)
  elseif 等级 > 11 and 等级 <= 18 then
    cc = 目标技能等级*(目标技能等级+1)*10*(1.625+(目标技能等级-12)*0.2)
  elseif 等级 > 18 and 等级 <= 25 then
    cc = 目标技能等级*(目标技能等级+10)*10*(2.37+(目标技能等级-19)*0.25)
  elseif 等级 > 25 and 等级 <= 31 then
    cc = 目标技能等级*(目标技能等级-10)*(94.3+(目标技能等级-26)*5.5)
  elseif 等级 > 25 and 等级 <= 31 then
    cc = 目标技能等级*(目标技能等级-10)*(94.3+(目标技能等级-26)*5.5)
  elseif 等级 > 31 and 等级 <= 40 then
    cc = 目标技能等级*(目标技能等级-10)*(130.22+(目标技能等级-32)*8)
  elseif 等级 > 40 and 等级 <= 50 then
    cc = 目标技能等级*(目标技能等级-10)*(207.4+(目标技能等级-41)*11)
  elseif 等级 > 50 and 等级 <= 60 then
    cc = 目标技能等级*(目标技能等级-10)*(329.41+(目标技能等级-51)*16)
  elseif 等级 > 60 and 等级 <= 70 then
    cc = 目标技能等级*(目标技能等级-10)*(495.2+(目标技能等级-61)*21)
  elseif 等级 > 70 and 等级 <= 80 then
    cc = 目标技能等级*(目标技能等级-10)*(710.93+(目标技能等级-71)*26)
  elseif 等级 > 80 and 等级 <= 90 then
    cc = 目标技能等级*(目标技能等级-10)*(982.9+(目标技能等级-81)*33)
  elseif 等级 > 90 and 等级 <= 100 then
    cc = 目标技能等级*(目标技能等级-10)*(1317.47+(目标技能等级-91)*39)
  elseif 等级 > 100 and 等级 <= 110 then
    cc = 目标技能等级*(目标技能等级-10)*(1720.997+(目标技能等级-101)*47)
  elseif 等级 > 110 and 等级 <= 120 then
    cc = 目标技能等级*(目标技能等级-10)*(2199.87+(目标技能等级-111)*55)
  elseif 等级 > 120 and 等级 <= 130 then
    cc = 目标技能等级*(目标技能等级-10)*(2760.33+(目标技能等级-121)*64)
  elseif 等级 > 130 and 等级 <= 140 then
    cc = 目标技能等级*(目标技能等级-10)*(3409.09+(目标技能等级-131)*73)
  elseif 等级 > 140 and 等级 <= 150 then
    cc = 目标技能等级*(目标技能等级-10)*(4152.37+(目标技能等级-141)*83)
  elseif 等级 > 150 and 等级 <= 155 then
    cc = 目标技能等级*(目标技能等级-10)*(5152.78+(目标技能等级-151)*247)
  elseif 等级 > 155 and 等级 <= 160 then
    cc = 目标技能等级*(目标技能等级-10)*(6557.72+(目标技能等级-156)*417)
  elseif 等级 > 160 and 等级 <= 170 then
    cc = 目标技能等级*(目标技能等级-10)*(8739.79+(目标技能等级-161)*503)
  elseif 等级 > 170 and 等级 <= 174 then
    cc = 目标技能等级*(目标技能等级-10)*(13980.32+(目标技能等级-171)*707)
  elseif 等级 > 174 and 等级 <= 179 then
    cc = 目标技能等级*(目标技能等级-10)*(16804.73+(目标技能等级-175)*918)
  elseif 等级 > 179 then
      local 临时等级 = 目标技能等级 -1
      cc=临时等级*(临时等级-10)*(16804.73+(临时等级-175)*918)*1.2
  end
  if 等级 > 0 and 等级 <= 3 then
    vv = 3*(目标技能等级+1)*目标技能等级
  elseif 等级 > 3 and 等级 <= 9 then
    vv = (目标技能等级+1)*目标技能等级*(3.25+(目标技能等级-4)*0.25)
  elseif 等级 > 9 and 等级 <= 16 then
    vv = (目标技能等级-5)*目标技能等级*(11.12+(目标技能等级-10)*0.4)
  elseif 等级 > 16 and 等级 <= 20 then
    vv = (目标技能等级-10)*目标技能等级*(24.91+(目标技能等级-17)*0.55)
  elseif 等级 > 20 and 等级 <= 30 then
    vv = (目标技能等级-10)*目标技能等级*(27.73+(目标技能等级-21)*1.7)
  elseif 等级 > 30 and 等级 <= 40 then
    vv = (目标技能等级-10)*目标技能等级*(46.24+(目标技能等级-31)*3)--
  elseif 等级 > 40 and 等级 <= 50 then
    vv = (目标技能等级-10)*目标技能等级*(77.75+(目标技能等级-41)*4.4)
  elseif 等级 > 50 and 等级 <= 60 then
    vv = (目标技能等级-10)*目标技能等级*(123.51+(目标技能等级-51)*6)
  elseif 等级 > 60 and 等级 <= 70 then
    vv = (目标技能等级-10)*目标技能等级*(185.69+(目标技能等级-61)*7.8)
  elseif 等级 > 70 and 等级 <= 80 then
    vv = (目标技能等级-10)*目标技能等级*(266.59+(目标技能等级-71)*10)
  elseif 等级 > 80 and 等级 <= 90 then
    vv = (目标技能等级-10)*目标技能等级*(368.58+(目标技能等级-81)*12.3)
  elseif 等级 > 90 and 等级 <= 100 then
    vv = (目标技能等级-10)*目标技能等级*(494.04+(目标技能等级-91)*14.8)
  elseif 等级 > 100 and 等级 <= 110 then
    vv = (目标技能等级-10)*目标技能等级*(645.36+(目标技能等级-101)*17.6)
  elseif 等级 > 110 and 等级 <= 120 then
    vv = (目标技能等级-10)*目标技能等级*(824.94+(目标技能等级-111)*20.65)
  elseif 等级 > 120 and 等级 <= 130 then
    vv = (目标技能等级-10)*目标技能等级*(1035.17+(目标技能等级-121)*24)
  elseif 等级 > 130 and 等级 <= 140 then
    vv = (目标技能等级-10)*目标技能等级*(1278.45+(目标技能等级-131)*27.5)
  elseif 等级 > 140 and 等级 <= 150 then
    vv = (目标技能等级-10)*目标技能等级*(1557.17+(目标技能等级-141)*31.3)
  elseif 等级 > 150 and 等级 <= 155 then
    vv = (目标技能等级-10)*目标技能等级*(1932.32+(目标技能等级-151)*92.6)
  elseif 等级 > 155 and 等级 <= 160 then
    vv = (目标技能等级-10)*目标技能等级*(2459.17+(目标技能等级-156)*156.3)
  elseif 等级 > 160 and 等级 <= 170 then
    vv = (目标技能等级-10)*目标技能等级*(3231.07+(目标技能等级-161)*144)
  elseif 等级 > 170 and 等级 <= 175 then
    vv = (目标技能等级-10)*目标技能等级*(4731.98+(目标技能等级-171)*205.3)
  elseif 等级 > 175 and 等级 <= 180 then
    vv = (目标技能等级-10)*目标技能等级*(5825.51+(目标技能等级-176)*283)
  elseif 等级 > 180 then
      local 临时等级 = 目标技能等级 -1
      vv = (临时等级-10)*临时等级*(5825.51+(临时等级-176)*283)*1.2
  end
   if  类型==1 then
    return {经验=math.floor(cc),金钱=math.floor(vv)}
  else
    return {经验=math.floor(cc*5),金钱=math.floor(vv*5)}--{经验=floor(技能消耗.经验[等级]*5),金钱=floor(技能消耗.金钱[等级]*3)}
  end

end


技能消耗={}
技能消耗.经验={
  [1]=16,
  [2]=32,
  [3]=52,
  [4]=75,
  [5]=103,
  [6]=136,
  [7]=179,
  [8]=231,
  [9]=295,
  [10]=372,
  [11]=466,
  [12]=578,
  [13]=711,
  [14]=867,
  [15]=1049,
  [16]=1280,
  [17]=1503,
  [18]=1780,
  [19]=2096,
  [20]=2452,
  [21]=2854,
  [22]=3304,
  [23]=3807,
  [24]=4364,
  [25]=4983,
  [26]=5664,
  [27]=6415,
  [28]=7238,
  [29]=8138,
  [30]=9120,
  [31]=10188,
  [32]=11347,
  [33]=12602,
  [34]=13959,
  [35]=15423,
  [36]=16998,
  [37]=18692,
  [38]=20508,
  [39]=22452,
  [40]=24532,
  [41]=26753,
  [42]=29121,
  [43]=31642,
  [44]=34323,
  [45]=37169,
  [46]=40186,
  [47]=43388,
  [48]=46773,
  [49]=50352,
  [50]=54132,
  [51]=58120,
  [52]=62324,
  [53]=66750,
  [54]=71407,
  [55]=76303,
  [56]=81444,
  [57]=86840,
  [58]=92500,
  [59]=104640,
  [60]=111136,
  [61]=117931,
  [62]=125031,
  [63]=132444,
  [64]=140183,
  [65]=148253,
  [66]=156666,
  [67]=156666,
  [68]=165430,
  [69]=174556,
  [70]=184052,
  [71]=193930,
  [72]=204198,
  [73]=214868,
  [74]=225948,
  [75]=237449,
  [76]=249383,
  [77]=261760,
  [78]=274589,
  [79]=287884,
  [80]=301652,
  [81]=315908,
  [82]=330662,
  [83]=345924,
  [84]=361708,
  [85]=378023,
  [86]=394882,
  [87]=412297,
  [88]=430280,
  [89]=448844,
  [90]=468000,
  [91]=487760,
  [92]=508137,
  [93]=529145,
  [94]=550796,
  [95]=573103,
  [96]=596078,
  [97]=619735,
  [98]=644088,
  [99]=669149,
  [100]=721452,
  [101]=748722,
  [102]=776755,
  [103]=805566,
  [104]=835169,
  [105]=865579,
  [106]=896809,
  [107]=928876,
  [108]=961792,
  [109]=995572,
  [110]=1030234,
  [111]=1065190,
  [112]=1102256,
  [113]=1139649,
  [114]=1177983,
  [115]=1217273,
  [116]=1256104,
  [117]=1298787,
  [118]=1341043,
  [119]=1384320,
  [120]=1428632,
  [121]=1473999,
  [122]=1520435,
  [123]=1567957,
  [124]=1616583,
  [125]=1666328,
  [126]=1717211,
  [127]=1769248,
  [128]=1822456,
  [129]=1876852,
  [130]=1932456,
  [131]=1989284,
  [132]=2047353,
  [133]=2106682,
  [134]=2167289,
  [135]=2229192,
  [136]=2292410,
  [137]=2356960,
  [138]=2422861,
  [139]=2490132,
  [140]=2558792,
  [141]=2628860,
  [142]=2700356,
  [143]=2773296,
  [144]=2847703,
  [145]=2923593,
  [146]=3000989,
  [147]=3079908,
  [148]=3160372,
  [149]=3242400,
  [150]=6652022,
  [151]=6822452,
  [152]=6996132,
  [153]=7173104,
  [154]=7353406,
  [155]=11305620,
  [156]=15305620,
  [157]=22305620,
  [158]=27305620,
  [159]=37305620,
  [160]=45305620,
  [161]=54305620,
  [162]=57305620,
  [163]=60305620,
  [164]=65305620,
  [165]=70305620,
  [166]=84305620,
}

for n=167,250 do
  技能消耗.经验[n]=math.floor(技能消耗.经验[n-1]*1.2)
end

技能消耗.金钱={
  [1]=6,
  [2]=12,
  [3]=19,
  [4]=28,
  [5]=38,
  [6]=51,
  [7]=67,
  [8]=86,
  [9]=110,
  [10]=139,
  [11]=174,
  [12]=216,
  [13]=266,
  [14]=325,
  [15]=393,
  [16]=472,
  [17]=563,
  [18]=667,
  [19]=786,
  [20]=919,
  [21]=1070,
  [22]=1238,
  [23]=1426,
  [24]=1636,
  [25]=1868,
  [26]=2124,
  [27]=2404,
  [28]=2714,
  [29]=3050,
  [30]=3420,
  [31]=3820,
  [32]=4255,
  [33]=4725,
  [34]=5234,
  [35]=5783,
  [36]=6374,
  [37]=7009,
  [38]=7680,
  [39]=8419,
  [40]=9199,
  [41]=10032,
  [42]=10920,
  [43]=11865,
  [44]=12871,
  [45]=13938,
  [46]=15070,
  [47]=16270,
  [48]=17540,
  [49]=18882,
  [50]=20299,
  [51]=21795,
  [52]=23371,
  [53]=25031,
  [54]=26777,
  [55]=28613,
  [56]=30541,
  [57]=32565,
  [58]=34687,
  [59]=36911,
  [60]=39240,
  [61]=41676,
  [62]=44224,
  [63]=46886,
  [64]=49666,
  [65]=52568,
  [66]=55595,
  [67]=58749,
  [68]=62036,
  [69]=65458,
  [70]=69019,
  [71]=72723,
  [72]=76574,
  [73]=80575,
  [74]=84730,
  [75]=89043,
  [76]=93516,
  [77]=98160,
  [78]=102971,
  [79]=107956,
  [80]=113119,
  [81]=118465,
  [82]=123998,
  [83]=129721,
  [84]=135640,
  [85]=141758,
  [86]=148080,
  [87]=154611,
  [88]=161355,
  [89]=168316,
  [90]=175500,
  [91]=182910,
  [92]=190551,
  [93]=198429,
  [94]=206548,
  [95]=214913,
  [96]=223529,
  [97]=232400,
  [98]=241533,
  [99]=250931,
  [100]=260599,
  [101]=270544,
  [102]=280770,
  [103]=291283,
  [104]=302087,
  [105]=313188,
  [106]=324592,
  [107]=336303,
  [108]=348328,
  [109]=360672,
  [110]=373339,
  [111]=386337,
  [112]=399671 ,
  [113]=413346,
  [114]=427368,
  [115]=441743,
  [116]=456477,
  [117]=471576,
  [118]=487045,
  [119]=502891,
  [120]=519120,
  [121]=535737,
  [122]=552749,
  [123]=570163,
  [124]=587984,
  [125]=606218,
  [126]=624873,
  [127]=643954,
  [128]=663468 ,
  [129]=683421,
  [130]=703819,
  [131]=724671,
  [132]=745981,
  [133]=767757,
  [134]=790005,
  [135]=812733,
  [136]=835947 ,
  [137]=859653,
  [138]=883860,
  [139]=908573 ,
  [140]=933799 ,
  [141]=959547 ,
  [142]=985822,
  [143]=1012633,
  [144]=1039986,
  [145]=1067888 ,
  [146]=1096347,
  [147]=1125371,
  [148]=1154965,
  [149]=1185139,
  [150]=1215900,
  [151]=2494508,
  [152]=2558419,
  [153]=2623549,
  [154]=2689914,
  [155]=2757527,
  [156]=4239607,
  [157]=6239607,
  [158]=8239607,
  [159]=10239607,
  [160]=15239607,
  [161]=18239607,
  [162]=20239607,
  [163]=25239607,
  [164]=30239607,
  [165]=36239607,
  [166]=40239607,
}
for n=167,250 do
  技能消耗.金钱[n]=math.floor(技能消耗.金钱[n-1]*1.2)
end


function 数额尾数转换(数值)
   数值 = tonumber(数值) or 0
   if 数值 < 10000 then
     return 数值
   elseif 数值 >= 10000 and 数值 < 100000000 then
     return string.format("%s%s",math.floor(数值/10000),"万")
   elseif 数值 >= 100000000 and 数值 < 1000000000000  then
     return string.format("%s%s",math.floor(数值/100000000),"亿")
   elseif  数值 >= 1000000000000 and 数值 < 10000000000000000  then
     return string.format("%s%s",math.floor(数值/1000000000000),"兆")
   elseif  数值 >= 10000000000000000  then
     return string.format("%s%s",math.floor(数值/10000000000000000),"京")
  end
end

function 金色id(名字,参数)
  local s=tostring(名字)
  local k=string.len(s)
  local list1={}
  for i=1,k do
   list1[i]=string.sub(s,i,i)
  end
  local 靓号=""
  for n=1,#list1 do
    靓号=靓号.."#"..list1[n]+111+参数*10
  end

  return 靓号
end





