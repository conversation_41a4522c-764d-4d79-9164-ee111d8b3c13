
local ggf = require("GGE.函数")
local CLT, REG = require("HPSocket.PackClient")(true)
local mp = require("网络/MessagePack")
function CLT:重载()
end
fgf="12345*-*12345"
fgc="12345@+@12345"




local key={["B"]="Cb,",["S"]="3C,",["5"]="6D,",["D"]="2W,",["c"]="dc,",["E"]="cj,",["b"]="vt,",["3"]="Iv,",["s"]="j1,",["N"]="23,",["d"]="mP,",["6"]="wd,",["7"]="7R,",["e"]="ET,",["t"]="nB,",["8"]="9v,",["4"]="yP,",["W"]="j6,",["9"]="Wa,",["H"]="D2,",["G"]="Ve,",["g"]="JA,",["I"]="Au,",["X"]="NR,",["m"]="DG,",["w"]="Cx,",["Y"]="Qi,",["V"]="es,",["F"]="pF,",["z"]="CO,",["K"]="XC,",["f"]="aW,",["J"]="DT,",["x"]="S9,",["y"]="xi,",["v"]="My,",["L"]="PW,",["u"]="Aa,",["k"]="Yx,",["M"]="qL,",["j"]="ab,",["r"]="fN,",["q"]="0W,",["T"]="de,",["l"]="P8,",["0"]="q6,",["n"]="Hu,",["O"]="A2,",["1"]="VP,",["i"]="hY,",["h"]="Uc,",["C"]="cK,",["A"]="f4,",["P"]="is,",["U"]="u2,",["o"]="m9,",["Q"]="vd,",["R"]="gZ,",["2"]="Zu,",["Z"]="Pf,",["a"]="Lq,",["p"]="Sw,"}

加密数据 = function (数据)
    数据=加密密码(数据)
    local jg=""
    for n=1,#数据 do
     local z=string.sub(数据,n,n)
 
    if z~="" then
     if key[z]==nil then
        jg=jg..z
      else
        jg=jg..key[z]
       end
      end
      end
    return jg
  end
  
 加密密码=function (source_str)
    local b64chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
    local s64 = ''
    local str = source_str
    while #str > 0 do
      local bytes_num = 0
      local buf = 0
      for byte_cnt=1,3 do
          buf = (buf * 256)
          if #str > 0 then
              buf = buf + string.byte(str, 1, 1)
              str = string.sub(str, 2)
              bytes_num = bytes_num + 1
          end
      end
      for group_cnt=1,(bytes_num+1) do
          local b64char = math.fmod(math.floor(buf/262144),64) + 1
          s64 = s64 .. string.sub(b64chars, b64char, b64char)
          buf = buf * 64
      end
      for fill_cnt=1,(3-bytes_num) do
          s64 = s64 .. '='
      end
    end
    return s64
  end

local mab = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/*=.，'
function 解密数据(数据)
  local jg=数据
  for n=1,#mab do
    local z=string.sub(mab,n,n)
    if key[z]~=nil then
       jg=string.gsub(jg,key[z],z)
    end
  end
  return 解密密码(jg)
end


-- function 解密密码(str64)
--   local b64chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
--   local temp={}
--   for i=1,64 do
--       temp[string.sub(b64chars,i,i)] = i
--   end
--   temp['=']=0
--   local str=""
--   for i=1,#str64,4 do
--       if i>#str64 then
--           break
--       end
--       local data = 0
--       local str_count=0
--       for j=0,3 do
--           local str1=string.sub(str64,i+j,i+j)
--           if not temp[str1] then
--               return
--           end
--           if temp[str1] < 1 then
--               data = data * 64
--           else
--               data = data * 64 + temp[str1]-1
--               str_count = str_count + 1
--           end
--       end
--       for j=16,0,-8 do
--           if str_count > 0 then
--               str=str..string.char(math.floor(data/math.pow(2,j)))
--               data=math.mod(data,math.pow(2,j))
--               str_count = str_count - 1
--           end
--       end
--   end
--   local last = tonumber(string.byte(str, string.len(str), string.len(str)))
--   if last == 0 then
--       str = string.sub(str, 1, string.len(str) - 1)
--   end
--   return str
-- end



function 解密密码(str64)
  local b64chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
  local temp={}
  for i=1,64 do
      temp[string.sub(b64chars,i,i)] = i
  end
  temp['=']=0
  local str=""
  for i=1,#str64,4 do
      if i>#str64 then
          break
      end
      local data = 0
      local str_count=0
      for j=0,3 do
          local str1=string.sub(str64,i+j,i+j)
          if not temp[str1] then
              return
          end
          if temp[str1] < 1 then
              data = data * 64
          else
              data = data * 64 + temp[str1]-1
              str_count = str_count + 1
          end
      end
      for j=16,0,-8 do
          if str_count > 0 then
              str= str..string.char((data >> j) & 0xFF)
              data=data % (2^j)
              str_count = str_count - 1
          end
      end
  end
  local last = tonumber(string.byte(str, string.len(str), string.len(str)))
  if last == 0 then
      str = string.sub(str, 1, string.len(str) - 1)
  end
  return str
end

  function 取回复量(a)
    return 3*a+12
end


function 请求服务 (序号, 内容)
    内容 = 内容 or {}
    if type(内容) == "table" then
        内容.时间 = os.time()
        内容 = zdtostring(内容)
    end

    local 校验数据=取校验数据(序号,__随机记录,__随机1记录,__时间记录)
    return CLT:发送(mp.pack{加密数据(gge.utf8togbk(序号 .. fgf .. 内容..fgf..__时间记录..fgf..校验数据))})
end



function 取校验数据(序号,随机1,随机2,时间)
    local 协议号="xzcjasdiwsnfaasddwf"
    return  ggf.MD5(序号..随机1..协议号..随机2..时间)
  end




-- function CLT:接收事件(数据内容)
--     local data1 = mp.unpack(数据内容)
--     local 内容 = data1[1]
   
--    -- if not __失去焦点计时 then
--     内容 = gge.gbktoutf8(内容)
--    -- print(内容)
--     if nil == 内容 or "" == 内容 then
--         return
--     end
    
--     local 加入数据 = ggf.insert(__全局数据)
--     加入数据(zdloadstring(内容))
--     --table.insert(__全局数据,zdloadstring(内容))
--     __收到信息=true
-- end


function CLT:接收事件(数据内容)
    -- local 收到数据 = 解密数据(数据内容)
    -- if not 收到数据 then return end
    local data1 = mp.unpack(数据内容)
    local 内容 = data1[1]
   -- if not __失去焦点计时 then
    内容 = gge.gbktoutf8(内容)
    if nil == 内容 or "" == 内容 then
        return
    end
    local 收到内容 = zdloadstring(内容)
    if 收到内容 and type(收到内容)=="table" and 收到内容.序号 then
      self:数据到达(收到内容.序号,收到内容.内容)
    end
  
end




function CLT:连接事件(内容)
    print(内容, "连接事件")
end
function CLT:断开事件(enOperation, iErrorCode)
    __连接信息.连接断开=true
    __全局数据 = {}
end
function CLT:重连()
    if __连接信息.连接断开 then
        if CLT:连接(__连接信息.IP, __连接信息.端口) then
            __连接信息.连接断开 = nil
            __连接信息.停止连接=false
        else
            __UI弹出.提示框:打开("#R连接服务器失败")
          --  引擎:关闭()
        end
    end
end


-- function CLT:数据到达()
--     if not __全局数据 or  #__全局数据==0  then
--         __收到信息=nil
--         return
--     end
--     local 序号 = __全局数据[1].序号
--     local 内容 = __全局数据[1].内容
--     if 序号 <= 1000 then
--         self:系统处理(序号, 内容)
--     elseif 序号 > 1000 and 序号 <= 1500 then
--         self:地图处理(序号, 内容)
--     elseif 序号 > 1500 and 序号 <= 2000 then
--         self:对话处理(序号, 内容)
--     elseif 序号 > 3500 and 序号 <= 4000 then
--         self:道具处理(序号, 内容)
--     elseif 序号 > 4000 and 序号 <= 4500 then
--         self:队伍处理(序号, 内容)
--     elseif 序号 > 5500 and 序号 <= 6000 then
--         self:战斗处理(序号, 内容)
--     elseif 序号 > 6100 and 序号 <= 6200 then
--         self:商会处理(序号, 内容)
--     elseif 序号>6000 and 序号<=6100 then
--         self:多角色处理(序号,内容)
--     elseif 序号>6200 and 序号<=6300 then
--         self:神器处理(序号,内容)

--     elseif  序号==76541 then
--         __时间记录=tonumber(内容.时间) 
--         __随机记录=tonumber(内容.随机) 
--         __随机1记录=tonumber(内容.随机1) 
--         请求服务(9527,"0")
--         if 内容.服务状态 ==0  then
--             __连接信息.服务状态 =false
--         else
--             __连接信息.服务状态= true
--         end
--         if __连接信息.服务状态 and not __连接信息.停止连接  and not __连接信息.跨服模式 and __连接信息.选中玩家id  then
--             __连接信息.停止连接=true
--             __连接信息.断开游戏提示=nil
--             __连接信息.重连进入=true
--             请求服务(1.1,_版本号..fgc..__res.配置.账号..fgc.. __res.配置.密码..fgc.."安卓")
--             __UI弹出.提示框:打开("#R正在重新登陆角色。。。。。。")
--             请求服务(4.1,__连接信息.选中玩家id..fgc.._版本号)
--         end
--     elseif 序号==76542 then
--         __连接信息.服务状态= true

--         __时间记录=tonumber(内容.时间) 
--         __随机记录=tonumber(内容.随机) 
--         __随机1记录=tonumber(内容.随机1) 
--         请求服务(1.5,取回复量(__时间记录))
--     elseif 序号==76543 then
--         __连接信息.停止连接 =  false
--         if not __连接信息.断开游戏提示 then
--             __连接信息.断开游戏提示=1
--             __UI弹出.提示框:打开("#Y断线重连中,请耐心等待,在提示连接成功前请不要进行操作,若连接时间较长，可以手动重启客户端")
--         end
--     -- elseif 数据.序号==76544 then
--     --     请求服务(1.6,__res.配置.账号..fgc..__res.配置.密码..fgc..__连接信息.名称..fgc..__连接信息.数字id..fgc..__连接信息.角色名称..fgc.."安卓")
--     end

    
--    table.remove(__全局数据, 1)
-- end



function CLT:数据到达(序号,内容)
    if  not 序号 or not 内容 then return end
    序号=tonumber(序号)
    if 序号 <= 1000 then
        self:系统处理(序号, 内容)
    elseif 序号 > 1000 and 序号 <= 1500 then
        self:地图处理(序号, 内容)
    elseif 序号 > 1500 and 序号 <= 2000 then
        self:对话处理(序号, 内容)
    elseif 序号 > 3500 and 序号 <= 4000 then
        self:道具处理(序号, 内容)
    elseif 序号 > 4000 and 序号 <= 4500 then
        self:队伍处理(序号, 内容)
    elseif 序号 > 5500 and 序号 <= 6000 then
        self:战斗处理(序号, 内容)
    elseif 序号 > 6100 and 序号 <= 6200 then
        self:商会处理(序号, 内容)
    elseif 序号>6000 and 序号<=6100 then
        self:多角色处理(序号,内容)
    elseif 序号>6200 and 序号<=6300 then
        self:神器处理(序号,内容)

    elseif  序号==76541 then
        __时间记录=tonumber(内容.时间) 
        __随机记录=math.random(1,9999)
        __随机1记录=math.random(1,99999)
        请求服务(9527,__随机记录..fgc..__随机1记录)
        if 内容.服务状态 ==0  then
            __连接信息.服务状态 =false
        else
            __连接信息.服务状态= true
        end
        if __连接信息.服务状态 and not __连接信息.停止连接  and not __连接信息.跨服模式 and __连接信息.选中玩家id  then
            __连接信息.停止连接=true
            __连接信息.断开游戏提示=nil
            __连接信息.重连进入=true
            请求服务(1.1,_版本号..fgc..__res.配置.账号..fgc.. __res.配置.密码..fgc.."安卓")
            __UI弹出.提示框:打开("#R正在重新登陆角色。。。。。。")
            请求服务(4.1,__连接信息.选中玩家id..fgc.._版本号)
        end
    elseif 序号==76542 then
        __连接信息.服务状态= true
        __时间记录=tonumber(内容) 
        请求服务(1.5,取回复量(__时间记录))
        __随机记录=math.random(1,9999)
        __随机1记录=math.random(1,99999)
        请求服务(9527,__随机记录..fgc..__随机1记录)
    elseif 序号==76543 then
        __连接信息.停止连接 =  false
        if not __连接信息.断开游戏提示 then
            __连接信息.断开游戏提示=1
            __UI弹出.提示框:打开("#Y断线重连中,请耐心等待,在提示连接成功前请不要进行操作,若连接时间较长，可以手动重启客户端")
        end
    -- elseif 数据.序号==76544 then
    --     请求服务(1.6,__res.配置.账号..fgc..__res.配置.密码..fgc..__连接信息.名称..fgc..__连接信息.数字id..fgc..__连接信息.角色名称..fgc.."安卓")
    end

    

end




function CLT:神器处理(序号,内容) --6200 and 数据.序号<=6300
    if 序号 == 6202 then
         __UI界面.窗口层.神器获得:打开(内容.mp)

    elseif 序号==6205 then
        -- if tp.窗口.修复神器.可视 then
        --     tp.窗口.修复神器:放弃镶嵌(内容)
        -- end
    elseif 序号 == 6203 then
        if __UI界面.窗口层.法宝.是否可见 then
            __UI界面.窗口层.法宝:按钮重置()
        end
    elseif 序号==6204 then
        if __UI界面.窗口层.神器修复.是否可见 then
            __UI界面.窗口层.神器修复:区域解锁(内容)
        end
    elseif 序号 == 6208 then
      if not __UI界面.窗口层.神器查看.是否可见 then
        __UI界面.窗口层.神器查看:打开(内容)
      end
    elseif 序号 == 6210 then
          if __UI界面.窗口层.神器修复.是否可见 then
            __UI界面.窗口层.神器修复:刷新(内容)
          else
              __UI界面.窗口层.神器修复:打开(内容)
          end
      
    elseif 序号==6215 then
      if __UI界面.窗口层.神器修复.是否可见 then
        __UI界面.窗口层.神器修复:激活插槽更新(内容)
      end
    elseif 序号==6216 then
      if __UI界面.窗口层.神器修复.是否可见 then
          __UI界面.窗口层.神器修复:刷新解锁点(内容)
      end
    elseif 序号==6217 then
        if __UI界面.窗口层.神器修复.是否可见 then
          __UI界面.窗口层.神器修复:刷新(内容)
        end
    elseif 序号==6218 then
      if __UI界面.窗口层.合成灵犀玉.是否可见 then
        __UI界面.窗口层.合成灵犀玉:刷新(内容)
      else
          __UI界面.窗口层.合成灵犀玉:打开(内容)
      end

    elseif 序号==6219 then
      if __UI界面.窗口层.合成灵犀玉.是否可见 then
          __UI界面.窗口层.合成灵犀玉:合成开始(内容)
      end
    elseif 序号==6201 then

        if __UI界面.窗口层.神器五行.是否可见 then
          __UI界面.窗口层.神器五行:刷新(内容)
        else
            __UI界面.窗口层.神器五行:打开(内容)
        end
    elseif 序号==6207 then
          if __UI界面.窗口层.神器五行.是否可见 then
                __UI界面.窗口层.神器五行:设置保存(内容)
          end
    elseif 序号==6209 then
          if __UI界面.窗口层.神器五行.是否可见 then
            __UI界面.窗口层.神器五行:刷新(内容)
          end
    elseif 序号==6211 then
          角色信息.神器佩戴 = 内容.是否
          if __UI界面.窗口层.法宝.是否可见 then
              __UI界面.窗口层.法宝:更新神器(角色信息.神器数据)
          end
     elseif 序号==6212 then
    --     tp.队伍[1].门派=内容
          if __UI界面.窗口层.法宝.是否可见 then
              __UI界面.窗口层.法宝:更新神器(角色信息.神器数据)
          end
    --     if tp.窗口.神器查看.可视 then
    --        tp.窗口.神器查看:打开()
    --     end
    --     if tp.窗口.修复神器.可视 then
    --        tp.窗口.修复神器:打开()
    --     end
          if __UI界面.窗口层.神器查看.是否可见 then
              __UI界面.窗口层.神器查看:置可见(false)
          end
          if __UI界面.窗口层.神器修复.是否可见 then
              __UI界面.窗口层.神器修复:置可见(false)
          end
    end
    内容=nil
  end





function CLT:多角色处理(序号,内容)

    --  elseif 序号 == 115 then
    --   内容.角色 = tonumber(内容.角色)
    --   tp.多角色[内容.角色]=tp._队伍.创建()
    --   tp.多角色[内容.角色]:重置属性(内容.玩家数据)
    -- elseif 序号 == 116 then
    --   内容.角色 = tonumber(内容.角色)
    --   tp.多角色[内容.角色].宝宝列表=内容.召唤兽
    --   tp.多角色[内容.角色]:刷新宝宝技能()
    --   tp.多角色[内容.角色]:刷新宝宝认证技能()
  
  -- print(序号)
  -- table.print(内容)
   if _tp.多角色[内容.角色]==nil then 内容=nil return end

    self:道具刷新()
    if 序号==6001 then
        _tp.多角色[内容.角色] = 内容.角色属性  
      if __UI界面.窗口层.角色属性.是否可见 then
         __UI界面.窗口层.角色属性:刷新(内容.角色)
      else
          __UI界面.窗口层.角色属性:打开(内容.角色)
          __UI界面.窗口层.多开系统:置可见(false)
      end
    elseif 序号==6002 then
        _tp.多角色[内容.角色] = 内容.角色属性
        if __UI界面.窗口层.角色属性.是否可见 and __UI界面.窗口层.角色属性.状态~="辅助"  then
            __UI界面.窗口层.角色属性:显示设置()
        end
    elseif 序号==6003 then
        _tp.多角色[内容.角色].辅助技能[内容.序列].等级=内容.等级
        __UI界面.窗口层.角色属性.角色信息 = table.copy( _tp.多角色[内容.角色])
        if __UI界面.窗口层.角色属性.是否可见 then
            __UI界面.窗口层.角色属性.辅助控件:技能设置()
        end
    elseif 序号==6004 then
        _tp.多角色[内容.角色].修炼=内容.人物
        _tp.多角色[内容.角色].bb修炼=内容.bb
        __UI界面.窗口层.角色属性.角色信息 = table.copy( _tp.多角色[内容.角色])
        __UI界面.窗口层.角色属性.修炼控件:修炼显示()
    elseif 序号==6005 then
        -- if __UI界面.窗口层.多开经脉.是否可见 then
        --    tp.窗口.多角色奇经八脉:刷新(tp.多角色[内容.角色],内容.角色)
        -- else
        --    tp.窗口.多角色奇经八脉:打开(tp.多角色[内容.角色],内容.角色)
        -- end

        if __UI界面.窗口层.多开经脉.是否可见 then
            __UI界面.窗口层.多开经脉:刷新(_tp.多角色[内容.角色],内容.角色)
        else
            __UI界面.窗口层.多开经脉:打开(_tp.多角色[内容.角色],内容.角色)
        end
     elseif 序号==6006 then
        if  __UI界面.窗口层.多开道具.是否可见 then
            __UI界面.窗口层.多开道具:刷新(内容.角色)
        else
            __UI界面.窗口层.多开道具:打开(内容.角色)
        end
     elseif 序号==6007 then
        _tp.多角色[内容.角色].道具列表=内容.道具.道具
        --  if __UI界面.窗口层.多开道具.可视 and  __UI界面.窗口层.多开道具.点击类型=="道具" then
        --      __UI界面.窗口层..多开道具:刷新道具资源()
        --  end
     elseif 序号==6008 then

          _tp.多角色[内容.角色].宝宝列表 = 内容.召唤兽 
          if __UI界面.窗口层.多开宠物.是否可见 then
              __UI界面.窗口层.多开宠物:显示设置()
          end
     elseif 序号==6009 then
        _tp.多角色[内容.角色].行囊列表=内容.道具.道具
        if  __UI界面.窗口层.多开道具.是否可见 then
            __UI界面.窗口层.多开道具:刷新(内容.角色)
        end
     elseif 序号==6010 then
        _tp.多角色[内容.角色].法宝列表 = 内容.道具.法宝
        _tp.多角色[内容.角色].法宝佩戴 = 内容.道具.佩戴
        -- if __UI界面.窗口层.可视 and __UI界面.窗口层.点击类型=="法宝" then
        --    tp.窗口.多开道具:刷新法宝资源()
        -- end
     elseif 序号==6011 then
       -- tp.窗口.多开道具:更新法宝经验(内容)
     elseif 序号==6012 then
            if __UI界面.窗口层.多开宠物.是否可见 then
                __UI界面.窗口层.多开宠物:显示设置()
            end
     elseif 序号==6013 then
        if  __UI界面.窗口层.多开道具.是否可见 then
            __UI界面.窗口层.多开道具.分类="灵饰"
            __UI界面.窗口层.多开道具:模型重置()
        end
     elseif 序号==6014 then
          if  __UI界面.窗口层.多开道具.是否可见 then
              __UI界面.窗口层.多开道具.分类="锦衣"
              __UI界面.窗口层.多开道具:模型重置()
          end
     elseif 序号==6015 then
         --  tp.窗口.多开道具:置形象()
    elseif 序号==6016 then
        _tp.多角色[内容.角色].坐骑=内容.坐骑
        if  __UI界面.窗口层.多开道具.是否可见 then
            __UI界面.窗口层.多开道具.角色信息=table.copy(_tp.多角色[内容.角色])
            __UI界面.窗口层.多开道具:重置窗口()
            __UI界面.窗口层.多开道具:模型重置()
        end



    elseif 序号==6017 then
         -- tp.窗口.多开道具:刷新坐骑饰品()
    elseif 序号==6018 then
      __UI界面.窗口层.多开仓库:打开(内容.道具.道具,内容.仓库总数,内容.召唤兽总数,内容.角色)
    elseif 序号==6019 then  
         if 内容.数据类型~=nil and 内容.数据类型=="道具" then
            _tp.多角色[内容.角色].道具列表=内容.道具.道具
         else
            _tp.多角色[内容.角色].行囊列表=内容.道具.道具
         end
        if  __UI界面.窗口层.多开道具.是否可见 then
              __UI界面.窗口层.多开道具:刷新(内容.角色)
        end

         -- tp.窗口.多角色仓库类.道具类型=内容.道具.数据类型
    elseif 序号==6020 then  
          __UI界面.窗口层.多开仓库:道具刷新()
    elseif 序号==6021 then
        if __UI界面.窗口层.多开仓库.类型=="道具" then
             __UI界面.窗口层.多开仓库:刷新仓库(内容.道具.道具,内容.页数)
        elseif __UI界面.窗口层.多开仓库.类型=="召唤兽" then
               _tp.多角色[内容.角色].宝宝列表 = 内容.宝宝列表
              __UI界面.窗口层.多开仓库:刷新仓库(内容.召唤兽仓库数据,内容.页数)
        end
    elseif 序号==6022 then  
        __UI界面.窗口层.多开仓库.类型="道具"
        __UI界面.窗口层.多开仓库.总页=内容.总数
        __UI界面.窗口层.多开仓库:刷新仓库(内容.道具.道具,1)
    elseif 序号==6023 then
          _tp.多角色[内容.角色].宝宝列表 = 内容.召唤兽
          if __UI界面.窗口层.多开仓库.是否可见 then
              __UI界面.窗口层.多开仓库.类型="召唤兽"
              __UI界面.窗口层.多开仓库.宝宝总页=内容.召唤兽仓库总数
              __UI界面.窗口层.多开仓库:刷新仓库(内容.召唤兽仓库数据,1)
          end
          if __UI界面.窗口层.多开宠物.是否可见 then
              __UI界面.窗口层.多开宠物:显示设置()
          end

    elseif 序号==6024 then
      __UI界面.窗口层.多开回收:刷新(内容)

    elseif 序号==6025 then--18
      for n=1,#_tp.多角色[内容.角色].宝宝列表 do
        _tp.多角色[内容.角色].宝宝列表[n].参战信息=nil
          if 内容.召唤兽.认证码 == _tp.多角色[内容.角色].宝宝列表[n].认证码 then
            _tp.多角色[内容.角色].宝宝列表[n].参战信息=1
          end
      end
      if __UI界面.窗口层.多开宠物.是否可见 then
          __UI界面.窗口层.多开宠物:显示设置()
      end
     elseif 序号==6026 then
        if __UI界面.窗口层.多开宠物.是否可见 then
            __UI界面.窗口层.多开宠物:刷新(内容.角色)
        end
     elseif 序号==6027 then
        _tp.多角色[内容.角色].宝宝列表[内容.序列].名称=内容.名称
        if __UI界面.窗口层.多开宠物.是否可见 then
            __UI界面.窗口层.多开宠物:显示设置()
        end
    elseif 序号==6028 then
        if __UI界面.窗口层.多开宠物.是否可见 then
             __UI界面.窗口层.多开宠物.宠物数据=table.copy(_tp.多角色[内容.角色].宝宝列表)
            __UI界面.窗口层.多开宠物.名称选择:置数据()
        end
  
  
  
  
  
    end
  内容 = nil
  end
  

function CLT:系统处理(序号, 内容)
 
    if   序号 == 2 then
        -- __UI界面.登录层.登录游戏:置可见(false)
        -- __UI界面.登录层.创建角色:置可见(true, true)
        __UI界面.登录层.角色界面:置可见(true)
        __UI界面.登录层.创建角色:置可见(false)
    elseif 序号 == 3 then
          __UI界面.登录层.角色界面:置可见(false)
          __UI界面.登录层.创建角色:打开()
    elseif 序号 == 4  then
        if __UI界面.登录层.登录游戏.是否可见 then
            __UI界面.登录层.登录游戏:置可见(false)
        elseif __UI界面.登录层.创建角色.是否可见 then
            __UI界面.登录层.创建角色:置可见(false)
        end
        __UI界面.登录层.角色界面:打开(内容)
  

    elseif  序号 == 5 then
        __主显 = require("显示/主显层")()
        角色信息 = require("对象/角色信息")()
        角色信息:重置属性(内容)
        __主显:加载(角色信息.地图数据.编号)
        __多开操作 = 内容.多角色操作
        if __连接信息.重连进入 then
            __UI界面.界面层:重新初始化()
            __连接信息.重连进入=nil
            __UI弹出.提示框:打开("#Y重连成功,欢迎继续游戏")
        else
            __UI界面.登录层:置可见(false)
            __UI界面.界面层:置可见(true)
            if __res.配置.分辨率 and __分辨率设置[__res.配置.分辨率]  and not __手机 then
                引擎:置宽高(__分辨率设置[__res.配置.分辨率][1],__分辨率设置[__res.配置.分辨率][2])
            end
        end
        __UI界面.界面层:重置(nil,true)
        __UI界面.界面层.队伍栏.队员网格:置头像({})
        if __连接信息.选中大区 then
              引擎:置标题(__连接信息.选中大区.." - ("..__连接信息.选中大区.."["..__连接信息.选中大区.."] - "..角色信息.名称.."["..角色信息.数字id.."])")
        end
     
      --  __连接信息.数字id = self.角色列表[选中].id

    elseif 序号==5.1 then
      
        __res:写出文件( "ggwb.txt", 内容.文本)

    -- elseif 序号==5.2 then
    --     if type(内容)=="table" then
    --         for k,v in pairs(内容) do
    --             if type(v)=="table" and not __NPC列表[k] then
    --               __NPC列表[k]=v
    --             end
    --         end
    --     end

    elseif 序号==6 then
        -- tp.窗口.宠物领养栏:打开()
    elseif 序号 == 7  then
        if 内容 == "#Y创建账号成功！" then
            __UI界面.登录层.注册账号:置可见(false)
            __UI界面.登录层.登录游戏:置可见(true, true)
         end
        __UI弹出.提示框:打开(内容)
    elseif 序号==8 then --检查宠物领养
        角色信息.宠物 = 内容
        __UI界面.窗口层.宠物:打开()
    elseif 序号 == 9 then
        __UI界面.窗口层.商店:打开(内容.商品,内容.名称, 内容.类型, 内容.银子)

        -- tp.金钱=内容.银子
        -- tp.窗口.商店:打开(内容.商品,内容.名称)
    elseif 序号==9.1 then --钓鱼积分商店
        -- tp.钓鱼积分=内容.钓鱼积分
        -- tp.窗口.钓鱼积分商店:打开(内容.商品,内容.名称)

        
    elseif 序号 == 10 then
        角色信息:重置属性(内容, 1)
     
        __UI界面.窗口层.人物属性:显示设置()
    
    elseif 序号==11 then

         __主显.主角:加入动画("升级")
    elseif 12 == 序号 then
        if __UI界面.窗口层.人物属性.是否可见 then
             请求服务(7)
        end
    elseif 13 == 序号 then
        __UI界面.窗口层.飞行符:打开()
    elseif 14 == 序号 then
        角色信息.银子=内容.银子
        _tp.道具列表=内容.道具
        if __UI界面.窗口层.打造.是否可见 then
            __UI界面.窗口层.打造:刷新材料(内容.道具)
        else
           __UI界面.窗口层.打造:打开(内容.道具)
        end
    elseif 14.1 == 序号 then
        角色信息.银子=内容.银子
        角色信息.储备=内容.储备
        角色信息.存银=内容.存银
        角色信息.体力=内容.体力
        _tp.道具列表=内容.道具
        self:道具刷新()
    elseif 15 == 序号 then
        if not 角色信息.体力 or not 角色信息.活力 then
            return
        end
        角色信息.体力 = 内容.体力
        角色信息.活力 = 内容.活力
        if __UI界面.窗口层.人物属性.是否可见 then
            __UI界面.窗口层.人物属性:显示设置()
        end
        if __UI界面.窗口层.制作仙露.是否可见 then
          __UI界面.窗口层.制作仙露:刷新()
        end
    elseif 16 == 序号 then
        刷新宝宝列表(内容)
    elseif 17 == 序号 then
        刷新宝宝列表(内容)
        __UI界面.窗口层.召唤属性:打开()
    elseif 序号==17.1 then
        刷新宝宝列表(内容)
    elseif 序号==17.2 then
         角色信息.子女列表=内容

    elseif 18 == 序号 then
        for n = 1, #角色信息.宝宝列表 do
            角色信息.宝宝列表[n].参战信息 = nil
            if 内容.认证码 == 角色信息.宝宝列表[n].认证码 then
                角色信息.宝宝列表[n].参战信息 = 1
            end
        end
        角色信息.参战宝宝 = 内容
        __UI界面.界面层.玩家界面.宠物头像:置头像(角色信息.参战宝宝)
        刷新宝宝窗口()
    elseif 19 == 序号 then
        角色信息.宝宝列表[内容.序列].名称 = 内容.名称
        刷新宝宝窗口()
    elseif 20 == 序号 then
        for n = 1, #角色信息.宝宝列表 do
            if 内容.认证码 == 角色信息.宝宝列表[n].认证码 then
                角色信息.宝宝列表[n] = 内容
            end
        end
        刷新宝宝窗口()
    elseif 21 == 序号 then
        if 角色信息 and 角色信息.宝宝列表 and 角色信息.宝宝列表[tonumber(内容)] then
            table.remove(角色信息.宝宝列表, tonumber(内容))
        end
        刷新宝宝窗口(true)
    elseif 22 == 序号 then
        角色信息.宠物 = 内容
        __UI界面.窗口层.宠物:打开()
    elseif 序号==22.1 then
            -- if  __UI界面.窗口层.成就提示.是否可见 then
            --     __UI界面.窗口层.成就提示:刷新(内容)
            -- else
            --     __UI界面.窗口层.成就提示:打开(内容)
            -- end

    elseif 23 == 序号 then
        _tp.道具列表=内容.道具
       __UI界面.窗口层.打书内丹:打开(内容.道具,"洗练")
    elseif 序号==24 or 序号==96 then
        _tp.道具列表=内容.道具
        __UI界面.窗口层.打书内丹:刷新道具(内容.道具)
      elseif  序号==95 then
        __UI界面.窗口层.打书内丹:清除()
      elseif 序号==26 then
        if  __UI界面.窗口层.打书内丹.是否可见 then
            __UI界面.窗口层.打书内丹.选中=nil
            __UI界面.窗口层.打书内丹:清除()
        elseif __UI界面.窗口层.召唤合宠.是否可见 then
            __UI界面.窗口层.召唤合宠:清除()
        end
     
    elseif 27 == 序号 then
        __UI界面.界面层.聊天控件:添加文本(内容.内容 or 内容.文本, 内容.频道)

    elseif 28 == 序号 then
            if __res.配置.行囊==1 then
                  if __UI界面.窗口层.道具行囊.是否可见 then
                      __UI界面.窗口层.道具行囊.窗口类型 = "召唤兽"
                      __UI界面.窗口层.道具行囊:重置窗口()
                      __UI界面.窗口层.道具行囊:模型重置()
                  end
            else
                if __UI界面.窗口层.新行囊.是否可见 then
                    __UI界面.窗口层.新行囊.窗口类型 = "召唤兽"
                    __UI界面.窗口层.新行囊:重置窗口()
                    __UI界面.窗口层.新行囊:模型重置()
                end
            end
        
    elseif 29 == 序号 then
        __UI界面.窗口层.染色:打开()
    elseif 30 == 序号 then
        角色信息.染色组 = 内容
        __主显.主角.染色组 = 内容
        __主显.主角:置模型()
    elseif 31 == 序号 then
        角色信息:重置属性(内容)
        角色信息.银子 = 内容.银子
        角色信息.储备 = 内容.储备
        角色信息.存银 = 内容.存银
        if __UI界面.窗口层.人物属性.是否可见 then
            if __UI界面.窗口层.人物属性.状态=="辅助" then 
                __UI界面.窗口层.人物属性.辅助控件:技能设置()
            else
                __UI界面.窗口层.人物属性:显示设置()
            end
        end
        if __UI界面.窗口层.技能学习.是否可见 then
            __UI界面.窗口层.技能学习:显示设置()
        end
        ----学习师门技能
    elseif 32 == 序号 then
        __UI界面.窗口层.技能学习:打开()
    elseif 序号==33 then
        角色信息:重置属性(内容)  
        if __UI界面.窗口层.人物属性.是否可见 then
             __UI界面.窗口层.人物属性:显示设置()
        end
    elseif 序号== 34   then
        角色信息.辅助技能[内容.序列].等级 = 内容.等级
        if __UI界面.窗口层.人物属性.是否可见 then
            __UI界面.窗口层.人物属性.辅助控件:技能设置()
        end
    elseif 序号==34.1 then
        角色信息.强化技能[内容.序列].等级=内容.等级
        if __UI界面.窗口层.人物属性.是否可见  then
            __UI界面.窗口层.人物属性.辅助控件:技能设置()
        end
    elseif 35 == 序号 then
        角色信息.银子 = 内容.银子
        角色信息.储备 = 内容.储备
        角色信息.存银 = 内容.存银
        角色信息.当前经验 = 内容.经验
    elseif 36 == 序号 then

        __主显.主角:加入动画(内容.动画)
    elseif 37 == 序号 then
        if "1" == 内容 or not 内容 then
            内容 = {}
        end
        角色信息.变身数据 = 内容.变身数据 
        角色信息.变异 =内容.变异 
        __主显.主角.变身数据 = 内容.变身数据
        __主显.主角.变异 =内容.变异 
        __主显.主角:置模型()
    elseif 38 == 序号 then
        if 内容 and 1 == 内容.方式 then
            return
        end
        __UI界面.界面层.聊天控件:添加文本(内容.内容,内容.频道)
  
    elseif 39 == 序号 then
        if __UI界面.窗口层.任务提示.是否可见 then
            请求服务(10)
        end
    elseif 40 == 序号 then
      if __UI界面.窗口层.任务提示.是否可见 then
          __UI界面.窗口层.任务提示:刷新(内容)
      else
          __UI界面.窗口层.任务提示:打开(内容)
      end

        ----------------------------------------------
    elseif 序号==41 then
        角色信息.快捷技能 = 内容
        if __UI界面.界面层.玩家界面.快捷控件.是否可见 then
            __UI界面.界面层.玩家界面.快捷控件:置技能()
        else
            __UI界面.界面层.玩家界面.快捷控件:打开()
        end
 





    elseif 42 == 序号 then
       
        角色信息.快捷技能 = 内容
        if __UI界面.界面层.玩家界面.快捷控件.是否可见 then
            __UI界面.界面层.玩家界面.快捷控件:置技能()
            
        end


       
    elseif 43 == 序号 then

        __UI界面.界面层.玩家界面.时辰控件:重置(内容.时辰)
    elseif 44 == 序号 then
        角色信息.修炼 = 内容.人物
        角色信息.bb修炼 = 内容.bb
        __UI界面.窗口层.人物属性.修炼控件:修炼显示()
    elseif 序号==45 then
        if 角色信息 and 角色信息.宝宝列表 and 角色信息.宝宝列表[tonumber(内容)] then
            table.remove(角色信息.宝宝列表, tonumber(内容))
        end
        刷新宝宝窗口(true)
    elseif 46 == 序号 then
        __UI界面.界面层.任务追踪:刷新(内容[1],内容[2],内容[3],内容[4])
    elseif 47 == 序号 then
        __UI界面.界面层.任务追踪.追踪列表:置数据()
        __UI界面.界面层.任务追踪.任务数据={}
        __UI界面.界面层.任务追踪.图标数据={}
    elseif 48 == 序号 then
        角色信息.自动遇怪 = 内容.遇怪
        if 角色信息.自动遇怪=="1" then 角色信息.自动遇怪=false end
    elseif 49 == 序号 then
        __UI界面.窗口层.排行榜:打开(内容)
    elseif 50 == 序号 then
       __UI界面.窗口层.好友列表:打开(内容)
    elseif 51 == 序号 then
        if __UI界面.窗口层.好友列表.是否可见 then
            __UI界面.窗口层.好友列表:更新数据(内容)
        end







    elseif 52 == 序号 then
        -- if __UI界面.窗口层.好友.是否可见 then
        --     请求服务(19)
        -- end
        
    elseif 53 == 序号 then
        if __UI界面.窗口层.好友查询.是否可见 then
            __UI界面.窗口层.好友查询:更新数据(内容.数据)
        end

    elseif 序号==54 then
        if __UI界面.窗口层.好友消息.是否可见 then
            __UI界面.窗口层.好友消息:更新数据(内容.数据)
        else
            __UI界面.窗口层.好友消息:打开(内容.数据)
        end

    
    elseif 序号==55 then
        if 内容.道具~=nil then
            _tp.道具列表=内容.道具.道具
        end
        if 内容.召唤兽~=nil then
            角色信息.宝宝列表=内容.召唤兽
            刷新宝宝窗口()
        end
         if __UI界面.窗口层.超级赐福.是否可见 then
            __UI界面.窗口层.超级赐福:刷新()
         else
            __UI界面.窗口层.超级赐福:打开()
         end
    
    elseif 56 == 序号 then
        __UI界面.界面层.玩家界面.按钮控件.好友.消息提醒 = true
    elseif 57 == 序号 then
        __UI界面.界面层.玩家界面.按钮控件.好友.消息提醒 = false
    elseif 58 == 序号 then
        if 内容.道具 then
          _tp.道具列表=内容.道具.道具
        end
        if __UI界面.窗口层.超级赐福.是否可见 and 内容.技能  then
            __UI界面.窗口层.超级赐福:刷新赐福(内容.技能)
        end
    elseif 序号==59 then
      --  __UI界面.界面层.聊天控件:添加文本("#Y"..内容,"xt")
        __UI界面.界面层.流动公告:打开(内容)
    elseif 序号==59.1 then
        -- 游戏传音:添加公告(内容)
      elseif 序号==59.2 then
        -- tp.窗口.传音打开:打开(内容)
    
    elseif 60 == 序号 then
        角色信息.坐骑 = 内容
        __主显.主角.坐骑 = 内容
        __主显.主角:置模型()
        if  __res.配置.行囊==1 and  __UI界面.窗口层.道具行囊.是否可见  then
                __UI界面.窗口层.道具行囊:重置窗口()
                __UI界面.窗口层.道具行囊:模型重置()
        elseif __UI界面.窗口层.新行囊.是否可见  then
                  __UI界面.窗口层.新行囊:重置窗口()
                  __UI界面.窗口层.新行囊:模型重置()
        end
        if __UI界面.窗口层.坐骑属性.是否可见 then
            __UI界面.窗口层.坐骑属性.骑乘:重置文字()
        end
    elseif 61 == 序号 then
        角色信息.坐骑列表 = 内容
        if __UI界面.窗口层.坐骑属性.是否可见 then
            __UI界面.窗口层.坐骑属性.选中=nil
            __UI界面.窗口层.坐骑属性.名称选择:置数据()
            __UI界面.窗口层.坐骑属性:显示设置()
        end
        if __res.配置.行囊==1 and __UI界面.窗口层.道具行囊.是否可见 and __UI界面.窗口层.道具行囊.窗口类型 == "坐骑" then
            __UI界面.窗口层.道具行囊:重置窗口()
            __UI界面.窗口层.道具行囊:模型重置()
        elseif __UI界面.窗口层.新行囊.是否可见 and __UI界面.窗口层.新行囊.窗口类型 == "坐骑" then
              __UI界面.窗口层.新行囊:重置窗口()
              __UI界面.窗口层.新行囊:模型重置()
        end
    elseif 序号==61.2 then
          角色信息.坐骑列表=内容
        __UI界面.窗口层.坐骑属性:打开()
      elseif 序号==61.1 then
            角色信息.坐骑列表[内容.编号]=内容.数据
            if __UI界面.窗口层.坐骑属性.是否可见 then
                __UI界面.窗口层.坐骑属性:显示设置()
            end
            if __UI界面.窗口层.坐骑技能.是否可见 then
                __UI界面.窗口层.坐骑技能:刷新(角色信息.坐骑列表[内容.编号],内容.编号)
            end
      elseif 序号 == 62 then
        __UI界面.窗口层.装备开运:打开(内容)
      elseif 序号 == 63 then
            if __UI界面.窗口层.装备开运.是否可见 then
                __UI界面.窗口层.装备开运.银子= 内容.银子
                __UI界面.窗口层.装备开运.体力= 内容.体力
                __UI界面.窗口层.装备开运:显示刷新()
            end
      elseif 序号 == 64 then
            __UI界面.窗口层.道具鉴定:打开(内容)
      elseif 序号 == 65 then
             __UI界面.窗口层.道具附魔:打开(内容)
------------------------------------------------------------
    elseif  序号 ==66 then
      __UI弹出.组合输入框:打开("创建帮派",{"为你的帮派取一个好听的名字吧,创建帮派需5亿银子","黄色"})
    elseif  序号==67 then
        if  __UI界面.窗口层.帮派查看.是否可见 then
            __UI界面.窗口层.帮派查看:刷新(内容)
          else
            __UI界面.窗口层.帮派查看:打开(内容)
          end
    elseif 序号 == 68 then --加入帮派

        __UI界面.窗口层.帮派加入:打开(内容)
    elseif 序号 == 69 then --1 帮派数据 2 称谓数据 3 退出帮派
        if 内容.项目 == "1" then
            角色信息.帮派数据 = 内容
            if  __UI界面.窗口层.帮派查看.是否可见 then
                __UI界面.窗口层.帮派查看.帮派权限 = 角色信息.帮派数据.权限
            end
        elseif 内容.项目 == "2" then
          角色信息.称谓 = 内容.称谓
          角色信息.当前称谓 = 内容.当前称谓
          __主显.主角:置称谓(内容.当前称谓)
        elseif 内容.项目 == "3" then
            角色信息.帮派数据 = nil
            if __UI界面.窗口层.帮派查看.是否可见 then
                __UI界面.窗口层.帮派查看:置可见(false)
            end
        end

    elseif 序号 == 70 then
            -- tp.窗口.跑商商店:打开(内容.商品,内容.银子,内容.道具)
    elseif 序号 == 71 then
             __主显.主角.飞行 = true
            __UI弹出.提示框:打开("#Y你飞起来了........")
    elseif 序号 == 72 then
            __主显.主角.飞行 = false
            __UI弹出.提示框:打开("#Y你降落了........")
    elseif 序号 == 73 then
            -- tp.进程 = 7
    elseif 序号 == 74 then
        __UI界面.窗口层.帮派点修:打开(内容.银子,内容.存银)
    elseif 序号 == 75 then
            --tp.窗口.帮派技能学习:打开(内容.银子,内容.储备,内容.帮派数据)
    elseif 序号 == 76 then
         __UI界面.窗口层.武器染色:打开(内容)

    elseif  序号==77 then
        if __UI界面.窗口层.奇经八脉.是否可见 then
            __UI界面.窗口层.奇经八脉:刷新(内容)
        else
            __UI界面.窗口层.奇经八脉:打开(内容)
        end
    elseif 序号==78 then
       -- 红尘进度=内容.进度
    elseif 序号==79 then
      if __UI界面.窗口层.符石合成.是否可见 then
            __UI界面.窗口层.符石合成:刷新材料(内容.道具)
      else
          __UI界面.窗口层.符石合成:打开(内容.道具)
      end
    elseif 序号==79.1 then
      --  tp.窗口.钟灵石合成:打开(内容)
    elseif 序号==80 then
       -- tp.窗口.坐骑染色:打开(内容)
    elseif 序号==81 then
      --  tp.窗口.坐骑饰品染色:打开(内容)
    elseif 序号==82 then
      _UI界面.窗口层.人物称谓:打开()
    elseif 序号==83 then
      --  tp.窗口.法宝合成:打开(内容)
    elseif 序号==84.1 then
       if 内容.编号 and 内容.道具 then
            local 编号=tonumber(内容.编号)
            local 道具=table.copy(内容.道具.道具)
            local 事件 = function (a)
              if 道具[a]~=nil then
                  if 道具[a].总类 == 2  then
                        请求服务(4515,{序列=道具[a],序列1=编号,序列2=a})
                    else
                          __UI弹出.提示框:打开("#Y请选择正确道具")
                    end
              end
          end
          __UI弹出.道具选择:打开(道具,事件)
       end
       




       






    elseif 序号==84.2 then
      --  tp.窗口.灵箓:打开(内容)
    elseif 序号==85 then
        -- if tp.窗口.神秘宝箱.可视 then
        --   tp.窗口.神秘宝箱:打开()
        -- end
        -- tp.窗口.神秘宝箱:打开(内容)
    elseif 序号 == 86 then
      __收到流程 = tonumber(内容.收到)
      __发送流程 = tonumber(内容.发送)
    elseif 序号 == 87 then
        __UI界面.窗口层.生死劫:打开()
    elseif 序号 == 88 then
            if __UI界面.窗口层.累计充值.是否可见 then
                __UI界面.窗口层.累计充值:刷新(内容)
            else
                __UI界面.窗口层.累计充值:打开(内容)
            end
        
    elseif 序号 == 89 then
        __UI弹出.组合输入框:打开("帮派竞赛报名",{"请输入报名帮费(最低费用50W)"})
    elseif 90 == 序号 then
        if __UI界面.窗口层.梦幻指引.是否可见 then
            __UI界面.窗口层.梦幻指引:刷新(内容)
        else
            __UI界面.窗口层.梦幻指引:打开(内容)
        end
    elseif 序号==90.1 then
        __UI界面.窗口层.攻略查看:打开(内容)
    elseif 序号==91 then
    __UI界面.窗口层.仙玉商城:打开(内容)
    elseif 序号==92 then
      __UI界面.窗口层.仙玉商城:刷新(内容)
    elseif 序号==92.1 then
       __UI界面.窗口层.召唤兽查看:打开(内容)
    elseif 序号==92.2 then
       __UI界面.窗口层.仙玉商城:刷新货币(内容)
    elseif 序号==93 then
    --    tp.pk开关 = 内容.开关
    elseif 序号==94 then
    --    tp.强p开关 = 内容.开关
 
 
    elseif 序号==99 then---=================屏蔽fwd向客户端发送时间
    --   local 临时数据=table.loadstring(数据)
    --   if 临时数据.内容~=nil and 临时数据.内容.内容~=nil and 临时数据.内容.频道~=nil then
    --     tp.窗口.消息框:添加文本(临时数据.内容.内容,临时数据.内容.频道)
    --   end
------------------------------------------------
    elseif 100 == 序号 then
     __跨地图寻径开关=false
     __跨地图寻径={}
     __跨地图寻径=__Greedy:getShortPath(角色信息.地图数据.编号+0,内容.地图+0)
      if __跨地图寻径~= nil and #__跨地图寻径>0 then
          table.insert(__跨地图寻径,{当前地图=内容.地图+0,x=内容.x+0,y=内容.y+0})
          __跨地图寻径开关=true
          local 内容1={x=__跨地图寻径[1].x+0,y=__跨地图寻径[1].y+0,距离=0}
          __UI弹出.提示框:打开("#Y自动寻路中…………")
          请求服务(1001,生成XY(__跨地图寻径[1].x+0,__跨地图寻径[1].y+0))
          __主显.主角:设置路径(内容1,true)
          table.remove(__跨地图寻径,1)
        else
            __跨地图寻径={}
           __UI弹出.提示框:打开("#Y目标位置无法通过传送点自动到达")
       end
    elseif 100.1 == 序号 then
              __主显.主角:设置路径(内容,true)

    elseif 101 == 序号 then
        if __UI界面.战斗层.自动抓鬼.是否可见 then
            __UI界面.战斗层.自动抓鬼:刷新(内容)
        else
            __UI界面.战斗层.自动抓鬼:打开(内容)
        end
    elseif 序号==102 then
          __UI界面.窗口层.自选系统:打开(内容)
    elseif 序号==102.1 then
          __UI界面.窗口层.自选灵饰系统:打开(内容)
    elseif 序号==103 then --获取助战物品数据
        -- if tp.窗口.每日活动.可视 then
        --   tp.窗口.每日活动:刷新(内容)
        -- else
        --   tp.窗口.每日活动:打开(内容)
        -- end
        -- elseif 231 == 序号 then
        --     __UI界面.窗口层["每日活动"]["打开"](__UI界面.窗口层["每日活动"], 内容)
    elseif 序号==104 then
        if __UI界面.窗口层.每日查看.是否可见 then
            __UI界面.窗口层.每日查看:刷新(内容)
        else
            __UI界面.窗口层.每日查看:打开(内容)
        end
        
    elseif 序号==105 then
        __UI界面.窗口层.多开系统:打开(内容)
    
    elseif 序号==106 then
        if  __UI界面.窗口层.签到界面.可视 then
            __UI界面.窗口层.签到界面:刷新(内容)
        else
            __UI界面.窗口层.签到界面:打开(内容)
        end
    
    
    elseif 序号==107 then
        __UI界面.窗口层.多开系统:刷新(内容)
    elseif 序号==108 then
      __UI界面.窗口层.炼丹炉:打开(内容)
    elseif 序号==108.1 then
      __UI界面.窗口层.炼丹炉:更新数据(内容)
    elseif 序号==108.2 then
      __UI界面.窗口层.炼丹炉:开奖(内容)
    elseif 序号==109 then
      if __UI界面.窗口层.炼丹炉.是否可见 then
        __UI界面.窗口层.炼丹炉:刷新(内容)
     else
          请求服务(99)
     end
    elseif 序号==110 then
        角色信息.携带宠物=tonumber(内容.数量)
        if __UI界面.窗口层.召唤属性.是否可见 then
            __UI界面.窗口层.召唤属性:显示设置()
        end
    elseif 序号==111 then
       -- tp.房屋数据 = 内容
        -- tp:关闭窗口()
        -- tp.场景.抓取物品 = nil
        -- tp.场景.抓取物品ID = nil
        -- tp.场景.抓取物品注释 = nil

        -- elseif 201 == 序号 then
     

        -- if 物品 or 及时刷新 then
        --     _tp["道具列表"][指定ID] = 物品
        --   end
        
       
    elseif 序号==112 then --打开藏宝阁
        if  __UI界面.窗口层.多开创建.是否可见 then
             __UI界面.窗口层.多开创建:置可见(false)
        end
    
    elseif 序号 == 113 then
        __多开操作 = true
        if 内容~=nil and 内容.选择角色~= nil then
            __连接信息.选中玩家id = tonumber(内容.选择角色)
        end

    elseif 序号 == 114 then
        __多开操作 = false
    elseif 序号 == 115 then
        内容.角色 = tonumber(内容.角色)
        _tp.多角色[内容.角色]=内容.玩家数据

    elseif 序号 == 116 then
         内容.角色 = tonumber(内容.角色)
         _tp.多角色[内容.角色].宝宝列表=内容.召唤兽

    elseif 序号 == 117 then
        -- tp.窗口.角色查看:打开(内容)
    elseif 序号 == 118 then
          if 内容~=nil and 内容.角色~=nil then
             内容.角色 = tonumber(内容.角色)
             if _tp.多角色[内容.角色]~=nil then
                _tp.多角色[内容.角色] =nil
             end
          end
    elseif 序号 == 119 then
         __UI界面.窗口层.大地图:打开(true)
        if  __UI界面.窗口层.超级传送.是否可见 then
            __UI界面.窗口层.超级传送:置可见(false)
        end
    elseif 序号 == 120 then
        __UI界面.窗口层.超级传送:打开()
        if  __UI界面.窗口层.大地图.是否可见 then
            __UI界面.窗口层.大地图:打开()
            __UI界面.窗口层.大地图:置可见(true)
        end
    
    elseif 序号==124 then
          -- tp.窗口.孩子技能学习:打开(内容)
          __UI界面.窗口层.门派转换:打开(内容.模式)
    elseif 序号==125 then
           -- tp.窗口.孩子技能学习:刷新数据(内容)
          --剑会天下
    elseif 序号==126 then --打开界面
        __UI界面.窗口层.剑会天下:打开()
        __UI界面.窗口层.剑会天下:加载数据(内容)
    elseif 序号==127 then --打开匹配界面
        if __UI界面.窗口层.剑会天下.是否可见 then
            __UI界面.窗口层.剑会天下:置可见(false)
        end

        __UI界面.窗口层.剑会匹配:打开(内容)
    elseif 序号==128 then --关闭匹配界面
        if  __UI界面.窗口层.剑会天下.是否可见 then
            __UI界面.窗口层.剑会天下:置可见(false)
        end
        if __UI界面.窗口层.剑会匹配.是否可见 then
            __UI界面.窗口层.剑会匹配:置可见(false)
        end
    elseif 序号==129 then --刷新面板积分数据
            if __UI界面.窗口层.剑会天下.是否可见 then
                __UI界面.窗口层.剑会天下:加载数据(内容)
            else
                __UI界面.窗口层.剑会天下:打开()
                __UI界面.窗口层.剑会天下.分类="模式"
                __UI界面.窗口层.剑会天下:加载数据(内容)
            end
    elseif 序号==130 then
            __UI界面.窗口层.购买召唤兽:打开(内容,"野怪")
    elseif 序号==130.1 then
            __UI界面.窗口层.购买召唤兽:打开(内容,"变异")
    elseif 序号==131 then
            __UI界面.窗口层.购买召唤兽:刷新(内容,"野怪")
    elseif 序号==131.1 then
            __UI界面.窗口层.购买召唤兽:刷新(内容,"变异")
  



    elseif 序号==132 then
        __UI弹出.组合输入框:打开(内容.操作,{内容.说明,nil,内容.回调[1],内容.回调[2],内容.回调[3],内容.回调[4],内容.回调[5]})

        -- elseif 216 == 序号 then
        --     __UI界面.窗口层.存款["打开"](__UI界面.窗口层.存款, 内容)
        -- elseif 217 == 序号 then
        --     __UI界面.窗口层.存款["重置"](__UI界面.窗口层.存款, 内容)
        --     角色信息.银子 = 内容["银子"]
        --     角色信息.存银 = 内容["存银"] 



    elseif 序号 == 133 then
            if __UI界面.窗口层.抽奖.是否可见 then
               __UI界面.窗口层.抽奖:刷新(内容)
            else
              __UI界面.窗口层.抽奖:打开(内容)
            end
        -- elseif 235 == 序号 then
        --     __UI界面.窗口层["宝箱抽奖"]["打开"](__UI界面.窗口层["宝箱抽奖"], 内容)
    elseif 序号==134 then
    
         if __UI界面.窗口层.世界boss.是否可见 then
             __UI界面.窗口层.世界boss:刷新(内容)
         else
          __UI界面.窗口层.世界boss:打开(内容)
          end
        
    elseif 序号==135 then
            -- if 内容.隐藏 then
            --    tp.窗口.战斗排行框.隐藏=false
            -- end
            -- if tp.窗口.战斗排行框.隐藏 == false then
            --    tp.窗口.战斗排行框:刷新数据(内容.数据)
            -- end
        
        
    elseif 序号==136 then
        --     tp.金钱=内容.银子
        --     if tp.窗口.幻化界面.可视==false then
        --       tp.窗口.幻化界面:打开(内容.道具)
        --     else
        --       tp.窗口.幻化界面:刷新道具(内容.道具)
        --     end
        -- elseif 239 == 序号 then
        --     __UI界面.窗口层["幻化"]["打开"](__UI界面.窗口层["幻化"])



    elseif 序号==137 then
        __UI弹出.组合输入框:打开("帮费设置",{"帮主你好，请输入贵帮需要设置的帮费！"})
    elseif 序号==142 then
        __UI弹出.组合输入框:打开("改名",{"为你的角色取一个好听的名字吧，费用5000仙玉。"})
    elseif 序号==142.1 then
        __UI弹出.组合输入框:打开("输入安全码",{"请输入你的安全码，如果没有设置安全码请先设置安全码。"})
        
    elseif 序号==143 then    ---注意这个编号  211   212是打开文件  211是服务端转发到客户端
            --   if not tp.窗口.钓鱼.可视 then
            --       tp.窗口.钓鱼:打开()
            --     end
    elseif 序号==143.1 then
            --   if tp.窗口.钓鱼.可视 then
            --       tp.窗口.钓鱼:打开()
            --     end
        
        
    elseif 序号==144  then ---------内置GM工具玩家信息
            if __UI界面.窗口层.功德录.是否可见 then
                __UI界面.窗口层.功德录:刷新(内容)
            else
                __UI界面.窗口层.功德录:打开(内容)
            end
        
        
    elseif 序号==145 then ---------内置GM工具打开gm工具界面
             if __UI界面.窗口层.靓号界面.是否可见 then
               __UI界面.窗口层.靓号界面:刷新(内容.编号)
             else
                __UI界面.窗口层.靓号界面:打开(内容.编号)
             end
        
    elseif 序号==146 then
            -- tp.窗口.好友观察:打开(内容)






    elseif 序号 == 147 then
            if __UI界面.窗口层.嘉年华.是否可见 then
               __UI界面.窗口层.嘉年华:刷新(内容)
            else
              __UI界面.窗口层.嘉年华:打开(内容)
            end
    elseif 序号==149 then
            if  __UI界面.窗口层.成就提示.是否可见 then
                __UI界面.窗口层.成就提示:刷新(内容)
            else
                __UI界面.窗口层.成就提示:打开(内容)
            end
        
    elseif 序号==150 then
            角色信息.接受给予 = 内容.接受给予
            if 角色信息.接受给予=="1" then  角色信息.接受给予=false end
   
    elseif 序号==151 then
            __UI界面.窗口层.召唤合宠:打开(内容.道具)
    elseif 序号==152 then
            _tp.道具列表=内容.道具
            __UI界面.窗口层.打书内丹:打开(内容.道具,"打书")
    elseif 序号==153 then
            _tp.道具列表=内容.道具
            __UI界面.窗口层.打书内丹:打开(内容.道具,"内丹")
    elseif 序号 == 155 then
            --  if tp.窗口.师门选择.可视 then
            --      tp.窗口.师门选择:刷新(内容)
            --      else
            --      tp.窗口.师门选择:打开(内容)
            --      end
          
            __UI界面.窗口层.门派选择:打开()
      
    elseif 序号==156 then
       if __UI界面.窗口层.长安保卫战.是否可见 then
            __UI界面.窗口层.长安保卫战:刷新(内容)
        else 
           __UI界面.窗口层.长安保卫战:打开(内容)
        end 
    elseif 序号  == 154 then
            -- if tp.窗口.彩虹争霸赛.可视 == false then
            --      tp.窗口.彩虹争霸赛:打开()
            --   end
            --   tp.窗口.彩虹争霸赛:数据处理(内容)
    elseif 序号  == 157  then
           __UI界面.窗口层.仙缘商店:打开(内容.商品,内容.名称,内容.仙缘积分)
        elseif 序号  == 157.1  then
        __UI界面.窗口层.文韵商店:打开(内容.商品,内容.名称,内容.文韵积分) 
    elseif 序号  == 158  then
                __连接信息.服务器.ip = 内容.ip
                __连接信息.服务器.端口= 内容.端口
                __连接信息.名称=内容.名称
                __连接信息.数字id = 角色信息.数字id
                __连接信息.角色名称 = 角色信息.名称
                __连接信息.跨服模式=true

    elseif 序号  == 160 then
                if 内容.道具 then
                  _tp.道具列表=内容.道具.道具
                end
                if 内容.体力 then
                    角色信息.体力=内容.体力
                end
                if __UI界面.窗口层.制作仙露.是否可见 then
                  __UI界面.窗口层.制作仙露:刷新()
                else
                  __UI界面.窗口层.制作仙露:打开()
                end
    elseif 序号  == 161 then
            
                 if __UI界面.窗口层.超级兽决.是否可见 then
                  __UI界面.窗口层.超级兽决:刷新(内容.技能)
                   
                 else
                  __UI界面.窗口层.超级兽决:打开(内容.技能)
                 end

    

    elseif 序号==162 then --3523
               __UI界面.窗口层.共享仓库:打开(内容.道具.道具,内容.仓库总数,内容.召唤兽总数)
               if __UI界面.窗口层.仓库.是否可见 then
                    __UI界面.窗口层.仓库:置可见(false)
                end
    elseif 序号==163 then --3524
              if __UI界面.窗口层.共享仓库.是否可见 then
                  if __UI界面.窗口层.共享仓库.类型=="道具" then
                      __UI界面.窗口层.共享仓库:刷新仓库(内容.道具.道具,内容.页数)
                  elseif __UI界面.窗口层.共享仓库.类型=="召唤兽" then
                          角色信息.宝宝列表 = 内容.宝宝列表
                          __UI界面.窗口层.共享仓库:刷新仓库(内容.召唤兽仓库数据,内容.页数)
                          刷新宝宝窗口()
                  end
              end
              if __UI界面.窗口层.仓库.是否可见 then
                  __UI界面.窗口层.仓库:置可见(false)
              end
    elseif 序号==164 then --3525
                if __UI界面.窗口层.共享仓库.是否可见 then
                    __UI界面.窗口层.共享仓库:道具刷新()
                end
                 if __UI界面.窗口层.仓库.是否可见 then
                    __UI界面.窗口层.仓库:置可见(false)
                end
    elseif 序号==165 then --3526
            if __UI界面.窗口层.共享仓库.是否可见 then
                角色信息.宝宝列表=内容.宝宝列表
                __UI界面.窗口层.共享仓库.类型="召唤兽"
                __UI界面.窗口层.共享仓库.宝宝总页=内容.召唤兽仓库总数
                __UI界面.窗口层.共享仓库:刷新仓库(内容.召唤兽仓库数据,1)
                刷新宝宝窗口()
            end
            if __UI界面.窗口层.仓库.是否可见 then
                __UI界面.窗口层.仓库:置可见(false)
            end
            self:道具刷新()
            
    
    elseif 序号==166 then --3540
            if __UI界面.窗口层.共享仓库.是否可见 then
                __UI界面.窗口层.共享仓库.类型="道具"
                __UI界面.窗口层.共享仓库.总页=内容.总数
                __UI界面.窗口层.共享仓库:刷新仓库(内容.道具.道具,1)
            end
            if __UI界面.窗口层.仓库.是否可见 then
                __UI界面.窗口层.仓库:置可见(false)
            end

    elseif 序号==167 then
            if __UI界面.窗口层.共享仓库.是否可见 then
                if __UI界面.窗口层.共享仓库.页数==内容.页数 and  __UI界面.窗口层.共享仓库.类型==内容.类型 then
                      if 内容.类型=="道具" and 内容.道具.道具 then
                          __UI界面.窗口层.共享仓库:刷新仓库(内容.道具.道具,内容.页数)
                      elseif 内容.类型=="召唤兽" and  内容.宝宝列表 then
                          角色信息.宝宝列表  = 内容.宝宝列表
                          __UI界面.窗口层.共享仓库:刷新仓库(内容.召唤兽仓库数据,内容.页数)
                          刷新宝宝窗口()
                      end
                end
            end
            if __UI界面.窗口层.仓库.是否可见 then
                __UI界面.窗口层.仓库:置可见(false)
            end

   
   
 
    elseif 序号==997 then
            引擎:关闭()
    elseif 序号==999 then
            引擎:消息框("下线通知",内容)
            引擎:关闭()
    elseif 序号==998 then
            引擎:消息框("下线通知",内容)
            引擎:关闭()

   
    end
end
function CLT:地图处理(序号, 内容)
    if 1001 == 序号 then
        if __主显.主角 then
            __主显.主角:设置路径(内容)
        end
    elseif 1003 == 序号 then
        if 内容.NPC then
           -- __主显:加载场景NPC(内容.NPC)

            __主显:设置假人(内容.NPC)
        end
        if 内容.传送 then
            __主显:清除场景特效()
            __主显:清除场景传送()
           -- __主显:加载场景传送(内容.传送)

            __主显:设置传送(内容.传送)
        end

  
      elseif  序号==1003 then
          if 内容.NPC then
              tp.场景:设置假人(内容.NPC)
          end
          if 内容.传送 then
              if tp.场景.人物 then
                  tp.场景.人物:停止移动(1)
              end
              tp.场景:设置传送(内容.传送)
          end


    elseif 1004 == 序号 then
        if 内容.NPC and not __NPC列表[内容.地图] then
            __NPC列表[内容.地图] = 内容.NPC
        end
        if 内容.传送 and not __传送数据[内容.地图] then
            __传送数据[内容.地图] = 内容.传送
        end
    
  
    elseif 1005 == 序号 then
        角色信息.地图数据.编号 = 内容[1]
        角色信息.坐标:pack(内容[2] * 20, 内容[3] * 20)
        __主显:加载(角色信息.地图数据.编号)
        __主显.主角:置坐标(角色信息.坐标)
        __UI界面.界面层.玩家界面.时辰控件:重置()
        
        if __跨地图寻径开关 and __跨地图寻径[1]~=nil then
            if __跨地图寻径[1].当前地图+0 == 角色信息.地图数据.编号+0 then
                local 内容={x=__跨地图寻径[1].x+0,y=__跨地图寻径[1].y+0,距离=0}
                请求服务(1001,生成XY(__跨地图寻径[1].x+0,__跨地图寻径[1].y+0))
                __主显.主角:设置路径(内容,true)
                table.remove(__跨地图寻径,1)
                if #__跨地图寻径==0 then
                    __跨地图寻径开关 =false
                    __跨地图寻径={}
                end
            else
                __跨地图寻径开关 = false
                __跨地图寻径 = {}
            end
        end
      
    elseif 1006 == 序号 then
        __主显:添加玩家(内容)
    elseif 1007 == 序号 then
        if  __主显.玩家[内容.id]==nil then
          return
        end
          __主显:删除玩家(内容.id)
   

    elseif 1008 == 序号 then

        if 内容~=nil and 内容.路径~=nil  and  __主显.玩家[内容.数字id] then
          __主显.玩家[内容.数字id]:设置路径(内容.路径)
      end

    elseif 1009 == 序号 then
        if __主显.玩家[内容.id] then
            if not __主显.玩家[内容.id].装备 then  __主显.玩家[内容.id].装备={} end
            __主显.玩家[内容.id].装备[3] = 内容.武器
            __主显.玩家[内容.id]:置模型()
        end
    elseif 1009.1 == 序号 then
        if __主显.玩家[内容.id] then
            if not __主显.玩家[内容.id].装备 then  __主显.玩家[内容.id].装备={} end
            __主显.玩家[内容.id].装备[4] = 内容.武器
            __主显.玩家[内容.id]:置模型()
        end

    elseif 1010 == 序号 then
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id]:加入动画(内容.类型)
        end
    elseif  序号==1010.1 then
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id].飞行=内容.飞行
        end
    elseif 1011 == 序号 then
        if __主显.主角 then
            __主显.主角:置坐标(内容.x, 内容.y)
        end
    elseif 1012 == 序号 then
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id]:置坐标(内容.x, 内容.y)
        end

    elseif 1013 == 序号 then
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id].染色方案=内容.染色方案
            __主显.玩家[内容.id].染色组=内容.染色组
            __主显.玩家[内容.id]:置模型()
        end
    elseif  序号==1014 then
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id].锦衣 = 内容.锦衣
            __主显.玩家[内容.id]:置模型()
        end


    elseif 1015 == 序号 then
        __主显:添加单位(内容)
    elseif  序号==1015.1 then
          __主显:更改单位(内容)
         
    elseif 1016 == 序号 then
        __主显:删除单位(内容.编号,内容.序列)

    elseif 1017 == 序号 then
        __主显.主角:添加喊话(内容.文本,内容.特效)
    elseif 序号==1018  then
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id]:添加喊话(内容.文本,内容.特效)
        end
   
    elseif 1019 == 序号 then
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id].坐骑 = 内容.坐骑
            __主显.玩家[内容.id]:置模型()
        end
    elseif 1020 == 序号 then
        if 内容.当前称谓 == nil then
            内容.当前称谓= ""
        end
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id].当前称谓=内容.当前称谓
            __主显.玩家[内容.id]:置称谓(内容.当前称谓)
        end
    elseif 1021 == 序号 then
        if 内容==nil then
            return
        end
        for i=1,#内容 do
          __主显:添加单位(内容[i])
        end
    elseif 1022 == 序号 then
        if 内容==nil then
          return
        end
        for i=1,#内容 do
            __主显:添加玩家(内容[i])-- tp.场景:添加玩家(内容)
        end
    elseif 序号==1023 then
          __主显.玩家[内容.id].pk开关 = 内容.开关
    elseif 序号==1024 then
          __主显.玩家[内容.id].变身数据=内容.变身
          __主显.玩家[内容.id].变异=内容.变异
          __主显.玩家[内容.id]:置模型()

    elseif 序号==1025 then
          __主显.玩家[内容.id].强p开关 = 内容.开关
    elseif 序号==1026 then
        _tp.房屋数据 = 内容[1]
    elseif  序号==1027 then
        __主显:房屋特效(内容[1])
      
    elseif  序号==1028 then
       _tp.如意符 = true
       __UI界面.鼠标层:法术形状()
    elseif 序号==1029 then
      if 内容[2] == "庭院" then
        _tp.房屋数据.庭院装饰 = 内容[1]
      elseif 内容[2] == "室内" then
        _tp.房屋数据.室内装饰 = 内容[1]
      elseif 内容[2] == "阁楼" then
        _tp.房屋数据.阁楼装饰 = 内容[1]
      elseif 内容[2] == "牧场" then
        _tp.房屋数据.牧场装饰 = 内容[1]
      end
        __主显:清除房屋特效()
        __主显:加载房屋特效()
    elseif 序号==1030 then
      if 内容[2] == "庭院" then
        _tp.房屋数据.庭院装饰 = 内容[1]
      elseif 内容[2] == "室内" then
        _tp.房屋数据.室内装饰 = 内容[1]
       elseif 内容[2] == "阁楼" then
        _tp.房屋数据.阁楼装饰 = 内容[1]
      elseif 内容[2] == "牧场" then
        _tp.房屋数据.牧场装饰 = 内容[1]
      end
      __主显:清除房屋特效()
      __主显:加载房屋特效()
    elseif 序号==1031 then
     -- tp.窗口.房屋拜访:打开()
 

    elseif 序号==1032 then
          if __主显.玩家[内容.id]==nil then
            return
          end
          __主显.玩家[内容.id].离线摆摊=true
          __主显.玩家[内容.id]:置模型()
    elseif 序号==1033 then
          if __主显.玩家[内容.id]==nil then
            return
          end
          __主显.玩家[内容.id].离线摆摊=nil
          __主显.玩家[内容.id]:置模型()



    end
end
function CLT:对话处理(序号, 内容)
    if not 内容 or not 内容.对话 then
        return 
    end
    if 1501 == 序号 then
     
        __UI界面.窗口层.对话栏:打开(内容.模型,内容.名称,内容.对话,内容.选项,nil,内容.下一页)
    elseif 序号==1502 then
        __UI界面.窗口层.对话栏:打开(内容.模型,内容.名称,内容.对话,内容.选项,nil,nil,true)
    end
end


function CLT:道具刷新()
      if  __res.配置.行囊==1 and __UI界面.窗口层.道具行囊.是否可见 then 
          __UI界面.窗口层.道具行囊:重置窗口()
      elseif  __UI界面.窗口层.新行囊.是否可见 then 
                __UI界面.窗口层.新行囊:重置窗口()
      end

      if __UI界面.窗口层.物品加锁.是否可见 then
          __UI界面.窗口层.物品加锁:道具刷新()
      end
      if __UI界面.窗口层.给予.是否可见 then
          __UI界面.窗口层.给予:道具刷新()
      end
      if __UI界面.窗口层.打造.是否可见 then
          __UI界面.窗口层.打造:刷新材料(_tp.道具列表)
      end
      if __UI界面.窗口层.符石合成.是否可见 then
        __UI界面.窗口层.符石合成:刷新材料(_tp.道具列表)
    end
      if __UI界面.窗口层.打书内丹.是否可见 then
          __UI界面.窗口层.打书内丹:刷新道具(_tp.道具列表)
      end
      if __UI界面.窗口层.道具鉴定.是否可见 then
          __UI界面.窗口层.道具鉴定:道具刷新(_tp.道具列表)
      end
      if __UI界面.窗口层.道具附魔.是否可见 then
          __UI界面.窗口层.道具附魔:道具刷新(_tp.道具列表)
      end
      if __UI界面.窗口层.摊位出售.是否可见 then
          __UI界面.窗口层.摊位出售:道具刷新()
      end
      if __UI弹出.道具选择.是否可见 then
          __UI弹出.道具选择:道具刷新(_tp.道具列表)
      end
      


      
end


function CLT:刷新道具(道具)
      if __UI界面.窗口层.给予.是否可见 and not 道具 then
            请求服务(3760)
            道具=true
      end
      if __UI界面.窗口层.打造.是否可见 and not 道具 then
            请求服务(3760)
            道具=true
      end
      if __UI界面.窗口层.符石合成.是否可见 and not 道具 then
            请求服务(3760)
            道具=true
      end
      if __UI界面.窗口层.打书内丹.是否可见 and not 道具 then
          请求服务(3760)
          道具=true
      end
      if __UI界面.窗口层.道具鉴定.是否可见 and not 道具 then
            请求服务(3760)
            道具=true
      end
      if __UI界面.窗口层.道具附魔.是否可见 and not 道具 then
            请求服务(3760)
            道具=true
      end
      if __UI界面.窗口层.摊位出售.是否可见 and not 道具 then
          请求服务(3760)
          道具=true
      end
      if __UI弹出.道具选择.是否可见 and not 道具 then
            请求服务(3760)
            道具=true
      end
      
    --   if tp.窗口.符石合成.可视 then
    --       请求服务(3805)
    --   end
      if __UI界面.窗口层.附魔宝珠.是否可见 then
          请求服务(3767)
      end


end

function CLT:道具处理(序号, 内容)

    --if tp.窗口.物品加锁.可视 then tp.窗口.物品加锁:刷新() end

    if 序号==3501 then
            --tp.窗口.对话栏:文本(内容.模型,内容.名称,内容.对话,内容.选项)
            角色信息.银子=内容.银子
            角色信息.储备=内容.储备
            角色信息.存银=内容.存银
            _tp.总格=内容.总格
            _tp.道具列表=内容.道具
            if __UI界面.窗口层.符石镶嵌.是否可见 then 
                for i, v in ipairs(__UI界面.窗口层.符石镶嵌.符石网格.子控件) do
                    if v._spr and v._spr.物品 and v._spr.物品.原始编号 then
                        _tp.道具列表[v._spr.物品.原始编号]=nil
                    end
                end
                for i, v in ipairs(__UI界面.窗口层.符石镶嵌.星石网格.子控件) do
                    if v._spr and v._spr.物品 and v._spr.物品.原始编号 then
                        _tp.道具列表[v._spr.物品.原始编号]=nil
                    end
                end
            end
            if not __UI界面.窗口层.物品加锁.是否可见 then
                if __res.配置.行囊==1  then
                    if  not __UI界面.窗口层.道具行囊.是否可见 then
                        __UI界面.窗口层.道具行囊:打开()
                    end
                else
                    if  not __UI界面.窗口层.新行囊.是否可见 then
                        __UI界面.窗口层.新行囊:打开()
                    end
                end
            end
            self:道具刷新()
           
         
    elseif 序号==3502 then
        --tp.窗口.对话栏:文本(内容.模型,内容.名称,内容.对话,内容.选项)
        角色信息.银子=内容.银子
        角色信息.储备=内容.储备
        角色信息.存银=内容.存银
        _tp.行囊列表=内容.道具
        self:道具刷新()

    elseif 序号==3503 then --更新装备
        角色信息.装备 = 内容
        if __res.配置.行囊==1 and  __UI界面.窗口层.道具行囊.是否可见 then
            __UI界面.窗口层.道具行囊:模型重置()
        end
        self:道具刷新()
    elseif 序号==3504 or 序号==3505  then --佩戴装备
        __主显.主角.装备 = 角色信息.装备
        __主显.主角:置模型()
        if __res.配置.行囊==1 and  __UI界面.窗口层.道具行囊.是否可见 then
            __UI界面.窗口层.道具行囊:模型重置()
        end
        self:道具刷新()


    elseif 序号==3506  then
        角色信息.灵饰 = 内容
        if __res.配置.行囊==1 and __UI界面.窗口层.道具行囊.是否可见 then
            __UI界面.窗口层.道具行囊.分类="灵饰"
            __UI界面.窗口层.道具行囊:模型重置()
        elseif __UI界面.窗口层.灵饰.是否可见 then
          __UI界面.窗口层.灵饰:刷新()
        end
        self:道具刷新()
    elseif 3507 == 序号 then
        角色信息.银子=内容.道具.银子
        角色信息.储备=内容.道具.储备
        角色信息.存银=内容.道具.存银
        _tp.道具列表=内容.道具.道具
        __UI界面.窗口层.给予:打开(内容.名称, 内容.类型, 内容.等级)
        self:道具刷新()
    elseif 序号==3508 then
        __UI界面.窗口层.交易:设置我方数据(内容)
   
    elseif 序号==3509 then
        --__UI界面.窗口层.交易.对方确定:置选中(true)
           -- tp.窗口.交易.选择1:置打勾框(true)

    elseif 序号==3510 then
        __UI界面.窗口层.交易:设置对方数据(内容)
   


    elseif  序号 == 3511 then
        __UI界面.窗口层.交易:关闭界面(1)
    elseif 序号 == 3512 then
        角色信息.宝宝列表=内容
        if __UI界面.窗口层.交易.是否可见 and __UI界面.窗口层.交易.类型=="召唤兽" then
            __UI界面.窗口层.交易.宝宝={}
            __UI界面.窗口层.交易.名称选择:置数据()
        end
        if __UI界面.窗口层.物品加锁.是否可见 then
            __UI界面.窗口层.物品加锁:道具刷新()
        end
        刷新宝宝窗口()


    elseif 序号 == 3513 then
        if 内容.数据类型~=nil and 内容.数据类型=="行囊" then
            _tp.行囊列表=内容.道具
        else
            _tp.道具列表=内容.道具
        end
        self:道具刷新()
    elseif 序号 == 3514 then
        __UI界面.窗口层.交易:打开(内容.名称,内容.等级)


    elseif 序号 == 3515 then
        __UI界面.窗口层.摊位出售:打开(内容.名称,内容.摊主名称,内容.id,内容.物品,内容.bb,内容.打造)
        __主显.主角:置摆摊(内容.名称)
    elseif 序号 == 3516 then
        __主显.主角:置摆摊(内容)
    elseif 序号 == 3517 then
        __UI界面.窗口层.摊位出售:刷新(内容.名称,内容.摊主名称,内容.id,内容.物品,内容.bb,内容.打造)
    elseif 序号 == 3518 then
        __主显.主角:置摆摊(nil)
        __UI界面.窗口层.摊位出售.关闭:左键弹起()

    elseif 序号==3519  then
            if __主显.玩家[内容.id] then
                __主显.玩家[内容.id]:置摆摊(内容.名称)
            end

    elseif 序号 == 3520 then
        角色信息.银子 = 内容 + 0
    elseif 序号 ==  3521 then
        __UI界面.窗口层.摊位购买:打开(内容.名称,内容.摊主名称,内容.id,内容.物品,内容.bb,内容.打造,内容.熟练度)
    elseif 序号 == 3522 then
        __UI界面.窗口层.摊位购买:刷新(内容.名称,内容.摊主名称,内容.id,内容.物品,内容.bb,内容.打造,内容.熟练度)
    elseif  序号== 3523 then
        __UI界面.窗口层.仓库:打开( 内容.道具.道具, 内容.仓库总数,内容.召唤兽总数)
        if __UI界面.窗口层.共享仓库.是否可见 then 
                __UI界面.窗口层.共享仓库:置可见(false)
        end
    elseif 序号 == 3524 then
        if __UI界面.窗口层.仓库.类型=="道具" then
            __UI界面.窗口层.仓库:刷新仓库(内容.道具.道具,内容.页数)
          elseif __UI界面.窗口层.仓库.类型=="召唤兽" then
                角色信息.宝宝列表 = 内容.宝宝列表
                __UI界面.窗口层.仓库:刷新仓库(内容.召唤兽仓库数据,内容.页数)
                刷新宝宝窗口()
          end
    elseif  序号== 3525 then
         __UI界面.窗口层.仓库:道具刷新()
        if __UI界面.窗口层.共享仓库.是否可见 then 
            __UI界面.窗口层.共享仓库:置可见(false)
        end

    elseif 序号 == 3526 then
        角色信息.宝宝列表=内容.召唤兽
        __UI界面.窗口层.仓库.类型="召唤兽"
        __UI界面.窗口层.仓库.宝宝总页=内容.召唤兽仓库总数
        __UI界面.窗口层.仓库:刷新仓库(内容.召唤兽仓库数据,1)

        刷新宝宝窗口()
        if __UI界面.窗口层.共享仓库.是否可见 then 
            __UI界面.窗口层.共享仓库:置可见(false)
        end
        self:道具刷新()


    elseif 序号 == 3527 then
          角色信息.法宝 = 内容.法宝
          角色信息.灵宝 = 内容.灵宝
          角色信息.法宝佩戴 = 内容.佩戴
          角色信息.灵宝佩戴 = 内容.灵宝佩戴
          角色信息.神器佩戴 = 内容.神器佩戴
          角色信息.神器数据 = 内容.神器数据
          if __UI界面.窗口层.法宝.是否可见 then
              __UI界面.窗口层.法宝:按钮重置()
          elseif not __UI界面.窗口层.物品加锁.是否可见 then
              __UI界面.窗口层.法宝:打开()
          end
          self:道具刷新()
    
    elseif 序号==3528 then  
        if __UI界面.窗口层.法宝.是否可见 then
              __UI界面.窗口层.法宝:更新修炼(内容)
        end        

      

    elseif 序号 == 3529 then
        __UI界面.窗口层.合成旗:打开(内容)
    elseif 序号 == 3530 then
        角色信息.锦衣=内容
        __主显.主角.锦衣 = 角色信息.锦衣
        __主显.主角:置模型()
        if __res.配置.行囊==1 and __UI界面.窗口层.道具行囊.是否可见 then
            __UI界面.窗口层.道具行囊.分类="锦衣"
            __UI界面.窗口层.道具行囊:模型重置() 
        elseif __UI界面.窗口层.锦衣.是否可见 then
            __UI界面.窗口层.锦衣:刷新()
        end
        self:道具刷新()
        
  



        -- if 新道具行囊 then
        --        if  ——tp.窗口.新道具行囊.可视 then
        --          tp.窗口.新道具行囊:刷新锦衣()
        --          tp.窗口.新道具行囊.窗口="主人公"
        --          tp.窗口.新道具行囊.主人公分类="锦衣"
        --        end
        --    else
        --          if tp.窗口.锦衣.可视 then
        --          tp.窗口.锦衣:刷新()
        --        end
        --    end
        
    elseif 序号 == 3531 then
          if __res.配置.行囊==1 and  __UI界面.窗口层.道具行囊.是否可见 then---坐骑饰品
              __UI界面.窗口层.道具行囊:重置窗口()
          elseif  __UI界面.窗口层.新行囊.是否可见 then---坐骑饰品
                __UI界面.窗口层.道具行囊:重置窗口()
          end
    elseif 序号==3540 then
        __UI界面.窗口层.仓库.类型="道具"
        __UI界面.窗口层.仓库.总页=内容.总数
        __UI界面.窗口层.仓库:刷新仓库(内容.道具.道具,1)
        if __UI界面.窗口层.共享仓库.是否可见 then
            __UI界面.窗口层.共享仓库:置可见(false)
        end


    elseif 序号==3548 then
        if __UI界面.窗口层.附魔宝珠.是否可见 then
            __UI界面.窗口层.附魔宝珠:刷新(内容)
        else
            __UI界面.窗口层.附魔宝珠:打开(内容)
        end


      

    elseif 序号==3549 then
        if __UI界面.窗口层.附魔宝珠.是否可见 then
            __UI界面.窗口层.附魔宝珠:置可见(false)
        end





       

    elseif 序号==3550 then
        if __UI界面.窗口层.符石镶嵌.是否可见 then    ----------------------符石没写 
            __UI界面.窗口层.符石镶嵌:刷新符石(内容.装备,内容.星石操作)
        else
            __UI界面.窗口层.符石镶嵌:打开(内容.装备)
        end


     

       -- ---------------------------




        -- if __UI界面.窗口层.符石["是否可见"] then
        --     __UI界面.窗口层.符石["镶嵌完成"](__UI界面.窗口层.符石, 内容)
        -- end
 
    elseif 序号==3552 then
        -- tp.金钱=内容.道具.银子
        -- if tp.窗口.符石合成.可视 then
        --   tp.窗口.符石合成:刷新(内容.道具.道具)
        -- else
        --   tp.窗口.符石合成:打开(内容.道具.道具)
        -- end

           --   if __UI界面.窗口层["装备点化"]["是否可见"] then
        --     __UI界面.窗口层["装备点化"]["重置"](__UI界面.窗口层["装备点化"])
        --   end





    elseif 3699 == 序号 then
        local 背包刷新= false
        __UI界面.窗口层.道具行囊:重置抓取()
        __UI界面.窗口层.新行囊:重置抓取()
        if __res.配置.行囊==1 and __UI界面.窗口层.道具行囊.是否可见 then
              if  __UI界面.窗口层.道具行囊.包裹类型 =="道具" then
                    请求服务(3699)
                    背包刷新=true
              elseif  __UI界面.窗口层.道具行囊.包裹类型 == "行囊" then
                    请求服务(3700)
              end
              __UI界面.窗口层.道具行囊:重置窗口()
        elseif  __UI界面.窗口层.新行囊.是否可见 then
              if  __UI界面.窗口层.新行囊.包裹类型 =="道具" then
                    请求服务(3699)
                    背包刷新=true
              elseif  __UI界面.窗口层.新行囊.包裹类型 == "行囊" then
                    请求服务(3700)
              end
              __UI界面.窗口层.新行囊:重置窗口()
        end
        self:刷新道具(背包刷新)
        if __UI界面.窗口层.物品加锁.是否可见 then 
            __UI界面.窗口层.物品加锁:道具刷新()
        end



    -- elseif 3700 == 序号 then
    --     __UI界面.窗口层["商城"]["打开"](__UI界面.窗口层["商城"], 内容)
    elseif 序号 == 3700 then
   
     --tp.窗口.回收系统:刷新(内容)
     __UI界面.窗口层.回收系统:刷新(内容)

    --elseif 序号 == 3701  then
        -- if __UI界面.窗口层["商城"]["是否可见"] then
        --     __UI界面.窗口层["商城"]["仙玉"] = 内容
        --     __UI界面.窗口层["商城"]["重置"](__UI界面.窗口层["商城"])
        -- end



        
    elseif 3702 == 序号 then
        -- __UI界面.窗口层["商城"]["积分"] = 内容
        -- __UI界面.窗口层["商城"]["重置"](__UI界面.窗口层["商城"])

       

    elseif 序号 == 3703 then
         if 内容.类型 == 1 or 内容.类型 == 2 then
            __UI界面.窗口层.藏宝阁出售:打开(内容)
        else
             __UI界面.窗口层.藏宝阁出售:刷新(内容)
        end


    elseif 序号 == 3704 then
          __UI界面.窗口层.藏宝阁:打开(内容.数据,内容.点卡)
    elseif 序号 == 3705 then
          __UI界面.窗口层.藏宝阁:刷新(内容.数据,内容.类型,内容.点卡)
    elseif 序号==3706 then
            if __UI界面.窗口层.物品加锁.是否可见 then 
                __UI界面.窗口层.物品加锁:道具刷新()
            else
                __UI界面.窗口层.物品加锁:打开()
            end
    elseif 序号==3707 then
        __UI界面.窗口层.物品密码:打开()

    elseif 序号==3708 then
        __UI界面.窗口层.对话栏:打开(角色信息.模型,角色信息.名称,"全面解锁”可解除需频繁操作的锁定，如果每天从钱庄和家里取出来金钱积累不超过500万只需“部分解锁”。仅有涉及加锁道具转移等操作需要“全面解锁”。",{"部分解锁","全面解锁","取消"})
    elseif 序号==3711 then
        if 内容.类型 == 1 or 内容.类型 == 2 then
            __UI界面.窗口层.藏宝阁出售寄存:打开(内容)
        else
            __UI界面.窗口层.藏宝阁出售寄存:刷新(内容)
        end
    elseif 序号==3712 then
        if 内容.类型 == 1 or 内容.类型 == 2 then
            __UI界面.窗口层.藏宝阁购买寄存:打开(内容)
        else
            __UI界面.窗口层.藏宝阁购买寄存:刷新(内容)
        end
    elseif 序号==3713 then
        __UI界面.窗口层.藏宝阁上架货币:打开(内容.类型,内容.数量)
    elseif 序号==3714 then
    
        __UI界面.窗口层.藏宝阁:物品数据刷新(内容.道具,内容.编号+0)
    elseif 序号==3800 then
     
        __UI界面.窗口层.道具行囊:重置抓取()
        __UI界面.窗口层.新行囊:重置抓取()
    elseif 序号==3900 then
        __UI界面.窗口层.铃铛界面:打开(内容)
    elseif 序号==3901 then
        -- tp.窗口.铃铛界面:刷新(内容)
    elseif 序号==3902 then
    --  local 动画编号=内容.编号
    --  local 人物组,事件组,初始镜头坐标 = 引擎.取剧情动画(动画编号)
    --  if #人物组==0 or #事件组==0 then
    --    tp.常规提示:打开("#Y引擎动画缺失，播放失败")
    --  else
	--      tp.第二场景:载入显示(人物组,事件组,初始镜头坐标)
    --  end
    elseif 序号==3910 then
    --    tp.窗口.交易中心:打开(内容)
    elseif 序号==3911 then
    --  tp.窗口.交易中心:更新数据(内容)
    --剧情信息
    elseif 序号==3532 then
        -- local 人物组 = {{名称="唐太宗",模型="皇帝",X=40,Y=20,方向=0,编号=1},{名称="玄奘法师",模型="唐僧",X=47,Y=20,方向=0,编号=2},{主角=true,X=45,Y=22,方向=2,编号=3}}
        -- local 事件组 = {
        --   [1]={[[引擎.场景.第二场景:加入文本(50,角色信息.模型,"#G"..角色信息.名称.."#:恭喜你完成了副本")]]}}
        -- tp.第二场景:载入显示(人物组,事件组)
    elseif 序号==3532.1 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",1)
        -- tp.第二场景:载入显示(人物组,事件组)
    elseif 序号==3532.2 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",2)
        -- tp.第二场景:载入显示(人物组,事件组)
    elseif 序号==3532.3 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("桃园浣熊",1)
        -- tp.第二场景:载入显示(人物组,事件组)
    elseif 序号==3532.4 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",3)
        -- tp.第二场景:载入显示(人物组,事件组)
    elseif 序号==3532.5 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",4)
        -- tp.第二场景:载入显示(人物组,事件组)
    elseif 序号==3532.6 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",5)
        -- tp.第二场景:载入显示(人物组,事件组)
    elseif 序号==3532.7 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",6)
        -- tp.第二场景:载入显示(人物组,事件组)
    elseif 序号==3532.8 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",7)
        -- tp.第二场景:载入显示(人物组,事件组)
   elseif 序号==3532.9 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",8)
        -- tp.第二场景:载入显示(人物组,事件组)
   elseif 序号==3533.1 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",9)
        -- tp.第二场景:载入显示(人物组,事件组)
   elseif 序号==3533.2 then
        -- local 人物组,事件组 = tp.窗口.对话栏:获取任务事件("枯萎金莲",10)
        -- tp.第二场景:载入显示(人物组,事件组)








     end
end
function CLT:队伍处理(序号, 内容)
    if 4001 == 序号 then
        __UI界面.界面层:重置("组队")
        __UI界面.鼠标层:组队形状()
    elseif 4002 == 序号 then
        _tp.队伍数据=内容
        __UI界面.窗口层.队伍列表:打开()
    elseif 4004 == 序号 then
        _tp.队伍数据=内容
        __UI界面.界面层.玩家界面.按钮控件.组队.消息提醒 = nil
        __UI界面.窗口层.队伍列表:重置()
        __UI界面.界面层.队伍栏.队员网格:置头像(_tp.队伍数据)
    elseif 4006 == 序号 then
        __主显.主角:置队长(true)
    elseif 4007 == 序号 then
        if 内容.逻辑 == nil then
            内容.逻辑 = false
        end
        __主显.玩家[内容.id]:置队长(内容.逻辑)
    elseif 4008 == 序号 then
        __主显.主角:置队长(false)
        __UI界面.窗口层.队伍列表:置可见(false)
    elseif 4010 == 序号 then
        __UI界面.窗口层.请求列表:打开(内容)
    elseif 4011 == 序号 then
        __UI界面.界面层.玩家界面.按钮控件.组队.消息提醒= true
        __UI界面.窗口层.请求列表:刷新(内容)
    elseif 4012 == 序号 then
        _tp.队伍数据={}
        __UI界面.界面层.队伍栏.队员网格:置头像({})
        __UI界面.窗口层.队伍列表:置可见(false)
        __UI界面.窗口层.请求列表:置可见(false)

  

    elseif 4013 == 序号 then
        __UI界面.窗口层.阵型选择:打开(内容)

    elseif 4014 == 序号 then
        if 内容.逻辑==nil then
            内容.逻辑=false
          end
        if __主显.玩家[内容.id] then
            __主显.玩家[内容.id]:置战斗(内容.逻辑)
        end

    elseif 序号==4015 then
      __UI界面.窗口层.好友查看:打开(内容)
    elseif 序号==4018 then
        if  __UI界面.窗口层.邀请组队.是否可见 then
            __UI界面.窗口层.邀请组队:刷新(内容)
        else
            __UI界面.窗口层.邀请组队:打开(内容)
        end

    end
end
function CLT:战斗处理(序号, 内容)

    if 5501 == 序号 then
        _tp.战斗中 = true
        __战斗主控.观战开关= false
        if 内容.观战 ~= nil then
            _tp.观战中 = true
            __战斗主控.观战开关 = true
        end
        __主显.地图.显示表={}
        __战斗主控.队伍id= 内容.id
        __战斗主控.单位总数 = 内容.总数
        __收到流程 = tonumber(内容.收到)
        __发送流程 = tonumber(内容.发送)
        _tp:停止音乐()
        _tp:播放战斗音乐()
        __UI界面.界面层:进入战斗()
        __主显.主角:置战斗(true)
    elseif 5502 == 序号 then
        __战斗主控:加载单位(内容)
    elseif 5503 == 序号 then
        __战斗主控:设置命令回合(内容[1],内容[2]+0)
    elseif 序号==5503.1 then
        __战斗主控:设置重连命令回合(内容[1],内容[3],内容[4])
  
    elseif __收到流程 == 序号 then
        __UI界面.界面层.战斗界面:退出()
        __战斗主控:设置战斗流程(内容)
    elseif 5505 == 序号 then
        if _tp.战斗中 then
            _tp.战斗中 = false
            _tp.观战中 = false
            __战斗主控:释放()
            _tp:停止战斗音乐()
            _tp:恢复音乐()
            __战斗主控.战斗单位 = {}
            __战斗主控.多开数据 = nil
            __战斗主控.自动开关 =false
            __UI界面.界面层:退出战斗()
            __主显.主角:置战斗(false)
           -- test1()
           collectgarbage()
      end
    elseif 5506 == 序号 then
        if nil ~= 内容[1].气血 then
            角色信息.气血 = 内容[1].气血
        end
        if nil ~= 内容[1].最大气血 then
            角色信息.最大气血 = 内容[1].最大气血
        end
        if nil ~= 内容[1].气血上限 then
            角色信息.气血上限 = 内容[1].气血上限
        end
        角色信息.魔法 = 内容[1].魔法
        角色信息.最大魔法 = 内容[1].最大魔法
        角色信息.愤怒 = 内容[1].愤怒
        if nil ~= 内容[2] then
            角色信息.参战宝宝.魔法 = 内容[2].魔法
            角色信息.参战宝宝.最大魔法 = 内容[2].最大魔法
        end
        __UI界面.界面层.玩家界面:重置人物()
       __UI界面.界面层.玩家界面:重置召唤兽()
    elseif 5507 == 序号 then
        __战斗主控:取消状态(内容)
    elseif 序号==5508 then
        __战斗主控:鬼魂复活(内容)
    elseif 5509 == 序号 then
       if 内容.玩家id == 角色信息.数字id then
             _tp.战斗道具=内容.道具.道具
       else
            _tp.多角色[内容.玩家id].战斗道具=内容.道具.道具
       end
       __UI界面.界面层.战斗界面:设置道具(内容.玩家id,内容.无法使用)
    elseif 序号==5510 then
        if 内容.玩家id ==角色信息.数字id then
            角色信息.宝宝列表=内容.召唤兽.数据
            __UI界面.战斗层.战斗召唤:打开(角色信息.宝宝列表,内容.召唤兽.数量)
             刷新宝宝窗口()
        else
            _tp.多角色[内容.玩家id].宝宝列表 = 内容.召唤兽.数据
            __UI界面.战斗层.战斗召唤:打开(_tp.多角色[内容.玩家id].宝宝列表,内容.召唤兽.数量)
        end
     

    elseif 序号==5511 then  --开启自动战斗后进入等待
        __战斗主控.进程="等待"
    elseif 序号==5512 then
         local 编号=内容.id
         if __战斗主控.战斗单位[编号]==nil then return  end
         __战斗主控.战斗单位[编号]:添加喊话(内容.文本,内容.特效)

    elseif 5513 == 序号 then
        for k,v in pairs(内容) do
            if __战斗主控.战斗单位[v.id] then
                __战斗主控.战斗单位[v.id].数据.自动指令=v.自动
             end
        end
    elseif 5514 == 序号 then
        for k,v in pairs(内容) do
          if __战斗主控.战斗单位[v.id] then
              __战斗主控.战斗单位[v.id].数据.自动战斗=v.自动
              if  __战斗主控.战斗单位[v.id] and __战斗主控.战斗单位[v.id].类型 == "角色" and __战斗主控.战斗单位[v.id].数字id == 角色信息.数字id then
                  __战斗主控.自动开关 = v.自动
              end
          end
        end
        __战斗主控:设置自动()
       
    elseif 序号==5515 then
          _tp.观战中 = true
          __战斗主控:加载单位(内容)
    --     if 内容==nil then
    --       return
    --     end
    --     for n=1,#内容 do
    --         __战斗主控:加载单位(内容[n])
    --     end
    -- elseif 序号==5516 then
    --     if 内容==nil then
    --        return
    --     end
    --     _tp.观战中 = true
    --     __主显.主角:置战斗(true)
    --     for n=1,#内容 do
    --         __战斗主控:加载单位(内容[n])
    --     end
    elseif 5517 == 序号 then
    --    角色信息.参战宝宝.气血=内容.气血
    --    角色信息.参战宝宝.最大气血=内容.最大气血
    --    角色信息.参战宝宝.魔法=内容.魔法
    --    角色信息.参战宝宝.最大魔法=内容.最大魔法
    --    __UI界面.界面层.玩家界面:重置人物()
    --    __UI界面.界面层.玩家界面:重置召唤兽()
    elseif 5518 == 序号 then---
        __战斗主控:刷新技能(内容)
    elseif 序号==5519 or 序号==5519.1 then
         __战斗主控:状态同步(内容,序号)
    elseif 5520 == 序号 then
        __战斗主控:设置血量同步(内容)
    elseif 5521 == 序号 then
       __战斗主控:设置魔法愤怒同步(内容)
    elseif 序号==5522 then
        if 内容~=nil then
           for k,v in pairs(内容) do
            if __战斗主控.战斗单位[k]~=nil then
                __战斗主控.战斗单位[k].技能冷却 = 内容[k]
            end
           end
        end
    elseif 序号==5523 then   ------------------灵宝
          if 内容.玩家id ==角色信息.数字id then
              角色信息.灵宝佩戴=内容.灵宝佩戴
         else
            _tp.多角色[内容.玩家id].灵宝佩戴=内容.灵宝佩戴
          end
         __UI界面.界面层.战斗界面:设置灵宝(内容)
 



    end
end


-- function CLT:商会处理(序号, 内容)
--     if 6101 == 序号 then
--     elseif 6106 == 序号 then
--         __UI界面.窗口层["商会列表"]["打开"](__UI界面.窗口层["商会列表"], 内容)
--     elseif 6107 == 序号 then
--         __UI界面.窗口层["商会物品"]["打开"](__UI界面.窗口层["商会物品"], 内容)
--     elseif 6108 == 序号 then
--         __UI界面.窗口层["商会物品"]["数据"]["道具"] = 内容["道具"]
--         __UI界面.窗口层["商会物品"]["页数"] = 内容["当前页数"]
--         __UI界面.窗口层["商会物品"]["重置"](__UI界面.窗口层["商会物品"])
--         __UI界面.窗口层["商会物品"]["刷新"](__UI界面.窗口层["商会物品"])
--     elseif 6113 == 序号 then
--         __UI界面.窗口层["商会召唤兽"]["打开"](__UI界面.窗口层["商会召唤兽"],
--             内容)
--     elseif 6114 == 序号 then
--         __UI界面.窗口层["商会召唤兽"]["数据"]["宠物"] = 内容["宠物"]
--         __UI界面.窗口层["商会召唤兽"]["页数"] = 内容["页数"]
--         __UI界面.窗口层["商会召唤兽"]["重置"](__UI界面.窗口层["商会召唤兽"])
--         __UI界面.窗口层["商会召唤兽"]["刷新"](__UI界面.窗口层["商会召唤兽"])
--     elseif 6115 == 序号 then
--         table.remove(__UI界面.窗口层["商会召唤兽"]["数据"]["宠物"], 内容["编号"])
--         __UI界面.窗口层["商会召唤兽"]["重置"](__UI界面.窗口层["商会召唤兽"])
--         __UI界面.窗口层["商会召唤兽"]["刷新"](__UI界面.窗口层["商会召唤兽"])
--     elseif 6116 == 序号 then
--   
--     end
-- end




return CLT
