-- <AUTHOR> <PERSON><PERSON>LUA
-- @Last Modified by    : baidwwy
-- @Date                : 2024-09-04 16:18:56
-- @Last Modified time  : 2024-10-07 13:40:21

--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-30 05:21:12
Last Modified by: G<PERSON><PERSON><PERSON><PERSON>
Last Modified time: 2023-02-12 20:37:45
--]]
function 置窗口背景(name,x,y,w,h,lx)
    local nsf = require('SDL.图像')(w,h+y)
    if nsf:渲染开始() then
            __res:getPNGCC(1,0,0,30,30):显示(0,0+y)
            __res:getPNGCC(1,0,30,30,140):平铺(30,h-60):显示(0,30+y)
            __res:getPNGCC(1,0,170,30,30):显示(0,h-30+y)

            __res:getPNGCC(1,30,0,140,30):平铺(w-60,30):显示(30,0+y)
            __res:getPNGCC(1,30,30,140,140):平铺(w-60,h-60):显示(30,30+y)
            __res:getPNGCC(1,30,170,140,30):平铺(w-60,30):显示(30,h-30+y)

            __res:getPNGCC(1,170,0,30,30):显示(w-30,0+y)
            __res:getPNGCC(1,170,30,30,140):平铺(30,h-60):显示(w-30,30+y)
            __res:getPNGCC(1,170,170,30,30):显示(w-30,h-30+y)
            if name then
                __res:getPNGCC(1,448,0,20,28):显示(math.floor(w/3),0+y)
                __res:getPNGCC(1,20+448,0,45,28):平铺(math.floor(w/3)-40,28):显示(20+math.floor(w/3),0+y)
                __res:getPNGCC(1,81+448,0,20,28):显示(math.floor(w/3)-20+math.floor(w/3),0+y)
                if name ~= "无" then
                    local 宽度 = 字体20:取宽度(name)
                    字体20:置颜色(255,255,255)
                    local A=math.floor(w/2-宽度/2)
                    字体20:取图像(name):显示(A,y+3)
                end
            end
        nsf:渲染结束()
    end
    if lx then
        return nsf
    else
        return nsf:到精灵()
    end
end



--xiao置窗口背景("系统设置", 0, 12, 719, 473, true):显示(0, 0)
function xiao置窗口背景(name,x,y,w,h,lx)
    local nsf = require('SDL.图像')(w,h+y)
    local num=588
    if nsf:渲染开始() then
            __res:getPNGCC(4,0,0+num,30,30):显示(0,0+y)
            __res:getPNGCC(4,0,30+num,30,140):平铺(30,h-60):显示(0,30+y)
            __res:getPNGCC(4,0,170+num,30,30):显示(0,h-30+y)

            __res:getPNGCC(4,30,0+num,140,30):平铺(w-60,30):显示(30,0+y)
            __res:getPNGCC(4,30,30+num,140,140):平铺(w-60,h-60):显示(30,30+y)
            __res:getPNGCC(4,30,170+num,140,30):平铺(w-60,30):显示(30,h-30+y)

            __res:getPNGCC(4,170,0+num,30,30):显示(w-30,0+y)
            __res:getPNGCC(4,170,30+num,30,140):平铺(30,h-60):显示(w-30,30+y)
            __res:getPNGCC(4,170,170+num,30,30):显示(w-30,h-30+y)

            __res:getPNGCC(4,205,num,229,31):显示(math.floor(w/2)-111,0+y) --横幅
            if name then
                if name ~= "无" then
                    local 宽度 = 字体20:取宽度(name)
                    字体20:置颜色(255,255,255)
                    字体20:取图像(name):显示(w/2-宽度/2,y+3)
                end
            end
        nsf:渲染结束()
    end
    if lx then
        return nsf
    else
        return nsf:到精灵()
    end
end



function huang置窗口背景(name,x,y,w,h,lx)
    local nsf = require('SDL.图像')(w,h+y)
    local num=0
    if nsf:渲染开始() then
            __res:getPNGCC(7,0,0+num,30,30):显示(0,0+y)
            __res:getPNGCC(7,0,30+num,30,140):平铺(30,h-60):显示(0,30+y)
            __res:getPNGCC(7,0,170+num,30,30):显示(0,h-30+y)

            __res:getPNGCC(7,30,0+num,140,30):平铺(w-60,30):显示(30,0+y)
            __res:getPNGCC(7,30,30+num,140,140):平铺(w-60,h-60):显示(30,30+y)
            __res:getPNGCC(7,30,170+num,140,30):平铺(w-60,30):显示(30,h-30+y)

            __res:getPNGCC(7,170,0+num,30,30):显示(w-30,0+y)
            __res:getPNGCC(7,170,30+num,30,140):平铺(30,h-60):显示(w-30,30+y)
            __res:getPNGCC(7,170,170+num,30,30):显示(w-30,h-30+y)

            --__res:getPNGCC(7,0,202,193,25):显示(math.floor(w/2)-111,0+y) --横幅
            if name then
                if name ~= "无" then
                    local 宽度 = 字体20:取宽度(name)
                    字体18:置颜色(__取颜色("黑色"))
                    字体18:取图像(name):显示(w/2-宽度/2,y+3)
                end
            end
        nsf:渲染结束()
    end
    if lx then
        return nsf
    else
        return nsf:到精灵()
    end
end


function 取提示背景(x,y,w,h,lx) --假设最多两派
    local nsf = require('SDL.图像')(w,h+y+5)
    if nsf:渲染开始() then
        -- nsf["渲染清除"](nsf, 0, 0, 0, 0)
        __res:getPNGCC(2, 1070, 305, 10, 14):显示(0, 0) --左
        __res:getPNGCC(2, 1070+83, 305, 10, 14):显示(w-10, 0) --右
        __res:getPNGCC(2, 1070+23, 305, 10, 14):平铺(w-20,14):显示(10,0)
        if h>30 then --只有1
            --下
            __res:getPNGCC(2, 1070, 319, 10, 14):显示(0, 14*1) --左
            __res:getPNGCC(2, 1070+83, 319, 10, 14):显示(w-10, 14*1) --右
            __res:getPNGCC(2, 1070+23, 319, 10, 14):平铺(w-20,14):显示(10,14*1)

            __res:getPNGCC(2, 1070, 370-14, 10, 14):显示(0, 14*2) --左
            __res:getPNGCC(2, 1070+83, 370-14, 10, 14):显示(w-10, 14*2) --右
            __res:getPNGCC(2, 1070+23, 370-14, 10, 14):平铺(w-20,14):显示(10,14*2)
        else
                --下
            __res:getPNGCC(2, 1070, 370-14, 10, 14):显示(0, 14) --左
            __res:getPNGCC(2, 1070+83, 370-14, 10, 14):显示(w-10, 14) --右
            __res:getPNGCC(2, 1070+23, 370-14, 10, 14):平铺(w-20,14):显示(10,14)
        end
        nsf:渲染结束()
    end
    if lx then
        return nsf
    else
        return nsf:到精灵()
    end
end