--[[
LastEditTime: 2024-04-14 07:21:34
--]]
local 合成旗 = 窗口层:创建窗口("合成旗",0,0, 100, 100)
function 合成旗:初始化()
  self.地图大小 = {
    [1092] = {w = 4480, h = 3020},
    [1001] = {w = 11000, h = 5600},
    [1501] = {w = 5760, h = 2880},
    [1070] = {w = 3200, h = 4200},
    [1208] = {w = 3840, h = 2400},
    [1226] = {w = 3200, h = 2400},
    [1040] = {w = 3280, h = 2480},
    [1173] = {w = 12800, h = 2400}
  }

end
function 合成旗:显示(x, y)
  if self.图像 then
    self.图像:显示(x + 20, y + 20)
  end
  for i, v in ipairs(self.坐标点) do
     v.tcp:显示(x + math.floor(v.x * 20 * self.py.x) + 20, y + math.floor(v.y * 20 * self.py.y) + 20)
  end
end
function 合成旗:左键弹起(x, y)
  for i, v in ipairs(self.坐标点) do
    if v.tcp:检查透明(x, y) then
      请求服务(3737, {
        ["序列"] = i
      })
      合成旗:置可见(false)
    end
  end
end
function 合成旗:打开(数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  if self.smap and self.smap ~= 数据.地图 then
    self:重置(数据)
  elseif not self.smap then
    self:重置(数据)
  end
end



function 合成旗:重置(数据)
  self.smap = 数据.地图
  self.图像 = nil
  self.传送点 = {}
  self.坐标点 = 数据.xy
  local Smap, Smapa = __小地图资源加载(self.smap)
  if Smapa and self.地图大小[self.smap] then
    self.图像 = __res:取资源动画(Smap, Smapa,"精灵")
    local w, h = self.图像.宽度 + 40, self.图像.高度 + 40
    self.py = {
      x = self.图像.宽度 / self.地图大小[self.smap].w,
      y = self.图像.高度 / self.地图大小[self.smap].h
    }
    self.pys = {
      x = self.地图大小[self.smap].w / self.图像["宽度"],
      y = self.地图大小[self.smap].h / self.图像["高度"]
    }
    
      self:置宽高(w + 30, h)
      self:置中心(0 - w / 2, 0 - h / 2)
      if __手机 then
        self.关闭:置大小(25,25)
        self.关闭:置坐标(w-27, 2)
      else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(w-18, 2)
      end
      
      self:置精灵(取黑色背景(0, 0, w, h))
      self:置坐标(引擎.宽度2-w//2,引擎.高度2-h//2)
      for i = 1, #self.坐标点 do
          self.坐标点[i].tcp = __res:getPNGCC(2, 1042, 15, 18, 19):到精灵():置中心(9, 9)
      end
  else
     self:置可见(false)
  end
end

local 关闭 = 合成旗:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  合成旗:置可见(false)
end
