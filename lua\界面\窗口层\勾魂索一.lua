--[[
Author: GGELUA
Date: 2024-11-17 22:03:56
Last Modified by: GGELUA
Last Modified time: 2024-11-17 22:07:40
--]]
--[[
Author: GGELUA
Date: 2024-11-06 21:48:13
Last Modified by: GGELUA
Last Modified time: 2024-11-17 15:15:55
--]]
local 勾魂索一 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "勾魂索一", 258 + abbr.py.x, 60 + abbr.py.y, 367+10, 157+10+87)
function 勾魂索一:初始化()
  local nsf = require("SDL.图像")(445, 410)
  if nsf["渲染开始"](nsf) then
    xiao置窗口背景("勾魂索准备", 0, 12, 367, 157+17+58-39, true):显示( 0, 0)
    --__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35)["显示"](__res:getPNGCC(2, 795, 885, 373, 115)["拉伸"](__res:getPNGCC(2, 795, 885, 373, 115), 266, 35), 72-24, 60+60-29)
    字体18:置颜色(__取颜色("白色"))
    字体18:取图像("勾魂已开始，请准备，倒计时10秒战斗"):显示(32,33+8+16)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 勾魂索一:打开()
  --self.发送时间=os.time()
  self:置可见(true)
  --self.shuru:置文本("")
end



local 关闭 = 勾魂索一["创建我的按钮"](勾魂索一, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 403-75, 0)
function 关闭:左键弹起(x, y, msg)
  勾魂索一["置可见"](勾魂索一, false)
 -- 勾魂索一["shuru"]["清空"](勾魂索一["shuru"])
end
--local shuru = 勾魂索一["创建我的输入"](勾魂索一, "shuru", 81-24, 67+60-28, 254, 24, nil, 88, "黑色", 字体20)

local 确定 = 勾魂索一["创建我的按钮"](勾魂索一, __res:getPNGCC(3, 2, 507, 120, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 120, 41, true), 120, 41), "确定", 153-26, 337-189-25+18, "准备")
function 确定:左键弹起(x, y, msg)
  --if 勾魂索一.shuru:取文本()~= ""  then
--    if os.time()-勾魂索一.发送时间>=3 then
      发送数据(6564)
      勾魂索一["置可见"](勾魂索一, false)
    --  勾魂索一.发送时间=os.time()
      --勾魂索一.shuru:置文本("")
    end
 -- else
    --__UI弹出["提示框"]:打开("#Y/请重新填写输入框")
  --end
--end
