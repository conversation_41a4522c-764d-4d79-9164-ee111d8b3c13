local 人物称谓 = 窗口层:创建窗口("人物称谓", 0, 0, 250, 350)
local  取称谓说明=function(称谓)
	local n = {}
	n["梦幻测试"] = "欢迎测试梦幻西游(本称谓为纪念称谓)#91"
	n["梦幻新秀"] = "欢迎测试梦幻西游(本称谓为纪念称谓)#91"
	n["内测用户"] = "欢迎测试梦幻西游(本称谓为纪念称谓)#91"
	n["无称谓"] = "对不起!你目前无称谓哦,请继续加油.#9"
	n["帮众"] = "帮派证明身份的基础标识.#9"
	n["堂主"] = "作为一帮堂主的身份标识.#9"
	n["长老"] = "作为一帮长老的身份标识.#9"
	n["护法"] = "作为一帮护法的身份标识.#9"
	n["帮主"] = "作为一帮帮主的身份标识.#9"
  n["武神坛冠军"] = "限时称号，某种实力的象征."
	n["武神坛亚军"] = "限时称号，某种实力的象征."
	n["武神坛季军"] = "限时称号，某种实力的象征."
  n["大唐官府首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["神木林首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["方寸山首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["化生寺首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["女儿村首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["天宫首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["普陀山首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["五庄观首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["凌波城首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["龙宫首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["魔王寨首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["狮驼岭首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["盘丝洞首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["无底洞首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["阴曹地府首席大弟子"] = "实力的象征\n#Y+10伤害#R+10灵力\n#G+10速度#L+10防御\n#P+100气血"
  n["大海龟杀手"] = "+10点血量"
  n["荒漠屠夫"] = "+10点伤害"
  n["僵尸道长"] = "+10点灵力"
  n["快递小哥"] = "+10点速度"
  n["摸金校尉"] = "+10点防御"
  n["当代清官"] = "+5点伤害和灵力"
  n["首席小弟子"] = "#Y+2伤害#R+2灵力\n#G+2速度#L+2防御\n#P+10气血"
  n["英雄大会冠军"] = "限时称号，实力的象征\n#Y+200伤害#R+200灵力\n#G+200速度#L+200防御\n#P+500气血"
  n["英雄大会亚军"] = "限时称号，实力的象征\n#Y+150伤害#R+150灵力\n#G+150速度#L+150防御\n#P+300气血"
  n["英雄大会季军"] = "限时称号，实力的象征\n#Y+100伤害#R+100灵力\n#G+100速度#L+100防御\n#P+200气血"
  n["英雄大会精英"] = "限时称号，积极参与活动\n#Y+50伤害#R+50灵力\n#G+50速度#L+50防御\n#P+100气血"
  n["大罗金仙"] = "5大基础属性+50"
  n["先天圣人"] = "5大基础属性+150"
  n["千亿称号[血]"] = "最大气血+2000"
  n["千亿称号[伤]"] = "伤害+1000"
  n["千亿称号[法]"] = "灵力+800"
  n["千亿称号[防]"] = "防御+800"
  n["千亿称号[速]"] = "速度+600"
  n["绝世英豪"] = "实力的象征,该称谓增加全属性#R+5%"
  n["横扫天下"] = "实力的象征,该称谓增加全属性#R+10%"
  n["独孤求败"] = "实力的象征,该称谓增加全属性#R+20%"
  n["合格菜商"] = "GM认证出菜人员，可以交易，有问题一切以聊天截图，后台记录进行判定处理！"
  n["彩虹霸主"] = "限时称号，积极参与活动\n#Y+200伤害#R+150法伤\n#G+100速度"
  n["帮战之星"] = "限时称号，积极参与活动\n#Y+350气血#R+150防御\n#G+100法防"
  if string.find(称谓,"镇妖塔") ~= nil then
        local 临时称谓=分割文本(称谓,"镇妖塔")
        if 临时称谓~=nil and  临时称谓[2]~=nil then
            local 临时层数 = 分割文本(临时称谓[2],"层")
            if 临时层数~=nil and  临时层数[1]~=nil and tonumber(临时层数[1])~=nil and tonumber(临时层数[1])>0 then
               local 临时倍数 = math.floor(tonumber(临时层数[1])/10)
               local 气血=  300*临时倍数
               local 伤害 = 50*临时倍数
               local 防御 = 50*临时倍数
               local 灵力 = 40*临时倍数
               local 速度=20*临时倍数
              return  "#L实力的象征:\n#R+"..伤害.."伤害 +"..灵力.."灵力\n#R+"..速度.."速度 +"..防御.."防御\n#R+"..气血.."气血"
            end
        else
        	 return  "实力的象征,该称谓增加属性.#9"
        end
  end

	if n[称谓]==nil and n[string.sub(称谓,-4)] == nil then
	    n[称谓]="暂时没有该称谓的记载!.#9"
	elseif n[string.sub(称谓,-4)] ~= nil then
		return n[string.sub(称谓,-4)]
	end
	if 称谓==nil or 称谓=="" then
	   return  "对不起!你目前无称谓哦,请继续加油.#9"
	end
	return n[称谓]
end






function 人物称谓:初始化()
      self:创建纹理精灵(function()
        置窗口背景("人物称谓", 0, 0, 250, 350, true):显示( 0, 0)
        蓝白标题背景(220,140,true):显示(15,55)
        取白色背景(0, 0, 220, 100, true):显示(15,205)
        取输入背景(0, 0, 160, 22):显示(75,30)
        文本字体:置颜色(255, 255, 255, 255):取图像("当前称谓:"):显示(10,33)
      end
    )
      self.选中 = nil
      self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
      self.可初始化=true
      if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-27, 2)
      else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-18, 2)
      end
end

function 人物称谓:更新(dt)
        if self.开始x and self.开始y then
              local xx,yy=窗口层.人物属性:取坐标()
              if self.开始x~=xx or self.开始y~=yy then
                  if xx - self.宽度>=5 then
                        self:置坐标(xx - self.宽度, yy)
                  else
                        self:置坐标(xx + 窗口层.人物属性.宽度, yy)
                  end
                  self.开始x,self.开始y=窗口层.人物属性:取坐标()
              end

        end
end
function 人物称谓:显示(x, y)
      if self.当前称谓 then
          self.当前称谓:显示(x+80, y + 33)
      end
end
function 人物称谓:打开()
      self:置可见(not self.是否可见)
        if not self.是否可见 then
            return
        end
      人物称谓.称谓列表:重置()
      self.选中 = nil
    
      self.开始x,self.开始y=窗口层.人物属性:取坐标()
      if self.开始x - self.宽度>=5 then
          self:置坐标(self.开始x - self.宽度, self.开始y)
      else
          self:置坐标(self.开始x + 窗口层.人物属性.宽度, self.开始y)
      end
     
     self.当前称谓=文本字体:置颜色(0, 0, 0, 255):取精灵(角色信息.当前称谓)
     


end
local 关闭 = 人物称谓:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y, msg)
  人物称谓:置可见( false)
end
local 称谓列表 = 人物称谓:创建列表("称谓列表", 20, 85, 210, 110)
function 称谓列表:初始化()
    self:置文字(文本字体)
    self:置颜色(0, 0, 0, 255)
    self.行间距 = 2
end
function 称谓列表:重置()
      人物称谓.说明文本:清空()
      self:清空()
      for i, v in ipairs(角色信息.称谓) do
          self:添加(v)
      end

end
function 称谓列表:左键弹起(x, y, i)
      if 角色信息 and 角色信息.称谓 and 角色信息.称谓[i] then
            人物称谓.选中 = i
            local 说明= 取称谓说明(角色信息.称谓[i])
            人物称谓.说明文本:清空()
            人物称谓.说明文本:置文本("#H"..说明)
      end
end

local 说明文本 = 人物称谓:丰富文本("说明文本", 20, 215, 210, 80)



local  隐藏= 人物称谓:创建红色按钮("隐藏", "隐藏", 100, 315,40,22) --红色
function 隐藏:左键弹起(x, y, msg)
  请求服务(31, {称谓ID = 0})
  角色信息.当前称谓 = ""
  __主显.主角:置称谓()
end
local 更改 = 人物称谓:创建红色按钮("更改", "更改", 40, 315,40,22) --红色
function 更改:左键弹起(x, y, msg)
  if 人物称谓.选中 then
    请求服务(31, { 称谓ID = 人物称谓.选中})
    角色信息.当前称谓 = 角色信息.称谓[人物称谓.选中]
    __主显.主角:置称谓(角色信息.当前称谓)
  end
end
local 删除 = 人物称谓:创建红色按钮("删除", "删除", 160, 315,40,22) --红色
function 删除:左键弹起(x, y, msg)
  if 角色信息.称谓[人物称谓.选中] == nil then
      __主显.主角:置称谓()
  end
  请求服务(37, { 称谓ID = 人物称谓.选中})
end


-- if tp.队伍[1].称谓[self.选中] == nil then
--   self:打开()
--   return false
-- end