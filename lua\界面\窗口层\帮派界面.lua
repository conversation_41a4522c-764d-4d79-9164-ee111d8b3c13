local 帮派界面 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "帮派界面", 72 + abbr.py.x, 11 + abbr.py.y, 757, 514)
local 帮派建筑升级经验 = {
  [0]=1600,
  [1]=1600,
  [2]=3200,
  [3]=5200,
  [4]=7500,
  [5]=10300,
  [6]=13600,
  [7]=17900,
  [8]=23100,
  [9]=29500,
  [10]=37200,
  [11]=46600,
  [12]=57800,
  [13]=71100,
  [14]=86700,
  [15]=104900,
  [16]=128000
}
local function 取繁荣等级(zz)
	if zz<8000 then
		return "无"
	elseif zz>=8000 then
		return "初显"
	elseif zz>=25000 then
		return "名门"
	elseif zz>=35000 then
		return "鼎盛"
	elseif zz>=45000 then
		return "泰山北斗"
	elseif zz>=50000 then
		return "天下盟主"
	end
end

local bpbb = {"当前建筑进度","需要建筑进度","需要耗繁荣度","需要耗人气度"}--{"需要损耗资金","需要建筑进度","需要耗繁荣度","需要耗人气度"}
local bpjn = {"强壮","神速","灵石技巧","强身术","健身术","冥想","炼金术","打造技巧","裁缝技巧","熔炼技巧","暗器技巧","烹饪技巧","中医药理","巧匠之术","逃离技巧","追捕技巧","淬灵之术","养身之道"}

function 帮派界面:初始化()
  local nsf = require("SDL.图像")(757, 514)
  if nsf["渲染开始"](nsf) then
    置窗口背景("帮派", 0, 12, 750, 445, true):显示(0, 0)
    -- 取灰色背景(0, 0, 775, 332, true)["显示"](取灰色背景(0, 0, 775, 332, true), 18, 110)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 帮派界面:打开(data)
  self:置可见(true)
  self.基础["置选中"](self.基础, true)
  
  self.数据 = data
  self.权限 = 0
  self.加入= 0

  self.修炼选中= 0

  self.在线选中=0
  self.离线选中=0
  self.技能选中=0
  self.建筑选中=0
  self.申请选中=0

  --要先拷贝一份帮派成员数据，自己排在前面--接下来是官职排序，接下来是在线玩家
  self.在线玩家 = {}
  for i ,v in pairs(self.数据.成员数据) do
    if v.id==角色信息.数字id then
      self.权限 = v.权限
      v.权限 = v.权限+100 --给玩家自己赋值一个最大的权限，排第一
      self.在线玩家[#self.在线玩家+1]=v
    else
      if v.职务~="帮众" then
        self.在线玩家[#self.在线玩家+1]=v
      else
        if v.在线 then
          self.在线玩家[#self.在线玩家+1]=v
        end
      end
    end
  end
  -- table.print(self.在线玩家)
  
  table.sort(self.在线玩家, function(a, b) return a.权限 > b.权限 end)
  self.离线玩家 = {}
  for i ,v in pairs(self.数据.成员数据) do
    if v.在线==false then
      self.离线玩家[#self.离线玩家+1]=v
    end
  end
  if 调试模式 then
    for i=1,10 do
      self.数据.申请人员[#self.数据.申请人员+1] = {
        名称="名称"..i,
        数字id=取随机数(9999,19999),
        等级=取随机数(89,129),
        门派="门派"..i,
        申请时间=os.time()+取随机数(9999,999999)
      }
    end
  end
  table.sort(self.离线玩家, function(a, b) return a.离线时间 < b.离线时间 end)

  self.帮派技能 = {}
  for k,v in pairs(self.数据.技能数据) do
    self.帮派技能[#self.帮派技能+1] = v
    self.帮派技能[#self.帮派技能].名称 = k
  end
  self.修炼技能 = {}
  for k,v in pairs(self.数据.修炼数据) do
    self.修炼技能[#self.修炼技能+1] = v
    self.修炼技能[#self.修炼技能].名称 = k
  end
  self.建筑情况 = {}
  for k,v in pairs(self.数据.帮派建筑) do
    self.建筑情况[#self.建筑情况+1] = v
    self.建筑情况[#self.建筑情况].名称 = k
  end
  帮派界面.堂务管理:置可见(false)
  帮派界面.逐出帮众:置可见(false)
  帮派界面.脱离帮派:置可见(false)

  self.zaixian控件["置可见"](self.zaixian控件, false)
  self.lixian控件["置可见"](self.lixian控件, false)
  self.jineng控件["置可见"](self.jineng控件, false)
  self.jianzhu控件["置可见"](self.jianzhu控件, false)
  self.shenqing控件["置可见"](self.shenqing控件, false)

  self:重置()
  -- self.数据 = data
  -- self.职位 = 22
  -- for i, v in ipairs(data["成员数据"]) do
  --   if v.id == 角色信息["数字id"] then
  --     self.职位 = v["职位"]
  --   end
  -- end
end



function 帮派界面:更新数据(内容)
	if 内容.类型=="任命职位" then
		-- table.print(内容)
		self.数据.成员数据=内容.成员数据
		-- table.print(self.数据.成员数据)
		self.在线玩家 = {}
		for i ,v in pairs(self.数据.成员数据) do
			if v.id==角色信息.数字id then
				self.权限 = v.权限
				v.权限 = v.权限+100 --给玩家自己赋值一个最大的权限，排第一
				self.在线玩家[#self.在线玩家+1]=v
			else
				if v.职务~="帮众" then
					self.在线玩家[#self.在线玩家+1]=v
				else
					if v.在线 then
						self.在线玩家[#self.在线玩家+1]=v
					end
				end
			end
		end
		table.sort(self.在线玩家, function(a, b) return a.权限 > b.权限 end)
    帮派界面.成员:左键弹起()
	elseif 内容.类型=="逐出帮派" then
		self.数据.成员数据=内容.成员数据
		self.离线玩家 = {}
		for i ,v in pairs(self.数据.成员数据) do
			if v.在线==false then
				self.离线玩家[#self.离线玩家+1]=v
			end
		end
		table.sort(self.离线玩家, function(a, b) return a.离线时间 < b.离线时间 end)
    帮派界面.统计:左键弹起()
	elseif 内容.类型=="修改内政" then
		-- table.print(内容)
		-- table.print(self.数据)
		self.数据.当前内政=内容.当前内政
    帮派界面.建筑:左键弹起()
	elseif 内容.类型=="提升规模" then
		self.数据.帮派规模=内容.帮派规模
		self.数据.帮派资金.上限=内容.帮派资金
		self.数据.帮派资材.上限=内容.资材上限
		self.数据.成员数量.上限=内容.成员上限
		self.数据.当前维护费=内容.维护费
    帮派界面.基础:左键弹起()
	end

end


local bzgk = {"帮 派 名","帮派资金","敌对帮派","帮派规模","储 备 金","同盟帮派","帮派编号","维护情况","安 定 度","创 始 人","维护时间","人 气 度","帮    主","目前资材","行 动 力","成    员","最大资材","研 究 力","繁荣等级","掌控区域","繁 荣 度"}
function 帮派界面:重置()
  local nsf = require("SDL.图像")(732, 514)
  if nsf["渲染开始"](nsf) then
    local lssj = 取输入背景(0, 0, 140, 23)
    __res:getPNGCC(3, 455, 1149, 145, 29):显示(60,109)
    __res:getPNGCC(3, 455, 1149, 145, 29):显示(60+243,109)
    __res:getPNGCC(3, 455, 1149, 145, 29):显示(60+243*2,109)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "基础数据"):置混合(0):显示(60+38, 109+4)
    字体18["取图像"](字体18, "资源信息"):置混合(0):显示(60+38+243, 109+4)
    字体18["取图像"](字体18, "其他信息"):置混合(0):显示(60+38+243*2, 109+4)
    local 行数 = 0
		local 列数 = 0
    for i=1, #bzgk do
      字体18["置颜色"](字体18, __取颜色("白色"))
      字体18["取图像"](字体18, bzgk[i]):置混合(0):显示(240*行数+25, 155+列数*37)
      lssj:显示(240*行数+25+82, 155+列数*37-2)
      行数=行数+1
			if 行数==3 then
				行数 = 0
				列数 = 列数 + 1
			end
    end
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    ---竖1
    字体18:取图像(self.数据.帮派名称):显示(240*0+25+82+12, 155+0*37)
    字体18:取图像(self.数据.帮派规模):显示(240*0+25+82+12, 155+1*37)
    字体18:取图像(self.数据.帮派编号):显示(240*0+25+82+12, 155+2*37)
    字体18:取图像(self.数据.创始人.名称):显示(240*0+25+82+12, 155+3*37)
    字体18:取图像(self.数据.现任帮主.名称):显示(240*0+25+82+12, 155+4*37)
    字体18:取图像(self.数据.成员数量.当前.."/"..self.数据.成员数量.上限):显示(240*0+25+82+12, 155+5*37)
    字体18:取图像(取繁荣等级(self.数据.繁荣度)):显示(240*0+25+82+12, 155+6*37)

    --竖2
    字体18:取图像(self.数据.帮派资金.当前):显示(240*1+25+82+12, 155+0*37)
    字体18:取图像(self.数据.储备金):显示(240*1+25+82+12, 155+1*37)
    字体18:取图像(self.数据.维护情况):显示(240*1+25+82+12, 155+2*37)
    字体18:取图像("整点"):显示(240*1+25+82+12, 155+3*37)
    字体18:取图像(self.数据.帮派资材.当前):显示(240*1+25+82+12, 155+4*37)
    字体18:取图像(self.数据.帮派资材.上限):显示(240*1+25+82+12, 155+5*37)
    字体18:取图像(self.数据.掌控区域):显示(240*1+25+82+12, 155+6*37)

    ---竖3
    字体18:取图像(self.数据.敌对帮派.名称):显示(240*2+25+82+12, 155+0*37)
    字体18:取图像(self.数据.同盟帮派.名称):显示(240*2+25+82+12, 155+1*37)
    字体18:取图像(self.数据.安定度):显示(240*2+25+82+12, 155+2*37)
    字体18:取图像(self.数据.人气度):显示(240*2+25+82+12, 155+3*37)
    字体18:取图像(self.数据.行动力):显示(240*2+25+82+12, 155+4*37)
    字体18:取图像(self.数据.研究力):显示(240*2+25+82+12, 155+5*37)
    字体18:取图像(self.数据.繁荣度):显示(240*2+25+82+12, 155+6*37)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end

local asdad={"基础","成员","统计","技能","建筑","联赛","申请"}
for i=1, #asdad do
  local 临时函数 = 帮派界面["创建我的单选按钮"](帮派界面, __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 82, 41),
  __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 82, 41), asdad[i], 16+(i-1)*105, 56, asdad[i])
   function  临时函数:左键弹起(x, y)
    if asdad[i] == "基础" then
      帮派界面.堂务管理:置可见(false)
      帮派界面.逐出帮众:置可见(false)
      帮派界面.脱离帮派:置可见(false)

      帮派界面["zaixian控件"]["置可见"](帮派界面["zaixian控件"], false)
      帮派界面["lixian控件"]["置可见"](帮派界面["lixian控件"], false)
      帮派界面["jineng控件"]["置可见"](帮派界面["jineng控件"], false)
      帮派界面["jianzhu控件"]["置可见"](帮派界面["jianzhu控件"], false)
      帮派界面["shenqing控件"]["置可见"](帮派界面["shenqing控件"], false)

      帮派界面["重置"](帮派界面, 帮派界面["数据"])
    elseif asdad[i] == "成员" then
      帮派界面["图像"] = nil
      帮派界面["zaixian控件"]:重置()

      帮派界面.堂务管理:置可见(帮派界面.权限>=4)
      帮派界面.逐出帮众:置可见(帮派界面.权限>=4)
      帮派界面.脱离帮派:置可见(true)

      帮派界面["zaixian控件"]["置可见"](帮派界面["zaixian控件"], true)
      帮派界面["lixian控件"]["置可见"](帮派界面["lixian控件"], false)
      帮派界面["jineng控件"]["置可见"](帮派界面["jineng控件"], false)
      帮派界面["jianzhu控件"]["置可见"](帮派界面["jianzhu控件"], false)
      帮派界面["shenqing控件"]["置可见"](帮派界面["shenqing控件"], false)
    elseif asdad[i] == "统计" then
      帮派界面["图像"] = nil
      帮派界面["lixian控件"]:重置()

      帮派界面.堂务管理:置可见(false)
      帮派界面.逐出帮众:置可见(帮派界面.权限>=4)
      帮派界面.脱离帮派:置可见(false)

      帮派界面["zaixian控件"]["置可见"](帮派界面["zaixian控件"], false)
      帮派界面["lixian控件"]["置可见"](帮派界面["lixian控件"], true)
      帮派界面["jineng控件"]["置可见"](帮派界面["jineng控件"], false)
      帮派界面["jianzhu控件"]["置可见"](帮派界面["jianzhu控件"], false)
      帮派界面["shenqing控件"]["置可见"](帮派界面["shenqing控件"], false)
    elseif asdad[i] == "技能" then
      帮派界面["图像"] = nil

      帮派界面.堂务管理:置可见(false)
      帮派界面.逐出帮众:置可见(false)
      帮派界面.脱离帮派:置可见(false)

      帮派界面["jineng控件"]:重置()
      帮派界面["zaixian控件"]["置可见"](帮派界面["zaixian控件"], false)
      帮派界面["lixian控件"]["置可见"](帮派界面["lixian控件"], false)
      帮派界面["jineng控件"]["置可见"](帮派界面["jineng控件"], true)
      帮派界面["jianzhu控件"]["置可见"](帮派界面["jianzhu控件"], false)
      帮派界面["shenqing控件"]["置可见"](帮派界面["shenqing控件"], false)
    elseif asdad[i] == "建筑" then
      帮派界面["图像"] = nil
      帮派界面.堂务管理:置可见(false)
      帮派界面.逐出帮众:置可见(false)
      帮派界面.脱离帮派:置可见(false)

      帮派界面["jianzhu控件"]:重置()
      帮派界面["zaixian控件"]["置可见"](帮派界面["zaixian控件"], false)
      帮派界面["lixian控件"]["置可见"](帮派界面["lixian控件"], false)
      帮派界面["jineng控件"]["置可见"](帮派界面["jineng控件"], false)
      帮派界面["jianzhu控件"]["置可见"](帮派界面["jianzhu控件"], true)
      帮派界面["shenqing控件"]["置可见"](帮派界面["shenqing控件"], false)
    elseif asdad[i] == "申请" then
      帮派界面["图像"] = nil
      帮派界面.堂务管理:置可见(false)
      帮派界面.逐出帮众:置可见(false)
      帮派界面.脱离帮派:置可见(false)

      帮派界面["shenqing控件"]:重置()
      帮派界面["zaixian控件"]["置可见"](帮派界面["zaixian控件"], false)
      帮派界面["lixian控件"]["置可见"](帮派界面["lixian控件"], false)
      帮派界面["jineng控件"]["置可见"](帮派界面["jineng控件"], false)
      帮派界面["jianzhu控件"]["置可见"](帮派界面["jianzhu控件"], false)
      帮派界面["shenqing控件"]["置可见"](帮派界面["shenqing控件"], true)
    end
  end
end

local zaixian控件 = 帮派界面["创建控件"](帮派界面, "zaixian控件", 18, 110, 775, 332)
function zaixian控件:重置()
  local nsf = require("SDL.图像")(732, 444)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 717, 248, true):显示(40-25, 120+27)
    __res:getPNGCC(4, 719, 281, 41, 35):显示(40-25, 120+27)
    __res:getPNGCC(4, 719+39, 281, 282, 35):显示(40-25+39, 120+27)
    __res:getPNGCC(4, 719+39, 281, 283, 35):显示(40-25+39+282, 120+27)
    __res:getPNGCC(4, 719+238, 281, 113, 35):显示(619, 120+27)
    local shu=__res:getPNGCC(4,1085, 281, 3, 35)
    shu:显示(159, 120+27)
    shu:显示(208, 120+27)
    shu:显示(312, 120+27)
    shu:显示(395, 120+27)
    shu:显示(521, 120+27)
    shu:显示(625, 120+27)
    字体18["置颜色"](字体18, __取颜色("绿色"))
    字体18:取图像("帮派在线玩家和管理人员："):置混合(0):显示(40-25, 127-21)
    for _, v in ipairs({
      {
        name = "名称",
        dy = "名称",
        x = 71,
        y = 127
      },
      {
        name = "等级",
        dy = "等级",
        x = 166,
        y = 127
      },
      {
        name = "ID",
        dy = "id",
        x = 255,
        y = 127
      },
      {
        name = "门派",
        dy = "门派",
        x = 338,
        y = 127
      },
      {
        name = "职位",
        dy = "职位",
        x = 439,
        y = 127
      },
      {
        name = "贡献",
        dy = "贡献",
        x = 553,
        y = 127
      },
      {
        name = "离线",
        dy = "离线",
        x = 657,
        y = 127
      },
    }) do
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, v.name or "无")["显示"](字体18["取图像"](字体18, v.name or "无"), v.x, v.y+28)
    end
    nsf["渲染结束"](nsf)
  end
  帮派界面["图像"] = nsf["到精灵"](nsf)
  if #帮派界面.在线玩家~=0 then
    self.在线列表["重置"](self.在线列表)
    -- self.申请列表["重置"](self.申请列表, 帮派界面["数据"]["申请人员"])
  end
  帮派界面.堂务管理:置可见(帮派界面.权限>=4)
  帮派界面.逐出帮众:置可见(帮派界面.权限>=4)
  帮派界面.脱离帮派:置可见(true)

  -- self.任命职位["置禁止"](self.任命职位, 1 ~= 帮派界面["职位"])
  -- self.逐出帮派["置禁止"](self.逐出帮派, 1 ~= 帮派界面["职位"])
  -- self.允许加入["置禁止"](self.允许加入, 1 ~= 帮派界面["职位"])
  -- self.清空列表["置禁止"](self.清空列表, 1 ~= 帮派界面["职位"])
end


local 在线列表 = zaixian控件["创建列表"](zaixian控件, "在线列表", 0, 54+18, 711, 200)
function 在线列表:初始化()
  self:置文字(字体20)
  self.行高度 = 40
  self.行间距 = 0
end
function 在线列表:重置()
  -- for i=1，5 do
  --   if 帮派界面.在线玩家[i] then
  --     字体18["置颜色"](字体18, __取颜色("浅黑"))
  --     local 宽度 = 字体18["取宽度"](字体18, 帮派界面.在线玩家[i].名称)
  --     字体18:取图像("帮派在线玩家和管理人员："):显示(54.0 - 宽度 / 2, 3)
  --   end
  -- end
  self.清空(self)
  for _, v in ipairs(帮派界面.在线玩家) do
    local nsf = require("SDL.图像")(684, 40)
    if nsf["渲染开始"](nsf) then
      字体16["置颜色"](字体16, __取颜色("浅黑"))
      local 宽度 = 字体16["取宽度"](字体16, v.名称)
      字体16:取图像(v.名称):显示(54.0 - 宽度 / 2+18, 15)
      宽度 = 字体16["取宽度"](字体16, v.等级)
      字体16:取图像(v.等级):显示(54.0 - 宽度 / 2+109, 15)
      字体16:取图像(v.id):显示(226, 15)
      宽度 = 字体16["取宽度"](字体16, v.门派)
      字体16:取图像(v.门派):显示(54.0 - 宽度 / 2+326-42, 15)
      宽度 = 字体16["取宽度"](字体16, v.职务)
      字体16:取图像(v.职务):显示(54.0 - 宽度 / 2+429-45, 15)
      宽度 = 字体16["取宽度"](字体16, v.帮贡.当前)
      字体16:取图像(v.帮贡.当前):显示(54.0 - 宽度 / 2+552-55, 15)
      if v.在线 then
        字体16["置颜色"](字体16, __取颜色("紫色"))
      end
      字体16:取图像("在线"):显示(639, 15)
      -- 字体16["取图像"](字体16, 帮派称谓[v["职位"]])["显示"](字体16["取图像"](字体16, 帮派称谓[v["职位"]]), 230, 15)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 在线列表:左键弹起(x, y, i, item, msg)
  帮派界面["在线选中"] = i
end



















local lixian控件 = 帮派界面["创建控件"](帮派界面, "lixian控件", 18, 110, 775, 332)
function lixian控件:重置()
  local nsf = require("SDL.图像")(732, 444)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 717, 248, true):显示(40-25, 120+27)
    __res:getPNGCC(4, 719, 281, 41, 35):显示(40-25, 120+27)
    __res:getPNGCC(4, 719+39, 281, 282, 35):显示(40-25+39, 120+27)
    __res:getPNGCC(4, 719+39, 281, 283, 35):显示(40-25+39+282, 120+27)
    __res:getPNGCC(4, 719+238, 281, 113, 35):显示(619, 120+27)
    local shu=__res:getPNGCC(4,1085, 281, 3, 35)
    shu:显示(159, 120+27)
    shu:显示(208, 120+27)
    shu:显示(312, 120+27)
    shu:显示(395, 120+27)
    shu:显示(521, 120+27)
    shu:显示(625, 120+27)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18:取图像("帮派离线玩家："):置混合(0):显示(40-25, 127-21)
    for _, v in ipairs({
      {
        name = "名称",
        dy = "名称",
        x = 71,
        y = 127
      },
      {
        name = "等级",
        dy = "等级",
        x = 166,
        y = 127
      },
      {
        name = "ID",
        dy = "id",
        x = 255,
        y = 127
      },
      {
        name = "门派",
        dy = "门派",
        x = 338,
        y = 127
      },
      {
        name = "职位",
        dy = "职位",
        x = 439,
        y = 127
      },
      {
        name = "贡献",
        dy = "贡献",
        x = 553,
        y = 127
      },
      {
        name = "离线",
        dy = "离线",
        x = 657,
        y = 127
      },
    }) do
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, v.name or "无")["显示"](字体18["取图像"](字体18, v.name or "无"), v.x, v.y+28)
    end
    nsf["渲染结束"](nsf)
  end
  帮派界面["图像"] = nsf["到精灵"](nsf)
  if #帮派界面.离线玩家~=0 then
    self.离线列表["重置"](self.离线列表)
    -- self.申请列表["重置"](self.申请列表, 帮派界面["数据"]["申请人员"])
  end
end
local 离线列表 = lixian控件["创建列表"](lixian控件, "离线列表", 0, 54+18, 711, 200)
function 离线列表:初始化()
  self:置文字(字体20)
  self.行高度 = 40
  self.行间距 = 0
end
function 离线列表:重置()
  self.清空(self)
  for _, v in ipairs(帮派界面.离线玩家) do
    local nsf = require("SDL.图像")(684, 40)
    if nsf["渲染开始"](nsf) then
      字体16["置颜色"](字体16, __取颜色("浅黑"))
      local 宽度 = 字体16["取宽度"](字体16, v.名称)
      字体16:取图像(v.名称):显示(54.0 - 宽度 / 2+18, 15)
      宽度 = 字体16["取宽度"](字体16, v.等级)
      字体16:取图像(v.等级):显示(54.0 - 宽度 / 2+109, 15)
      字体16:取图像(v.id):显示(226, 15)
      宽度 = 字体16["取宽度"](字体16, v.门派)
      字体16:取图像(v.门派):显示(54.0 - 宽度 / 2+326-42, 15)
      宽度 = 字体16["取宽度"](字体16, v.职务)
      字体16:取图像(v.职务):显示(54.0 - 宽度 / 2+429-45, 15)
      宽度 = 字体16["取宽度"](字体16, v.帮贡.当前)
      字体16:取图像(v.帮贡.当前):显示(54.0 - 宽度 / 2+552-55, 15)
      字体16:取图像(os.date("%m", v.离线时间).. "-" .. os.date("%d", v.离线时间)):显示(639, 15)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 离线列表:左键弹起(x, y, i, item, msg)
  帮派界面["离线选中"] = i
end

























local jineng控件 = 帮派界面["创建控件"](帮派界面, "jineng控件", 18, 100, 775, 360+77)
local 研究技能 = 帮派界面.jineng控件["创建我的按钮"](帮派界面.jineng控件, __res:getPNGCC(3, 511, 11, 117, 43, true),"研究技能", 48, 323-116+61-12,"研究技能")
function 研究技能:左键弹起(x, y, msg)
end
local 提升等级 = 帮派界面.jineng控件["创建我的按钮"](帮派界面.jineng控件, __res:getPNGCC(3, 511, 11, 117, 43, true),"提升等级", 48, 323-116+50+61-12,"提升等级")
function 提升等级:左键弹起(x, y, msg)
end
function jineng控件:重置()
  local nsf = require("SDL.图像")(732, 360+77)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 462, 336, true):显示(267, 103)
    取灰色背景(0, 0, 242, 238, true):显示(11, 103)
    __res:getPNGCC(4, 719, 281, 41, 35):显示(267, 103)
    __res:getPNGCC(4, 719+39, 281, 282, 35):显示(267+39, 103)
    __res:getPNGCC(4, 719+351-143, 281, 143, 35):显示(267+39+282-1, 103)
    local shu=__res:getPNGCC(4,1085, 281, 3, 35)
    shu:显示(267+166, 103)
    shu:显示(267+166+100, 103)
    shu:显示(267+166+200, 103)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18:取图像("研究经验： 29852"):显示(30, 149-37)
    字体18:取图像("升级经验： 654545"):显示(30, 149-37+25)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    for _, v in ipairs({
      {
        name = "技能",
        dy = "技能",
        x = 30+285+25,
        y = 149-37+3-32
      },
      {
        name = "当前等级",
        dy = "当前等级",
        x = 30+420,
        y = 149-37+3-32
      },
      {
        name = "上限等级",
        dy = "上限等级",
        x = 30+526-6,
        y = 149-37+3-32
      },
    }) do
      
      字体18["取图像"](字体18, v.name or "无")["显示"](字体18["取图像"](字体18, v.name or "无"), v.x, v.y+28)
    end
    nsf["渲染结束"](nsf)
  end
  帮派界面["图像"] = nsf["到精灵"](nsf)
  self.研究技能:置禁止(帮派界面.权限<4)
  self.提升等级:置禁止(帮派界面.权限<4)
  self.技能列表:重置()
end
local 技能列表 = jineng控件["创建列表"](jineng控件, "技能列表", 236+14, 54+18-40+6, 463, 300)
function 技能列表:初始化()
  self:置文字(字体22)
  self.行高度 = 45
  self.行间距 = 0
end
function 技能列表:重置()
  self.清空(self)
  for _, v in ipairs(帮派界面.帮派技能) do
    local nsf = require("SDL.图像")(684, 45)
    if nsf["渲染开始"](nsf) then
      字体16["置颜色"](字体16, __取颜色("浅黑"))
      local 宽度 = 字体16["取宽度"](字体16, v.名称)
      字体16:取图像(v.名称):显示(54.0 - 宽度 / 2+18+30-13, 15)
      字体16:取图像(v.当前):显示(167+46-3, 15)
      字体16:取图像(v.上限):显示(271+42-3, 15)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 技能列表:左键弹起(x, y, i, item, msg)
  帮派界面["技能选中"] = i
  jineng控件.技能文本:清空()
  -- 取技能(数据["名称"], self.门派)
  -- table.print(取技能(帮派界面.帮派技能[帮派界面["技能选中"]].名称))
  -- local wenben="#W"..(帮派界面.帮派技能[帮派界面["技能选中"]].当前).."\n"..(帮派界面.帮派技能[帮派界面["技能选中"]].上限).."\n".."#G"..取技能(帮派界面.帮派技能[帮派界面["技能选中"]].名称)[1]
  jineng控件.技能文本:置文本("#G"..取技能(帮派界面.帮派技能[帮派界面["技能选中"]].名称)[1])
end
-- function jineng控件:JINGYAN()--houmianzais
--   local nsf = require("SDL.图像")(239, 169)
--   if nsf["渲染开始"](nsf) then

--     nsf["渲染结束"](nsf)
--   end
--   帮派界面["图像2"] = nsf["到精灵"](nsf)
-- end
local 技能文本 = jineng控件["创建文本"](jineng控件, "技能文本", 28-12-9, 170-78, 218, 150)
function 技能文本:初始化()
end

















local jianzhu控件 = 帮派界面["创建控件"](帮派界面, "jianzhu控件", 18, 100, 775, 360+88)
local 研究建筑 = 帮派界面.jianzhu控件["创建我的按钮"](帮派界面.jianzhu控件, __res:getPNGCC(3, 511, 11, 117, 43, true),"研究建筑", 499-72+20, 323-116+61-12+39,"研究建筑")
function 研究建筑:左键弹起(x, y, msg)
  if 帮派界面["建筑选中"] and 帮派界面["建筑选中"]~=0  then
    发送数据(42,帮派界面.建筑情况[帮派界面.建筑选中])
  end
end
local 提升帮派规模 = 帮派界面.jianzhu控件["创建我的按钮"](帮派界面.jianzhu控件, __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(138,43),"提升帮派规模", 499+130-72+20, 323-116+61-12+39,"提升帮派规模")
function 提升帮派规模:左键弹起(x, y, msg)
  发送数据(43)
end
local bpxx = {"金 库 数","物价指数","书 院 数","修理指数","兽 室 数","守 护 兽","厢 房 数","帮派迷宫","药 房 数","当前内政","仓 库 数","百 草 谷","药品增量","藏 宝 室","维 护 费"}
function jianzhu控件:重置()
  local nsf = require("SDL.图像")(775, 360+88)
  if nsf["渲染开始"](nsf) then
    取属性背景(0, 0, 424, 339, true):显示(11, 103) 
    取属性背景(0, 0, 290, 250, true):显示(447, 103)
    local 行数 = 0
		local 列数 = 0
    local lssj = 取输入背景(0, 0, 92, 23)
    for i=1, #bpxx do
      字体18["置颜色"](字体18, __取颜色("白色"))
      字体18["取图像"](字体18, bpxx[i]):显示(196*行数+23, 131+列数*40)
      lssj:显示(193*行数+23+79, 131+列数*40-2)
      行数=行数+1
			if 行数==2 then
				行数 = 0
				列数 = 列数 + 1
			end
    end

    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18:取图像(帮派界面.数据.帮派建筑.金库.数量):显示(196*0+23+93, 131+0*40)
    字体18:取图像(帮派界面.数据.物价指数):显示(196*1+23+93, 131+0*40)

    字体18:取图像(帮派界面.数据.帮派建筑.书院.数量):显示(196*0+23+93, 131+1*40)
    字体18:取图像(5):显示(196*1+23+93, 131+1*40)

    字体18:取图像(帮派界面.数据.帮派建筑.兽室.数量):显示(196*0+23+93, 131+2*40)
    字体18:取图像(150):显示(196*1+23+93, 131+2*40)

    字体18:取图像(帮派界面.数据.帮派建筑.厢房.数量):显示(196*0+23+93, 131+3*40)
    字体18:取图像("21:00"):显示(196*1+23+93, 131+3*40)

    字体18:取图像(帮派界面.数据.帮派建筑.药房.数量):显示(196*0+23+93, 131+4*40)
    字体18:取图像(帮派界面.数据.当前内政):显示(196*1+23+93, 131+4*40)

    字体18:取图像(帮派界面.数据.帮派建筑.仓库.数量):显示(196*0+23+93, 131+5*40)
    字体18:取图像(0):显示(196*1+23+93, 131+5*40)

    字体18:取图像(帮派界面.数据.药品增加量):显示(196*0+23+93, 131+6*40)
    字体18:取图像(0):显示(196*1+23+93, 131+6*40)

    字体18:取图像(帮派界面.数据.当前维护费):显示(196*0+23+93, 131+7*40)



    nsf["渲染结束"](nsf)
  end
  帮派界面["图像"] = nsf["到精灵"](nsf)
  self.研究建筑:置禁止(帮派界面.权限<4)
  self.提升帮派规模:置禁止(帮派界面.权限<4)
  self.建筑网格:置物品()
end

local 建筑网格 = jianzhu控件["创建网格"](jianzhu控件, "建筑网格", 463-22, 105-92, 278, 233)
function 建筑网格:左键弹起(x, y, a, b, msg)
  if 帮派界面["建筑选中"] and 帮派界面["建筑选中"]~=0  then
    self.子控件[帮派界面["建筑选中"]]._spr["选中"] = nil
  end
  if self.子控件[a] and self.子控件[a]._spr then
    帮派界面["建筑选中"] = a
    self.子控件[a]._spr["选中"] = true
  end
end
function 建筑网格:置物品()
  local data=帮派界面.建筑情况
  self:创建格子(278, 67, 7, 7, #data, 1, true)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __建筑格子["创建"]()
      lssj["置物品"](lssj, data[i])
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end













local shenqing控件 = 帮派界面["创建控件"](帮派界面, "shenqing控件", 18, 100, 775, 360+77)
local 同意申请 = shenqing控件["创建我的按钮"](shenqing控件, __res:getPNGCC(3, 511, 11, 117, 43, true),"同意申请", 416, 323-116+50+61-15,"同意申请")
function 同意申请:左键弹起(x, y, msg)
  if 帮派界面.申请选中~=0 then
    发送数据(41,{对方数据=帮派界面.数据.申请人员[帮派界面.申请选中],类型="同意加入"})
    table.remove(帮派界面.数据.申请人员,帮派界面.申请选中)
    帮派界面.申请选中=0
    shenqing控件.申请列表:重置()
  end
end
local 清空列表 = shenqing控件["创建我的按钮"](shenqing控件, __res:getPNGCC(3, 511, 11, 117, 43, true),"清空列表", 416+137, 323-116+50+61-15,"清空列表")
function 清空列表:左键弹起(x, y, msg)
  帮派界面.数据.申请人员={}
  帮派界面.申请选中=0
  shenqing控件.申请列表:重置()
  发送数据(40)
end
function shenqing控件:重置()
  local nsf = require("SDL.图像")(732, 444)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 717, 248, true):显示(40-25, 120+27)
    __res:getPNGCC(4, 719, 281, 41, 35):显示(40-25, 120+27)
    __res:getPNGCC(4, 719+39, 281, 282, 35):显示(40-25+39, 120+27)
    __res:getPNGCC(4, 719+39, 281, 283, 35):显示(40-25+39+282, 120+27)
    __res:getPNGCC(4, 719+238, 281, 113, 35):显示(619, 120+27)
    local shu=__res:getPNGCC(4,1085, 281, 3, 35)
    shu:显示(159, 120+27)
    -- shu:显示(208, 120+27)
    shu:显示(312, 120+27)
    shu:显示(395+63, 120+27)
    shu:显示(521+43, 120+27)
    -- shu:显示(625, 120+27)
    字体18["置颜色"](字体18, __取颜色("绿色"))
    字体18:取图像("申请入帮名单"):置混合(0):显示(40-25+227+62, 127-21)
    for _, v in ipairs({
      {
        name = "名称",
        dy = "名称",
        x = 71,
        y = 127
      },
      
      {
        name = "ID",
        dy = "id",
        x = 255-29,
        y = 127
      },
      {
        name = "门派",
        dy = "门派",
        x = 338+32,
        y = 127
      },
      {
        name = "等级",
        dy = "等级",
        x = 338+101+52,
        y = 127
      },
      {
        name = "申请时间",
        dy = "申请时间",
        x = 338+101+105+52+22,
        y = 127
      },
    }) do
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, v.name or "无")["显示"](字体18["取图像"](字体18, v.name or "无"), v.x, v.y+28)
    end
    nsf["渲染结束"](nsf)
  end
  帮派界面["图像"] = nsf["到精灵"](nsf)
  self.申请列表:重置()
  -- 帮派界面.堂务管理:置可见(帮派界面.权限>=4)
  -- 帮派界面.逐出帮众:置可见(帮派界面.权限>=4)
  -- 帮派界面.脱离帮派:置可见(true)

  -- self.任命职位["置禁止"](self.任命职位, 1 ~= 帮派界面["职位"])
  -- self.逐出帮派["置禁止"](self.逐出帮派, 1 ~= 帮派界面["职位"])
  -- self.允许加入["置禁止"](self.允许加入, 1 ~= 帮派界面["职位"])
  -- self.清空列表["置禁止"](self.清空列表, 1 ~= 帮派界面["职位"])
end
local 申请列表 = shenqing控件["创建列表"](shenqing控件, "申请列表", 0, 54+18+11, 711, 200)
function 申请列表:初始化()
  self:置文字(字体20)
  self.行高度 = 40
  self.行间距 = 0
end
function 申请列表:重置()
  self.清空(self)
  for _, v in ipairs(帮派界面.数据.申请人员) do
    local nsf = require("SDL.图像")(684, 40)
    if nsf["渲染开始"](nsf) then
      字体16["置颜色"](字体16, __取颜色("黑色"))
      local 宽度 = 字体16["取宽度"](字体16, v.名称)
      字体16:取图像(v.名称):显示(54.0 - 宽度 / 2+18, 15)
      字体16:取图像(v.数字id):显示(226-30, 15)
      宽度 = 字体16["取宽度"](字体16, v.门派)
      字体16:取图像(v.门派):显示(54.0 - 宽度 / 2+326-42+30, 15)
      宽度 = 字体16["取宽度"](字体16, v.等级)
      字体16:取图像(v.等级):显示(54.0 - 宽度 / 2+326-42+30+126, 15)
      字体16:取图像(os.date("%m", v.申请时间).. "-" .. os.date("%d", v.申请时间)):显示(639-33, 15)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end
function 申请列表:左键弹起(x, y, i, item, msg)
  帮派界面["申请选中"] = i
end








local 堂务管理 = 帮派界面["创建我的按钮"](帮派界面, __res:getPNGCC(3, 511, 11, 117, 43, true),"堂务管理", 315, 402,"堂务管理")
function 堂务管理:左键弹起(x, y, msg)
  if 帮派界面.在线选中 == 0 then
    __UI弹出.提示框:打开("#Y/请选择你要任命的玩家！")
  else
    local 选项信息 = {}
    if 帮派界面.权限 == 5 then
      选项信息 = {"帮主","副帮主","左护法","右护法","长老","堂主","帮众","商人"}
    elseif 帮派界面.权限 == 4 then
      选项信息 = {"左护法","右护法","长老","堂主","帮众","商人"}
    elseif 帮派界面.权限 ==3 then
      选项信息 = {"长老","堂主","帮众","商人"}
    end
    if #选项信息>0 then
      __UI界面["窗口层"]["对话栏"]:打开("男人_兰虎","帮派总管","请选中你要任命玩家#Y"..帮派界面.在线玩家[帮派界面.在线选中].名称.."#W的职位",选项信息)
    end
  end
end
local 逐出帮众 = 帮派界面["创建我的按钮"](帮派界面, __res:getPNGCC(3, 511, 11, 117, 43, true),"逐出帮众", 315+138, 402,"逐出帮众")
function 逐出帮众:左键弹起(x, y, msg)
  if  帮派界面.zaixian控件.是否可见 then
    if 帮派界面.权限 >= 4 and 帮派界面.在线玩家[帮派界面.在线选中] then
      -- print(帮派界面.在线选中,帮派界面.在线玩家[帮派界面.在线选中].名称)
      local 事件 = function()
      发送数据(41,{对方数据=帮派界面.在线玩家[帮派界面.在线选中],类型="逐出帮派"}) --踢出帮派
      帮派界面.在线选中 = 0
      -- print(self.在线选中,self.在线玩家[self.在线选中].名称)
      end
      __UI界面.窗口层.文本栏:打开("你确定要将#Y"..帮派界面.在线玩家[帮派界面.在线选中].名称.."#R逐出帮派#W吗？", 285, 155, 390, 200, 事件)
    end
  elseif 帮派界面.lixian控件.是否可见 then
    if 帮派界面.权限 >= 4 and 帮派界面.离线玩家[帮派界面.离线选中] then
      -- print(帮派界面.离线选中,帮派界面.离线玩家[帮派界面.离线选中].名称)
      local 事件 = function()
      发送数据(41,{对方数据=帮派界面.离线玩家[帮派界面.离线选中],类型="逐出帮派"}) --踢出帮派
      帮派界面.离线选中 = 0
      -- print(self.离线选中,self.离线玩家[self.离线选中].名称)
      end
      __UI界面.窗口层.文本栏:打开("你确定要将#Y"..帮派界面.离线玩家[帮派界面.离线选中].名称.."#R逐出帮派#W吗？", 285, 155, 390, 200, 事件)
    end
  end

end
local 脱离帮派 = 帮派界面["创建我的按钮"](帮派界面, __res:getPNGCC(3, 511, 11, 117, 43, true),"脱离帮派", 315+138*2, 402,"脱离帮派")
function 脱离帮派:左键弹起(x, y, msg)
  if 帮派界面.权限 == 5 then
    local 事件 = function()
      发送数据(37)
      帮派界面["置可见"](帮派界面, false)
    end
    __UI界面.窗口层.文本栏:打开("你现在是帮派的帮主，如果你退出帮派，#Y你的帮派会因此#R解散#W，请谨慎选择，你确定要退出帮派吗？", 285, 155, 390, 200,事件)
  else
    local 事件 = function()
    发送数据(37.2)
    帮派界面["置可见"](帮派界面, false)
    end
    __UI界面.窗口层.文本栏:打开("你真的要脱离帮派吗？", 285, 155, 390, 200, 事件)
  end
end

local 关闭 = 帮派界面["创建我的按钮"](帮派界面, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 773-65, 0)
function 关闭:左键弹起(x, y, msg)
  帮派界面["置可见"](帮派界面, false)
  self.图像 = nil
  -- self.数据 = nil
end