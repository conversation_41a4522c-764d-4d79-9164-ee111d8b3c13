--[[
LastEditTime: 2024-09-17 23:50:30
--]]
local 嘉年华 = 窗口层:创建窗口("嘉年华")
function 嘉年华:初始化()
        local tcp =__res:取资源动画("pic","cjzpbj.png","图片")
        self:置宽高(tcp.宽度,tcp.高度)
        self:置精灵(tcp:到精灵())
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        self.消耗次数=文本字体:置颜色(0,0,0,255):取投影精灵("10")
        self.剩余次数=文本字体:置颜色(0,0,0,255):取投影精灵("0")

end

function 嘉年华:显示(x,y)
  self.消耗次数:显示(x+267,y+35)
  self.剩余次数:显示(x+260,y+416)
  if self.物品 then
      for i=1,2 do
        if self.物品[i]  and self.物品[i].物品 then
              self.物品[i]:显示(x+195+(i-1)*90,y+95)

        end

      end
      local xx = 0
      local yy = 0
      for i=3,6 do
          if self.物品[i]  and self.物品[i].物品 then
              self.物品[i]:显示(x+133+xx*215,y+165+yy*85)
          end
          xx =xx +1 
          if xx>=2 then
              xx=0
              yy=yy+1
          end
      end
      for i=7,8 do
        if self.物品[i]  and self.物品[i].物品 then
              self.物品[i]:显示(x+195+(i-7)*90,y+318)

        end

      end
  end
end

function 嘉年华:获得鼠标(x,y)
        if self.物品 then
            for i = 1, 8 do
                if self.物品[i] and self.物品[i].物品 and self.物品[i]:检查透明(x, y)  then
                    __UI弹出.自定义提示:打开(self.物品[i].物品,x+20,y+20)
                end
            end
        end
end


function 嘉年华:左键弹起(x,y)
  if self.物品 and __手机 then
      for i = 1, 8 do
          if self.物品[i] and self.物品[i].物品 and self.物品[i]:检查透明(x, y)  then
              __UI弹出.自定义提示:打开(self.物品[i].物品,x,y)
          end
      end
  end
end


function 嘉年华:打开(数据)
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self:刷新(数据)
end

function 嘉年华:刷新(数据)
        self.物品={}
        self.剩余次数=文本字体:置颜色(0,0,0,255):取投影精灵("0")
        if 数据.次数 then 
            self.剩余次数=文本字体:置颜色(0,0,0,255):取投影精灵(数据.次数)
        end
        if 数据.道具 then
            for i=1,8 do
                self.物品[i]=nil
                if 数据.道具[i] then
                      local lssj = __物品格子:创建()
                      lssj:置物品(数据.道具[i],50,50)
                      if lssj.物品 then
                         if 数据.道具[i].说明 and 数据.道具[i].说明~="普通" then
                            lssj.物品.介绍=数据.道具[i].说明
                         end
                         if 数据.道具[i].备注 and 数据.道具[i].备注~="无" then
                            lssj.物品.备注="#Y"..数据.道具[i].备注
                         end
                      end
                      self.物品[i]=lssj
                end
            end
        
        end



end


local 确认抽奖 =嘉年华:创建按钮("确认抽奖", 208, 175)
function 确认抽奖:初始化()
  self:创建按钮精灵(__res:取资源动画("pic","cjan.png","图片"),1)
end
function 确认抽奖:左键弹起(x, y)
      请求服务(62,{文本="抽奖一次"})
end


local 关闭 = 嘉年华:创建按钮("关闭", 330,415)
function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("pic","qdwlq.png","图片"),1)
end
function 关闭:左键弹起(x, y)
    嘉年华:置可见(false)
end



  


















