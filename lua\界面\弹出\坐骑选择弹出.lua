__UI弹出["坐骑选择弹出"] = __UI界面["创建弹出窗口"](__UI界面, "坐骑选择弹出", 339+45 + abbr.py.x, 284-30-64 + abbr.py.y, 150, 192+64)
local 坐骑选择弹出 = __UI弹出["坐骑选择弹出"]
function 坐骑选择弹出:初始化()
  self:置精灵(取黑色背景(0, 0, 150, 192+64))
end
function 坐骑选择弹出:打开(lx)
  if self.是否可见 then
    self:置可见(false)
  else
    self:置可见(true)
  end
end


for i = 1, #lsan do
  local 临时函数 = 坐骑选择弹出["创建我的按钮"](坐骑选择弹出, __res:getPNGCC(2, 368, 906, 126, 52, true), lsan[i], 12, 11 + (i - 1) * 60, lsan[i])
  function  临时函数:左键弹起(x, y, msg)
    if "临时包裹" == lsan[i] then
      发送数据(3749, {
        ["方式"] = "索取"
      })
    elseif "仓 库" == lsan[i] then
      发送数据(6701)
    elseif "一键出售" == lsan[i] then
      发送数据(3785,{shijian="一键出售"})
    elseif "整 理" == lsan[i] then
      if __UI界面["窗口层"]["道具行囊"]["是否可见"] then 
        if __UI界面["窗口层"]["道具行囊"]["包裹类型"]=="行囊" then 
          发送数据(3815,{类型="整理行囊"})
        else
          发送数据(3815,{类型="整理道具"})
        end
      end
    end
    坐骑选择弹出["置可见"](坐骑选择弹出, false)
  end
end