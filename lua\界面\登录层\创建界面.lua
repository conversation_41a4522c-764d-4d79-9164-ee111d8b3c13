local 选中
local 染色 = 0
local 角色按钮表 = {
    {
        ["模型"] = "飞燕女",
        ["染色方案"] = 3,
        ["介绍"] = "深山有佳人,灼灼芙蓉姿,飞燕女轻盈飘逸,灵慧动人,自幼怜爱弱小,嫉恶如仇,一生自由自在,是大自然骄纵的宠儿\n#Z/擅使兵器：双剑、环圈",
        ["兵器"] = "双剑\n环圈",
        ["门派"] = "大唐官府\n方寸山\n女儿村\n神木林"
    },
    {
        ["模型"] = "英女侠",
        ["染色方案"] = 4,
        ["介绍"] = "兰心惠质出名门,英姿飒爽自芳华,英女侠天资聪颖,精通琴棋书画,心怀仁爱,行善不落人后,是位侠骨柔情的奇女子\n#Z/擅使兵器：双剑、鞭",
        ["兵器"] = "双剑\n长鞭",
        ["门派"] = "大唐官府\n方寸山\n女儿村\n神木林"
    },
    {
        ["模型"] = "巫蛮儿",
        ["染色方案"] = 201,
        ["介绍"] = "嫣然巧笑踏绿萝,一路银铃一路歌,巫蛮儿质朴单纯,灵动可人,生性善良,活泼可爱,花盈翠影出神木,环佩婉转披香来\n#Z/擅使兵器：宝珠、法杖",
        ["兵器"] = "宝珠\n法杖",
        ["门派"] = "大唐官府\n方寸山\n女儿村\n神木"
    },
    {
        ["模型"] = "偃无师",
        ["染色方案"] = 205,
        ["介绍"] = "铁手隐机枢，巧夺天工，猛力执巨剑，志敌万均。偃无师性情冷厉，疏狂不羁，亦有奇谋满腹，铮铮傲骨。\n#Z/擅使兵器：剑、巨剑",
        ["兵器"] = "巨剑\n剑",
        ["门派"] = "大唐官府\n化生寺\n方寸山\n神木林"
    },
    {
        ["模型"] = "逍遥生",
        ["染色方案"] = 1,
        ["介绍"] = "快意恩仇事,把酒踏歌行,一袭白衫,一纸折扇,逍遥生风流倜傥,潇洒自如,行事光明磊落,是世人乐于结交的谦谦君子\n#Z/擅使兵器：剑、扇",
        ["兵器"] = "剑\n扇",
        ["门派"] = "大唐官府\n化生寺\n方寸山\n神木林"
    },
    {
        ["模型"] = "剑侠客",
        ["染色方案"] = 2,
        ["介绍"] = "霜刃露锋芒,飒沓如流星,剑侠客率情任性,狂放不羁,一生淡泊名利,嗜武如痴,英雄意,儿女情,独闯江湖半生醉,举杯邀月最销魂\n#Z/擅使兵器：刀、剑",
        ["兵器"] = "刀\n剑",
        ["门派"] = "大唐官府\n化生寺\n方寸山\n神木林"
    },
    {
        ["模型"] = "狐美人",
        ["染色方案"] = 7,
        ["介绍"] = "修眉连娟,斜挑入眉,媚眼如丝,含娇含笑,狐美人柔情绰态,胜似海棠醉日,风情万种,颠倒众生\n#Z/擅使兵器：爪刺、鞭",
        ["兵器"] = "爪刺\n鞭",
        ["门派"] = "盘丝洞\n阴曹地府\n魔王寨\n无底洞"
    },
    {
        ["模型"] = "骨精灵",
        ["染色方案"] = 8,
        ["介绍"] = "眉黛春山秀,横波剪秋水,骨精灵娇妍俏皮,顾盼神飞,机敏聪慧,好打不平,对世间万物充满好奇\n#Z/擅使兵器：爪刺、魔棒",
        ["兵器"] = "爪刺\n魔棒",
        ["门派"] = "盘丝洞\n阴曹地府\n魔王寨\n无底洞"
    },
    {
        ["模型"] = "鬼潇潇",
        ["染色方案"] = 206,
        ["介绍"] = "寒眸印秋水，魂隐三生途，素手执竹伞，影馀幽冥路。鬼潇潇青丝如墨，红杉如火，一对异色瞳里似乎藏着无尽的忧愁和神秘。\n#Z/擅使兵器：爪刺、伞",
        ["兵器"] = "爪刺\n伞",
        ["门派"] = "狮驼岭\n魔王寨\n阴曹地府\n无底洞"
    },
    {
        ["模型"] = "杀破狼",
        ["染色方案"] = 202,
        ["介绍"] = "一啸生风雪,长歌动寒霜,杀破狼飘逸潇洒,气宇轩昂,能文能武,卓尔不群,身具的神秘天狼血统,纵横骄天下,傲立三界间.\n#Z/擅使兵器：弓弩、宝珠",
        ["兵器"] = "弓弩\n宝珠",
        ["门派"] = "狮驼岭\n魔王寨\n阴曹地府\n无底洞"
    },
    {
        ["模型"] = "巨魔王",
        ["染色方案"] = 5,
        ["介绍"] = "一怒震乾坤,杀气凝如山,巨魔王力拔山气兮盖世,肩负魔族神秘使命,叱咤风云,威风凛凛\n#Z/擅使兵器：斧钺、刀",
        ["兵器"] = "斧钺\n刀",
        ["门派"] = "狮驼岭\n魔王寨\n阴曹地府\n无底洞"
    },
    {
        ["模型"] = "虎头怪",
        ["染色方案"] = 6,
        ["介绍"] = "戏谑犹可爱,虽有神力不欺人,虎头怪弯弧五百步,长戟八十斤,勇武过人,生性耿直豁达,对朋友忠肝义胆,是顶天立地的大丈夫\n#Z/擅使兵器：斧钺、锤",
        ["兵器"] = "斧钺\n锤",
        ["门派"] = "狮驼岭\n魔王寨\n阴曹地府\n无底洞"
    },
    {
        ["模型"] = "舞天姬",
        ["染色方案"] = 11,
        ["介绍"] = "霓裳曳广带,飘拂升天行,舞天姬明眸珠辉,瑰姿艳逸,生性善解人意,令人如沐春风.一舞绡丝动四方,观之心魂俱醉\n#Z/擅使兵器：飘带、环圈",
        ["兵器"] = "飘带\n环圈",
        ["门派"] = "天宫\n普陀山\n龙宫\n凌波城"
    },
    {
        ["模型"] = "玄彩娥",
        ["染色方案"] = 12,
        ["介绍"] = "桂露对仙娥,星星下云逗,玄彩娥在花从中蝶翼翩翩,婀娜曼妙,犹聚晨露新聚,奇花初蕊,是集天地灵气于一身的百花仙子\n#Z/擅使兵器：飘带、魔棒",
        ["兵器"] = "飘带\n魔棒",
        ["门派"] = "龙宫\n普陀山\n天宫\n凌波城"
    },
    {
        ["模型"] = "桃夭夭",
        ["染色方案"] = 204,
        ["介绍"] = "桃夭柳媚梦酣眠，笑语嫣然化春风。一朝春近晴光好，清波潋滟映芳菲，桃夭夭是蟠桃园含花吐蕊的花苞，历经三千毓秀钟灵，化身一个机灵爽朗，骄憨顽皮的少女。\n#Z/擅使兵器：飘带、灯笼",
        ["兵器"] = "飘带\n灯笼",
        ["门派"] = "天宫\n龙宫\n普陀山\n凌波城"
    },
    {
        ["模型"] = "羽灵神",
        ["染色方案"] = 203,
        ["介绍"] = "游侠红尘里,豪情动九天.羽灵神热情正直,率性豁达,游侠三界间,交友遍天下;乐见四海尽升平,愿引凤鸣遍九州\n#Z/擅使兵器：弓弩、法杖",
        ["兵器"] = "弓弩\n法杖",
        ["门派"] = "天宫\n龙宫\n五庄观\n凌波城"
    },
    {
        ["模型"] = "神天兵",
        ["染色方案"] = 9,
        ["介绍"] = "金甲腾云受天命,神枪破逆卫灵霄,神天兵风采鹰扬,锋芒毕露,守护天庭立天威,所向披靡,妖魔皆闻风丧胆\n#Z/擅使兵器：枪矛、锤",
        ["兵器"] = "枪矛\n锤",
        ["门派"] = "龙宫\n天宫\n五庄观\n凌波城"
    },
    {
        ["模型"] = "龙太子",
        ["染色方案"] = 10,
        ["介绍"] = "乘风破浪翔碧海,腾云架雾上青天,龙太子凭借天生的优势领悟仙法精髓,是当之无愧的龙族骄子,身经百战的天界战将\n#Z/擅使兵器：枪矛、扇",
        ["兵器"] = "枪矛\n扇",
        ["门派"] = "龙宫\n天宫\n五庄观\n凌波城"
    },
   {
        ["模型"] = "影精灵",
        ["染色方案"] = 10,
        ["介绍"] = "乘风破浪翔碧海,腾云架雾上青天,龙太子凭借天生的优势领悟仙法精髓,是当之无愧的龙族骄子,身经百战的天界战将\n#Z/擅使兵器：枪矛、扇",
        ["兵器"] = "枪矛\n扇",
        ["门派"] = "龙宫\n天宫\n五庄观\n凌波城"
    }
}
local 创建界面 = __UI界面["登录层"]["创建控件"](__UI界面["登录层"], "创建界面",
    0 + abbr.py2.x, 0 + abbr.py2.y, 960, 540)
function 创建界面:初始化()
    self.创建角色 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 3018340204))
    self.人魔仙 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 2763615374))
    self.未选择人物背景 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 901660716))
    self.未选择人物 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 2684911458))
    self.已选择头像背景 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 2181883247))
    self.头像框大图背景 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 10559827))
    self.姓名框 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 463628453))
    self.染色切换 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 1553175592))
    self.影子 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 3705976162))
    self.人物说明框 = __res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 2647271957))
end

function 创建界面:更新(dt)
    if self.模型 then
        self.模型["更新"](self.模型, dt)
    end
end

function 创建界面:显示(x, y)
    self.创建角色["显示"](self.创建角色, 670 + abbr.py2.x, 65 + abbr.py2.y)
    self.人魔仙["显示"](self.人魔仙, 900 + abbr.py2.x, 180 + abbr.py2.y)
    self.头像框大图背景["显示"](self.头像框大图背景, 376 + abbr.py2.x, 122 + abbr.py2.y+30)
    self.姓名框["显示"](self.姓名框, 73 + abbr.py2.x, 352 + abbr.py2.y)
    self.染色切换["显示"](self.染色切换, 77 + abbr.py2.x, 471 + abbr.py2.y)
    if not 选中 then
        self.未选择人物背景["显示"](self.未选择人物背景, 75 + abbr.py2.x, 82 + abbr.py2.y)
        self.未选择人物["显示"](self.未选择人物, 75 + abbr.py2.x, 95 + abbr.py2.y)
    else
        self.已选择头像背景["显示"](self.已选择头像背景, 76 + abbr.py2.x, 71 + abbr.py2.y)
    end
    self.影子["显示"](self.影子, 174 + abbr.py2.x, 485 + abbr.py2.y)
    self.人物说明框["显示"](self.人物说明框, 396 + abbr.py2.x, 428 + abbr.py2.y)
    if self.半身 then
        if 选中 then
            if 选中 == 19 then
                self.半身["显示"](self.半身, 71 + abbr.py2.x, 124 + abbr.py2.y)
            else
                self.半身["显示"](self.半身, 171 + abbr.py2.x, 324 + abbr.py2.y)
            end
        end
    end
    if self.模型 then
        self.模型["显示"](self.模型, 160 + abbr.py2.x, 485 + abbr.py2.y)
    end
end

local 染色控件 = 创建界面["创建控件"](创建界面, "染色控件", 117, 489, 130, 30)
function 染色控件:初始化()
end

local 染色按钮表 = {
    {
        "一号染色",
        __res["取地址"](__res, "shape/ui/cj/", 3811101460)
    },
    {
        "二号染色",
        __res["取地址"](__res, "shape/ui/cj/", 1473958280)
    },
    {
        "三号染色",
        __res["取地址"](__res, "shape/ui/cj/", 408784279)
    }
}
for i = 1, 3 do
    local 临时函数名 = 染色控件["创建我的was单选按钮"](染色控件, 染色按钮表[i][2], 染色按钮表
        [i][1], 0 + 46 * (i - 1), 0)
    if 1 == i then
        临时函数名["置选中"](临时函数名, true)
    end
    function 临时函数名:左键按下(消息, x, y)
        if 消息 and 选中 then
            print(染色按钮表[i][1])
            染色 = i - 1
            local lssj = 取模型(角色按钮表[选中]["模型"])
            local lsmx = __res["取动画"](__res, "shape/mx/" .. string.sub(string.format("%#x", lssj[1]), 3) .. ".tcp")
            __UI界面["登录层"]["创建界面"]["模型"] = lsmx["取动画"](lsmx, 5)["播放"](lsmx["取动画"
                ](lsmx, 5), true)
        end
    end
end
local 动作控件 = 创建界面["创建控件"](创建界面, "动作控件", 240, 386, 50, 105)
function 动作控件:初始化()
end

local 动作按钮表 = {
    {
        "站立",
        __res["取地址"](__res, "shape/ui/cj/", 1953522549)
    },
    {
        "奔跑",
        __res["取地址"](__res, "shape/ui/cj/", 1555459698)
    },
    {
        "攻击",
        __res["取地址"](__res, "shape/ui/cj/", 3531451090)
    },
    {
        "施法",
        __res["取地址"](__res, "shape/ui/cj/", 3102462437)
    }
}
for i = 1, 4 do
    local 临时函数名 = 动作控件["创建我的was单选按钮"](动作控件, 动作按钮表[i][2], 动作按钮表
        [i][1], 0, 0 + 26 * (i - 1))
    if 1 == i then
        临时函数名["置选中"](临时函数名, true)
    end
    function 临时函数名:左键按下(消息, x, y)
        if 消息 then
            print(动作按钮表[i][1])
        end
    end
end
local 详情控件 = 创建界面["创建控件"](创建界面, "详情控件", 394, 399, 276, 36)
function 详情控件:初始化()
end

local 详情按钮表 = {
    {
        "介绍",
        __res["取地址"](__res, "shape/ui/cj/", 2663164568)
    },
    {
        "兵器",
        __res["取地址"](__res, "shape/ui/cj/", 358197109)
    },
    {
        "门派",
        __res["取地址"](__res, "shape/ui/cj/", 4038530496)
    }
}
for i = 1, 3 do
    local 临时函数名 = 详情控件["创建我的was单选按钮"](详情控件, 详情按钮表[i][2], 详情按钮表
        [i][1], 0 + 80 * (i - 1), 0)
    if 1 == i then
        临时函数名["置选中"](临时函数名, true)
    end
    function 临时函数名:左键按下(消息, x, y)
        if 消息 then
            print(详情按钮表[i][1])
        end
    end
end
local 角色控件 = 创建界面["创建控件"](创建界面, "角色控件", 397, 138, 520, 270)
function 角色控件:初始化()
end

local pyx = 1
local pyy = 1
for i = 1, 18 do
    local 临时函数名 = 角色控件["创建单选按钮"](角色控件, 角色按钮表[i]["模型"],
        0 + 74 * (pyx - 1), 0 + 74 * (pyy - 1)+40)
    function 临时函数名:初始化()
        local lssj = 取头像(角色按钮表[i]["模型"])
        self:置精灵(__res["取精灵"](__res, "shape/mx/" .. string.sub(string.format("%#x", lssj[5]), 3) .. ".tcp"))
        self:置正常精灵(__res["取精灵"](__res,
            "shape/mx/" .. string.sub(string.format("%#x", lssj[5]), 3) .. ".tcp"))
        self:置按下精灵(__res["取精灵"](__res,
            "shape/mx/" .. string.sub(string.format("%#x", lssj[5]), 3) .. ".tcp"))
        self:置选中正常精灵(__res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 937255917)))
    end

    function 临时函数名:左键按下(消息, x, y)
        if 消息 then
            选中 = i
            染色 = 0
          --  print(角色按钮表[i]["模型"])
            local lssj = 取头像(角色按钮表[i]["模型"])
            __UI界面["登录层"]["创建界面"]["半身"] = __res["取精灵"](__res,
                "shape/mx/" .. string.sub(string.format("%#x", lssj[6]), 3) .. ".tcp")
            lssj = 取模型(角色按钮表[i]["模型"])
            print(string.sub(string.format("%#x", lssj[1]), 3))
            __UI界面["登录层"]["创建界面"]["模型"] = __res["取动画"](__res,
                "shape/mx/" .. string.sub(string.format("%#x", lssj[1]), 3) .. ".tcp")["取动画"](__res["取动画"](__res
                , "shape/mx/" .. string.sub(string.format("%#x", lssj[1]), 3) .. ".tcp"), 5)["播放"](__res["取动画"
                ](__res, "shape/mx/" .. string.sub(string.format("%#x", lssj[1]), 3) .. ".tcp")["取动画"](__res["取动画"
                    ](__res, "shape/mx/" .. string.sub(string.format("%#x", lssj[1]), 3) .. ".tcp"), 5), true)
        end
    end

    pyx = pyx + 1
    if 7 == pyx then
        pyx = 1
        pyy = pyy + 1
    end
end

local x=200
if 九黎城 then
    x=0
end
local 临时函数名 = 角色控件["创建单选按钮"](角色控件, 角色按钮表[19]["模型"], 430+x,  110)
function 临时函数名:初始化()
    local lssj = 取头像(角色按钮表[19]["模型"])
    self:置精灵(__res["取精灵"](__res, __res["取地址"](__res, "shape/mx/", lssj[5])))
    self:置正常精灵(__res["取精灵"](__res, __res["取地址"](__res, "shape/mx/", lssj[5])))
    self:置按下精灵(__res["取精灵"](__res, __res["取地址"](__res, "shape/mx/", lssj[5])))
    self:置选中正常精灵(__res["取精灵"](__res, __res["取地址"](__res, "shape/ui/cj/", 937255917)))
end
function 临时函数名:左键按下(消息, x, y)
    if 消息 then
        选中 = 19
        染色 = 0
        local lssj = 取头像(角色按钮表[19]["模型"])
        __UI界面["登录层"]["创建界面"]["半身"] = __res["取精灵"](__res, __res["取地址"](__res, "shape/mx/", lssj[6]))
        lssj = 取模型(角色按钮表[19]["模型"])
        __UI界面["登录层"]["创建界面"]["模型"] = __res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["置循环"](__res["取动画2"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), true)
        -- __UI界面["登录层"]["创建界面"]["模型"] = __res["取动画"](__res,__res["取地址"](__res, "shape/mx/", lssj[1]))["取动画"](__res["取动画"](__res
        --     , "shape/mx/" .. string.sub(string.format("%#x", lssj[1]), 3) .. ".tcp"), 5)["播放"](__res["取动画"
        --     ](__res, "shape/mx/" .. string.sub(string.format("%#x", lssj[1]), 3) .. ".tcp")["取动画"](__res["取动画"
        --         ](__res, "shape/mx/" .. string.sub(string.format("%#x", lssj[1]), 3) .. ".tcp"), 5), true)
    end
end
local 方向左 = 创建界面["创建我的was按钮"](创建界面, __res["取地址"](__res, "shape/ui/cj/", 363896609)
    , "方向左", 82, 414)
function 方向左:左键弹起(x, y, msg)
end

local 方向右 = 创建界面["创建我的was按钮"](创建界面, __res["取地址"](__res, "shape/ui/cj/", 721017153)
    , "方向右", 208, 414)
function 方向右:左键弹起(x, y, msg)
end

local 说明上翻 = 创建界面["创建我的was按钮"](创建界面, __res["取地址"](__res, "shape/ui/cj/",
    4248658418), "说明上翻", 646, 429)
function 说明上翻:左键弹起(x, y, msg)
end

local 说明下翻 = 创建界面["创建我的was按钮"](创建界面, __res["取地址"](__res, "shape/ui/cj/",
    153189907), "说明下翻", 646, 486)
function 说明下翻:左键弹起(x, y, msg)
end

local 名称输入 = 创建界面["创建输入"](创建界面, "名称输入", 130, 365, 90, 18)
function 名称输入:初始化()
    self.取光标精灵(self)
    self:置限制字数(8)
    self:置颜色(255, 255, 255, 255)
end

local 上一步 = 创建界面["创建我的was按钮"](创建界面, "shape/ui/xj/611107AA.tcp", "上一步", 758, 423)
function 上一步:左键弹起(x, y, msg)
    if nil ~= 选中 then
    end
end

local 下一步 = 创建界面["创建我的was按钮"](创建界面, "shape/ui/xj/51A45362.tcp", "下一步", 758, 471)
function 下一步:左键弹起(x, y, msg)
    if nil ~= 选中 and 名称输入["取文本"](名称输入) ~= "" then
        发送数据(3, {角色按钮表[选中]["模型"]..fgc..名称输入["取文本"](名称输入)..fgc..染色..fgc..__全局账号})
    end
end
