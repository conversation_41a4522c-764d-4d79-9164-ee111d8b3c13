local VIP赞助 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "VIP赞助", 138 + abbr.py.x, 17 + abbr.py.y, 695, 496)
function VIP赞助:初始化()
  local nsf = require("SDL.图像")(695, 496)
  if nsf["渲染开始"](nsf) then
    置窗口背景("VIP赞助", 0, 12, 686, 435, true)["显示"](置窗口背景("VIP赞助", 0, 12, 686, 435, true), 0, 0)
    取白色背景(0, 0, 650, 286, true)["显示"](取白色背景(0, 0, 650, 286, true), 17, 106)
    -- __res:getPNGCC(4, 540, 51, 467, 89)["显示"](__res:getPNGCC(4, 540, 51, 467, 89), 23, 395)
    字体18["置颜色"](字体18, __取颜色("白色"))
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
local data
local vip数据
function VIP赞助:打开(数据)
  self:置可见(true)
  data= 数据
  self.vip=1

  self.奖励物品 = {
    [1] = {"修炼果", "九转金丹礼包", "60灵饰礼包", "120无级别礼包", "月卡"},
    [2] = {"神兜兜", "120无级别礼包", "80灵饰礼包", "大金砖", "特殊兽决·碎片"},
    [3] = {"灵兜兜", "神兜兜", "130无级别礼包", "特殊兽决·碎片", "100灵饰礼包"},
    [4] = {"140无级别礼包", "灵兜兜", "特殊技能", "120灵饰礼包", "红孩儿法系"},
    [5] = {"鲲鹏法系", "150无级别礼包", "灵兜兜", "特殊技能", "140灵饰礼包"},
    [6] = {"160无级别礼包", "鲲鹏物理", "特殊技能", "160灵饰礼包", "和田玉"},
    [7] = {"包子", "包子", "包子", "包子", "包子"}, --7
    [8] = {"包子", "包子", "包子", "包子", "包子"}, --7
    [9] = {"包子", "包子", "包子", "包子", "包子"}, --7
    [10] = {"包子", "包子", "包子", "包子", "包子"}, --7
}
  self.奖励数量 = {
    [1] = {99, 10, 1, 1, 1},
    [2] = {99, 1, 1, 3, 50},
    [3] = {1, 99, 1, 50, 1},
    [4] = {1, 2, 1, 1, 1},
    [5] = {1, 1, 3, 3, 1},
    [6] = {1, 1, 5, 1, 50},
    [7] = {0, 0, 0, 0, 0},
    [8] = {0, 0, 0, 0, 0},
    [9] = {0, 0, 0, 0, 0},
    [10] = {0, 0, 0, 0, 0},
  }


--   VIP赞助["自动抓鬼"]["置可见"](VIP赞助["自动抓鬼"], VIP赞助["自动抓鬼"], not VIP赞助["自动抓鬼"])
VIP赞助["自动抓鬼"]["重置"](VIP赞助["自动抓鬼"])
VIP赞助["自动抓鬼"]["置可见"](VIP赞助["自动抓鬼"],VIP赞助["自动抓鬼"]== VIP赞助["自动抓鬼"], not VIP赞助["自动抓鬼"])
VIP赞助["赞助界面"]["置可见"](VIP赞助["赞助界面"],VIP赞助["赞助界面"], false)
VIP赞助["一键强化"]["置可见"](VIP赞助["一键强化"],VIP赞助["一键强化"], false)
VIP赞助["CDK兑换面板"]["置可见"](VIP赞助["CDK兑换面板"],VIP赞助["CDK兑换面板"], false)


end


function VIP赞助:重置vip数据(数据)
vip数据=数据
VIP赞助.vip=vip数据.vip.等级
VIP赞助["赞助界面"]["重置"](VIP赞助["赞助界面"],vip数据.vip.等级)
VIP赞助["自动抓鬼"]["置可见"](VIP赞助["自动抓鬼"],VIP赞助["自动抓鬼"], false)
VIP赞助["一键强化"]["置可见"](VIP赞助["一键强化"],VIP赞助["一键强化"], false)
VIP赞助["CDK兑换面板"]["置可见"](VIP赞助["CDK兑换面板"],VIP赞助["CDK兑换面板"], false)
VIP赞助["赞助界面"]["置可见"](VIP赞助["赞助界面"],VIP赞助["赞助界面"]== VIP赞助["赞助界面"], not VIP赞助["赞助界面"])
end
function VIP赞助:重置vip数据1(数据)
vip数据.vip=数据
VIP赞助.vip=vip数据.vip.等级
VIP赞助["赞助界面"]["重置"](VIP赞助["赞助界面"],vip数据.vip.等级)
VIP赞助["自动抓鬼"]["置可见"](VIP赞助["自动抓鬼"],VIP赞助["自动抓鬼"], false)
VIP赞助["一键强化"]["置可见"](VIP赞助["一键强化"],VIP赞助["一键强化"], false)
VIP赞助["CDK兑换面板"]["置可见"](VIP赞助["CDK兑换面板"],VIP赞助["CDK兑换面板"], false)
VIP赞助["赞助界面"]["置可见"](VIP赞助["赞助界面"],VIP赞助["赞助界面"]== VIP赞助["赞助界面"], not VIP赞助["赞助界面"])
end

local 一键强化 = VIP赞助["创建我的控件"](VIP赞助, "一键强化", 0, 0, 650, 486)
function 一键强化:初始化()
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    字体25["置颜色"](字体25, __取颜色("浅黑"))
    字体25["取图像"](字体25, "一键使用18个强化符")["显示"](字体25["取图像"](字体25, "一键使用18个强化符"), 230, 140)
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18["取图像"](字体18, "[主角]        需要500W银子")["显示"](字体18["取图像"](字体18, "[主角]        需要500W银子"), 150, 180)
    -- 字体18["取图像"](字体18, "[助战1]      需要500W银子")["显示"](字体18["取图像"](字体18, "[助战1]      需要500W银子"), 150, 180+30)
    -- 字体18["取图像"](字体18, "[助战2]      需要500W银子")["显示"](字体18["取图像"](字体18, "[助战2]      需要500W银子"), 150, 180+30*2)
    -- 字体18["取图像"](字体18, "[助战3]      需要500W银子")["显示"](字体18["取图像"](字体18, "[助战3]      需要500W银子"), 150, 180+30*3)
    -- 字体18["取图像"](字体18, "[助战4]      需要2500W银子")["显示"](字体18["取图像"](字体18, "[助战4]      需要2500W银子"), 150, 180+30*4)
    字体18["置颜色"](字体18, __取颜色("红色"))
    -- 字体18["取图像"](字体18, "[全部]        需要9000W银子")["显示"](字体18["取图像"](字体18, "[全部]        需要9000W银子"), 150, 180+30*5)
    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end

local 主角确定 = 一键强化["创建我的按钮"](一键强化, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 100, 22), "主角确定", 450, 165+15, "确定")
function 主角确定:左键弹起(x, y, msg)
  发送数据(179,{数据序列=4,参数=1})
end
-- local 助战1确定 = 一键强化["创建我的按钮"](一键强化, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 100, 22), "助战1确定", 450, 165+15+30, "确定")
-- function 助战1确定:左键弹起(x, y, msg)
--   发送数据(179,{数据序列=4,参数=2})
-- end
-- local 助战2确定 = 一键强化["创建我的按钮"](一键强化, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 100, 22), "助战2确定", 450, 165+15+30*2, "确定")
-- function 助战2确定:左键弹起(x, y, msg)
--   发送数据(179,{数据序列=4,参数=3})
-- end
-- local 助战3确定 = 一键强化["创建我的按钮"](一键强化, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 100, 22), "助战3确定", 450, 165+15+30*3, "确定")
-- function 助战3确定:左键弹起(x, y, msg)
--   发送数据(179,{数据序列=4,参数=4})
-- end
-- local 助战4确定 = 一键强化["创建我的按钮"](一键强化, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 100, 22), "助战4确定", 450, 165+15+30*4, "确定")
-- function 助战4确定:左键弹起(x, y, msg)
--   发送数据(179,{数据序列=4,参数=5})
-- end
-- local 全部确定 = 一键强化["创建我的按钮"](一键强化, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 100, 22), "全部确定", 450, 165+15+30*5, "确定")
-- function 全部确定:左键弹起(x, y, msg)
--   发送数据(179,{数据序列=4,参数=6})
-- end
local 赞助界面 = VIP赞助["创建我的控件"](VIP赞助, "赞助界面", 0, 0, 650, 486)
function 赞助界面:初始化()
  self.格子背景={}
  for i = 1, 5 do
  self.格子背景[i] = __res:getPNGCC(3, 133, 507, 56, 56)["到精灵"]((__res:getPNGCC(3, 133, 507, 56, 56)))
  end
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(1,920,400,200,50):拉伸(635,270):显示(30,117)
    nsf["渲染结束"](nsf)
    end
  self:置精灵(nsf["到精灵"](nsf))
end
function 赞助界面:重置(vip)
  local nsf = require("SDL.图像")(750, 550)
  if nsf["渲染开始"](nsf) then
    -- __res:getPNGCC(1,920,400,200,50):拉伸(635,270):显示(30,117)
    字体25["置颜色"](字体25, __取颜色("红色"))
    字体18["置颜色"](字体18, __取颜色("红色"))
    字体18["取图像"](字体18, "充值后10分钟内将发放邮箱，自动到账，超时请联系管理员")["显示"](字体18["取图像"](字体18, "充值后5分钟内将发放邮箱，自动到账，超时请联系管理员"), 100, 120)
    字体25["取图像"](字体25, "当前展示等级:VIP"..VIP赞助.vip+1)["显示"](字体25["取图像"](字体25, "当前展示等级:VIP"..VIP赞助.vip+1), 100, 150)
  字体25["置颜色"](字体25, __取颜色("黄色"))
  字体25["取图像"](字体25, "当前vip等级:VIP"..vip数据.vip.等级)["显示"](字体25["取图像"](字体25, "当前vip等级:VIP"..vip数据.vip.等级), 100, 180)
  字体25["取图像"](字体25, "当前vip经验:"..vip数据.vip.当前经验)["显示"](字体25["取图像"](字体25, "当前vip经验:"..vip数据.vip.当前经验), 100, 180+110)
  字体25["取图像"](字体25, "距离升级所需:"..(vip数据.vip.升级经验-vip数据.vip.当前经验))["显示"](字体25["取图像"](字体25, "距离升级所需:"..(vip数据.vip.升级经验-vip数据.vip.当前经验)), 100, 220+110)
    -- print(vip)
  for i = 1,#VIP赞助.奖励物品[VIP赞助.vip+1] do
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18["取图像"](字体18, ""..VIP赞助.奖励数量[VIP赞助.vip+1][i])["显示"](字体18["取图像"](字体18, ""..VIP赞助.奖励数量[VIP赞助.vip+1][i]), 13+i*100, 210)

  end
  nsf["渲染结束"](nsf)
  end
self.图像1 = nsf["到精灵"](nsf)

local 道具名称={}
for n=1,#VIP赞助.奖励物品[VIP赞助.vip+1] do
  道具名称[n]={}
  道具名称[n].名称=VIP赞助.奖励物品[VIP赞助.vip+1][n]
  VIP赞助["赞助界面"]["道具网格"]["置物品"](VIP赞助["赞助界面"]["道具网格"],道具名称)
 end
end

local 领取奖励 = 赞助界面["创建我的按钮"](赞助界面, __res:getPNGCC(3, 880, 331, 86, 37, true), "领取奖励", 45-28+120+350, 155+273-110,"领取奖励")
function 领取奖励:左键弹起(x, y, msg)
  发送数据(179,{数据序列=3,参数=vip数据.vip.等级+1})
end
local 下一等级 = 赞助界面["创建我的按钮"](赞助界面, __res:getPNGCC(2, 1083, 408, 40, 65, true), "下一等级", 45-28+120+450, 155+273-110-85)
function 下一等级:左键弹起(x, y, msg)
    -- 发送数据(179,{数据序列=5})
     VIP赞助.vip=VIP赞助.vip+1

    if VIP赞助.vip>5 then
      VIP赞助.vip=5
    end
    VIP赞助["赞助界面"]["重置"](VIP赞助["赞助界面"],VIP赞助.vip)
end
local 上一等级 = 赞助界面["创建我的按钮"](赞助界面, __res:getPNGCC(2, 1138, 478, 40, 65, true), "上一等级", 45-28+120-70, 155+273-110-85)
function 上一等级:左键弹起(x, y, msg)
  VIP赞助.vip=VIP赞助.vip-1
  if VIP赞助.vip<0 then
    VIP赞助.vip=0
  end
  VIP赞助["赞助界面"]["重置"](VIP赞助["赞助界面"],VIP赞助.vip)
end
function 赞助界面:显示(x,y)
  for i = 1,#VIP赞助.奖励物品[VIP赞助.vip+1] do
    self.格子背景[i]["显示"](self.格子背景[i], x + 14 + i*100, y + 53+160+25)
  end
  if self.图像1 then
  self.图像1["显示"](self.图像1, x, y+25)
  end
end

local 道具网格 = 赞助界面["创建网格"](赞助界面, "道具网格", 115,218+25, 800, 272)
function 道具网格:初始化()
  self:创建格子(57, 57, 0, 43, 1, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
    if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
        if not self.子控件[a]._spr["确定"] then
        --  self.子控件[a]._spr["确定"] = true
        -- __UI弹出.提示框:打开('#Y双击即可使用物品')
         self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 70, 86, w, h, "选择", a)
        else
          发送数据(178,{参数=助战编号,数据序列=11,编号=a})
        end
    end
end
function 道具网格:置物品(数据)
  for i = 1, #道具网格["子控件"] do
    if 数据[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, 数据[i], nil, "道具行囊")
      -- lssj["置偏移"](lssj, 10, 10)
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i], lssj)

    else
      道具网格["子控件"][i]["置精灵"](道具网格["子控件"][i])
    end
  end
end



local 自动抓鬼 = VIP赞助["创建我的控件"](VIP赞助, "自动抓鬼", 0, 0, 650, 486)
function 自动抓鬼:初始化()
  local nsf = require("SDL.图像")(650, 550)
  self:置精灵(nsf["到精灵"](nsf))
end
function 自动抓鬼:重置()
        local nsf = require("SDL.图像")(695, 496)
        if nsf["渲染开始"](nsf) then
            __res:getPNGCC(1, 1072, 10, 40, 140)["显示"](__res:getPNGCC(1, 1072, 10, 40, 140), 77+247, 110)
            字体25["置颜色"](字体25, __取颜色("黑色"))
            字体18["置颜色"](字体18, __取颜色("黑色"))
            字体25["取图像"](字体25, "自动抓鬼")["显示"](字体25["取图像"](字体25, "自动抓鬼"), 140, 160)
            字体25["取图像"](字体25, "自动鬼王")["显示"](字体25["取图像"](字体25, "自动鬼王"), 440, 160)
            字体18["取图像"](字体18, "每日次数：500")["显示"](字体18["取图像"](字体18, "每日次数：300"), 140, 160+40)
            字体18["取图像"](字体18, "每日次数：500")["显示"](字体18["取图像"](字体18, "每日次数：300"), 440, 160+40)
            字体18["取图像"](字体18, "剩余次数："..data.自动抓鬼.次数)["显示"](字体18["取图像"](字体18, "剩余次数："..data.自动抓鬼.次数), 140, 160+80)
            字体18["取图像"](字体18, "剩余次数："..data.自动鬼王.次数)["显示"](字体18["取图像"](字体18, "剩余次数："..data.自动鬼王.次数), 440, 160+80)
	        if data.自动抓鬼.开启==false then
            字体18["置颜色"](字体18, __取颜色("红色"))
            字体18["取图像"](字体18, "是否开通：未开通")["显示"](字体18["取图像"](字体18, "是否开通：未开通"), 140, 160+120)
	        else
            字体18["置颜色"](字体18, __取颜色("绿色"))
            字体18["取图像"](字体18, "是否开通：已开通")["显示"](字体18["取图像"](字体18, "是否开通：已开通"), 140, 160+120)
            end
            if data.自动鬼王.开启==false then
            字体18["置颜色"](字体18, __取颜色("红色"))
            字体18["取图像"](字体18, "是否开通：未开通")["显示"](字体18["取图像"](字体18, "是否开通：未开通"), 440, 160+120)
	        else
            字体18["置颜色"](字体18, __取颜色("绿色"))
            字体18["取图像"](字体18, "是否开通：已开通")["显示"](字体18["取图像"](字体18, "是否开通：已开通"), 440, 160+120)
            end
            nsf["渲染结束"](nsf)
        end
        self.图像 = nsf["到精灵"](nsf)
end

function 自动抓鬼:显示(x,y)
    if self.图像 then
    self.图像["显示"](self.图像, x, y)
    end
end

local 前往抓鬼 = 自动抓鬼["创建我的按钮"](自动抓鬼, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 130, 40), "前往抓鬼", 45-28+120, 155+265-110, "自动抓鬼")
function 前往抓鬼:左键弹起(x, y, msg)
    发送数据(179,{数据序列=5})
end
local 前往鬼王 = 自动抓鬼["创建我的按钮"](自动抓鬼, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 130, 40), "前往鬼王", 345-28+125, 155+265-110, "自动鬼王")
function 前往鬼王:左键弹起(x, y, msg)
    发送数据(179,{数据序列=6})
end

local 关闭 = VIP赞助["创建我的按钮"](VIP赞助, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 645, 0)
function 关闭:左键弹起(x, y, msg)
  VIP赞助["置可见"](VIP赞助, false)
end

for i, v in ipairs({

  {
    name = "自动抓鬼1",
    x = 182+30,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 140, 35),
    font = "自动抓鬼"
  },
  -- {
  --   name = "自动鬼王",
  --   x = 182,
  --   y = 56,
  --   tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 152, 43),
  --   tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 152, 43),
  --   font = "自动鬼王"
  -- },
  {
    name = "赞助介绍",
    x = 182+165+30,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 140, 35),
    font = "赞助介绍"
  },
  {
    name = "一键附魔",
    x = 182+330+15,
    y = 56,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 140, 35),
    font = "一键附魔"
  },
 {
    name = "每日签到",
    x = 42,
    y = 64+335,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 140, 35),
    font = "每日签到"
  },
  {
    name = "购买赞助",
    x = 212,
    y = 64+335,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 140, 35),
    font = "购买赞助"
  },
  {
    name = "物品回收",
    x = 182+165+30,
    y = 64+335,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 140, 35),
    font = "物品回收"
  },
  {
    name = "CDK兑换",
    x = 182+330+15,
    y = 64+335,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 35),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 140, 35),
    font = "CDK兑换"
  }
}) do
  local 临时函数 = VIP赞助["创建我的单选按钮"](VIP赞助, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "自动抓鬼1" or  v.name == "自动鬼王"  then
        VIP赞助["自动抓鬼"]["重置"](VIP赞助["自动抓鬼"])
        VIP赞助["赞助界面"]["置可见"](VIP赞助["赞助界面"],VIP赞助["赞助界面"], false)
        VIP赞助["一键强化"]["置可见"](VIP赞助["一键强化"],VIP赞助["一键强化"], false)
        VIP赞助["自动抓鬼"]["置可见"](VIP赞助["自动抓鬼"],VIP赞助["自动抓鬼"]== VIP赞助["自动抓鬼"], not VIP赞助["自动抓鬼"])
        VIP赞助["CDK兑换面板"]["置可见"](VIP赞助["CDK兑换面板"],VIP赞助["CDK兑换面板"], false)

      elseif    v.name == "赞助介绍"  then
      -- VIP赞助["赞助界面"]["重置"](VIP赞助["赞助界面"])
      -- VIP赞助["自动抓鬼"]["置可见"](VIP赞助["自动抓鬼"],VIP赞助["自动抓鬼"], false)
      -- VIP赞助["赞助界面"]["置可见"](VIP赞助["赞助界面"],VIP赞助["赞助界面"]== VIP赞助["赞助界面"], not VIP赞助["赞助界面"])
      发送数据(179,{数据序列=1})
    elseif    v.name == "一键附魔"  then
      VIP赞助["赞助界面"]["置可见"](VIP赞助["赞助界面"],VIP赞助["赞助界面"], false)
      VIP赞助["自动抓鬼"]["置可见"](VIP赞助["自动抓鬼"],VIP赞助["自动抓鬼"], false)
      VIP赞助["一键强化"]["置可见"](VIP赞助["一键强化"],VIP赞助["一键强化"]== VIP赞助["一键强化"], not VIP赞助["一键强化"])
      VIP赞助["CDK兑换面板"]["置可见"](VIP赞助["CDK兑换面板"],VIP赞助["CDK兑换面板"], false)

    elseif    v.name == "每日签到"  then
      发送数据(120,{文本= "七日签到"})
      __UI界面["窗口层"]["VIP赞助"]["置可见"](__UI界面["窗口层"]["VIP赞助"], false)

    elseif    v.name == "购买赞助"  then
         local SDL = require('SDL')
         SDL.OpenURL(赞助地址)
    elseif    v.name == "CDK兑换"  then
   --   VIP赞助["赞助界面"]["置可见"](VIP赞助["赞助界面"],VIP赞助["赞助界面"], false)
   --   VIP赞助["自动抓鬼"]["置可见"](VIP赞助["自动抓鬼"],VIP赞助["自动抓鬼"], false)
  --    VIP赞助["一键强化"]["置可见"](VIP赞助["一键强化"],VIP赞助["一键强化"], false)
    --  VIP赞助["CDK兑换面板"]["置可见"](VIP赞助["CDK兑换面板"],VIP赞助["CDK兑换面板"]== VIP赞助["CDK兑换面板"], false)
    __UI界面["窗口层"].CDK兑换:打开()

    elseif    v.name == "物品回收"  then
      发送数据(179,{数据序列=10})
      __UI界面["窗口层"]["VIP赞助"]["置可见"](__UI界面["窗口层"]["VIP赞助"], false)
    -- else

    --     __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y正在逐渐完善中，请稍作等待！")
    end
  end
end

local CDK兑换面板 = VIP赞助["创建控件"](VIP赞助, "CDK兑换面板", 0, 0, 773, 483)
function CDK兑换面板:初始化()
  local CDK输入 = VIP赞助.CDK兑换面板["创建输入"](VIP赞助.CDK兑换面板, "CDK输入", 252, 148, 145, 30)
function CDK输入:初始化()
    self.取光标精灵(self)
    self:置限制字数(160)
    self:置颜色(255, 255, 255, 255)
    self:置文本("请输入你的CDK")
end
  local 兑换CDK按钮 = VIP赞助.CDK兑换面板["创建我的按钮"](VIP赞助.CDK兑换面板, __res:getPNGCC(3, 511, 11, 58, 35, true), "兑换CDK按钮", 388, 220,"兑换")
  function 兑换CDK按钮:左键弹起(x, y, msg)
    if CDK输入["取文本"](CDK输入) ~= "" then
    local CDK=CDK输入["取文本"](CDK输入)
    发送数据(100.1,{卡号=CDK})
    end
  end
  兑换CDK按钮:置可见(false,false)

  self:置可见(false)
end

function CDK兑换面板:重置()
  local nsf = require("SDL.图像")(773, 483)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(2, 230, 964, 402, 51):显示(240,130)

    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  VIP赞助.CDK兑换面板.兑换CDK按钮:置可见(true,true)
  VIP赞助.CDK兑换面板.CDK输入:置可见(true,true)
  self:置可见(true)
end