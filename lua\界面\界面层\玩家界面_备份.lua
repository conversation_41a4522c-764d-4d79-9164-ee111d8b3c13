-- <AUTHOR> GGELUA
-- @Last Modified by    : baidwwy
-- @Date                : 2024-09-04 16:18:55
-- @Last Modified time  : 2024-10-16 19:34:38

local SDL = require("SDL")
local 界面层 = __UI界面["创建界面"](__UI界面, "界面层")
function 界面层:初始化()
    self.鼠标点击动画组={}
end


function 界面层:显示(x, y)
    if self.图像 then
        self.图像["显示"](self.图像, x, y)
    end
    if self.图像2 then
        self.图像2["显示"](self.图像2, x, y)
    end
    -- for i=1,#self.鼠标点击动画组 do
	-- 	if self.鼠标点击动画组[i] ~= nil then
    --         self.鼠标点击动画组[i].ani:更新(0.027958)
	-- 		self.鼠标点击动画组[i].ani:显示(self.鼠标点击动画组[i].x,self.鼠标点击动画组[i].y)
    --         -- print(self.鼠标点击动画组[i].ani.当前帧)
	-- 		if (self.鼠标点击动画组[i].ani.当前帧 >= self.鼠标点击动画组[i].ani.帧数) then
	-- 			self.鼠标点击动画组[i].ani = nil
	-- 			table.remove(self.鼠标点击动画组,i)
	-- 		end
	-- 	end
	-- end
end

function 界面层:左键按下(x, y)
end

function 界面层:左键弹起(x, y)
end

function 界面层:消息事件(msg)
    if not __主控["战斗中"] then
        for i, v in pairsi(__主显["显示表"]) do
            if v["消息事件"] then
                v["消息事件"](v, msg, x, y)
            end
        end
    end
    for i, v in ipairs(msg["鼠标"]) do
        local x, y = v.x, v.y
        if v.button == SDL.BUTTON_LEFT then
            if v.type == SDL.MOUSE_DOWN then
                if x < 0 then
                    break
                end
                self.按下 = os.time()
                -- local v = {
                --     x = x,
                --     y = y,
                --     ani =  __res["取动画"](__res, __res["取地址"](__res, "shape/gg/", 0x0D98AC0A))["取动画"](__res[
                --         "取动画"](__res, __res["取地址"](__res, "shape/gg/", 0x0D98AC0A)), 1)["播放"](__res["取动画"](__res,
                --         __res["取地址"](__res, "shape/gg/", 0x0D98AC0A))["取动画"](__res["取动画"](__res,
                --         __res["取地址"](__res, "shape/gg/", 0x0D98AC0A)), 1), true)
                -- }
                -- table.insert(self.鼠标点击动画组,v)
            else
                if not (v.type == SDL.MOUSE_UP and self.按下) then
                    goto lbl_403
                end
                self.按下 = false
                if x < 0 then
                    break
                end
                if not __主控["战斗中"] then
                    if not self.返回["是否可见"] then
                        if __UI界面["界面层"]["右上角"]["玩家头像"]["是否可见"] then
                            __UI界面["界面层"]["右上角"]["玩家头像"]["置可见"](__UI界面["界面层"]["右上角"]["玩家头像"], false)
                        end
                        if __主显["主角"] and (not __主显["主角"]["是否组队"] or __主显["主角"]["是否队长"]) and not __主显["主角"]["是否摆摊"] then
                            local xy = require("GGE.坐标")(x, y) - __主显["屏幕坐标"]
                            __主显["主角"]["设置路径"](__主显["主角"], xy)
                        end
                    end
                else
                    if self.战斗界面["是否可见"] and not __UI界面["窗口层"]["战斗法术"]["是否可见"] and not __UI界面["窗口层"]["战斗道具"]["是否可见"] then
                        self:战斗主角操作(x, y)
                    elseif self.助战操作界面["是否可见"] and not __UI界面["窗口层"]["助战法术"]["是否可见"] and not __UI界面["窗口层"]["战斗道具"]["是否可见"] and not __UI界面["窗口层"]["战斗法术"]["是否可见"] then
                        self:战斗助战操作(x, y)
                    end
                    
                end
            end
        elseif v.button == SDL.BUTTON_RIGHT then
            if v.type == SDL.MOUSE_UP then
            end
        elseif not v.button and self.按下 and not __主控["战斗中"] and not self.返回["是否可见"] and
            v.type == SDL.MOUSE_MOTION then
            if x < 0 then
                break
            end
            if __主显["主角"] and (not __主显["主角"]["是否组队"] or __主显["主角"]["是否队长"]) and not __主显["主角"]["是否摆摊"] then
                local xy = require("GGE.坐标")(x, y) - __主显["屏幕坐标"]
                __主显["主角"]["设置路径"](__主显["主角"], xy)
            end
        end
        ::lbl_403::
    end
end

function 界面层:战斗主角操作(x, y)
    for n=#__战斗主控["战斗单位"],1,-1 do
        local v=__战斗主控["战斗单位"][n]
        if v["检查点"](v, x, y) and v.不可操作==nil then
            local 允许选择 = false
            if (not self.战斗界面["命令类型"] or self.战斗界面["命令类型"] == "攻击") and self.战斗界面["取类型选择"](self.战斗界面, v["敌我"]) then
                允许选择 = true
            elseif self.战斗界面["命令类型"] == "保护" and self.战斗界面["取类型选择"](self.战斗界面, v["敌我"], n) then
                允许选择 = true
            elseif (self.战斗界面["命令类型"] == "法术" or self.战斗界面["命令类型"] =="特技") then
                local mc=self.战斗界面["法术名称"]
                if 2 == v["敌我"] then
                    if self.战斗界面["取类型选择"](self.战斗界面, v["敌我"])  
                        and (mc == "后发制人" or not skill增益[mc]) 
                        and not skill恢复[mc]  
                        and (skill减益[mc] or skill封印[mc] or skill法攻[mc] or skill物攻[mc] or mc == "后发制人")  then
                        允许选择 = true
                    end
                elseif 1 == v["敌我"] then
                    if (skill增益[mc] or skill恢复[mc]) 
                            and not skill减益[mc] 
                            and not skill封印[mc] 
                            and not skill法攻[mc] 
                            and not skill物攻[mc] 
                        then
                        允许选择 = true
                    end
                end
            elseif self.战斗界面["命令类型"] == "道具" and
                self.战斗界面["取类型选择"](self.战斗界面, v["敌我"]) then
                允许选择 = true
            elseif self.战斗界面["命令类型"] == "灵宝" and
                self.战斗界面["取类型选择"](self.战斗界面, v["敌我"]) then
                允许选择 = true
            elseif self.战斗界面["命令类型"] == "捕捉" and
                self.战斗界面["取类型选择"](self.战斗界面, v["敌我"], n) then
                允许选择 = true
            end
            if 允许选择 then
                self.战斗界面:设置指令0(n)
                break
            end
            print("我点击战斗中的目标了"..n)
        end
    end
    -- for k, v in pairs(__战斗主控["战斗单位"]) do
    --     if v["检查点"](v, x, y) then
    --         local 允许选择 = false
    --         if (not self.战斗界面["命令类型"] or self.战斗界面["命令类型"] == "攻击") and self.战斗界面["取类型选择"](self.战斗界面, v["敌我"]) then
    --             允许选择 = true
    --         elseif self.战斗界面["命令类型"] == "保护" and self.战斗界面["取类型选择"](self.战斗界面, v["敌我"], k) then
    --             允许选择 = true
    --         elseif (self.战斗界面["命令类型"] == "法术" or self.战斗界面["命令类型"] =="特技") then
    --             local mc=self.战斗界面["法术名称"]
    --             if 2 == v["敌我"] then
    --                 if self.战斗界面["取类型选择"](self.战斗界面, v["敌我"])  
    --                     and (mc == "后发制人" or not skill增益[mc]) 
    --                     and not skill恢复[mc]  
    --                     and (skill减益[mc] or skill封印[mc] or skill法攻[mc] or skill物攻[mc] or mc == "后发制人")  then
    --                     允许选择 = true
    --                 end
    --             elseif 1 == v["敌我"] then
    --                 if (skill增益[mc] or skill恢复[mc]) 
    --                         and not skill减益[mc] 
    --                         and not skill封印[mc] 
    --                         and not skill法攻[mc] 
    --                         and not skill物攻[mc] 
    --                     then
    --                     允许选择 = true
    --                 end
    --             end
    --         elseif self.战斗界面["命令类型"] == "道具" and
    --             self.战斗界面["取类型选择"](self.战斗界面, v["敌我"]) then
    --             允许选择 = true
    --         elseif self.战斗界面["命令类型"] == "灵宝" and
    --             self.战斗界面["取类型选择"](self.战斗界面, v["敌我"]) then
    --             允许选择 = true
    --         elseif self.战斗界面["命令类型"] == "捕捉" and
    --             self.战斗界面["取类型选择"](self.战斗界面, v["敌我"], k) then
    --             允许选择 = true
    --         end
    --         if 允许选择 then
    --             self.战斗界面:设置指令0(k)
    --             break
    --         end
    --         print("我点击战斗中的目标了")
    --     end
    -- end
end

function 界面层:战斗助战操作(x, y)
    for n=#__战斗主控["战斗单位"],1,-1 do
        local v=__战斗主控["战斗单位"][n]
        if v["检查点"](v, x, y) and v.不可操作==nil then
            local 允许选择 = false
            if (not self.助战操作界面["命令类型"] or self.助战操作界面["命令类型"] == "攻击") and self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"]) then
                允许选择 = true
            elseif self.助战操作界面["命令类型"] == "保护" and self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"], n) then
                允许选择 = true
            elseif (self.助战操作界面["命令类型"] == "法术" or self.助战操作界面["命令类型"] =="特技") then
                local mc=self.助战操作界面["法术名称"]
                if 2 == v["敌我"] then
                    if self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"])  
                        and (mc == "后发制人" or not skill增益[mc]) 
                        and not skill恢复[mc]  
                        and (skill减益[mc] or skill封印[mc] or skill法攻[mc] or skill物攻[mc] or mc == "后发制人")  then
                        允许选择 = true
                    end
                elseif 1 == v["敌我"] then
                    if (skill增益[mc] or skill恢复[mc]) 
                            and not skill减益[mc] 
                            and not skill封印[mc] 
                            and not skill法攻[mc] 
                            and not skill物攻[mc] 
                        then
                        允许选择 = true
                    end
                end    
            elseif self.助战操作界面["命令类型"] == "道具" and self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"]) then
                允许选择 = true
            -- elseif self.助战操作界面["命令类型"] == "灵宝" and self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"]) then
            --     允许选择 = true
            -- elseif self.助战操作界面["命令类型"] == "捕捉" and
            --     self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"], n) then
            --     允许选择 = true
            end
            print("助战：我点击战斗中的目标了")
            if 允许选择 then
                self.助战操作界面:设置指令0(n)
                break
            end
           
        end
    end
    -- for k, v in pairs(__战斗主控["战斗单位"]) do
    --     if v["检查点"](v, x, y) then
    --         local 允许选择 = false
    --         if (not self.助战操作界面["命令类型"] or self.助战操作界面["命令类型"] == "攻击") and self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"]) then
    --             允许选择 = true
    --         elseif self.助战操作界面["命令类型"] == "保护" and self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"], k) then
    --             允许选择 = true
    --         elseif (self.助战操作界面["命令类型"] == "法术" or self.助战操作界面["命令类型"] =="特技") then
    --             local mc=self.助战操作界面["法术名称"]
    --             if 2 == v["敌我"] then
    --                 if self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"])  
    --                     and (mc == "后发制人" or not skill增益[mc]) 
    --                     and not skill恢复[mc]  
    --                     and (skill减益[mc] or skill封印[mc] or skill法攻[mc] or skill物攻[mc] or mc == "后发制人")  then
    --                     允许选择 = true
    --                 end
    --             elseif 1 == v["敌我"] then
    --                 if (skill增益[mc] or skill恢复[mc]) 
    --                         and not skill减益[mc] 
    --                         and not skill封印[mc] 
    --                         and not skill法攻[mc] 
    --                         and not skill物攻[mc] 
    --                     then
    --                     允许选择 = true
    --                 end
    --             end    
    --         elseif self.助战操作界面["命令类型"] == "道具" and
    --             self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"]) then
    --             允许选择 = true
    --         elseif self.助战操作界面["命令类型"] == "灵宝" and
    --             self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"]) then
    --             允许选择 = true
    --         elseif self.助战操作界面["命令类型"] == "捕捉" and
    --             self.助战操作界面["取类型选择"](self.助战操作界面, v["敌我"], k) then
    --             允许选择 = true
    --         end
    --         print("助战：我点击战斗中的目标了")
    --         if 允许选择 then
    --             self.助战操作界面:设置指令0(k)
    --             break
    --         end
           
    --     end
    -- end
end

function 界面层:重置(lx, cz)
    self.图像 = nil
    self.类型 = nil
    if "给予" == lx or "攻击" == lx or "组队" == lx then
        self.左上角["置可见"](self.左上角, false)
        self.右上角["置可见"](self.右上角, false)
        self.左下角["置可见"](self.左下角, false)
        self.右下角["置可见"](self.右下角, false)
        self.返回["置可见"](self.返回, true, true)
        local nsf = require("SDL.图像")(引擎["宽度"], 引擎["高度"])
        if nsf["渲染开始"](nsf) then
            nsf["渲染清除"](nsf, 3, 0, 0, 150)
            local kd = 字体18["取宽度"](字体18, "请选择" .. lx .. "对象")
            __res["UI素材"][3]:复制区域(216, 1148, 224, 28):置透明(150):显示(引擎["宽度"] / 2 - (kd + 20) / 2-38, 85)
            字体18["置颜色"](字体18, 255, 255, 255)
            字体18["取图像"](字体18, "请选择" .. lx .. "对象")["显示"](字体18["取图像"](字体18,"请选择" .. lx .. "对象"), 引擎["宽度"] / 2 - kd / 2, 90)
            nsf["渲染结束"](nsf)
        end
        self.图像 = nsf["到精灵"](nsf)
        self.类型 = lx
    else
        self.左上角["置可见"](self.左上角, true, true)
        self.右上角:重置界面()
        self.左下角["置可见"](self.左下角, true, true)
        self.右下角:重置界面()
        self.队伍栏["置可见"](self.队伍栏, true, true)
        if self.左上角["功能栏"]["是否可见"] then
            self.左上角["功能栏"]["置可见"](self.左上角["功能栏"], false)
           -- self.左上角["左关闭"]["置坐标"](self.左上角["左关闭"], 10, 85)
        else
            self.左上角["功能栏"]["置可见"](self.左上角["功能栏"], true)
        --    self.左上角["左关闭"]["置坐标"](self.左上角["左关闭"], 465, 85)
        end
        if self.右上角["任务栏"]["是否可见"] then
            self.右上角["任务栏"]["置可见"](self.右上角["任务栏"], false)
            self.右上角["右关闭"]["置坐标"](self.右上角["右关闭"], 272, 133)
        else
            self.右上角["任务栏"]["置可见"](self.右上角["任务栏"], true)
            self.右上角["右关闭"]["置坐标"](self.右上角["右关闭"], 272, 133)
        end
        if cz then
            self.左上角["时辰按钮"]["重置"](self.左上角["时辰按钮"], 1)
            self.右上角["人物头像"]["置头像"](self.右上角["人物头像"], 角色信息)
            self.右上角["召唤兽头像"]["置头像"](self.右上角["召唤兽头像"], 角色信息["参战宝宝"])
        end
        self.返回["置可见"](self.返回, false)
        self:置精灵(nil)
    end
end

function 界面层:进入战斗()
    self.右下角["置可见"](self.右下角, false)
    if self.右上角["任务栏"]["是否可见"] then
        self.右上角["任务栏"]["置可见"](self.右上角["任务栏"], false)
    end
    if self.左上角["功能栏"]["是否可见"] then
        self.左上角["功能栏"]:置可见(false)
    end
    -- if self.左上角["助战按钮"]["是否可见"] then
    --     self.左上角["助战按钮"]:置可见(false)
    -- end
    -- if self.左上角["助战仓库"]["是否可见"] then
    --     self.左上角["助战仓库"]:置可见(false)
    -- end
    -- if self.左上角["赞助按钮"]["是否可见"] then
    --     self.左上角["赞助按钮"]:置可见(false)
    -- end
    -- if self.左上角["商城按钮"]["是否可见"] then
    --     self.左上角["商城按钮"]:置可见(false)
    -- end
 --   self.左上角["退出战斗按钮"]:置可见(false)
   -- self.左上角["左关闭"]:置可见(false)
    self.右上角["右关闭"]:置可见(false)
    self.右上角.玩家头像:置可见(false)
    self.队伍栏["置可见"](self.队伍栏, false)
    if __UI界面["窗口层"]["战斗自动"]["自动kaiguan"] then
        __UI界面["窗口层"]["战斗自动"]["置可见"](__UI界面["窗口层"]["战斗自动"], true)
    end
    __UI界面["界面层"]["助战框显示"]:进入战斗()
end

function 界面层:退出战斗()
    self.右下角["置可见"](self.右下角, true)
    if self.右上角["右关闭"]["取坐标"](self.右上角["右关闭"]) - self.右上角["取坐标"](self.右上角)== 240 then
        self.右上角["右关闭"]["置可见"](self.右上角["右关闭"], true)
    else
        self.右上角["任务栏"]["置可见"](self.右上角["任务栏"], true)
        self.右上角["右关闭"]["置可见"](self.右上角["右关闭"], true)
    end
    --self.左上角["退出战斗按钮"]:置可见(false)
    --self.左上角["左关闭"]:置可见(true)
  --  self.左上角["助战按钮"]:置可见(true)
  --  self.左上角["助战仓库"]:置可见(true)
    self.队伍栏["置可见"](self.队伍栏, true)
    __UI界面["窗口层"]["战斗自动"]["置可见"](__UI界面["窗口层"]["战斗自动"], false)
    __UI界面["界面层"]["战斗界面"]:置可见(false)
    __UI界面["界面层"]["助战框显示"]:退出战斗()
end

local 返回 = __UI界面["界面层"]["创建控件"](__UI界面["界面层"], "返回", 860 + abbr.py.x,
    440 + abbr.py.y, 56, 56)
function 返回:初始化()
end

local 返回按钮 = 返回["创建我的按钮"](返回, __res["UI素材"][2]["复制区域"](__res["UI素材"][2], 973
    , 1, 56, 56), "返回按钮", 0, 0)
function 返回按钮:左键弹起(x, y, msg)
    界面层["重置"](界面层, nil)
end

require("界面/界面层/左上角")
require("界面/界面层/右上角")
require("界面/界面层/左下角")
require("界面/界面层/右下角")
require("战斗逻辑/战斗界面")
require("战斗逻辑/zhuzhan/助战框显示")
require("战斗逻辑/zhuzhan/助战操作界面")
require("界面/界面层/队伍栏")
require("界面/界面层/游戏公告类")
require("界面/界面层/游戏传音类")
require("界面/界面层/提示框")
