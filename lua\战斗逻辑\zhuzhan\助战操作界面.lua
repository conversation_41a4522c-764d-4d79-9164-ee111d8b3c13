--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-30 05:21:12
Last Modified by: GGELUA
Last Modified time: 2024-08-26 04:28:05
--]]
local 助战操作界面 = __UI界面["界面层"]["创建我的控件"](__UI界面["界面层"], "助战操作界面",12,80, 440, 450)
function 助战操作界面:初始化()
    self.quanquan= __res:getPNGCC(5, 1031, 163, 50, 50):到精灵()
    self.hbk= __res:getPNGCC(5, 904, 215, 198, 57):到精灵()
    self.hbk= __res:getPNGCC(5, 904, 215, 198, 57):到精灵()
    self.灰色背景=取黑透明背景(0, 0, 155, 255):置透明(180)
    self.hbxztx=0
    self.操作对象 = 1
end
local 灰色是否显示=false
function 助战操作界面:进入战斗()
    self.hbxztx=0
    self.操作对象 = 1
end
function 助战操作界面:退出战斗()
    self.hbxztx=0
    self.操作对象 = 1
	self:置可见(false)
end

function 助战操作界面:显示(x,y)
	if self.图像5 then
		self.图像5:显示(x,y)
	end
    if self.hbxztx~=0 then
		self.hbk:显示(x,y-2+(self.hbxztx-1)*58)
		self.quanquan:显示(x+72-6+10+(self.操作对象-1)*55,y-3+4+(self.hbxztx-1)*58)
        if 灰色是否显示 then
            self.灰色背景:显示(x+72+122-6+10,y+(self.hbxztx-1)*58)
        end
	end
end

function 助战操作界面:更新角色类型(sj,操作序号)
    助战操作界面["置可见"](助战操作界面, true, true)
    self.图像5=nil
    self.单位编号 = sj.mb
    self.单位id = sj.id
    self.操作对象 = 1
    self.完成指令 = {}
    self.对象上限 = #sj.mb
    self.参战单位 = __战斗主控["战斗单位"]
    self.命令参数=""
    self.参战单位编号=self.单位编号[self.操作对象]
	self.参战单位[self.参战单位编号].主动技能 =  sj.zdjn1
    self.hbxztx=操作序号
    self.人物指令类:置坐标(0, 2+(操作序号-1)*58)
    灰色是否显示=true
    __UI界面["界面层"]["战斗界面"]:置可见(false)
    self.命令类型 = nil
    self.命令附加 = 4
    self.人物指令类:置可见(true)
    self.召唤兽指令类:置可见(false)
    __UI界面["界面层"]["左上角"].图像5=nil
    self.九黎多次 = {}
end

function 助战操作界面:更新bb类型(sj,操作序号)
    助战操作界面["置可见"](助战操作界面, true, true)
    self.图像5=nil
    self.单位编号 = sj.mb
    self.单位id = sj.id
    self.操作对象 = 2
    self.完成指令 = {}
    self.对象上限 = #sj.mb
    self.参战单位 = __战斗主控["战斗单位"]
    self.命令参数=""
    self.参战单位编号=self.单位编号[self.操作对象]
	self.参战单位[self.参战单位编号].主动技能 =  sj.zdjn2
    self.hbxztx=操作序号
    self.召唤兽指令类:置坐标(0, 2+(操作序号-1)*58)
    灰色是否显示=true
    __UI界面["界面层"]["战斗界面"]:置可见(false)
    self.命令类型 = nil
    self.命令附加 = 4
    self.人物指令类:置可见(false)
    self.召唤兽指令类:置可见(true)
    __UI界面["界面层"]["左上角"].图像5=nil
end

function 助战操作界面:操作重置()
    if not self.是否可见 then
        self:置可见(true)
    end
    self.人物指令类:置可见(false)
    self.召唤兽指令类:置可见(false)
    灰色是否显示=false
    -- self.hbxztx=0
end



function 助战操作界面:取类型选择(敌我, bh)
    if self.命令类型 == "法术" or self.命令类型 == "特技" or self.命令类型 == "道具" or
        self.命令类型 == "灵宝" then
        if 2 == 敌我 and 4 == self.命令附加 then
            return true
        elseif 1 == 敌我 and (5 == self.命令附加 or 6 == self.命令附加 or 3 == self.命令附加) then
            return true
        end
    elseif self.命令类型 == "捕捉" and 2 == 敌我 then
        return true
    elseif self.命令类型 == "保护" and 1 == 敌我 and bh ~= self.操作对象 then
        return true
    elseif self.命令类型 == "攻击" then
        return true
    elseif not self.命令类型 and 2 == 敌我 then
        self.命令类型 = "攻击"
        return true
    end
    return false
end

function 助战操作界面:设置指令0(编号)
    -- print(1111111111)
    if self.命令类型 == "攻击" and 1 == self.参战单位[编号]["敌我"] then
        if 编号 == self.单位编号[self.操作对象] then
            self.操作重置(self)
            return
        else
            self.命令类型 = "攻击"
            self.命令附加 = "友伤"
        end
    elseif not self.命令类型 then
        self.命令类型 = "攻击"
    end
    self.完成指令={类型=self.命令类型,目标=编号,敌我=0,参数=self.命令参数,附加=self.命令附加,id=self.单位id[self.操作对象],操作编号=self.参战单位编号}
	local 继续 = true
    if self.参战单位[self.单位编号[self.操作对象]].门派 == "九黎城" and self.命令类型 == "法术" and self.参战单位[self.单位编号[self.操作对象]].黎魂 ~= nil and 九黎城攻击技能[self.命令参数] ~= nil then
        if self.九黎多次[self.单位编号[self.操作对象]] == nil then
            self.九黎多次[self.单位编号[self.操作对象]] = {
                {
                    self.命令参数,
                    编号
                }
            }
            继续 = false
        elseif #self.九黎多次[self.单位编号[self.操作对象]] < self.参战单位[self.单位编号[self.操作对象]].黎魂 - 1 then
            self.九黎多次[self.单位编号[self.操作对象]][#self.九黎多次[self.单位编号[self.操作对象]] + 1] = {
                self.命令参数,
                编号
            }
            继续 = false
        else
            self.九黎多次[self.单位编号[self.操作对象]][#self.九黎多次[self.单位编号[self.操作对象]] + 1] = {
                self.命令参数,
                编号
            }
            self.完成指令 = {
                敌我 = 0,
                类型 = self.命令类型,
                目标 = 编号,
                参数 = self.九黎多次[self.单位编号[self.操作对象]],
                附加 = self.命令附加,
                id=self.单位id[self.操作对象],
                操作编号=self.参战单位编号
            }
        end
    end
    if 继续 then
    发送数据(5527,{self.完成指令})
    显示战斗鼠标=false
    local zd="攻击"
    if self.物品名称 then
		zd=self.物品名称
	elseif self.命令类型~="法术" then
		zd=self.命令类型
	else
		zd=self.命令参数
	end
    __UI界面["界面层"]["助战框显示"]:更新战斗图标(self.hbxztx,self.操作对象,zd)
    self.物品名称=nil
    __UI界面["界面层"]["左上角"].图像5=nil
    self:置可见(false)
    self.hbxztx=0
    self.操作对象 = 1
    if  __战斗主控["进程"]=="命令" then
		__UI界面["界面层"]["战斗界面"]:置可见(true)
	end
end
显示战斗鼠标=false
self.命令类型 = nil
self.命令附加 = 4
self.人物指令类:置可见(true)


end
function 助战操作界面:设置指令1(编号)
    self.完成指令={类型=self.命令类型,目标=编号,敌我=0,参数=self.命令参数,附加=self.命令附加,id=self.单位id[self.操作对象],操作编号=self.参战单位编号}
	local 继续 = true
    if self.参战单位[self.单位编号[self.操作对象]].门派 == "九黎城" and self.命令类型 == "法术" and self.参战单位[self.单位编号[self.操作对象]].黎魂 ~= nil and 九黎城攻击技能[self.命令参数] ~= nil then
        if self.九黎多次[self.单位编号[self.操作对象]] == nil then
            self.九黎多次[self.单位编号[self.操作对象]] = {
                {
                    self.命令参数,
                    编号
                }
            }
            继续 = false
        elseif #self.九黎多次[self.单位编号[self.操作对象]] < self.参战单位[self.单位编号[self.操作对象]].黎魂 - 1 then
            self.九黎多次[self.单位编号[self.操作对象]][#self.九黎多次[self.单位编号[self.操作对象]] + 1] = {
                self.命令参数,
                编号
            }
            继续 = false
        else
            self.九黎多次[self.单位编号[self.操作对象]][#self.九黎多次[self.单位编号[self.操作对象]] + 1] = {
                self.命令参数,
                编号
            }
            self.完成指令 = {
                敌我 = 0,
                类型 = self.命令类型,
                目标 = 编号,
                参数 = self.九黎多次[self.单位编号[self.操作对象]],
                附加 = self.命令附加,
                id=self.单位id[self.操作对象],
                操作编号=self.参战单位编号
            }
        end
    end
    if 继续 then
    发送数据(5527,{self.完成指令})
    显示战斗鼠标=false
	local zd="攻击"
    if self.物品名称 then
		zd=self.物品名称
	elseif self.命令类型~="法术" then
		zd=self.命令类型
	else
		zd=self.命令参数
	end
    __UI界面["界面层"]["助战框显示"]:更新战斗图标(self.hbxztx,self.操作对象,zd)
    self.物品名称=nil
    __UI界面["界面层"]["左上角"].图像5=nil
    self:置可见(false)
    self.hbxztx=0
    self.操作对象 = 1
    if  __战斗主控["进程"]=="命令" then
		__UI界面["界面层"]["战斗界面"]:置可见(true)
	end
end
end
function 助战操作界面:设置法术参数(法术, silllx)
    if nil == 法术 then
        return
    end
    self.命令类型 = "法术"
    if "特技" == silllx then
        self.命令类型 = "特技"
    end
    self.命令参数 = 法术["名称"]
    local 临时种类 = 取技能(法术["名称"])
    self.法术名称 = 法术["名称"]
    self.命令附加 = 临时种类[3]
    self.命令版面 = false
    self.法术开关 = false
    if 法术["名称"] == "妙手空空" then
        self.命令附加 = 4
    end
    if 2 == self.命令附加 then
        self:设置指令0(self.单位编号[self.操作对象])
    else
        self.操作重置(self)
        __UI界面["界面层"]["左上角"]:asdsad(法术["名称"])
    end
end

function 助战操作界面:设置披坚执锐(法术,可选编号)
    if nil == 法术 or not 法术.可选 then
        return
    end
    self.完成指令={类型="披坚执锐",目标=1,敌我=0,参数=法术.名称,附加=可选编号,id=self.单位id[self.操作对象],操作编号=self.参战单位编号}
	发送数据(5527,{self.完成指令})
    显示战斗鼠标=false
	-- local zd="攻击"
	-- if self.命令类型~="法术" then
	-- 	zd=self.命令类型
	-- else
	-- 	zd=self.命令参数
	-- end
    -- __UI界面["界面层"]["助战框显示"]:更新战斗图标(self.hbxztx,self.操作对象,zd)
    __UI界面["界面层"]["左上角"].图像5=nil
    self:置可见(false)
    self.hbxztx=0
    self.操作对象 = 1
    if  __战斗主控["进程"]=="命令" then
		__UI界面["界面层"]["战斗界面"]:置可见(true)
	end
end


function 助战操作界面:设置道具(data, lx,助战id)
    self.助战id=助战id
    __UI界面["窗口层"]["战斗道具"]:打开(data["道具"], lx,助战id)
end

function 助战操作界面:设置灵宝(data)
end

function 助战操作界面:设置道具参数(编号, 对象,助战id,物品名称)
    --print(编号, 对象,助战id,物品名称)
    self.助战id=助战id
    self.物品名称=物品名称
    self.命令类型 = "道具"
    self.命令附加 = 对象
    self.命令参数 = 编号
    __UI界面["窗口层"]["战斗道具"]["置可见"](__UI界面["窗口层"]["战斗道具"], false)
    __UI界面["界面层"]["左上角"]:asdsad("道具")
    if 2 == self.命令附加 then
        self:设置指令0(self.单位编号[self.操作对象])
    else
        self.操作重置(self)
    end
end

function 助战操作界面:设置灵宝参数(编号, 对象)
end

function 助战操作界面:设置逃跑(id)
    -- self.完成指令[self.操作对象] = {
    --     ["类型"] = "逃跑",
    --     ["目标"] = 1,
    --     ["敌我"] = 0,
    --     ["参数"] = self.命令参数,
    --     ["附加"] = 1,
    --     id = self.单位id[self.操作对象]
    -- }
    -- if self.操作对象 >= self.对象上限 then
    --     self.完成命令(self)
    -- else
    --     self.操作对象 = self.操作对象 + 1
    --     self.操作重置(self)
    -- end
end

function 助战操作界面:设置召唤(id)
    self.命令类型 = "召唤"
   -- self.完成指令={类型=self.命令类型,目标=编号,敌我=0,参数=self.命令参数,附加=self.命令附加,id=self.单位id[self.操作对象],操作编号=self.参战单位编号}
    self.完成指令={类型=self.命令类型,目标=id,敌我=0,参数=self.命令参数,附加=self.命令附加,id=self.单位id[self.操作对象],操作编号=self.参战单位编号}
	发送数据(5527,{self.完成指令})
    显示战斗鼠标=false
    local zd="攻击"
    if self.物品名称 then
		zd=self.物品名称
	elseif self.命令类型~="法术" then
		zd=self.命令类型
	else
		zd=self.命令参数
	end
	-- if self.命令类型~="法术" then
	-- 	zd=self.命令类型
	-- else
	-- 	zd=self.命令参数
	-- end

    __UI界面["界面层"]["助战框显示"]:更新战斗图标(self.hbxztx,self.操作对象,zd)
    self.物品名称=nil
    __UI界面["界面层"]["左上角"].图像5=nil
    self:置可见(false)
    self.hbxztx=0
    self.操作对象 = 1
    if  __战斗主控["进程"]=="命令" then
		__UI界面["界面层"]["战斗界面"]:置可见(true)
	end
end

local 人物指令类 = 助战操作界面["创建控件"](助战操作界面, "人物指令类", 0, 0, 440, 450)
function 人物指令类:初始化()
end

for i, v in ipairs({
    {
        name = "攻击",
        x = 376-328+164,
        y = 0+62-62,
        tcp = __res:getPNGCC(2, 780, 3, 54, 54)
    },
    {
        name = "法术",
        x = 376-328+164,
        y = 63+62-62,
        tcp = __res:getPNGCC(2, 719, 3, 54, 54)
    },
    {
        name = "防御",
        x = 376-328+164,
        y = 125+62-62,
        tcp = __res:getPNGCC(2, 329, 3, 54, 54)
    },
    {
        name = "道具",
        x = 376-328+164+70,
        y = 0+62-62,
        tcp = __res:getPNGCC(2, 521, 3, 54, 54)
    },
    {
        name = "特技",
        x = 376-328+164+70,
        y = 63+62-62,
        tcp = __res:getPNGCC(2, 586, 3, 54, 54)
    },
    {
        name = "法宝",
        x = 376-328+164+70,
        y = 125+62-62,
        tcp = __res:getPNGCC(2, 653, 3, 54, 54)
    },
    {
        name = "召唤",
        x = 376-328+164,
        y = 125+62,
        tcp =__res:getPNGCC(2, 459.0, 3, 54, 54)
    },
}) do
    local 临时函数 = 人物指令类["创建我的按钮"](人物指令类, v.tcp, v.name, v.x, v.y)
    function 临时函数:左键弹起(x, y)
        if v.name == "防御" then
            助战操作界面["命令类型"] = "防御"
            助战操作界面["设置指令1"](助战操作界面, 0)
        elseif v.name == "法术" then
            助战操作界面["命令类型"] = "法术"
            __UI界面.窗口层.助战法术:打开(助战操作界面.参战单位[助战操作界面.参战单位编号].主动技能, 
                助战操作界面.参战单位[助战操作界面.参战单位编号].单位类型, 
                助战操作界面.命令类型,
                助战操作界面.参战单位[助战操作界面.参战单位编号].追加法术
            )
            助战操作界面["操作重置"](助战操作界面)
        elseif v.name == "攻击" then
            助战操作界面["命令类型"] = "攻击"
            助战操作界面["操作重置"](助战操作界面)
            __UI界面["界面层"]["左上角"]:asdsad("攻击")
        elseif v.name == "道具" then
           -- 助战操作界面["命令类型"] = "道具"
           助战操作界面["操作重置"](助战操作界面)
           发送数据(5700,{助战id=助战操作界面.参战单位[助战操作界面.参战单位编号].单位id})
        elseif v.name == "特技" then    
            助战操作界面["命令类型"] = "特技"
            __UI界面.窗口层.助战法术:打开(助战操作界面.参战单位[助战操作界面.参战单位编号].特技技能, 
                助战操作界面.参战单位[助战操作界面.参战单位编号].单位类型, "法术")
            助战操作界面["操作重置"](助战操作界面)
        elseif v.name == "法宝" then
            发送数据(5701,{助战id=助战操作界面.参战单位[助战操作界面.参战单位编号].单位id})
        elseif v.name == "召唤" then
            发送数据(5702,{助战id=助战操作界面.参战单位[助战操作界面.参战单位编号].单位id})
        end
    end
end
local 召唤兽指令类 = 助战操作界面["创建控件"](助战操作界面, "召唤兽指令类", 60, 0, 440, 450)
function 召唤兽指令类:初始化()
    self:置可见(false)
end

for i, v in ipairs({
    {
        name = "防御按钮",
        x = 376-328+164,
        y = 125+62-62,
        tcp = __res:getPNGCC(2, 329, 3, 54, 54)
    },
    {
        name = "法术按钮",
        x = 376-328+164,
        y = 63+62-62,
        tcp = __res:getPNGCC(2, 719, 3, 54, 54)
    },
    {
        name = "攻击按钮",
        x = 376-328+164,
        y = 0+62-62,
        tcp = __res:getPNGCC(2, 780, 3, 54, 54)
    },
    {
        name = "道具按钮",
        x = 376-328+164+70,
        y = 125+62-62,
        tcp = __res:getPNGCC(2, 521, 3, 54, 54)
    },
}) do
    local 临时函数 = 召唤兽指令类["创建我的按钮"](召唤兽指令类, v.tcp, v.name, v.x, v.y)
    function 临时函数:左键弹起(x, y)
        if v.name == "防御按钮" then
            助战操作界面["命令类型"] = "防御"
            助战操作界面["设置指令1"](助战操作界面, 0)
        elseif v.name == "法术按钮" then
            助战操作界面["命令类型"] = "法术"
            __UI界面["窗口层"]["助战法术"]:打开(
                助战操作界面["参战单位"][助战操作界面["参战单位编号"]]["主动技能"],
                助战操作界面["参战单位"][助战操作界面["参战单位编号"]]["单位类型"], 
                助战操作界面["命令类型"]
            )
            助战操作界面["操作重置"](助战操作界面)
        elseif v.name == "攻击按钮" then
            助战操作界面["命令类型"] = "攻击"
            助战操作界面["操作重置"](助战操作界面)
            __UI界面["界面层"]["左上角"]:asdsad("攻击")
        elseif v.name == "道具按钮" then
            助战操作界面["操作重置"](助战操作界面)
            发送数据(5700,{助战id=助战操作界面.参战单位[助战操作界面.参战单位编号].单位id})
        end
    end
end
-- {
--     name = "返回按钮",
--     x = 376-328+164,
--     y = 125+62,
--     tcp = __res:getPNGCC(2, 973, 1, 56, 56)
-- },

local 返回按钮 = 助战操作界面["创建我的按钮"](助战操作界面, __res:getPNGCC(2, 973, 1, 56, 56), "返回按钮", 376-328+164-90, 125+62+44)
function 返回按钮:左键弹起(x, y)
    __UI界面["界面层"]["左上角"].图像5=nil
    助战操作界面:置可见(false)
    助战操作界面.hbxztx=0
    助战操作界面.操作对象 = 1
    显示战斗鼠标=false
    灰色是否显示=false
    if  __战斗主控["进程"]=="命令" then
		__UI界面["界面层"]["战斗界面"]:置可见(true)
	end
end
-- local 操作类 = 助战操作界面["创建控件"](助战操作界面, "操作类", 0, 0, 440, 450)
-- function 操作类:初始化()
--     self:置可见(false)
-- end