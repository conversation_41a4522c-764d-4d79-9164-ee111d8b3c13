
local 战斗界面 = 界面层:创建控件("战斗界面")
local SDL = require("SDL")
function 战斗界面:初始化()
    self.默认法术={}
    self.操作对象 = 1
   
  
    self.可初始化=true
    if __手机 then
        self:置宽高(420, 380)
        self:置坐标(引擎.宽度-self.宽度, 引擎.高度-self.高度)
    else
        self:置宽高(360, 330)
        self:置坐标(引擎.宽度-self.宽度, 引擎.高度2-self.高度//2)

    end

end





function 战斗界面:重新初始化()
    for k, v in self:遍历控件() do
        v:初始化()
        if v.重新初始化 then
            v:重新初始化()
        end
    end
end




function 战斗界面:更新类型(编号)
    self.单位编号={}
    self.法术次数={}
    for k,v in pairs(编号) do
        if type(v)=="table" then
            self.单位编号[k]=v.目标
            self.法术次数[k]=v.法术次数
        else
            self.单位编号[k]=v
        end
    end
    self.操作对象 = 1
    self.完成指令 = {}
    self.对象上限 = #编号
    self.参战单位 = __战斗主控.战斗单位
    self.命令参数 = ""
    for n = 1, #编号 do
        self.完成指令[n] = {}
    end
    self:置可见(true,true)
    self:重置()
end



function 战斗界面:操作重置()
        if not self.是否可见 then
            self:置可见(true,true)
        end
        if self.命令类型=="法术" or self.命令类型=="特技" then
                鼠标层:法术形状()
        elseif self.命令类型=="捕捉" then
                鼠标层:捕捉形状()
        elseif self.命令类型=="保护" then
                鼠标层:保护形状()
        elseif self.命令类型=="道具"  or  self.命令类型 == "灵宝" then
                鼠标层:道具形状()
        else
             鼠标层:攻击形状()
        end
        self.宝宝指令:置可见(false)
        self.人物指令:置可见(false)
        self.角色指令:置可见(false)
        self.九黎次数:置可见(false)
        if __手机 then
            self.操作类:置可见(true)
        end
        
end

function 战斗界面:退出()
        self:置可见(false)
        战斗层.战斗法术:置可见(false)
        战斗层.战斗道具:置可见(false)
        战斗层.战斗灵宝:置可见(false)
        战斗层.战斗召唤:置可见(false)
        战斗层.多开法术:置可见(false) 
        战斗层.九黎法术:置可见(false) 
end


function 战斗界面:重置()
    self.命令类型 = nil
    self.命令附加 = 4
    self.图像 = nil
    if not self.是否可见 then
        self:置可见(true,true)
    end
    战斗层.战斗法术:置可见(false)
    战斗层.战斗道具:置可见(false)
    战斗层.战斗灵宝:置可见(false)
    战斗层.战斗召唤:置可见(false)
    战斗层.多开法术:置可见(false)
    战斗层.九黎法术:置可见(false) 
    self.操作类:置可见(false)
    self.宝宝指令:置可见(false)
    self.人物指令:置可见(false)
    self.角色指令:置可见(false)
    self.九黎次数:置可见(false)
    if not self.单位编号[self.操作对象] or not self.参战单位[self.单位编号[self.操作对象]] then self:置可见(false) return end
    if self.操作对象==1 then
         self.人物指令:置可见(true)
         self.九黎次数:置可见(true)
    else
        if self.参战单位[self.单位编号[self.操作对象]].类型 == "角色" then
           self.角色指令:置可见(true)
           self.九黎次数:置可见(true)
        else
            self.宝宝指令:置可见(true)
        end
    end
    self.图像 = 文本字体:置颜色(__取颜色("黄色")):取精灵(self.参战单位[self.单位编号[self.操作对象]].名称) 


   
    鼠标层:正常形状()
end

function 战斗界面:显示(x,y)
    if self.图像 then
        if __手机 then 
            self.图像:显示(x+(418-self.图像.宽度),y+4)
        else
            self.图像:显示(x+(313-self.图像.宽度),y+4)
        end
    
    end
end




function 战斗界面:完成命令()
    请求服务(5502, self.完成指令)
    __战斗主控.进程 = "等待"
    self.命令类型=""
    self:置可见(false)
    self.图像 = nil
    鼠标层:正常形状()
end

function 战斗界面:取类型选择(敌我, bh)
    if self.命令类型=="法术" or self.命令类型 == "特技" or self.命令类型 == "道具" or self.命令类型 == "灵宝" then
      if 敌我==2 and  (self.命令附加==4 or self.命令附加==99) then
        return true
      elseif 敌我==1 and  (self.命令附加==5 or self.命令附加==6 or self.命令附加==3 or self.命令附加==99) then
        return true
      elseif self.命令附加==88 then
        return true
      end
    elseif self.命令类型 == "保护" and 1 == 敌我 and bh ~= self.操作对象 then
          return true
    elseif self.命令类型=="攻击" or self.命令类型=="捕捉" and 敌我==2  then
        return true
    elseif (not self.命令类型 or self.命令类型=="") and 2 == 敌我 then
        self.命令类型 = "攻击"
        return true
    end

    return false
  end

function 战斗界面:设置指令(编号)
    if self.命令类型 == "攻击" and 1 == self.参战单位[编号].敌我 then
        if 编号 == self.单位编号[self.操作对象] then
            self:重置()
            return
        else
            self.命令类型 = "攻击"
            self.命令附加 = "友伤"
        end
    elseif not self.命令类型 then
        self.命令类型 = "攻击"
    end
    if not self.完成指令[self.操作对象] then self.完成指令[self.操作对象]={} end

    if self.法术次数[self.操作对象] and self.命令类型=="法术" then
        if  self.完成指令[self.操作对象].多重施法 and self.完成指令[self.操作对象].多重施法[1] then
            if #self.完成指令[self.操作对象].多重施法<self.法术次数[self.操作对象] then
              table.insert(self.完成指令[self.操作对象].多重施法,{类型=self.命令类型,目标=编号,敌我=0,参数=self.命令参数,附加=self.命令附加})
            end
        else
            self.完成指令[self.操作对象].多重施法={{类型=self.命令类型,目标=编号,敌我=0,参数=self.命令参数,附加=self.命令附加}}
        end
    end
    self.完成指令[self.操作对象].类型=self.命令类型
    self.完成指令[self.操作对象].目标=编号
    self.完成指令[self.操作对象].敌我=0
    self.完成指令[self.操作对象].参数=self.命令参数
    self.完成指令[self.操作对象].附加=self.命令附加
    local 继续执行=true
    if self.命令类型=="法术" and self.法术次数[self.操作对象] and  self.完成指令[self.操作对象].多重施法 and #self.完成指令[self.操作对象].多重施法<self.法术次数[self.操作对象] then
        继续执行=false
    end
    if 继续执行 then
        if self.操作对象 >= self.对象上限 then
            self:完成命令()
        else
            self.操作对象 = self.操作对象 + 1
        end
    end
    self:重置()
end

function 战斗界面:设置默认法术(法术,操作对象)
            local 查找主动 = false
            if self.参战单位[self.单位编号[操作对象]] and self.参战单位[self.单位编号[操作对象]].主动技能 then
                for k,v in pairs(self.参战单位[self.单位编号[操作对象]].主动技能) do
                    if 法术 ==v.名称 then
                        查找主动=true
                        break
                    end
                end
            end
            if 查找主动 then
                self.默认法术[操作对象]=法术
            else
                __UI弹出.提示框:打开("#Y设置失败该技能不是法术技能")
            end
end

function 战斗界面:获取默认法术(操作对象)
        local 返回="右键使用及设置默认法术"
        if self.默认法术[操作对象] then
            返回=self.默认法术[操作对象]
        end
        return 返回
  end




function 战斗界面:设置指令1(编号)
    self.完成指令[self.操作对象] = {
        类型 = self.命令类型,
        目标 = 编号,
        敌我 = 0,
        参数 = self.命令参数,
        附加 = self.命令附加,
        id = self.单位编号[self.操作对象]
    }
    if self.操作对象 >= self.对象上限 then
        self:完成命令()
    else
        self.操作对象 = self.操作对象 + 1
        self:重置()
    end
end

function 战斗界面:设置法术参数(法术)
    if nil == 法术 then
        return
    end
    local  查找主动 = false
    local 类型="法术"
    for k,v in pairs(self.参战单位[self.单位编号[self.操作对象]].主动技能) do
        if 法术.名称 ==v.名称 then
            查找主动=true
            break
        end
    end
    if self.参战单位[self.单位编号[self.操作对象]].类型== "角色" and not 查找主动 then
        for k,v in pairs(self.参战单位[self.单位编号[self.操作对象]].特技技能) do
            if 法术.名称 ==v.名称 then
                查找主动=true
                类型="特技"
                break
            end
        end
    end
   if  查找主动 then
        self.命令类型=类型
        self.命令参数 = 法术.名称
        local 临时种类 = 取技能(法术.名称)

        self.命令附加 = 临时种类[3]
        if 法术.名称 == "妙手空空" then
            self.命令附加 = 4
        end
        if self.命令附加==66 then
            self:设置指令(self.单位编号[self.操作对象])
        else
            self:操作重置()
        end
    else
        __UI弹出.提示框:打开("#Y无法使用该法术")
        self:重置()
    end
end


function 战斗界面:设置快捷法术(法术)
    if nil == 法术 then
        return
    end
    local  查找主动 = false
    for k,v in pairs(self.参战单位[self.单位编号[self.操作对象]].主动技能) do
        if 法术 == v.名称 then
            查找主动=true
            break
        end
    end
   if  查找主动 then
        self.命令类型="法术"
        self.命令参数 = 法术
        local 临时种类 = 取技能(法术)
        self.命令附加 = 临时种类[3]
        if 法术 == "妙手空空" then
            self.命令附加 = 4
        end
        if self.命令附加==66 then
            self:设置指令(self.单位编号[self.操作对象])
        else
                self.敌方组 = {}
                self.我方组 = {}
                for n = 1, #self.参战单位, 1 do
                    if self.参战单位[n].敌我 == 2 then
                        self.敌方组[#self.敌方组 + 1] = n
                    elseif self.参战单位[n].敌我 == 1 then
                        self.我方组[#self.我方组 + 1] = n
                    end
                end
                local 编号 = 1
                if self.命令附加==4 then
                    编号 = self.敌方组[math.random(1, #self.敌方组)]
                elseif self.命令附加==3 or self.命令附加==5 or self.命令附加==6 then
                    编号 = self.我方组[math.random(1, #self.我方组)]
                end
                self:设置指令(编号)
        end
        鼠标层:正常形状()
    else
        __UI弹出.提示框:打开("#Y未找到该技能!自动转为普通攻击...")
        战斗界面.命令类型 = "攻击"
        战斗界面:设置指令1(0)
    end
end








function 战斗界面:设置道具(玩家id,无法使用)
    战斗层.战斗道具:打开(玩家id,无法使用)
end

function 战斗界面:设置灵宝(data)
    战斗层.战斗灵宝:打开(data)
end

function 战斗界面:设置道具参数(编号, 对象)
    self.命令类型 = "道具"
    self.命令附加 = 对象
    self.命令参数 = 编号
    战斗层.战斗道具:置可见(false)
    self:操作重置()
end



function 战斗界面:设置灵宝参数(编号, 对象)
    self.命令类型 = "道具"
    self.命令附加 = 对象
    self.命令参数 = 编号
    战斗层.战斗灵宝:置可见(false)
    if  self.命令附加==66 then
        self:设置指令(self.单位编号[self.操作对象])
    else
        self:操作重置()
    end
end

function 战斗界面:设置逃跑(id)
    self.完成指令[self.操作对象] = {
        类型 = "逃跑",
        目标 = 1,
        敌我 = 0,
        参数 = self.命令参数,
        附加 = 1,
    }
    if self.操作对象 >= self.对象上限 then
        self:完成命令()
    else
        self.操作对象 = self.操作对象 + 1
        self:重置()
    end
end

function 战斗界面:设置召唤(id)
    self.命令类型 = "召唤"
    self.完成指令[self.操作对象] = {
       类型 = self.命令类型,
       目标 = id,
       敌我 = 0,
       参数 = self.命令参数,
       附加 = self.命令附加,
    }
    self:置可见(true)
    战斗层.战斗召唤:置可见(false)
    if self.操作对象 >= self.对象上限 then
        self:完成命令()
    else
        self.操作对象 = self.操作对象 + 1
        self:重置()
    end
end







local 人物指令 = 战斗界面:创建控件("人物指令")

function 人物指令:初始化()

    if __手机 then
        self:置坐标(0,25)
        self:置宽高(420, 355)
        self:置精灵()
    else
        self:置坐标(250,25)
        self:置宽高(70, 300)
        self:置精灵(__res:取资源动画("dlzy",0xF5A9A3F5,"精灵"))
    end
end


local 法术 = 人物指令:创建按钮("法术")
function 法术:初始化()
    local tcp=__res:取资源动画("dlzy",0x839C6C7D)
    self:置坐标(3,4)
    if __手机 then
        tcp=__res:getPNGCC(2, 719, 3, 54, 54)
        self:置坐标(360,0)  
    end
    self:创建按钮精灵(tcp,1)
 
end

function 法术:左键按下(x, y)
      界面层.按下=false
end
function 法术:左键弹起(x, y)
        local 操作单位=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]]
        战斗层.战斗法术:打开(操作单位.主动技能,"法术",战斗界面.操作对象,操作单位)
      
end


local 法宝 = 人物指令:创建按钮("法宝")
function 法宝:初始化()
    local tcp=__res:取资源动画("dlzy",0x2E8F2187)
    self:置坐标(3,28)
    if __手机 then
        tcp=__res:getPNGCC(2, 653, 3, 54, 54)
        self:置坐标(360,60) 
    end
    self:创建按钮精灵(tcp,1)
end
function 法宝:左键弹起(x, y)
        请求服务(5508)
end
function 法宝:左键按下(x, y)
  界面层.按下=false
end

local 特技 = 人物指令:创建按钮("特技")
function 特技:初始化()
        local tcp=__res:取资源动画("dlzy",0x389DCCF5)
        self:置坐标(3,52)
        if __手机 then
            tcp=__res:getPNGCC(2, 586, 3, 54, 54)
            self:置坐标(360,120)
        end
        self:创建按钮精灵(tcp,1)
end
function 特技:左键弹起(x, y)
    local 操作单位=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]]
    战斗层.战斗法术:打开(操作单位.特技技能,"特技",战斗界面.操作对象,操作单位)
     
end
function 特技:左键按下(x, y)
  界面层.按下=false
end

local 灵宝 = 人物指令:创建按钮("灵宝")
function 灵宝:初始化()
    local tcp=__res:取资源动画("jszy/zztx",0x00000001)
    self:置坐标(3,76)
    if __手机 then
        tcp=__res:getPNGCC(2, 845, 3, 54, 54)
        self:置坐标(360,180)
    end
    self:创建按钮精灵(tcp,1)
end
function 灵宝:左键弹起(x, y)
     请求服务(5523)
end
function 灵宝:左键按下(x, y)
  界面层.按下=false
end

local 道具 = 人物指令:创建按钮("道具")
function 道具:初始化()
    local tcp=__res:取资源动画("dlzy",0x1587C26E)
    self:置坐标(3,100)
    if __手机 then
        tcp=__res:getPNGCC(2, 521, 3, 54, 54)
        self:置坐标(360,240) 
    end
    self:创建按钮精灵(tcp,1)
end
function 道具:左键弹起(x, y)
        请求服务(5599)
end
function 道具:左键按下(x, y)
  界面层.按下=false
end
local 防御 = 人物指令:创建按钮("防御")
function 防御:初始化()
    local tcp=__res:取资源动画("dlzy",0xA662D44B)
    self:置坐标(3,124)
    if __手机 then
        tcp=__res:getPNGCC(2, 329, 3, 54, 54)
        self:置坐标(300,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 防御:左键弹起(x, y)
        战斗界面.命令类型 = "防御"
        战斗界面:设置指令1(0)
end

function 防御:左键按下(x, y)
  界面层.按下=false
end


local 保护 = 人物指令:创建按钮("保护")
function 保护:初始化()
    local tcp=__res:取资源动画("dlzy",0x0467A0A8)
    self:置坐标(3,148)
    if __手机 then
        tcp=__res:getPNGCC(2, 203, 3, 54, 54)
        self:置坐标(240,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 保护:左键弹起(x, y)
        战斗界面.命令类型 = "保护"
        战斗界面:操作重置()
end

function 保护:左键按下(x, y)
  界面层.按下=false
end
local 召唤 = 人物指令:创建按钮("召唤")
function 召唤:初始化()
    local tcp=__res:取资源动画("dlzy",0xA2E2DC42)
    self:置坐标(3,172)
    if __手机 then
        tcp=__res:getPNGCC(2, 459.0, 3, 54, 54)
        self:置坐标(180,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 召唤:左键弹起(x, y)
        请求服务(5505)
end
function 召唤:左键按下(x, y)
  界面层.按下=false
end
local 召还 = 人物指令:创建按钮("召还")
function 召还:初始化()
    local tcp=__res:取资源动画("dlzy",0xE1A79F93)
    self:置坐标(3,196)
    if __手机 then
        tcp=__res:getPNGCC(2, 138, 3, 54, 54)
        self:置坐标(120,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 召还:左键弹起(x, y)

end
function 召还:左键按下(x, y)
  界面层.按下=false
end

local 自动 = 人物指令:创建按钮("自动")
function 自动:初始化()
    local tcp=__res:取资源动画("dlzy",0xEFB4F757)
    self:置坐标(3,220)
    if __手机 then
        tcp=__res:getPNGCC(2, 391, 3, 54, 54)
        self:置坐标(360,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 自动:左键弹起(x, y)
        请求服务(5507)
end
function 自动:左键按下(x, y)
  界面层.按下=false
end




local 捕捉 = 人物指令:创建按钮("捕捉")
function 捕捉:初始化()
    local tcp=__res:取资源动画("dlzy",0x2ACB414D)
    self:置坐标(3,244)
    if __手机 then
        tcp=__res:getPNGCC(2, 10, 3, 54, 54)
        self:置坐标(60,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 捕捉:左键弹起(x, y)
        战斗界面.命令类型 = "捕捉"
        战斗界面.命令附加 = 4
        战斗界面:操作重置()
end
function 捕捉:左键按下(x, y)
  界面层.按下=false
end

local 逃跑 = 人物指令:创建按钮("逃跑")
function 逃跑:初始化()
    local tcp=__res:取资源动画("dlzy",0x132041E1)
    self:置坐标(3,268)
    if __手机 then
        tcp=__res:getPNGCC(2, 72, 3, 54, 54)
        self:置坐标(0,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 逃跑:左键弹起(x, y)
        战斗界面:设置逃跑()
end
function 逃跑:左键按下(x, y)
  界面层.按下=false
end



local 宝宝指令 = 战斗界面:创建控件("宝宝指令")
function 宝宝指令:初始化()
    if __手机 then
        self:置坐标(300,25)
        self:置宽高(120, 360)
        self:置精灵()
    else
        self:置坐标(250,25)
        self:置宽高(70, 185)
        self:置精灵(__res:取资源动画("dlzy",0xAABBCC8A,"精灵"))
    end

    
end


local 法术1 = 宝宝指令:创建按钮("法术")
function 法术1:初始化()
    local tcp=__res:取资源动画("dlzy",0x839C6C7D)
    self:置坐标(3,2)
    if __手机 then
        tcp=__res:getPNGCC(2, 719, 3, 54, 54)
        self:置坐标(60,0)
    end
    self:创建按钮精灵(tcp,1)
end
function 法术1:左键弹起(x, y)
        local 操作单位=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]]
        战斗层.战斗法术:打开(操作单位.主动技能,"法术",战斗界面.操作对象,操作单位)
end
function 法术1:左键按下(x, y)
  界面层.按下=false
end
local 道具1 = 宝宝指令:创建按钮("道具")
function 道具1:初始化()
    local tcp=__res:取资源动画("dlzy",0x1587C26E)
    self:置坐标(3,25)
    if __手机 then
        tcp=__res:getPNGCC(2, 521, 3, 54, 54)
        self:置坐标(60,60)
    end
    self:创建按钮精灵(tcp,1)
end
function 道具1:左键弹起(x, y)
        请求服务(5504,{玩家=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].编号})
end
function 道具1:左键按下(x, y)
  界面层.按下=false
end
local 防御1 = 宝宝指令:创建按钮("防御")
function 防御1:初始化()
    local tcp=__res:取资源动画("dlzy",0xA662D44B)
    self:置坐标(3,48)
    if __手机 then
        tcp=__res:getPNGCC(2, 329, 3, 54, 54)
        self:置坐标(60,120)
    end
    self:创建按钮精灵(tcp,1)
end
function 防御1:左键弹起(x, y)
        战斗界面.命令类型 = "防御"
        战斗界面:设置指令1(0)
end
function 防御1:左键按下(x, y)
  界面层.按下=false
end
local 保护1 = 宝宝指令:创建按钮("保护")
function 保护1:初始化()
    local tcp=__res:取资源动画("dlzy",0x0467A0A8)
    self:置坐标(3,71)
    if __手机 then
        tcp=__res:getPNGCC(2, 203, 3, 54, 54)
        self:置坐标(60,180)  
    end
    self:创建按钮精灵(tcp,1)
end
function 保护1:左键弹起(x, y)
        战斗界面.命令类型 = "保护"
        战斗界面:操作重置()
end
function 保护1:左键按下(x, y)
  界面层.按下=false
end
local 召还1 = 宝宝指令:创建按钮("召还")
function 召还1:初始化()
    local tcp=__res:取资源动画("dlzy",0xE1A79F93)
    self:置坐标(3,94)
    if __手机 then
        tcp=__res:getPNGCC(2, 138, 3, 54, 54)
        self:置坐标(60,240)
    end
    self:创建按钮精灵(tcp,1)
end
function 召还1:左键弹起(x, y)

end
function 召还1:左键按下(x, y)
  界面层.按下=false
end



local 逃跑1 = 宝宝指令:创建按钮("逃跑")
function 逃跑1:初始化()
    local tcp=__res:取资源动画("dlzy",0x132041E1)
    self:置坐标(3,117)
    if __手机 then
        tcp=__res:getPNGCC(2, 72, 3, 54, 54)
        self:置坐标(0,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 逃跑1:左键弹起(x, y)
        战斗界面:设置逃跑()
end
function 逃跑1:左键按下(x, y)
  界面层.按下=false
end
local 自动1 = 宝宝指令:创建按钮("自动")
function 自动1:初始化()
    local tcp=__res:取资源动画("dlzy",0xEFB4F757)
    self:置坐标(3,141)
    if __手机 then
        tcp=__res:getPNGCC(2, 391, 3, 54, 54)
        self:置坐标(60,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 自动1:左键按下(x, y)
  界面层.按下=false
end





local 角色指令 = 战斗界面:创建控件("角色指令")
function 角色指令:初始化()
    if __手机 then
        self:置坐标(180,25)
        self:置宽高(240, 360)
        self:置精灵()
    else
        self:置坐标(250,25)
        self:置宽高(70, 230)
        self:置精灵(__res:取资源动画("dlzy",0xAABBCC8A,"图像"):拉伸(63,220):到精灵())
    end

    
end


local 法术2 = 角色指令:创建按钮("法术")
function 法术2:初始化()
    local tcp=__res:取资源动画("dlzy",0x839C6C7D)
    self:置坐标(3,2)
    if __手机 then
        tcp=__res:getPNGCC(2, 719, 3, 54, 54)
        self:置坐标(180,0)
    end
    self:创建按钮精灵(tcp,1)
   
end
function 法术2:左键按下(x, y)
  界面层.按下=false
end


function 法术2:左键弹起(x, y)
        local 操作单位=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]]
        战斗层.战斗法术:打开(操作单位.主动技能,"法术",战斗界面.操作对象,操作单位)
end



local 法宝2 = 角色指令:创建按钮("法宝")
function 法宝2:初始化()
    local tcp=__res:取资源动画("dlzy",0x2E8F2187)
    self:置坐标(3,26)
    if __手机 then
        tcp=__res:getPNGCC(2, 653, 3, 54, 54)
        self:置坐标(180,60)
    end
    self:创建按钮精灵(tcp,1)
end
function 法宝2:左键弹起(x, y)
        请求服务(5508,{玩家=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].编号})
end
function 法宝2:左键按下(x, y)
  界面层.按下=false
end

local 特技2 = 角色指令:创建按钮("特技")
function 特技2:初始化()
    local tcp=__res:取资源动画("dlzy",0x389DCCF5)
    self:置坐标(3,50)
    if __手机 then
        tcp=__res:getPNGCC(2, 586, 3, 54, 54)
        self:置坐标(180,120)
    end
    self:创建按钮精灵(tcp,1)
end
function 特技2:左键弹起(x, y)
    local 操作单位=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]]
    战斗层.战斗法术:打开(操作单位.特技技能,"特技",战斗界面.操作对象,操作单位)


     
end
function 特技2:左键按下(x, y)
  界面层.按下=false
end

local 灵宝2 = 角色指令:创建按钮("灵宝")
function 灵宝2:初始化()
    local tcp=__res:取资源动画("jszy/zztx",0x00000001)
    self:置坐标(3,74)
    if __手机 then
        tcp=__res:getPNGCC(2, 845, 3, 54, 54)
        self:置坐标(180,180)  
    end
    self:创建按钮精灵(tcp,1)
end
function 灵宝2:左键弹起(x, y)
        请求服务(5523,{玩家=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].编号})
end

function 灵宝2:左键按下(x, y)
  界面层.按下=false
end

local 保护2 = 角色指令:创建按钮("保护")
function 保护2:初始化()
    local tcp=__res:取资源动画("dlzy",0x0467A0A8)
   -- self:置坐标(3,71)
    self:置坐标(3,98)
    if __手机 then
        tcp=__res:getPNGCC(2, 203, 3, 54, 54)
        self:置坐标(120,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 保护2:左键弹起(x, y)
        战斗界面.命令类型 = "保护"
        战斗界面:操作重置()
end
function 保护2:左键按下(x, y)
  界面层.按下=false
end



local 道具2 = 角色指令:创建按钮("道具")
function 道具2:初始化()
    local tcp=__res:取资源动画("dlzy",0x1587C26E)
    --self:置坐标(3,98)
    self:置坐标(3,122)
    if __手机 then
        tcp=__res:getPNGCC(2, 521, 3, 54, 54)
        self:置坐标(180,240)
    end
    self:创建按钮精灵(tcp,1)
end
function 道具2:左键弹起(x, y)
        请求服务(5599,{玩家=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].编号})
end
function 道具2:左键按下(x, y)
  界面层.按下=false
end

local 防御2 = 角色指令:创建按钮("防御")
function 防御2:初始化()
    local tcp=__res:取资源动画("dlzy",0xA662D44B)
    --self:置坐标(3,122)
    self:置坐标(3,146)
    if __手机 then
        tcp=__res:getPNGCC(2, 329, 3, 54, 54)
        self:置坐标(180,300)    
    end
    self:创建按钮精灵(tcp,1)
end
function 防御2:左键弹起(x, y)
        战斗界面.命令类型 = "防御"
        战斗界面:设置指令1(0)
end
function 防御2:左键按下(x, y)
  界面层.按下=false
end

local 召唤2 = 角色指令:创建按钮("召唤")
function 召唤2:初始化()
    local tcp=__res:取资源动画("dlzy",0xA2E2DC42)
   -- self:置坐标(3,146)
    self:置坐标(3,170)
    if __手机 then
        tcp=__res:getPNGCC(2, 459.0, 3, 54, 54)
        self:置坐标(60,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 召唤2:左键弹起(x, y)
         请求服务(5505,{玩家=战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].编号})
end
function 召唤2:左键按下(x, y)
  界面层.按下=false
end
local 捕捉2 = 角色指令:创建按钮("捕捉")
function 捕捉2:初始化()
    local tcp=__res:取资源动画("dlzy",0x2ACB414D)
    --self:置坐标(3,170)
    self:置坐标(3,194)
    if __手机 then
        tcp=__res:getPNGCC(2, 10, 3, 54, 54)
        self:置坐标(0,300)
    end
    self:创建按钮精灵(tcp,1)
end
function 捕捉2:左键弹起(x, y)
        战斗界面.命令类型 = "捕捉"
        战斗界面.命令附加 = 4
        战斗界面:操作重置()
end
function 捕捉2:左键按下(x, y)
  界面层.按下=false
end
local 九黎次数 = 战斗界面:创建控件("九黎次数")
function 九黎次数:初始化()
    self:置宽高(20, 20)
    if __手机 then
        self:置坐标(387,32)
    else
        self:置坐标(298,20)
    end
    self.法术显示={
        [1]=__res:取资源动画("pic","h1","图片"):到精灵(),
        [2]=__res:取资源动画("pic","h2","图片"):到精灵(),
        [3]=__res:取资源动画("pic","h3","图片"):到精灵(),
        [4]=__res:取资源动画("pic","h4","图片"):到精灵(),
    }
end


function 九黎次数:显示(x, y)
    if 战斗界面.法术次数[战斗界面.操作对象] and self.法术显示 then
        if 战斗界面.完成指令[战斗界面.操作对象] and 战斗界面.完成指令[战斗界面.操作对象].多重施法 and  self.法术显示[战斗界面.法术次数[战斗界面.操作对象]-#战斗界面.完成指令[战斗界面.操作对象].多重施法]  then
             self.法术显示[战斗界面.法术次数[战斗界面.操作对象]-#战斗界面.完成指令[战斗界面.操作对象].多重施法]:显示(x,y)
        elseif self.法术显示[战斗界面.法术次数[战斗界面.操作对象]] then
             self.法术显示[战斗界面.法术次数[战斗界面.操作对象]]:显示(x,y)
        end
     end
end



local 操作类 = 战斗界面:创建控件("操作类")
function 操作类:初始化()
    self:置宽高(70, 130)
    self:置坐标(350,210)
end


for i, v in ipairs({
    {
        name = "随机目标",
        x=0,
        y=0,
        tcp = __res:getPNGCC(2, 903, 1, 68, 55)
    },
    {
        name = "返回",
        x=11,
        y=65,
        tcp = __res:getPNGCC(2, 973, 1, 56, 56):拉伸(50,50)
    }
}) do
    local 临时函数 = 操作类:创建按钮(v.name, v.x, v.y)
    function 临时函数:初始化()
      self:创建按钮精灵(v.tcp,1)
    end
    function 临时函数:左键弹起(x, y)
        if v.name == "随机目标" then
            战斗界面:设置指令(0)
        elseif v.name == "返回" then
            战斗界面:重置()
        end
    end
end


function 战斗界面:键盘弹起(键码, 功能)
    if 功能 & SDL.KMOD_ALT ~= 0 then
        if 键码 == SDL.KEY_Q then
            if 战斗界面.默认法术[战斗界面.操作对象] and 战斗界面.参战单位 and 战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]] and 战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].主动技能 then  
                    战斗界面:设置法术参数({名称=战斗界面.默认法术[战斗界面.操作对象]})
            else
                __UI弹出.提示框:打开("#Y未设置快捷键技能!自动转为普通攻击...")
                战斗界面.命令类型 = "攻击"
                战斗界面:设置指令1(0)
            end
        elseif 键码 == SDL.KEY_S then
                if 战斗界面.默认法术[战斗界面.操作对象] and 战斗界面.参战单位 and 战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]] and 战斗界面.参战单位[战斗界面.单位编号[战斗界面.操作对象]].主动技能 then  
                        战斗界面:设置快捷法术(战斗界面.默认法术[战斗界面.操作对象])
                else
                    __UI弹出.提示框:打开("#Y未设置快捷键技能!自动转为普通攻击...")
                    战斗界面.命令类型 = "攻击"
                    战斗界面:设置指令1(0)
                end
        elseif 键码 == SDL.KEY_A then--快捷攻击 召唤兽资源组 角色资源组
                战斗界面.命令类型 = "攻击"
                战斗界面:设置指令1(0)
        elseif 键码 == SDL.KEY_W then--打开法术 召唤兽资源组 角色资源组
                if 战斗界面.人物指令.是否可见 then
                        战斗界面.人物指令.法术:左键弹起()
                elseif 战斗界面.宝宝指令.是否可见 then
                        战斗界面.宝宝指令.法术:左键弹起()
                elseif 战斗界面.角色指令.是否可见 then
                        战斗界面.角色指令.法术:左键弹起()
                end
        elseif 键码 == SDL.KEY_Z then--打开特技
                if 战斗界面.人物指令.是否可见 then
                        战斗界面.人物指令.特技:左键弹起()
                elseif 战斗界面.角色指令.是否可见 then
                        战斗界面.角色指令.特技:左键弹起()
                end
        elseif 键码 == SDL.KEY_T then
                战斗界面.命令类型 = "保护"
                战斗界面:操作重置()
        elseif 键码 == SDL.KEY_E then
                if 战斗界面.人物指令.是否可见 then
                        战斗界面.人物指令.道具:左键弹起()
                elseif 战斗界面.宝宝指令.是否可见 then
                        战斗界面.宝宝指令.道具:左键弹起()
                elseif 战斗界面.角色指令.是否可见 then
                        战斗界面.角色指令.道具:左键弹起()
                end
        elseif 键码 == SDL.KEY_D then
                战斗界面.命令类型 = "防御"
                战斗界面:设置指令1(0)
        elseif 键码 == SDL.KEY_R then
                if 战斗界面.人物指令.是否可见 then
                        战斗界面.人物指令.召唤:左键弹起()
                elseif 战斗界面.角色指令.是否可见 then
                        战斗界面.角色指令.召唤:左键弹起()
                end
        elseif 键码 == SDL.KEY_X  then
                if 战斗界面.人物指令.是否可见 then
                    战斗界面.人物指令.自动:左键弹起()
                end
        elseif 键码 == SDL.KEY_G  then
                if 战斗界面.人物指令.是否可见 then
                        战斗界面.人物指令.捕捉:左键弹起()
                elseif 战斗界面.角色指令.是否可见 then
                        战斗界面.角色指令.捕捉:左键弹起()
                end
        end
    end
end



