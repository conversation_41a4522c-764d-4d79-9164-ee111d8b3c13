__UI弹出["玩家信息弹出"] = __UI界面["创建弹出窗口"](__UI界面, "玩家信息弹出", 495 + abbr.py.x, 102 + abbr.py.y, 230, 322)
local 玩家信息弹出 = __UI弹出["玩家信息弹出"]
function 玩家信息弹出:初始化()
  local nsf = require("SDL.图像")(230, 322)
  if nsf["渲染开始"](nsf) then
    取黑透明背景(0, 0, 230, 322, true)["显示"](取黑透明背景(0, 0, 230, 322, true), 0, 0)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 玩家信息弹出:显示(x, y)
  if self.图像 then
    self.图像["显示"](self.图像, x, y)
  end
  if self.图像2 then
    self.图像2["显示"](self.图像2, x, y)
  end
end
function 玩家信息弹出:打开(data)
  self:置可见(true)
  self:重置(data)
end
function 玩家信息弹出:重置(data)
  local nsf = require("SDL.图像")(247, 395)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "陌生人")["显示"](字体18["取图像"](字体18, "陌生人"), 139, 20)
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18["取图像"](字体18, data["名称"])["显示"](字体18["取图像"](字体18, data["名称"]), 21, 20)
    字体18["取图像"](字体18, "ID:" .. data.ID)["显示"](字体18["取图像"](字体18, "ID:" .. data.ID), 21, 48)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.数据 = data
end
for i, v in ipairs({
  {
    name = "给予",
    x = 15,
    y = 73,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "给予"
  },
  {
    name = "交易",
    x = 119,
    y = 73,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "交易"
  },
  {
    name = "组队",
    x = 15,
    y = 123,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "组队"
  },
  {
    name = "团队",
    x = 119,
    y = 123,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "团队"
  },
  {
    name = "加为好友",
    x = 15,
    y = 173,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "加为好友"
  },
  {
    name = "发起聊天",
    x = 119,
    y = 173,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "发起聊天"
  },
  {
    name = "攻击",
    x = 15,
    y = 222,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "攻击"
  },
  {
    name = "信息",
    x = 119,
    y = 222,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "信息"
  },
  {
    name = "空间",
    x = 15,
    y = 271,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "空间"
  },
  {
    name = "举报",
    x = 119,
    y = 271,
    tcp = __res:getPNGCC(3, 880, 331, 86, 37, true)["拉伸"](__res:getPNGCC(3, 880, 331, 86, 37, true), 98, 42),
    font = "举报"
  }
}) do
  local 临时函数 = 玩家信息弹出["创建我的按钮"](玩家信息弹出, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
  if 玩家信息弹出["数据"].ID==角色信息.数字id then
    return
  end
    if v.name == "给予" then
      发送数据(3716, {
        id = 玩家信息弹出["数据"].ID
      })
    elseif v.name == "交易" then
      发送数据(3718, {
        id = 玩家信息弹出["数据"].ID
      })
    end
    玩家信息弹出["置可见"](玩家信息弹出, false)
  end
end
