
local SDLF = require("SDL.函数")
local 实时更新 = require("HPSocket.HttpClient")(true)
local ggf = require("GGE.函数")

function 实时更新:重载()
    self.receivedata = ""
    self.更新=1
    self.updata = {}
    self.upsize = 0
    self.tablereceivedata = {}
    if not self.npdata then self.npdata = {} end
end

function 实时更新:报文事件(dwConnID, pData)
   if string.find(pData,"<title>404") or string.find(pData,"<h2>404") then
        self.savepath =  nil
        self.receivedata=""
        table.remove(self.updata, 1)
    else
        self.receivedata = self.receivedata .. pData
        if #self.receivedata >= 10024000 then
           table.insert(self.tablereceivedata, self.receivedata)
           self.receivedata = ""
        end
    end
   -- print(v)
end
function 实时更新:结束事件(data)
   
    self:保存事件()
end

function 实时更新:报头事件(dwConnID, lpszName, lpszValue)
    if "Content-Length" == lpszName then
        self.upsize = lpszValue
    end
end

-- function 实时更新:结束事件(data)
--     self:保存事件()
-- end


function 实时更新:连接事件(dwConnID)
    -- print(self:取状态())
     print(dwConnID, "连接事件")

    -- self._co = dwConnID
end

function 实时更新:断开事件(ec, ed)
   -- print(self:取状态())
    print(ec, ed)
end


function 实时更新:发送请求(lpszPath, path)
     self.savepath = path
     self:GET(lpszPath)
end

function 实时更新:效验资源()
    if __res.更新资源 and self.更新==1 and not 调试模式 then
        local 加入数据 = ggf.insert(self.updata)
        for k, v in pairs(__res.更新资源) do
            if not self.npdata[k] then
                加入数据(v)
                self.npdata[k] =true
            end
        end
        __res.更新资源={}
        self:保存事件()
        self.更新=2
        __res.实时更新 = os.time()
    end
end


function 实时更新:检查更新(ip,dk)
    self.ip=ip
    self.dk=dk
    if not __手机 then return end
    if not self._hp or not self:是否连接() then
        local 访问="http://"..self.ip..":"..self.dk
        self:连接(访问)
        self:重载()
    else
        self:效验资源()
    end
end


-- function 实时更新:检查更新(ip,dk)
--         if ip and dk then
--             self.ip=ip
--             self.dk=dk
--         else
--             self.ip="*************"
--             self.dk="80"
--         end
--         if not self:是否连接() then
    
--             if self:连接(self.ip, self.dk) then

--             elseif self:连接("**************","80") then
--                 self.ip = "**************"
--                 self.dk = "80"
    
    
--             elseif self:连接("*************","80") then
--                     self.ip = "*************"
--                     self.dk = "80"
--             end 
--             self:重载()
--         else
--             self:效验资源()
--         end
-- end

function 实时更新:保存事件()

    if self.savepath then
        if #self.tablereceivedata > 0 then
            local recata = ""
            for i, v in ipairs(self.tablereceivedata) do--你有打包好的没
                recata = recata .. v
            end
            __res:写出文件(self.savepath, recata .. self.receivedata)
        elseif self.receivedata~="" then
            __res:写出文件(self.savepath, self.receivedata)
        end
        self.receivedata = ""
        self.tablereceivedata = {}
        if #self.updata > 0 then
            if self.updata[1].path == self.savepath then
                if self.updata[1].lx == "WDF" then
                    __res.wdfs:加载(SDLF.取内部存储路径() .. "/" .. self.savepath)
                    __res.wdfs:allwas(self.updata[1].pid, self.updata[1].jb)
                    if not  __res.配置.资源包[self.updata[1].name]  then
                        __res.配置.资源包[self.updata[1].name] = true
                    end
                    __res:写出文件("config.txt", zdtostring(__res.配置))
                end
            end
            table.remove(self.updata, 1)
        end
    end

    self.savepath = nil
    self.upsize = 0
    if #self.updata > 0 then
        self:发送请求(self.updata[1].http, self.updata[1].path)
    else
        __res.更新资源={}
        __res.实时更新 = nil
        self:重载()
       -- self:断开()
        --  self.receivedata = "" 
       
    end
end
return 实时更新

