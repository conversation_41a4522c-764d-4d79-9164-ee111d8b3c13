.class Lorg/libsdl/app/SDLAudioManager$1;
.super Landroid/media/AudioDeviceCallback;
.source "SDLAudioManager.java"


# annotations
.annotation system Ldalvik/annotation/EnclosingMethod;
    value = Lorg/libsdl/app/SDLAudioManager;->initialize()V
.end annotation

.annotation system Ldalvik/annotation/InnerClass;
    accessFlags = 0x0
    name = null
.end annotation


# direct methods
.method constructor <init>()V
    .locals 0

    .line 34
    invoke-direct {p0}, Landroid/media/AudioDeviceCallback;-><init>()V

    return-void
.end method

.method static synthetic lambda$onAudioDevicesAdded$0(Landroid/media/AudioDeviceInfo;)V
    .locals 2
    .param p0, "deviceInfo"    # Landroid/media/AudioDeviceInfo;

    .line 37
    invoke-virtual {p0}, Landroid/media/AudioDeviceInfo;->isSink()Z

    move-result v0

    invoke-virtual {p0}, Landroid/media/AudioDeviceInfo;->getId()I

    move-result v1

    invoke-static {v0, v1}, Lorg/libsdl/app/SDLAudioManager;->addAudioDevice(ZI)V

    return-void
.end method

.method static synthetic lambda$onAudioDevicesRemoved$1(Landroid/media/AudioDeviceInfo;)V
    .locals 2
    .param p0, "deviceInfo"    # Landroid/media/AudioDeviceInfo;

    .line 42
    invoke-virtual {p0}, Landroid/media/AudioDeviceInfo;->isSink()Z

    move-result v0

    invoke-virtual {p0}, Landroid/media/AudioDeviceInfo;->getId()I

    move-result v1

    invoke-static {v0, v1}, Lorg/libsdl/app/SDLAudioManager;->removeAudioDevice(ZI)V

    return-void
.end method


# virtual methods
.method public onAudioDevicesAdded([Landroid/media/AudioDeviceInfo;)V
    .locals 2
    .param p1, "addedDevices"    # [Landroid/media/AudioDeviceInfo;

    .line 37
    invoke-static {p1}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lorg/libsdl/app/SDLAudioManager$1$$ExternalSyntheticLambda0;->INSTANCE:Lorg/libsdl/app/SDLAudioManager$1$$ExternalSyntheticLambda0;

    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->forEach(Ljava/util/function/Consumer;)V

    .line 38
    return-void
.end method

.method public onAudioDevicesRemoved([Landroid/media/AudioDeviceInfo;)V
    .locals 2
    .param p1, "removedDevices"    # [Landroid/media/AudioDeviceInfo;

    .line 42
    invoke-static {p1}, Ljava/util/Arrays;->stream([Ljava/lang/Object;)Ljava/util/stream/Stream;

    move-result-object v0

    sget-object v1, Lorg/libsdl/app/SDLAudioManager$1$$ExternalSyntheticLambda1;->INSTANCE:Lorg/libsdl/app/SDLAudioManager$1$$ExternalSyntheticLambda1;

    invoke-interface {v0, v1}, Ljava/util/stream/Stream;->forEach(Ljava/util/function/Consumer;)V

    .line 43
    return-void
.end method
