--[[
LastEditTime: 2024-10-30 15:03:34
--]]


local 公告界面 = 登录层:创建控件("公告界面", 0, 0, 引擎.宽度, 引擎.高度)

function 公告界面:初始化()
  self:创建纹理精灵(function()
        __res:取资源动画("ui",0x01000158,"图像"):显示(引擎.宽度2-130,20)
        __res:取资源动画('ui',"01000261.was","图像"):显示(引擎.宽度2-285, 引擎.高度-450)
        __res:取资源动画('dlzy',0xD8632D24,"图像"):显示(引擎.宽度2-265, 引擎.高度-430)
        __res:取资源动画('jszy/jmxiufu',0x00010007,"图像"):显示(引擎.宽度2-90, 引擎.高度-430)
        __res:取资源动画('jszy/jmxiufu',0x00010008,"图像"):显示(引擎.宽度2+85, 引擎.高度-430)
        __res:取资源动画('jszy/other1',0x00000086,"图像"):显示(引擎.宽度2-265, 引擎.高度-380)
        __res:取资源动画('jszy/other1',0x00000019,"图像"):显示(引擎.宽度2+195, 引擎.高度-380)
      end
    )


  

    self.公告文本:置文本(__res.公告文本)
end

local 公告文本 = 公告界面:丰富文本("公告文本", 引擎.宽度2-265, 引擎.高度-345, 455, 290)
local 滑块=公告文本:创建竖向滑块("文本滑块",引擎.宽度2+200,引擎.高度-361,10,271)


local 上滚动 = 公告界面:创建按钮("上滚动", 引擎.宽度2+196, 引擎.高度-380)
function 上滚动:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/other1",0x00000031), 1)
end
function 上滚动:左键弹起(x, y, msg)
    公告文本:向上滚动()
end

local 下滚动 = 公告界面:创建按钮("下滚动", 引擎.宽度2+196, 引擎.高度-90)
function 下滚动:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/other1",0x00000032), 1)
end

function 下滚动:左键弹起(x, y, msg)
      公告文本:向下滚动()
end

local 注册账号 = 公告界面:创建按钮("注册账号", 引擎.宽度-150,  引擎.高度-210)
function 注册账号:初始化()
  self:创建按钮精灵(__res:取资源动画("ui","00010104.was"), 1)
end
local 进入游戏 = 公告界面:创建按钮("进入游戏", 引擎.宽度-150,  引擎.高度-160)
function 进入游戏:初始化()
  self:创建按钮精灵(__res:取资源动画("ui", 0x01000258),  1)
end
function 进入游戏:左键弹起(x, y, msg)
    公告界面:置可见(false)
    登录层.选择大区:置可见(true, true)
end
local 退出游戏 = 公告界面:创建按钮("退出游戏",引擎.宽度-150,  引擎.高度-110)
function 退出游戏:初始化()
  self:创建按钮精灵(__res:取资源动画("ui","01000247.was"), 1)
end
function 退出游戏:左键按下(消息, x, y)
    if 消息 then
        引擎:关闭()
    end
end
