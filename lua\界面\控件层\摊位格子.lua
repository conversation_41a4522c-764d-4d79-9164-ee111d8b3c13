local 基类 = require("界面/控件层/基类/物品基类")
local 摊位格子 = class("摊位格子", 基类)
function 摊位格子:初始化()
  self.py = {x = 0, y = 0}
end
function 摊位格子:置物品(数据, lx, 背景)
  self.精灵 = nil
  self.物品 = nil
  if "摊位物品" == lx then
    local nsf = require("SDL.图像")(244, 67)
    self:取数据(数据)
    if nsf["渲染开始"](nsf) then
      -- __res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 244, 67)["显示"](__res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 244, 67), 0, 0)
     -- __res:getPNGCC(4, 540, 345, 244, 66):显示(0, 0)
      __res:getPNGCC(3, 997, 1047, 178, 64):拉伸(244, 67):显示(0, 0)
      __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 7, 3)
      local 目录="shape/dj/"
      if 锦衣文件完整 and self.物品.资源 and (self.物品.资源=="r3d.dll" or self.物品.资源=="nx3d5.dll" or self.物品.资源=="nx3d6.dll") then
        目录="shape/sys/"
      end
      if self.物品.颜色区分 then
        __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])):置颜色(检查是否有物品颜色(self.物品.属性)):显示( 8, 6)
        else
        __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])),  8, 6)
        end
      -- __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])), 8, 6)
      -- 取输入背景(0, 0, 118, 23)["显示"](取输入背景(0, 0, 118, 23), 116, 38)
      -- __res:getPNGCC(3, 461, 1152, 119, 25):显示(116, 38)
      local xq=""
      if self.物品.名称=="魔兽要诀" or self.物品.名称=="高级魔兽要诀" or self.物品.名称=="点化石" or self.物品.名称=="召唤兽内丹" or self.物品.名称=="高级召唤兽内丹" then
        xq=取兽决缩写(self.物品.附带技能)
      end
      if self.物品.名称=="召唤兽内丹" or self.物品.名称=="高级召唤兽内丹" then
          if self.物品.特效 then
            xq=self.物品.特效
        end
      end
      if self.物品.名称=="百炼精铁" or self.物品.名称=="制造指南书" or self.物品.名称=="元灵晶石" or self.物品.名称=="灵饰指南书" then
        if self.物品.子类 then
            xq=self.物品.子类.."级"
        end
      end
      if self.物品.总类==5 and self.物品.分类==6 then
        if self.物品.级别限制 then
            xq=self.物品.级别限制.."级"
        end
      end
      if xq~="" then
        __res:getPNGCC(4, 716, 322, 55, 19):显示(5, 45+2-9)
        字体14["置颜色"](字体14, __取颜色("黄色"))
        local tsf = 字体14["取图像"](字体14, xq)
        tsf["显示"](tsf, (55 - tsf["宽度"]) // 2+5, 31+15+2-9)
      elseif self.物品.碎片 then
        __res:getPNGCC(4, 716, 322, 55, 19):显示(5, 45+2-9)
        字体14["置颜色"](字体14, __取颜色("黄色"))
        local tsf = 字体14["取图像"](字体14, "碎片")
        tsf["显示"](tsf, (55 - tsf["宽度"]) // 2+5, 31+15+2-9)
      end

      字体18["置颜色"](字体18, __取颜色("白色"))
      if self.物品["数量"] then
        置轮廓文字(字体18,self.物品["数量"],"黑色","白色",8, 6)
        -- 字体18["取描边图像"](字体18, self.物品["数量"])["显示"](字体18["取描边图像"](字体18, self.物品["数量"]), 8, 6)
      end
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      -- print(self.物品["名称"])
      字体18["取图像"](字体18, self.物品["名称"])["显示"](字体18["取图像"](字体18, self.物品["名称"]), 74, 9)
      字体18["取图像"](字体18, "单价")["显示"](字体18["取图像"](字体18, "单价"), 74, 40)
      字体18["置颜色"](字体18, __取银子颜色(self.物品["价格"]))
      字体18["取图像"](字体18, self.物品["价格"])["显示"](字体18["取图像"](字体18, self.物品["价格"]), 126, 37)
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
    self.格子类型 = lx
  elseif "摊位召唤兽" == lx then
    local nsf = require("SDL.图像")(350, 67)
    self:取数据(数据)
    if nsf["渲染开始"](nsf) then
      __res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 350, 67)["显示"](__res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 350, 67), 0, 0)
      __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 9, 8)
      local lssj = 取头像(数据["模型"])
      __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50), 9, 9)
      -- 取输入背景(0, 0, 118, 23)["显示"](取输入背景(0, 0, 118, 23), 114, 38)
      __res:getPNGCC(4, 790, 378, 119, 25):显示(116, 36)
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, 数据["模型"])["显示"](字体18["取图像"](字体18, 数据["模型"]), 74, 9)
      字体18["取图像"](字体18, "等级：" .. 数据["等级"])["显示"](字体18["取图像"](字体18, "等级：" .. 数据["等级"]), 195, 9)
      字体18["取图像"](字体18, "单价")["显示"](字体18["取图像"](字体18, "单价"), 74, 40)
      字体18["置颜色"](字体18, __取银子颜色(self.物品["价格"]))
      字体18["取图像"](字体18, self.物品["价格"])["显示"](字体18["取图像"](字体18, self.物品["价格"]), 124, 40)
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
    self.格子类型 = lx
  elseif "摊位出售物品" == lx then
    self:取数据(数据)
    local nsf = require("SDL.图像")(67, 67)
    if nsf["渲染开始"](nsf) then
      if "白格子" == 背景 then
        __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 0, 0)
      end
      local xq=""
      if self.物品.名称=="魔兽要诀" or self.物品.名称=="高级魔兽要诀" or self.物品.名称=="点化石" or self.物品.名称=="召唤兽内丹" or self.物品.名称=="高级召唤兽内丹" then
        xq=取兽决缩写(self.物品.附带技能)
      end
      if self.物品.名称=="召唤兽内丹" or self.物品.名称=="高级召唤兽内丹" then
          if self.物品.特效 then
            xq=self.物品.特效
        end
      end
      if self.物品.名称=="百炼精铁" or self.物品.名称=="制造指南书" or self.物品.名称=="元灵晶石" or self.物品.名称=="灵饰指南书" then
        if self.物品.子类 then
            xq=self.物品.子类.."级"
        end
      end
      if self.物品.总类==5 and self.物品.分类==6 then
        if self.物品.级别限制 then
            xq=self.物品.级别限制.."级"
        end
      end
      local 目录="shape/dj/"
      if 锦衣文件完整 and self.物品.资源 and (self.物品.资源=="r3d.dll" or self.物品.资源=="nx3d5.dll" or self.物品.资源=="nx3d6.dll") then
        目录="shape/sys/"
      end
      __res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"]))["显示"](__res["取图像"](__res, __res["取地址"](__res, 目录, self.物品["小模型资源"])), 10, 10)
      if xq~="" then
        __res:getPNGCC(4, 716, 322, 55, 19):显示(5, 45+2)
        字体14["置颜色"](字体14, __取颜色("黄色"))
        local tsf = 字体14["取图像"](字体14, xq)
        tsf["显示"](tsf, (55 - tsf["宽度"]) // 2+5, 31+15+2)
      elseif self.物品.碎片 then
        __res:getPNGCC(4, 716, 322, 55, 19):显示(5, 45+2)
        字体14["置颜色"](字体14, __取颜色("黄色"))
        local tsf = 字体14["取图像"](字体14, "碎片")
        tsf["显示"](tsf, (55 - tsf["宽度"]) // 2+5, 31+15+2)
      end
      if self.物品["数量"] then
        字体18["置颜色"](字体18, __取颜色("白色"))
        -- 字体18["取描边图像"](字体18, self.物品["数量"])["显示"](字体18["取描边图像"](字体18, self.物品["数量"]), 35, 2)
        置轮廓文字(字体18,self.物品["数量"],"黑色","白色",8, 6)
      end
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
    self.格子类型 = lx
  elseif "摊位出售召唤兽" == lx then
    local nsf = require("SDL.图像")(332, 67)
    self:取数据(数据)
    if nsf["渲染开始"](nsf) then
      __res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 332, 67)["显示"](__res:getPNGCC(3, 997, 1047, 178, 64)["拉伸"](__res:getPNGCC(3, 997, 1047, 178, 64), 332, 67), 0, 0)
      __res:getPNGCC(3, 757, 291, 57, 56)["显示"](__res:getPNGCC(3, 757, 291, 57, 56), 9, 5)
      local lssj = 取头像(数据["模型"])
      __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50), 12, 9)
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, 数据["名称"])["显示"](字体18["取图像"](字体18, 数据["名称"]), 86, 14)
      字体18["取图像"](字体18, "等级：" .. 数据["等级"])["显示"](字体18["取图像"](字体18, "等级：" .. 数据["等级"]), 86, 37)
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
    self.格子类型 = lx
  elseif "交易召唤兽" == lx then
    local nsf = require("SDL.图像")(317, 64)
    self:取数据(数据)
    if nsf["渲染开始"](nsf) then
      __res:getPNGCC(3, 851, 539, 198, 57)["拉伸"](__res:getPNGCC(3, 851, 539, 198, 57), 317, 64)["显示"](__res:getPNGCC(3, 851, 539, 198, 57)["拉伸"](__res:getPNGCC(3, 851, 539, 198, 57), 317, 64), 0, 0)
      __res:getPNGCC(3, 757, 291, 57, 56)["显示"](__res:getPNGCC(3, 757, 291, 57, 56), 9, 4)
      local lssj = 取头像(数据["模型"])
      __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50), 12, 7)
      字体18["置颜色"](字体18, __取颜色("白色"))
      字体18["取图像"](字体18, 数据["名称"])["显示"](字体18["取图像"](字体18, 数据["名称"]), 86, 14)
      字体18["取图像"](字体18, "等级：" .. 数据["等级"])["显示"](字体18["取图像"](字体18, "等级：" .. 数据["等级"]), 86, 37)
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
    self.格子类型 = lx
  end
  self.数据 = 数据
end
function 摊位格子:详情打开(x, y, w, h, lx, bh)
  if self.格子类型 == "摊位物品" then
    local Button, Button2, Button3, Button4
    __UI弹出["道具详情"]["置可见"](__UI弹出["道具详情"], true, true)
    __UI弹出["道具详情"]["道具文本"]["清空"](__UI弹出["道具详情"]["道具文本"])
    __UI弹出["道具详情"]["打开"](__UI弹出["道具详情"], self.物品, x, y, 360, 360, Button, Button2, Button3, Button4, bh, lx)
  end
end
-- function 摊位格子:更新(dt)
-- end
function 摊位格子:显示(x, y)
  if self.精灵 then
    self.精灵["显示"](self.精灵, x + self.py.x, y + self.py.y)
  end
  if self.确定 and self.格子类型 == "摊位物品" then
    __主控["摊位物品选中"]["显示"](__主控["摊位物品选中"], x, y)
  elseif self.确定 and self.格子类型 == "摊位召唤兽" then
    __主控["摊位召唤兽选中"]["显示"](__主控["摊位召唤兽选中"], x+1, y)
  end
  if self.格子类型 == "摊位出售物品" then
    if self.确定 then
      __主控["道具选中大"]["显示"](__主控["道具选中大"], x+1, y+2)
    end
    if self.已上架 then
      __主控["已上架"]["显示"](__主控["已上架"], x + 21, y + 4)
    end
  elseif self.格子类型 == "摊位出售召唤兽" then
    if self.确定 then
      __主控["摊位召唤兽出售选中"]["显示"](__主控["摊位召唤兽出售选中"], x, y)
      -- __主控["摊位召唤兽选中"]["显示"](__主控["摊位召唤兽选中"], x+1, y)
    end
    if self.已上架 then
      __主控["已上架"]["显示"](__主控["已上架"], x + 284, y)
    end
  end
end
return 摊位格子
