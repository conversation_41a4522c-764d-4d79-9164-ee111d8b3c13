

local 摊位出售 = 窗口层:创建窗口("摊位出售", 0, 0, 290, 405)
function 摊位出售:初始化()
  self.类型="物品类"
  self.打造上架 = {打造技巧={},裁缝技巧={},炼金术={},淬灵之术={}}
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true 
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end





function 摊位出售:打开(名称,角色名称,id,物品数据,bb数据,打造数据)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.bb选中=nil
  self.选中技能=nil
  self.选中编号=nil
  self.物品选中=nil
  self.类型="物品类"
  self.道具网格:置数据()
  self.单价输入:置数值()
  self.单价输入:置颜色(0, 0, 0, 255)
  self.名称输入:置文本("杂货摊位")
  self.打造上架 = {打造技巧={},裁缝技巧={},炼金术={},淬灵之术={}}
  self:刷新(名称,角色名称,id,物品数据,bb数据,打造数据)
end

function 摊位出售:刷新(名称,角色名称,id,物品数据,bb数据,打造数据)
        self.摊主id= id
        self.摊主名称 = 角色名称
        self.上架bb = bb数据
        self.上架物品 = 物品数据
        self.打造上架 = 打造数据
        self.名称输入:置文本(名称)
        self[self.类型]:置选中(true)



        -- self.召唤兽列表:置数据()


        self:道具刷新()
end

function 摊位出售:道具刷新()
        self.道具列表 = table.copy(_tp.道具列表)
        for k, v in pairs(self.道具列表) do
          if  self.上架物品 and  self.上架物品[k] then
              v.灰度=true
              v.附加显示="已上架"
          end
        end



       
        self.道具网格:置物品(self.道具列表)
        self.制造控件.制造网格:置数据()
        self.名称选择:置数据()
        self:显示刷新()
end


function 摊位出售:显示刷新()
  self.道具网格:置可见(false)
  self.选择技能:置可见(false)
  self.单价输入:置可见(false)
  self.名称选择:置可见(false)
  self.名称滑块:置可见(false)
  self.收摊回家:置可见(false)
  self.上架按钮:置可见(false)
  self.制造控件:置可见(false)
  

  
  self.上架按钮:置文字(45,22,"上架")
  self:创建纹理精灵(function()
      置窗口背景("摊位出售", 0, 0, 290, 405,true):显示(0,0)
      取输入背景(0, 0, 120, 22):显示(50,32)
      取输入背景(0, 0, 120, 22):显示(50,62)
      取输入背景(0, 0, 80, 22):显示(195,62)
      文本字体:置颜色(255,255,255,255)
      文本字体:取图像("招牌"):显示(15, 35)
      文本字体:取图像("摊主"):显示(15, 65)
      文本字体:取图像("ID"):显示(175, 65)
      文本字体:置颜色(0,0,0,255)
      文本字体:取图像(self.摊主名称):显示(55, 66)
      文本字体:取图像(self.摊主id):显示(200, 66)
      if self.类型~="制造类" then
          self.上架按钮:置可见(true)
          self.收摊回家:置可见(true)
          self.单价输入:置可见(true)
          self.单价输入:置颜色(0, 0, 0, 255)
          取输入背景(0, 0, 100, 22):显示(80,367)
          文本字体:置颜色(255,255,255,255):取图像("出售单价"):显示(15, 370)
      end
      if self.类型=="物品类"  then
          self.收摊回家:置可见(true)
          self.道具网格:置可见(true)
          if self.物品选中 and self.道具列表[self.物品选中] and self.上架物品[self.物品选中] then
            self.上架按钮:置文字(45,22,"下架")
            self.单价输入:置颜色(__取银子颜色(self.上架物品[self.物品选中]))
            self.单价输入:置数值(self.上架物品[self.物品选中])
            
          end




        
      elseif self.类型=="召唤兽" then
            self.名称选择:置可见(true)
            self.名称滑块:置可见(true)
            取白色背景(0, 0, 260, 240, true):显示(15,120)
            if self.bb选中 and 角色信息.宝宝列表[self.bb选中] and self.上架bb[self.bb选中] then
                  self.上架按钮:置文字(45,22,"下架")
                  self.单价输入:置颜色(__取银子颜色(self.上架bb[self.bb选中]))
                  self.单价输入:置数值(self.上架bb[self.bb选中])
            end
      elseif self.类型=="制造类" then
            self.选择技能:置可见(true)
            self.制造控件:置可见(true)
            取白色背景(0, 0, 260, 240, true):显示(15,150)
            取输入背景(0, 0, 120, 22):显示(80,122)
            文本字体:置颜色(255,255,255,255):取图像("选择技能"):显示(15, 125)
            if self.选中技能 then
                文本字体:置颜色(0,0,0,255):取图像(self.选中技能):显示(85, 125)
                self.制造控件.制造网格:置数据()
            end




      end

  end)

end







local 道具网格=摊位出售:创建背包网格("道具网格",15,120)
function 道具网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
              __UI弹出.道具提示:打开(物品.物品,x+25,y+25)
          end
end



function 道具网格:左键弹起(x, y, a)
      local 物品=self:选中物品() 
      if 物品 and 物品.物品 and self:选中()~=0 then
            if __手机 then
                __UI弹出.道具提示:打开(物品.物品,x+25,y+25,道具网格,"选择",1)
            else
                self:选择(1)
            end
      end

end

function 道具网格:选择(编号)
    if 编号 and 编号~=0 then
        摊位出售.物品选中=self:选中()
        摊位出售:显示刷新()
    end
end




local 名称选择 = 摊位出售:创建列表("名称选择", 20, 125, 243, 230)  
function 名称选择:初始化()
    self.行高度= 37
    self.行间距 = 3
end
local 滑块=摊位出售:创建竖向滑块("名称滑块",265,120,10,240,true)
名称选择:绑定滑块(滑块.滑块)
--local 滑块=名称选择:创建竖向滑块("名称滑块",180,35,10,190)

function 名称选择:置数据()
      self:清空()
      for i, v in ipairs(角色信息.宝宝列表) do
          self:添加():创建纹理精灵(function()
            __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
            local lssj = 取头像(v.模型)
            __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
            if v.加锁 then
              __res:取资源动画("jszy/xjiem",0X85655274,"图像"):显示(225,22)
            end
            if 摊位出售.上架bb[i] then
                文本字体:置颜色(__取颜色("红色"))
                文本字体:取图像("上架中"):显示(200,12)
                文本字体:取图像(v.名称):显示(40,4)
                文本字体:取图像(v.等级.."级"):显示(40,20)
            else
                文本字体:置颜色(0,0,0,255)
                文本字体:取图像(v.名称):显示(40,4)
                文本字体:取图像(v.等级.."级"):显示(40,20)
            end
           
          end)
      end
      if 摊位出售.bb选中 and 角色信息.宝宝列表[摊位出售.bb选中] and self.子控件[摊位出售.bb选中] then
            self:置选中(摊位出售.bb选中)
      else
          摊位出售.bb选中=nil
      end
 
end



function 名称选择:左键弹起(x, y, i)
      if 角色信息.宝宝列表[i] then
            摊位出售.bb选中 = i
            摊位出售:显示刷新()
      end
end

local 制造控件 = 摊位出售:创建控件("制造控件", 15, 150, 260,240)
local 制造网格 = 制造控件:创建网格("制造网格", 5, 5, 240, 230)
local 滑块1=制造控件:创建竖向滑块("制造滑块",250,0,10,240,true)
制造网格:绑定滑块(滑块1.滑块)

function 制造网格:置数据()
        local 数量= 0
        if 摊位出售.选中技能 and 摊位出售.选中编号 and 角色信息.辅助技能[摊位出售.选中编号].名称==摊位出售.选中技能 then
             数量=math.floor(角色信息.辅助技能[摊位出售.选中编号].等级/10)
        end
        self:创建格子(240,25,0,0,数量,1)
       
        if 数量>0 then
            for i, v in ipairs(self.子控件) do
                  v:创建纹理精灵(function()
                     取输入背景(0, 0, 140, 22):显示(45,2)
                     文本字体:置颜色(0,0,0):取图像((i*10).."级"):显示(0,5)
                  end)
                  local 临时按钮= v:创建红色按钮("上架","按钮",190,2,50,22)
                  local 临时输入 = v:创建文本输入("输入文本",50,6, 130, 18)
                  function 临时输入:初始化()
                        self:取光标精灵()
                        self:置限制字数(11)
                        self:置模式(self.数字模式)
                  end
                  if 摊位出售.打造上架 and 摊位出售.打造上架[摊位出售.选中技能] and 摊位出售.打造上架[摊位出售.选中技能][i] then
                      临时按钮:置文字(50,22,"下架")
                      临时输入:置颜色(__取银子颜色(摊位出售.打造上架[摊位出售.选中技能][i]))
                      临时输入:置数值(摊位出售.打造上架[摊位出售.选中技能][i])
                  else
                      临时按钮:置文字(50,22,"上架")
                      临时输入:置颜色(0, 0, 0, 255)
                      临时输入:置数值()
                  end
                  function 临时按钮:左键弹起(x,y)
                    if 摊位出售.打造上架 and 摊位出售.打造上架[摊位出售.选中技能] and 摊位出售.打造上架[摊位出售.选中技能][i] then
                          请求服务(3723,{打造=摊位出售.选中技能,等级 = i})
                    else
                        if 临时输入:取数值()==nil  or 临时输入:取数值()==0 or 临时输入:取数值()<1 then
                            __UI弹出.提示框:打开("#Y请输入正确的价格")
                        else
                            摊位出售.打造上架[摊位出售.选中技能][i]=临时输入:取数值()
                            请求服务(3722,{打造=摊位出售.打造上架,价格=临时输入:取数值()})
                        end
                    end
                  end
                  临时输入:置可见(true)
                  临时按钮:置可见(true)
            end
        end
end


local 选择技能=摊位出售:创建红色按钮("选择","选择技能",210,122,60, 22)

function 选择技能:左键弹起(x, y)
        local 列表={}
        for i=7,10 do
            if 角色信息.辅助技能[i].等级 >= 10 then
                local 临时列表 =  self:创建纹理精灵(function()
                      local lxxs = 取技能(角色信息.辅助技能[i].名称)
                          __res:取资源动画(lxxs[6], lxxs[7],"图像"):显示(1,1)
                          文本字体:置颜色(0,0,0,255)
                          文本字体:取图像(角色信息.辅助技能[i].名称):显示(45,4)
                          文本字体:取图像(角色信息.辅助技能[i].等级.."级"):显示(45,20) 
                      end,1,120,40
                  )
                  临时列表.技能编号 = i
                  table.insert(列表,临时列表)
            end
        end
        local 事件 =function (a)
              摊位出售.选中技能=nil
              摊位出售.选中编号=nil
            if 列表[a] and 列表[a].技能编号 and  角色信息.辅助技能[列表[a].技能编号] then
                  摊位出售.选中技能=角色信息.辅助技能[列表[a].技能编号].名称
                  摊位出售.选中编号=列表[a].技能编号
                  摊位出售:显示刷新()
            end
        end
        local xx,yy=self:取坐标()
        __UI弹出.弹出列表:打开(列表,取白色背景(0,0,120,#列表*42+8),事件,xx-130,yy+24)

end












local 名称输入 = 摊位出售:创建文本输入("名称输入", 55, 36, 110, 18)
function 名称输入:初始化()
    self:取光标精灵()
    self:置限制字数(12)
    self:置颜色(0, 0, 0, 255)
end

  local 更改招牌 = 摊位出售:创建红色按钮("更改招牌","更改招牌", 175,32,80, 22) 

  function 更改招牌:左键弹起(x, y)
    if 摊位出售.名称输入:取文本()==nil  or 摊位出售.名称输入:取文本()=="" then
        __UI弹出.提示框:打开("#Y请输入招牌名称")
    else
        请求服务(3721,{名称=摊位出售.名称输入:取文本()})
    end   
  end


  local 单价输入 = 摊位出售:创建文本输入("单价输入",85,371,90,18)
  function 单价输入:初始化()
        self:取光标精灵()
        self:置限制字数(11)
      
        self:置模式(self.数字模式)
  end






  local 上架按钮 = 摊位出售:创建红色按钮("上架","上架按钮",185,367,45,22) 
  function 上架按钮:左键弹起(x, y)
         if 摊位出售.类型 == "物品类" then
              if 摊位出售.物品选中 and 摊位出售.物品选中~=0 and 摊位出售.道具列表[摊位出售.物品选中] then
                  if 摊位出售.上架物品 and 摊位出售.上架物品[摊位出售.物品选中] then
                       请求服务(3723,{道具=摊位出售.物品选中})
                  else
                      if not 摊位出售.单价输入:取数值() or 摊位出售.单价输入:取数值()==0 or 摊位出售.单价输入:取数值()<1 then
                          __UI弹出.提示框:打开("#Y请输入正确的价格")
                      elseif 摊位出售.道具列表[摊位出售.物品选中].不可交易 then
                              __UI弹出.提示框:打开("#Y该物品无法转移给他人")
                      elseif 摊位出售.道具列表[摊位出售.物品选中].加锁 then
                            __UI弹出.提示框:打开("#Y请解锁该物品后在交易")
                      else
                           请求服务(3722,{道具=摊位出售.物品选中,价格=摊位出售.单价输入:取数值()})
                      end
                  end
              else
                  __UI弹出.提示框:打开("#Y请选择你要上架的物品")
              end
          elseif 摊位出售.类型 == "召唤兽" then
                if 摊位出售.bb选中 and 摊位出售.bb选中~=0 and 角色信息.宝宝列表[摊位出售.bb选中] then
                      if 摊位出售.上架bb and 摊位出售.上架bb[摊位出售.bb选中] then
                            请求服务(3723,{bb=摊位出售.bb选中})
                      else
                          if not 摊位出售.单价输入:取数值() or 摊位出售.单价输入:取数值()==0 or 摊位出售.单价输入:取数值()<1 then
                              __UI弹出.提示框:打开("#Y请输入正确的价格")
                          elseif 角色信息.宝宝列表[摊位出售.bb选中].不可交易 then
                                  __UI弹出.提示框:打开("#Y该宝宝无法转移给他人")
                          elseif 角色信息.宝宝列表[摊位出售.bb选中].加锁 then
                                __UI弹出.提示框:打开("#Y请解锁该宝宝后在交易")
                          else
                              请求服务(3722,{bb=摊位出售.bb选中,价格=摊位出售.单价输入:取数值()})
                          end
                      end
                  else
                      __UI弹出.提示框:打开("#Y请选择你要上架的宝宝")
                  end

         end 
  end




  local 收摊回家 = 摊位出售:创建红色按钮("收摊","收摊回家", 235,367,45,22) 
  function 收摊回家:左键弹起(x, y)
      请求服务(3724)
      摊位出售:置可见(false)
  end





  local 类型设置 = {"物品类","召唤兽","制造类"}
  for i, v in ipairs(类型设置) do
        local 临时函数 = 摊位出售:创建红色单选按钮(v, v,22+(i-1)*85,95,80, 22)
        function  临时函数:左键弹起(x, y)
            if 摊位出售.类型~=v then
                摊位出售.单价输入:置数值()
                摊位出售.单价输入:置颜色(0, 0, 0, 255)
                摊位出售.bb选中=nil
                摊位出售.选中技能=nil
                摊位出售.物品选中=nil
                摊位出售.类型=v
                摊位出售:道具刷新()
            end
        end
  end


local 关闭 = 摊位出售:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  摊位出售:置可见(false)
end



