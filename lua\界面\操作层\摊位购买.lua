

local 摊位购买 = 窗口层:创建窗口("摊位购买", 0, 0, 290, 450)
function 摊位购买:初始化()


  self.类型="物品类"
  self.打造上架 = {}
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
end


function 摊位购买:打开(名称,角色名称,id,物品数据,bb数据,打造数据,熟练度)
      self:置可见(not self.是否可见)
      if not self.是否可见 then
          return
      end
      self.bb选中=nil
      self.选中技能=nil
      self.物品选中=nil
      self.类型="物品类"
      self.打造上架 = {打造技巧={},裁缝技巧={},炼金术={},淬灵之术={}}
      self.熟练度 ={打造技巧=0,裁缝技巧=0,炼金术=0,淬灵之术=0}
      self.购买数量=1
      self.道具网格:置数据()
      self:刷新(名称,角色名称,id,物品数据,bb数据,打造数据,熟练度)
end

function 摊位购买:刷新(名称,角色名称,id,物品数据,bb数据,打造数据,熟练度)
      self[self.类型]:置选中(true)
      self.摊位名称=名称
      self.摊主id=id
      self.摊主名称=角色名称
      self.道具列表 ={}
      for k, v in pairs(物品数据) do
            table.insert(self.道具列表,v)
      end
      self.上架bb={}
      for k, v in pairs(bb数据) do
          local 添加数据 = v
          添加数据.上架编号=tonumber(k)
          table.insert(self.上架bb,添加数据)
      end
      self.打造上架 =table.copy(打造数据)
      self.熟练度 = table.copy(熟练度)
      self:道具刷新()
end
function 摊位购买:道具刷新()
      self.道具网格:置物品(self.道具列表)
      self.制造控件.制造网格:置数据()
      self.名称选择:置数据()
      self:显示刷新()
end



function 摊位购买:显示刷新()
  self.道具网格:置可见(false)
  self.选择技能:置可见(false)
  self.名称选择:置可见(false)
  self.名称滑块:置可见(false)
  self.制造控件:置可见(false)
  self.数量输入:置可见(false)
  self.数量输入:置数值()
  self.关注:置选中(false)
  if __关注摊位[self.摊主id] then
    self.关注:置选中(true)
  end
  self:创建纹理精灵(function()
      置窗口背景("摊位购买", 0, 0, 290, 450,true):显示(0,0)
      取输入背景(0, 0, 120, 22):显示(50,32)
      取输入背景(0, 0, 120, 22):显示(50,62)
      取输入背景(0, 0, 80, 22):显示(195,62)
      文本字体:置颜色(255,255,255,255)
      文本字体:取图像("招牌"):显示(15, 35)
      文本字体:取图像("摊主"):显示(15, 65)
      文本字体:取图像("ID"):显示(175, 65)
      文本字体:取图像("关注"):显示(235, 35)
      文本字体:置颜色(0,0,0,255)
      文本字体:取图像(self.摊位名称):显示(55, 35)
      文本字体:取图像(self.摊主名称):显示(55, 66)
      文本字体:取图像(self.摊主id):显示(200, 66)
      
      if self.类型~="制造类" then
          self.数量输入:置可见(true)
          取输入背景(0, 0, 100, 22):显示(40,367)
          取输入背景(0, 0, 100, 22):显示(180,367)
          取输入背景(0, 0, 100, 22):显示(40,392)
          取输入背景(0, 0, 100, 22):显示(180,392)
          文本字体:置颜色(255,255,255,255)
          文本字体:取图像("单价"):显示(7, 370)
          文本字体:取图像("数量"):显示(145, 370)
          文本字体:取图像("总价"):显示(7, 395)
          文本字体:取图像("现金"):显示(145, 395)

         
          文本字体:置颜色(__取银子颜色(角色信息.银子)):取图像(角色信息.银子):显示(185, 395)
      end
      if self.类型=="物品类" then
          self.道具网格:置可见(true)
          local 选中 = self.道具网格:选中()
          if 选中 and 选中~=0 and self.道具列表[选中]  then
            self.数量输入:置数值(self.购买数量)
            文本字体:置颜色(__取银子颜色(self.道具列表[选中].价格)):取图像(self.道具列表[选中].价格):显示(45, 370)
            local 购买价格 = self.道具列表[选中].价格*self.购买数量
            文本字体:置颜色(__取银子颜色(购买价格)):取图像(购买价格):显示(45, 395)
          end




        
      elseif self.类型=="召唤兽" then
            self.名称选择:置可见(true)
            self.名称滑块:置可见(true)
            取白色背景(0, 0, 260, 240, true):显示(15,120)
            if self.bb选中 and self.上架bb[self.bb选中] then
                self.数量输入:置数值(1)
                文本字体:置颜色(__取银子颜色(self.上架bb[self.bb选中].价格))
                文本字体:取图像(self.上架bb[self.bb选中].价格):显示(45, 370)
                文本字体:取图像(self.上架bb[self.bb选中].价格):显示(45, 395)
            end
      elseif self.类型=="制造类" then
            self.选择技能:置可见(true)
            self.制造控件:置可见(true)
            取白色背景(0, 0, 260, 240, true):显示(15,175)
            取输入背景(0, 0, 120, 22):显示(80,122)
            取输入背景(0, 0, 220, 22):显示(50,147)
            文本字体:置颜色(255,255,255,255)
            文本字体:取图像("现金"):显示(15, 150)

            文本字体:取图像("选择技能"):显示(15, 125)
            文本字体:置颜色(__取银子颜色(角色信息.银子)):取图像(角色信息.银子):显示(55, 150)
            
            if self.选中技能 then
                文本字体:置颜色(0,0,0,255):取图像(self.选中技能):显示(85, 125)
                self.制造控件.制造网格:置数据()
            end




      end

  end)

end







local 道具网格=摊位购买:创建背包网格("道具网格",15,120)
function 道具网格:获得鼠标(x,y,i)
          local 物品 = self:焦点物品()
          if 物品 and 物品.物品  then
              __UI弹出.道具提示:打开(物品.物品,x+25,y+25)
          end
end



function 道具网格:左键弹起(x, y, a)
      local 物品=self:选中物品() 
      if 物品 and 物品.物品 and self:选中()~=0 then
         if 摊位购买.物品选中 and 摊位购买.物品选中==self:选中() then
              if 物品.物品.数量 and 摊位购买.购买数量 <物品.物品.数量 then
                  摊位购买.购买数量=摊位购买.购买数量+1
              else
                  摊位购买.购买数量=1
              end
              摊位购买:显示刷新()
          else
              if __手机 then
                  __UI弹出.道具提示:打开(物品.物品,x+25,y+25,道具网格,"选择",1)
              else
                self:选择(1)
              end
          end
      else
          摊位购买.物品选中=nil
          摊位购买:显示刷新()
      end
end

function 道具网格:选择(编号)
    if 编号 and 编号~=0 then
        摊位购买.购买数量=1
        摊位购买.物品选中=self:选中()
        摊位购买:显示刷新()
    end
end






local 名称选择 = 摊位购买:创建列表("名称选择", 20, 125, 243, 230)  
function 名称选择:初始化()
    self.行高度= 37
    self.行间距 = 3
end
local 滑块=摊位购买:创建竖向滑块("名称滑块",265,120,10,240,true)
名称选择:绑定滑块(滑块.滑块)
--local 滑块=名称选择:创建竖向滑块("名称滑块",180,35,10,190)

function 名称选择:置数据()
      self:清空()
      for i, v in ipairs(摊位购买.上架bb) do
          self:添加():创建纹理精灵(function()
            __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
            local lssj = 取头像(v.模型)
            __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
            文本字体:置颜色(0,0,0,255)
            文本字体:取图像(v.名称):显示(40,4)
            文本字体:取图像(v.等级.."级"):显示(40,20)
           
          end)
      end
      if 摊位购买.bb选中 and 摊位购买.上架bb[摊位购买.bb选中] and self.子控件[摊位购买.bb选中] then
            self:置选中(摊位购买.bb选中)
      else
          摊位购买.bb选中=nil
      end
 
end



function 名称选择:左键弹起(x, y, i)
    if 摊位购买.上架bb[i] then
          if __手机 then
                local 事件=function (编号)
                        if 编号==1 then
                            摊位购买.bb选中 = i
                            摊位购买:显示刷新()
                        else
                            窗口层.召唤兽查看:打开(摊位购买.上架bb[i])
                        end
                  end
                __UI弹出.临时按钮:打开({"选择","查看"},事件,x,y)
          else
              摊位购买.bb选中 = i
              摊位购买:显示刷新()
          end
    end
end


function 名称选择:右键弹起(x, y, i)
    if 摊位购买.上架bb[i] then
        窗口层.召唤兽查看:打开(摊位购买.上架bb[i])
    end
end


local 制造控件 = 摊位购买:创建控件("制造控件", 15, 175, 260,240)
local 制造网格 = 制造控件:创建网格("制造网格", 5, 5, 240, 230)
local 滑块1=制造控件:创建竖向滑块("制造滑块",250,0,10,240,true)
制造网格:绑定滑块(滑块1.滑块)

function 制造网格:置数据()
        local 数量={}
        if 摊位购买.选中技能 and 摊位购买.打造上架[摊位购买.选中技能] then
              for i = 1, 16 do
                
                  if 摊位购买.打造上架[摊位购买.选中技能][i] and tonumber(摊位购买.打造上架[摊位购买.选中技能][i]) >0 then
                      table.insert(数量,{等级=i,价格=tonumber(摊位购买.打造上架[摊位购买.选中技能][i])})
                  end
              end
        end
        self:创建格子(240,25,0,0,#数量,1)
       
        if #数量>0 then
            for i, v in ipairs(self.子控件) do
                  v:创建纹理精灵(function()
                     取输入背景(0, 0, 140, 22):显示(45,2)
                     文本字体:置颜色(0,0,0):取图像((数量[i].等级*10).."级"):显示(0,5)
                     文本字体:置颜色(__取银子颜色(数量[i].价格)):取图像(数量[i].价格):显示(50,5)
                  end)
                  local 临时按钮= v:创建红色按钮("购买","按钮",190,2,50,22)
                  function 临时按钮:左键弹起(x,y)
                      if 摊位购买.打造上架 and 摊位购买.打造上架[摊位购买.选中技能][数量[i].等级] then
                            请求服务(3726,{打造=摊位购买.选中技能,数量=数量[i].等级})
                      end
                  end
                  临时按钮:置可见(true)
            end
        end
end


local 选择技能=摊位购买:创建红色按钮("选择","选择技能",210,122,60, 22)

function 选择技能:左键弹起(x, y)
        local 列表={}
        for k, v in pairs(摊位购买.打造上架) do
              local 加载= false 
              for i=1,16 do
                  if v[i] then
                      加载 = true
                      break
                  end
              end
              if 加载 then
                local 临时列表 =  self:创建纹理精灵(function()
                      local lxxs = 取技能(k)
                          __res:取资源动画(lxxs[6], lxxs[7],"图像"):显示(1,1)
                          文本字体:置颜色(0,0,0,255)
                          文本字体:取图像(k):显示(45,4)
                          if 摊位购买.熟练度 and 摊位购买.熟练度[k] then
                              文本字体:取图像("熟练度:"..摊位购买.熟练度[k]):显示(45,20) 
                          end
                      end,1,190,40
                  )
                  临时列表.技能编号 = k
                  table.insert(列表,临时列表)
              end
        end
        local 事件 =function (a)
              摊位购买.选中技能=nil
              if 列表[a] and 列表[a].技能编号 then
                摊位购买.选中技能=列表[a].技能编号
                摊位购买:显示刷新()
              end
        end
        local xx,yy=self:取坐标()
        __UI弹出.弹出列表:打开(列表,取白色背景(0,0,190,#列表*42+8),事件,xx-130,yy+24)

end











  local 购买按钮 = 摊位购买:创建红色按钮("购买", "购买按钮", 60,420,50,22)

  function 购买按钮:左键弹起(x, y)
         if 摊位购买.类型 == "物品类" then
              local 选中 = 摊位购买.道具网格:选中()
              if 选中 and 选中~=0 and 摊位购买.道具列表[选中] then
                  if 摊位购买.购买数量 ~= 摊位购买.数量输入:取数值() then
                      摊位购买.购买数量=摊位购买.数量输入:取数值()
                  end
                  if not 摊位购买.购买数量 or 摊位购买.购买数量<1 then
                      摊位购买.购买数量=1
                  end
                  请求服务(3726,{道具=摊位购买.道具列表[选中].背包编号,数量=摊位购买.购买数量})
              else
                  __UI弹出.提示框:打开("#Y请选择你要购买的物品")
              end
          elseif 摊位购买.类型 == "召唤兽" then
                if 摊位购买.bb选中 and 摊位购买.bb选中~=0 and 摊位购买.上架bb[摊位购买.bb选中]  then
                      请求服务(3726,{bb=摊位购买.上架bb[摊位购买.bb选中].上架编号})
                  else
                      __UI弹出.提示框:打开("#Y请选择你要购买的宝宝")
                  end

         end
  end

  local 取消按钮 = 摊位购买:创建红色按钮("取消", "取消按钮", 180,420,50,22)

  function 取消按钮:左键弹起(x, y)
        摊位购买:置可见(false)
  end

  -- local 查看按钮 = 摊位购买:创建红色按钮("查看", "查看按钮", 0,0,120, 40,说明字体)

  -- function 查看按钮:左键弹起(x, y)
  --       if 摊位购买.类型 == "召唤兽" then
  --          if 摊位购买.bb选中 and 摊位购买.bb选中~=0 and 摊位购买.上架bb[摊位购买.bb选中]  then
  --               窗口层.召唤兽查看:打开(摊位购买.上架bb[摊位购买.bb选中])
  --           else
  --                __UI弹出.提示框:打开("#Y请选择你要购买的宝宝")
  --           end
  --       end
  -- end


local 数量输入 = 摊位购买:创建输入("数量输入", 185,371, 90,18)     
function 数量输入:初始化()
    self:取光标精灵()
    self:置限制字数(3)
    self:置颜色(0, 0, 0, 255)
    self:置模式(self.数字模式)
end



function 数量输入:输入事件()
    摊位购买.购买数量=self:取数值()
    if  not 摊位购买.购买数量 then
        摊位购买.购买数量=1
    end
    if 摊位购买.类型=="物品类" then
      local 物品 = 摊位购买.道具网格:选中物品()
      if 物品 and 物品.物品 and 物品.物品.数量  then
         if 摊位购买.购买数量>物品.物品.数量 then
              摊位购买.购买数量=tonumber(物品.物品.数量) 
         end
      else
          摊位购买.购买数量=1
      end
    else
        摊位购买.购买数量=1
    end
   
    摊位购买:显示刷新()
end














local 类型设置 = {"物品类","召唤兽","制造类"}
for i, v in ipairs(类型设置) do
      local 临时函数 = 摊位购买:创建红色单选按钮(v, v,22+(i-1)*85,95,80, 22)
      function  临时函数:左键弹起(x, y)
          if 摊位购买.类型~=v then
              摊位购买.数量输入:置数值()
              摊位购买.bb选中=nil
              摊位购买.类型=v
              摊位购买:道具刷新()
          end
      end
end
local 加号 = 摊位购买:创建按钮("加号",185,35)
function 加号:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/dd",0x10000004),1)
end
local 关注=摊位购买:创建多选按钮("关注", 205, 32)
function 关注:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000009))
end

function 关注:左键弹起(x, y)
        if self.是否选中 then
            __关注摊位[摊位购买.摊主id]=nil
        else
             __关注摊位[摊位购买.摊主id]=true
        end
        if __主显.玩家[摊位购买.摊主id] then
            __主显.玩家[摊位购买.摊主id]:置摆摊(摊位购买.摊位名称)
        end
end




local 关闭 = 摊位购买:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  摊位购买:置可见(false)
end


