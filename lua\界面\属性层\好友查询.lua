--[[
LastEditTime: 2024-12-24 03:18:04
--]]
local 好友查询 = 窗口层:创建窗口("好友查询", 0, 0, 345, 270)
function 好友查询:初始化()


  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end
function 好友查询:打开()
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.ID输入:清空()
  self.昵称输入:清空()
  self.搜索玩家:置选中(true)
  self:更新数据()
  
end

function 好友查询:更新数据(内容)
  self.数据=nil
  if 内容 and 内容.名称 and 内容.数字ID then
    self.数据 = 内容
  end

  self:创建纹理精灵(function()
        置窗口背景("在线查询", 0, 0, 345, 270, true):显示(0, 0)
        取白色背景(0, 0, 325, 140, true):显示(10, 90)
        文本字体:置颜色(255,255,255,255)
        文本字体:取图像("昵称"):显示(10, 63)
        文本字体:取图像("数字ID"):显示(165, 63)
        取输入背景(0, 0, 110, 22):显示(45,60)
        取输入背景(0, 0, 85, 22):显示(215,60)
        if self.数据 and  self.数据.名称 and self.数据.数字ID then
          文本字体:置颜色(0,0,0,255)
          文本字体:取图像("名字："..self.数据.名称.."("..self.数据.数字ID..")"):显示(15, 95)
          文本字体:取图像("种族："..self.数据.种族):显示(15, 115)
          文本字体:取图像("性别："..self.数据.性别):显示(15, 135)
          文本字体:取图像("等级："..self.数据.等级):显示(15, 155)
          文本字体:取图像("曾用名"):显示(15, 175)
        end 

  end
  )

end
local 搜索玩家= 好友查询:创建红色单选按钮("搜索玩家", "搜索玩家",10,32,74,22)  

local 搜索队伍= 好友查询:创建红色按钮("搜索队伍", "搜索队伍",95,32,74,22)


local 查询按钮= 好友查询:创建红色按钮("查", "查询按钮",308,60,26,22)
function  查询按钮:左键弹起(x, y)
      if (好友查询.昵称输入:取文本()~="" and 好友查询.昵称输入:取文本()~=nil) or (好友查询.ID输入:取数值()and 好友查询.ID输入:取数值()~=0)  then
            请求服务(22,{名称=好友查询.昵称输入:取文本(),id=好友查询.ID输入:取数值()})
      end
end

local 类型设置 = {"加为好友","临时好友","设为私聊","屏蔽名单"}

for i, v in ipairs(类型设置) do
    local 临时函数 =好友查询:创建红色按钮(v,v,10+(i-1)*85,240,65,22)    
    function  临时函数:左键弹起(x, y)
        if  v =="加为好友" and 好友查询.数据 and 好友查询.数据.数字ID then
           请求服务(19,{id=好友查询.数据.数字ID})
        elseif v =="临时好友" and 好友查询.数据 and 好友查询.数据.数字ID then
           请求服务(18,{id=好友查询.数据.数字ID})
        elseif v =="设为私聊" and 好友查询.数据 and 好友查询.数据.数字ID then
        elseif v =="屏蔽名单" and 好友查询.数据 and 好友查询.数据.数字ID then 
           请求服务(23,{id=好友查询.数据.数字ID,数据=好友查询.数据})
       end
    end
end

local 昵称输入 = 好友查询:创建文本输入("昵称输入", 50, 63, 100, 18)
function 昵称输入:初始化()
  self:取光标精灵()
  self:置限制字数(20)
  self:置颜色(0,0,0,255)
end


local ID输入 = 好友查询:创建文本输入("ID输入", 220, 63, 75, 18)
function ID输入:初始化()
  self:取光标精灵()
  self:置限制字数(5)
  self:置颜色(0,0,0,255)
  self:置模式(2)
end


local 关闭 = 好友查询:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
    好友查询:置可见(false)
end

