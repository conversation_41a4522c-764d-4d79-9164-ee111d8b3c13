local SDLF = require("SDL.函数")
local Http = require("HPSocket.HttpClient")(true)
local ggf = require("GGE.函数")

function Http:重载()
    self.receivedata = ""
    self.更新 = 1
    self.updata = {}
    self.upsize = 0
    self.tablereceivedata = {}
 
end


function Http:报文事件(dwConnID, pData)

      if string.find(pData,"<title>404") and self.更新>=3 then
          self.savepath =  nil
          self.receivedata=""
          table.remove(self.updata, 1)
      else
          self.receivedata = self.receivedata .. pData
          if #self.receivedata >= 10024000 then
            table.insert(self.tablereceivedata, self.receivedata)
            self.receivedata = ""
          end
      end
end

function Http:结束事件(data)
    if self.更新 ==1 and not 调试模式 then
        if __res.配置.版本号 ~= self.receivedata then
            self:更新主体(self.receivedata)
        else
            -- 版本一致，隐藏更新界面
            __UI界面.登录层.更新界面:置可见(false)
            __UI界面.登录层.公告界面:置可见(true, true)
        end
    end
    self:保存事件()
end


function Http:报头事件(dwConnID, lpszName, lpszValue)
    if "Content-Length" == lpszName then
        self.upsize = lpszValue
    end
end



-- function Http:结束事件(data)
--     if self.更新 ==1 and not 调试信息 then
--         if __res.配置.版本号 ~= self.receivedata  then
        
--             self:更新主体(self.receivedata)
--         end
--     end
--     self:保存事件()
-- end



function Http:更新主体(bbh)
    self.receivedata=""
    self.tablereceivedata = {}
    __res.配置.版本号 = bbh
    local 下载名="ggelua"

    if __主体名称 then
        下载名=__主体名称
    end

    if __手机 then
    self.updata[1]= {
        path = "/ggelua.com",
        http = "/"..下载名,
        lx = "脚本"
    }
    else
        self.updata[1]= {
            path = "/MHXY_new.exe",
            http = "/MHXY.exe",
            lx = "程序"
        }
    end
    
  self.更新 = 2
end



function Http:开始事件(dwConnID)
   -- print(dwConnID)

end

function Http:状态事件(dwConnID, usStatusCode, lpszDesc)
   --print(dwConnID, usStatusCode, lpszDesc)

end

function Http:发送请求(lpszPath, path)--
   self.savepath = path
   self:GET(lpszPath)
end


function Http:保存事件()

    if self.savepath then
        if #self.tablereceivedata > 0 then
            local recata = ""
            for i, v in ipairs(self.tablereceivedata) do--你有打包好的没
                recata = recata .. v
            end
            __res:写出文件(self.savepath, recata .. self.receivedata)
        else
            __res:写出文件(self.savepath, self.receivedata)
        end
        self.receivedata = ""
        self.tablereceivedata = {}
        if self.savepath == "/ggelua.com" then
           __res:写出文件("config.txt", zdtostring(__res.配置))
           引擎:关闭()
           return
        elseif self.savepath == "/MHXY_new.exe" then
           __res:写出文件("config.txt", zdtostring(__res.配置))
           self:执行PC端自我更新()
           return
        elseif #self.updata > 0 then
            if self.updata[1].path == self.savepath then
                if self.updata[1].lx == "WDF" then
                    __res.wdfs:加载(SDLF.取内部存储路径() .. "/" .. self.savepath)
                    __res.wdfs:allwas(self.updata[1].pid, self.updata[1].jb)
                    if not  __res.配置.资源包[self.updata[1].name]  then
                        __res.配置.资源包[self.updata[1].name] = true
                    end
                    __res:写出文件("config.txt", zdtostring(__res.配置))
                end
            end
            table.remove(self.updata, 1)
        end
    end
    self.savepath = nil
    self.upsize = 0
    if #self.updata > 0 then
        self:发送请求(self.updata[1].http, self.updata[1].path)
    else
         self:断开()
         self.receivedata = "" 
         self.更新=99
         __res.实时更新=nil
         __res.更新资源={}
        if __UI界面.登录层.更新界面.是否可见 then
           __UI界面.登录层.更新界面.更新完成=true
           __UI界面.登录层.更新界面:置可见(false)
           __UI界面.登录层.公告界面:置可见(true, true)
        end
    end
end

function Http:连接事件(dwConnID)
    -- self._co = dwConnID
end

function Http:断开事件(ec, ed)
end


function Http:效验版本号()
    if  __热更地址 and __热更端口 then
            self.ip = __热更地址
            self.dk = __热更端口
    else
        self.ip = "127.0.0.1"
        self.dk = "80"
    end
    __UI界面.登录层.更新界面:置可见(true, true)
     local 访问="http://"..self.ip..":"..self.dk
    if self.ip and self.dk and self:连接(访问) then
        self:发送请求("/bbb.txt")
    else
        self.更新=99
        __UI界面.登录层.更新界面:置可见(false)
        __UI界面.登录层.公告界面:置可见(true, true)
    end
end

function Http:执行PC端自我更新()
    local SDLF = require("SDL.函数")
    local SDL = require("SDL")
    local 内部存储路径 = SDLF.取内部存储路径()
    
    local 更新脚本内容 = string.format([[
Start-Sleep -Seconds 3
$newExe = "%s\\MHXY_new.exe"
$currentExe = "MHXY.exe"
$backupExe = "MHXY.exe.bak"
if (-not (Test-Path $newExe)) { exit 1 }
if (Test-Path $backupExe) { Remove-Item $backupExe -Force }
if (Test-Path $currentExe) { Move-Item $currentExe $backupExe }
Move-Item $newExe $currentExe
if (-not (Test-Path $currentExe)) {
    if (Test-Path $backupExe) { Move-Item $backupExe $currentExe }
    exit 1
}
Start-Process $currentExe
Start-Sleep -Seconds 1
]], 内部存储路径:gsub("/", "\\"))
    
    __res:写出文件("/update.ps1", 更新脚本内容)
    
    local 启动脚本内容 = string.format([[
@echo off
cd /d "%s"
powershell -ExecutionPolicy Bypass -WindowStyle Hidden -File "update.ps1"
]], 内部存储路径:gsub("/", "\\"))
    
    __res:写出文件("/start_update.bat", 启动脚本内容)
    
    local 启动脚本路径 = 内部存储路径 .. "/start_update.bat"
    local 命令 = 'start /min "" "' .. 启动脚本路径:gsub("/", "\\") .. '"'
    
    os.execute(命令)
    SDL.Delay(500)
    引擎:关闭()
end

return Http
