--[[
LastEditTime: 2025-06-25 00:39:33
--]]


local _ENV = setmetatable({}, { __index = _G })
SDL = require 'SDL'
界面 = require('GUI')(引擎, 文本字体)
--地图层 = GUI:创建界面('地图层')
战斗层 = 界面:创建界面('战斗层')
加载层 = 界面:创建界面('加载层')
登录层 = 界面:创建界面('登录层')
界面层 = 界面:创建界面('界面层')
窗口层 = 界面:创建界面('窗口层')
鼠标层 = 界面:创建鼠标('鼠标层')



function 更新(_, dt, x, y)
    界面:更新(dt, x, y)
    
    -- 更新移动端摇杆控件
    if __手机 and 界面层.玩家界面 and 界面层.玩家界面.移动摇杆 then
        界面层.玩家界面.移动摇杆:更新(dt)
    end
end

-- 添加全局事件拦截处理摇杆事件
function 消息事件(_, 消息)
    -- 优先处理摇杆事件，防止穿透
    if __手机 and 界面层.玩家界面 and 界面层.玩家界面.移动摇杆 and 消息.鼠标 then
        for _, v in ipairs(消息.鼠标) do
            -- 检查是否在摇杆区域内，如果是则拦截所有事件
            if 界面层.玩家界面.移动摇杆:是否在摇杆区域内(v.x, v.y) then
                if v.type == SDL.MOUSE_DOWN and v.button == SDL.BUTTON_LEFT then
                    界面层.玩家界面.移动摇杆:鼠标按下(v.x, v.y)
                    v.type = nil -- 清除事件，阻止进一步处理
                    return true
                elseif v.type == SDL.MOUSE_UP and v.button == SDL.BUTTON_LEFT then
                    界面层.玩家界面.移动摇杆:鼠标释放(v.x, v.y)
                    v.type = nil -- 清除事件，阻止进一步处理
                    return true
                elseif v.type == SDL.MOUSE_MOTION then
                    界面层.玩家界面.移动摇杆:鼠标移动(v.x, v.y)
                    v.type = nil -- 清除事件，阻止进一步处理
                    return true
                else
                    -- 其他事件也要拦截，防止穿透
                    v.type = nil
                    return true
                end
            end
        end
    end
    
    -- 如果摇杆没有处理事件，继续正常流程
    界面:消息事件(消息)
    return false
end

function 显示(_, x, y)
    界面:显示(x, y)
    
    -- 最后显示移动端摇杆控件，确保在最前面
    if __手机 and 界面层.玩家界面 and 界面层.玩家界面.移动摇杆 then
        界面层.玩家界面.移动摇杆:显示(x or 0, y or 0)
    end
end

local function loadgui(file)
    local env = setmetatable({}, {
        __index = function(t, k)
            local v = _G[k]
            if v ~= nil then
                return v
            end
            return _ENV[k]
        end,
        __newindex = function(t, k, v)
            if k:sub(1, 1) == '_' then
                _ENV[k] = v
            elseif k:sub(1, 2) == '__' then
                _G[k] = v
            end
            rawset(t, k, v)
        end
    })
    local r = gge.require(file, env)
    package.loaded[file] = r or file
end



loadgui('对象/主角')
loadgui('对象/玩家')
loadgui('对象/NPC')
loadgui('对象/特效')
loadgui('对象/家具')
loadgui('对象/跳转')

loadgui("界面/控件层/物品格子")
loadgui("界面/控件层/家具格子")
loadgui("界面/控件层/技能格子")
loadgui("界面/控件层/头像格子")
loadgui("界面/控件层/队伍格子")
loadgui("界面/控件层/UI模型格子")
loadgui("界面/控件层/角色选择格子")

loadgui("数据/Greedy")
loadgui("战斗逻辑/战斗主控")
loadgui("战斗逻辑/战斗单位")
loadgui("战斗逻辑/战斗动画")

----------------------------------------------------------------
loadgui('界面/鼠标层')
loadgui('界面/加载层')
loadgui('界面/登录层')
loadgui("界面/登录层/公告界面")
loadgui("界面/登录层/登录界面")
loadgui("界面/登录层/选区界面")
loadgui("界面/登录层/角色界面")
loadgui("界面/登录层/创建角色")
loadgui("界面/登录层/注册界面")
loadgui("界面/登录层/更新界面")

-------------------------------------------------------------
loadgui("界面/弹出层/提示框")
loadgui("界面/弹出层/道具提示")
loadgui("界面/弹出层/技能详情")
loadgui("界面/弹出层/弹出列表")
loadgui("界面/弹出层/临时按钮")
loadgui("界面/弹出层/道具选择")
loadgui("界面/弹出层/气魔补充")
loadgui("界面/弹出层/战斗状态")
loadgui("界面/弹出层/状态提示")
loadgui("界面/弹出层/好友弹出")
loadgui("界面/弹出层/子女弹出")
loadgui("界面/弹出层/玩家信息")
loadgui("界面/弹出层/频道表情")
loadgui("界面/弹出层/组合输入框")
loadgui("界面/弹出层/自定义提示")
loadgui("界面/弹出层/快捷技能选择")
loadgui("界面/弹出层/超级技能详情")

-------------------------------------------------------------


loadgui('界面/界面层')
if __手机 then
  loadgui("界面/界面层/聊天栏_手机")
else
  loadgui("界面/界面层/聊天栏")
end
loadgui("界面/界面层/任务追踪")
loadgui("界面/界面层/玩家界面")
loadgui("界面/界面层/战斗界面")
loadgui("界面/界面层/队伍栏")
loadgui("界面/界面层/状态图标")
loadgui("界面/界面层/流动公告")


loadgui('界面/战斗层')
loadgui("界面/战斗层/战斗道具")
loadgui("界面/战斗层/战斗法术")
loadgui("界面/战斗层/战斗灵宝")
loadgui("界面/战斗层/战斗召唤")
loadgui("界面/战斗层/战斗自动")
loadgui("界面/战斗层/自动抓鬼")
loadgui("界面/战斗层/多开法术")
loadgui("界面/战斗层/多开自动")
loadgui("界面/战斗层/九黎法术")

-------------------------------------------------------------------
loadgui('界面/窗口层')
if __手机 then
    loadgui("界面/窗口层/对话栏_手机")
else
    loadgui("界面/窗口层/对话栏")
end
loadgui("界面/窗口层/给予")
loadgui("界面/窗口层/抽奖")
loadgui("界面/窗口层/染色")
loadgui("界面/窗口层/飞行符")
loadgui("界面/窗口层/小地图")
loadgui("界面/窗口层/大地图")
loadgui("界面/窗口层/大地图a")
loadgui("界面/窗口层/大地图b")
loadgui("界面/窗口层/大地图c")
loadgui("界面/窗口层/大地图d")
loadgui("界面/窗口层/排行榜")
loadgui("界面/窗口层/文本栏")
loadgui("界面/窗口层/藏宝阁")
loadgui("界面/窗口层/功德录")
loadgui("界面/窗口层/嘉年华")
loadgui("界面/窗口层/任务提示")
loadgui("界面/窗口层/消息管理")
loadgui("界面/窗口层/消息综合")
loadgui("界面/窗口层/系统设置")
loadgui("界面/窗口层/退出提示")
loadgui("界面/窗口层/靓号界面")
loadgui("界面/窗口层/帮派查看")
loadgui("界面/窗口层/帮派申请")
loadgui("界面/窗口层/梦幻指引")
loadgui("界面/窗口层/攻略查看")
loadgui("界面/窗口层/剑会匹配")
loadgui("界面/窗口层/剑会天下")
loadgui("界面/窗口层/世界boss")
loadgui("界面/窗口层/超级赐福")
loadgui("界面/窗口层/自选系统")
loadgui("界面/窗口层/更多属性")
loadgui("界面/窗口层/签到界面")
loadgui("界面/窗口层/邀请组队")
loadgui("界面/窗口层/成就提示")
loadgui("界面/窗口层/神器获得")
loadgui("界面/窗口层/神器修复")
loadgui("界面/窗口层/神器五行")
loadgui("界面/窗口层/神器查看")
loadgui("界面/窗口层/武器染色")
loadgui("界面/窗口层/宝宝染色")
loadgui("界面/窗口层/饰品染色")
loadgui("界面/窗口层/小地图NPC")
loadgui("界面/窗口层/召唤兽查看")
loadgui("界面/窗口层/藏宝阁出售")
loadgui("界面/窗口层/合成灵犀玉")
loadgui("界面/窗口层/世界小地图")
loadgui("界面/窗口层/自选灵饰系统")
loadgui("界面/窗口层/藏宝阁出售寄存")
loadgui("界面/窗口层/藏宝阁购买寄存")
loadgui("界面/窗口层/藏宝阁上架货币")

-------------------------------------------------
loadgui("界面/属性层/宠物")
loadgui("界面/属性层/法宝")
loadgui("界面/属性层/灵饰")
loadgui("界面/属性层/锦衣")
loadgui("界面/属性层/新行囊")
loadgui("界面/属性层/符石镶嵌")
loadgui("界面/属性层/道具鉴定")
loadgui("界面/属性层/道具附魔")
loadgui("界面/属性层/道具行囊")
loadgui("界面/属性层/法宝锻造")
loadgui("界面/属性层/技能学习")
loadgui("界面/属性层/奇经八脉")
loadgui("界面/属性层/人物称谓")
loadgui("界面/属性层/人物属性")
loadgui("界面/属性层/装备开运")
loadgui("界面/属性层/属性加点")
loadgui("界面/属性层/门派转换")
loadgui("界面/属性层/附魔宝珠")
loadgui("界面/属性层/坐骑属性")
loadgui("界面/属性层/坐骑技能")
loadgui("界面/属性层/召唤合宠")
loadgui("界面/属性层/召唤属性")
loadgui("界面/属性层/召唤资质")
loadgui("界面/属性层/打书内丹")
loadgui("界面/属性层/符石合成")
loadgui("界面/属性层/好友列表")
loadgui("界面/属性层/好友查询")
loadgui("界面/属性层/好友查看")



loadgui("界面/属性层/好友消息")
-------------------------------------------------------------

loadgui("界面/操作层/仓库")
loadgui("界面/操作层/幻化")
loadgui("界面/操作层/交易")
loadgui("界面/操作层/商店")
loadgui("界面/操作层/打造")
loadgui("界面/操作层/生死劫")
loadgui("界面/操作层/炼丹炉")
loadgui("界面/操作层/合成旗")
loadgui("界面/操作层/仙玉商城")
loadgui("界面/操作层/仙缘商店")
loadgui("界面/操作层/文韵商店")
loadgui("界面/操作层/阵型选择")
loadgui("界面/操作层/帮派加入")
loadgui("界面/操作层/宝箱抽奖")
loadgui("界面/操作层/充值窗口")
loadgui("界面/操作层/队伍列表")
loadgui("界面/操作层/回收系统")
loadgui("界面/操作层/门派选择")
loadgui("界面/操作层/超级传送")
loadgui("界面/操作层/摊位出售")
loadgui("界面/操作层/摊位购买")
loadgui("界面/操作层/帮派点修")
loadgui("界面/操作层/每日查看")
loadgui("界面/操作层/累计充值")
loadgui("界面/操作层/共享仓库")
loadgui("界面/操作层/请求列表")
loadgui("界面/操作层/物品加锁")
loadgui("界面/操作层/物品密码")
loadgui("界面/操作层/物品解锁")
loadgui("界面/操作层/解锁修改")
loadgui("界面/操作层/制作仙露")
loadgui("界面/操作层/超级兽决")
loadgui("界面/操作层/铃铛界面")
loadgui("界面/操作层/购买召唤兽")
loadgui("界面/操作层/长安保卫战")
loadgui("界面/操作层/多开系统/多开系统")
loadgui("界面/操作层/多开系统/多开创建")
loadgui("界面/操作层/多开系统/多开宠物")
loadgui("界面/操作层/多开系统/角色属性")
loadgui("界面/操作层/多开系统/多开资质")
loadgui("界面/操作层/多开系统/多开道具")
loadgui("界面/操作层/多开系统/多开回收")
loadgui("界面/操作层/多开系统/多开仓库")
loadgui("界面/操作层/多开系统/多开经脉")
---------------------------------------------------








return _ENV
