--[[
LastEditTime: 2024-12-23 22:31:24
--]]
local 好友列表 = 窗口层:创建窗口("好友列表", 0, 0, 230, 505)
function 好友列表:初始化()
  self:创建纹理精灵(function()
    置窗口背景("好友列表", 0, 0, 230, 505, true):显示(0, 0)
    取白色背景(0, 0, 210, 410, true):显示(10, 60)
    __res:取资源动画("pic/ltk","CC.png","图片"):显示(90, 478)
    __res:取资源动画("pic/ltk","HB.png","图片"):显示(115, 478)
    __res:取资源动画("pic/ltk","GN.png","图片"):显示(140, 478)
  end
)


  self.画线=require("SDL.精灵")(0, 0, 0, 1,15):置颜色(255, 255, 255, 255)
  self.选中=0
  self:置坐标(引擎.宽度 -235 , (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end
--引擎:置颜色(255, 255, 255, 255):画线(x+64,y+475,x+64,y+495)---画线

function 好友列表:显示(x,y)  
    self.画线:显示(x+75,y+479)
    self.画线:显示(x+175,y+479)
end





function 好友列表:打开(内容)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.状态="好友"
  self.选中=0
  self.分组数量 =1
  self:更新数据(内容)


end



function 好友列表:更新数据(内容)
    self.数据=table.copy(内容)
    self.分组数量 = #self.数据.分组
    self.在线列表={}
    self.不在列表={}
    for i = 2, self.分组数量+1 do
        self.在线列表[i]={}
        self.不在列表[i]={}
        for k, v in pairs(self.数据.分组[i-1].好友) do
              if self.数据.好友[k] then
                  if self.数据.在线[k] then
                      table.insert(self.在线列表[i],self.数据.好友[k])
                  else
                      table.insert(self.不在列表[i],self.数据.好友[k])
                  end
              end
        end
        table.sort(self.在线列表[i],function(a,b) return a.好友度>a.好友度 end)
        table.sort(self.不在列表[i],function(a,b) return a.好友度>a.好友度 end)
    end

    self:显示设置()
end



function 好友列表:显示设置()
    self[self.状态]:置选中(true)
    if  self.状态=="好友" then
      self.内容列表:置可见(true)
      self.内容列表:置数据()
    else
      self.选中=0
      self.分组数量 =1
      self.内容列表:置可见(false)
    end
    self.顶部列表:置数据()
end




local 内容列表 = 好友列表:创建树形列表("内容列表", 15, 140, 200, 325)

function 内容列表:初始化()
      self.行高度=35
end

function 内容列表:调整宽高()
      local h = 0
      for _, v in self:遍历项目() do
          if v.编号 then
             h = h+20
            if v.是否展开 then
                if v.编号==1 then
                    h=h+35*self:取数量(好友列表.数据.在线,好友列表.数据.好友)
                elseif v.编号<=好友列表.分组数量+1 then
                      h=h+35*self:取数量(好友列表.数据.分组[v.编号-1].好友)
                elseif v.编号==好友列表.分组数量+2 then 
                      h=h+35*self:取数量(好友列表.数据.临时)
                elseif v.编号==好友列表.分组数量+3 then 
                      h=h+35*self:取数量(好友列表.数据.黑名单)
                end
            end
          end 
      end
      if  h>325 then
          h=325
      end
      self:置宽高(200,h)
end




function 内容列表:更换图标(控件)
      if 控件.编号 then 
              if 控件.编号==1 then
                控件:创建纹理精灵(
                  function ()
                      if 控件.是否展开 then
                          __res:取资源动画("pic/ltk","X.png","图片"):显示(0, 6)
                      else
                          __res:取资源动画("pic/ltk","YB.png","图片"):显示(0, 4)
                      end
                      文本字体:置颜色(0,0,0,255):取图像("所有在线好友("..self:取数量(好友列表.数据.在线,好友列表.数据.好友)..")"):显示(9, 0)
                  end)

              elseif  控件.编号<=好友列表.分组数量+1 then
                      控件:创建纹理精灵(
                          function ()
                              if 控件.是否展开 then
                                  __res:取资源动画("pic/ltk","X.png","图片"):显示(0, 6)
                              else
                                  __res:取资源动画("pic/ltk","YB.png","图片"):显示(0, 4)
                              end
                              文本字体:置颜色(0,0,0,255):取图像(好友列表.数据.分组[控件.编号-1].名称.."("..#好友列表.在线列表[控件.编号].."/"..self:取数量(好友列表.数据.分组[控件.编号-1].好友)..")"):显示(9, 0)
                          end)
              elseif 控件.编号==好友列表.分组数量+2 then 
                    控件:创建纹理精灵(
                      function ()
                          if 控件.是否展开 then
                              __res:取资源动画("pic/ltk","X.png","图片"):显示(0, 6)
                          else
                              __res:取资源动画("pic/ltk","YB.png","图片"):显示(0, 4)
                          end
                          文本字体:置颜色(0,0,0,255):取图像("临时好友("..self:取数量(好友列表.数据.临时)..")"):显示(9, 0)
                      end)
              elseif 控件.编号==好友列表.分组数量+3 then 
                      控件:创建纹理精灵(
                        function ()
                            if 控件.是否展开 then
                                __res:取资源动画("pic/ltk","X.png","图片"):显示(0, 6)
                            else
                                __res:取资源动画("pic/ltk","YB.png","图片"):显示(0, 4)
                            end
                            文本字体:置颜色(0,0,0,255):取图像("屏蔽名单("..self:取数量(好友列表.数据.黑名单)..")"):显示(9, 0)
                        end)
              end

          end
end




function 内容列表:置数据()
      self:清空()
      self:置颜色(__取颜色("黑色"))
      if 好友列表.数据 and 好友列表.数据.好友 and 好友列表.数据.分组 then
            local 在线好友=self:添加(nil,0,0,200,20)
            在线好友.编号=1
            self:更换图标(在线好友)
            local 添加列表 = {}
            for k, v in pairs(好友列表.数据.在线) do
              if 好友列表.数据.好友[k] then
                  table.insert(添加列表,好友列表.数据.好友[k])
              end
            end
            table.sort(添加列表,function(a,b) return a.好友度>a.好友度 end)
            for i, v in ipairs(添加列表) do
                  在线好友:添加():创建纹理精灵(
                      function ()
                          __res:取资源动画("pic/ltk", 取人物模型代码(v.模型)..".png","图片"):显示(2,2)
                          if  v.好友度>10 then
                              文本字体:置颜色(__取颜色("绿色"))
                          else
                              文本字体:置颜色(0,0,0,255)
                          end
                          文本字体:取图像(v.名称):显示(40,2)
                          __res:取资源动画("pic/ltk","XFZ.png","图片"):显示(40,19)
                      end)
                  self._node[#self._node]._node[#self._node[#self._node]._node].数据=v
                    
            end
 
            
            for i = 2, 好友列表.分组数量+1 do
                if 好友列表.在线列表[i] and 好友列表.不在列表[i] then
                    local 临时分组 = self:添加(nil,0,0,200,20)
                    临时分组.编号=i
                    self:更换图标(临时分组)
                    for z, v in ipairs(好友列表.在线列表[i]) do
                            临时分组:添加():创建纹理精灵(
                                function ()
                                      __res:取资源动画("pic/ltk", 取人物模型代码(v.模型)..".png","图片"):显示(2,2)
                                      if  v.好友度>10 then
                                          文本字体:置颜色(__取颜色("绿色"))
                                      else
                                          文本字体:置颜色(0,0,0,255)
                                      end
                                      文本字体:取图像(v.名称):显示(40,2)
                                      __res:取资源动画("pic/ltk","XFZ.png","图片"):显示(40,19)
                                end)
                            self._node[#self._node]._node[#self._node[#self._node]._node].数据=v
                    end
                    for z, v in ipairs(好友列表.不在列表[i]) do
                            临时分组:添加():创建纹理精灵(
                                function ()
                                      __res:取资源动画("pic/ltk", 取人物模型代码(v.模型)..".png","图片"):到灰度():显示(2,2)
                                      文本字体:置颜色(128,128,128,255):取图像(v.名称):显示(40,2)
                                      __res:取资源动画("pic/ltk","XFZ.png","图片"):到灰度():显示(40,19)
                                end)
                            self._node[#self._node]._node[#self._node[#self._node]._node].数据=v
                    end


                end
    
            end

            local 临时好友=self:添加(nil,0,0,200,20)
            临时好友.编号=好友列表.分组数量+2
            self:更换图标(临时好友)
            local 在线临时={}
            local 不在临时={}
            for k, v in pairs(好友列表.数据.临时) do
                  if 好友列表.数据.在线[k] then
                      table.insert(在线临时,v)
                  else
                      table.insert(不在临时,v)
                  end
            end
            for z, v in ipairs(在线临时) do
                临时好友:添加():创建纹理精灵(
                      function ()
                            __res:取资源动画("pic/ltk", 取人物模型代码(v.模型)..".png","图片"):显示(2,2)
                            文本字体:置颜色(0,0,0,255):取图像(v.名称):显示(40,2)
                            __res:取资源动画("pic/ltk","XFZ.png","图片"):显示(40,19)
                      end)
                self._node[#self._node]._node[#self._node[#self._node]._node].数据=v
            end
            for z, v in ipairs(不在临时) do
                  临时好友:添加():创建纹理精灵(
                      function ()
                            __res:取资源动画("pic/ltk", 取人物模型代码(v.模型)..".png","图片"):到灰度():显示(2,2)
                            文本字体:置颜色(128,128,128,255):取图像(v.名称):显示(40,2)
                            __res:取资源动画("pic/ltk","XFZ.png","图片"):到灰度():显示(40,19)
                      end)
                  self._node[#self._node]._node[#self._node[#self._node]._node].数据=v
            end

            local 屏蔽名单=self:添加(nil,0,0,200,20)
            屏蔽名单.编号=好友列表.分组数量+3
            self:更换图标(屏蔽名单)
            local 在线屏蔽={}
            local 不在屏蔽={}
            for k, v in pairs(好友列表.数据.黑名单) do
                  if 好友列表.数据.在线[k] then
                      table.insert(在线屏蔽,v)
                  else
                      table.insert(不在屏蔽,v)
                  end
            end
            for z, v in ipairs(在线屏蔽) do
                  屏蔽名单:添加():创建纹理精灵(
                      function ()
                            __res:取资源动画("pic/ltk", 取人物模型代码(v.模型)..".png","图片"):显示(3,3)
                            文本字体:置颜色(0,0,0,255):取图像(v.名称):显示(40,2)
                            __res:取资源动画("pic/ltk","XFZ.png","图片"):显示(40,19)
                      end)
                  self._node[#self._node]._node[#self._node[#self._node]._node].数据=v
            end
            for z, v in ipairs(不在屏蔽) do
                  屏蔽名单:添加():创建纹理精灵(
                      function ()
                            __res:取资源动画("pic/ltk", 取人物模型代码(v.模型)..".png","图片"):到灰度():显示(3,3)
                            文本字体:置颜色(128,128,128,255):取图像(v.名称):显示(40,2)
                            __res:取资源动画("pic/ltk","XFZ.png","图片"):到灰度():显示(40,19)
                      end)
                  self._node[#self._node]._node[#self._node[#self._node]._node].数据=v
            end
      end
      self:调整宽高()

end

function 内容列表:右键弹起(x, y, i, v)
      if v.编号 then
          if v.编号<=好友列表.分组数量+1 and 好友列表.数据.分组[v.编号-1]  then
                __UI弹出.组合输入框:打开("修改分组名称",{"请输入"..好友列表.数据.分组[v.编号-1].名称.."分组名称，点击确认修改分组名称","白色",v.编号-1})
          end
      elseif v.数据 then
            窗口层.好友查看:打开(v.数据)
      end

end


function 内容列表:左键弹起(x, y, i, v)
      if v.编号 then
          self:更换图标(v)
          self:调整宽高()
          if 好友列表.选中~=v.编号 then
              好友列表.选中=v.编号
          end
      elseif v.数据 then
            if __手机 then
                local 事件 =function (编号)
                    if 编号==1 then
                        请求服务(25,{数据=v.数据})
                    else
                        窗口层.好友查看:打开(v.数据)
                    end
                end
                __UI弹出.临时按钮:打开({"对话","查看"},事件,x,y)
            else
                请求服务(25,{数据=v.数据})
            end
      end
end




function 内容列表:取数量(数据,类型)
    local 数量 = 0
    if 数据 and type(数据)=="table" then
        for k, v in pairs(数据) do
            if 类型  then
               if 类型[k] and v then
                  数量 = 数量 + 1
               end
            else
                if v then
                  数量 = 数量 + 1
                end
            end
        end
    end
    return 数量
end
local 顶部列表 = 好友列表:创建列表('顶部列表', 15, 65, 200, 70)
function 顶部列表:初始化()
      self.行高度 = 35
end
function 顶部列表:置数据()
      self:清空()
      for i = 1, 2 do
            self:添加():创建纹理精灵(
                  function ()
                    if  i==1 then
                         __res:取资源动画("pic/ltk", "MHTX.png","图片"):显示(3,3)
                         文本字体:置颜色(__取颜色("紫色")):取图像("梦幻精灵"):显示(40,2)
                    else
                        if 好友列表.状态=="好友" then
                            __res:取资源动画("pic/ltk", 取人物模型代码(角色信息.模型)..".png","图片"):显示(3,3)
                            文本字体:置颜色(0,0,0,255):取图像(角色信息.名称):显示(40,2)
                        else
                            __res:取资源动画("pic/ltk","XTTX.png","图片"):显示(3,3)
                            文本字体:置颜色(__取颜色("紫色")):取图像("系统消息"):显示(40,2)
                        end
                    end
                    if 好友列表.状态=="好友" then
                        if  i==1 then
                              __res:取资源动画("pic/ltk","MP.png","图片"):显示(60,19)
                        else
                              __res:取资源动画("pic/ltk","Y.png","图片"):显示(60,19)
                        end
                        __res:取资源动画("pic/ltk","XFZ.png","图片"):显示(40,19)
                    end
                  end
              )
      end
end





local 类型设置 = {"消息","好友"}
for i, v in ipairs(类型设置) do
    local 临时函数 =好友列表:创建红色单选按钮(v,v, 10+(i-1)*70,32,65,22)
    function  临时函数:左键弹起(x, y)
       if 好友列表.状态~=v then
          好友列表.状态=v
          好友列表:显示设置()
        end
    
    end
end

local 群 = 好友列表:创建红色按钮("群", "群",150,32,65,22) 

local 创建分组 = 好友列表:创建按钮("创建分组",20,478) 

function 创建分组:初始化()
  self:创建按钮精灵(__res:取资源动画("pic/ltk","JH.png","图片"))
end

function 创建分组:左键弹起(x, y)
    if 好友列表.数据 and 好友列表.数据.分组 and #好友列表.数据.分组<10 and 好友列表.状态=="好友" then 
          窗口层.文本栏:打开("你确定创建新的分组么",17.1)
    else
          __UI弹出.提示框:打开("#Y自定义分组已满")
    end
end
  

local 查找按钮 = 好友列表:创建按钮("查找按钮",45,478) 

function 查找按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("pic/ltk","FDJ.png","图片"))
end

function 查找按钮:左键弹起(x, y)
  -- if 窗口层.在线查询.是否可见 then
  --       窗口层.在线查询.关闭:左键弹起()
  --   else
        窗口层.好友查询:打开()
    -- end
  --tp.窗口.好友查找:打开()
end
  

local 设置按钮 = 好友列表:创建按钮("设置按钮",185,478) 

function 设置按钮:初始化()
  self:创建按钮精灵(__res:取资源动画("pic/ltk","SZ.png","图片"))
end

function 设置按钮:左键弹起(x, y)
     if 好友列表.选中 and 好友列表.选中<=好友列表.分组数量+1 and 好友列表.数据.分组[好友列表.选中-1]  then
          __UI弹出.组合输入框:打开("修改分组名称",{"请输入"..好友列表.数据.分组[好友列表.选中-1].名称.."分组名称，点击确认修改分组名称","白色",好友列表.选中-1})
      else
          __UI弹出.提示框:打开("#Y请选中正确的自定义分组")    
     end

end



local 关闭 = 好友列表:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  好友列表:置可见(false)
end

