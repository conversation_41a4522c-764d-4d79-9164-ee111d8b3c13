--[[
LastEditTime: 2024-09-29 13:16:11
--]]
--[[
LastEditTime: 2024-09-02 14:52:10
--]]
--[[
LastEditTime: 2024-04-10 13:22:14
--]]
--[[
LastEditTime: 2024-04-07 21:23:20
--]]

local 角色选择格子 = class("角色选择格子")
local ggf = require("GGE.函数")
function 角色选择格子:初始化()
  self.动画 = {}
  self.x, self.y = 0, 0
end
function 角色选择格子:置数据(数据, x, y)
  self.动画 = {}
  local 加入数据 = ggf.insert(self.动画)
  self.x, self.y = 0, 0
  self.确定 = nil
  self.人物=nil
  self.武器=nil
  self.副武器=nil
  if 数据 then
    local 资源 = 取模型(数据.造型)
    local m 
    if 数据.武器数据 and 数据.武器数据.名称 ~= nil and 数据.武器数据.子类 then
       m = _tp:取武器子类(数据.武器数据.子类)
       资源 = 取模型(数据.造型, m)
    end
   -- 
    self.人物 =__res:取资源动画(资源[3], 资源[1])
    local  是否显示武器 = true
    if 数据.锦衣数据 ~= nil and 数据.锦衣数据.名称 ~= nil then
      local 锦衣名称 = 数据.锦衣数据.名称
        if 锦衣名称=="青春" or 锦衣名称=="素颜" or 锦衣名称=="绝色" or 锦衣名称=="春秋" or  锦衣名称=="夏蚕"
        or 锦衣名称=="星河" or 锦衣名称=="白峨" or 锦衣名称=="糖果" or 锦衣名称=="青涩" or 锦衣名称=="傲然"
        or 锦衣名称=="牛仔" or  锦衣名称=="试剑" or 锦衣名称=="骨龙战骑" or 锦衣名称=="水嘟嘟·钻白"or 锦衣名称=="斗战神"
        or 锦衣名称=="斗战胜佛"  or  锦衣名称=="八部天龙马·玄" or  锦衣名称=="龙凰·桃" or  锦衣名称=="龙凰·皑" then
           资源 = 取战斗锦衣素材(数据.锦衣数据.名称,数据.造型)
           self.人物 =  __res:取资源动画(资源[5],资源[3])
           是否显示武器 = false
         elseif 新加战斗锦衣[锦衣名称]~=nil  then
                资源 = 取武器锦衣素材(数据.锦衣数据.名称,数据.造型,m)
                self.人物 =  __res:取资源动画(资源[5],资源[3])
                是否显示武器 = true
         end
    end
    if 数据.武器数据 and 数据.武器数据.名称 ~= nil and 数据.武器数据.名称 ~= ""   then
        local ms = _tp:取武器附加名称(数据.武器数据.子类, 数据.武器数据.级别限制,数据.武器数据.名称)
        资源 = 取模型(ms .. "_" .. 数据.造型)
        self.武器 = __res:取资源动画(资源[3],资源[1])
        if 数据.武器数据.染色方案~=nil and 数据.武器数据.染色方案~=0 and 数据.武器数据.染色组~=nil and #数据.武器数据.染色组>0 then
            local 调色板  = __dewpal(数据.武器数据.染色方案)
            self.武器:调色(调色板,取调色数据(数据.武器数据.染色组))
        end
    end
    if 数据.副武器 and 数据.副武器.名称 ~= nil and 数据.副武器.名称 ~= "" and (数据.武器数据==nil or string.find(数据.武器数据.名称,"(乾)"))  then
        资源 = 取模型(数据.副武器.名称 .. "_" .. 数据.造型)
        self.副武器 = __res:取资源动画(资源[3],资源[1])
        if 数据.副武器.染色方案~=nil and 数据.副武器.染色方案~=0 and 数据.副武器.染色组~=nil and 数据.副武器.染色组~=0 and #数据.副武器.染色组>0 then
            local 调色板  = __dewpal(数据.副武器.染色方案)
            self.副武器:调色(调色板,取调色数据(数据.副武器.染色组))
        end
    end
      加入数据(self.人物:取动画(5):置当前帧(1):播放(true))
      if 是否显示武器 then
          if self.武器 then
              加入数据(self.武器:取动画(5):置当前帧(1):播放(true))
          end
          if self.副武器 then
              加入数据(self.副武器:取动画(5):置当前帧(1):播放(true))
          end
      end
  end
  self.x, self.y = x, y
end
function 角色选择格子:清空()
  self.动画 = {}
end
function 角色选择格子:更新(dt)
  for k, v in pairs(self.动画) do
    v:更新(dt)
  end
end
function 角色选择格子:显示(x, y)
  if self.确定 then
       _tp.角色选择背景选中:显示(x, y)
  else
       _tp.角色选择背景:显示(x, y)
  end
  for k, v in pairs(self.动画) do
        v:显示(x + self.x, y + self.y)
  end
end
return 角色选择格子
