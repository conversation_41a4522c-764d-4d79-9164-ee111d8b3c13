--[[
Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
Date: 2024-06-09 14:15:18
LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
LastEditTime: 2024-06-09 14:21:28
FilePath: \紫禁之巅互通\lua\紫禁之巅界面\窗口层\兽魂系统.lua
Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
--]]



local 兽魂系统 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "兽魂系统", 138 + abbr.py.x, 17 + abbr.py.y, 695, 496)
function 兽魂系统:初始化()
  local nsf = require("SDL.图像")(695, 500)
  if nsf["渲染开始"](nsf) then
    置窗口背景("兽魂系统", 0, 12, 550, 500, true)["显示"](置窗口背景("兽魂系统", 0, 12, 550, 500, true), 0, 0)
    取白色背景(0, 0, 223+290, 400, true)["显示"](取白色背景(0, 0, 223+290, 400, true), 17, 70)
  
  --  __res:getPNGCC(1, 682, 386, 219, 126)["拉伸"](__res:getPNGCC(1, 682, 386, 219, 126), 219, 100)
    __res:getPNGCC(1, 682, 386, 219, 126, true):拉伸(600, 75):显示(25, 100)
    __res:getPNGCC(1, 682, 386, 219, 126, true):拉伸(600, 75):显示(25, 190)
    __res:getPNGCC(1, 682, 386, 219, 126, true):拉伸(300, 115):显示(25, 280)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.模型格子 = __UI模型格子["创建"]()
end

local data 
local shzzbh 
function 兽魂系统:刷新(数据)
  data=数据
  self:重置数据()
end
function 兽魂系统:打开(数据)
  self:置可见(true)
  shzzbh=0
  data=数据
  self:重置数据()
end
function 兽魂系统:更新(dt)
 if self.模型格子 then 
  self.模型格子["更新"](self.模型格子, dt)
end
end
function 兽魂系统:显示(x, y)
  if self.模型格子 then 
  self.模型格子["显示"](self.模型格子, x, y)
end
  self.数据["显示"](self.数据, x, y)
end


function 兽魂系统:取技能图标(名称)
  local 图标数据={}
  if 名称=="暴起" then --白 0x630ce9c2
      图标数据[1]=0x44670c70
  elseif 名称=="千手" then--蓝
      图标数据[1]=0x3b593e30
  elseif 名称=="傀儡" then
      图标数据[1]=0x77f08d10
  elseif 名称=="辉耀" then--红
      图标数据[1]=0xd4ce9460
  elseif 名称=="雷伤" then --白
      图标数据[1]=0x185c7768
  elseif 名称=="疗愈" then--蓝
      图标数据[1]=0x6aaef804
  elseif 名称=="陷足" then--青
      图标数据[1]=0x497b7159
  elseif 名称=="沸血" then--红
      图标数据[1]=0x1618c7ad
  elseif 名称=="灵涌" then--白
      图标数据[1]=0xa71dee70
  elseif 名称=="残忍" then--蓝
      图标数据[1]=0x87040366
  elseif 名称=="暴戾" then--青
      图标数据[1]=0xf6254282
  elseif 名称=="返照" then--红
      图标数据[1]=0x72491389
  elseif 名称=="相胜" then--白
      图标数据[1]=0x8ff3db32
  elseif 名称=="激励" then--蓝
      图标数据[1]=0xe588745d
  elseif 名称=="破体" then--青
      图标数据[1]=0xa2572ffc
  elseif 名称=="爆裂" then--红
      图标数据[1]=0xa8939527
  elseif 名称=="祈胜" then--白
      图标数据[1]=0xe364bf7c
  elseif 名称=="附体" then--蓝 --0xe9d07953
      图标数据[1]=0xc1c16e08
  elseif 名称=="御盾" then--青
      图标数据[1]=0xf0f2e8f1
  elseif 名称=="破伤" then--红
      图标数据[1]=0x630ce9c2
  end
  return 图标数据[1]
end
function 兽魂系统:重置数据(数据)
  
  local nsf = require("SDL.图像")(695, 500)
  if nsf["渲染开始"](nsf) then
    字体16["置颜色"](字体16, __取颜色("白色"))
    字体16["取图像"](字体16, "名称："..data.名称)["显示"](字体16["取图像"](字体16,"名称："..data.名称), 10+130, 110)
    字体16["取图像"](字体16, "兽魂等级："..data.等级)["显示"](字体16["取图像"](字体16,"兽魂等级："..data.等级), 210+130, 110)
    字体16["取图像"](字体16, "人物等级："..data.人物等级)["显示"](字体16["取图像"](字体16,"人物等级："..data.人物等级), 10+130, 150)
    if data.开启 then 
    字体16["取图像"](字体16, "兽魂系统：已开启")["显示"](字体16["取图像"](字体16,"兽魂系统：已开启"), 210+130, 150)
    else 
      字体16["取图像"](字体16, "兽魂系统：未开启")["显示"](字体16["取图像"](字体16,"兽魂系统：未开启"), 210+130, 150)
    end 
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(43,90+8+18)
    local lssj = 取头像(data.模型)
    __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])):显示(43+3,90+8+3+18)

    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(43,180+8+18)

    if data.参战宝宝 and data.参战宝宝.名称 then 
   兽魂系统.模型格子["置数据"](兽魂系统.模型格子, data.参战宝宝, "召唤兽", 400, 385) 
    local lssj = 取头像(data.参战宝宝.模型)
    __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[2])):显示(43+3,180+8+3+18)

    字体16["置颜色"](字体16, __取颜色("黄色"))
      字体16["取图像"](字体16, "名称："..data.参战宝宝.名称)["显示"](字体16["取图像"](字体16,"名称："..data.参战宝宝.名称), 10+130, 200)
      字体16["取图像"](字体16, "兽魂组合："..data.组合)["显示"](字体16["取图像"](字体16,"兽魂组合："..data.组合), 210+130, 200)
      字体16["取图像"](字体16, "等级："..data.参战宝宝.等级)["显示"](字体16["取图像"](字体16,"等级："..data.参战宝宝.等级), 10+130, 240)
    else
      字体16["置颜色"](字体16, __取颜色("黄色"))
      字体16["取图像"](字体16, "名称：未参战")["显示"](字体16["取图像"](字体16,"名称：未参战"), 10+130, 200)
      字体16["取图像"](字体16, "兽魂组合："..data.组合)["显示"](字体16["取图像"](字体16,"兽魂组合："..data.组合), 210+130, 200)
      字体16["取图像"](字体16, "等级：未参战")["显示"](字体16["取图像"](字体16,"等级：未参战"), 10+130, 240)
    end 

    字体16["置颜色"](字体16, __取颜色("红色"))
    字体16["取图像"](字体16, "兽魂提供人物加成")["显示"](字体16["取图像"](字体16,"兽魂提供人物加成"), 10+30, 290)
    字体16["置颜色"](字体16, __取颜色("黄色"))
    字体16["取图像"](字体16, "伤害： "..data.等级)["显示"](字体16["取图像"](字体16,"伤害： "..data.等级), 10+30, 310)
    字体16["取图像"](字体16, "灵力： "..data.等级)["显示"](字体16["取图像"](字体16,"灵力： "..data.等级), 10+30, 330)
    字体16["取图像"](字体16, "防御： "..data.等级)["显示"](字体16["取图像"](字体16,"防御： "..data.等级), 10+30, 350)
    字体16["取图像"](字体16, "速度： "..data.等级)["显示"](字体16["取图像"](字体16,"速度： "..data.等级), 10+30, 370)
  
    
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(43+20,385+8+18)
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(43+20+95,385+8+18)
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(43+20+95+95,385+8+18)
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(43+20+95+95+95,385+8+18)
    __res["取图像"](__res, "shape/shuijing/360B8373.tcp"):显示(43+20+95+95+95+95,385+8+18)
    if data.一号 ~="无" then 
    __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self:取技能图标(data.一号))):显示(43+26,391+8+18)
    end
    if data.二号 ~="无" then 
      __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self:取技能图标(data.二号))):显示(43+26+95,391+8+18)
    end
    if data.三号 ~="无" then 
      __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self:取技能图标(data.三号))):显示(43+26+95+95,391+8+18)
    end
    if data.四号 ~="无" then 
      __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self:取技能图标(data.四号))):显示(43+26+95+95+95,391+8+18)
    end
    if data.五号 ~="无" then 
      __res["取图像"](__res, __res["取地址"](__res, "shape/jn/", self:取技能图标(data.五号))):显示(43+26+95+95+95+95,391+8+18)
    end
    nsf["渲染结束"](nsf)
  end
  self.数据 = nsf["到精灵"](nsf)
end 

local 关闭 = 兽魂系统["创建我的按钮"](兽魂系统, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 505, 0)
function 关闭:左键弹起(x, y, msg)
  兽魂系统["置可见"](兽魂系统, false)
end




for i, v in ipairs({
    {
      name = "开启兽魂",
      x =20,
      y = 125+350,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(90, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(90, 20),
      font = "开启兽魂"
    },
    {
      name = "升级兽魂",
      x =20+140,
      y = 125+350,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(90, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(90, 20),
      font = "升级兽魂"
    },
    {
      name = "重置技能",
      x =20+280,
      y = 125+350,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(90, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(90, 20),
      font = "重置技能"
    },
    {
      name = "兽魂技能",
      x =20+420,
      y = 125+350,
      tcp =  __res:getPNGCC(3, 511, 11, 117, 43, true):拉伸(90, 20),
      tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true):拉伸(90, 20),
      font = "兽魂技能"
    },
  }) do
    local 临时函数 = 兽魂系统["创建我的按钮"](兽魂系统, v.tcp, v.name, v.x, v.y, v.font)
   function  临时函数:左键弹起(x, y)
      if v.name == "开启兽魂"  then 
        if shzzbh~=0 then
          发送数据(200,{序号1=2,助战编号=shzzbh})
         else
          发送数据(200,{序号1=2})
         end
        elseif  v.name == "升级兽魂"  then
          if shzzbh~=0 then
            发送数据(200,{序号1=3,助战编号=shzzbh})
           else
            发送数据(200,{序号1=3})
           end
        elseif  v.name == "重置技能"  then
          if shzzbh~=0 then
            发送数据(200,{序号1=4,助战编号=shzzbh})
           else
            发送数据(200,{序号1=4})
           end
        elseif  v.name == "兽魂技能"  then
          
          兽魂系统["置可见"](兽魂系统, false)
          __UI界面["窗口层"]["兽魂展示"]["打开"](__UI界面["窗口层"]["兽魂展示"])
        -- elseif  v.name == "一助战"  then
        --   发送数据(200,{序号1=1,助战编号=1})
        --   shzzbh=1
        -- elseif  v.name == "二助战"  then
        --   发送数据(200,{序号1=1,助战编号=2})
        --   shzzbh=2
        -- elseif  v.name == "三助战"  then
        --   发送数据(200,{序号1=1,助战编号=3})
        --   shzzbh=3
        -- elseif  v.name == "四助战"  then
        --   发送数据(200,{序号1=1,助战编号=4})
        --   shzzbh=4
      end 
    end
  end