local 商会物品 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "商会物品", 0 + abbr.py.x, 5 + abbr.py.y, 424, 519)
function 商会物品:初始化()
  local nsf = require("SDL.图像")(424, 519)
  if nsf["渲染开始"](nsf) then
    置窗口背景("商店详情", 0, 12, 417, 505, true)["显示"](置窗口背景("商店详情", 0, 12, 417, 505, true), 0, 0)
    取灰色背景(0, 0, 375, 285, true)["显示"](取灰色背景(0, 0, 375, 285, true), 22, 90)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "商业指数")["显示"](字体18["取图像"](字体18, "商业指数"), 213, 57)
    local lssj = 取输入背景(0, 0, 135, 23)
    字体18["取图像"](字体18, "单价")["显示"](字体18["取图像"](字体18, "单价"), 18, 390)
    字体18["取图像"](字体18, "数量")["显示"](字体18["取图像"](字体18, "数量"), 214, 390)
    字体18["取图像"](字体18, "总价")["显示"](字体18["取图像"](字体18, "总价"), 18, 424)
    字体18["取图像"](字体18, "现金")["显示"](字体18["取图像"](字体18, "现金"), 214, 424)
    lssj["显示"](lssj, 70, 388)
    lssj["显示"](lssj, 264, 388)
    lssj["显示"](lssj, 70, 422)
    lssj["显示"](lssj, 264, 422)
    取输入背景(0, 0, 104, 23)["显示"](取输入背景(0, 0, 104, 23), 64, 461)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 商会物品:打开(data)
  self:置可见(true)
  self.数据 = data
  self.页数 = 1
  self.重置(self)
  self.数量输入["置数值"](self.数量输入, 1)
  self.刷新(self)
  local nsf = require("SDL.图像")(424, 92)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18["取图像"](字体18, self.数据["名称"])["显示"](字体18["取图像"](字体18, self.数据["名称"]), 20, 57)
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
end
function 商会物品:重置()
  self.道具网格["置物品"](self.道具网格, self.数据["道具"])
  self.选中 = nil
end
function 商会物品:刷新()
  local nsf = require("SDL.图像")(424, 130)
  if nsf["渲染开始"](nsf) then
    if 商会物品["选中"] then
      字体18["置颜色"](字体18, __取银子颜色(商会物品["数据"]["道具"][商会物品["选中"]]["价格"]))
      字体18["取图像"](字体18, 商会物品["数据"]["道具"][商会物品["选中"]]["价格"])["显示"](字体18["取图像"](字体18, 商会物品["数据"]["道具"][商会物品["选中"]]["价格"]), 80, 20)
      if 商会物品["数量输入"]["取数值"](商会物品["数量输入"]) then
        字体18["置颜色"](字体18, __取银子颜色(商会物品["数据"]["道具"][商会物品["选中"]]["价格"] * 商会物品["数量输入"]["取数值"](商会物品["数量输入"])))
        字体18["取图像"](字体18, 商会物品["数据"]["道具"][商会物品["选中"]]["价格"] * 商会物品["数量输入"]["取数值"](商会物品["数量输入"]))["显示"](字体18["取图像"](字体18, 商会物品["数据"]["道具"][商会物品["选中"]]["价格"] * 商会物品["数量输入"]["取数值"](商会物品["数量输入"])), 80, 50)
      end
    end
    字体18["置颜色"](字体18, __取银子颜色(角色信息["银子"]))
    字体18["取图像"](字体18, 角色信息["银子"])["显示"](字体18["取图像"](字体18, 角色信息["银子"]), 272, 50)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, self.页数 .. " / 10")["显示"](字体18["取图像"](字体18, self.页数 .. " / 10"), 98, 90)
    nsf["渲染结束"](nsf)
  end
  商会物品["图像2"] = nsf["到精灵"](nsf)
  商会物品["图像2"]["置中心"](商会物品["图像2"], 0, -378)
end
local 关闭 = 商会物品["创建我的按钮"](商会物品, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 374, 0)
function 关闭:左键弹起(x, y, msg)
  商会物品["置可见"](商会物品, false)
end
local 道具网格 = 商会物品["创建网格"](商会物品, "道具网格", 45, 115, 328, 256)
function 道具网格:初始化()
  self:创建格子(55, 55, 13, 13, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    if 商会物品["选中"] then
      self.子控件[商会物品["选中"]]._spr["确定"] = nil
    end
    商会物品["选中"] = a
    self.子控件[商会物品["选中"]]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 430, 86, w, h, "选择", a)
    商会物品["刷新"](商会物品)
  end
end
function 道具网格:置物品(data, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], "白格子", "战斗道具")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data, "白格子", "战斗道具")
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end
local 数量输入 = 商会物品["创建我的输入"](商会物品, "数量输入", 310, 392, 44, 18, 2, 2, "黑色")
for i, v in ipairs({
  {
    name = "上一页",
    x = 16,
    y = 457,
    tcp = __res:getPNGCC(4, 970, 6, 44, 44, true)
  },
  {
    name = "下一页",
    x = 180,
    y = 457,
    tcp = __res:getPNGCC(4, 1018, 5, 44, 44, true)
  },
  {
    name = "购买",
    x = 262,
    y = 458,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 140, 41),
    font = "购 买"
  }
}) do
  local 临时函数 = 商会物品["创建我的按钮"](商会物品, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "上一页" and 商会物品["页数"] > 1 then
      发送数据(6109, {
        ["编号"] = 商会物品["数据"]["编号"],
        ["店主id"] = 商会物品["数据"]["店主id"],
        ["页数"] = 商会物品["页数"] - 1
      })
    elseif v.name == "下一页" and 商会物品["页数"] < 10 then
      发送数据(6110, {
        ["编号"] = 商会物品["数据"]["编号"],
        ["店主id"] = 商会物品["数据"]["店主id"],
        ["页数"] = 商会物品["页数"] + 1
      })
    elseif v.name == "购买" then
      if 商会物品["选中"] then
        if 商会物品["数量输入"]["取数值"](商会物品["数量输入"]) then
          发送数据(6111, {
            ["数量"] = 商会物品["数量输入"]["取数值"](商会物品["数量输入"]),
            ["序列"] = 商会物品["选中"],
            ["编号"] = 商会物品["数据"]["编号"],
            ["店主id"] = 商会物品["数据"]["店主id"],
            ["页数"] = 商会物品["页数"]
          })
        end
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#请选中一个物品")
      end
    end
  end
end
