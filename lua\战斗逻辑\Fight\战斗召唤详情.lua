local 战斗召唤详情 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "战斗召唤详情", 407 + abbr.py.x, 57 + abbr.py.y, 258, 454)
function 战斗召唤详情:初始化()
end
function 战斗召唤详情:打开(data)
  if not self.是否可见 then
    self:置可见(true)
  end
 -- table.print(data)
  self:重置(data)
  self.数据=data
  self.装备格子["重置"](self.装备格子, data["装备"])
  self.技能格子["重置"](self.技能格子, data["技能"])
end
local lsb = {
  "气血",
  "魔法",
  "寿命",
  "灵力",
  "伤害",
  "防御",
  "速度",
  "忠诚"
}
function 战斗召唤详情:重置(pet)
  local nsf = require("SDL.图像")(258, 454)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(2, 355, 67, 258, 454)["显示"](__res:getPNGCC(2, 355, 67, 258, 454), 0, 0)
    字体18["置颜色"](字体18, __取颜色("黄色"))
    字体18["取图像"](字体18, pet["名称"])["显示"](字体18["取图像"](字体18, pet["名称"]), 16, 39)
    字体18["置颜色"](字体18, __取颜色())
    字体18["取图像"](字体18, "Lv." .. pet["等级"])["显示"](字体18["取图像"](字体18, "Lv." .. pet["等级"]), 185, 39)
    local pyx = 0
    local pyy = 0
    for i = 1, #lsb do
      if i > 4 then
        pyx = 130
        pyy = -120
      end
      if lsb[i]=="寿命" and pet.种类=="神兽" then
        字体18:取图像("寿命 ★永生★"):显示(16 + pyx, 70 + pyy + (i - 1) * 30)
      else
        字体18["取图像"](字体18, lsb[i] .. " " .. pet[lsb[i]])["显示"](字体18["取图像"](字体18, lsb[i] .. " " .. pet[lsb[i]]), 16 + pyx, 70 + pyy + (i - 1) * 30)
      end
    end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
local 装备格子 = 战斗召唤详情["创建网格"](战斗召唤详情, "装备格子", 45-3, 235-2, 174, 52)
function 装备格子:初始化()
  self:创建格子(50, 50, 0, 11, 1, 3)
end
function 装备格子:重置(data)
  for i = 1, #self.子控件 do
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data[i], "白格子", "战斗召唤")
    self.子控件[i]["置精灵"](self.子控件[i], lssj)
  end
end
local 技能格子 = 战斗召唤详情["创建网格"](战斗召唤详情, "技能格子", 40, 324, 183, 100)
function 技能格子:初始化()
  self:创建格子(55, 55, 9, 9, 4, 3, true)
end
function 技能格子:重置(data)
  for i = 1, #self.子控件 do
    local lssj = __召唤兽技能格子["创建"]()
    local 是否赐福= nil 
    if 战斗召唤详情.数据 and 战斗召唤详情.数据.赐福技能_生效 then
      是否赐福=判断是否赐福技能(战斗召唤详情.数据.赐福技能_生效  ,  data[i])
    end
    lssj:置数据(data[i], 55, 55, nil, 认证,是否赐福)
   -- lssj["置数据"](lssj, data[i], 55, 55)
    self.子控件[i]["置精灵"](self.子控件[i], lssj)
  end
end
