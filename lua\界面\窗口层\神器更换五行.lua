local 神器换五行 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "神器换五行", 50 +100+ abbr.py.x, 0 + abbr.py.y, 645, 555)
function 神器换五行:初始化()
  local nsf = require("SDL.图像")(640, 455)

  self.dongtai1 = __res:取动画(__res:取地址("shape/sq/", 0xC057E026)):取动画(1):播放(true)
  self.dongtai = __res:取动画(__res:取地址("shape/sq/", 0xBCA98FEB)):取动画(1):播放(true)
  if nsf["渲染开始"](nsf) then
   require("SDL.图像")("assets/shape/sq/czwx.png"):显示(0, 0)



   字体18["置颜色"](字体18, __取颜色("白色"))
  -- 字体18["取图像"](字体18, "未解锁")["显示"](字体8["取图像"](字体18, "未解锁"), 290, 253)
    nsf["渲染结束"](nsf)
  end
  self.新五行 =false
end
local 神器属性 = {
  大唐官府 = {"伤　　害","物理暴击"},化生寺 = {"防　　御","治疗能力"},方寸山 = {"封印命中","法术伤害"},女儿村 = {"封印命中","固定伤害"},天宫 = {"法术伤害","封印命中"},
  普陀山 = {"固定伤害","治疗能力"},龙宫 = {"法术伤害","法术暴击"},五庄观 = {"伤　　害","封印命中"},魔王寨 = {"法术伤害","法术暴击"},狮驼岭 = {"伤　　害","物理暴击"},
  盘丝洞 = {"封印命中","法术防御"},阴曹地府 = {"伤　　害","法术防御"},神木林 = {"法术伤害","法术暴击"},凌波城 = {"伤　　害","物理暴击"},无底洞 = {"封印命中","治疗能力"},
  花果山 = {"伤　　害","物理暴击"}
}
local 五行坐标 = {{321,208},{386,272},{321,337},{257,272}}

function 神器换五行:显示(x,y)

  神器换五行.dongtai1:显示(x-70,y-70)
  神器换五行.dongtai1:更新(0.03)

end
function 神器换五行:保存五行属性(数据)
 self.神器属性 = 数据
  self.新五行=false
	self.五行显示:更新显示()
end
local 五行显示= 神器换五行["创建控件"](神器换五行, "五行显示", 395, 230, 90, 50)
function 五行显示:更新显示()

 local nsf = require("SDL.图像")(655, 455)
 字体18["置颜色"](字体18, __取颜色("白色"))
  字体16["置颜色"](字体16, __取颜色("白色"))
  if nsf["渲染开始"](nsf) then
    require("SDL.图像")("assets/shape/sq/czwx.png"):显示(0, 0)

    字体16["取图像"](字体16, "1")["显示"](字体16["取图像"](字体16, "体力消耗   10"),50, 270)
    字体16["取图像"](字体16, "1")["显示"](字体16["取图像"](字体16, "金钱消耗   100000"),50, 300)
    字体16["取图像"](字体18, "1")["显示"](字体18["取图像"](字体16, "金：速　　度"),60, 85)
    字体18["取图像"](字体18, "1")["显示"](字体18["取图像"](字体16, "木：气　　血"),60, 110)
    字体18["取图像"](字体18, "1")["显示"](字体18["取图像"](字体16, "水："..神器属性[角色信息.门派][1]),60,135)
    字体18["取图像"](字体18, "1")["显示"](字体18["取图像"](字体16, "火："..神器属性[角色信息.门派][2]),60, 160)
    字体18["取图像"](字体18, "1")["显示"](字体18["取图像"](字体16, "土：抵抗封印"),60, 185)
    local sq=神器换五行.神器属性
       if sq.神器解锁[神器换五行.主显示部件]~=nil then
        神器换五行.更换五行:置可见(true)
            if 神器换五行.新五行==false then
      
          __res:取图像(__res:取地址("shape/sq/", 0xE4C15C10)):拉伸(145, 145):显示(350,180)
          字体18["置颜色"](字体18, __取颜色("黑色"))
          字体18["取图像"](字体18, "1")["显示"](字体18["取图像"](字体18, "当前"),405, 242)
          for j=1,4 do
         字体16["置颜色"](字体16, __取颜色("白色"))
         字体16["取图像"](字体16, "未解锁")["显示"](字体16["取图像"](字体16, ""..sq.神器解锁[神器换五行.主显示部件].神器五行[j]),五行坐标[j][1]+101-5-5, 五行坐标[j][2]-25)
     
           end
           神器换五行.更换五行:置可见(true)

        else
          __res:取图像(__res:取地址("shape/sq/", 0xE4C15C10)):拉伸(145, 145):显示(350-120,180)
          __res:取图像(__res:取地址("shape/sq/", 0xACEB1F9B)):拉伸(145, 145):显示(350+120,180)
          字体18["置颜色"](字体18, __取颜色("黑色"))
          字体18["取图像"](字体18, "1")["显示"](字体18["取图像"](字体18, "当前"),405-120, 242)

          字体18["取图像"](字体18, "1")["显示"](字体18["取图像"](字体18, "更换"),405+120, 242)
             字体16["置颜色"](字体16, __取颜色("白色"))
             for j=1,4 do
             字体16["取图像"](字体16, "未解锁")["显示"](字体16["取图像"](字体16, ""..sq.神器解锁[神器换五行.主显示部件].神器五行[j]),五行坐标[j][1]-14-10, 五行坐标[j][2]-25)
        
             字体16["取图像"](字体16, "未解锁")["显示"](字体16["取图像"](字体16, ""..神器换五行.新五行属性[j]),五行坐标[j][1]+101+110, 五行坐标[j][2]-20)
             end
             神器换五行.更换五行:置可见(false)
             神器换五行.放弃:置可见(true)
             神器换五行.保存:置可见(true)
            
        end


       else
        __res:取图像(__res:取地址("shape/sq/", 0xE4C15C10)):拉伸(145, 145):显示(350,180)
        字体18["取图像"](字体18, "1")["显示"](字体25["取图像"](字体18, "当前"),405, 242)
        神器换五行.更换五行:置可见(false)
      
     end
  nsf["渲染结束"](nsf)
end
神器换五行:置精灵(nsf["到精灵"](nsf))


end



local 五行动画= 神器换五行["创建控件"](神器换五行, "五行动画", 395, 230, 90, 50)
function 五行动画:初始化()
  神器换五行.dongtai = __res:取动画(__res:取地址("shape/sq/", 0xBCA98FEB)):取动画(1):播放(true)
end
function 五行动画:显示(x,y)

  神器换五行.dongtai:显示(x-20,y+0)
  神器换五行.dongtai:更新(0.03)

end
function 神器换五行:打开(数据)
  self:置可见(true)
  self.神器属性 = 数据
  self.新五行属性 = 数据
  self.主显示部件 =1
  self.新五行=false
  self.更换五行:置可见(false)
  self.放弃:置可见(false)
  self.保存:置可见(false)
  五行动画:置可见(false)
  五行显示:置可见(true)
  五行显示:更新显示()

end
local 更换一= 神器换五行["创建我的was按钮"](神器换五行, "shape/sq/467F2FCB.tcp", "修复一", 277+100, 55)
function  更换一:左键弹起(x, y, msg)
  五行动画:置可见(false)

  神器换五行.主显示部件 =1
  五行显示:更新显示()
end

local  更换二= 神器换五行["创建我的was按钮"](神器换五行, "shape/sq/467F2FCB.tcp", "更换二", 205+100, 48)

function  更换二:左键弹起(x, y, msg)
  五行动画:置可见(false)
  神器换五行.主显示部件 = 2
  五行显示:更新显示()
end


local  更换三= 神器换五行["创建我的was按钮"](神器换五行, "shape/sq/467F2FCB.tcp", "更换三", 348+100, 48)

function  更换三:左键弹起(x, y, msg)
  五行动画:置可见(false)
  神器换五行.主显示部件 = 3
  五行显示:更新显示()
end
local 关闭 = 神器换五行["创建我的按钮"](神器换五行, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 585,  0)
function 关闭:左键弹起(x, y, msg)
  五行动画:置可见(false)
  五行显示:置可见(false)
  神器换五行["置可见"](神器换五行, false)

end
local 更换五行 = 神器换五行["创建我的按钮"](神器换五行, __res:getPNGCC(3, 536, 560, 102, 35, true), "更换五行", 280-20+85+40,388+24+35-130+40, "更换五行")

function 更换五行:左键弹起(x, y, a, b, msg)
  
   发送数据(6225)
end

local 放弃 = 神器换五行["创建我的按钮"](神器换五行, __res:getPNGCC(3, 536, 560, 102, 35, true), "放弃", 280-80+85+40,388+24+35-130+40, "放弃本次")

function 放弃:左键弹起(x, y, a, b, msg)
  神器换五行.更换五行:置可见(false)
  神器换五行.放弃:置可见(false)
  神器换五行.保存:置可见(false)
  五行动画:置可见(false)
  神器换五行.新五行=false
  五行显示:更新显示()
end
local 保存 = 神器换五行["创建我的按钮"](神器换五行, __res:getPNGCC(3, 536, 560, 102, 35, true), "保存", 280-20+60+85+40,388+24+35-130+40, "保存结果")

function 保存:左键弹起(x, y, a, b, msg)
  发送数据(6226,{神器部件= 神器换五行.主显示部件,五行= 神器换五行.新五行属性})
  五行动画:置可见(false)
  神器换五行.更换五行:置可见(false)
  神器换五行.放弃:置可见(false)
  神器换五行.保存:置可见(false)
  神器换五行.新五行=false

end
function 神器换五行:更新五行属性(数据)
	神器换五行.新五行属性 = 数据
  self.新五行=true
	self.新五行图 = {}
  五行显示:更新显示()
	五行动画:置可见(true)
end