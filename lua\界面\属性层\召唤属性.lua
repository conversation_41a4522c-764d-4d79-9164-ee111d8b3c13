--[[
LastEditTime: 2024-04-21 16:39:12
--]]
local 召唤属性 = 窗口层:创建窗口("召唤属性", 0,0, 370, 470)
local bd = {"体质","魔力","力量","耐力","敏捷"}
local bd1 = {"伤害","防御","速度","灵力"}
local 取属性=function(成长,速度资质,三维)
      local 输出属性={气血=0,魔法=0,伤害=0,防御=0,速度=0,灵力=0}
      输出属性.气血 =  math.floor(三维.体质 * 成长 * 7)
      输出属性.魔法 = math.floor(三维.魔力 * 成长 * 6)
      输出属性.伤害 =math.floor( 三维.力量 * 成长*2.4) 
      输出属性.防御 =math.floor(三维.耐力 * 成长 * 1.6) 
      输出属性.灵力 = math.floor(三维.魔力 * 成长 * 1.8 + 三维.力量*0.1+ 三维.体质 *0.2+三维.耐力*0.1) 
      输出属性.速度 = math.floor(三维.敏捷 * 速度资质/1000) 

  return 输出属性
end


function 召唤属性:初始化()
  self:创建纹理精灵(function()
      置窗口背景("召唤兽属性", 0, 0, 370, 470, true):显示(0, 0)
        取白色背景(0, 0, 180, 190,true):显示(10,35)
        取白色背景(0, 0, 160, 160,true):显示(200,35)
        for i = 1, 6 do
            取输入背景(0, 0, 100, 23):显示(50, 255+(i-1)*25)
            取输入背景(0, 0, 110, 23):显示(200, 230+(i-1)*25)
        end
        文本字体:置颜色(255,255,255,255)
        文本字体:取图像("召唤兽携带数量:"):显示(15, 233)
        文本字体:取图像("气血"):显示(15, 259)
        文本字体:取图像("魔法"):显示(15, 284)
        文本字体:取图像("参战等级:"):显示(200, 205)
        for i, v in ipairs(bd1) do
            文本字体:取图像(v):显示(15, 284+i*25)
        end
        for i, v in ipairs(bd) do
            文本字体:取图像(v):显示(165, 259+(i-1)*25)
        end
        文本字体:取图像("经验"):显示(15, 440)
        文本字体:置颜色(__取颜色("橙色"))
        文本字体:取图像("忠诚"):显示(15, 410)
        文本字体:取图像("潜能"):显示(165, 385)   
        取输入背景(0, 0, 45, 23):显示(50,408)
        取输入背景(0, 0, 45, 23):显示(195,382)
        __res:取资源动画("pic","bossjyt.png","图片"):拉伸(220, 18):显示(50,440)
    end
  )
    
    self.选中 = nil
    self.模型格子 = __UI模型格子.创建()
    self.可初始化=true
    self.临时加点={体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
    self.预览属性={气血=0,魔法=0,伤害=0,防御=0,速度=0,灵力=0}
    self:置坐标(0, 30)
    if __手机 then
          self.关闭:置大小(25,25)
          self.关闭:置坐标(self.宽度-27, 2)
    else
          self.关闭:置大小(16,16)
          self.关闭:置坐标(self.宽度-18, 2)
    end
end







function 召唤属性:更新(dt)
    if self.模型格子 then
        self.模型格子:更新(dt)
    end
    
end
function 召唤属性:显示(x, y)
      if self.模型格子 then
          self.模型格子:显示(x, y)
      end
      if self.属性背景 then
          self.属性背景:显示(x, y)
      end
      if self.加点属性 then
        self.加点属性:显示(x, y)
      end
end



function 召唤属性:打开()
  self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self.选中 = nil
  self.名称选择:置数据()
  self:显示设置()
end

function 召唤属性:显示设置()
      
        self.选中潜力=0
        self.属性背景=nil
        self.名称输入:清空()
        self.模型格子:清空()
        self.参战:重置文字("参战",true)
        self.改名:置禁止(true)
        self.重置:置禁止(true)
        self.放生:置禁止(true)
        self.仓库:置禁止(true)
        self.鉴定:置禁止(true)
        self.确认加点:置禁止(true)
        self.推荐加点:置禁止(true)
        self.查看资质:置禁止(true)
        self.饰品染色:置可见(false)
        self.临时加点={体质=0,魔力=0,力量=0,耐力=0,敏捷=0}
        self.预览属性={气血=0,魔法=0,伤害=0,防御=0,速度=0,灵力=0}
        if 窗口层.饰品染色.是否可见 then
            窗口层.饰品染色:置可见(false)
        end
        if self.选中 and 角色信息 and 角色信息.宝宝列表[self.选中] then
              self.模型格子:置数据(角色信息.宝宝列表[self.选中], "召唤兽", 280,175)
              self.名称输入:置文本(角色信息.宝宝列表[self.选中].名称)
              self.选中潜力=角色信息.宝宝列表[self.选中].潜力
              self.经验条.显示经验= 文本字体:置颜色(255, 255, 255, 255):取描边精灵(string.format("%s/%s", 角色信息.宝宝列表[self.选中].当前经验, 角色信息.宝宝列表[self.选中].最大经验),0,0,0,255)
              self.经验条:置位置(math.floor(角色信息.宝宝列表[self.选中].当前经验 / 角色信息.宝宝列表[self.选中].最大经验 * 100))
              if self.名称选择.子控件[self.选中]~=nil then
                  self.名称选择:置选中(self.选中)
              end
              self.参战:重置文字("参战")
              self.改名:置禁止(false)
              self.重置:置禁止(false)
              self.放生:置禁止(false)
              self.确认加点:置禁止(false)
              self.推荐加点:置禁止(false)
              self.查看资质:置禁止(false)
              if 角色信息.宝宝列表[self.选中].饰品 then
                  self.饰品染色:置可见(true)
              end
              if 角色信息.宝宝列表[self.选中].参战信息 then
                  self.参战:重置文字("休息")
              end
              if 窗口层.召唤资质.是否可见 then
                 窗口层.召唤资质:刷新(角色信息.宝宝列表[self.选中])
              end
        elseif 窗口层.召唤资质.是否可见 then
                窗口层.召唤资质:置可见(false)

        end
        self.属性背景 = self:创建纹理精灵(function()
                        if 角色信息 and 角色信息.携带宠物 then
                              文本字体:置颜色(255,255,255,255):取描边图像(#角色信息.宝宝列表.."/"..角色信息.携带宠物,100,100,100,100):显示(120,233)
                        else
                              文本字体:置颜色(255,255,255,255):取描边图像(#角色信息.宝宝列表.."/3",100,100,100,100):显示(120,233)
                        end
                        if self.选中 and 角色信息 and  角色信息.宝宝列表[self.选中] then
                            文本字体:置颜色(255,255,255,255):取描边图像(角色信息.宝宝列表[self.选中].参战等级,100,100,100,100):显示(265, 205)
                        else
                              文本字体:置颜色(255,255,255,255):取描边图像("--",100,100,100,100):显示(265, 205)
                        end
                        文本字体:置颜色(0,0,0,255)
                        if self.选中 and 角色信息 and  角色信息.宝宝列表[self.选中] then
                              文本字体:取图像(角色信息.宝宝列表[self.选中].种类):显示(215, 45)
                              文本字体:取图像(角色信息.宝宝列表[self.选中].气血.."/"..角色信息.宝宝列表[self.选中].最大气血):显示(55, 259)
                              文本字体:取图像(角色信息.宝宝列表[self.选中].魔法.."/"..角色信息.宝宝列表[self.选中].最大魔法):显示(55, 284)
                              for i, v in ipairs(bd1) do
                                  文本字体:取图像(角色信息.宝宝列表[self.选中][v]):显示(55, 284+i*25)
                              end
                              文本字体:取图像(角色信息.宝宝列表[self.选中].忠诚):显示(55, 413)
                              for i, v in ipairs(bd) do
                                  文本字体:置颜色(0,0,0,255):取图像(角色信息.宝宝列表[self.选中][v]):显示(205, 259+(i-1)*25)
                                  if 角色信息.宝宝列表[self.选中].进阶属性 and 角色信息.宝宝列表[self.选中].进阶属性[v] and 角色信息.宝宝列表[self.选中].进阶属性[v]>0 then
                                      文本字体:置颜色(__取颜色("红色")):取图像("+"..角色信息.宝宝列表[self.选中].进阶属性[v]):显示(210+文本字体:取宽度(角色信息.宝宝列表[self.选中][v]), 259+(i-1)*25)
                                      if 角色信息.宝宝列表[self.选中].统御属性 and 角色信息.宝宝列表[self.选中].统御属性[v] and 角色信息.宝宝列表[self.选中].统御属性[v]>0 then
                                          文本字体:置颜色(__取颜色("紫色")):取图像("+"..角色信息.宝宝列表[self.选中].统御属性[v]):显示(215+文本字体:取宽度(角色信息.宝宝列表[self.选中][v])+文本字体:取宽度("+"..角色信息.宝宝列表[self.选中].进阶属性[v]), 259+(i-1)*25)
                                      end
                                  else
                                      if 角色信息.宝宝列表[self.选中].统御属性 and 角色信息.宝宝列表[self.选中].统御属性[v] and 角色信息.宝宝列表[self.选中].统御属性[v]>0 then
                                          文本字体:置颜色(__取颜色("紫色")):取图像("+"..角色信息.宝宝列表[self.选中].统御属性[v]):显示(210+文本字体:取宽度(角色信息.宝宝列表[self.选中][v]), 259+(i-1)*25)
                                      end
                                  end
                              end
                        end
                      end,1
                    )

        self:属性显示()
        if __手机 then
            self.重置:置文字(40,22,"加点")
        else
            self.重置:置文字(40,22,"重置")
        end

end

function 召唤属性:属性显示()
            for i=1,5 do
                  self[bd[i].."加"]:置禁止(true)
                  self[bd[i].."减"]:置禁止(true)
                  if self.选中 and  角色信息 and 角色信息.宝宝列表[self.选中] and self.选中潜力>0 then
                      self[bd[i].."加"]:置禁止(false)
                  end
                  if self.选中 and 角色信息 and 角色信息.宝宝列表[self.选中] and self.临时加点[bd[i]]>0 then
                      self[bd[i].."减"]:置禁止(false)
                  end
            end
            if self.选中 and 角色信息 and 角色信息.宝宝列表[self.选中]  then
                self.预览属性=取属性(角色信息.宝宝列表[self.选中].成长,角色信息.宝宝列表[self.选中].速度资质,self.临时加点)
            end
              self.加点属性 = self:创建纹理精灵(function()
                    if self.选中 and 角色信息 and 角色信息.宝宝列表[self.选中] then
                      文本字体:置颜色(__取颜色("红色"))
                      if self.预览属性.气血>0 then
                          文本字体:取图像("+"..self.预览属性.气血):显示(50+(100-文本字体:取宽度("+"..self.预览属性.气血)), 256)
                      end
                      if self.预览属性.魔法>0 then
                          文本字体:取图像("+"..self.预览属性.魔法):显示(50+(100-文本字体:取宽度("+"..self.预览属性.魔法)), 281)
                      end

                      for i, v in ipairs(bd1) do
                        if self.预览属性[v]>0 then
                            文本字体:取图像("+"..self.预览属性[v]):显示(50+(100-文本字体:取宽度("+"..self.预览属性[v])), 281+i*25)
                        end
                      end
                      for i, v in ipairs(bd) do
                          if self.临时加点[v]>0 then
                              文本字体:取图像("+"..self.临时加点[v]):显示(200+(110-文本字体:取宽度("+"..self.临时加点[v])), 231+i*25)
                          end
                      end
                      
                      文本字体:置颜色(0,0,0,255):取图像(self.选中潜力):显示(200, 388)
                  end
              end,1
            )


end        

local 经验条 = 召唤属性:创建进度("经验条", 51, 441, 218, 16)
function 经验条:初始化()
   -- __res:取资源动画("dlzy",0x3906F9F1,"图像"):显示(50,440)
  self:置精灵(__res:取资源动画("dlzy",0x3906F9F1,"图像"):拉伸(188,16):到精灵())
end
function 经验条:显示(x, y)
      if self.显示经验 then
          self.显示经验:显示(x + (self.宽度-self.显示经验.宽度)//2, y-1)
      end
end


local 名称输入 = 召唤属性:创建文本输入("名称输入", 205, 235, 100, 18)
function 名称输入:初始化()
  self:取光标精灵()
  self:置限制字数(16)
  self:置颜色(0,0,0,255)
end



local 名称选择 = 召唤属性:创建列表("名称选择", 20, 45, 160, 170)
function 名称选择:初始化()
    self.行高度= 37
    self.行间距 = 3
end
local 滑块=召唤属性:创建竖向滑块("名称滑块",180,35,10,190,true)
名称选择:绑定滑块(滑块.滑块)
--local 滑块=名称选择:创建竖向滑块("名称滑块",180,35,10,190)

function 名称选择:置数据()
      self:清空()
      for i, v in ipairs(角色信息.宝宝列表) do

          self:添加():创建纹理精灵(function()
            __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
            local lssj = 取头像(v.模型)
            __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
            if v.加锁 then
              __res:取资源动画("jszy/xjiem",0X85655274,"图像"):显示(145,22)
            end
            文本字体:置颜色(0,0,0,255)
            文本字体:取图像(v.名称):显示(40,4)
            文本字体:取图像(v.等级.."级"):显示(40,20)
          end)
      end
      if not 角色信息.携带宠物 or (角色信息.携带宠物 and 角色信息.携带宠物<10) then
          self:添加():创建纹理精灵(function()
              __res:取资源动画("jszy/ddck",0x0000390,"图像"):显示(0,0)
              文本字体:置颜色(0,0,0,255)
              文本字体:取图像("新增召唤兽栏"):显示(40,14)
            end
          )
      end
end


function 名称选择:左键弹起(x, y, i)
    if 角色信息.宝宝列表[i] then
      召唤属性.选中 = i
      召唤属性:显示设置()
        if 窗口层.召唤资质.是否可见 then
              窗口层.召唤资质:刷新(角色信息.宝宝列表[i])
        end
    elseif i<10 then
        请求服务(104)
    end
end

local 参战 = 召唤属性:创建红色按钮("参战", "参战", 315, 205,40,22) 
function 参战:重置文字(txt,jz)
    self:置文字(40,22,txt)
    self:置禁止(false)
    if jz then
        self:置禁止(jz)
    end
end
function 参战:左键弹起(x, y)
    if 召唤属性.选中 and 角色信息.宝宝列表[召唤属性.选中] then
      请求服务(5002, {
        序列 = 角色信息.宝宝列表[召唤属性.选中].认证码
      })
    end
end
local 改名 = 召唤属性:创建红色按钮("改名", "改名", 315, 230,40,22) 
function 改名:左键弹起(x, y)
        if 召唤属性.选中 and 角色信息.宝宝列表[召唤属性.选中] then
            if 召唤属性.名称输入:取文本() then
              请求服务(5003, {
                序列 = 角色信息.宝宝列表[召唤属性.选中].认证码,
                名称 = 召唤属性.名称输入:取文本()
              })
            else
              __UI弹出.提示框:打开("输入名称不能是空")
            end
        end

end
local 重置 = 召唤属性:创建红色按钮("重置", "重置", 323, 383,40,22) 
function 重置:左键弹起(x, y)
      if 召唤属性.选中 and 角色信息.宝宝列表[召唤属性.选中] then
          if __手机 then
              窗口层.属性加点:打开(角色信息.宝宝列表[召唤属性.选中],"召唤兽")
          else
            召唤属性:显示设置()
          end
      end

end
local 驯养 = 召唤属性:创建红色按钮("驯养", "驯养", 100, 410,40,22) 
function 驯养:左键弹起(x, y)
      请求服务(3742)
end
local 放生 = 召唤属性:创建红色按钮("放生", "放生", 145, 410,40,22) 
function 放生:左键弹起(x, y)
      if 召唤属性.选中 and 角色信息.宝宝列表[召唤属性.选中] then
          local wb = "真的要放生#Y" .. 角色信息.宝宝列表[召唤属性.选中].等级 .. "级的#R" .. 角色信息.宝宝列表[召唤属性.选中].名称 .. "#W/吗?"
          窗口层.文本栏:打开(wb,5005,{序列 = 角色信息.宝宝列表[召唤属性.选中].认证码})
      end
end
local 仓库 = 召唤属性:创建红色按钮("仓库", "仓库", 190, 410,40,22) --红色
function 仓库:左键弹起(x, y)

end
local 鉴定 = 召唤属性:创建红色按钮("鉴定", "鉴定", 235, 410,40,22) --红色
function 鉴定:左键弹起(x, y)

end
local 确认加点 = 召唤属性:创建红色按钮("确认加点", "确认加点", 280, 410,74,22) --红色  (215, 385)
function 确认加点:左键弹起(x, y)
        if 召唤属性.选中 and 角色信息.宝宝列表[召唤属性.选中] then
          召唤属性.临时加点.序列 =角色信息.宝宝列表[召唤属性.选中].认证码
              请求服务(5004,召唤属性.临时加点)
        end
end

local 推荐加点 = 召唤属性:创建红色按钮("推荐加点", "推荐加点", 245, 383,74,22) --红色
function 推荐加点:左键弹起(x, y)
        if 召唤属性.选中 and 角色信息.宝宝列表[召唤属性.选中] then
              local 是否推荐 =true
              local 记录加点 = {}
              if  角色信息.宝宝列表[召唤属性.选中].灵性==nil then 角色信息.宝宝列表[召唤属性.选中].灵性=0 end
              local 已加属性 = 角色信息.宝宝列表[召唤属性.选中].等级*5+角色信息.宝宝列表[召唤属性.选中].灵性*2-召唤属性.选中潜力
              for i=1,5 do
                记录加点[bd[i]]=0
                if 角色信息.宝宝列表[召唤属性.选中].加点记录[bd[i]]~=0 and 已加属性>=5 then
                    记录加点[bd[i]]=math.floor(角色信息.宝宝列表[召唤属性.选中].加点记录[bd[i]]/已加属性*5)
                    是否推荐 = false
                end
              end

              if 是否推荐 then
                召唤属性.临时加点.力量 = 召唤属性.临时加点.力量 + 召唤属性.选中潜力
                召唤属性.选中潜力 = 0
              else
                  local 循环次数 = 召唤属性.选中潜力
                  for i=1,循环次数 do
                    if 召唤属性.选中潜力>0 then
                        for k,v in pairs(记录加点) do
                              if 召唤属性.选中潜力>= v then
                                召唤属性.选中潜力= 召唤属性.选中潜力-v
                                召唤属性.临时加点[k]=召唤属性.临时加点[k]+v
                              end
                          end
                      end
                  end
              end
              召唤属性:属性显示()
          end


end

for i, v in ipairs(bd) do
  local 加号 = 召唤属性:创建按钮(v.."加",315,257+(i-1)*25)
  function 加号:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/dd",0x10000004),1)
  end
  local 减号 = 召唤属性:创建按钮(v.."减",340,257+(i-1)*25)
  function 减号:初始化()
    self:创建按钮精灵(__res:取资源动画("jszy/dd",0x10000003),1)
  end
  function 加号:左键弹起(x, y)
          if 召唤属性.选中 and  角色信息 and 角色信息.宝宝列表[召唤属性.选中] and 召唤属性.选中潜力>0  then
            召唤属性.临时加点[v]=召唤属性.临时加点[v]+1
            召唤属性.选中潜力=召唤属性.选中潜力-1
            召唤属性:属性显示()
          end
  end
  function 减号:左键弹起(x, y)
          if  召唤属性.选中 and  角色信息 and 角色信息.宝宝列表[召唤属性.选中] and 召唤属性.临时加点[v]>0  then
            召唤属性.临时加点[v]=召唤属性.临时加点[v]-1
            召唤属性.选中潜力=召唤属性.选中潜力+1
            召唤属性:属性显示()
          end
  end


end
local 查看资质 = 召唤属性:创建按钮("查看资质",280,435)
function 查看资质:初始化()
    self:创建按钮精灵(__res:取资源动画("dlzy",0xB15C5678),1,"查看资质")
end

function 查看资质:左键弹起(x, y)
      if 召唤属性.选中 and 角色信息.宝宝列表[召唤属性.选中] then
          窗口层.召唤资质:打开(角色信息.宝宝列表[召唤属性.选中])
      end
end
local 宝宝染色 = 召唤属性:创建按钮("宝宝染色",325, 45)
function 宝宝染色:初始化()
      self:创建按钮精灵(__res:取资源动画("jszy/fwtb",0x00000333),1)

end
function 宝宝染色:获得鼠标(x, y)
  __UI弹出.自定义:打开(x+20,y+20,"#G宠物染色")
end
function 宝宝染色:左键弹起(x, y)
  if 召唤属性.选中~=nil then
      窗口层.宝宝染色:打开(角色信息.宝宝列表[召唤属性.选中],召唤属性.选中)
 else
   __UI弹出.提示框:打开('#Y请先选中宝宝！！')
 end
end

local 饰品染色 = 召唤属性:创建按钮("饰品染色",210, 165)
function 饰品染色:初始化()
      self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000115),1)

end
function 饰品染色:获得鼠标(x, y)
  __UI弹出.自定义:打开(x+20,y+20,"#Y该召唤兽拥有饰品可以提高#G10%#Y的资质\n左键点击打开#G饰品染色\n没有饰品效果的宠物#G无法染色饰品\n右键卸下饰品")
end
function 饰品染色:右键弹起(x, y)
  if 召唤属性.选中~=nil then
      请求服务(149,{序列=角色信息.宝宝列表[召唤属性.选中].认证码})
      self:置可见(false)
  else
    __UI弹出.提示框:打开('#Y请先选中宝宝！！')
  end
end


function 饰品染色:左键弹起(x, y)
        local 饰品显示 = 取战斗模型(角色信息.宝宝列表[召唤属性.选中].模型 .. "_饰品") 
        if 召唤属性.选中~=nil and 饰品显示 and #饰品显示>0 then
            if __手机 then
                  local 事件 =function (编号)
                    if 编号==1 then
                           窗口层.饰品染色:打开(角色信息.宝宝列表[召唤属性.选中],召唤属性.选中)
                    else
                          请求服务(149,{序列=角色信息.宝宝列表[召唤属性.选中].认证码})
                          self:置可见(false)
                    end
                end
                __UI弹出.临时按钮:打开({"染色","卸掉"},事件,x,y)
                __UI弹出.自定义:打开(x+20,y+20,"#Y该召唤兽拥有饰品可以提高#G10%#Y的资质\n左键点击打开#G饰品染色\n没有饰品效果的宠物#G无法染色饰品\n右键卸下饰品")
            else
               窗口层.饰品染色:打开(角色信息.宝宝列表[召唤属性.选中],召唤属性.选中)
            end
        else
              __UI弹出.提示框:打开('#Y请先选中宝宝！！')
        end
end




local 关闭 = 召唤属性:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  召唤属性:置可见(false)
    窗口层.召唤资质:置可见(false)
end