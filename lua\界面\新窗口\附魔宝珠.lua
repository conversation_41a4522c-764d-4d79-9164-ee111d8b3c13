local 附魔宝珠 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "附魔宝珠", 80+67 + abbr.py.x, 50 + abbr.py.y, 361+45+290, 492-102)

function 附魔宝珠:初始化()
  local nsf = require("SDL.图像")(361+4+290, 492-102)
  if nsf["渲染开始"](nsf) then
    置窗口背景("附魔宝珠", 0, 12, 355+9+290,480-102, true):显示(0, 0)
    __res:getPNGCC(3, 694, 4, 336, 273):显示(13, 44+3)
    取白色背景(0, 0, 278, 132, true):显示(366, 50)
    字体18:置颜色(139,33,31) --
    字体18:取图像("变身术"):显示(366+71-28,121-89+51+134-145)
    字体18:取图像("变化咒"):显示(366+71-28,121-89+51+134-145+70)
    字体18:取图像("追加法术"):显示(366+71-28+152,121-89+51+134-145)
    字体18:取图像("附加状态"):显示(366+71-28+152,121-89+51+134-145+70)
    __res:getPNGCC(3, 132, 506, 55, 55):显示(366,121-89+51+134)
    
    local lssj = 取输入背景(0, 0, 120, 23)
    lssj:显示(366+158,121-89+51+131)
    lssj:显示(366+158,121-89+51+131+27*1)
    lssj:显示(366+158,121-89+51+131+27*2)
    lssj:显示(366+158,121-89+51+131+27*3)
    字体18:置颜色(255, 255, 255)
    字体18:取图像("消耗经验"):显示(366+71,121-89+51+134)
    字体18:取图像("消耗"):显示(366+71,121-89+51+134+27*1)
    字体18:取图像("消耗金钱"):显示(366+71,121-89+51+134+27*2)
    字体18:取图像("祈福值"):显示(366+71,121-89+51+134+27*3)
    字体18:置颜色(__取颜色("黄色"))
    字体18:取图像("请选择要附加套装效果的装备"):显示(31,347)
    --取白色背景(0, 0, 440, 300, true)["显示"](取白色背景(0, 0, 440, 300, true), 20, 56)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
local sdsawe= {"变身术","追加法术","变化咒","附加状态"}
function 附魔宝珠:打开(道具,宝珠数据)
  self:置可见(true)
  self.宝珠数据=宝珠数据 or 0
  self.道具网格:指导局(__主控.道具列表)
  self.选择 = self.选择 or 2
  self.材料位置 = 0
  self.消耗材料 = 0
  self.消耗 = {经验=0,材料数量=0,金钱=0,祈福值=0}
  self.zhuangb:zhuzhuangb(zb)
  self[sdsawe[self.选择]]:置选中(true)
end
function 附魔宝珠:刷新(数据)
end
local cl = {"青龙石","朱雀石","玄武石","白虎石"}
local function 消耗经验(等级)
	return 等级 * 3000
end

local function 消耗金钱(等级)
	return 等级 * 5000
end

local function 消耗石头(等级)
	return math.floor(等级/10)
end
function 附魔宝珠:计算强消耗(zb)
  if zb and zb.总类 == 2 and zb.分类<=6 and zb.分类~=3 then
    if zb.分类 == 5 or zb.分类 == 6 then
      self.消耗材料 = 3
    else
      self.消耗材料 = zb.分类
    end
    self.消耗 = {经验=0,材料数量=0,金钱=0,祈福值=0}
    self.消耗.金钱 = 消耗金钱(zb.级别限制)
    self.消耗.材料数量 = 消耗石头(zb.级别限制)
    self.消耗.经验 = 消耗经验(zb.级别限制)
    if zb.祈福值~=nil then
      self.消耗.祈福值 = zb.祈福值
    end
    local nsf = require("SDL.图像")(163, 130)
    if nsf["渲染开始"](nsf) then
      字体18:置颜色(__取颜色("紫色"))
      字体18:取图像(cl[self.消耗材料]..":"..self.消耗.材料数量):显示(0,0+27*1)
      字体18:置颜色(__取颜色("浅黑"))
      字体18:取图像(self.消耗.经验):显示(0,0)
      字体18:取图像(self.消耗.金钱):显示(0,0+27*2)
      字体18:取图像(self.消耗.祈福值 .."/30"):显示(0,0+27*3)
      nsf["渲染结束"](nsf)
    end

    self.图像 = nsf["到精灵"](nsf)
    self.图像:置中心(-446-96,-117-100)
  end
end

local 道具网格 = 附魔宝珠["创建网格"](附魔宝珠, "道具网格", 18-5, 47+4, 339, 272)
function 道具网格:左键弹起(x, y, a, b, msg)
  if 附魔宝珠["材料位置"] and self.子控件[附魔宝珠["材料位置"]] then
    self.子控件[附魔宝珠["材料位置"]]._spr["确定"] = nil
  end
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] and not self.子控件[a]._spr["物品"].物品禁止 then
    if self.子控件[a]._spr["物品"].总类 == 2  and self.子控件[a]._spr["物品"].分类==3 then
      __UI弹出.提示框:打开("#Y/武器无法进行点化操作。")
      return
    end
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    附魔宝珠["材料位置"] = a
    附魔宝珠.zhuangb:zhuzhuangb(self.子控件[a]._spr["物品"])
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170+255, 86, w, h, "选择", a)
  end
end
function 道具网格:指导局(data)
  附魔宝珠["材料位置"] = nil
  self:创建格子(67, 67, 0, 0, math.ceil(#data / 5), 5)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], nil, "附魔宝珠")
      lssj["置偏移"](lssj, 10, 10)
      lssj["总类禁止111"](lssj, 2)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end


local zhuangb = 附魔宝珠["创建网格"](附魔宝珠, "zhuangb", 360,121-89+51+130, 67, 67)
function zhuangb:初始化()
  self:创建格子(67, 67, 0, 0, 1, 1)
end
function zhuangb:zhuzhuangb(zb)
  for i = 1, #self.子控件 do
    if zb then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, zb, nil, "附魔宝珠")
      lssj["置偏移"](lssj, 10, 10)
      -- lssj["总类禁止111"](lssj, 2)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
      附魔宝珠:计算强消耗(zb)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end


local aa=0
local bb=0
for i = 1, #sdsawe do
  local 临时函数 = 附魔宝珠["创建我的单选按钮"](附魔宝珠, __res:getPNGCC(2, 1172, 106, 27, 27, true), __res:getPNGCC(2, 1171, 74, 27, 27, true), sdsawe[i], 280-35+128+aa*152, 70 + bb * 70)
  aa=aa+1
  if aa>1 then
    aa=0
    bb=1
  end
  function  临时函数:左键弹起(x, y)
    if i~=3 then
      附魔宝珠.选择 = i
    else
      __UI弹出.提示框:打开("#Y/暂未开放变化咒套装。")
    end
  end
end



local 点化按钮 = 附魔宝珠:创建我的按钮(__res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(143, 41), "点化按钮", 423, 337, "点化")
function  点化按钮:左键弹起(x, y)
  if 附魔宝珠.材料位置 and 附魔宝珠.材料位置~=0 and 附魔宝珠.选择~=0 and 附魔宝珠.宝珠数据~=0 then
    发送数据(3763,{装备=附魔宝珠.材料位置,套装=附魔宝珠.选择,宝珠数据 = 附魔宝珠.宝珠数据})
  else
    if 附魔宝珠.宝珠数据== 0 then
     __UI弹出.提示框:打开("#Y/宝珠数据异常，请重新打开界面操作。")
    else
     __UI弹出.提示框:打开("#Y/请先选择需要点化套装的装备或您未选择点化的套装类型。")
    end
  end
end
local 关闭 = 附魔宝珠["创建我的按钮"](附魔宝珠, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 355+29-53+290, 0)
function 关闭:左键弹起(x, y, msg)
  附魔宝珠["置可见"](附魔宝珠, false)
end