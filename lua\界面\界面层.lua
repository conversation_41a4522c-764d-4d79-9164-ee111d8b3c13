--[[
LastEditTime: 2024-10-21 10:13:11
--]]
local SDL = require("SDL")

function 界面层:初始化()
   self:置宽高(引擎.宽度,引擎.高度)
end

function 界面层:重新初始化()
  self:置宽高(引擎.宽度,引擎.高度)
    for k, v in self:遍历控件() do
        if v.可初始化 then
            v:初始化()
            if v.重新初始化 then
                v:重新初始化()
            end
        end
      
    end


    
end



function 界面层:显示(x, y)
    if self.图像 then
        self.图像:显示(x, y)
    end
    
    -- 摇杆在主界面最后显示，这里不重复显示
    -- if __手机 and self.玩家界面 and self.玩家界面.移动摇杆 and self.玩家界面.移动摇杆.是否可见 then
    --     self.玩家界面.移动摇杆:显示(x, y)
    -- end
end




function 界面层:消息事件(msg) --消息事件是协程
        if not _tp.战斗中 then
            __主显:消息事件(msg)
        end
        if not msg.鼠标 then return end
        for i, v in ipairs(msg.鼠标) do
            local x, y = v.x, v.y
            if x < 0 then
                break
            end
            
            -- 处理移动端摇杆事件 - 添加安全检查
            if __手机 and self.玩家界面 and self.玩家界面.移动摇杆 and self.玩家界面.移动摇杆.是否可见 then
                local 摇杆处理了事件 = false
                if v.button == SDL.BUTTON_LEFT then
                    if v.type == SDL.MOUSE_DOWN then
                        摇杆处理了事件 = self.玩家界面.移动摇杆:鼠标按下(x, y)
                    elseif v.type == SDL.MOUSE_UP then
                        摇杆处理了事件 = self.玩家界面.移动摇杆:鼠标释放(x, y)
                    end
                elseif not v.button and v.type == SDL.MOUSE_MOTION then
                    摇杆处理了事件 = self.玩家界面.移动摇杆:鼠标移动(x, y)
                end
                
                -- 如果摇杆处理了事件，跳过其他处理
                if 摇杆处理了事件 then
                    break
                end
            end
            
            if v.button == SDL.BUTTON_LEFT then
                if v.type == SDL.MOUSE_DOWN then
                         if not _tp.战斗中 and  鼠标层.是否正常 then
                            self.按下 = true
                        elseif _tp.战斗中 and  (not 鼠标层.是否正常 or __手机) and self:页面状态() then
                            self.按下 = true
                        end
                elseif v.type == SDL.MOUSE_UP and self.按下 then
                        self.按下 = false
                        if  _tp.战斗中  then
                            self:战斗左弹起(x, y)
                        elseif not _tp.战斗中 and not self.返回.是否可见 and not 窗口层.对话栏.是否可见 and not 窗口层.交易.是否可见 and 鼠标层.附加 then
                                self:非战斗左弹起(x, y) 
                        end
                end
            elseif v.button == SDL.BUTTON_RIGHT  and not __手机 then
                      if v.type == SDL.MOUSE_DOWN then
                        self.按下1=true
                      elseif v.type == SDL.MOUSE_UP and self.按下1 then
                        self.按下1=false
                        if _tp.战斗中 then
                          if __战斗主控.进程=="命令" then
                              self.战斗界面:重置()
                          end
                          self:战斗右键(x, y)
                        elseif 鼠标层.附加 and ggetype(鼠标层.附加) =="家具格子" then
                              鼠标层.附加:转换方向()
                        end
                      end
            elseif not v.button and  v.type == SDL.MOUSE_MOTION and _tp.战斗中 and not __手机 then
                    self:战斗获得(x, y)
            end
        end
        
end
function 界面层:页面状态()
        if 战斗层.战斗法术.是否可见 then
            return false
        elseif 战斗层.战斗道具.是否可见 then
            return false
        elseif 战斗层.战斗灵宝.是否可见 then
            return false
        elseif 战斗层.战斗召唤.是否可见 then
            return false
        elseif 战斗层.多开法术.是否可见 then
            return false
        elseif 战斗层.九黎法术.是否可见 then
            return false
        end
  return true
end

function 界面层:战斗左弹起(x, y)
      if self:页面状态() then
         for k, z in pairs(__战斗主控.战斗单位) do
                if z:检查点(x, y) then
                    if  __战斗主控.进程=="命令"  and self.战斗界面:取类型选择(z.敌我,k) then
                            self.战斗界面:设置指令(k)
                            z:置高亮(false)
                            鼠标层:正常形状()
                    elseif __手机 then
                            local 传入数据=__战斗主控:鼠标右键状态(k)
                            if 传入数据 and #传入数据>0 then
                                  __UI弹出.战斗状态:打开(传入数据,x, y)
                            end
                    end
                   -- print("我点击战斗中的目标了",k)
                    break
                end
            end
      end
end

function 界面层:非战斗左弹起(x, y)
            local 道具行囊 =  窗口层.新行囊
            if __res.配置.行囊==1 then
                道具行囊 =  窗口层.道具行囊
            end
            if  ggetype(鼠标层.附加) == '物品格子' and 道具行囊.是否可见 and  道具行囊.抓取物品ID  then
                    local 删除名称=""
                    if 道具行囊.包裹类型=="道具" then
                        if _tp.道具列表[道具行囊.抓取物品ID]~=nil then
                            删除名称=_tp.道具列表[道具行囊.抓取物品ID].名称
                        end
                    else  
                        if _tp.行囊列表[道具行囊.抓取物品ID]~=nil then
                            删除名称=_tp.行囊列表[道具行囊.抓取物品ID].名称
                        end
                    end
                    if 删除名称~=nil and 删除名称~="" then
                            窗口层.文本栏:打开("确认#R销毁#Y" .. 删除名称 .. "#W?", 3702, {
                            物品 = 道具行囊.抓取物品ID,
                            类型 = 道具行囊.包裹类型
                        })
                    else
                        道具行囊:重置抓取()
                    end

            elseif  ggetype(鼠标层.附加) =="技能格子" and 鼠标层.附加.编号 and 鼠标层.附加.技能数据 and self.玩家界面.快捷控件.是否可见 and not self.玩家界面.快捷控件.锁住开关 then
                    请求服务(11,鼠标层.附加.技能数据)
                    self.玩家界面.快捷控件["快捷F"..鼠标层.附加.编号].选中技能=nil
                    鼠标层.附加=nil
            elseif  ggetype(鼠标层.附加) =="家具格子" then
                    local xy = require("GGE.坐标")(x, y) - __主显.屏幕坐标
                    if __手机 then
                          local 事件 =function (编号)
                            if 编号==1 then
                                  鼠标层.附加:转换方向()
                                  __主显:家具旋转特效({x=math.floor(xy.x / 20),y=math.floor(xy.y / 20),方向=鼠标层.附加.方向,名称=鼠标层.附加.名称,编号=鼠标层.附加.编号})
                            else
                                  请求服务(1005,{x=math.floor(xy.x / 20),y=math.floor(xy.y / 20),方向=鼠标层.附加.方向,编号=鼠标层.附加.编号})
                                  鼠标层.附加=nil
                            end
                        end
                        if not __主显.地图.家具[鼠标层.附加.编号] then
                            __主显:家具旋转特效({x=math.floor(xy.x / 20),y=math.floor(xy.y / 20),方向=鼠标层.附加.方向,名称=鼠标层.附加.名称,编号=鼠标层.附加.编号})
                        end
                        __UI弹出.临时按钮:打开({"转向","放置"},事件,x,y)
                    else
                        local xy = require("GGE.坐标")(x, y) - __主显.屏幕坐标
                        请求服务(1005,{x=math.floor(xy.x / 20),y=math.floor(xy.y / 20),方向=鼠标层.附加.方向,编号=鼠标层.附加.编号})
                        鼠标层.附加=nil
                    end
            elseif 鼠标层.附加 then
                    鼠标层.附加 =nil
                    if 道具行囊.是否可见 then
                      道具行囊:重置抓取()
                    end
            --elseif 引擎:取功能键状态(SDL.KMOD_CTRL) then

            end

end

function 界面层:战斗右键(x, y)
  if self:页面状态() then
      for k, z in pairs(__战斗主控.战斗单位) do
          if z:检查点(x, y) then
                  local 传入数据=__战斗主控:鼠标右键状态(k)
                  if 传入数据 and #传入数据>0 then
                        __UI弹出.战斗状态:打开(传入数据,x, y)
                  end
                break
          end
      end
  end
end

function 界面层:战斗获得(x, y)
    if self:页面状态() then
        for k, z in pairs(__战斗主控.战斗单位) do
            if z:检查点(x, y) then
                z:置高亮(true)
                z:选中名称(true)
                 if __战斗主控.进程=="命令" and (not self.战斗界面.命令类型 or self.战斗界面.命令类型=="") and 鼠标层.是否正常  then
                    鼠标层:攻击形状()
                 end
                break
            else
                z:置高亮(false)
                if z.名称变色 then
                    z:选中名称()
                end
            end
        end
    end
end


function 界面层:重置(lx, cz)
    self.图像 = nil
    self.类型 = nil
    if "给予" == lx or "攻击" == lx or "组队" == lx or "交易" == lx  then
            self.玩家界面:置可见(false)
            self.聊天控件:置可见(false)
            self.任务追踪:置可见(false)
            self.状态图标:置可见(false)
            self.返回:置可见(true, true)
            self.图像 = self:创建纹理精灵(function()
                              local kd = 文本字体:取宽度("请选择" .. lx .. "对象")
                              __res.UI素材[2]:复制区域(230, 964, 402, 51):拉伸(kd + 20, 20):显示(引擎.宽度2 - (kd + 20) / 2, 85)
                              文本字体:置颜色(255, 255, 255)
                              文本字体:取图像("请选择" .. lx .. "对象"):显示(引擎.宽度2 - kd // 2, 89)
                          end,1
                        )
            self.类型 = lx
    else
            self.玩家界面:置可见(true, true)
            self.玩家界面.时辰控件:重置()
            --self.聊天控件:重置()
            self.聊天控件:置可见(true,true)
            if not __手机 then
                --self.聊天控件.频道表情:置可见(false)

                __UI弹出.频道表情:置可见(false)
                self.聊天控件.频道颜色:置可见(false)
                self.聊天控件.频道控件:置可见(false)
            end
            self.任务追踪:置可见(true, true)
            self.状态图标:置可见(true,true)
            self.队伍栏:置可见(true, true)
            if cz then
              self.玩家界面.人物头像:置头像(角色信息)
              self.玩家界面.宠物头像:置头像(角色信息.参战宝宝)
            end
            self.返回:置可见(false)
            self:置精灵(nil)
            if __手机 then
                self.玩家界面.按钮控件.成就:置可见(false)
               -- self.玩家界面.按钮控件.宠物:置可见(false)
                self.玩家界面.按钮控件.任务:置可见(false)
            end
            if self.任务追踪.显示开关 then
                self.任务追踪.追踪列表:置可见(true)
                self.任务追踪.任务:置可见(true)
                self.任务追踪.时钟:置可见(true)
                if __手机 then
                    self.任务追踪.开关:置坐标(0,80)
                else
                    self.任务追踪.开关:置坐标(-(self.任务追踪.开关.宽度+5), 0)
                end
                self.任务追踪:背景设置()
            else
                self.任务追踪.开关:置坐标(-(self.任务追踪.开关.宽度+5), 0)
                self.任务追踪:置精灵()
                self.任务追踪.追踪列表:置可见(false)
                self.任务追踪.任务:置可见(false)
                self.任务追踪.时钟:置可见(false)
            end



    end
end




function 界面层:进入战斗()
    self.任务追踪:置可见(false)
    self.队伍栏:置可见(false)
    if __手机 then
          self.玩家界面.按钮控件:置可见(false)
    else
        if self.聊天控件.聊天窗口 then
            self.聊天控件.聊天窗口:进入战斗()
        end
    end
    for k, v in 窗口层:遍历控件() do
       if v.是否可见 then
            v:置可见(false)
       end
    end
end

function 界面层:退出战斗()
    self.队伍栏:置可见(true)
    self.玩家界面.按钮控件:置可见(true)
    self.任务追踪:置可见(true)
    if self.任务追踪.显示开关 then
        self.任务追踪.追踪列表:置可见(true)
        self.任务追踪.任务:置可见(true)
        self.任务追踪.时钟:置可见(true)
        if __手机 then
            self.任务追踪.开关:置坐标(0,80)
        else
            self.任务追踪.开关:置坐标(-(self.任务追踪.开关.宽度+5), 0)
        end
        self.任务追踪:背景设置()
    else
        self.任务追踪.开关:置坐标(-(self.任务追踪.开关.宽度+5), 0)
        self.任务追踪:置精灵()
        self.任务追踪.追踪列表:置可见(false)
        self.任务追踪.任务:置可见(false)
        self.任务追踪.时钟:置可见(false)
    end
    if __手机 then
        self.玩家界面.按钮控件.成就:置可见(false)
      --  self.玩家界面.按钮控件.宠物:置可见(false)
        self.玩家界面.按钮控件.任务:置可见(false)
    else
        if self.聊天控件.聊天窗口 then
           self.聊天控件.聊天窗口:退出战斗()
        end
    end
    self.战斗界面:退出()
    战斗层.多开自动:置可见(false)
    战斗层.战斗自动:置可见(false)
end


local 返回 = 界面层:创建控件("返回", 0 ,0, 56, 56)
function 返回:初始化()
    self:置坐标(引擎.宽度-self.宽度-30,引擎.高度-self.高度-30)
end

local 返回按钮 =返回:创建按钮("返回按钮")
function 返回按钮:初始化()
    self:创建按钮精灵(__res.UI素材[2]:复制区域(973, 1, 56, 56),1)
end


function 返回按钮:左键弹起(x, y, msg)
      界面层:重置()
      鼠标层:正常形状()
end





    



   





