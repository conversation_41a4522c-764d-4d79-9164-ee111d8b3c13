local 装备幻化 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "装备幻化", 96 + abbr.py.x, 23 + abbr.py.y, 727, 484)
function 装备幻化:初始化()
  local nsf = require("SDL.图像")(727, 484)
  if nsf["渲染开始"](nsf) then
    置窗口背景("幻化", 0, 12, 720, 473, true)["显示"](置窗口背景("幻化", 0, 12, 720, 473, true), 0, 0)
    __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 30, 72)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 410, 72)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 613, 72)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 410, 263)
    __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 613, 263)
    local lssj = 取输入背景(0, 0, 55, 23)
    lssj["显示"](lssj, 410, 133)
    lssj["显示"](lssj, 613, 133)
    lssj["显示"](lssj, 410, 321)
    lssj["显示"](lssj, 613, 321)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "体力消耗：")["显示"](字体18["取图像"](字体18, "体力消耗："), 30, 370)
    lssj["显示"](lssj, 115, 368)
    字体18["取图像"](字体18, "成功率：")["显示"](字体18["取图像"](字体18, "成功率："), 195, 370)
    lssj["显示"](lssj, 305, 368)
    字体18["取图像"](字体18, "剩余陨铁：")["显示"](字体18["取图像"](字体18, "剩余陨铁："), 30, 420)
    lssj["显示"](lssj, 115, 418)
    字体18["取图像"](字体18, "强化石消耗：")["显示"](字体18["取图像"](字体18, "强化石消耗："), 195, 420)
    lssj["显示"](lssj, 305, 418)
    __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", 取物品("青龙石")[12]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", 取物品("青龙石")[12])), 416, 79)
    __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", 取物品("白虎石")[12]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", 取物品("白虎石")[12])), 615, 79)
    __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", 取物品("朱雀石")[12]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", 取物品("朱雀石")[12])), 418, 270)
    __res["取图像"](__res, __res["取地址"](__res, "shape/dj/", 取物品("玄武石")[12]))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/dj/", 取物品("玄武石")[12])), 614, 273)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 装备幻化:打开()
  self:置可见(true)
  self.道具网格["置物品"](self.道具网格, __主控["道具列表"], {2, 204}, {
    1,
    2,
    3,
    4,
    5,
    6
  })
  self.材料网格["置物品"](self.材料网格, nil)
  self.选中道具 = nil
  self.幻化数据 = {
    ["青龙石"] = 0,
    ["白虎石"] = 0,
    ["朱雀石"] = 0,
    ["玄武石"] = 0,
    ["陨铁"] = __取背包物品数量("陨铁")
  }
  self.成功率 = 0
  self.刷新(self)
end
function 装备幻化:刷新(data)
  if data then
    self.道具网格["置物品"](self.道具网格, data, {2, 204}, {
      1,
      2,
      3,
      4,
      5,
      6
    })
    self.材料网格["置物品"](self.材料网格, nil)
    self.选中道具 = nil
    self.幻化数据 = {
      ["青龙石"] = 0,
      ["白虎石"] = 0,
      ["朱雀石"] = 0,
      ["玄武石"] = 0,
      ["陨铁"] = __取背包物品数量("陨铁")
    }
    self.成功率 = 0
  end
  local nsf = require("SDL.图像")(727, 484)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, self.幻化数据["青龙石"])["显示"](字体18["取图像"](字体18, self.幻化数据["青龙石"]), 425, 135)
    字体18["取图像"](字体18, self.幻化数据["白虎石"])["显示"](字体18["取图像"](字体18, self.幻化数据["白虎石"]), 628, 135)
    字体18["取图像"](字体18, self.幻化数据["朱雀石"])["显示"](字体18["取图像"](字体18, self.幻化数据["朱雀石"]), 425, 323)
    字体18["取图像"](字体18, self.幻化数据["玄武石"])["显示"](字体18["取图像"](字体18, self.幻化数据["玄武石"]), 628, 323)
    字体18["取图像"](字体18, 20)["显示"](字体18["取图像"](字体18, 20), 130, 370)
    字体18["取图像"](字体18, self.成功率)["显示"](字体18["取图像"](字体18, self.成功率), 320, 370)
    字体18["取图像"](字体18, self.幻化数据["陨铁"])["显示"](字体18["取图像"](字体18, self.幻化数据["陨铁"]), 130, 420)
    字体18["取图像"](字体18, self.幻化数据["青龙石"] + self.幻化数据["白虎石"] + self.幻化数据["朱雀石"] + self.幻化数据["玄武石"])["显示"](字体18["取图像"](字体18, self.幻化数据["青龙石"] + self.幻化数据["白虎石"] + self.幻化数据["朱雀石"] + self.幻化数据["玄武石"]), 320, 420)
  end
  self.图像 = nsf["到精灵"](nsf)
end
local 关闭 = 装备幻化["创建我的按钮"](装备幻化, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 677, 0)
function 关闭:左键弹起(x, y, msg)
  装备幻化["置可见"](装备幻化, false)
end
for i, v in ipairs({
  {
    name = "自动加入强化石",
    x = 384,
    y = 409,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 175, 41),
    font = "自动加入强化石"
  },
  {
    name = "确定",
    x = 591,
    y = 409,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 103, 41),
    font = "确定"
  },
  {
    name = "青龙加",
    x = 470,
    y = 131,
    tcp = __res:getPNGCC(1, 641, 320, 29, 29),
    lx = "青龙石"
  },
  {
    name = "青龙减",
    x = 378,
    y = 131,
    tcp = __res:getPNGCC(1, 601, 319, 29, 29),
    lx = "青龙石"
  },
  {
    name = "白虎加",
    x = 676,
    y = 131,
    tcp = __res:getPNGCC(1, 641, 320, 29, 29),
    lx = "白虎石"
  },
  {
    name = "白虎减",
    x = 584,
    y = 131,
    tcp = __res:getPNGCC(1, 601, 319, 29, 29),
    lx = "白虎石"
  },
  {
    name = "朱雀加",
    x = 470,
    y = 317,
    tcp = __res:getPNGCC(1, 641, 320, 29, 29),
    lx = "朱雀石"
  },
  {
    name = "朱雀减",
    x = 378,
    y = 317,
    tcp = __res:getPNGCC(1, 601, 319, 29, 29),
    lx = "朱雀石"
  },
  {
    name = "玄武加",
    x = 676,
    y = 317,
    tcp = __res:getPNGCC(1, 641, 320, 29, 29),
    lx = "玄武石"
  },
  {
    name = "玄武减",
    x = 584,
    y = 317,
    tcp = __res:getPNGCC(1, 601, 319, 29, 29),
    lx = "玄武石"
  }
}) do
  local 临时函数 = 装备幻化["创建我的按钮"](装备幻化, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "自动加入强化石" then
      if 装备幻化["材料网格"]["子控件"][1]._spr["物品"] and 装备幻化["材料网格"]["子控件"][1]._spr["原始编号"] and 装备幻化["材料网格"]["子控件"][1]._spr["物品"]["幻化次数"] and 装备幻化["材料网格"]["子控件"][1]._spr["物品"]["幻化次数"] >= 10 then
        装备幻化["幻化数据"]["青龙石"] = 0
        装备幻化["幻化数据"]["白虎石"] = 0
        装备幻化["幻化数据"]["朱雀石"] = 0
        装备幻化["幻化数据"]["玄武石"] = 0
        local lssl = 40
        for s, k in ipairs({
          {
            name = "青龙石",
            ["数量"] = __取背包物品数量("青龙石")
          },
          {
            name = "白虎石",
            ["数量"] = __取背包物品数量("白虎石")
          },
          {
            name = "朱雀石",
            ["数量"] = __取背包物品数量("朱雀石")
          },
          {
            name = "玄武石",
            ["数量"] = __取背包物品数量("玄武石")
          }
        }) do
          if lssl > 0 and 0 ~= k["数量"] then
            if lssl > k["数量"] then
              装备幻化["幻化数据"][k.name] = k["数量"]
              lssl = lssl - k["数量"]
            else
              装备幻化["幻化数据"][k.name] = lssl
              lssl = 0
            end
          end
        end
        装备幻化["成功率"] = 装备幻化["成功率"] + (40 - lssl) // 2
        装备幻化["刷新"](装备幻化)
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y元身幻化前10次无需添加强化石")
      end
    elseif v.name == "确定" then
      if 装备幻化["材料网格"]["子控件"][1]._spr["物品"] and 装备幻化["材料网格"]["子控件"][1]._spr["原始编号"] then
        发送数据(4502, {
          ["序列"] = 装备幻化["材料网格"]["子控件"][1]._spr["原始编号"],
          ["材料"] = {
            ["青龙石"] = 装备幻化["幻化数据"]["青龙石"],
            ["白虎石"] = 装备幻化["幻化数据"]["白虎石"],
            ["朱雀石"] = 装备幻化["幻化数据"]["朱雀石"],
            ["玄武石"] = 装备幻化["幻化数据"]["玄武石"]
          }
        })
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请选择要幻化的装备")
      end
    end
  end
end
local 道具网格 = 装备幻化["创建网格"](装备幻化, "道具网格", 30, 72, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] and not self.子控件[a]._spr["物品禁止"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    if 装备幻化["选中道具"] and self.子控件[装备幻化["选中道具"]]._spr["物品"] then
      self.子控件[装备幻化["选中道具"]]._spr["确定"] = nil
    end
    装备幻化["选中道具"] = a
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
    装备幻化["材料网格"]["置物品"](装备幻化["材料网格"], __主控["道具列表"][a], 1, a)
    if not self.子控件[a]._spr["物品"]["幻化次数"] or self.子控件[a]._spr["物品"]["幻化次数"] < 10 then
      装备幻化["成功率"] = 100
    else
      装备幻化["成功率"] = 80
    end
    装备幻化["刷新"](装备幻化)
  end
end
function 道具网格:置物品(data, zl, fl)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], nil, "道具选择")
      lssj["置总类禁止"](lssj, zl)
      lssj["置分类禁止"](lssj, fl)
      lssj["置等级禁止"](lssj, 150, "小")
      lssj["置偏移"](lssj, 10, 10)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end
local 材料网格 = 装备幻化["创建网格"](装备幻化, "材料网格", 511, 190, 55, 55)
function 材料网格:初始化()
  self:创建格子(55, 55, 0, 0, 1, 1)
end
function 材料网格:左键弹起(x, y, a, b, msg)
end
function 材料网格:置物品(data, bh, pid)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __材料格子["创建"]()
      lssj["置物品"](lssj, data)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __材料格子["创建"]()
    lssj["置物品"](lssj, data)
    lssj["原始编号"] = pid
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end
