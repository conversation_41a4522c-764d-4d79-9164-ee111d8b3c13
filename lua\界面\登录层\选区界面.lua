

local 选区界面 = 登录层:创建控件("选择大区", 0, 0, 引擎.宽度, 引擎.高度)

function 选区界面:初始化()
 
  self:创建纹理精灵(function()
                __res:取资源动画("ui",0x01000158,"图像"):显示(引擎.宽度2-150,10)
                __res:取资源动画('dlzy',0xD8632D28,"图像"):显示(引擎.宽度2-345, 引擎.高度-470)
                for i=1,7 do
                    __res:取资源动画('jszy/jmxiufu',0x00000024,"图像"):显示(引擎.宽度2-405+i*70, 引擎.高度-455)
                    for n=1,9 do
                    __res:取资源动画('jszy/jmxiufu',0x00000024,"图像"):显示(引擎.宽度2-405+i*70, 引擎.高度-455+n*29)
                    end
                end
                for a=1,7 do
                    for b=1,2 do
                        __res:取资源动画('jszy/jmxiufu',0x00000025,"图像"):显示 (引擎.宽度2-405+a*70, 引擎.高度-185+b*29)
                    end
                end
                __res:取资源动画('ui',0x3961AD58,"图像"):显示(引擎.宽度-140, 引擎.高度-460)
                __res:取资源动画('jszy/jmxiufu',0x00000038,"图像"):显示(引擎.宽度-145, 引擎.高度-230)
                __res:取资源动画('jszy/jmxiufu',0x00000020,"图像"):显示(引擎.宽度2-325, 引擎.高度-73)
                __res:取资源动画('jszy/jmxiufu',0x00000021,"图像"):显示(引擎.宽度2+50, 引擎.高度-73)
                __res:取资源动画('jszy/jmxiufu',0x00000016,"图像"):显示(引擎.宽度2-325, 引擎.高度-100)
                __res:取资源动画('jszy/jmxiufu',0x00000018,"图像"):显示(引擎.宽度2+55, 引擎.高度-100)
                for i=1,5 do
                  __res:取资源动画('jszy/jmxiufu',0x00000026,"图像"):显示(引擎.宽度2-370+i*70, 引擎.高度-75)
                end
                __res:取资源动画('jszy/jmxiufu',0x00000026,"图像"):显示(引擎.宽度2+75, 引擎.高度-75)

            end
    )
    self.新区 = __res:取资源动画('ui',0x5286B0B5,"动画")

end
function 选区界面:更新(dt)
    self.新区:更新(dt)
    
end



function 选区界面:显示(x, y)
        self.新区:显示(引擎.宽度-120, 引擎.高度-268)
end



local 大区列表=选区界面:创建网格("大区列表", 引擎.宽度2-335, 引擎.高度-454, 490, 290)
function 大区列表:初始化()
     self:置数据()
end
function 大区列表:置数据()
    self:创建格子(65, 24, 5, 5, 10, 7)
    for i = 1, #self.子控件 do
        if  __区名列表 and  __区名列表[i] then
            self.子控件[i]:创建纹理精灵(function()
              if 选区界面.选中大区~=nil and 选区界面.选中大区==i then
                  __res:取资源动画("dlzy", 0xF5674AFF,"图像"):显示(0, 0)
              else
                  __res:取资源动画("dlzy", 0xD330CE3F,"图像"):显示(0, 0)
              end
            文本字体:置颜色(255, 255, 255)
            文本字体:取图像(__区名列表[i]):显示((65 - 文本字体:取宽度(__区名列表[i]))// 2, 3)
            end
          )
        else
            self.子控件[i]:置精灵()
        end
      end
end
function 大区列表:左键弹起(x, y, a)
    选区界面.选中小区=nil
    if __区名列表 and  __区名列表[a] then 
        选区界面.选中大区 = a
    else
        选区界面.选中大区 =nil
    end
    选区界面.小区列表:置数据()
    self:置数据()
 end



local 小区列表=选区界面:创建网格("小区列表", 引擎.宽度2-335, 引擎.高度-155, 490, 60)
function 小区列表:初始化()
      
end
function 小区列表:左键弹起(x, y, a)
      if 选区界面.选中大区~=nil and  __分区列表 and  __分区列表[选区界面.选中大区]  and  __分区列表[选区界面.选中大区][a] then
              if 选区界面.选中小区~=nil  then
                  if  选区界面.选中小区~=a then
                      选区界面.选中小区=a
                      self:置数据()
                  else
                      __CLT:断开()
                      if not __CLT:连接( __分区列表[选区界面.选中大区][a].IP,  __分区列表[选区界面.选中大区][a].端口) then
                          print("连接失败")
                      else
                          __连接信息.IP =   __分区列表[选区界面.选中大区][a].IP
                          __连接信息.端口 =  __分区列表[选区界面.选中大区][a].端口
                          __连接信息.选中大区=__区名列表[选区界面.选中大区]
                          __连接信息.选中小区= __分区列表[选区界面.选中大区][a].名称
                          选区界面:置可见( false)
                          登录层.登录游戏:置可见(true, true)
                      end
                  end
              else
                  选区界面.选中小区=a
                  self:置数据()
              end
      end
  end



  function 小区列表:置数据()
    self:创建格子(65, 24, 5, 5, 2, 7)
    for i = 1, #self.子控件 do
        if 选区界面.选中大区~=nil and __分区列表 and  __分区列表[选区界面.选中大区] and  __分区列表[选区界面.选中大区][i] then
            self.子控件[i]:创建纹理精灵(function()
                      if 选区界面.选中小区~=nil and 选区界面.选中小区==i then
                          __res:取资源动画("dlzy", 0xF5674AFF,"图像"):显示(0, 0)
                      else
                          __res:取资源动画('dlzy',0xBD57A592,"图像"):显示(0, 0)
                      end
                      local zts=文本字体:置颜色(255, 255, 255):取图像(__分区列表[选区界面.选中大区][i].名称) 
                      zts:显示((65 - zts.宽度)// 2, 3)
                  end
              )
        else
            self.子控件[i]:置精灵()
        end
      end
end



local 进入游戏 = 选区界面:创建按钮("进入游戏", 引擎.宽度-150,  引擎.高度-130)
function 进入游戏:初始化()
  self:创建按钮精灵(__res:取资源动画("ui", 0x01000258), 1)
end
function 进入游戏:左键弹起(x, y, msg)
    if 选区界面.选中大区~=nil and 选区界面.选中小区~=nil and __分区列表 then
 
        __CLT:断开()
        if not __CLT:连接( __分区列表[选区界面.选中大区][选区界面.选中小区].IP,  __分区列表[选区界面.选中大区][选区界面.选中小区].端口) then
            print("连接失败")
        else  
             
                __连接信息.IP =  __分区列表[选区界面.选中大区][选区界面.选中小区].IP
                __连接信息.端口 =  __分区列表[选区界面.选中大区][选区界面.选中小区].端口
                __连接信息.选中大区= __区名列表[选区界面.选中大区]
                __连接信息.选中小区= __分区列表[选区界面.选中大区][选区界面.选中小区].名称
                选区界面:置可见( false)
                登录层.登录游戏:置可见(true, true)
        end
    end
end
local 退出游戏 = 选区界面:创建按钮( "退出游戏",引擎.宽度-150,  引擎.高度-80)
function 退出游戏:初始化()
  self:创建按钮精灵(__res:取资源动画("ui","01000247.was"), 1)
end
function 退出游戏:左键按下(消息, x, y)
    if 消息 then
        引擎:关闭()
    end
end
