--[[
LastEditTime: 2024-10-06 01:37:40
--]]
local 武器染色 = 窗口层:创建窗口("武器染色", 0,0, 510, 280)
local 武器染色方案={
	[1]={方案={[1]=1,[2]=0},id=64},
	[2]={方案={[1]=1,[2]=0},id=0},
	[3]={方案={[1]=1,[2]=0},id=106},
	[4]={方案={[1]=1,[2]=0},id=77},
	[5]={方案={[1]=1,[2]=0},id=2051},
	[6]={方案={[1]=1,[2]=0},id=2065},
	[7]={方案={[1]=1,[2]=1},id=56},
	[8]={方案={[1]=1,[2]=0},id=67},
	[9]={方案={[1]=1,[2]=0},id=52},
	[10]={方案={[1]=1,[2]=1},id=107},
	[11]={方案={[1]=1,[2]=0},id=76},
	[12]={方案={[1]=1,[2]=0},id=2070},
	[13]={方案={[1]=0,[2]=1},id=2057},
	[14]={方案={[1]=2},id=119},
	[15]={方案={[1]=1,[2]=0},id=101},
	[16]={方案={[1]=2,[2]=0},id=2000},
	[17]={方案={[1]=5,[2]=0},id=2078},
	[18]={方案={[1]=5,[2]=0},id=2079},
	[19]={方案={[1]=1,[2]=0},id=2042},
	[20]={方案={[1]=1,[2]=1},id=90},
	[21]={方案={[1]=1,[2]=0},id=2071},
	[22]={方案={[1]=1,[2]=0},id=95},
	[23]={方案={[1]=1,[2]=1},id=55},
	[24]={方案={[1]=1,[2]=0},id=66},
	[25]={方案={[1]=4,[2]=0},id=20113},
	[26]={方案={[1]=1,[2]=0},id=98},
	[27]={方案={[1]=1,[2]=1},id=94},
	[28]={方案={[1]=1,[2]=1},id=59},
	[29]={方案={[1]=1,[2]=0},id=68},
	[30]={方案={[1]=1,[2]=0},id=78},
	[31]={方案={[1]=1,[2]=0},id=62},
	[32]={方案={[1]=1,[2]=0},id=103},
	[33]={方案={[1]=1,[2]=1},id=92},
	[34]={方案={[1]=0,[2]=3},id=20103},
	[35]={方案={[1]=1,[2]=0},id=65},
	[36]={方案={[1]=1,[2]=1},id=91},
	[37]={方案={[1]=1,[2]=0},id=97},
	[38]={方案={[1]=1,[2]=0},id=96},
	[39]={方案={[1]=1,[2]=1},id=88},
	[40]={方案={[1]=1,[2]=1},id=73},
	[41]={方案={[1]=3,[2]=0},id=20230},
}



function 武器染色:初始化()
        self:创建纹理精灵(function()
            置窗口背景("武器染色", 0, 0, 510, 280, true):显示(0, 0)
            取白色背景(0, 0, 230, 240, true):显示(10, 30)
            __res:取资源动画("pic", "wqrs.png","图片"):拉伸(230,240):显示(10,30)
            __res:取资源动画("pic", "rsbeij.png","图片"):显示(255,70)
            __res:取资源动画("pic", "rstiao.png","图片"):显示(310,130)
            __res:取资源动画("pic", "rsxbj.png","图片"):显示(335,33)
            取输入背景(0, 0, 120, 23):显示(330,175)
            取输入背景(0, 0, 120, 23):显示(330,205)
            文本字体:置颜色(255,255,255,255)
            文本字体:取图像("选中颜色为:"):显示(250, 35)
            文本字体:取图像("五彩织"):显示(250, 130)
            文本字体:取图像("我的仙玉"):显示(265, 180)
            文本字体:取图像("所需仙玉"):显示(265, 210)
            排行标题:置颜色(0,0,0,255)
            排行标题:取投影图像("1000"):显示(335, 210)
        end)
        self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
        self.可初始化=true
        if __手机 then
            self.关闭:置大小(25,25)
            self.关闭:置坐标(self.宽度-27, 2)
        else
            self.关闭:置大小(16,16)
            self.关闭:置坐标(self.宽度-18, 2)
        end
end
function 武器染色:打开(内容)
        self:置可见(not self.是否可见)
        if not self.是否可见 then
            return
        end
        self.仙玉=排行标题:置颜色(__取银子颜色(内容.仙玉+0)):取描边精灵(内容.仙玉)
        self.染色id = 0
        self.方向 = 5
        self:置模型()
end 

function 武器染色:更新(dt)
        if self.人物模型 then
                self.人物模型:更新(dt)
        end
        if self.武器显示 then
                self.武器显示:更新(dt)
        end
        

end
function 武器染色:显示(x,y)
        if self.仙玉 then
           self.仙玉:显示(x+335,y+180)  
        end
        if self.染色显示 then
             self.染色显示:显示(x+335+(35-self.染色显示.宽度)//2,y+35)
        end
        if self.人物模型 then
            self.人物模型:显示(x+125,y+210)
        end
        if self.武器显示 then
            self.武器显示:显示(x+125,y+210)
        end

end


function 武器染色:置模型()

        if self.染色id ==0 then
            self.染色显示=文本字体:置颜色(255,255,255,255):取精灵(0)
            self.染色方案=0
            self.染色组={}
            self.滑块:置位置(0)
        else
            self.染色显示=文本字体:置颜色(255,255,255,255):取精灵(self.染色id)
            self.染色方案=武器染色方案[self.染色id].id
            self.染色组={}
            self.染色组[1]=武器染色方案[self.染色id].方案[1] 
            self.染色组[2]=武器染色方案[self.染色id].方案[2] or 0
        end
        local 资源 = 取模型(角色信息.模型)
        local  m
        if 角色信息.装备 and 角色信息.装备[3] ~= nil then
                m= _tp:取武器子类(角色信息.装备[3].子类)
                if 角色信息.装备[3].名称 == "龙鸣寒水" or 角色信息.装备[3].名称 == "非攻" then
                        m = "弓弩1"
                end
                资源 = 取模型(角色信息.模型, m)
        end
        self.人物模型 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
        if 角色信息.装备 and 角色信息.装备[3] ~= nil then
                local ms = _tp:取武器附加名称(角色信息.装备[3].子类, 角色信息.装备[3].级别限制,角色信息.装备[3].名称)
                资源 = 取模型(ms .. "_" .. 角色信息.模型)
                self.武器显示 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)

                if self.染色方案~=nil and self.染色方案~=0 and self.染色组~=nil and self.染色组~=0 and #self.染色组>0 then
                        local 调色板  = __dewpal(self.染色方案)
                        self.武器显示:调色(调色板,取调色数据(self.染色组))
                end
        end
        self:置方向(self.方向)
end






function 武器染色:置方向(v)
        if self.人物模型 then
            self.人物模型:置方向(v)
        end
        if self.武器显示 then
            self.武器显示:置方向(v)
        end

        
end





local 滑块=武器染色:创建滑块("滑块",258,66,235,37)
滑块.最大值=41

function 滑块:滚动事件(x,y,a)
        武器染色.染色id=a
        武器染色:置模型()
end

local 按钮= 滑块:创建滑块按钮("按钮",0,0)
function 按钮:初始化()
        self:创建按钮精灵(__res:取资源动画('jszy/dd',00000007),nil,nil,10,37)
end


local 方向按钮 = 武器染色:创建按钮("方向按钮",110, 240)
function 方向按钮:初始化()
      self:创建按钮精灵(__res:取资源动画("jszy/ui",0x00000072),1)

end

function 方向按钮:左键弹起()
       武器染色.方向 = 武器染色.方向 - 1
	if 武器染色.方向<1 then
            武器染色.方向=8
	end
	武器染色:置方向(武器染色.方向)
end

local 还原=武器染色:创建红色按钮("还原", "还原按钮", 300, 240,50,22)
function 还原:左键弹起()
    武器染色.染色id = 0
    武器染色:置模型()
end

local 染色=武器染色:创建红色按钮("染色", "染色按钮", 400, 240,50,22)
function 染色:左键弹起()
    if 角色信息.装备 and 角色信息.装备[3]  then
        角色信息.装备[3].染色方案= 武器染色.染色方案
        角色信息.装备[3].染色组={}
        角色信息.装备[3].染色组[1]=武器染色.染色组[1]
        角色信息.装备[3].染色组[2]=武器染色.染色组[2]
        请求服务(3745,{
            序列=武器染色.染色方案,
            序列1=武器染色.染色组[1],
            序列2=武器染色.染色组[2]
          })
      
    end
end

local 关闭 = 武器染色:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
        武器染色:置可见(false)
end