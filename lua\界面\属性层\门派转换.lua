
local 门派转换 = 窗口层:创建窗口("门派转换", 0, 0, 545, 345)
function 门派转换:初始化()
  self:置精灵(置窗口背景("门派转换", 0, 12, 545, 345)) 
  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)

  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
end


function 门派转换:打开(方式)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
      return
  end
  self.方式=nil
  if 方式 then
    self.方式 = 方式
  end
  self.角色控件:置可见(true)
  self.门派选择:置可见(false)
  self.门派选择:重置(角色信息.模型,"未选择")
  self.银两支付:置可见(false)
  self.银两支付:重置(角色信息.模型,角色信息.门派)
end

local 门派按钮组={
  大唐官府= __res:取资源动画("pic/zmp", "dtgf.png","图片"),
  方寸山= __res:取资源动画("pic/zmp", "fcs.png","图片"),
  女儿村= __res:取资源动画("pic/zmp", "nrc.png","图片"),
  神木林= __res:取资源动画("pic/zmp", "sml.png","图片"),
  化生寺=__res:取资源动画("pic/zmp", "hss.png","图片"),
  魔王寨=__res:取资源动画("pic/zmp", "mwz.png","图片"),
  阴曹地府= __res:取资源动画("pic/zmp", "ycdf.png","图片"),
  盘丝洞=__res:取资源动画("pic/zmp", "psd.png","图片"),
  无底洞= __res:取资源动画("pic/zmp", "wdd.png","图片"),
  狮驼岭=__res:取资源动画("pic/zmp", "stl.png","图片"),
  天宫= __res:取资源动画("pic/zmp", "tg.png","图片"),
  龙宫=__res:取资源动画("pic/zmp", "lg.png","图片"),
  普陀山= __res:取资源动画("pic/zmp", "pts.png","图片"),
  凌波城= __res:取资源动画("pic/zmp", "lbc.png","图片"),
  花果山=__res:取资源动画("pic/zmp", "hgs.png","图片"),
  五庄观= __res:取资源动画("pic/zmp", "wzg.png","图片"),
}



local 模型 ={"逍遥生","剑侠客","偃无师","飞燕女","英女侠","巫蛮儿","巨魔王","虎头怪","杀破狼","狐美人","骨精灵","鬼潇潇","羽灵神","神天兵","龙太子","舞天姬","玄彩娥","桃夭夭"}

local 角色控件= 门派转换:创建控件("角色控件", 10, 45, 525, 295)
function 角色控件:初始化()
  self:创建纹理精灵(function ()
                取白色背景(0, 0, 525, 295, true):显示(0, 0)
                __res:取资源动画("pic", "rmx.png","图片"):显示(20, 30)
                local xx = 0
                local yy = 0
                for i =1,18 do
                  __res:取资源动画("pic", "fgbj.png","图片"):显示(70+xx*70, 30+yy*90)
                  xx = xx + 1
                  if xx>=6  then
                    xx = 0
                      yy = yy +1
                  end
                end
              end)

end

local 门派选择= 门派转换:创建控件("门派选择", 10, 45, 525, 295)
function 门派选择:初始化()
  self:创建纹理精灵(function()
          取白色背景(0, 0, 220, 300, true):显示(0, 0)
          取白色背景(0, 0, 300, 200, true):显示(230, 95)
          __res:取资源动画("pic", "jsdt.png","图片"):显示(230, 5)
        end
      )
      self.选择门派="未选择"
      self.模型格子= __UI模型格子:创建()
end


for i, v in pairs(门派按钮组) do
      local 临时按钮 = 门派选择:创建按钮(i, 0,0)
      function 临时按钮:初始化()
            self:创建按钮精灵(v,1)
      end
      function 临时按钮:左键弹起(x, y)
            门派选择:重置(门派选择.角色,i)
        --   __UI弹出.提示弹出:打开("以下是您要创建的#R伙伴#W,请确定进行此项操作\n角色: #G"..门派选择.角色.."  #W门派: #G" ..i, 6973, {门派=i,角色=门派选择.角色})
      end

end

function 门派选择:更新(dt)
  if self.模型格子 then
      self.模型格子:更新(dt)
  end
end

function 门派选择:显示(x,y)
  if self.模型格子 then
      self.模型格子:显示(x,y)
  end
  if self.图像 then
      self.图像:显示(x,y)
  end

end

function 门派选择:重置(角色,门派)
    
   self.图像= nil
   self.角色 = 角色
   local 模型1={模型=角色,装备={}}
   self.模型格子:清空()
   self.模型格子:置数据(模型1, "角色", 100,200)  
   self.选择门派=门派
  self.图像 =  self:创建纹理精灵(function()
    标题字体:置颜色(0,0,0,255)
    标题字体:取图像(角色):显示(380,25)
    标题字体:取图像(self.选择门派):显示(380,55)
  end,1
)

  self.门派类型 = {}

  if 角色=="飞燕女" or 角色=="英女侠" or 角色=="巫蛮儿" then
		self.门派类型={"大唐官府","方寸山","女儿村","神木林"}
	elseif 角色=="偃无师" or 角色=="逍遥生" or 角色=="剑侠客" then
		self.门派类型={"大唐官府","方寸山","化生寺","神木林"}
	elseif 角色=="狐美人" or 角色=="骨精灵" or 角色=="鬼潇潇" then
		self.门派类型={"魔王寨","阴曹地府","盘丝洞","无底洞"}
	elseif 角色=="杀破狼" or 角色=="巨魔王" or 角色=="虎头怪" then
		self.门派类型={"魔王寨","阴曹地府","狮驼岭","无底洞"}
	elseif 角色=="舞天姬" or 角色=="玄彩娥" or 角色=="桃夭夭" then
		self.门派类型={"天宫","龙宫","普陀山","凌波城"}
	elseif 角色=="羽灵神" or 角色=="神天兵" or 角色=="龙太子" then
		self.门派类型={"天宫","龙宫","五庄观","凌波城"}
	end
  for i, v in pairs(门派按钮组) do
      self[i]:置可见(false)
  end
  local xx = 0
  local yy=0
  for i=1,#self.门派类型 do
     if self[self.门派类型[i]] then
        self[self.门派类型[i]]:置可见(true)
        self[self.门派类型[i]]:置坐标(255+xx*70,115+yy*55)
        xx=xx+1
        if xx>=4 then
          xx=0
          yy=yy+1
        end
    end

  end
end


local 银两支付= 门派转换:创建控件("银两支付", 10, 45, 525, 295)
function 银两支付:初始化()
      self.背景 = __res:取资源动画("pic/zmp", "dt.png","图片")
      self:创建纹理精灵(function()
            self.背景:复制区域(0,0,35,self.背景.高度):显示(0, 0)
            self.背景:复制区域(240,0,20,self.背景.高度):平铺(130,self.背景.高度):显示(35, 0)
            self.背景:复制区域(35,0,220,self.背景.高度):显示(165, 0)
            self.背景:复制区域(240,0,20,self.背景.高度):平铺(120,self.背景.高度):显示(385, 0)
            self.背景:复制区域(self.背景.宽度-35,0,35,self.背景.高度):显示(525-35, 0)
            取白色背景(0, 0, 525, 110, true):显示(0, 175)
            任务字体:置颜色(__取颜色("紫色")):取图像("消费方式"):显示(10,180)
            标题字体:置颜色(0,0,0,255):取图像("经验:"):显示(10,210)
            标题字体:置颜色(0,0,0,255):取图像("银子:"):显示(10,240)
            
            取输入背景(0, 0, 250, 23):显示(60, 207)
            取输入背景(0, 0, 250, 23):显示(60, 237)
          end
        )



end



function 银两支付:重置(角色,门派)
    self.角色=角色
    self.门派=门派
    self.方式 = "经验银子"
    self.图像 = self:创建纹理精灵(function()
    标题字体:置颜色(0,0,0,255)
    if 角色信息.转门派 then
      标题字体:取图像("是否含免费洗点机会：否"):显示(100,185)
    else
      标题字体:取图像("是否含免费洗点机会：是"):显示(100,185)
    end
     标题字体:取图像(角色信息.种族):显示(265,23)
     标题字体:取图像(角色信息.模型):显示(265,53)
     标题字体:取图像(角色信息.门派):显示(265,81)
     标题字体:取图像(self.角色):显示(265,111)
     标题字体:取图像(self.门派):显示(265,138)
  end,1
)
  self.方式一:置可见(false)
  self.方式二:置可见(false)
  self:银两配置()
end


function 银两支付:银两配置()
  self.图像2 =  self:创建纹理精灵(function()
      if not 门派转换.方式 then
        说明字体:置颜色(__取颜色("紫色")):取图像("方式一 :"):显示(325,190)
        说明字体:置颜色(__取颜色("紫色")):取图像("方式二 :"):显示(425,190)
        self.方式一:置可见(true)
        self.方式二:置可见(true)
        if self.方式=="经验银子" then
          self.方式一:置选中(true)
        else  
            self.方式二:置选中(true)
        end
    end
    if 门派转换.方式 then
      if self.角色==角色信息.模型 then
            标题字体:置颜色(__取银子颜色(0)):取图像("0"):显示(65,210)
            标题字体:置颜色(__取银子颜色(5000000)):取图像("500万"):显示(65,240)
        else
            标题字体:置颜色(__取银子颜色(0)):取图像("0"):显示(65,210)
            标题字体:置颜色(__取银子颜色(20000000)):取图像("2000万"):显示(65,240)
        end
    else
      if self.角色==角色信息.模型 then
          if self.方式=="经验银子" then
              标题字体:置颜色(__取银子颜色(300000000)):取图像("3亿"):显示(65,210)
              标题字体:置颜色(__取银子颜色(15000000)):取图像("500万"):显示(65,240)
          else
              标题字体:置颜色(__取银子颜色(0)):取图像("0"):显示(65,210)
              标题字体:置颜色(__取银子颜色(15000000)):取图像("1500万"):显示(65,240)
          end
        else
            if self.方式=="经验银子" then
                标题字体:置颜色(__取银子颜色(600000000)):取图像("6亿"):显示(65,210)
                标题字体:置颜色(__取银子颜色(20000000)):取图像("2000万"):显示(65,240)
            else
                标题字体:置颜色(__取银子颜色(0)):取图像("0"):显示(65,210)
                标题字体:置颜色(__取银子颜色(40000000)):取图像("4000万"):显示(65,240)
            end
        end
    end
  end,1
)


end
function 银两支付:显示(x,y)
        if self.图像 then
            self.图像:显示(x,y)
        end
        if self.图像2 then
            self.图像2:显示(x,y)
        end

end


local 方式一=银两支付:创建单选按钮("方式一", 390, 185)
function 方式一:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000009))
end
function 方式一:左键按下(x, y)
  银两支付.方式="经验银子"
  银两支付:银两配置()
end

local 方式二=银两支付:创建单选按钮("方式二", 490, 185)
function 方式二:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/dd",0x00000009))
end
function 方式二:左键按下(x, y)
  银两支付.方式="银子"
  银两支付:银两配置()
end




local 重选按钮 = 门派选择:创建红色按钮("重选","重选", 430, 220,74,40)
function 重选按钮:左键按下(x, y)
      角色控件:置可见(true)
      门派选择:置可见(false)
      银两支付:置可见(false)
      银两支付:重置(角色信息.模型,角色信息.门派)
end

local 确定按钮 = 门派选择:创建红色按钮("确定","确定", 260, 220,74,40)
function 确定按钮:左键按下(x, y)
  if 门派选择.选择门派~=nil and 门派选择.选择门派~="" and 门派选择.选择门派~="未选择" then
    角色控件:置可见(false)
    门派选择:置可见(false)
    银两支付:置可见(true)
    银两支付:重置(门派选择.角色,门派选择.选择门派)
  end
end


local 转换按钮 = 银两支付:创建红色按钮("我要转换门派","确定", 340, 225,160,40)
function 转换按钮:左键按下(x, y)
  if 银两支付.门派~=nil and 银两支付.门派~="" and 银两支付.门派~="未选择"  then
      请求服务(100,{门派=银两支付.门派,角色=银两支付.角色,支付方式=银两支付.方式})
      门派转换:置可见(false)
  end
end

local 重选角色 = 银两支付:创建红色按钮("重选角色","重选角色", 410, 70,74,40)
function 重选角色:左键按下(x, y)
      角色控件:置可见(true)
      门派选择:置可见(false)
      门派选择:重置(角色信息.模型,"未选择")
      银两支付:置可见(false)
      银两支付:重置(角色信息.模型,角色信息.门派)
end

local 重选门派 = 银两支付:创建红色按钮("重选门派","重选门派", 410, 120,74,40)
function 重选门派:左键按下(x, y)
      角色控件:置可见(false)
      门派选择:置可见(true)
      门派选择:重置(门派选择.角色,"未选择")
      银两支付:置可见(false)
      银两支付:重置(角色信息.模型,角色信息.门派)
end



local xx = 0
local yy = 0
for i = 1, 18 do
    local 临时函数名 = 角色控件:创建按钮(模型[i],75+xx*70,35+yy*90)
    function 临时函数名:初始化()
       local lssj = 取头像(模型[i])
       self:创建按钮精灵(__res:取资源动画(lssj[7],lssj[2]),1)
    end
    function 临时函数名:左键按下(x, y)
         角色控件:置可见(false)
         门派选择:置可见(true)
         门派选择:重置(模型[i],"未选择")
    end
    xx = xx + 1
    if xx>=6  then
       xx = 0
       yy = yy +1
    end
end





local 关闭 = 门派转换:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  门派转换:置可见(false)
end






