local 阵型选择 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "阵型选择", 97 + abbr.py.x, 23 + abbr.py.y, 776, 484)
local zhenxing = {
  "普通",
  "天覆阵",
  "地载阵",
  "风扬阵",
  "云垂阵",
  "鸟翔阵",
  "龙飞阵",
  "虎翼阵",
  "蛇蟠阵",
  "鹰啸阵",
  "雷绝阵"
}
function 阵型选择:初始化()
  local nsf = require("SDL.图像")(774, 484)
  if nsf["渲染开始"](nsf) then
    置窗口背景("队伍列表", 0, 12, 766, 473, true)["显示"](置窗口背景("队伍列表", 0, 12, 766, 473, true), 0, 0)
    require("SDL.图像")("assets/wpal/20230/zudui.png"):显示(210, 52)
    -- 取白色背景(0, 0, 546, 406, true)["显示"](取白色背景(0, 0, 546, 406, true), 214, 57)
    -- __res["取图像"](__res, __res["取地址"](__res, "shape/ui/dw/", 1470975054))["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/ui/dw/", 1470975054)), 214, 170)
    -- 字体20["置颜色"](字体20, 255, 255, 255)
    -- for i = 1, 5 do
    --   字体20["取描边图像"](字体20, i)["显示"](字体20["取描边图像"](字体20, i), 541, 75 + (i - 1) * 78)
    -- end
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.标识 = {}
  字体16["置颜色"](字体16, 255, 255, 255)
  local man = __res["取图像"](__res, __res["取地址"](__res, "shape/ui/dw/", 239329503))
  for i = 1, 5 do
    local nsf = require("SDL.图像")(man["宽度"], man["高度"])
    if nsf["渲染开始"](nsf) then
      man["显示"](man, 0, 0)
      字体16["取描边图像"](字体16, i)["显示"](字体16["取描边图像"](字体16, i), 1, 12)
      nsf["渲染结束"](nsf)
    end
    self.标识[i] = nsf
  end
end
function 阵型选择:打开()
  self:置可见(true)
  self.选中 = nil
  self.阵型信息 = nil
  self.阵型网格["置数据"](self.阵型网格, zhenxing)
end
function 阵型选择:重置()
  self.图像 = nil
  if self.选中 and self.阵型信息 then
    local nsf = require("SDL.图像")(774, 484)
    if nsf["渲染开始"](nsf) then
      置轮廓文字(字体20,zhenxing[self.选中],"黑色","黄色",224, 80)
      置轮廓文字(字体20,self.阵型信息["说明"],"黑色","绿色",224, 113)
      -- 字体20["置颜色"](字体20, __取颜色("黄色"))
      -- 字体20["取图像"](字体20, zhenxing[self.选中])["显示"](字体20["取图像"](字体20, zhenxing[self.选中]), 224, 80)
      -- 字体20["置颜色"](字体20, 20, 255, 130)
      -- 字体20["取图像"](字体20, self.阵型信息["说明"])["显示"](字体20["取图像"](字体20, self.阵型信息["说明"]), 224, 113)
      字体20["置颜色"](字体20, __取颜色("浅黑"))
      for i = 1, 5 do
        self.标识[i]["显示"](self.标识[i], self.阵型信息[i].x + 74-59-14, self.阵型信息[i].y - 20+24+11)
        字体20["取图像"](字体20, self.阵型信息[i].tip)["显示"](字体20["取图像"](字体20, self.阵型信息[i].tip), 564, 75 + (i - 1) * 76)
      end
      -- local kz = ""
      -- for k, v in pairs(self.阵型信息["克制"]) do
      --   kz = kz .. "克制" .. k .. v .. "%\n"
      -- end
      -- 字体20["置颜色"](字体20, __取颜色("绿色"))
      -- 字体20["取图像"](字体20, kz)["显示"](字体20["取图像"](字体20, kz), 224, 170)
      -- local bkz = ""
      -- for k, v in pairs(self.阵型信息["被克制"]) do
      --   bkz = bkz .. "被克制" .. k .. v .. "%\n"
      -- end
      -- 字体20["置颜色"](字体20, __取颜色("红色"))
      -- 字体20["取图像"](字体20, bkz)["显示"](字体20["取图像"](字体20, bkz), 224, 290)
      nsf["渲染结束"](nsf)
    end
    self.图像 = nsf["到精灵"](nsf)
  end
end
local 关闭 = 阵型选择["创建我的按钮"](阵型选择, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 726, 0)
function 关闭:左键弹起(x, y, msg)
  阵型选择["置可见"](阵型选择, false)
end
local 阵型网格 = 阵型选择["创建网格"](阵型选择, "阵型网格", 15, 53, 184, 417)
function 阵型网格:初始化()
  self:创建格子(184, 55, 9, 0, 11, 1, true)
end
function 阵型网格:左键弹起(x, y, a, b, msg)
  if 阵型选择["选中"] then
    self.子控件[阵型选择["选中"]]._spr["确定"] = nil
  end
  阵型选择["选中"] = a
  self.子控件[a]._spr["确定"] = true
  阵型选择["阵型信息"] = __取阵法(zhenxing[a])
  阵型选择["重置"](阵型选择)
end
function 阵型网格:置数据(数据)
  for i = 1, #阵型网格["子控件"] do
    local lssj = __阵型格子["创建"]()
    lssj["置数据"](lssj, 数据[i])
    阵型网格["子控件"][i]["置精灵"](阵型网格["子控件"][i], lssj)
  end
end
local 选的阵型按钮 = 阵型选择["创建我的按钮"](阵型选择, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 147, 41), "选定阵型按钮", 299, 408, "选定阵型")
function 选的阵型按钮:左键弹起(x, y, msg)
  if 阵型选择["选中"] then
    发送数据(4009, {
      ["名称"] = zhenxing[阵型选择["选中"]]
    })
  end
end
