--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-31 22:57:27
Last Modified by: GGELUA
Last Modified time: 2024-10-13 01:38:16
--]]
local 经脉流派 = __UI界面.窗口层:创建我的窗口("经脉流派", 110-94 + abbr.py.x, 10 + abbr.py.y, 751+25, 510)
local 门派神器名称 = {
  大唐官府 = "轩辕剑",化生寺 = "墨魂笔",方寸山 = "黄金甲",女儿村 = "泪痕碗",天宫 = "独弦琴",
  普陀山 = "华光玉",龙宫 = "清泽谱",五庄观 = "星斗盘",魔王寨 = "明火珠",狮驼岭 = "噬魂齿",
  盘丝洞 = "昆仑镜",阴曹地府 = "四神鼎",神木林 = "月光草",凌波城 = "天罡印",无底洞 = "玲珑结",
  花果山 = "鸿蒙石",九黎城 = "魔息角"
}
local 经脉描述 = {
  大唐官府 = {"攻无不克，战无不胜","一夫当关，万夫莫开","披坚执锐，智勇双全"},
  化生寺 = {"怒目金刚，千军无畏","春满杏林，医者侠心","无量箴言，十方普照"},
  方寸山 = {"逍遥散修，载一抱素","伏魔除妖，正法论道","五气朝元，天地自在"},
  女儿村 = {"绝代妖娆，智计无双","花雨伊人，暗藏锋芒","花间倩影，一舞翩跹"},
  天宫 = {"神使执戒，镇妖压邪","踏电行雷，耀武天尊","霹雳手段，百战凌风"},
  普陀山 = {"莲台端坐，普渡众生","五行制化，咒令乾坤","落伽大士，扶危济厄"},
  龙宫 = {"碧海青天，踏浪穿波","云龙现身，呼风唤雨","潜龙在渊，脾睨沧海"},
  五庄观 = {"清风望月，羽客归心","乾坤飞剑，锋刃无形","清净守笃，修斋行道"},
  魔王寨 = {"威震山河，气慨平天","魔君盖世，独霸一方","妖王怒火，势焰滔天"},
  狮驼岭 = {"狂兽奔杀，激突猛进","狮咆鹰啸，万兽之王","炽热兽魂，无畏战狂"},
  盘丝洞 = {"迷瘴之殇，谁解情丝","多姿多情，引魂噬心","百媚婀娜，千丝笼影"},
  阴曹地府 = {"九幽阎罗，勾魂锢魄","无惧黑夜，不畏轮回","毒刹诛刑，瘴蔽幽冥"},
  神木林 ={"通天之灵，师法自然","巫神幽语，蛊魅人心","神木之侍，灵佑之恩"},
  凌波城 = {"九天神力，三界光明","诛魔驱暗，战意凌然","风云荡邪，天眼诛恶"},
  无底洞 = {"地涌金莲，华光灵照","摄魂萦魄，封心掠窍","幽冥诡巫，夺血为煞"},
  花果山 = {"圣势齐天，威慑九霄","棒搅乾坤，棍卷风云","大道通天，行者无疆"},
  九黎城 = {"古神余威，动地惊天","古神余威，动地惊天","古神余威，动地惊天"},

}
local function 法宝名称(mp,lv)
if mp == "大唐官府" then
-- if lv>=100 then
    return "干将莫邪","物理输出","物理输出","物理输出"
-- end
-- return "七杀"
elseif mp == "化生寺" then
-- if lv>=100 then
    return "慈悲","治疗回复","增益强化","法术输出"
-- end
-- return "罗汉珠"
elseif mp == "龙宫" then
-- if lv>=100 then
    return "镇海珠","法术输出","法术输出","法术输出"
-- end
-- return "分水"
elseif mp == "魔王寨" then
-- if lv>=100 then
    return "五火神焰印","法术输出","法术输出","法术输出"
-- end
-- return "赤焰"
elseif mp == "神木林" then
-- if lv>=100 then
    return "月影","法术输出","法术输出","法术输出"
-- end
-- return "神木宝鼎"
elseif mp == "方寸山" then
-- if lv>=100 then
    return "救命毫毛","封印控制","法术输出","法术输出"
-- end
-- return "天师符"
elseif mp == "女儿村" then
-- if lv>=100 then
    return "曼陀罗","封印控制","固定伤害","物理输出"
-- end
-- return "织女扇"
elseif mp == "天宫" then
-- if lv>=100 then
    return "伏魔天书","封印控制","法术输出","物理输出"
-- end
-- return "雷兽"
elseif mp == "普陀山" then
-- if lv>=100 then
    return "普渡","治疗回复","固定伤害","物理输出"
-- end
-- return "金刚杵"
elseif mp == "盘丝洞" then
-- if lv>=100 then
    return "忘情","封印控制","固定伤害","物理输出"
-- end
-- return "迷魂灯"
elseif mp == "阴曹地府" then
-- if lv>=100 then
    return "九幽","死亡禁锢","物理输出","物理输出"
-- end
-- return "摄魂"
elseif mp == "狮驼岭" then
-- if lv>=100 then
    return "失心钹","物理输出","召唤物","物理输出"
-- end
-- return "兽王令"
elseif mp == "五庄观" then
-- if lv>=100 then
    return "奇门五行令","封印控制","物理输出","物理输出"
-- end
-- return "定风珠"
elseif mp == "无底洞" then
-- if lv>=100 then
    return "金蟾","治疗回复","封印控制","召唤物"
-- end
-- return "宝烛"
elseif mp == "凌波城" then
-- if lv>=100 then
    return "斩魔","物理输出","物理输出","物理输出"
-- end
-- return "天煞"
elseif mp == "花果山" then
-- if lv>=100 then
    return "金箍棒","物理输出","物理输出","法术输出"
-- end
-- return "琉璃灯"

elseif mp == "九黎城" then
	-- if lv>=100 then
			return "铸兵锤","物理输出","物理输出","物理输出"
end
end

local function 特色法术(mp)
if mp == "大唐官府" then
  return {"横扫千军","后发制人","杀气诀","翩鸿一击"},{"连破","横扫千军","后发制人","杀气诀"},{"披坚执锐","横扫千军","后发制人","杀气诀"}
elseif mp == "化生寺" then
return {"活血","推气过宫","我佛慈悲","佛眷"},{"聚气","金刚护法","金刚护体","推气过宫"},{"唧唧歪歪","谆谆教诲","金刚护体","达摩护体"}
elseif mp == "龙宫" then
return {"龙卷雨击","龙腾","龙魂","龙魂"},{"龙卷雨击","龙腾","龙魂","龙魂"},{"龙卷雨击","龙腾","龙魂","龙魂"}
elseif mp == "魔王寨" then
return {"三昧真火","飞砂走石","牛劲","魔冥"},{"三昧真火","飞砂走石","牛劲","魔冥"},{"三昧真火","飞砂走石","牛劲","魔冥"}
elseif mp == "神木林" then
return {"风灵","落叶萧萧","荆棘舞","鞭挞"},{"风灵","蛊木迷瘴","催化","雾杀"},{"风灵","木精","风萦","疾风秋叶","古藤秘咒"}
elseif mp == "方寸山" then
return {"催眠符","凝神术","失心符","落魄符"},{"五雷咒","落雷符","悲恸","奔雷"},{"五雷正法","雷法·崩裂","雷法·震煞","雷法·坤伏","咒符"}
elseif mp == "女儿村" then
return {"似玉生香","莲步轻舞","如花解语","自矜"},{"雨落寒沙","子母神针","似玉生香"},{"葬玉焚花","满天花雨","自矜"}
elseif mp == "天宫" then
return {"错乱","镇妖","掌心雷","知己知彼"},{"雷霆万钧","天神护体","电芒"},{"风雷斩","霹雳弦惊","雷怒霆激","返璞"}
elseif mp == "普陀山" then
return {"普渡众生","自在心法","杨柳甘露"},{"紧箍咒","日光华","莲心剑意"},{"五行珠","日光耀","剑意莲心"}
elseif mp == "盘丝洞" then
return {"含情脉脉","神迷","魔音摄魂","天罗地网"},{"含情脉脉","神迷","魔音摄魂","姐妹同心"},{"千蛛噬魂","蛛丝缠绕","神迷","天罗地网"}
elseif mp == "阴曹地府" then
return {"锢魂术","尸腐毒","魂飞魄散","阎罗令"},{"锢魂术","尸腐毒","魂飞魄散","六道无量"},{"血影蚀心","百鬼噬魂","魂飞魄散","幽冥鬼眼"}
elseif mp == "狮驼岭" then
return {"变身","鹰击","连环击","象形","狮搏"},{"驯兽·幼狮","幼狮之搏","变身","狮搏"},{"狂怒","变身","鹰击","连环击","象形"}
elseif mp == "五庄观" then
return {"日月乾坤","生命之泉","炼气化神"},{"烟雨剑法","飘渺式","骤雨"},{"敲金击玉","还丹","金击式"}
elseif mp == "无底洞" then
return {"金莲","地涌金莲","燃血术","由己渡人"},{"夺魄令","煞气诀","惊魂掌","燃血术"},{"裂魂","夺命咒","追魂刺","燃血术"}
elseif mp == "凌波城" then
return {"战意","天崩地裂","翻江搅海","吞山","饮海"},{"战意","超级战意","天崩地裂","翻江搅海"},{"战意","天眼神通","天崩地裂","翻江搅海"}
elseif mp == "花果山" then
return {"如意神通","当头一棒","神针撼海","无所遁形"},{"如意神通","当头一棒","神针撼海","无所遁形"},{"如意神通","棒掀北斗","兴风作浪","无所遁形"}
elseif mp == "九黎城" then
return {"枫魂汲魄","铁风撼盾","一斧开天","铁火双扬"},{"枫影二刃","三荒尽灭","铁血生风","魔神四法"},{"枫魂汲魄","铁风撼盾","一斧开天","铁火双扬"}
end
end
local function sk(mp)
if mp == "大唐官府" then
local 技能表={
  浴血豪侠={"目空", "风刃", "扶阵", "翩鸿", "勇武", "长驱直入", "杀意", "念心", "静岳", "干将", "勇念", "神凝", "狂狷", "不惊", "傲视", "破空", "历战", "安神", "额外能力", "无敌", "浴血豪侠"},
  无双战神={"目空", "勇进", "突刺", "翩鸿", "勇武", "长驱直入", "亢强", "念心", "静岳", "干将", "勇念", "神凝", "惊天动地", "不惊", "突进", "破势", "孤勇", "熟练", "额外能力", "破军", "无双战神"},
  虎贲上将={"潜心", "笃志", "昂扬", "效法", "追戮", "烈光", "摧枯拉朽", "肃杀", "厉兵", "怒伤", "奉还", "催迫", "攻伐", "暴突", "诛伤", "破刃", "奋战", "灵能", "额外能力", "披挂上阵", "虎贲上将"}
}
return 技能表
elseif mp == "化生寺" then
local 技能表={
  杏林妙手={"销武", "止戈", "圣手", "妙手", "仁心", "化瘀", "佛显", "心韧", "归气", "天照", "舍利", "佛佑", "佛法", "佛性", "妙悟", "慈心", "虔诚", "佛缘", "额外能力", "渡劫金身", "杏林妙手"},
  护法金刚={"施他", "佛屠", "销武", "聚念", "仁心", "磅礴", "佛显", "心韧", "归气", "感念", "舍利", "无碍", "佛法", "佛性", "妙悟", "慈心", "映法", "流刚", "额外能力", "诸天看护", "护法金刚"},
  无量尊者={"诵律", "授业", "修习", "诵经", "悲悯", "解惑", "持戒", "生花", "悟彻", "抚琴", "舍利", "静气", "自在", "无量", "慧定", "金刚", "达摩", "韦陀", "额外能力", "坐禅", "无量尊者"}
}
return 技能表
elseif mp == "龙宫" then
local 技能表={
  海中蛟虬={"波涛", "破浪", "狂浪", "叱咤", "踏涛", "龙啸", "逐浪", "龙珠", "龙息", "龙慑", "傲翔", "飞龙", "骇浪", "月光", "戏珠", "汹涌", "龙魄", "斩浪", "额外能力", "亢龙归海", "海中蛟虬"},
  云龙真身={"波涛", "破浪", "云霄", "呼风", "踏涛", "清吟", "龙息", "龙珠", "唤雨", "龙慑", "傲翔", "飞龙", "戏珠", "月光", "云变", "沐雨", "龙魄", "摧意", "额外能力", "雷浪穿云", "云龙真身"},
  沧海潜龙={"傲岸", "云魂", "雨魄", "盘龙", "踏涛", "叱咤", "凛然", "龙珠", "回灵", "龙慑", "傲翔", "飞龙", "戏珠", "月光", "波涛", "龙钩", "睥睨", "惊鸿", "额外能力", "潜龙在渊", "沧海潜龙"},
}
return 技能表
elseif mp == "魔王寨" then
local 技能表={
  平天大圣={"充沛", "震怒", "激怒", "蚀天", "邪火", "赤暖", "火神", "震天", "真炎", "神焰", "崩摧", "焚尽", "咆哮", "狂月", "燃魂", "威吓", "连营", "魔心", "额外能力", "魔焰滔天", "平天大圣"},
  盖世魔君={"充沛", "震怒", "炙烤", "烈焰", "赤暖", "邪火", "火神", "震天", "折服", "焰星", "崩摧", "焰威", "咆哮", "狂月", "魔焱", "威吓", "连营", "狂劲", "额外能力", "升温", "盖世魔君"},
  风火妖王={"五蕴神焰", "烈火真言", "漫卷狂沙", "极炙", "咒言", "摧山", "不忿", "震天", "融骨", "神焰", "焦土", "不灭", "烬藏", "固基", "惊悟", "威吓", "旋阳", "魔心", "额外能力", "风火燎原", "风火妖王"},
}
return 技能表
elseif mp == "神木林" then
local 技能表={
  通天法王={"法身", "风魂", "灵佑", "追击", "咒法", "狂叶", "劲草", "冰锥", "苍埃", "神木", "月影", "薪火", "纯净", "蔓延", "破杀", "星光", "滋养", "灵归", "额外能力", "风卷残云", "通天法王"},
  巫影祭司={"风魂", "迷缚", "法身", "伏毒", "咒法", "灵木", "绞藤", "冰锥", "寄生", "神木", "月影", "薪火", "纯净", "蔓延", "破杀", "激活", "滋养", "毒萃", "额外能力", "凋零之歌", "巫影祭司"},
  灵木神侍={"风魂", "灵秀", "归原", "苍风", "咒法", "焕新", "萦风", "奉愿", "秀木", "神木", "月影", "薪火", "凉秋", "蔓延", "碾杀", "星光", "滋养", "灵精", "额外能力", "枯木逢春", "灵木神侍"},
}
return 技能表
elseif mp == "方寸山" then
local 技能表={
  拘灵散修={"雷动", "苦缠", "灵咒", "黄粱", "制约", "必果", "补缺", "不倦", "精炼", "化身", "调息", "幻变", "斗法", "吐纳", "专神", "鬼念", "灵威", "碎甲", "额外能力", "顺势而为", "拘灵散修"},
  伏魔天师={"驱雷", "策电", "雷动", "鬼怮", "穿透", "余悸", "宝诀", "妙用", "不灭", "化身", "怒霆", "批亢", "顺势", "炼魂", "吐纳", "灵能", "碎甲", "摧心", "额外能力", "钟馗论道", "伏魔天师"},
  五雷正宗={"震怒", "雷动", "天篆", "咒诀", "穿透", "符威", "宝诀", "妙用", "不灭", "雷法·翻天", "吞雷", "雷法·倒海", "顺势", "神机", "吐纳", "造化", "碎甲", "摧心", "额外能力", "五雷·挪移", "五雷正宗"},
}
return 技能表
elseif mp == "女儿村" then
local 技能表={
  绝代妖娆={"独尊", "暗伤", "重明", "倩影", "花舞", "风行", "傲娇", "花护", "空灵", "叶护", "国色", "轻霜", "抑怒", "机巧", "毒雾", "嫣然", "磐石", "倾国", "额外能力", "碎玉弄影", "绝代妖娆"},
  花雨伊人={"涂毒", "杏花", "暗伤", "淬芒", "花舞", "暗刃", "傲娇", "花护", "天香", "轻霜", "鸿影", "百花", "毒雾", "毒引", "余韵", "磐石", "飞花", "花殇", "额外能力", "鸿渐于陆", "花雨伊人"},
  花间美人={"花刺", "花骨", "汹涌", "花落", "花开", "花雨", "毒芒", "追毒", "曼珠", "清澈", "轻刃", "怒放", "驯宠", "乘胜", "痴念", "磐石", "轻霜", "毒引", "额外能力", "花谢花飞", "花间美人"},
}
return 技能表
elseif mp == "天宫" then
local 技能表={
  镇妖神使={"威吓", "疾雷", "轰鸣", "趁虚", "余韵", "缭乱", "震慑", "神念", "藏招", "苏醒", "护佑", "坚壁", "月桂", "怒火", "套索", "神律", "神尊", "洞察", "额外能力", "画地为牢", "镇妖神使"},
  踏雷天尊={"频变", "威吓", "惊曜", "震荡", "轰鸣", "驭意", "电掣", "神念", "伏魔", "雷霆汹涌", "苏醒", "天劫", "怒电", "共鸣", "灵光", "洞察", "仙音", "雷波", "额外能力", "风雷韵动", "踏雷天尊"},
  霹雳真君={"霆震", "疾雷", "激越", "存雄", "余韵", "慨叹", "电掣", "伏魔", "惊霆", "雷吞", "苏醒", "电光火石", "神采", "劲健", "啸傲", "神律", "气势", "洞察", "额外能力", "威仪九霄", "霹雳真君"},
}
return 技能表
elseif mp == "普陀山" then
local 技能表={
  莲台仙子={"推衍", "化戈", "普照", "莲花心音", "静心", "慈佑", "劳心", "普渡", "度厄", "甘露", "清净", "莲动", "法华", "灵动", "感念", "玉帛", "雨润", "道衍", "额外能力", "波澜不惊", "莲台仙子"},
  五行咒师={"庄严", "借灵", "推衍", "默诵", "静心", "莲花心音", "赐咒", "普渡", "慧眼", "无怖", "清净", "秘术", "感念", "莲心剑意", "灵动", "道衍", "缘起", "法咒", "额外能力", "五行制化", "五行咒师"},
  落伽神女={"湛然", "因缘", "莲音", "安忍", "静心", "低眉", "顿悟", "怒目", "馀威", "清净", "业障", "困兽", "无尽", "抖擞", "莲华", "相生", "智念", "执念", "额外能力", "万象", "落伽神女"},
}
return 技能表
elseif mp == "盘丝洞" then
local 技能表={
  风华舞圣={"粘附", "妖气", "怜心", "迷瘴", "鼓乐", "魔音", "玲珑", "安抚", "丹香", "迷梦", "忘川", "连绵", "情劫", "绝殇", "幻镜", "结阵", "媚态", "绝媚", "额外能力", "落花成泥", "风华舞圣"},
  迷情妖姬={"粘附", "妖气", "怜心", "迷瘴", "鼓乐", "忘忧", "玲珑", "安抚", "倾情", "连绵", "忘川", "意乱", "情劫", "魔瘴", "迷意", "结阵", "绝媚", "利刃", "额外能力", "偷龙转凤", "迷情妖姬"},
  百媚魔姝={"粘附", "杀戮", "罗网", "天网", "凌弱", "制怒", "狂击", "千蛛", "引诛", "附骨", "亡缚", "罗刹", "障眼", "连绵", "意乱", "结阵", "牵魂蛛丝", "扑袭", "额外能力", "绝命毒牙", "百媚魔姝"},
}
return 技能表
elseif mp == "阴曹地府" then
local 技能表={
  勾魂阎罗={"阎罗", "回旋", "夜行", "入骨", "聚魂", "拘魄", "索魂", "伤魂", "瘴幕", "黄泉", "幽冥", "冥视", "幽光", "泉爆", "鬼火", "魂飞", "汲魂", "扼命", "额外能力", "魍魉追魂", "勾魂阎罗"},
  六道魍魉={"阎罗", "回旋", "夜行", "聚魂", "狱火", "六道", "索魂", "伤魂", "百炼", "黄泉", "幽冥", "百爪狂杀", "咒令", "泉爆", "鬼火", "恶焰", "汲魂", "噬毒", "额外能力", "夜之王者", "六道魍魉"},
  诛刑毒师={"毒炽", "回旋", "阴翳", "聚魂", "狱火", "入魂", "毒慑", "破印", "瘴幕", "无赦咒令", "幽冥", "通瞑", "狂宴", "鬼火", "轮回", "蚀骨", "汲魂", "恶焰", "额外能力", "生杀予夺", "诛刑毒师"},
}
return 技能表
elseif mp == "狮驼岭" then
local 技能表={
  嗜血狂魔={"爪印", "迅捷", "驭兽", "化血", "宁息", "兽王", "威压", "怒象", "鹰啸", "九天", "魔息", "协战", "怒火", "狂袭", "癫狂", "死地", "乱击", "肝胆", "额外能力", "背水", "嗜血狂魔"},
  万兽之王={"拟形", "念主", "夜视", "宁息", "饮血", "健壮", "守势", "狂化", "矫健", "协同", "九天", "争宠", "羁绊", "狂袭", "钢牙", "追逐", "逞凶", "肝胆", "额外能力", "功勋", "万兽之王"},
  狂怒斗兽={"狂躁", "狂化", "狂啸", "攫取", "屏息", "不羁", "狮噬", "象踏", "长啸", "九天", "魔息", "协战", "羁绊", "狂袭", "狂血", "狂乱", "雄风", "狩猎", "额外能力", "困兽之斗", "狂怒斗兽"},
}
return 技能表
elseif mp == "五庄观" then
local 技能表={
  清心羽客={"体恤", "运转", "行气", "心浪", "养生", "蓄志", "归本", "修心", "存思", "修身", "同辉", "守中", "乾坤", "意境", "存神", "陌宝", "心随意动", "玄机", "额外能力", "清风望月", "清心羽客"},
  乾坤力士={"体恤", "锤炼", "神附", "心浪", "养生", "强击", "无极", "修心", "混元", "修身", "剑气", "雨杀", "意境", "起雨", "滂沱", "剑势", "心随意动", "致命", "额外能力", "天命剑法", "乾坤力士"},
  万寿真仙={"木摧", "道果", "饮露", "炼果", "心浪", "聚力", "无极", "修心", "混元", "刺果", "修身", "三元", "凝神", "纳气", "气盛", "剑势", "还元", "致命", "额外能力", "落土止息", "万寿真仙"},
}
return 技能表
elseif mp == "无底洞" then
local 技能表={
  妙谛金莲={"灵照", "秉幽", "护法", "涌泉", "绝处逢生", "烛照", "华光", "风墙", "血潮", "精进", "救人", "灵身", "持戒", "罗汉", "灵通", "忍辱", "暗潮", "噬魂", "额外能力", "同舟共济", "妙谛金莲"},
  摄魂迷影={"阴魅", "诡印", "萦魄", "御兽", "绝处逢生", "陷阱", "椎骨", "风墙", "血潮", "灵身", "精进", "救人", "烈煞", "持戒", "罗汉", "忍辱", "暗潮", "噬魂", "额外能力", "妖风四起", "摄魂迷影"},
  幽冥巫煞={"弥愤", "魂守", "刺骨", "余咒", "鬼袭", "羽裂", "分魄", "盛怒", "血潮", "夺血", "灵变", "深刻", "牵动", "独一", "聚魂", "纠缠", "灵身", "踏魄", "额外能力", "冥煞", "幽冥巫煞"},
}
return 技能表
elseif mp == "凌波城" then
local 技能表={
  九天武圣={"山破", "战诀", "无双", "聚气", "贯通", "魂聚", "神躯", "斩魔", "不动", "力战", "破击", "巧变", "海沸", "怒火", "煞气", "强袭", "混元", "再战", "额外能力", "天神怒斩", "九天武圣"},
  灵霄斗士={"石摧", "战诀", "天泽", "聚气", "贯通", "魂聚", "神躯", "斩魔", "不动", "妙得", "闪雷", "惊涛", "海沸", "怒火", "煞气", "乘势", "追袭", "再战", "额外能力", "真君显灵", "灵霄斗士"},
  风云战将={"山破", "战诀", "天泽", "凝息", "贯通", "魂聚", "神躯", "斩魔", "不动", "威震", "盛势", "天眼", "海沸", "怒火", "煞气", "蓄势", "杀罚", "再战", "额外能力", "耳目一新", "风云战将"},
}
return 技能表
elseif mp == "花果山" then
local 技能表={
  齐天武圣={"威仪", "逐胜", "愈勇", "斗志", "忘形", "贪天", "显圣", "火眼", "棒打雄风", "闹天", "铁骨", "填海", "伏妖", "豪胆", "压邪", "翻天", "圈养", "荡魔", "额外能力", "齐天神通", "齐天武圣"},
  斗战真神={"顽心", "逐胜", "自在", "变通", "忘形", "顽性", "显圣", "金睛", "棒打雄风", "通天", "铁骨", "威震", "伏妖", "豪胆", "压邪", "朝拜", "圈养", "荡魔", "额外能力", "战神", "斗战真神"},
  通天行者={"威仪", "闹海", "愈勇", "斗志", "忘形", "顽性", "显圣", "逞胜", "得意", "斗战", "添威", "胜意", "大圣", "冲霄", "锻炼", "朝拜", "圈养", "荡魔", "额外能力", "齐天神通", "通天行者"},
}
return 技能表
elseif mp == "九黎城" then
  local 技能表={
    铁火战魔={"枫魂","怒刃","震怒","俾睨","识破","得势","飞扬","凌人","生风","蛮横","难保","乘风","擎天","族魂","魂力","狂暴","驭魔","野蛮","额外能力","魔神之刃","铁火战魔"},
    铁火战魔={"枫魂","怒刃","震怒","俾睨","识破","得势","飞扬","凌人","生风","蛮横","难保","乘风","擎天","族魂","魂力","狂暴","驭魔","野蛮","额外能力","魔神之刃","铁火战魔"},
    铁火战魔={"枫魂","怒刃","震怒","俾睨","识破","得势","飞扬","凌人","生风","蛮横","难保","乘风","擎天","族魂","魂力","狂暴","驭魔","野蛮","额外能力","魔神之刃","铁火战魔"},

  }
  return 技能表
end
end
local function 技能树(a)
	if a == 1 or a == 4 or a == 7 or a == 10 or a == 13 or a == 16 then
		if a == 16 then
			return {20}
		else
			return {a+3,a+4}
		end
	elseif a == 2 or a == 5 or a == 8 or a == 11 or a == 14 or a == 17 then
		if a == 17 then
			return {20}
		else
			return {a+2,a+3,a+4}
		end
	elseif a == 3 or a == 6 or a == 9 or a == 12 or a == 15 or a == 18 then
		if a == 18 then
			return {20}
		else
			return {a+2,a+3}
		end
	end
end
function 经脉流派:初始化()
  local nsf = require("SDL.图像")(751+25, 510)
  local tux = require("SDL.图像")
  self.输出图片暗 = tux("assets/wpal/20230/jingmai/sc1.png")
  self.封印图片暗 = tux("assets/wpal/20230/jingmai/fy1.png")
  self.辅助图片暗 = tux("assets/wpal/20230/jingmai/fz1.png")
  self.输出图片亮 = tux("assets/wpal/20230/jingmai/sc.png")
  self.封印图片亮 = tux("assets/wpal/20230/jingmai/fy.png")
  self.辅助图片亮 = tux("assets/wpal/20230/jingmai/fz.png")
  self.红框=tux("assets/wpal/20230/jingmai/hong.png")
	self.黑框=tux("assets/wpal/20230/jingmai/hei.png")
  -- self.当前=tux("assets/wpal/20230/jingmai/124.png")
  self.已启用=tux("assets/wpal/20230/jingmai/dg.png")
  if nsf:渲染开始() then
    置窗口背景("经脉流派", 0, 12, 751+15, 496, true):显示(0, 0)
    -- 取白色背景(0, 0, 665, 395, true):显示(26, 59)
    tux("assets/wpal/20230/jingmai/123.png"):显示(12, 48)
    nsf:渲染结束()
  end
  self:置精灵(nsf:到精灵())
end

local function _取文字(wz)
  local ssdw=""
  for i=1,4 do
      local start = (i-1) * 3 + 1
      ssdw=ssdw..wz:sub(start, start + 2).."\n"
      -- print(get_chinese_char(str, i))
  end
  return ssdw
end

function 经脉流派:打开()
  if 角色信息.门派 == "无门派" then
    __UI弹出.提示框:打开("#Y加入门派方可查看")
    return
  end
  if 角色信息.奇经八脉.技能树 == nil then
    角色信息.奇经八脉.技能树 = {
      1,
      2,
      3
    }
  end
  self.显示界面=1
  for i=1,3 do
    if 角色信息.奇经八脉["当前流派"] == 角色信息.奇经八脉[i] then
      self.显示界面=i
      break
    end
  end

  self.神器 = {}
  self.神器.名称 = 门派神器名称[角色信息.门派]
  local 神器资源=取物品(self.神器.名称)

  self.法宝 = {}
  self.法宝.名称,self.定位1,self.定位2,self.定位3 = 法宝名称(角色信息.门派)
  self.输出1=nil
  self.辅助1=nil
  self.封印1=nil
  self.输出2=nil
  self.辅助2=nil
  self.封印2=nil
  self.输出3=nil
  self.辅助3=nil
  self.封印3=nil
  if self.定位1=="物理输出" or self.定位1=="法术输出" or self.定位1=="固定伤害" then
    self.输出1=true
  elseif self.定位1=="死亡禁锢" or self.定位1=="封印控制" then
    self.封印1=true
  elseif self.定位1=="治疗回复" or self.定位1=="增益强化" then
    self.辅助1=true
  end
  if self.定位2=="物理输出" or self.定位2=="法术输出" or self.定位2=="固定伤害" then
    self.输出2=true
  elseif self.定位2=="死亡禁锢" or self.定位2=="封印控制" then
    self.封印2=true
  elseif self.定位2=="治疗回复" or self.定位2=="增益强化" then
    self.辅助2=true
  end
  if self.定位3=="物理输出" or self.定位3=="法术输出" or self.定位3=="固定伤害" then
    self.输出3=true
  elseif self.定位3=="死亡禁锢" or self.定位3=="封印控制" then
    self.封印3=true
  elseif self.定位3=="治疗回复" or self.定位3=="增益强化" then
    self.辅助3=true
  end

  
  


  self.经脉表 = sk(角色信息.门派)
  self:置可见(true)
  self.经脉网格:置数据()
  self.选中 = nil
  self.法宝网格:置物品()
  self.神器网格:置物品()
  self.ts法术展示:置数据()

  self.流派1:我的单选按钮置文字(self.流派1, __res:getPNGCC(3, 1107, 906, 42, 132, true), __res:getPNGCC(3, 1107, 752, 42, 132, true),_取文字(角色信息.奇经八脉[1]))
  self.流派2:我的单选按钮置文字(self.流派2, __res:getPNGCC(3, 1107, 906, 42, 132, true), __res:getPNGCC(3, 1107, 752, 42, 132, true),_取文字(角色信息.奇经八脉[2]))
  self.流派3:我的单选按钮置文字(self.流派3, __res:getPNGCC(3, 1107, 906, 42, 132, true), __res:getPNGCC(3, 1107, 752, 42, 132, true),_取文字(角色信息.奇经八脉[3]))
  self["流派"..self.显示界面]:置选中(true)
  


end


function 经脉流派:刷新()
  local nsf = require("SDL.图像")(430, 500)
  if nsf:渲染开始() then
    字体20:置颜色(__取颜色("浅黑"))
    字体20:取图像(角色信息.奇经八脉[经脉流派.显示界面]):显示(101, 87)
    字体16:置颜色(__取颜色("浅黑"))
    字体16:取图像(经脉描述[角色信息.门派][经脉流派.显示界面]):显示(101, 114)
    字体16:取图像(经脉流派.法宝.名称):显示(50+70, 380+15+18)
    字体16:取图像(经脉流派.神器.名称):显示(50+70+193, 380+15+18)
    if 角色信息.奇经八脉["当前流派"] == 角色信息.奇经八脉[经脉流派.显示界面] then
      self.红框:显示(38+82, 64+92)
      if self["输出"..经脉流派.显示界面] then
        self.输出图片亮:显示(39, 63+10)
      elseif self["辅助"..经脉流派.显示界面] then
        self.辅助图片亮:显示(39,  63+10)
      elseif self["封印"..经脉流派.显示界面] then
        self.封印图片亮:显示(39,  63+10)
      end
      字体14:置颜色(__取颜色("浅红"))
      字体14:取图像(self["定位"..经脉流派.显示界面]):显示(38+82+2, 64+92+2) 
      经脉流派.切换按钮:置可见(false)
      self.已启用:显示(300,  473)
    else
      self.黑框:显示(38+82, 64+92)
      if self["输出"..经脉流派.显示界面] then
        self.输出图片暗:显示(39,  63+10)
      elseif self["辅助"..经脉流派.显示界面] then
        self.辅助图片暗:显示(39,  63+10)
      elseif self["封印"..经脉流派.显示界面] then
        self.封印图片暗:显示(39,  63+10)
      end
      字体14:置颜色(__取颜色("浅黑"))
      字体14:取图像(self["定位"..经脉流派.显示界面]):显示(38+82+2, 64+92+2)
      经脉流派.切换按钮:置可见(true)
    end
    nsf:渲染结束()
  end
  self.图像 = nsf:到精灵()
  for i=1,3 do
    if 角色信息.奇经八脉["当前流派"] == 角色信息.奇经八脉[i] then
      self["当前按钮"..i]:置可见(true)
    else
      self["当前按钮"..i]:置可见(false)
    end
  end
end



local 关闭 = 经脉流派:创建我的按钮(__res:getPNGCC(1, 401, 0, 46, 46), "关闭", 701+25, 0)
function 关闭:左键弹起(x, y, msg)
  经脉流派:置可见(false)
end
local 经脉网格 = 经脉流派:创建网格("经脉网格", 453, 73, 170, 376)
function 经脉网格:初始化()
  self:创建格子(57, 57, -3, 1, 7, 3)
end
function 经脉网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr then 
    经脉流派.选中 = a
    self.子控件[a]._spr:详情打开(450, 210,self.子控件[a]._spr.可选)
  --   经脉流派.选中 = a
  --   self.子控件[a]._spr:置数据(奇经八脉.经脉表[a], true)
  --   for n = 1, #角色信息.奇经八脉.技能树 do
  --     if a ~= 角色信息.奇经八脉.技能树[n] and self.子控件[角色信息.奇经八脉.技能树[n]]._spr.可选 then
  --       self.子控件[角色信息.奇经八脉.技能树[n]]._spr:置数据(经脉流派.经脉表[角色信息.奇经八脉.技能树[n]])
  --     end
  --   end
  end
end

function 经脉流派:学习经脉1()
  if 经脉流派.选中 then
    local go=false
    if 角色信息.奇经八脉.当前流派==角色信息.奇经八脉[经脉流派.显示界面] and 角色信息.奇经八脉.技能树 and type(角色信息.奇经八脉.技能树) ~= "number" then
      for n = 1, #角色信息.奇经八脉.技能树 do
        if 角色信息.奇经八脉.技能树[n]==经脉流派.选中 then
          go=true
          break
        end
      end
    end
    if go then
      -- table.print(角色信息.QYD)
      if 角色信息.QYD.可用乾元丹>0 then
          发送数据(32,{序列=self.选中})
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y/乾元丹数量不足！")
      end
    end

  end
end
function 经脉流派:学习成功()
  for i=1,3 do
		if 角色信息.奇经八脉["当前流派"] == 角色信息.奇经八脉[i] then
			self.显示界面=i
			break
		end
	end
  if self.选中 >= 20 then
    角色信息.奇经八脉[经脉流派.经脉表[角色信息.奇经八脉[经脉流派.显示界面]][19]]=1
    角色信息.奇经八脉[经脉流派.经脉表[角色信息.奇经八脉[经脉流派.显示界面]][20]]=1
    角色信息.奇经八脉[经脉流派.经脉表[角色信息.奇经八脉[经脉流派.显示界面]][21]]=1
    角色信息.奇经八脉.技能树={}
    self.经脉网格:置数据()
    return
  end
  角色信息.奇经八脉[经脉流派.经脉表[角色信息.奇经八脉[经脉流派.显示界面]][self.选中]]=1
  角色信息.奇经八脉.技能树 = 技能树(self.选中) or 1
  -- table.print(角色信息.奇经八脉.技能树)
  self.经脉网格:置数据()
end
function 经脉流派:切换流派()
  for i=1,3 do
		if 角色信息.奇经八脉["当前流派"] == 角色信息.奇经八脉[i] then
			self.显示界面=i
      self["流派"..self.显示界面]:置选中(true)
			break
		end
	end
  for i=1,3 do
    if 角色信息.奇经八脉["当前流派"] == 角色信息.奇经八脉[i] then
      self["当前按钮"..i]:置可见(true)
    else
      self["当前按钮"..i]:置可见(false)
    end
  end
  经脉流派.经脉网格:置数据()
  经脉流派.ts法术展示:置数据()
  -- 角色信息.奇经八脉.技能树 = {
  --   1,
  --   2,
  --   3
  -- }
end

function 经脉网格:置数据()
   --table.print(经脉流派.经脉表)
  -- table.print(角色信息.奇经八脉)
  local go={}
  if 角色信息.奇经八脉.当前流派==角色信息.奇经八脉[经脉流派.显示界面] and 角色信息.奇经八脉.技能树 and type(角色信息.奇经八脉.技能树) ~= "number" then
    for n = 1, #角色信息.奇经八脉.技能树 do
      go[角色信息.奇经八脉.技能树[n]]=true
    end
  end
  -- table.print(角色信息.奇经八脉.技能树)
  -- table.print(go)
  for i = 1, #self.子控件 do
    local lssj = __经脉格子:创建()
    local tiaojian=角色信息.奇经八脉.当前流派~=角色信息.奇经八脉[经脉流派.显示界面] or go[i] or 角色信息.奇经八脉[经脉流派.经脉表[角色信息.奇经八脉[经脉流派.显示界面]][i]]~=1
    lssj:置数据(角色信息.奇经八脉[经脉流派.显示界面], 经脉流派.经脉表[角色信息.奇经八脉[经脉流派.显示界面]][i],tiaojian)-- (流派,技能,灰色)
    self.子控件[i]:置精灵(lssj)
  end
  if 角色信息.奇经八脉.当前流派==角色信息.奇经八脉[经脉流派.显示界面] and 角色信息.奇经八脉.技能树 and type(角色信息.奇经八脉.技能树) ~= "number" then
    for n = 1, #角色信息.奇经八脉.技能树 do
      go[角色信息.奇经八脉.技能树[n]]=true
      self.子控件[角色信息.奇经八脉.技能树[n]]._spr.可选 = 经脉流派:取可兑换乾元丹()>=n/3
      -- print(n)
    end
  end
  
  经脉流派:刷新()
end


local 流派1 = 经脉流派["创建我的单选按钮"](经脉流派, __res:getPNGCC(3, 1107, 906, 42, 132, true), __res:getPNGCC(3, 1107, 752, 42, 132, true), "流派1", 669+35, 58, "浴\n血\n豪\n侠") 
function 流派1:左键弹起(x, y, msg)
  经脉流派.显示界面=1
  经脉流派.经脉网格:置数据()
  经脉流派.ts法术展示:置数据()
end
local 流派2 = 经脉流派["创建我的单选按钮"](经脉流派, __res:getPNGCC(3, 1107, 906, 42, 132, true), __res:getPNGCC(3, 1107, 752, 42, 132, true), "流派2", 669+35, 58+135, "无\n双\n战\n神") 
function 流派2:左键弹起(x, y, msg)
  经脉流派.显示界面=2
  经脉流派.经脉网格:置数据()
  经脉流派.ts法术展示:置数据()
end
local 流派3 = 经脉流派["创建我的单选按钮"](经脉流派, __res:getPNGCC(3, 1107, 906, 42, 132, true), __res:getPNGCC(3, 1107, 752, 42, 132, true), "流派3", 669+35, 58+135*2, "虎\n贲\n上\n将") 
function 流派3:左键弹起(x, y, msg)
  经脉流派.显示界面=3
  经脉流派.经脉网格:置数据()
  经脉流派.ts法术展示:置数据()
end
local 切换按钮 = 经脉流派["创建我的按钮"](经脉流派, __res:getPNGCC(3, 2, 507, 124, 41, true)["拉伸"](__res:getPNGCC(3, 2, 507, 124, 41, true), 66, 41), "切换按钮", 637, 401, "切换")
function 切换按钮:左键弹起(x, y, msg)
  -- if 经脉流派.显示界面==2 then
  --   if 角色信息.门派=="狮驼岭" then
  --     __UI弹出.提示框:打开("#R/该流派正在完善中…")
  --     return
  --   end
  -- elseif 经脉流派.显示界面==3 then
  --   if 角色信息.门派=="方寸山" or 角色信息.门派=="凌波城" or 角色信息.门派=="无底洞" or 角色信息.门派=="花果山" or 角色信息.门派=="天宫" or 角色信息.门派=="魔王寨" then
  --     __UI弹出.提示框:打开("#R/该流派正在完善中…")
  --     return
  --   end
  -- end
  local 事件 = function()
    发送数据(64,{新流派=角色信息.奇经八脉[经脉流派.显示界面],旧流派=角色信息.奇经八脉.当前流派})
  end
  local wb = "是否消耗#G10#W点体力与#R10#W点活力切换流派？"
  __UI界面.窗口层.文本栏.打开(__UI界面.窗口层.文本栏, wb, 285, 155, 390, 200, 事件)
end

local 当前按钮1 = 经脉流派["创建我的按钮"](经脉流派, require("SDL.图像")("assets/wpal/20230/jingmai/124.png"), "当前按钮1", 669-18+35, 53)
local 当前按钮2 = 经脉流派["创建我的按钮"](经脉流派, require("SDL.图像")("assets/wpal/20230/jingmai/124.png"), "当前按钮2", 669-18+35, 53+135)
local 当前按钮3 = 经脉流派["创建我的按钮"](经脉流派, require("SDL.图像")("assets/wpal/20230/jingmai/124.png"), "当前按钮3", 669-18+35, 53+135*2)

local 法宝网格 = 经脉流派["创建网格"](经脉流派, "法宝网格", 50, 380+15, 60, 60)
function 法宝网格:初始化()
  self:创建格子(60, 60, 0, 0, 1, 1)
end
function 法宝网格:置物品()
  for i = 1, #法宝网格.子控件 do
    local lssj = __物品格子.创建()
    lssj:置物品(经脉流派.法宝, "经脉界面")
    self.子控件[i]:置精灵(lssj)
  end
end
function 法宝网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 0, 86, w, h, "无操作", a)
  end
end

local 神器网格 = 经脉流派["创建网格"](经脉流派, "神器网格", 50+193, 380+15, 60, 60)
function 神器网格:初始化()
  self:创建格子(60, 60, 0, 0, 1, 1)
end
function 神器网格:置物品()
  for i = 1, #神器网格.子控件 do
    local lssj = __物品格子.创建()
    lssj:置物品(经脉流派.神器, "经脉界面")
    self.子控件[i]:置精灵(lssj)
  end
end
function 神器网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    local x, y = self.子控件[a]["取坐标"](self.子控件[a])
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 180, 86, w, h, "无操作", a)
  end
end

local ts法术展示 = 经脉流派["创建网格"](经脉流派, "ts法术展示", 50, 212+15, 390, 120)
function ts法术展示:初始化()
  self:创建格子(155, 55, 13, 80, 2, 2)
end
function ts法术展示:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr.数据 then
    self.子控件[a]._spr:详情打开(x, y)
  end
end
function ts法术展示:置数据()
  -- table.print(特色法术(角色信息.门派))
  local mmww1,mmww2,mmww3=特色法术(角色信息.门派)
  for i = 1, #ts法术展示.子控件 do
      local lssj = __经脉格子.创建()
      if 经脉流派.显示界面==1 then
        lssj:置数据(nil, mmww1[i], 角色信息.奇经八脉.当前流派~=角色信息.奇经八脉[经脉流派.显示界面], true, true)
      elseif 经脉流派.显示界面==2 then
        lssj:置数据(nil, mmww2[i], 角色信息.奇经八脉.当前流派~=角色信息.奇经八脉[经脉流派.显示界面], true, true)
      elseif 经脉流派.显示界面==3 then
        lssj:置数据(nil, mmww3[i], 角色信息.奇经八脉.当前流派~=角色信息.奇经八脉[经脉流派.显示界面], true, true)
      end
      self.子控件[i]:置精灵(lssj)
  end
end
function 经脉流派:取可兑换乾元丹()
	local 数量=0
	if 角色信息.等级>=69 and 角色信息.等级<89 then
		数量=1
	elseif 角色信息.等级>=89 and 角色信息.等级<109 then
		数量=2
	elseif 角色信息.等级>=109 and 角色信息.等级<129 then
		数量=3
	elseif 角色信息.等级>=129 and 角色信息.等级<155 then
		数量=5
	elseif 角色信息.等级>=155 and 角色信息.等级<159 then
		数量=5
	elseif 角色信息.等级>=159 and 角色信息.等级<164 then
		数量=6
	elseif 角色信息.等级>=164 and 角色信息.等级<168 then
		数量=7
	elseif 角色信息.等级>=168 and 角色信息.等级<171 then
		数量=8
	elseif 角色信息.等级>=171 then
		数量=9
	end
	return 数量
end

-- for i, v in ipairs({
--   {
--     name = "确定",
--     x = 454,
--     y = 460,
--     tcp = __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(88, 41),
--     font = "确定"
--   },
--   {
--     name = "取消",
--     x = 579,
--     y = 460,
--     tcp = __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(88, 41),
--     font = "取消"
--   },
--   {
--     name = "炼化乾元丹",
--     x = 65,
--     y = 460,
--     tcp = __res:getPNGCC(3, 2, 507, 124, 41, true),
--     font = "炼化乾元丹"
--   },
--   {
--     name = "遗忘经脉",
--     x = 265,
--     y = 460,
--     tcp = __res:getPNGCC(3, 2, 507, 124, 41, true),
--     font = "遗忘经脉"
--   }
-- }) do
--   local 临时函数 = 经脉流派:创建我的按钮(v.tcp, v.name, v.x, v.y, v.font)
--   function 临时函数:左键弹起(x, y)
--     if v.name == "确定" and 奇经八脉.选中 and 角色信息.QYD.剩余乾元丹 > 0 then
--       发送数据(32, {
--         序列 = 奇经八脉.选中
--       })
--       角色信息.奇经八脉[奇经八脉.经脉表[奇经八脉.选中]] = 1
--       if 奇经八脉.选中 >= 19 then
--         角色信息.奇经八脉[奇经八脉.经脉表[20]] = 1
--       end
--       角色信息.奇经八脉.技能树 = 技能树(奇经八脉.选中) or 1
--       角色信息.QYD.剩余乾元丹 = 角色信息.QYD.剩余乾元丹 - 1
--       奇经八脉.经脉网格:置数据()
--       奇经八脉.选中 = nil
--       经脉流派:刷新()
--     elseif v.name == "取消" then
--       奇经八脉.经脉网格:置数据()
--       奇经八脉.选中 = nil
--       奇经八脉.刷新()
--     elseif v.name == "炼化乾元丹" then
--       发送数据(34)
--       奇经八脉.关闭:左键弹起()
--     elseif v.name == "遗忘经脉" then
--       发送数据(50)
--       奇经八脉.关闭:左键弹起()
--     end
--   end
-- end
