
local 消息综合 = 窗口层:创建窗口("消息综合", 0, 0, 引擎.宽度, 265)
function 消息综合:初始化()
  self:创建纹理精灵(function()
          取黑透明背景(0, 0, 引擎.宽度, 265, true):显示(0, 0)
          取灰色背景(0, 0, 引擎.宽度-236, 247, true):显示(176, 9)
          说明字体:置颜色(255, 255, 255)
          说明字体:取图像("表情"):显示(16, 68)
          说明字体:取图像("颜色"):显示(71, 68)
      end
    )
    self:置宽高(引擎.宽度, 265)
    self.可初始化=true
end
function 消息综合:打开(窗口)
  self.窗口 = 窗口
  if self.是否可见 then
    self:置可见(false)
    self.窗口:置中心(0, 0)
  else
    self:置可见(true)
    self.窗口:置中心(0, 250)
    self:置坐标(10,self.窗口.高度-265)
  end
  self:切换界面(self.表情网格)
  self.点击类型 = nil
end
function 消息综合:切换界面(界面)
    self.表情网格:置可见(界面 == self.表情网格, not self.表情网格.是否实例)
    self.颜色控件:置可见(界面 == self.颜色控件, not self.颜色控件.是否实例)
end
local 表情网格 = 消息综合:创建网格("表情网格", 207, 20, 704, 215)
function 表情网格:初始化()
  self:创建格子(55, 55, 0, 0, 10, 12, true)
  for i = 1, #self.子控件 do
    self.子控件[i]["置精灵"](self.子控件[i], _tp.表情[i]:复制():置中心(-2, -2-_tp.表情[i].高度))
  end
end
function 表情网格:左键弹起(x, y, a, b, msg)
  消息综合.窗口.消息输入:插入文本("#" .. a)
end

local 颜色控件 = 消息综合:创建控件("颜色控件", 176, 9, 704, 247)
local 颜色网格 = 颜色控件:创建网格("颜色网格", 20, 47, 838, 184)
local ysb = {
  {颜色 = "红色",字符 = "#R"},
  {颜色 = "橙色",字符 = "#C"},
  {颜色 = "黄色",字符 = "#Y"},
  {颜色 = "绿色",字符 = "#G"},
  {颜色 = "青色",字符 = "#P"},
  {颜色 = "蓝色",字符 = "#B"},
  {颜色 = "紫色",字符 = "#F"},
  {颜色 = "白色",字符 = "#W"}
}
function 颜色网格:初始化()
  self:创建格子(80, 80, 12, 40, 2, 4, true)
  for i = 1, #self.子控件 do
    self.子控件[i]:置精灵(require("SDL.精灵")(0, 0, 0, 80, 80):置颜色(__取颜色(ysb[i].颜色)))
  end
end
function 颜色网格:左键弹起(x, y,a)
  if self.子控件[a]._spr then
    消息综合.窗口.消息输入:插入文本(ysb[a].字符)
  end
end
for i, v in ipairs({
  {"表情", __res:getPNGCC(4, 588, 2, 44, 46)},
  {"颜色",__res:getPNGCC(4, 766, 2, 44, 45)}
}) do
  local 临时函数 = 消息综合:创建按钮(v[1], 12+(i-1)*54, 12)
  function 临时函数:初始化()
    self:创建按钮精灵(v[2],1)
  end
 function  临时函数:左键弹起(x, y)
      if v[1] == "表情" then
          消息综合:切换界面(消息综合.表情网格)
      elseif v[1] == "颜色" then
          消息综合:切换界面(消息综合.颜色控件)
      end
  end
end
