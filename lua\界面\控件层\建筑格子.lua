local 建筑格子 = class("建筑格子")
function 建筑格子:初始化()
end
function 建筑格子:置物品(数据, lx, 背景)
  self.精灵 = nil
  local nsf = require("SDL.图像")(278, 67)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 997, 1047, 178, 64):拉伸(269, 67):显示(0, 0)
    -- __res:getPNGCC(3, 132, 506, 55, 55)["显示"](__res:getPNGCC(3, 132, 506, 55, 55), 9, 8)
    -- local lssj = 取头像(数据["模型"])
    -- __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50), 9, 9)
    -- -- 取输入背景(0, 0, 118, 23)["显示"](取输入背景(0, 0, 118, 23), 114, 38)
    -- __res:getPNGCC(4, 790, 378, 119, 25):显示(116, 36)
    字体18["置颜色"](字体18, __取颜色("黑色"))
    字体18["取图像"](字体18, 数据.名称)["显示"](字体18["取图像"](字体18, 数据.名称), 90+24, 25)
    -- table.print(数据)
    -- 字体18["取图像"](字体18, 数据["模型"])["显示"](字体18["取图像"](字体18, 数据["模型"]), 74, 9)
    -- 字体18["取图像"](字体18, "等级：" .. 数据["等级"])["显示"](字体18["取图像"](字体18, "等级：" .. 数据["等级"]), 195, 9)
    -- 字体18["取图像"](字体18, "单价")["显示"](字体18["取图像"](字体18, "单价"), 74, 40)
    -- 字体18["置颜色"](字体18, __取银子颜色(self.物品["价格"]))
    -- 字体18["取图像"](字体18, self.物品["价格"])["显示"](字体18["取图像"](字体18, self.物品["价格"]), 124, 40)
    nsf["渲染结束"](nsf)
  end
  self.精灵 = nsf["到精灵"](nsf)
end

function 建筑格子:显示(x, y)
  if self.精灵 then
    self.精灵["显示"](self.精灵, x , y )
  end
  if self.选中 then
    __主控["建筑选中"]["显示"](__主控["建筑选中"], x+1, y)
  end
  -- if self.确定 and self.格子类型 == "摊位物品" then
  --   __主控["摊位物品选中"]["显示"](__主控["摊位物品选中"], x, y)
  -- elseif self.确定 and self.格子类型 == "摊位召唤兽" then
  --   __主控["摊位召唤兽选中"]["显示"](__主控["摊位召唤兽选中"], x+1, y)
  -- end
  -- if self.格子类型 == "摊位出售物品" then
  --   if self.确定 then
  --     __主控["道具选中大"]["显示"](__主控["道具选中大"], x+1, y+2)
  --   end
  --   if self.已上架 then
  --     __主控["已上架"]["显示"](__主控["已上架"], x + 21, y + 4)
  --   end
  -- elseif self.格子类型 == "摊位出售召唤兽" then
  --   if self.确定 then
  --     __主控["摊位召唤兽出售选中"]["显示"](__主控["摊位召唤兽出售选中"], x, y)
  --     -- __主控["摊位召唤兽选中"]["显示"](__主控["摊位召唤兽选中"], x+1, y)
  --   end
  --   if self.已上架 then
  --     __主控["已上架"]["显示"](__主控["已上架"], x + 284, y)
  --   end
  -- end
end
return 建筑格子
