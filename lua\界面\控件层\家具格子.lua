--[[
LastEditTime: 2025-05-20 06:28:12
--]]


local 家具格子 = class("家具格子")
function 家具格子:初始化()
    
end


function 家具格子:置数据(名称,方向,编号)
    self.名称 = 名称
    self.方向 = 方向
    self.编号 = 编号
    self.模型=nil
    local zx = 取房屋特效(名称..方向)
    if zx.切换 and zx.资源 then
        self.模型=__res:取资源动画(zx.资源,zx.切换,"精灵")
    end
end


function 家具格子:转换方向()
        self.方向 = self.方向+1
        local zx = 取房屋特效(self.名称..self.方向)
        if zx.切换 and zx.资源 then
            self.模型=__res:取资源动画(zx.资源,zx.切换,"精灵")
        else
            self.方向 = 1
            local zx1 = 取房屋特效(self.名称..self.方向)
            self.模型=__res:取资源动画(zx1.资源,zx1.切换,"精灵")
        end
end



function 家具格子:显示(x, y)
    if self.模型 then
        self.模型:显示(x, y)
    end
end
return 家具格子
