local 属性详细={"名称","等级","气血","魔法","忠诚"}
local 属性详细1={"伤害","防御","速度","灵力","寿命"}






local 战斗召唤 = 战斗层:创建窗口("战斗召唤",0, 0, 390, 400)
function 战斗召唤:初始化()

 self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
end


function 战斗召唤:打开(数据,数量)
    self:置可见(true)
    self.召唤数据={}
    self.宝宝列表 ={}
    if 数量 then
        self.召唤数据 =table.copy(数量)
    end
    if 数据 then
      self.宝宝列表 =table.copy(数据)
    end
		self.选中 = nil
    self.技能控件:置数据()
    self.装备网格:置物品()
    self:属性显示()
    self.名称选择:置数据()
end
function 战斗召唤:属性显示()
  
  self:创建纹理精灵(function()
        置窗口背景("召唤兽详情", 0, 0, 390, 400, true):显示(0,0)
        取白色背景(0, 0, 190, 185, true):显示(5, 35)
        for i, v in ipairs(属性详细) do
            文本字体:置颜色(255,255,255,255)
            文本字体:取图像(v):显示(10, 233+(i-1)*25)
            取输入背景(0, 0, 145, 22):显示(40,230+(i-1)*25)
            if self.选中 and self.宝宝列表 and self.宝宝列表[self.选中] then
              if v=="气血" then
                  文本字体:置颜色(0,0,0,255):取图像(self.宝宝列表[self.选中].气血.."/"..self.宝宝列表[self.选中].最大气血):显示(45,233+(i-1)*25)
              elseif v =="魔法" then
                  文本字体:置颜色(0,0,0,255):取图像(self.宝宝列表[self.选中].魔法.."/"..self.宝宝列表[self.选中].最大魔法):显示(45,233+(i-1)*25)
              else
                  文本字体:置颜色(0,0,0,255):取图像(self.宝宝列表[self.选中][v]):显示(45,233+(i-1)*25)
              end
            end
        end
        for i, v in ipairs(属性详细1) do
            if v=="寿命"  then 
                文本字体:置颜色(__取颜色("黄色"))
            else
                文本字体:置颜色(255,255,255,255)
            end
            文本字体:取图像(v):显示(205, 233+(i-1)*25)
            取输入背景(0, 0, 135, 22):显示(235,230+(i-1)*25)
            if self.选中 and self.宝宝列表 and self.宝宝列表[self.选中] then
                if v=="寿命" and self.宝宝列表[self.选中].种类=="神兽" then
                    文本字体:置颜色(0,0,0,255):取图像("★永生★"):显示(240,233+(i-1)*25)
                else
                  文本字体:置颜色(0,0,0,255):取图像(self.宝宝列表[self.选中][v]):显示(240,233+(i-1)*25)
                end
                
            end
        end
  end)
  if self.选中 and self.宝宝列表  and self.宝宝列表[self.选中] then
      self.装备网格:置物品(self.宝宝列表[self.选中].装备)
      self.技能控件:置数据(self.宝宝列表[self.选中],true)
      if self.名称选择.子控件[self.选中]~=nil then
          self.名称选择:置选中(self.选中)
      end
  end 
  self.技能控件.技能:置可见(false)
  self.技能控件.内丹:置可见(false)
  self.技能控件.进阶:置可见(false)
  self.技能控件.赐福:置可见(false)
  
end







local 名称选择 = 战斗召唤:创建列表("名称选择", 10, 40, 175, 175)  
function 名称选择:初始化()
    self.行高度= 37
    self.行间距 = 3
end
local 滑块=战斗召唤:创建竖向滑块("名称滑块",185,35,10,185,true)
名称选择:绑定滑块(滑块.滑块)
--local 滑块=名称选择:创建竖向滑块("名称滑块",180,35,10,190)

function 名称选择:置数据()
      self:清空()
      for i, v in ipairs(战斗召唤.宝宝列表) do
          self:添加():创建纹理精灵(function()
                __res:取资源动画("dlzy", 0x363AAF1B,"图像"):显示(0,0)
                local lssj = 取头像(v.模型)
                __res:取资源动画(lssj[7],lssj[2],"图像"):拉伸(30, 30):显示(4,4)
                文本字体:置颜色(0,0,0,255)
                文本字体:取图像(v.名称):显示(40,4)
                文本字体:取图像(v.等级.."级"):显示(40,20)
                if 战斗召唤:取是否召唤(i) then
                    __res:取资源动画('jszy/xjiem',0x00000197,"图像"):显示(135, 2)
                end
          end)
      end
    
end


  
function 名称选择:左键弹起(x, y, i)
    if 战斗召唤.宝宝列表[i] then
      战斗召唤.选中 = i
      战斗召唤:属性显示()
    end
end



local  技能控件=战斗召唤:创建技能内丹控件("技能控件", 205, 95)


local  装备网格=战斗召唤:创建网格("装备网格", 205, 35,170,52)

function 装备网格:初始化()
    self:创建格子(51, 51, 7, 7, 1, 3)
end

function 装备网格:置物品(数据)
  for i = 1, #self.子控件 do
          local lssj = __物品格子:创建()
          lssj:置物品(nil,50,50,nil,true)
          if 数据 and 数据[i] then
              lssj:置物品(数据[i],50,50,nil,true)
          end
          self.子控件[i]:置精灵(lssj)   
    end
end

function 装备网格:获得鼠标(x,y,a)
      self.子控件[a]._spr.焦点=true
      if self.子控件[a]._spr and self.子控件[a]._spr.物品  then
              __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
      end
end
function 装备网格:失去鼠标(x,y)
      for i = 1, #self.子控件 do
          self.子控件[i]._spr.焦点=nil
      end
end
function 装备网格:左键弹起(x, y, a)
  if self.子控件[a]._spr and self.子控件[a]._spr.物品 and __手机 then
          __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
  end
end


function 战斗召唤:取是否召唤(id)
  for n = 1, #self.召唤数据 do
    if self.召唤数据[n] == id then
        return true
    end
  end
  return false
end
local 召唤按钮 = 战斗召唤:创建红色按钮("召唤", "召唤按钮", 155, 360,80,22)
function 召唤按钮:左键弹起(x, y, msg)
      if 战斗召唤.选中 and 战斗召唤.宝宝列表 and 战斗召唤.宝宝列表[战斗召唤.选中] and not 战斗召唤:取是否召唤(战斗召唤.选中) then
          界面层.战斗界面:设置召唤(战斗召唤.选中)
          窗口层.召唤兽查看:置可见(false)
      end
end

local 关闭 = 战斗召唤:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y, msg)
    战斗召唤:置可见(false)
    窗口层.召唤兽查看:置可见(false)
    界面层.战斗界面:重置()
    界面层.战斗界面:置可见(true)
end

