local 战斗预选技能格子 = class("战斗预选技能格子")
function 战斗预选技能格子:初始化()
end
-- function 战斗预选技能格子:置数据(数据, sj)
function 战斗预选技能格子:置数据(技能,灰色)
  local nsf = require("SDL.图像")(145, 57)
  local tux = require("SDL.图像")
  if 技能 then
    if nsf["渲染开始"](nsf) then
      self.数据 = 取技能(技能)
      if self.数据 then
        self.数据.名称=技能
        local wenj="shape/jn/"
            if self.数据[10] then
                wenj="shape/xinzengsucai/"
            end
        if 灰色 then
          __res:取图像(__res:取地址(wenj, self.数据[7])):到灰度():拉伸(45, 45):显示(3,3)
        else
          -- if self.数据[10] then
          --   tux("assets/shape/jingmai/jmk/"..__jing<PERSON><PERSON><PERSON>oshu[角色信息.门派].."/"..__jing<PERSON><PERSON><PERSON><PERSON>u[技能]..".jpg"):拉伸(45, 45):显示(0,0)
          -- else
          __res:取图像(__res:取地址(wenj, self.数据[7])):拉伸(45, 45):显示(3,3)
          -- end
        end
      end
      nsf["渲染结束"](nsf)
    end
    self.精灵 = nsf["到精灵"](nsf)
  end
end
function 战斗预选技能格子:详情打开(x, y,可选)
  __UI弹出["经脉详情"]["置可见"](__UI弹出["经脉详情"], true, true)
  __UI弹出["经脉详情"]["技能文本"]["清空"](__UI弹出["经脉详情"]["技能文本"])
  __UI弹出["经脉详情"]["打开"](__UI弹出["经脉详情"], self.数据, x - 240, y - 125, 280, 300, self.精灵,可选)
end
function 战斗预选技能格子:更新(dt)
end
function 战斗预选技能格子:显示(x, y)
  if self.精灵 then
   -- print(111)
    self.精灵["显示"](self.精灵, x, y)
  end
  if self.可选 then
    __主控["经脉可选"]["显示"](__主控["经脉可选"], x-3, y-3)
  end
end
return 战斗预选技能格子
