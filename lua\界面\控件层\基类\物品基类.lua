--[[
LastEditTime: 2024-04-20 22:34:49
--]]
local 物品基类 = class("物品基类")
function 物品基类:初始化()
  
  self.变身资源={
    野猪=0xEF3A830D,
    巨蛙=0x98E3377F,
    大海龟=0x3C7B89E8,
    树怪=0x4ED5C9C4,
    赌徒=0x6BE81A68,
    大蝙蝠=0x2481DFCC,
    强盗=0xD5C2566E,
    山贼=0x5F7346A8,
    海毛虫=0x3BD0B554,
    护卫=0x7003F174,
    海星=0xE709CDAD,
    章鱼=0xABB68989,
    浣熊=0xA60EB0D9,
  
    骷髅怪=0xD0BE29D3,
    羊头怪=0x8F19EF2A,
    黑熊=0xD4D2660A,
    蛤蟆精=0x8A53158C,
    狐狸精=0xDC14E699,
    花妖=0xD294444C,
    老虎=0x463F3E9B,
  
    牛妖=0x3AF799AA,
    小龙女=0xE05E7656,
    野鬼=0xB90EC617,
    狼=0x92B59426,
    虾兵=0xE89179D1,
    蟹将=0x65DBE48A,
  
      兔子怪=0xA1B8ACD0,
      蜘蛛精=0xD2C2093D,
      僵尸=0xC7B126C6,
      黑熊精=0x35BDCFC8,
    龟丞相=0x67E0006E,
      牛头=0x06971D21,
    马面=0xCA322977,
  
      古代瑞兽=0x7728C3B2,
    雷鸟人=0x4E646343,
    蝴蝶仙子=0x976975FB,
    白熊=0x7092E7F5,
    黑山老妖=0x95FDC90D,
    进阶古代瑞兽=0x7728C3B2,
    进阶雷鸟人=0x4E646343,
    进阶蝴蝶仙子=0x976975FB,
    进阶白熊=0x7092E7F5,
    进阶黑山老妖=0x95FDC90D,
  
    天兵=0x9AB7515F,
    天将=0x7E86C2A9,
    地狱战神=0xB5FE5920,
    风伯=0xDF2F3035,
    凤凰=0x2A4159F7,
      碧水夜叉=0x22B8518F,
      雨师=0xF53D7AE7,
      蚌精=0x4BAA9CBE,
      鲛人=0x0349DFA6,
    蛟龙=0xD4442C3A,
    进阶天兵=0x9AB7515F,
    进阶天将=0x7E86C2A9,
    进阶地狱战神=0xB5FE5920,
    进阶风伯=0xDF2F3035,
    进阶凤凰=0x2A4159F7,
      进阶碧水夜叉=0x22B8518F,
      进阶雨师=0xF53D7AE7,
      进阶蚌精=0x4BAA9CBE,
      进阶鲛人=0x0349DFA6,
    进阶蛟龙=0xD4442C3A,
  
    巡游天神=0xFD35D4E3,
    芙蓉仙子=0xBAFBFAA8,
    星灵仙子=0xD226E204,
    如意仙子=0x411A18C7,
    锦毛貂精=0xEABE811E,
    千年蛇魅=0x65D26225,
    百足将军=0xDC338F6E,
    犀牛将军人形=0x696AF38B,
    犀牛将军兽形=0xCABF6445,
    野猪精=0xF1127EEA,
    鼠先锋=0xDC338F6E,
    泪妖=0xA22EBC2E,
    镜妖=0x0A545E77,
    阴阳伞=0x60AC19B3,
    进阶巡游天神=0xFD35D4E3,
    进阶芙蓉仙子=0xBAFBFAA8,
    进阶星灵仙子=0xD226E204,
    进阶如意仙子=0x411A18C7,
    进阶锦毛貂精=0xEABE811E,
    进阶千年蛇魅=0x65D26225,
    进阶百足将军=0xDC338F6E,
    进阶犀牛将军人形=0x696AF38B,
    进阶犀牛将军兽形=0xCABF6445,
    进阶野猪精=0xF1127EEA,
    进阶鼠先锋=0xDC338F6E,
    进阶泪妖=0xA22EBC2E,
    进阶镜妖=0x0A545E77,
    进阶阴阳伞=0x60AC19B3,
  
    律法女娲=0x4ED64302,
    炎魔神=0xB9917494,
    画魂=0xB73FB904,
    灵符女娲=0x5726FB84,
    吸血鬼=0x9D5FA3EC,
    巴蛇=0xBE748368,
    净瓶女娲=0x7E99A5F4,
    踏云兽=0xC6F3C665,
    龙龟=0x964E7FB8,
    红萼仙子=0x2D0D5755,
    噬天虎=0x8DC23CAE,
    灵鹤=0xACEF8DB7,
    大力金刚=0x64287AE7,
    机关鸟=0x8423434C,
    鬼将=0x023AA63E,
    葫芦宝贝=0x48E257D4,
    机关人=0xEA888609,
    幽灵=0x62875401,
    幽萤娃娃=0xD576EDF9,
    雾中仙=0xA88F486E,
    夜罗刹=0xAB1EFFB3,
    连弩车=0xD123A916,
    机关兽=0x9655B4E8,
    琴仙=0x3C3F4DB7,
    金铙僧=0xA8259D75,
    进阶律法女娲=0x4ED64302,
    进阶炎魔神=0xB9917494,
    进阶画魂=0xB73FB904,
    进阶灵符女娲=0x5726FB84,
    进阶吸血鬼=0x9D5FA3EC,
    进阶巴蛇=0xBE748368,
    进阶净瓶女娲=0x7E99A5F4,
    进阶踏云兽=0xC6F3C665,
    进阶龙龟=0x964E7FB8,
    进阶红萼仙子=0x2D0D5755,
    进阶噬天虎=0x8DC23CAE,
    进阶灵鹤=0xACEF8DB7,
    进阶大力金刚=0x64287AE7,
    进阶机关鸟=0x8423434C,
    进阶鬼将=0x023AA63E,
    进阶葫芦宝贝=0x48E257D4,
    进阶机关人=0xEA888609,
    进阶幽灵=0x62875401,
    进阶幽萤娃娃=0xD576EDF9,
    进阶雾中仙=0xA88F486E,
    进阶夜罗刹=0xAB1EFFB3,
    进阶连弩车=0xD123A916,
    进阶机关兽=0x9655B4E8,
    进阶琴仙=0x3C3F4DB7,
    进阶金铙僧=0xA8259D75,
  
      混沌兽=0xA416C496,
      猫灵人形=0x9ED7CAA8,
    猫灵兽形=0x4C82A29E,
    狂豹人形=0x5579CC03,
    狂豹兽形=0xD20879BD,
    蝎子精=0x07B74E81,
    蜃气妖=0x904F2844,
    曼珠沙华=0xEA0AD25A,
    长眉灵猴=0xBE6EDEDE,
    巨力神猿=0x99FACF23,
    金身罗汉=0xB1B68D86,
    蔓藤妖花=0x5BC38FA5,
    修罗傀儡鬼=0x4D34C63C,
    修罗傀儡妖=0x5E499D7E,
    毗舍童子=0x29A568DE,
    持国巡守=0xE13AF8E0,
    真陀护法=0x021526EC,
    增长巡守=0xCCD35C11,
    灵灯侍者=0x275209D3,
    般若天女=0xC020441E,
    进阶混沌兽=0xA416C496,
      进阶猫灵人形=0x9ED7CAA8,
    进阶猫灵兽形=0x4C82A29E,
    进阶狂豹人形=0x5579CC03,
    进阶狂豹兽形=0xD20879BD,
    进阶蝎子精=0x07B74E81,
    进阶蜃气妖=0x904F2844,
    进阶曼珠沙华=0xEA0AD25A,
    进阶长眉灵猴=0xBE6EDEDE,
    进阶巨力神猿=0x99FACF23,
    进阶金身罗汉=0xB1B68D86,
    进阶蔓藤妖花=0x5BC38FA5,
    进阶修罗傀儡鬼=0x4D34C63C,
    进阶修罗傀儡妖=0x5E499D7E,
    进阶毗舍童子=0x29A568DE,
    进阶持国巡守=0xE13AF8E0,
    进阶真陀护法=0x021526EC,
    进阶增长巡守=0xCCD35C11,
    进阶灵灯侍者=0x275209D3,
    进阶般若天女=0xC020441E,
    }

  self.五行_ = {
    "金",
    "木",
    "水",
    "火",
    "土"
  }
end


 
function 物品基类:取重复名物品(wd,分类,类型,lv)
  local wds = {}
  if wd == "未激活的星石" then
    if  分类 == 1 then
      wds[1] = "jszy/wptb"
      wds[2] = 0xA2E438B8
      wds[3] = 0x457CA6AB
      elseif 分类 == 2 then
      wds[1] = "jszy/wptb"
      wds[2] = 0xF53294B4
      wds[3] = 0xF91B9A26
      elseif 分类 == 3 then
      wds[1] = "jszy/wptb"
      wds[2] = 0x0C940FE8
      wds[3] = 0x9A9DB0F8
      elseif 分类 == 4 then
      wds[1] = "jszy/wptb"
      wds[2] = 0x6DC88994
      wds[3] = 0x155DBB4A
      elseif 分类 == 5 then
      wds[1] = "jszy/wptb"
      wds[2] = 0xA61A5027
      wds[3] = 0x3B34F66E
      elseif 分类 == 6 then ----鞋子没有
      wds[1] = "jszy/wptb"
      wds[2] = 0xDE2C33B7
      wds[3] = 0x40DAD77D
    end
  elseif wd == "灵犀玉" then
      if  分类 == 1 then
        wds[1] = "jszy/xjjm"
        wds[2] = 0x01AC0027
        wds[3] = 0x01AC0028
      elseif 分类 == 2 then
        wds[1] = "jszy/xjjm"
        wds[2] = 0x01AC0029
        wds[3] = 0x01AC0030
      elseif 分类 == 3 then
        wds[1] = "jszy/xjjm"
        wds[2] = 0x01AC0031
        wds[3] = 0x01AC0032
      end
   elseif wd=="精魄灵石" then
       if 分类 == 1 then--红
      wds[2] = 0xFFED8430
        wds[3] = 0xFFED8428
      wds[1] = "jszy/wptb"
    elseif 分类 == 2 then--黄
        wds[2] = 0xFFED8426
      wds[3] = 0xFFED8429
      wds[1] = "jszy/wptb"
    elseif 分类 == 3 then--蓝
      wds[2] = 0xFFED8425
      wds[3] = 0xFFED8427
        wds[1] = "jszy/wptb"
    end
  elseif wd == "云荒" and 类型==1 then
  wds[1] = "jszy/wptb"
  wds[2] = 1639797654
  wds[3] = 848108449
  wds[4] = 0X4EE48F77
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "云荒" and 类型==2 then
  wds[1] = "jszy/wptb"
  wds[2] = 1018740492
  wds[3] = 4203095380
  wds[4] = 0XE633635B
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "暮霭" and 类型==1 then
  wds[1] = "jszy/wptb"
  wds[2] = 1065389537
  wds[3] = 1677468959
  wds[4] = 0x3D302BBC
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "暮霭" and 类型==2 then
  wds[1] = "jszy/wptb"
  wds[2] = 2383577972
  wds[3] = 879606821
  wds[4] = 0x90D8195A
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "落日" and 类型==1 then
  wds[1] = "jszy/wptb"
  wds[2] = 3920559191
  wds[3] = 1090705523
  wds[4] = 0x8B739012
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "落日" and 类型==2 then
  wds[1] = "jszy/wptb"
  wds[2] = 2963087605
  wds[3] = 1043228755
  wds[4] = 0xD35327B8
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "晓天" and 类型==1 then
  wds[1] = "jszy/wptb"
  wds[2] = 3678698195
  wds[3] = 1056757354
  wds[4] = 0x1544B91F
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "晓天" and 类型==2 then
  wds[1] = "jszy/wptb"
  wds[2] = 2032429139
  wds[3] = 1124603941
  wds[4] = 0x03313AA0
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "林海" and 类型==1 then
  wds[1] = "jszy/wptb"
  wds[2] = 1476113795
  wds[3] = 4117653658
  wds[4] = 0xCFCA19E0
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "林海" and 类型==2 then
  wds[1] = "jszy/wptb"
  wds[2] = 810832091
  wds[3] = 3075778314
  wds[4] = 0xB43AF865
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "流霞" and 类型==1 then
  wds[1] = "jszy/wptb"
  wds[2] = 231944287
  wds[3] = 424822088
  wds[4] = 0xF37C270D
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  elseif wd == "流霞" and 类型==2 then
  wds[1] = "jszy/wptb"
  wds[2] = 2961927632
  wds[3] = 1735590777
  wds[4] = 0xE2C2C735
  wds[5] = "jszy/wptb"
  wds[6] = {25,25}
  
  
  end
      return wds
  end
function 物品基类:取数据(data)
  if  data.名称 == "怪物卡片" then
      self:置对象(data.名称,data.造型)
  else
      self:置对象(data.名称)
  end
 
  for n, v in pairs(data) do
      self.物品[n] = v
  end

    local 返回值 = self:取重复名物品(self.物品.名称,self.物品.子类,self.物品.类型,self.物品.等级)
    
  if 返回值[5] or 返回值[1] or 返回值[6]   then
      self.物品.资源 = 返回值[5] or 返回值[1]
      self.物品.小模型资源 = 返回值[2]
      if 返回值[5] then
           self.物品.url = 返回值[1]
           self.物品.小模型资源 = 返回值[4]
      end
      self.物品.大模型资源 = 返回值[3]
      self.物品.大动画 =返回值[3]
      if 返回值[6] then
        self.物品.pyz = 返回值[6]
      end
    end
  if data.名称 == "怪物卡片" then
    if 1 == data.等级 then
      self.物品.小模型资源=0x4A028BEE
    elseif data.等级==2 then
      self.物品.小模型资源=0xBB35E1EE
    elseif data.等级==3 then
      self.物品.小模型资源=0xE7FC64D2
    elseif data.等级==4 then
      self.物品.小模型资源=0xA00740F6
    elseif data.等级==5 then
      self.物品.小模型资源=0x21838782
    elseif data.等级==6 then
      self.物品.小模型资源=0xF1C84EB5
    elseif data.等级==7 then
      self.物品.小模型资源=0xEFA4BA2C
    elseif data.等级==8 then
      self.物品.小模型资源=0x2E030271
      elseif data.等级==9 then
      self.物品.小模型资源=0x2E030271
    end
    self.物品.大模型资源 = self.变身资源[data.造型]
    self.物品.大模型 = self.变身资源[data.造型]
    self.物品.资源="jszy/wptb"
  end
  if data.价格 then
    self.物品.价格 = data.价格
  end
end






function 物品基类:置对象(名称, 打造, 总类)
    self.物品 = {}
    local 道具 = 取物品(名称)
    self.物品.介绍 = 道具[1]
    self.物品.总类 = 道具[2]
    self.物品.分类 = 道具[3]
    self.物品.子类 = 道具[4]
    self.物品.名称 = 名称
    if self.物品.总类==2 then
        self.物品.级别限制 = 0
        if  道具[7] then
          self.物品.角色限制 = 道具[7]
        end
        if 道具[6] then
          self.物品.性别限制 = 道具[6]
        end
        if 道具[5] then
          self.物品.级别限制 = 道具[5]
        end
    elseif self.物品.总类==1 then
      self.物品.阶品 = 道具[8]
    end
    if 道具[14] then
      self.物品.价格 = 道具[14]
    end
    if 道具[9] then
      self.物品.气血 = 道具[9]
    end
    if 道具[10] then
      self.物品.魔法 = 道具[10]
    end
    self.物品.资源 = 道具[11]
    self.物品.小模型资源 = 道具[12]
    self.物品.大模型资源 = 道具[13]
    self.物品.大动画 = 道具[13]

  if self.物品.总类==2 then
      if not self.物品.级别限制 == nil then
          self.物品.级别限制 = 0
      end
      local lv = self.物品.级别限制
      if self.物品.分类 == 3 then
        self.物品.伤害 = math.ceil((lv/10)*30+10)
        self.物品.命中 = math.ceil((lv/10)*35+10+2)
      elseif self.物品.分类 == 4 then
        self.物品.防御 = math.ceil((lv/10)*15+10)
      elseif self.物品.分类 == 1 then
        self.物品.防御 = math.ceil((lv/10)*5+5)
        self.物品.魔法 = math.ceil((lv/10)*10+5+2)
      elseif self.物品.分类 == 5 then
        self.物品.防御 = math.ceil((lv/10)*5+5)
        self.物品.气血 = math.ceil((lv/10)*20+10)
      elseif self.物品.分类 == 2 then
        self.物品.灵力 = math.ceil((lv/10)*12+5)
      elseif self.物品.分类 == 6 then
        self.物品.防御 = math.ceil((lv/10)*5+5)
        self.物品.敏捷 = math.ceil((lv/10)*3+5)
      elseif self.物品.分类 == 10 then
        self.物品.气血 = math.ceil((lv/5)*15+20)
      elseif self.物品.分类 == 11 then
        self.物品.魔法 = math.ceil((lv/5)*5+40)
      elseif self.物品.分类 == 12 then
        self.物品.伤害 = math.ceil((lv/5)*20+20)
      elseif self.物品.分类 == 13 then
        self.物品.防御 = math.ceil((lv/5)*3+10)
      elseif self.物品.分类 == 14 then
        self.物品.敏捷 = math.ceil((lv/5)*2+10)
      end
      self.物品.鉴定 = true
      self.物品.五行 = self.五行_[math.random(1, 5)]
    if self.物品.分类 == 3 then
      if self.物品.级别限制 >= 0 and self.物品.级别限制 < 10 then
            self.物品.价格 = 500
      elseif self.物品.级别限制 >= 10 and self.物品.级别限制 < 20 then
            self.物品.价格 = 3000
      elseif self.物品.级别限制 >= 20 and self.物品.级别限制 < 30 then
            self.物品.价格 = 6000
      elseif self.物品.级别限制 >= 30 and self.物品.级别限制 < 40 then
            self.物品.价格 = 10000
      else
            self.物品.价格 = math.floor(self.物品.级别限制 * 3500 / 5)
      end
    elseif self.物品.级别限制 >= 0 and self.物品.级别限制 < 10 then
        self.物品.价格 = 500
    elseif self.物品.级别限制 >= 10 and self.物品.级别限制 < 20 then
        self.物品.价格 = 1000
    elseif self.物品.级别限制 >= 20 and self.物品.级别限制< 30 then
        self.物品.价格 = 2500
    elseif self.物品.级别限制 >= 30 and self.物品.级别限制 < 40 then
        self.物品.价格 = 5000
    else
        self.物品.价格 = math.floor(self.物品.级别限制 * 2500 / 5)
    end
    self.物品.开运孔数 ={当前=0,上限=0}
    if self.物品.级别限制 <= 40 then
        self.物品.开运孔数 = {当前=0,上限=2}
    elseif self.物品.级别限制 > 40 and self.物品.级别限制 <= 80 then
        self.物品.开运孔数 = {当前=0,上限=3}
    elseif self.物品.级别限制 > 80 and self.物品.级别限制 <= 120 then
        self.物品.开运孔数 = {当前=0,上限=4}
    elseif self.物品.级别限制 > 120 and self.物品.级别限制 <= 160 then
        self.物品.开运孔数 = {当前=0,上限=5}
    elseif self.物品.级别限制 > 160 and self.物品.级别限制 <= 180 then
        self.物品.开运孔数 = {当前=0,上限=6}
    end
		self.符石={}
		self.星位={}
		self.符石组合 = {符石组合 = {},门派条件 ={},部位条件={},效果说明={}}
		self.熔炼效果={}
  elseif self.物品.总类==3 and self.物品.分类==1 then
      self.物品.附带技能 = 打造

  elseif self.物品.总类==1 then
    if self.物品.阶品 ~= 3 or self.物品.分类 ~= 11 then
			  self.物品.可叠加 = true
		else
		    self.物品.可叠加 = false
		end
  elseif self.物品.总类==4  then
    self.物品.可叠加 = true
  elseif self.物品.总类==5 then
        self.物品.子类 = 打造
        if 总类 then
          self.物品.特效 = 总类
        end
        self.物品.可叠加 = false
        if self.物品.分类==4 then
          self.物品.子类 = 道具[4]
          self.物品.特效 = 道具[5]
          self.物品.可叠加 = true
        elseif self.物品.分类==6  then
          self.物品.子类 = 道具[4]
          self.物品.角色限制 = 道具[8]
          self.物品.级别限制 = 打造 or 1
          self.物品.特效 = 道具[9]
          if 道具[5] then
            self.物品.级别限制 = 道具[5]
          end
        end
	elseif self.物品.总类 == 6 then
		  self.物品.可叠加 = true
  elseif self.物品.总类 == 7 then
    if self.物品.分类==2  then
        self.物品.可叠加 = true
    else
        self.物品.可叠加 = false
    end
  elseif self.物品.总类 ==9  then
        self.物品.可叠加 = true
  elseif self.物品.总类 ==10  then
    self.物品.可叠加 = true
  elseif self.物品.总类 ==11 then
    self.物品.可叠加 = false
  elseif self.物品.总类 ==12  then
    self.物品.子类 = 打造
    self.物品.可叠加 = false
  elseif self.物品.总类 ==13 then
    self.物品.子类 = 打造
    self.物品.可叠加 = false
	elseif self.总类 == 100 then
		self.物品.可叠加 = true
	elseif self.物品.总类 == 20 or self.物品.总类 == 25 or self.物品.总类 == 8 then
		self.物品.可叠加 = true
	elseif self.物品.总类 == 1000 then
      self.物品.分类 = 道具[3]
      self.物品.使用 = 道具[5]
      self.物品.特技 = 道具[6]
      self.物品.气血 = 0
      self.物品.魔法 = 2000
      self.物品.角色限制 = 道具[7] or 0
      self.物品.五行 = self.五行_[math.random(1, 5)]
      self.物品.伤害 = 道具[8] or 0
  elseif self.物品.总类 == 1005 then  ----灵宝
      self.物品.使用 = 道具[5]
      self.物品.魔法 = 道具[3] * 50
      self.物品.特技 = 道具[6]
      self.物品.效果 = 道具[15]
      self.物品.气血 = 0
  elseif self.物品.总类 == 21 then
    self.物品.特效 = 道具[8]
    if self.物品.分类==3 then
      self.物品.可叠加 = false
    else
      self.物品.可叠加 = true
    end
  elseif self.物品.总类 == 30  then
    self.物品.角色限制, self.物品.资源, self.物品.小模型资源, self.物品.大模型资源, self.物品.特技, self.物品.特效, self.物品.魔法, self.物品.气血, self.物品.伤害, self.物品.小模型id = __变身卡(打造)
  end
end








return 物品基类
