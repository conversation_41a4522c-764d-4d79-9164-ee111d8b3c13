--[[
LastEditTime: 2024-10-19 01:24:11
--]]
local 合成灵犀玉 = 窗口层:创建窗口("合成灵犀玉",0,0,650,560)


local 按钮坐标 = {{180,300},{178,237},{218,185},{274,140},{341,140},{393,183},{437,237},{437,300},{341,395},{274,395}}
local 格子坐标 = {{175,290},{173,227},{212,175},{264,130},{336,130},{383,176},{427,230},{425,293},{333,387},{269,385}}
local 时辰 = {"申","酉","戌","亥","子","丑","寅","卯","辰","巳","午","未"}
local 时辰坐标 = {子 = {344,47},丑 = {442,102}, 寅 = {497,199},卯 = {494,311},辰 = {445,404},巳 = {350,460},午 = {238,460},未 = {133,403},申 = {82,316},酉 = {79,206},戌 = {134,104},亥 = {233,46},}
local 时辰背景 = {子 = 0xEB658CF1,丑 = 0xA59A5675,寅 = 0xF56CEDD2,卯 = 0x259869FB,辰 = 0xCF7FE283,巳 = 0xC0EC5E5F,午 = 0x87B01ACC,未 = 0xCBB1F1C2,申 = 0xA72202ED,酉 = 0x3164459D,戌 = 0x775AB575,亥 = 0xAE8DDDA1}
local 时辰光效 = {子 = 0x1F9A4A9A,丑 = 0xCC1BEF97,寅 = 0x2F2889A6, 卯 = 0xE778A2FA,辰 = 0x19888E8E,巳 = 0x5CA04E2E,午 = 0x8AB16B84,未 = 0x90E84362,申 = 0x83534900,酉 = 0x97AE47C3,戌 = 0x3419BF6E,亥 = 0x26120EBB}



function 合成灵犀玉:初始化()
          self:创建纹理精灵(function ()
            __res:取资源动画("pic/sqsc","hcbj.png","图片"):显示(0,0)
            __res:取资源动画("pic/sqsc","tbwzbj.png","图片"):显示(190,0)
            __res:取资源动画("jszy/xjjm",0X4E6476BE,"图像"):显示(260,5)
            -- __res:取资源动画("pic/sqsc","sqzbbj.png","图片"):显示(20,80)
          end)
          self.背景动画=__res:取资源动画("jszy/xjjm",0xC057E026,"动画") 
          self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
          self.可初始化=true
          self.当前数量=0
          self.加入数量=0
          self.时辰高亮 = {}
          self.物品 = {}
          for i = 1, 10 do
              self.物品[i]=__物品格子.创建()
              self.物品[i]:置物品()
          end
          self.背景=self:创建纹理精灵(function ()
                  __res:取资源动画("jszy/xjjm",0XC4D34CA9,"图像"):显示(101,64)
                  __res:取资源动画("jszy/xjjm",0X060482C3,"图像"):显示(125,90)
                  __res:取资源动画("jszy/xjjm",0X36758F6F,"图像"):显示(163,123)
                  __res:取资源动画("jszy/xjjm",0X4902C991,"图像"):显示(175,150)
                  __res:取资源动画("jszy/xjjm",0XDD1D0997,"图像"):显示(262,226)
                  __res:取资源动画("jszy/xjjm",0XF5BCEA0B,"图像"):显示(194,343)
                  __res:取资源动画("jszy/xjjm",0X5429517B,"图像"):显示(385,343)
                  __res:取资源动画("jszy/xjjm",0X01AC0007,"图像"):显示(312,192)
                  __res:取资源动画("jszy/xjjm",0X01AC0007,"图像"):显示(312,113)
                  __res:取资源动画("jszy/xjjm",0X01AC0007,"图像"):显示(312,435)
                  __res:取资源动画("jszy/xjjm",0X01AC0007,"图像"):显示(152,277)
                  __res:取资源动画("jszy/xjjm",0X01AC0007,"图像"):显示(474,277)
                  __res:取资源动画("jszy/xjjm",0X01AC0010,"图像"):显示(160,284)
                  __res:取资源动画("jszy/xjjm",0X01AC0011,"图像"):显示(482,284)
                  __res:取资源动画("jszy/xjjm",0X01AC0012,"图像"):显示(319,120)
                  __res:取资源动画("jszy/xjjm",0X01AC0013,"图像"):显示(320,442)
                  __res:取资源动画("jszy/xjjm",0X01AC0014,"图像"):显示(320,198)
                  --------------------------------------------------------------
                  for i, v in ipairs(时辰) do
                     __res:取资源动画("jszy/xjjm",时辰背景[v],"图像"):显示(时辰坐标[v][1],时辰坐标[v][2])
                     self.时辰高亮[i]=__res:取资源动画("jszy/xjjm",时辰光效[v],"精灵")
                  end
                   


            end,1)
end

function 合成灵犀玉:更新(dt)
  self.背景动画:更新(dt)
end



function 合成灵犀玉:显示(x,y)
        self.背景动画:显示(x-90,y-70)
        self.背景:显示(x,y)
        for i = 1, 10 do
            if self.物品[i] and  self.物品[i].物品 then
                if i==9 then
                    self.时辰高亮[9]:显示(x+时辰坐标[时辰[9]][1],y+时辰坐标[时辰[9]][2])
                    self.时辰高亮[10]:显示(x+时辰坐标[时辰[10]][1],y+时辰坐标[时辰[10]][2])
                elseif i==10 then
                    self.时辰高亮[11]:显示(x+时辰坐标[时辰[11]][1],y+时辰坐标[时辰[11]][2])
                    self.时辰高亮[12]:显示(x+时辰坐标[时辰[12]][1],y+时辰坐标[时辰[12]][2])
                else
                    self.时辰高亮[i]:显示(x+时辰坐标[时辰[i]][1],y+时辰坐标[时辰[i]][2])
                end
            end
        end
        if self.物品数量 then
            self.物品数量:显示(x+303+(55-self.物品数量.宽度)//2,y+275)
        end
end







function 合成灵犀玉:打开(数据)
    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
    self.当前数量=0
    self:刷新(数据)
  
end


function 合成灵犀玉:刷新(数据)

      self.当前数量=数据
      self.加入数量=0
      for i=1,10 do
          self.物品[i]:置物品()
      end
      self.物品数量=文本字体:置颜色(0,0,0):取精灵(self.当前数量)



end

function 合成灵犀玉:合成开始(数据)
      self.当前数量=数据.剩余灵犀玉
      self.加入数量=0
      self.物品数量=文本字体:置颜色(0,0,0):取精灵(self.当前数量)
      for i=1,10 do
          self.物品[i]:置物品()
      end
      if 数据.是否成功 then
          self.物品控件.动画效果=__res:取资源动画("jszy/xjjm",0x01AC0034,"置动画") 
          self.物品控件.动画效果:置提速(2)
      else
          self.物品控件.动画效果=__res:取资源动画("jszy/xjjm",0x01AC0033,"置动画") 
          self.物品控件.动画效果:置提速(2)
      end
   

end


for i, v in ipairs(按钮坐标) do
    local 临时按钮 =合成灵犀玉:创建按钮("加号"..i, v[1], v[2])
    function 临时按钮:初始化()
        self:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x16C7CDDB),nil,nil,40,40)
    end
    function 临时按钮:左键弹起(x, y)
          if not 合成灵犀玉.物品[i] or not 合成灵犀玉.物品[i].物品 then
            if 合成灵犀玉.当前数量>0 then
                合成灵犀玉.当前数量=合成灵犀玉.当前数量-1
                合成灵犀玉.加入数量=合成灵犀玉.加入数量+1
                合成灵犀玉.物品[i]:置物品("灵犀之屑",50,50)
                合成灵犀玉.物品数量=文本字体:置颜色(0,0,0):取精灵(合成灵犀玉.当前数量)
            else
                __UI弹出.提示框:打开("#Y你的灵犀之屑数量不足")

            end
          else
                if __手机 then
                    __UI弹出.道具提示:打开(合成灵犀玉.物品[i].物品,x+20,y+20,临时按钮,"卸下",i)
                else
                      self:卸下(i)
                end
          end
    end
    function 临时按钮:获得鼠标(x, y)
        if 合成灵犀玉.物品[i] and  合成灵犀玉.物品[i].物品 then
              __UI弹出.道具提示:打开(合成灵犀玉.物品[i].物品,x+20,y+20)
        end
    end

    function 临时按钮:卸下(编号)
              if 编号 and 编号~=0 then
                  if 合成灵犀玉.加入数量>0 then
                        合成灵犀玉.物品[编号]:置物品()
                        合成灵犀玉.当前数量=合成灵犀玉.当前数量+1
                        合成灵犀玉.加入数量=合成灵犀玉.加入数量-1
                        合成灵犀玉.物品数量=文本字体:置颜色(0,0,0):取精灵(合成灵犀玉.当前数量)
                  end
              end
    end




end


local 合成按钮 =合成灵犀玉:创建按钮("合成按钮", 270,307)
function 合成按钮:初始化()
    self:创建按钮精灵(__res:取资源动画('jszy/xjjm',0x2E78E809))
end


function 合成按钮:左键弹起(x, y)
    if 合成灵犀玉.加入数量>0 then
         请求服务(3773,{数量=合成灵犀玉.加入数量})
    else
      __UI弹出.提示框:打开("#Y请放入灵犀之屑")
    end
end




local 物品控件=合成灵犀玉:创建控件("物品控件",0,0,650,560)
function 物品控件:更新(dt)
      if self.动画效果 then
          self.动画效果:更新(dt)
          if not self.动画效果:是否播放() or self.动画效果:取当前帧()>= self.动画效果:取帧数()   then
              self.动画效果=nil
          end
      end
end

function 物品控件:显示(x,y)

        for i=1,10 do
          if 合成灵犀玉.物品[i] and 合成灵犀玉.物品[i].物品 then
            合成灵犀玉.物品[i]:显示(x+格子坐标[i][1],y+格子坐标[i][2])
          end
        end
        if self.动画效果 then
            self.动画效果:显示(x,y)
        end



end


local 关闭 = 合成灵犀玉:创建按钮( "关闭", 605,2) 
function 关闭:初始化()
  self:创建按钮精灵(__res:取资源动画("jszy/xjjm",0x20FD5715),1)
end
function 关闭:左键弹起(x, y)
  合成灵犀玉:置可见(false)
end







  


















