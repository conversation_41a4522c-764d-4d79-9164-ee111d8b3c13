local 一键回收 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "一键回收", 80+ abbr.py.x,  abbr.py.y, 850, 496)
function 一键回收:初始化()
  local nsf = require("SDL.图像")(850, 496)
  if nsf["渲染开始"](nsf) then
    置窗口背景("物品回收", 0, 12, 825, 500, true)["显示"](置窗口背景("物品回收", 0, 12, 825, 500, true), 0, 0)
    取白色背景(0, 0, 785, 400, true)["显示"](取白色背景(0, 0, 785, 400, true), 17, 45)
    字体18["置颜色"](字体18, __取颜色("浅黑"))

    local wpmc={
      "强化石","环装","高级内丹","锻造图策",
      "1级符石","低兽决","五宝","锻造图策115",
      "2级符石","高魔兽","炼妖石75","锻造图策125",
      "3级符石","金柳露","炼妖石85","锻造图策135",
      "月华露","超级金柳露","炼妖石95","锻造图策145",
      "如意丹","净瓶玉露","炼妖石105","附魔宝珠",
      "易经丹","超净瓶玉露","炼妖石115","元宵",
      "玉葫灵髓","玄天残卷","炼妖石125","珍珠",
      "宝石","暗器","炼妖石135","符石卷轴",----36
      "星辉石","彩果","炼妖石145","藏宝图",---40
      "环装<=60","炼妖石65","制造书","天赋符",---44
      "法宝材料","百炼精铁","灵饰制造书","法宝",---48
      "本命秘籍","内丹","变身卡","元灵晶石"---52
        }
        local wpjg={
          "1W","<120级8W","50W","<105级3W",
          "3W","3W","3W","3W",
          "10W","50W","2W","3W",
          "30W","1W","2W","3W",
          "1W","5W","2W","3W",
          "5W","3W","2W","5W",
          "10W","5W","2W","5W",
          "10W","1W","2W","非160=5W",
          "1W*等级","2W","2W","2W",
          "1.5W*等级","1W","2W","1W",
          "1W","1W","5W","2W",
          "1W","5W","5W","20W*等级",
          "5W","8W","1W","5W"
            }
            local xx=0 
            local yy = 0
        for i = 1,52 do 
            字体18["取图像"](字体18, ""..wpmc[i].."("..wpjg[i]..")")["显示"](字体18["取图像"](字体18, ""..wpmc[i].."("..wpjg[i]..")"), 20+xx*190, 50+yy*30)
            xx=xx+1 
            if xx== 4 then 
                xx=0 
                yy=yy+1 
            end 
        end 
        字体16["置颜色"](字体16, __取颜色("黄色"))
        字体16["取图像"](字体16, "请勾选你要出售的物品。")["显示"](字体16["取图像"](字体16, "请勾选你要出售的物品。"), 20, 450)
        字体16["取图像"](字体16, "开启自动回收权限后,战斗结束背包格子<5个时,将自动进行回收")["显示"](字体16["取图像"](字体16, "开启自动回收权限后,战斗结束背包格子<5个时,将自动进行回收"), 20, 478)
        字体16["置颜色"](字体16, __取颜色("浅黑"))
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
end


local xx=0 
local yy = 0
for i = 1, 52 do
    local 临时函数 = 一键回收["创建我的多选按钮"](一键回收, __res:getPNGCC(2, 1172, 107, 26, 26, true), __res:getPNGCC(2, 1171, 75, 26, 26, true), i .. "回收按钮", 155+xx*200,  50+yy*30)
   function  临时函数:左键弹起(x, y)
    发送数据(179,{数据序列=11,参数=i})
    end
    xx=xx+1 
    if xx== 4 then 
        xx=0 
        yy=yy+1 
    end 
end


local data 
function 一键回收:打开(数据)
  self:置可见(true)
  data= 数据
 self:重置()
end
function 一键回收:刷新数据(数据)
    data= 数据
   self:重置()
end 


function 一键回收:重置(数据)
    for i=1,52 do 
        if data[i]== true then 
          一键回收[i.."回收按钮"]["置选中"](一键回收[i.."回收按钮"], true)
        else 
          一键回收[i.."回收按钮"]["置选中"](一键回收[i.."回收按钮"], false)
        end 
    end
end

local 关闭 = 一键回收["创建我的按钮"](一键回收, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 675+105, 15)
function 关闭:左键弹起(x, y, msg)
  一键回收["置可见"](一键回收, false)
end

for i, v in ipairs({
  {
    name = "回收",
    x = 220,
    y = 452,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 120, 25),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 120, 25),
    font = "回收"
  },
  {
    name = "取消",
    x = 220+150,
    y = 452,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 120, 25),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 120, 25),
    font = "取消"
  },
  {
    name = "全部选定",
    x = 220+300,
    y = 452,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 120, 25),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 120, 25),
    font = "全部选定"
  },
  {
    name = "全部取消",
    x = 220+450,
    y = 452,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 120, 25),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 120, 25),
    font = "全部取消"
  },
}) do
  local 临时函数 = 一键回收["创建我的单选按钮"](一键回收, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "回收"  then 
    发送数据(179,{数据序列=12})
    elseif  v.name == "取消"  then 
      一键回收["置可见"](一键回收, false)
    elseif  v.name == "全部选定"  then 
        发送数据(179,{数据序列=13})
    elseif  v.name == "全部取消"  then 
        发送数据(179,{数据序列=14})
    end 
  end
end
