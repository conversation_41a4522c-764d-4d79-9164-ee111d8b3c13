__取界面小图标 = function(zj)
  local zjs = {}
  if "摄妖香" == zj then
    zjs[1] = 0x3ab6780f    
    zjs[2] = 0xde5dbdfd   
    zjs[3] = 'jszy/jmxtubiao'
  elseif "丹药紫" == zj then
    zjs[1] = 0x24c33db7   
    zjs[2] = 0x61000754  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "丹药绿" == zj then
    zjs[1] = 0x54f0ed9b   
    zjs[2] = 0x81ba998e    
    zjs[3] = 'jszy/jmxtubiao'
  elseif "丹药蓝" == zj then
    zjs[1] = 0x8beb614e    
    zjs[2] = 0x2ea936e3    
    zjs[3] = 'jszy/jmxtubiao'
  elseif "丹药黄" == zj then
    zjs[1] = 0xefaeb7bb   
    zjs[2] = 0x994a022e   
    zjs[3] = 'jszy/jmxtubiao'
  elseif "翅膀" == zj then
    zjs[1] = 0x41d36e7e  
    zjs[2] = 0x41a7c6c1  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "心" == zj then
    zjs[1] =  0xed258967
    zjs[2] =  0x3d580ca6 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "宝箱" == zj then
    zjs[1] = 0x4a95b4d0  
    zjs[2] = 0x5f31923c  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "火" == zj then
    zjs[1] = 0x3e1ca604  
    zjs[2] = 0x1e0ee479   
    zjs[3] = 'jszy/jmxtubiao'
  elseif "太极" == zj then
    zjs[1] = 0xf73aa23d  
    zjs[2] = 0x02989d09       ---没有
    zjs[3] = 'jszy/jmxtubiao'
  elseif "树叶" == zj then
    zjs[1] = 0x8ad8ab32  
    zjs[2] = 0xc6f35885  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "问号" == zj then
    zjs[1] = 0x2ecec5fa  
    zjs[2] = 0xcee1e88c  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "毛笔" == zj then
    zjs[1] = 0x4bd8ba48  
    zjs[2] = 0xa8bf2975  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "钻石" == zj then
    zjs[1] = 0xf335d150  
    zjs[2] = 0xd66565ea  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "剪刀手" == zj then
    zjs[1] = 0xf1ac5f88  
    zjs[2] = 0xcff1459e  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "蜘蛛" == zj then
    zjs[1] = 0xf61c3184  
    zjs[2] = 0xacb9752b  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "心碎" == zj then
    zjs[1] = 0x3d4d2758  
    zjs[2] = 0x229c127c   
    zjs[3] = 'jszy/jmxtubiao'
  elseif "莲座" == zj then
    zjs[1] = 0x28e5311b  
    zjs[2] = 0x80ade1b4 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "虚弱" == zj then
    zjs[1] = 0xeb11694b  
    zjs[2] = 0x1ae89684   
    zjs[3] = 'jszy/jmxtubiao'
  elseif "拳头" == zj then
    zjs[1] = 0x67aaefcd  
    zjs[2] = 0xc0a9b3f8  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "骷髅头" == zj then
    zjs[1] = 0xe929d40f  
    zjs[2] = 0x26d5d07e  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "白菜" == zj then
    zjs[1] = 0xc789f28f  
    zjs[2] = 0x122f0f47  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "鸽子" == zj then
    zjs[1] = 0x8eac70f1  
    zjs[2] = 0xc7c409c5  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "流火" == zj then
    zjs[1] = 0xcc5c6e2f  
    zjs[2] = 0x090e374a   
    zjs[3] = 'jszy/jmxtubiao'
  elseif "婴儿" == zj then
    zjs[1] = 0xd1c773a0  
    zjs[2] = 0xb2af45b3  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "蓝火头" == zj then
    zjs[1] = 0x8eb86f68  
    zjs[2] = 0x66324c99  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "彩虹" == zj then
    zjs[1] = 0x9908e263  
    zjs[2] = 0x5dcd56ce  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "嘴唇" == zj then
    zjs[1] = 0xa87da61c  
    zjs[2] = 0x1d902490  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "蝴蝶" == zj then
    zjs[1] = 0x986e4394  
    zjs[2] = 0xe71d2f5f  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "奔跑" == zj then
    zjs[1] = 0x9911bc29 
    zjs[2] = 0xa92286c0 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "乌龟" == zj then
    zjs[1] = 0xc011687b  
    zjs[2] = 0x9f474c0f  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "狮子" == zj then
    zjs[1] = 0xe977a845  
    zjs[2] = 0x312a2187  
    zjs[3] = 'jszy/jmxtubiao' 
  elseif "竹笋" == zj then
    zjs[1] = 0x79fa3f6d  
    zjs[2] = 0x57c5a649  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "弓箭" == zj then
    zjs[1] = 0x781da811  
    zjs[2] = 0xbae626f8  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "龙卷风" == zj then
    zjs[1] = 0xe514f02b 
    zjs[2] = 0xe6e67711  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "爪印" == zj then
    zjs[1] = 0x605779aa 
    zjs[2] = 0xee15d655 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "肌肉" == zj then
    zjs[1] = 0xe5da2599 
    zjs[2] = 0x16be59e3  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "宝箱2" == zj then
    zjs[1] = 0x4f7c3447  
    zjs[2] = 0x59e2a565  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "蛇" == zj then
    zjs[1] = 0xd1d86d54 
    zjs[2] = 0xbc37a095 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "爪尖" == zj then
    zjs[1] = 0xe29c7276 
    zjs[2] = 0xe08b50cc 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "海浪" == zj then
    zjs[1] = 0xa4acc8e8 
    zjs[2] = 0x99a2a32e 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "太阳" == zj then
    zjs[1] = 0x77c62ee0 
    zjs[2] = 0xbffe1444 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "金丹" == zj then
    zjs[1] = 0x74e1cf0d 
    zjs[2] = 0xe8581220 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "旗子" == zj then
    zjs[1] = 0x70cd1776 
    zjs[2] = 0xf318fdef 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "玉净瓶" == zj then
    zjs[1] = 0x7416e5b6 
    zjs[2] = 0x7b1a158d 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "云朵" == zj then
    zjs[1] = 0xa54d5872 
    zjs[2] = 0x45e15411 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "扇子" == zj then
    zjs[1] = 0x14b50878 
    zjs[2] = 0x08b88a88 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "眼睛" == zj then
    zjs[1] = 0x0274346e 
    zjs[2] = 0x01a2d012 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "木鱼" == zj then
    zjs[1] = 0x1139d6aa 
    zjs[2] = 0xb97a817d 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "闪电" == zj then
    zjs[1] = 0x0c4620dc 
    zjs[2] = 0xf5a8b88c 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "花骨朵" == zj then
    zjs[1] = 0x853f35d5 
    zjs[2] = 0xd40dbfa6 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "变身卡" == zj then
    zjs[1] = 0x462db5f2  
    zjs[2] = 0x27cabf0b  
    zjs[3] = 'jszy/jmxtubiao'
  end
  return zjs
end
__取界面小图标外框 = function(zj)
  local zjs = {}
  if "绿色" == zj then
    zjs[1] = 0x8eaa4f08  
    zjs[2] = 0x13153890  
    zjs[3] = 'jszy/jmxtubiao'
  elseif "橙色" == zj then
    zjs[1] = 0x47fab5d2 
    zjs[2] = 0x73b9ccf0 
    zjs[3] = 'jszy/jmxtubiao'
  elseif "黑色" == zj then
    zjs[1] = 0x950db829 
    zjs[2] = 0x88bc4e93 
    zjs[3] = 'jszy/jmxtubiao'
  end
  return zjs
end
