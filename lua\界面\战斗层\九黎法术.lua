
local 九黎法术 = 战斗层:创建窗口("九黎法术",0,0,270,380)

local 按钮设置={"攻击","防御","设置"}

function 九黎法术:初始化()
 
end


function 九黎法术:打开(战斗单位,参战位置)
  战斗层.多开法术:置可见(false)
  self:置可见(not self.是否可见)
  if not self.是否可见 then
    return
  end
  self.参战位置 = 参战位置
  self.冷却数据 = 战斗单位.技能冷却
  self.已修改={}
  self.已修改.id =战斗单位.数据.id
  self.已修改.类型 = 战斗单位.类型
  self.已修改.九黎技能={}
  self.选择技能 = 0
  self:置精灵(置窗口背景(战斗单位.名称,0,0,270,380))

  self:创建纹理精灵(function ()
        置窗口背景(战斗单位.名称,0,0,270,380,true):显示(0, 0)
        文本字体:置颜色(255, 255, 255)
        文本字体:取图像("双技能设置:"):显示(23,130)
        文本字体:取图像("三技能设置:"):显示(23,200)
        文本字体:取图像("四技能设置:"):显示(23,270)
    end
    )
  local 技能=table.copy(战斗单位.主动技能)
  for i, v in ipairs(技能) do
      if self.技能冷却 and self.技能冷却[v.名称] then
          v.是否冷却 = 战斗单位.技能冷却[v.名称]
      end
  end
  self.技能网格:置技能(技能)

  if 战斗单位.数据.自动指令.九黎挂机 then
      local 挂机数据 = 战斗单位.数据.自动指令.九黎挂机
      for i = 2, 4 do
          if 挂机数据[i] then
             for k, v in pairs(挂机数据[i]) do
                local 增加 = 0
                if i==3 then
                    增加 = 2
                elseif i==4 then
                    增加 = 5
                end
                self.已修改.九黎技能[k+增加] = v.参数
             end
          end
      end
  end
  local 双数据 = {}
  local 三数据 = {}
  local 四数据 = {}
  for i = 1, 9 do
      if self.已修改.九黎技能[i] then
          local 冷却 = false
          if self.技能冷却 and self.技能冷却[self.已修改.九黎技能[i]] then
              冷却 = self.技能冷却[self.已修改.九黎技能[i]]
          end
          if i<=2 then
              双数据[i]={名称=self.已修改.九黎技能[i],是否冷却=冷却}
          elseif i>2 and i<=5 then
                三数据[i-2]={名称=self.已修改.九黎技能[i],是否冷却=冷却}
          else
                四数据[i-5]={名称=self.已修改.九黎技能[i],是否冷却=冷却}
          end
      end
  end
  self.双技能:置技能(双数据)
  self.三技能:置技能(三数据)
  self.四技能:置技能(四数据)

  self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
  if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
  else
      self.关闭:置大小(16,16)
      self.关闭:置坐标(self.宽度-18, 2)
  end

end



function 九黎法术:左键弹起(x,y)
        if self.选择子类  then
          for k, v in self[self.选择子类]:遍历控件() do
              if v._spr.确定 then
                v._spr.确定 = nil
              end
          end
        end
        self.选择子类 = nil
        self.选择技能 = 0
end



local 技能网格=九黎法术:创建网格("技能网格",15,33,244,89)
function 技能网格:初始化()
      self:创建格子(40,40,1,10,2,5,true)
end

function 技能网格:置技能(数据)
      for i, v in ipairs(self.子控件) do
          local lssj =  __技能格子:创建()
          if 数据[i] and 数据[i].是否冷却 then
              lssj:置数据(数据[i],true,nil,数据[i].是否冷却)
              v.是否冷却=true
          else
              lssj:置数据(数据[i],true)
              v.是否冷却=nil
          end
          v:置精灵(lssj)
      end
end

function 技能网格:获得鼠标(x,y,a)
  if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
      self.子控件[self.焦点]._spr.焦点=nil
  end
 if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
        self.焦点=a
        self.子控件[a]._spr.焦点=true
        __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
  end
end

function 技能网格:失去鼠标(x,y)
  for i, v in ipairs(self.子控件) do
      if v._spr.焦点 then
          v._spr.焦点=nil
      end
  end
  self.焦点=nil
end


function 技能网格:左键弹起(x,y,a)
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
      if self.子控件[a].是否冷却 then
            __UI弹出.提示框:打开("#Y该技能还未冷却")
      else
            if __手机 then
                    __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20,技能网格,"使用",a)
              else
                    self:使用(a)
              end
      end
  end
  界面层.按下=false

end

function 技能网格:使用(编号)
        if not 编号 or 编号==0 then return end
          九黎法术:设置技能(self.子控件[编号]._spr.数据.名称)
end


function 技能网格:左键按下(x,y,a)
      界面层.按下=false
end

function 技能网格:右键弹起(x,y,a)
      if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
              if self.子控件[a].是否冷却 then
                    __UI弹出.提示框:打开("#Y该技能还未冷却")
              else
                  self:使用(a)
              end
      end
      界面层.按下1=false
end
function 技能网格:右键按下(x,y,a)
      界面层.按下1=false
end




local 双技能=九黎法术:创建网格("双技能",23,150,250,45)
function 双技能:初始化()
      self:创建格子(40,40,1,20,1,2,true)
end


function 双技能:置技能(数据)
      for i, v in ipairs(self.子控件) do
          local lssj =  __技能格子:创建()
          if 数据[i] and 数据[i].是否冷却 then
              lssj:置数据(数据[i],true,nil,数据[i].是否冷却)
              v.是否冷却=true
          else
              lssj:置数据(数据[i],true)
              v.是否冷却=nil
          end
          v:置精灵(lssj)
      end
end

function 双技能:获得鼠标(x,y,a)
        if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
            self.子控件[self.焦点]._spr.焦点=nil
        end
        if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
              self.焦点=a
              self.子控件[a]._spr.焦点=true
              __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
        end
end

function 双技能:失去鼠标(x,y)
      for i, v in ipairs(self.子控件) do
          if v._spr.焦点 then
              v._spr.焦点=nil
          end
      end
      self.焦点=nil
end


function 双技能:左键弹起(x,y,a)
      if self.子控件[a]._spr and self.子控件[a]._spr.数据 and __手机  then
          __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
      end
      九黎法术:技能选择(a,"双技能")
      界面层.按下=false
end

function 双技能:左键按下(x,y,a)
      界面层.按下=false
end





local 三技能=九黎法术:创建网格("三技能",23,220,250,45)
function 三技能:初始化()
      self:创建格子(40,40,1,20,1,3,true)
end

function 三技能:置技能(数据)
      for i, v in ipairs(self.子控件) do
          local lssj =  __技能格子:创建()
          if 数据[i] and 数据[i].是否冷却 then
              lssj:置数据(数据[i],true,nil,数据[i].是否冷却)
              v.是否冷却=true
          else
              lssj:置数据(数据[i],true)
              v.是否冷却=nil
          end
          v:置精灵(lssj)
      end
end

function 三技能:获得鼠标(x,y,a)
      if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点=nil
      end
      if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
            self.焦点=a
            self.子控件[a]._spr.焦点=true
            __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
      end
end

function 三技能:失去鼠标(x,y)
    for i, v in ipairs(self.子控件) do
        if v._spr.焦点 then
            v._spr.焦点=nil
        end
    end
    self.焦点=nil
end

function 三技能:左键弹起(x,y,a)
  if self.子控件[a]._spr and self.子控件[a]._spr.数据 and __手机  then
      __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
  end
  九黎法术:技能选择(a,"三技能")
  界面层.按下=false
end

function 三技能:左键按下(x,y,a)
    界面层.按下=false
end



local 四技能=九黎法术:创建网格("四技能",23,290,250,45)
function 四技能:初始化()
    self:创建格子(40,40,1,20,1,4,true)
end




function 四技能:置技能(数据)
    for i, v in ipairs(self.子控件) do
        local lssj =  __技能格子:创建()
        if 数据[i] and 数据[i].是否冷却 then
            lssj:置数据(数据[i],true,nil,数据[i].是否冷却)
            v.是否冷却=true
        else
            lssj:置数据(数据[i],true)
            v.是否冷却=nil
        end
        v:置精灵(lssj)
    end
end

function 四技能:获得鼠标(x,y,a)
      if self.焦点 and self.子控件[self.焦点]._spr.焦点 then
          self.子控件[self.焦点]._spr.焦点=nil
      end
      if self.子控件[a]._spr and self.子控件[a]._spr.数据 then
            self.焦点=a
            self.子控件[a]._spr.焦点=true
            __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
      end
end

function 四技能:失去鼠标(x,y)
      for i, v in ipairs(self.子控件) do
          if v._spr.焦点 then
              v._spr.焦点=nil
          end
      end
      self.焦点=nil
end

function 四技能:左键弹起(x,y,a)
      if self.子控件[a]._spr and self.子控件[a]._spr.数据 and __手机  then
          __UI弹出.技能详情:打开(self.子控件[a]._spr.数据,x+20,y+20)
      end
      九黎法术:技能选择(a,"四技能")
      界面层.按下=false
end

function 四技能:左键按下(x,y,a)
    界面层.按下=false
end

-- function 九黎法术:技能选择(编号,子类)
--   if self.选择子类 then
--       self[self.选择子类].子控件[self.选择技能]._spr.确定 =nil
--   end
--   self[子类].子控件[编号]._spr.确定 =true
--   self.选择子类 = 子类
--   self.选择技能 = 编号
 
-- end



function 九黎法术:技能选择(编号,子类)
          if self.选择子类 then
              for k, v in self[self.选择子类]:遍历控件() do
                  if v._spr.确定 then
                     v._spr.确定 = nil
                  end
              end
          end
          self.选择子类 = 子类
          self.选择技能 = 编号
          self[self.选择子类].子控件[编号]._spr.确定 =true
         
end

function 九黎法术:设置技能(名称)
          if not self.选择子类 or not self.选择技能 or self.选择技能 ==0 then
              __UI弹出.提示框:打开('#Y请先选择下边修改的技能')
          else
              if self.选择子类=="双技能" then
                  self.已修改.九黎技能[self.选择技能] = 名称
              elseif self.选择子类=="三技能" then
                  self.已修改.九黎技能[self.选择技能+2] = 名称
              elseif self.选择子类=="四技能" then
                  self.已修改.九黎技能[self.选择技能+5] = 名称
              end
              local 双数据 = {}
              local 三数据 = {}
              local 四数据 = {}
              for i = 1, 9 do
                  if self.已修改.九黎技能[i] then
                      local 冷却 = false
                      if self.技能冷却 and self.技能冷却[self.已修改.九黎技能[i]] then
                          冷却 = self.技能冷却[self.已修改.九黎技能[i]]
                      end
                      if i<=2 then
                          双数据[i]={名称=self.已修改.九黎技能[i],是否冷却=冷却}
                      elseif i>2 and i<=5 then
                            三数据[i-2]={名称=self.已修改.九黎技能[i],是否冷却=冷却}
                      else
                            四数据[i-5]={名称=self.已修改.九黎技能[i],是否冷却=冷却}
                      end
                  end
              end
              self.双技能:置技能(双数据)
              self.三技能:置技能(三数据)
              self.四技能:置技能(四数据)
              self.选择子类 = nil
              self.选择技能 = 0
          end
end

for i, v in ipairs(按钮设置) do
      local 临时按钮=九黎法术:创建红色按钮(v,v,50+(i-1)*60,350,40,22) 
      function 临时按钮:左键弹起(x,y)
              if v ~="设置" then
                  九黎法术.已修改.九黎技能=nil
                  九黎法术.已修改.参数 =  v
              end
              请求服务(5511,{修改=九黎法术.已修改})
              九黎法术:置可见(false)
      end
end




local 关闭 = 九黎法术:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  九黎法术:置可见(false)
end
