local 摊位出售 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "摊位出售", 98 + abbr.py.x, 21 + abbr.py.y, 773, 488)
function 取指定门派技能(名称)
	local 等级=0
  for n=1,#角色信息.师门技能 do
    for i=1,#角色信息.师门技能[n].包含技能 do
      if 角色信息.师门技能[n].包含技能[i].名称==名称 and 角色信息.师门技能[n].包含技能[i].学会 then
        -- table.print(角色信息.额外技能等级)
        if 角色信息.额外技能等级[角色信息.师门技能[n].名称]~=nil then
          等级=角色信息.额外技能等级[角色信息.师门技能[n].名称]
        end
        等级=等级+角色信息.师门技能[n].等级
        return 等级--角色信息.师门技能[n].等级
      end
    end
  end
 	return 0
end

function 取门派技能()
	local 门派=角色信息.门派
	local 技能 = nil
	local 等级 = 0
	if 门派=="龙宫" then
		技能="龙附"
		等级=取指定门派技能(技能)
	elseif 门派=="大唐官府" then
		技能="嗜血"
		等级=取指定门派技能(技能)
	elseif 门派=="化生寺" then
		技能="拈花妙指"
		等级=取指定门派技能(技能)
	elseif 门派=="女儿村" then
		技能="轻如鸿毛"
		等级=取指定门派技能(技能)
	elseif 门派=="天宫" then
		技能="浩然正气"
		等级=取指定门派技能(技能)
	elseif 门派=="五庄观" then
		技能="一气化三清"
		等级=取指定门派技能(技能)
	elseif 门派=="普陀山" then
		技能="莲华妙法"
		等级=取指定门派技能(技能)
	elseif 门派=="阴曹地府" then
		技能="尸气漫天"
		等级=取指定门派技能(技能)
	elseif 门派=="盘丝洞" then
		技能="盘丝舞"
		等级=取指定门派技能(技能)
	elseif 门派=="魔王寨" then
		技能="魔王护持"
		等级=取指定门派技能(技能)
	elseif 门派=="狮驼岭" then
		技能="神力无穷"
		等级=取指定门派技能(技能)
	elseif 门派=="无底洞" then
		技能="元阳护体"
		等级=取指定门派技能(技能)
	elseif 门派=="神木林" then
		技能="神木呓语"
		等级=取指定门派技能(技能)
	elseif 门派=="方寸山" then
		技能="神兵护法"
		等级=取指定门派技能(技能)
	elseif 门派=="花果山" then
		技能="担山赶月"
		等级=取指定门派技能(技能)
	elseif 门派=="凌波城" then
		技能="穿云破空"
		等级=取指定门派技能(技能)
	end
	return {名称=技能,等级=等级,类别="其他类"}
end

function 取辅助技能()
	return {[1]={名称="打造",等级=角色信息.辅助技能[7].等级,类别="打造类"},[2]={名称="裁缝",等级=角色信息.辅助技能[8].等级,类别="打造类"},[3]={名称="炼金",等级=角色信息.辅助技能[9].等级,类别="打造类"}}
end

function 取修理技能()
	return {[1]={名称="打造修理类",等级=角色信息.辅助技能[7].等级,类别="修理类"},[2]={名称="裁缝修理类",等级=角色信息.辅助技能[8].等级,类别="修理类"},[3]={名称="炼金修理类",等级=角色信息.辅助技能[9].等级,类别="修理类"}}
end
function 摊位出售:初始化()
  local nsf = require("SDL.图像")(773, 488)
  if nsf["渲染开始"](nsf) then
    置窗口背景("我的摊位", 0, 12, 766, 476, true)["显示"](置窗口背景("我的摊位", 0, 12, 766, 476, true), 0, 0)
    取白色背景(0, 0, 353, 313, true)["显示"](取白色背景(0, 0, 353, 313, true), 21, 109)
    __res:getPNGCC(4, 719, 281, 351, 35):拉伸(353, 35):显示(21, 108)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "摊位招牌")["显示"](字体18["取图像"](字体18, "摊位招牌"), 19, 71)
    字体18["取图像"](字体18, "出售单价")["显示"](字体18["取图像"](字体18, "出售单价"), 403, 444)
    取输入背景(0, 0, 150, 23)["显示"](取输入背景(0, 0, 150, 23), 481, 442)
    取输入背景(0, 0, 160, 23)["显示"](取输入背景(0, 0, 160, 23), 102, 69)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 摊位出售:打开(名称,角色名称,id,物品数据,bb数据,制造数据)
  self:置可见(true)
  self.物品类["置选中"](self.物品类, true)
  self:刷新(名称,角色名称,id,物品数据,bb数据,制造数据)
  self.单价输入["置数值"](self.单价输入, 1)
end
function 摊位出售:刷新(名称,角色名称,id,物品数据,bb数据,制造数据)
  self.数据={}
  -- self.数据 = data
  -- table.print(bb数据)
  -- print(111111111)
  self.数据["物品表"] = {}
  self.数据["召唤兽表"] = {}
  for i, v in pairs(物品数据) do
    table.insert(self.数据["物品表"], {
      ["原始编号"] = i,
      ["价格"] = v.价格,
      ["物品"] = v
    })
  end
  for i, v in pairs(bb数据) do
    table.insert(self.数据["召唤兽表"], {
      ["原始编号"] = i,
      ["价格"] = v[1],
      ["bb"] = v
    })
  end
  local 制造技能组 = {}
	self.制造组 = {}
  self.上架制造=制造数据
-- table.print(self.上架制造)
  local 打造类组 = {}
	local 修理类组 = {}
	local 其他类组 = {}
	制造技能组.门派技能信息 = 取门派技能()
	制造技能组.辅助技能信息 = 取辅助技能()
	制造技能组.辅助修理信息 = 取修理技能()
	if 制造技能组.门派技能信息.名称~=nil and 制造技能组.门派技能信息.等级~=0 then
		self.制造组[#self.制造组+1] = 制造技能组.门派技能信息
	end
	for n=1,3 do
		if 制造技能组.辅助技能信息[n].等级~=0 then
			self.制造组[#self.制造组+1] = 制造技能组.辅助技能信息[n]
		end
	end
	for n=1,3 do
		if 制造技能组.辅助修理信息[n].等级~=0 then
			self.制造组[#self.制造组+1] = 制造技能组.辅助修理信息[n]
		end
	end
  -- if self.上架制造.制造组~=nil then
	-- 	for n=1,#self.上架制造.制造组 do
	-- 		if self.上架制造[n]~=nil then
	-- 			local 排序组 = {}
	-- 			for k,v in pairs(self.上架制造[n]) do
	-- 				table.insert(排序组,k)
	-- 				table.sort(排序组,function(a,b) return a < b end)
	-- 			end
  --       -- table.print(排序组)
	-- 			for h,j in pairs(排序组) do
	-- 				if self.上架制造.制造组[n].类别=="打造类" then
  --           print(222222,self.上架制造[n][j]["价格"])
	-- 					table.insert(打造类组,{名称=self.上架制造.制造组[n].名称,类别=self.上架制造.制造组[n].类别,序号=self.上架制造[n][j]["序号"],等级=math.floor(self.上架制造.制造组[n].等级/10)*10 - (j-1)*10,价格=self.上架制造[n][j]["价格"]})
	-- 				elseif self.上架制造.制造组[n].类别=="修理类" then
	-- 					table.insert(修理类组,{名称=self.上架制造.制造组[n].名称,类别=self.上架制造.制造组[n].类别,序号=self.上架制造[n][j]["序号"],等级=math.floor(self.上架制造.制造组[n].等级/10)*10 - (j-1)*10,价格=self.上架制造[n][j]["价格"]})
	-- 				elseif self.上架制造.制造组[n].类别=="其他类" then
	-- 					table.insert(其他类组,{名称=self.上架制造.制造组[n].名称,类别=self.上架制造.制造组[n].类别,序号=self.上架制造[n][j]["序号"],等级=self.上架制造.制造组[n].等级,价格=self.上架制造[n][j]["价格"]})
	-- 				end
	-- 			end
	-- 		end
	-- 	end
  -- end
  -- print(1111111)
  -- table.print(self.上架制造)

  self.招牌输入["置文本"](self.招牌输入, 名称)
  if self.物品类["是否选中"] then
    self.物品类["左键弹起"](self.物品类)
  elseif self.召唤兽类["是否选中"] then
    self.召唤兽类["左键弹起"](self.召唤兽类)
  elseif self.制造类["是否选中"] then
    self.制造类["左键弹起"](self.制造类)
  end
end


local 上架列表 = 摊位出售["创建列表"](摊位出售, "上架列表", 22, 143, 350, 274)
function 上架列表:初始化()
  self:置文字(字体18)
  self.行高度 = 69
  self.行间距 = 0
end
function 上架列表:重置(data, lx)
  self.清空(self)
  if "物品类" == lx then
    local nsf = require("SDL.图像")(398, 144)
    if nsf["渲染开始"](nsf) then
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, "物品名        数量    单价"):显示(57, 116)
      nsf["渲染结束"](nsf)
    end
    摊位出售["图像2"] = nsf["到精灵"](nsf)
    for _, v in ipairs(data) do
      local nsf = require("SDL.图像")(350, 69)
      if nsf["渲染开始"](nsf) then
        字体18["置颜色"](字体18, __取颜色("浅黑"))
        字体18["取图像"](字体18, __主控["道具列表"][v["原始编号"]]["名称"])["显示"](字体18["取图像"](字体18, __主控["道具列表"][v["原始编号"]]["名称"]), 25, 30)
        字体18["取图像"](字体18, __主控["道具列表"][v["原始编号"]]["数量"] or 1)["显示"](字体18["取图像"](字体18, __主控["道具列表"][v["原始编号"]]["数量"] or 1), 150, 30)
        字体18["置颜色"](字体18, __取银子颜色(v["价格"]))
        字体18["取图像"](字体18, v["价格"])["显示"](字体18["取图像"](字体18, v["价格"]), 200, 30)
        -- table.print(v)
        nsf["渲染结束"](nsf)
      end
      local r = self.添加(self)
      r["置精灵"](r, nsf["到精灵"](nsf))
      local 按钮 = r["创建我的按钮"](r, __res:getPNGCC(4, 926, 10, 37, 38), "按钮" .. _, 298, 18)
     function  按钮:左键弹起(x, y, msg)
        发送数据(3723, {
          ["道具"] = v["原始编号"]
        })
      end
      按钮["置可见"](按钮, true, true)
    end
  elseif "召唤兽类" == lx then
    local nsf = require("SDL.图像")(398, 144)
    if nsf["渲染开始"](nsf) then
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, "        召唤兽名      单价 ")["显示"](字体18["取图像"](字体18, "        召唤兽名      单价 "), 57, 116)
      nsf["渲染结束"](nsf)
    end
    摊位出售["图像2"] = nsf["到精灵"](nsf)
    for _, v in ipairs(data) do
      local nsf = require("SDL.图像")(350, 69)
      if nsf["渲染开始"](nsf) then
        local lssj = 取头像(角色信息["宝宝列表"][v["原始编号"]]["模型"])
        __res:getPNGCC(3, 757, 291, 57, 56)["显示"](__res:getPNGCC(3, 757, 291, 57, 56), 14, 13)
        __res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50)["显示"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1]))["拉伸"](__res["取图像"](__res, __res["取地址"](__res, "shape/mx/", lssj[1])), 50, 50), 16, 15)
        字体18["置颜色"](字体18, __取颜色("浅黑"))
        字体18["取图像"](字体18, 角色信息["宝宝列表"][v["原始编号"]]["名称"])["显示"](字体18["取图像"](字体18, 角色信息["宝宝列表"][v["原始编号"]]["名称"]), 76, 12)
        字体18["取图像"](字体18, 角色信息["宝宝列表"][v["原始编号"]]["等级"] .. "级")["显示"](字体18["取图像"](字体18, 角色信息["宝宝列表"][v["原始编号"]]["等级"] .. "级"), 76, 40)
        字体18["置颜色"](字体18, __取银子颜色(v["价格"]))
        字体18["取图像"](字体18, v["价格"])["显示"](字体18["取图像"](字体18, v["价格"]), 195, 30)
        nsf["渲染结束"](nsf)
      end
      local r = self.添加(self)
      r["置精灵"](r, nsf["到精灵"](nsf))
      local 按钮 = r["创建我的按钮"](r, __res:getPNGCC(4, 926, 10, 37, 38), "按钮" .. _, 298, 18)
      function  按钮:左键弹起(x, y, msg) --下架
        发送数据(3723, {
          bb = v["原始编号"]
        })
      end
      按钮["置可见"](按钮, true, true)
    end
  elseif "制造类" == lx then
    local nsf = require("SDL.图像")(398, 144)
    if nsf["渲染开始"](nsf) then
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, "制造类别           单价"):显示(57, 116)
      nsf["渲染结束"](nsf)
    end
    摊位出售["图像2"] = nsf["到精灵"](nsf)
    -- table.print(摊位出售.上架制造)
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    if 摊位出售.上架制造.制造组 then
      -- table.print(摊位出售.上架制造)
      for n, v in pairs(摊位出售.上架制造) do
        -- print(n)
        -- table.print(v)
        if n~="制造组" then
          for a, b in pairs(摊位出售.上架制造.制造组) do
            -- print(a,n)
            -- table.print(v)
            -- table.print(b)
            if a==n and v[1] then
              -- print(b["名称"])
              local nsf = require("SDL.图像")(350, 71)
              if nsf["渲染开始"](nsf) then
                字体18["取图像"](字体18, b["名称"].. b["等级"]):显示(50, 30)
                字体18["取图像"](字体18, v[1]["价格"]):显示(202, 30)
                nsf["渲染结束"](nsf)
              end
              local r = self.添加(self)
              r["置精灵"](r, nsf["到精灵"](nsf))
              local 按钮 = r["创建我的按钮"](r, __res:getPNGCC(4, 926, 10, 39, 39), "按钮" .. n, 288, 18)
              function  按钮:左键弹起(x, y, msg)
                发送数据(3723, {
                  ["制造类"] = b["名称"]
                })
              end
              按钮["置可见"](按钮, true, true)
            end
          end
        end
      end
    end 
    -- for n=1,#摊位出售.制造组 do
    --   local nsf = require("SDL.图像")(350, 71)
    --   if nsf["渲染开始"](nsf) then
    --     字体18["置颜色"](字体18, __取颜色("浅黑"))
    --     字体18["取图像"](字体18, 摊位出售.制造组[n]["名称"])["显示"](字体18["取图像"](字体18, 摊位出售.制造组[n]["名称"]), 50, 30)
    --     字体18["取图像"](字体18, 摊位出售.制造组[n]["等级"])["显示"](字体18["取图像"](字体18, 摊位出售.制造组[n]["等级"]), 205, 30)
    --     nsf["渲染结束"](nsf)
    --   end
    --   local r = self.添加(self)
    --   r["置精灵"](r, nsf["到精灵"](nsf))
    --   local 按钮 = r["创建我的按钮"](r, __res:getPNGCC(4, 926, 10, 37, 36), "按钮" .. n, 288, 18)
    --   function  按钮:左键弹起(x, y, msg)
    --     发送数据(3723, {
    --       ["制造类"] = 摊位出售.制造组[n]["名称"]
    --     })
    --   end
    --   按钮["置可见"](按钮, true, true)
    -- end
    -- for _, v in ipairs(摊位出售.制造组) do
      -- local nsf = require("SDL.图像")(350, 69)
      -- if nsf["渲染开始"](nsf) then
      --   字体18["置颜色"](字体18, __取颜色("浅黑"))
      --   字体18["取图像"](字体18, v["名称"])["显示"](字体18["取图像"](字体18, v["名称"]), 50, 30)
      --   字体18["取图像"](字体18, v["价格"])["显示"](字体18["取图像"](字体18, v["价格"]), 205, 30)
      --   nsf["渲染结束"](nsf)
      -- end
      -- local r = self.添加(self)
      -- r["置精灵"](r, nsf["到精灵"](nsf))
      -- local 按钮 = r["创建我的按钮"](r, __res:getPNGCC(4, 926, 10, 37, 36), "按钮" .. _, 288, 18)
      -- function  按钮:左键弹起(x, y, msg)
      --   发送数据(3723, {
      --     ["制造类"] = v["名称"]
      --   })
      -- end
      -- 按钮["置可见"](按钮, true, true)
    -- end
  end
end











function 摊位出售:切换界面(界面)
  self.物品类控件["置可见"](self.物品类控件, 界面 == self.物品类控件, not self.物品类控件["是否实例"])
  self.召唤兽类控件["置可见"](self.召唤兽类控件, 界面 == self.召唤兽类控件, not self.召唤兽类控件["是否实例"])
  self.制造类控件["置可见"](self.制造类控件, 界面 == self.制造类控件, not self.制造类控件["是否实例"])
end
for i, v in ipairs({
  {
    name = "物品类",
    x = 395,
    y = 62,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 118, 41),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 41),
    font = "物品类"
  },
  {
    name = "召唤兽类",
    x = 517,
    y = 62,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 118, 41),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 41),
    font = "召唤兽类"
  },
  {
    name = "制造类",
    x = 639,
    y = 62,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 118, 41),
    tcp2 = __res:getPNGCC(3, 390, 12, 118, 43, true)["拉伸"](__res:getPNGCC(3, 390, 12, 118, 43, true), 118, 41),
    font = "制造类"
  }
}) do
  local 临时函数 = 摊位出售["创建我的单选按钮"](摊位出售, v.tcp, v.tcp2, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "物品类" then
      摊位出售["切换界面"](摊位出售, 摊位出售[v.name .. "控件"])
      摊位出售["物品类控件"]["重置"](摊位出售["物品类控件"])
      摊位出售["物品类控件"]["道具网格"]["置物品"](摊位出售["物品类控件"]["道具网格"], __主控["道具列表"])
      摊位出售["上架列表"]["重置"](摊位出售["上架列表"], 摊位出售["数据"]["物品表"], v.name)
    elseif v.name == "召唤兽类" then
      摊位出售["切换界面"](摊位出售, 摊位出售[v.name .. "控件"])
      摊位出售["召唤兽类控件"]["重置"](摊位出售["召唤兽类控件"])
      摊位出售["召唤兽类控件"]["召唤兽网格"]["置物品"](摊位出售["召唤兽类控件"]["召唤兽网格"], 角色信息["宝宝列表"])
      摊位出售["上架列表"]["重置"](摊位出售["上架列表"], 摊位出售["数据"]["召唤兽表"], v.name)
    elseif v.name == "制造类" then
      摊位出售["切换界面"](摊位出售, 摊位出售[v.name .. "控件"])
      摊位出售["制造类控件"]["重置"](摊位出售["制造类控件"])
      摊位出售["制造类控件"]["制造列表"]["重置"](摊位出售["制造类控件"]["制造列表"])
      摊位出售["上架列表"]["重置"](摊位出售["上架列表"], 摊位出售.制造组, v.name)
    end
    摊位出售["选中"] = nil
  end
end




local 制造类控件 = 摊位出售["创建控件"](摊位出售, "制造类控件", 398, 106, 360, 320)
function 制造类控件:重置()
  摊位出售["图像"]=nil
  local nsf = require("SDL.图像")(351, 320)
  if nsf["渲染开始"](nsf) then
    取白色背景(0, 0, 350, 314, true)["显示"](取白色背景(0, 0, 350, 314, true), 0, 4)
    字体18["置颜色"](字体18, __取颜色("浅黑"))
    字体18["取图像"](字体18, "制造类别     熟练度     技能等级")["显示"](字体18["取图像"](字体18, "制造类别     熟练度     技能等级"), 36, 11)
    nsf["渲染结束"](nsf)
  end
  摊位出售["图像"] = nsf["到精灵"](nsf)
  摊位出售["图像"]["置中心"](摊位出售["图像"], -398, -106)
end
local 制造列表 = 制造类控件["创建列表"](制造类控件, "制造列表", 0, 38, 350, 274)
function 制造列表:初始化()
  self:置文字(字体18)
  self.行高度 = 69
  self.行间距 = 0
end
function 制造列表:左键弹起(x, y, i, item, msg)
  摊位出售["选中"] = i
end
function 制造列表:重置()
  self.清空(self)
  for _, v in ipairs(摊位出售.制造组) do
    local nsf = require("SDL.图像")(350, 69)
    if nsf["渲染开始"](nsf) then
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, v["名称"])["显示"](字体18["取图像"](字体18, v["名称"]), 52, 26)
      字体18["取图像"](字体18, v["熟练度"] or 0)["显示"](字体18["取图像"](字体18, v["熟练度"] or 0), 165, 26)
      字体18["取图像"](字体18, v["等级"])["显示"](字体18["取图像"](字体18, v["等级"]), 267, 26)
      nsf["渲染结束"](nsf)
    end
    local r = self.添加(self)
    r["置精灵"](r, nsf["到精灵"](nsf))
  end
end






for i, v in ipairs({
  {
    name = "改名",
    x = 267,
    y = 61,
    tcp = __res:getPNGCC(3, 126, 563, 111, 36, true)["拉伸"](__res:getPNGCC(3, 126, 563, 111, 36, true), 108, 36),
    font = "改名"
  },
  {
    name = "收摊",
    x = 20,
    y = 434,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "收摊"
  },
  {
    name = "替身货郎",
    x = 140,
    y = 434,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "替身货郎"
  },
  {
    name = "更多",
    x = 260,
    y = 434,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41),
    font = "更多"
  },
  
}) do
  local 临时函数 = 摊位出售["创建我的按钮"](摊位出售, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "改名" then
      if 摊位出售["招牌输入"]["取文本"](摊位出售["招牌输入"]) then
        发送数据(3721, {
          ["名称"] = 摊位出售["招牌输入"]["取文本"](摊位出售["招牌输入"])
        })
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "请输入招牌名称")
      end
    elseif v.name == "收摊" then
      发送数据(3724)
    elseif v.name == "替身货郎" then
    
    end
  end
end


local 招牌输入 = 摊位出售["创建我的输入"](摊位出售, "招牌输入", 105, 71, 110, 18, nil, 8, "黑色", 字体18)
local 单价输入 = 摊位出售["创建我的输入"](摊位出售, "单价输入", 497, 444, 120, 18, 2, 10, "黑色")
function 单价输入:初始化()
  self:置限制字数(9)
    self:置模式(self.数字模式)
end
local 上架按钮=摊位出售["创建我的按钮"](摊位出售, __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 114, 41), "上架按钮", 642, 434, "上架")
function 上架按钮:左键弹起(x, y, msg)
  if 摊位出售["单价输入"]["取数值"](摊位出售["单价输入"]) and 摊位出售["单价输入"]["取数值"](摊位出售["单价输入"]) > 0 then
    if 摊位出售["物品类"]["是否选中"] then
      发送数据(3722, {
        ["道具"] = 摊位出售["选中"],
        ["价格"] = 摊位出售["单价输入"]["取数值"](摊位出售["单价输入"])
      })
    elseif 摊位出售["召唤兽类"]["是否选中"] then
      发送数据(3722, {
        bb = 摊位出售["选中"],
        ["价格"] = 摊位出售["单价输入"]["取数值"](摊位出售["单价输入"])
      })
    elseif 摊位出售["制造类"]["是否选中"] then
      发送数据(3722, {
        制造 = {[1]=摊位出售["选中"],[2]=1},
        制造组=摊位出售.制造组,
        价格 = 摊位出售["单价输入"]["取数值"](摊位出售["单价输入"])
      })
    end
  else
    __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "请输入正确的单机")
  end
end
local 关闭 = 摊位出售["创建我的按钮"](摊位出售, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 723, 0)
function 关闭:左键弹起(x, y, msg)
  摊位出售["置可见"](摊位出售, false)
end










local 物品类控件 = 摊位出售["创建控件"](摊位出售, "物品类控件", 398, 106, 360, 375)
function 物品类控件:重置()
  摊位出售["图像"]=nil
  local nsf = require("SDL.图像")(360, 375)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(3, 694, 4, 338, 273)["显示"](__res:getPNGCC(3, 694, 4, 338, 273), 0, 0)
    nsf["渲染结束"](nsf)
  end
  摊位出售["图像"] = nsf["到精灵"](nsf)
  摊位出售["图像"]["置中心"](摊位出售["图像"], -398, -106)
end
local 道具网格 = 物品类控件["创建网格"](物品类控件, "道具网格", 0, 0, 339, 272)
function 道具网格:初始化()
  self:创建格子(67, 67, 0, 0, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if 摊位出售["选中"] then
    self.子控件[摊位出售["选中"]]._spr["确定"] = nil
  end
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    摊位出售["选中"] = a
    self.子控件[a]._spr["确定"] = true
  end
end
function 道具网格:置物品(data, zl, fl)
  self:创建格子(67, 67, 0, 0, math.ceil(#data / 5), 5)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __摊位格子["创建"]()
      lssj["置物品"](lssj, data[i], "摊位出售物品")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
      for a,b in pairs(摊位出售["数据"]["物品表"]) do
        if b.原始编号==i then
          self.子控件[i]._spr["已上架"] = true
          break
        end
      end
      -- if 摊位出售["数据"]["物品表"][i] then
      --   self.子控件[i]._spr["已上架"] = true
      -- end
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
  -- for i = 1, #self.子控件 do
  --   if self.子控件[i] and self.子控件[i]._spr and self.子控件[i]._spr["已上架"] then
  --   end
  -- end

end













local 召唤兽类控件 = 摊位出售["创建控件"](摊位出售, "召唤兽类控件", 398, 106, 351, 310)
function 召唤兽类控件:重置()
  摊位出售["图像"]=nil
  local nsf = require("SDL.图像")(351, 320)
  if nsf["渲染开始"](nsf) then
    取灰色背景(0, 0, 351, 314, true)["显示"](取灰色背景(0, 0, 351, 314, true), 0, 4)
    nsf["渲染结束"](nsf)
  end
  摊位出售["图像"] = nsf["到精灵"](nsf)
  摊位出售["图像"]["置中心"](摊位出售["图像"], -398, -106)
end
local 召唤兽网格 = 召唤兽类控件["创建网格"](召唤兽类控件, "召唤兽网格", 11, 12, 332, 300)
function 召唤兽网格:左键弹起(x, y, a, b, msg)
  if 摊位出售["选中"] then
    self.子控件[摊位出售["选中"]]._spr["确定"] = nil
  end
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    摊位出售["选中"] = a
    self.子控件[a]._spr["确定"] = true
  end
end
function 召唤兽网格:置物品(data)
  self:创建格子(332, 67, 10, 10, #data, 1, true)
  for i = 1, #self.子控件 do
    if data[i] then
      local lssj = __摊位格子["创建"]()
      lssj["置物品"](lssj, data[i], "摊位出售召唤兽")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
      -- if 摊位出售["数据"].bb[i] then
      --   self.子控件[i]._spr["已上架"] = true
      -- end
      for a,b in pairs(摊位出售["数据"]["召唤兽表"]) do
        if b.原始编号==i then
          self.子控件[i]._spr["已上架"] = true
          break
        end
      end
      local 按钮 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(4, 822, 9, 38, 38), "按钮" .. i, 274, 15) --
      function  按钮:左键弹起(x, y, msg)
        __UI界面["窗口层"]["召唤兽查看"]["打开"](__UI界面["窗口层"]["召唤兽查看"], data[i])
      end
      按钮["置可见"](按钮, true, true)
    else
      self.子控件[i]["置精灵"](self.子控件[i])
    end
  end
end