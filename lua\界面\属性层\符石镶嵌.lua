local 符石镶嵌 = 窗口层:创建窗口("符石镶嵌", 0, 10, 400, 510)
function 符石镶嵌:初始化()
  self:创建纹理精灵(function()
            置窗口背景("符石镶嵌", 0, 0, 400, 495, true):显示(0, 0)
            取属性背景(0, 0, 370, 135, true):显示(10, 305)
            __res:getPNGCC(2, 892, 179, 143, 209):拉伸(140, 190):置颜色(54, 161, 202, 255):显示(14, 69)
            说明字体:置颜色(__取颜色("黄色"))
            说明字体:取图像("点击孔位镶嵌:"):显示(15, 270)
          end
    )

  self.符石道具={}
  self.已选符石={}
  self.镶嵌数量=0
  self.装备物品 = __物品格子:创建()
  self.装备物品:置物品()
  self:置坐标((引擎.宽度 - self.宽度) // 2, 10)
  self.可初始化=true
  if __手机 then
    self.关闭:置大小(25,25)
    self.关闭:置坐标(self.宽度-27, 2)
  else
    self.关闭:置大小(16,16)
    self.关闭:置坐标(self.宽度-18, 2)
  end
  
  

end






function 符石镶嵌:打开(数据)
    if 数据==nil then
        return
    end

    self:置可见(not self.是否可见)
    if not self.是否可见 then
        return
    end
  self:刷新符石(数据)
end
function 符石镶嵌:显示(x,y)
    if self.图像 then
        self.图像:显示(x+20,y+100)
    end
end


function 符石镶嵌:刷新符石(数据,是否)
	-- table.print(数据)
    self.发送内容={}
    self.装备 = 数据
    self.装备物品:置物品(self.装备,55,55)
    self.图像=nil
    if self.装备物品.物品~=nil and self.装备物品.物品.资源~=nil and self.装备物品.物品.大模型资源~=nil then
        self.图像= __res:取资源动画(self.装备物品.物品.资源, self.装备物品.物品.大模型资源,"精灵")  
    end
    self.镶嵌数量=0
    请求服务(3699)
    if self.装备.开运孔数~=nil and  self.装备.开运孔数.当前 ~= nil and self.装备.开运孔数.当前  ~= 0 then
        self.镶嵌数量=self.装备.开运孔数.当前
    else
        self:置可见(false)
    end
    --self.道具列表=table.copy(_tp.道具列表)
    self.符石文本:清空()
    self.符石文本:置文本("#Y" .. self.装备.名称)
    self.符石文本:置文本("#G开运孔数：" .. self.装备.开运孔数.当前 .. "孔\\" .. self.装备.开运孔数.上限 .. "孔")
    self.符石道具={}
    self.星石道具 = nil
    if self.装备.星位~=nil then
        for n=1,5 do
            if self.装备.星位[n]~=nil then
                local 组合语句="#G符石："
                local 计数 = 1
                for k,v in pairs(self.装备.星位[n].符石属性) do
                    if 计数 == 1 then
                        组合语句=组合语句..k.." +"..v.." "
                    else
                        组合语句=组合语句..k.." +"..v
                    end
                    计数=计数+1
                end
                self.符石文本:置文本(组合语句)
               
                self.符石道具[n]=table.copy(self.装备.星位[n])
            end    
        end
        if self.装备.星位组 then
            if self.装备.星位[6] ~= nil then
                local 临时属性 = ""
                for k,v in pairs(self.装备.星位[6].符石属性) do
                    临时属性 = k.." +"..v
                end
                self.符石文本:置文本("#G星位: "..临时属性)
                if self.装备.星位[6].相互~=nil then
                    local 临时属性1 = ""
                    for k,v in pairs(self.装备.星位[6].相互) do
                            临时属性1 = k.." +"..v
                    end
                    self.符石文本:置文本("#G星相互合: "..临时属性1)
                end
                self.星石道具=table.copy(self.装备.星位[6])
                self.星石道具.sx = 临时属性
            else
                self.符石文本:置文本("#G星位: 已开")
            end
        end
        if  self.装备.星位.组合~=nil then
            self.符石文本:置文本(string.format("#X符石组合： %s",self.装备.星位.组合))
            self.符石文本:置文本(string.format("#X门派条件： %s",self.装备.星位.门派 or "无"))
            self.符石文本:置文本(string.format("#X部位条件： %s",取符石部位(self.装备.星位.部位)))
            self.符石文本:置文本(string.format("#X%s",取符石组合说明(self.装备.星位.组合,self.装备.星位.组合等级)))
        end
    end

    self.符石网格:置物品(self.符石道具)
    self.星石网格:置物品(self.星石道具)

end



function 符石镶嵌:符石预览()
    self.符石文本:清空()
    self.符石文本:置文本("#Y" .. self.装备.名称)
    self.符石文本:置文本("#G开运孔数：" .. self.装备.开运孔数.当前 .. "孔\\" .. self.装备.开运孔数.上限 .. "孔")
    local 物品表 = {}
    local 等级计算 = 0
    for i=1,5 do
        if self.符石网格.子控件[i]~=nil and self.符石网格.子控件[i]._spr~=nil and self.符石网格.子控件[i]._spr.物品~=nil then
            物品表[i]={}
            物品表[i].颜色=self.符石网格.子控件[i]._spr.物品.颜色
            等级计算 = 等级计算 + self.符石网格.子控件[i]._spr.物品.子类
            local  组合语句="#G符石："
            local 计数 = 1
            for k,v in pairs(self.符石网格.子控件[i]._spr.物品.符石属性) do
                if 计数 == 1 then
                    组合语句=组合语句..k.." +"..v.." "
                else
                    组合语句=组合语句..k.." +"..v
                end
                计数=计数+1
            end
            self.符石文本:置文本(组合语句)
        end
    end
    if self.装备.星位组 then
        if self.装备.星位~=nil and self.装备.星位[6] ~= nil then
            local 临时属性 = ""
            for k,v in pairs(self.装备.星位[6].符石属性) do
                临时属性 = k.." +"..v
            end
            self.符石文本:置文本("#G星位: "..临时属性)
            if 等级计算~=0 then
                if 等级计算%2==0 then
                    if self.装备.星位[6].阴阳==2 then
                        self.符石文本:置文本("#G星相互合: "..取星位相互(self.装备.分类).." +2")
                    end
                else
                    if self.装备.星位[6].阴阳==1 then
                        self.符石文本:置文本("#G星相互合: "..取星位相互(self.装备.分类).." +2")
                    end
                end
            end
        else
            self.符石文本:置文本("#G星位: 已开")
        end
    end
    local 临时组合 = 取星位组合(物品表)
    if 临时组合 and 临时组合.组合 then
        self.符石文本:置文本(string.format("#X符石组合： %s",临时组合.组合))
        self.符石文本:置文本(string.format("#X门派条件： %s",临时组合.门派 or "无"))
        self.符石文本:置文本(string.format("#X部位条件： %s",取符石部位(临时组合.部位)))
        self.符石文本:置文本(string.format("#X%s",取符石组合说明(临时组合.组合,临时组合.等级)))
    end
end



function 符石镶嵌:替换符石(格子)
        local 事件 = function (编号)
          if _tp.道具列表[编号]~=nil then
              if _tp.道具列表[编号].总类 == 889 and _tp.道具列表[编号].分类 == 88 and _tp.道具列表[编号].子类 < 4 then
                    if self.符石道具[格子] and self.符石道具[格子].原始编号 then
                            local 道具id =self.符石道具[格子].原始编号
                            self.符石道具[格子].原始编号=nil
                            _tp.道具列表[道具id]=table.copy(self.符石道具[格子])
                    end
                    self.符石道具[格子]=table.copy(_tp.道具列表[编号])
                    self.符石道具[格子].原始编号=编号
                    _tp.道具列表[编号]=nil
                    self.发送内容[格子]={背包类型="道具",物品id=编号,方式="镶嵌"}
                    self.符石网格:置物品(self.符石道具)
                    self:符石预览()
                else
                      __UI弹出.提示框:打开("#Y请选择正确道具")
                end
          end
      end
      __UI弹出.道具选择:打开(_tp.道具列表,事件)
end

local 符石网格 = 符石镶嵌:创建网格("符石网格", 28, 375, 350, 55)
function 符石网格:初始化()
    self:创建格子(55, 55, 0, 15, 1, 5)
end

function 符石网格:获得鼠标(x, y, a)
      if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not  __手机 then
          __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
      end
end



function 符石网格:左键弹起(x, y, a)
    if self.子控件[a]._spr and 符石镶嵌.镶嵌数量>=a  then
       if self.子控件[a]._spr.物品  and __手机 then
        __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,符石网格,"替换",a)
       else
            self:替换(a)
       end
    else
        __UI弹出.提示框:打开("#Y请开孔后在操作")
    end

end


function 符石网格:替换(编号)
        if 编号 and 编号~=0 then
            符石镶嵌:替换符石(编号)
        end

end


function 符石网格:置物品(数据)
    for i = 1, #self.子控件 do
        local lssj = __物品格子:创建()
        lssj:置物品(nil,55,55,nil,true)
        if 数据 and 数据[i] then
            lssj:置物品(数据[i], 55,55,nil,true)
            if 数据[i].子类 then
                lssj.物品.子类 = 数据[i].子类
            end
            if 数据[i].颜色 then
                lssj.物品.颜色 = 数据[i].颜色
            end
            if  数据[i].符石属性 then
                lssj.物品.符石属性 = 数据[i].符石属性
            end
        end
      self.子控件[i]:置精灵(lssj)

    end
end


local 星石网格 = 符石镶嵌:创建网格("星石网格", 168, 310, 350, 55)
function 星石网格:初始化()
    self:创建格子(55, 55, 0, 0, 1, 1)
end
function 星石网格:获得鼠标(x, y, a)
  if self.子控件[a]._spr and self.子控件[a]._spr.物品 and not  __手机 then
      __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20)
  end
end
function 星石网格:左键弹起(x, y, a)
  if self.子控件[a]._spr and  符石镶嵌.装备.星位组  then
        if self.子控件[a]._spr.物品 and not self.子控件[a]._spr.物品.原始编号  then
            if __手机 then
                  __UI弹出.道具提示:打开(self.子控件[a]._spr.物品,x+20,y+20,星石网格,"翻转",1) 
            else
                  self:翻转(1)
            end
        else
            local 事件 = function (编号)
                if _tp.道具列表[编号]~=nil then
                    if _tp.道具列表[编号].总类 == 889 and _tp.道具列表[编号].分类 == 91 then
                        if self.子控件[a]._spr.物品 and self.子控件[a]._spr.物品.原始编号 then
                            local 道具id =self.子控件[a]._spr.物品.原始编号
                            self.子控件[a]._spr.物品.原始编号=nil
                            _tp.道具列表[道具id]=table.copy(self.子控件[a]._spr.物品)
                        end
                        符石镶嵌.星石道具=table.copy(_tp.道具列表[编号])
                        符石镶嵌.星石道具.原始编号=编号
                        _tp.道具列表[编号]=nil
                        符石镶嵌.发送内容[6]={背包类型="道具",物品id=编号,方式="镶嵌"}
                        self:置物品(符石镶嵌.星石道具)
                    else
                        __UI弹出.提示框:打开("#Y请选择正确道具")
                    end
                end
              end
              __UI弹出.道具选择:打开(_tp.道具列表,事件) 
        end
  end

end
function 星石网格:翻转(编号)
          if 编号 and 编号~=0 then
               请求服务(3802)
          end
end

function 星石网格:置物品(数据)
    for i = 1, #self.子控件 do
        local lssj = __物品格子:创建()
        lssj:置物品(数据,55,55,nil,true)
        if 数据 and 数据.阴阳 then
            lssj.物品.类型 = 数据.阴阳
        end
        self.子控件[i]:置精灵(lssj)

    end
end



local 符石文本 = 符石镶嵌:创建文本("符石文本", 170, 69, 206, 220)
function 符石文本:初始化()
end


local 装备详情 =符石镶嵌:创建按钮("装备详情", 114, 220)
function 装备详情:初始化()
    self:创建按钮精灵(__res:getPNGCC(2, 1066, 6, 37, 37, true),1)
end
function 装备详情:左键弹起(x, y)
    if 符石镶嵌.装备物品 and 符石镶嵌.装备物品.物品 and __手机 then
        __UI弹出.道具提示:打开(符石镶嵌.装备物品.物品,x+20,y+20)
    end

end
function 装备详情:获得鼠标(x, y)
    if 符石镶嵌.装备物品 and 符石镶嵌.装备物品.物品 then
        __UI弹出.道具提示:打开(符石镶嵌.装备物品.物品,x+20,y+20)
    end
end


local 镶嵌 = 符石镶嵌:创建红色按钮( "镶嵌", "镶嵌", 80, 455,80,22)
function 镶嵌:左键弹起(x, y)
    local 数量=0
    for key, value in pairs(符石镶嵌.发送内容) do
        数量=数量+1
    end
    if 数量>0 then
        请求服务(3801,{内容=符石镶嵌.发送内容})
        符石镶嵌.发送内容={}
    end
end

local 还原 = 符石镶嵌:创建红色按钮( "还原", "还原", 250, 455,80,22)
function 还原:左键弹起(x, y)
  
    for i, v in ipairs(符石镶嵌.符石网格.子控件) do
        if v._spr and v._spr.物品 and v._spr.物品.原始编号 then
            local 道具id =v._spr.物品.原始编号
            v._spr.物品.原始编号=nil
            _tp.道具列表[道具id]=table.copy(v._spr.物品)
        end
    end

    if 符石镶嵌.星石网格.子控件[1] and 符石镶嵌.星石网格.子控件[1]._spr and 符石镶嵌.星石网格.子控件[1]._spr.物品 and 符石镶嵌.星石网格.子控件[1]._spr.物品.原始编号 then
        local 道具id =符石镶嵌.星石网格.子控件[1]._spr.物品.原始编号
        符石镶嵌.星石网格.子控件[1]._spr.物品.原始编号=nil
        _tp.道具列表[道具id]=table.copy(符石镶嵌.星石网格.子控件[1]._spr.物品)
    end

    符石镶嵌:刷新符石(符石镶嵌.装备)
end



local 关闭 = 符石镶嵌:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y)
  符石镶嵌:置可见(false)
end
