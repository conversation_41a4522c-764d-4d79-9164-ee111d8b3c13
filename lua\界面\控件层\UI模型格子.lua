

local UI模型格子 = class("UI模型格子")
local ggf = require("GGE.函数")


function UI模型格子:初始化()
  self.动画 = {}
  self.x, self.y = 0, 0
  self.影子=__res:取资源动画('dlzy',0xDCE4B562,"精灵")
end


function UI模型格子:置数据(数据, 类型, x, y, 附加)

  self.人物=nil
  self.武器=nil
  self.副武器=nil
  self.光环=nil
  self.足迹=nil
  self.方向 = 1
  local 加入数据 = ggf.insert(self.动画)
  if not 数据 then
    self.x, self.y = 0, 0
    return
  end
  if "角色" == 类型 then
      if not 数据.造型 then 数据.造型 = 数据.模型 end
      local 资源 = 取模型(数据.造型)
      local m 
      if 数据.装备 and 数据.装备[3] ~= nil and 数据.装备[3].子类 then
        m = _tp:取武器子类(数据.装备[3].子类)
        资源 = 取模型(数据.造型, m)
      end

      self.人物 =__res:取资源动画(资源[3], 资源[1],"置动画"):置循环(true)
      local  是否显示武器 = true
      if 数据.锦衣  and 数据.锦衣[1] ~= nil and 数据.锦衣[1].名称  then
          local 锦衣名称 = 数据.锦衣[1].名称
          if 锦衣名称=="青春" or 锦衣名称=="素颜" or 锦衣名称=="绝色" or 锦衣名称=="春秋" or  锦衣名称=="夏蚕"
          or 锦衣名称=="星河" or 锦衣名称=="白峨" or 锦衣名称=="糖果" or 锦衣名称=="青涩" or 锦衣名称=="傲然"
          or 锦衣名称=="牛仔" or  锦衣名称=="试剑" or 锦衣名称=="骨龙战骑" or 锦衣名称=="水嘟嘟·钻白"or 锦衣名称=="斗战神"
          or 锦衣名称=="斗战胜佛" or  锦衣名称=="八部天龙马·玄" or  锦衣名称=="龙凰·桃" or  锦衣名称=="龙凰·皑"  then
            资源 = 取战斗锦衣素材(锦衣名称,数据.造型)
            self.人物 =  __res:取资源动画(资源[5],资源[3],"置动画"):置循环(true)
            是否显示武器 = false
          elseif 新加战斗锦衣[锦衣名称]~=nil  then
                  资源 = 取武器锦衣素材(锦衣名称,数据.造型,m)
                  self.人物 =  __res:取资源动画(资源[5],资源[3],"置动画"):置循环(true)
                  是否显示武器 = true
          end
          数据.染色方案=nil
          数据.染色组=nil
      end
      if 数据.染色方案~=nil and 数据.染色方案~=0 and 数据.染色组~=nil and 数据.染色组~=0 and #数据.染色组>0 then
          local 调色板  = __dewpal(数据.染色方案)
          self.人物:调色(调色板,取调色数据(数据.染色组))
      end
      if  数据.装备 and 数据.装备[3] ~= nil and 数据.装备[3].名称 ~= nil and 数据.装备[3].名称 ~= ""   then
          local ms = _tp:取武器附加名称(数据.装备[3].子类, 数据.装备[3].级别限制,数据.装备[3].名称)
          资源 = 取模型(ms .. "_" .. 数据.造型)
          self.武器 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
          if 数据.装备[3].染色方案~=nil and 数据.装备[3].染色方案~=0 and 数据.装备[3].染色组~=nil and 数据.装备[3].染色组~=0 and #数据.装备[3].染色组>0 then
              local 调色板  = __dewpal(数据.装备[3].染色方案)
              self.武器:调色(调色板,取调色数据(数据.装备[3].染色组))
          end
      end
      if 数据.装备 and 数据.装备[4]~=nil and 数据.造型=="影精灵" and string.find(数据.装备[4].名称,"(坤)") and (not 数据.装备[3] or string.find(数据.装备[3].名称,"(乾)")) then
          资源 = 取模型(数据.装备[4].名称 .. "_" .. 数据.造型)
          self.副武器 = __res:取资源动画(资源[3],资源[1],"置动画"):置循环(true)
          if 数据.装备[4].染色方案~=nil and 数据.装备[4].染色方案~=0 and 数据.装备[4].染色组~=nil and 数据.装备[4].染色组~=0 and #数据.装备[4].染色组>0 then
              local 调色板  = __dewpal(数据.装备[4].染色方案)
              self.副武器:调色(调色板,取调色数据(数据.装备[4].染色组))
          end
      end
      if 数据.锦衣 and 数据.锦衣[3] ~= nil and 数据.锦衣[3].名称 ~= nil  then
            local n = 取足迹(数据.锦衣[3].名称)
            self.足迹 = __res:取资源动画(n[4],n[1],"置动画"):置循环(true)
      end
      if  数据.锦衣 and 数据.锦衣[2] ~= nil and 数据.锦衣[2].名称 ~= nil then
            local n = 取光环(数据.锦衣[2].名称)
            self.光环 = __res:取资源动画(n[4],n[1],"置动画"):置循环(true)
      end
      if not 是否显示武器 then
          self.武器=nil
          self.副武器=nil
      end

      self.方向 = 5
  elseif "召唤兽" == 类型 or "子女" == 类型 then
      local lssj = 取战斗模型(数据.模型)
      self.人物 = __res:取资源动画(lssj[10], lssj[6],"置动画"):置循环(true)

      if 数据.染色方案~=nil and 数据.染色方案~=0 and 数据.染色组~=nil and 数据.染色组~=0 and #数据.染色组>0 then
          local 调色板  = __dewpal(数据.染色方案)
          self.人物:调色(调色板,取调色数据(数据.染色组))
      end
      if 数据.饰品 then
          lssj = 取战斗模型(数据.模型 .. "_饰品")
          if lssj[6] then
            self.武器 =__res:取资源动画(lssj[10], lssj[6],"置动画"):置循环(true)
            if 数据.饰品染色方案~=nil and 数据.饰品染色方案~=0 and 数据.饰品染色组~=nil and 数据.饰品染色组~=0 and #数据.饰品染色组>0 then
                local 调色板  = __dewpal(数据.饰品染色方案)
                self.人物:调色(调色板,取调色数据(数据.饰品染色组))
            end
          end
      end
      self.方向 = 1
  elseif "坐骑" == 类型 then
        local lssj = {}
        if 新增坐骑(附加,数据.模型,"站立") ~= nil and 新增坐骑(附加,数据.模型,"站立") ~= ""  then
            lssj.坐骑资源 = "jszy/xzzq"
            lssj.坐骑行走 = 新增坐骑(附加,数据.模型,"奔跑")
            lssj.坐骑站立 = 新增坐骑(附加,数据.模型,"站立")
        else
            lssj = 坐骑库(附加,数据.模型,数据.饰品 or "空")
        end
        if lssj then
            self.人物 =__res:取资源动画(lssj.坐骑资源, lssj.坐骑站立,"置动画"):置循环(true)
            if 数据.染色方案~=nil and 数据.染色方案~=0 and 数据.染色组~=nil and 数据.染色组~=0 and #数据.染色组>0 then
                local 调色板  = __dewpal(数据.染色方案)
                self.人物:调色(调色板,取调色数据(数据.染色组))
            end
            if lssj.坐骑饰品站立 ~= nil then
              self.武器 =__res:取资源动画(lssj.坐骑饰品资源, lssj.坐骑饰品站立,"置动画"):置循环(true)
            end
        end
        self.方向 = 1
  elseif "宠物" == 类型 then
      local lssj = 取模型(数据)
      self.人物=__res:取资源动画(lssj[3], lssj[1],"置动画"):置循环(true)
      self.方向 = 1
  end
  self:置方向(self.方向)
  self.x, self.y = x, y
end
function UI模型格子:清空()
    self.人物=nil
    self.武器=nil
    self.副武器=nil
    self.光环=nil
    self.足迹=nil
    self.方向 =1
    self.x, self.y = 0, 0
end
function UI模型格子:更新(dt)
  if self.足迹 then
    self.足迹:更新(dt)
  end
  if self.光环 then
      self.光环:更新(dt)
  end
  if self.人物 then
      self.人物:更新(dt)
  end
  if self.武器 then
      self.武器:更新(dt)
  end
  if self.副武器 then
    self.副武器:更新(dt)
  end
end

function UI模型格子:置方向(方向)
   self.方向 = 方向
   if self.人物 then
      self.人物:置方向(方向)
   end
   if self.武器 then
      self.武器:置方向(方向)
   end
   if self.副武器 then
      self.副武器:置方向(方向)
   end
   if self.光环 then
      self.光环:置方向(方向)
   end
   if self.足迹 then
     self.足迹:置方向(方向)
   end
end


function UI模型格子:显示(x, y)
  if self.人物 then
     self.影子:显示(x + self.x, y + self.y)
  end
  if self.足迹 then
      self.足迹:显示(x + self.x, y + self.y)
  end
  if self.光环 then
      self.光环:显示(x + self.x, y + self.y)
  end
  if self.人物 then
      self.人物:显示(x + self.x, y + self.y)
  end
  if self.武器 then
      self.武器:显示(x + self.x, y + self.y)
  end
  if self.副武器 then
      self.副武器:显示(x + self.x, y + self.y)
  end
end
return UI模型格子
