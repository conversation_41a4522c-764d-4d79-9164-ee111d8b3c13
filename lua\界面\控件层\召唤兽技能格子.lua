--[[
    <AUTHOR> GGELUA
    @Date         : 2022-10-31 22:57:27
    @Last Modified by: GGELUA
    @Last Modified time: 2022-11-01 05:59:20
--]]
local 召唤兽技能格子 = class("召唤兽技能格子")
function 召唤兽技能格子:初始化()
  self.py = {x = 0, y = 0}
  self.文字 = false
end
超级技能转换表={}
超级技能转换表["夜战"]="超级夜战"
超级技能转换表["高级夜战"]="超级夜战"
超级技能转换表["反震"]="超级反震"
超级技能转换表["高级反震"]="超级反震"
超级技能转换表["吸血"]="超级吸血"
超级技能转换表["连击"]="超级连击"
超级技能转换表["飞行"]="超级飞行"
超级技能转换表["隐身"]="超级隐身"
超级技能转换表["感知"]="超级感知"
超级技能转换表["再生"]="超级再生"
超级技能转换表["冥思"]="超级冥思"
超级技能转换表["驱鬼"]="超级驱鬼"
超级技能转换表["毒"]="超级毒"
超级技能转换表["慧根"]="超级慧根"
超级技能转换表["必杀"]="超级必杀"
超级技能转换表["幸运"]="超级幸运"
超级技能转换表["精神集中"]="超级精神集中"
超级技能转换表["神迹"]="超级神迹"
超级技能转换表["招架"]="超级招架"
超级技能转换表["永恒"]="超级永恒"
超级技能转换表["敏捷"]="超级敏捷"
超级技能转换表["否定信仰"]="超级否定信仰"
超级技能转换表["魔之心"]="超级魔之心"
超级技能转换表["偷袭"]="超级偷袭"
超级技能转换表["雷属性吸收"]="超级雷属性吸收"
超级技能转换表["水属性吸收"]="超级水属性吸收"
超级技能转换表["火属性吸收"]="超级火属性吸收"
超级技能转换表["土属性吸收"]="超级土属性吸收"
超级技能转换表["强力"]="超级强力"
超级技能转换表["防御"]="超级防御"
超级技能转换表["法术连击"]="超级法术连击"
超级技能转换表["法术暴击"]="超级法术暴击"
超级技能转换表["壁垒击破"]="超级壁垒击破"
超级技能转换表["盾气"]="超级盾气"
超级技能转换表["合纵"]="超级合纵"
超级技能转换表["奔雷咒"]="超级奔雷咒"
超级技能转换表["泰山压顶"]="超级泰山压顶"
超级技能转换表["地狱烈火"]="超级地狱烈火"
超级技能转换表["水漫金山"]="超级水漫金山"
--------
超级技能转换表["高级吸血"]="超级吸血"
超级技能转换表["高级连击"]="超级连击"
超级技能转换表["高级飞行"]="超级飞行"
超级技能转换表["高级隐身"]="超级隐身"
超级技能转换表["高级感知"]="超级感知"
超级技能转换表["高级再生"]="超级再生"
超级技能转换表["高级冥思"]="超级冥思"
超级技能转换表["高级驱鬼"]="超级驱鬼"
超级技能转换表["高级毒"]="超级毒"
超级技能转换表["高级慧根"]="超级慧根"
超级技能转换表["高级必杀"]="超级必杀"
超级技能转换表["高级幸运"]="超级幸运"
超级技能转换表["高级精神集中"]="超级精神集中"
超级技能转换表["高级神迹"]="超级神迹"
超级技能转换表["高级招架"]="超级招架"
超级技能转换表["高级永恒"]="超级永恒"
超级技能转换表["高级敏捷"]="超级敏捷"
超级技能转换表["高级否定信仰"]="超级否定信仰"
超级技能转换表["高级魔之心"]="超级魔之心"
超级技能转换表["高级偷袭"]="超级偷袭"
超级技能转换表["高级雷属性吸收"]="超级雷属性吸收"
超级技能转换表["高级水属性吸收"]="超级水属性吸收"
超级技能转换表["高级火属性吸收"]="超级火属性吸收"
超级技能转换表["高级土属性吸收"]="超级土属性吸收"
超级技能转换表["高级强力"]="超级强力"
超级技能转换表["高级防御"]="超级防御"
超级技能转换表["高级法术连击"]="超级法术连击"
超级技能转换表["高级法术暴击"]="超级法术暴击"
超级技能转换表["高级盾气"]="超级盾气"
超级技能转换表["高级合纵"]="超级合纵"
超级技能转换表["法术波动"]="超级法术波动"
超级技能转换表["高级法术波动"]="超级法术波动"
超级技能转换表["法术抵抗"]="超级法术抵抗"
超级技能转换表["高级法术抵抗"]="超级法术抵抗"
超级技能转换表["遗志"]="超级遗志"
超级技能转换表["高级遗志"]="超级遗志"
超级技能转换表["反击"]="超级反击"
超级技能转换表["高级反击"]="超级反击"



取所有超级技能={}
取所有超级技能["超级夜战"]=1
取所有超级技能["超级反击"]=1
取所有超级技能["超级反震"]=1
取所有超级技能["超级吸血"]=1
取所有超级技能["超级连击"]=1
取所有超级技能["超级飞行"]=1
取所有超级技能["超级隐身"]=1
取所有超级技能["超级感知"]=1
取所有超级技能["超级再生"]=1
取所有超级技能["超级冥思"]=1
取所有超级技能["超级驱鬼"]=1
取所有超级技能["超级毒"]=1
取所有超级技能["超级慧根"]=1
取所有超级技能["超级必杀"]=1
取所有超级技能["超级幸运"]=1
取所有超级技能["超级精神集中"]=1
取所有超级技能["超级神迹"]=1
取所有超级技能["超级招架"]=1
取所有超级技能["超级永恒"]=1
取所有超级技能["超级敏捷"]=1
取所有超级技能["超级否定信仰"]=1
取所有超级技能["超级魔之心"]=1
取所有超级技能["超级偷袭"]=1
取所有超级技能["超级奔雷咒"]=1
取所有超级技能["超级泰山压顶"]=1
取所有超级技能["超级地狱烈火"]=1
取所有超级技能["超级水漫金山"]=1
取所有超级技能["超级雷属性吸收"]=1
取所有超级技能["超级水属性吸收"]=1
取所有超级技能["超级火属性吸收"]=1
取所有超级技能["超级土属性吸收"]=1
取所有超级技能["超级强力"]=1
取所有超级技能["超级防御"]=1
取所有超级技能["超级法术连击"]=1
取所有超级技能["超级法术暴击"]=1
取所有超级技能["超级壁垒击破"]=1
取所有超级技能["超级盾气"]=1
取所有超级技能["超级合纵"]=1
取所有超级技能["超级法术波动"]=1
取所有超级技能["超级法术抵抗"]=1
取所有超级技能["超级遗志"]=1
function 判断是否赐福技能(赐福技能组,原技能)
	if not 原技能 then
		return
	end
	local 技能名称=原技能.名称 or 原技能
	if 赐福技能组 then
		for k,v in pairs(赐福技能组) do
			if v==技能名称 then
				return 技能名称
			end
		end
	end
end
function 召唤兽技能格子:置数据(数据, w, h, lx, 认证,赐福技能,灰度)
  self.数据 = nil
  self.模型 = nil
  self.图像 = nil
  self.是否赐福= nil
  if 数据 then
    self.数据 = 取技能(数据,lx or "召唤兽")
    self.数据.名称 = 数据
    self.数据.类型 = "召唤兽技能"
    local wenj="shape/jn/"
      if self.数据[10] then
          wenj="shape/xinzengsucai/"
      end
    self.图像 = __res:取图像(__res:取地址(wenj, self.数据[7])):拉伸(w, h)
    if 灰度 then
      --print(灰度)
      self.图像:到灰度()
    end
    local nsf = require("SDL.图像")(w, h)
    if nsf:渲染开始() then
      self.图像:显示(0, 0)
      if 认证 and self.数据.名称 == 认证 then
        字体18:置颜色(__取颜色("红色"))
        字体18:取描边图像("认"):显示(0, 0)
      end
      nsf:渲染结束()
    end
    self.数据.是否赐福= 超级技能转换表[赐福技能]  or 赐福技能 --1
    if self.数据.是否赐福 and skill取技能[self.数据.是否赐福] then 
      self.数据[1]=skill取技能[self.数据.是否赐福].jns_1
    end
    self.模型 = nsf:到精灵()
  else
    self.模型 = __res:getPNGCC(3, 757, 291, 57, 56):拉伸(w, h):到精灵(w, h)
  end
end
function 召唤兽技能格子:详情打开(x, y, w, h)
  __UI弹出.技能详情:置可见(true, true)
  __UI弹出.技能详情.技能文本:清空()
  __UI弹出.技能详情:打开(self.数据, x - 240, y - 125, 240, 300, self.图像)
end
function 召唤兽技能格子:更新(dt)
end
function 召唤兽技能格子:显示(x, y)
  if self.模型 then
    self.模型:显示(x + self.py.x, y + self.py.y)
   if self.数据 and  取所有超级技能[self.数据.名称] then
      __主控.超级技能边框:显示(x + self.py.x, y + self.py.y)
    end
  end
  if self.确定 then
    __主控.技能选中小2:显示(x + self.py.x, y + self.py.y)
  end
  if self.数据 and self.数据.是否赐福 then
    __主控.赐福图标:显示(x + self.py.x+1, y + self.py.y+3)
  end
end
return 召唤兽技能格子
