local 游戏公告类 = __UI界面.界面层:创建控件("游戏公告类",0,65,引擎["宽度"],33) --创建提示控件
function 游戏公告类:初始化()
    local nsf = require("SDL.图像")(引擎["宽度"], 33)
    if nsf["渲染开始"](nsf) then
        local tx=__res:getPNGCC(1,1090,309,56,33)
        local num=qz(引擎["宽度"]/56)
        for i=0,num do
            tx:显示(i*56,0)
        end
        nsf["渲染结束"](nsf)
    end
    self:置精灵(nsf["到精灵"](nsf))
    self.xx=引擎["宽度"]-70
    self.数据组={}
    self.显示次数=3
end

function 游戏公告类:添加公告(内容)
    -- self.数据组[#self.数据组+1]="#Y"..内容
    if not self.是否可见 then
        self.xx=引擎["宽度"]-70
        self.显示次数=3
        self.提示文本11["清空"](self.提示文本11)
        self.提示文本11["置文本"](self.提示文本11, 内容)
        -- table.print(self.提示文本11)
        self:置可见(true,true)
        -- self.提示文本11:置可见(true)
    end
end
function 游戏公告类:更新()
    if self.是否可见 then
        self.xx=self.xx-2
        if self.xx<=0 then
            if self.显示次数~=0 then
                self.显示次数=self.显示次数-1
                self.xx=1000
            else
                self:置可见(false)
            end
        end
    end
end

local 提示文本11 = 游戏公告类["创建文本"](游戏公告类, "提示文本11", 0, 8, 引擎["宽度"], 30)
function 提示文本11:初始化()
    self.shifougundong=true
end

function 提示文本11:显示(x, y)
    for _, line in ipairs(self._数据表) do
        for _, v in ipairs(line) do
            v:显示(x+游戏公告类.xx, y + line.y)
        end
    end
end

