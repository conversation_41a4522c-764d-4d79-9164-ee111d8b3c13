


local 宠物 = 窗口层:创建窗口("宠物", 0,0, 330, 200)
local lsb = {
  "名称",
  "等级",
  "耐力",
  "经验"
}
local lsb2 = {
  "赐福",
  "洗炼",
  "合宠",
  "打书",
  "内丹",
  
}
function 宠物:初始化()
  self:创建纹理精灵(function ()
    置窗口背景("宠物", 0, 0, 330, 200, true):显示(0, 0)
    取白色背景(0, 0, 100, 100, true):显示(30, 40)
    local lssj = 取输入背景(0, 0, 120, 22)
    标题字体:置颜色(255, 255, 255)
    for i = 1, #lsb do
        lssj:显示(182, 30 + (i - 1) * 30)
        标题字体:取图像(lsb[i]):显示(144, 35 + (i - 1) * 30)
    end
  end
)


    self.模型格子 = __UI模型格子:创建()
    self:置坐标((引擎.宽度 - self.宽度) // 2, (引擎.高度 - self.高度) // 2)
    self.可初始化=true
    if __手机 then
      self.关闭:置大小(25,25)
      self.关闭:置坐标(self.宽度-27, 2)
    else
        self.关闭:置大小(16,16)
        self.关闭:置坐标(self.宽度-18, 2)
    end
end

function 宠物:更新(dt)
    self.模型格子:更新(dt)
end
function 宠物:显示(x,y)
  self.模型格子:显示(x,y)
  if self.图像 then
      self.图像:显示(x,y)
  end
end



function 宠物:打开()
  self:置可见(not self.是否可见)
  if not self.是否可见 then
        return
  end

 self.图像= self:创建纹理精灵(function()
                      for i = 1, #lsb do
                        标题字体:置颜色(39, 53, 81)
                        if "耐力" == lsb[i] then
                          标题字体:取图像(角色信息.宠物[lsb[i]] .. "/" .. 角色信息.宠物.最大耐力):显示(190, 35 + (i - 1) * 30)
                        elseif "经验" == lsb[i] then
                          标题字体:取图像(角色信息.宠物[lsb[i]] .. "/" .. 角色信息.宠物.最大经验):显示(190, 35 + (i - 1) * 30)
                        else
                          标题字体:取图像(角色信息.宠物[lsb[i]]):显示(190, 35 + (i - 1) * 30)
                        end
                      end
              end,1
            )

  宠物.模型格子:置数据(角色信息.宠物.模型, "宠物", 80, 120)
end


local 关闭 = 宠物:创建关闭按钮("关闭")
function 关闭:左键弹起(x, y, msg)
  宠物:置可见(false)
end
local pyx = 0
local pyy = 0
for i = 1, #lsb2 do
  if i > 3 then
    pyx = -450
    pyy = 60
  end
  local 临时函数 = 宠物:创建红色按钮(lsb2[i], lsb2[i], 15+(i-1)*60,165,55,22,文本字体)
  
 function  临时函数:左键弹起(x, y, msg)
    if "洗炼" == lsb2[i] then
        请求服务(5007)
    elseif "炼化" == lsb2[i] then
        请求服务(5007)
    elseif "打书" == lsb2[i] then
        请求服务(5016)
    elseif "内丹" == lsb2[i] then
        请求服务(5017)
    elseif "合宠" == lsb2[i] then
        请求服务(5015.1)
    elseif "赐福" == lsb2[i] then
        -- 请求服务(5026)  
        请求服务(5021,{类型="打开"})
        宠物:置可见(false)
    end
  end
end


