--[[
LastEditTime: 2024-05-04 01:20:44
--]]
local 战斗自动 = 战斗层:创建窗口("战斗自动", 0, 0, 220, 140)
function 战斗自动:初始化()
        self.可初始化=true
          if  __手机 then
              self:置坐标(10, 界面层.高度-界面层.聊天控件.高度-self.高度-5)
              self.关闭:置大小(25,25)
              self.关闭:置坐标(self.宽度-30, 5)
          else
              self.关闭:置大小(16,16)
              self.关闭:置坐标(self.宽度-21, 5)
              if 界面层.聊天控件.聊天窗口 then
                  self:置坐标(10, 界面层.高度-self.高度-40)
              else
                  self:置坐标(10, 界面层.高度-205-self.高度)
              end
          end
end

function 战斗自动:取自动语句(类型)
  if 类型==1 then --人物
		local 编号=0
		for n=1,#__战斗主控.战斗单位 do
			if __战斗主控.战斗单位[n].类型=="角色" and __战斗主控.战斗单位[n].数字id==角色信息.数字id then
				编号=n
			end
		end

    
		local 语句="攻击"
		if 编号==0 then return "无" end
		if __战斗主控.战斗单位[编号].数据.自动指令~=nil then
			if __战斗主控.战斗单位[编号].数据.自动指令.类型=="法术" then
				  语句=__战斗主控.战斗单位[编号].数据.自动指令.参数
			else
				  语句=__战斗主控.战斗单位[编号].数据.自动指令.类型
			end
		end
		return 语句
	elseif 类型==2 then --人物
      local 编号=0
      for n=1,#__战斗主控.战斗单位 do
        if __战斗主控.战斗单位[n].类型~="角色" and __战斗主控.战斗单位[n].数字id==角色信息.数字id then
          编号=n
        end
      end
		if 编号==0 then return "无" end
      local 语句="攻击"
      if __战斗主控.战斗单位[编号].数据.自动指令~=nil then
          if __战斗主控.战斗单位[编号].数据.自动指令.类型=="法术" then
            语句=__战斗主控.战斗单位[编号].数据.自动指令.参数
          else
            语句=__战斗主控.战斗单位[编号].数据.自动指令.类型
        end
      end
		  return 语句
	end
end


function 战斗自动:打开()
      self:置可见(not self.是否可见)
      if not self.是否可见 then
          return
      end
      self:显示重置()
end

function 战斗自动:显示重置()
  self:创建纹理精灵(function()
    置窗口背景("自动战斗",0,0,220,140,true):显示(0,0)
    文本字体:置颜色(255, 255, 255)
    文本字体:取图像("剩余"):显示(66,33)
    文本字体:取图像("回合"):显示(66+文本字体:取宽度("剩余 ∞ "), 33)
    文本字体:取图像("人物："):显示(20,55)
    文本字体:取图像("召唤兽："):显示(20,82)
    文本字体:置颜色(__取颜色("黄色"))
    文本字体:取图像(self:取自动语句(1)):显示(20+文本字体:取宽度("人物："), 55)
    文本字体:取图像(self:取自动语句(2)):显示(20+文本字体:取宽度("召唤兽："), 82)
    文本字体:取图像("∞"):显示(66+文本字体:取宽度("剩余 "), 33)
  end)
  if __战斗主控.自动开关 then
      self.自动开关:重置文字("取消")
  else
      self.自动开关:重置文字("开启")
  end
end


local 自动开关 = 战斗自动:创建红色按钮("开启", "自动开关",135,110,60,22) 
function 自动开关:重置文字(txt,jz)
    self:置文字(74,22,txt)
    self:置禁止(false)
    if jz then
        self:置禁止(jz)
    end
end
function 自动开关:左键弹起(x,y)
    请求服务(5507)
end
local 重置按钮 = 战斗自动:创建红色按钮("重置", "重置按钮",70,110,60,22) 

local 暂离按钮 = 战斗自动:创建红色按钮("暂离", "暂离按钮",5,110,60,22) 


 local 关闭 = 战斗自动:创建关闭按钮("关闭")
 function 关闭:左键弹起(x,y)
     战斗自动:置可见(false)
 end