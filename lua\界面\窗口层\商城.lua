local shangcheng = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "商城", 163-59, 8 + abbr.py.y, 838, 534)
local 菜单 ={"银子商城","仙玉商城","宝宝","法宝","特殊商城","锦衣","光环","足迹","祥瑞"}
function shangcheng:加载数据(sj)
  self.数据={}
  for k,v in pairs(sj) do
    -- print(k,v)
    for i=1,#菜单 do
      if k==菜单[i] then
        self.数据[k]=v
      end
    end
  end
  -- table.print(sj)
end
function shangcheng:初始化()
  local nsf = require("SDL.图像")(838, 534)
  if nsf["渲染开始"](nsf) then
    __res:getPNGCC(6, 0, 0, 798, 511):置透明(240):显示(0, 0)
    取灰色背景(0, 0, 649+4, 399, true):置透明(220):显示(171-38, 61-5)
    local lssj = 取输入背景(0, 0, 130, 23)
    字体18["置颜色"](字体18, __取颜色("白色"))
    -- 字体18["取图像"](字体18, "现金"):显示(25, 485-20+5)
    字体18["取图像"](字体18, "价格"):显示(25+120, 485-13)
    字体18["取图像"](字体18, "数量"):显示(0+240-10+120, 485-13)
    字体16["置颜色"](字体16, __取颜色("白色"))
    -- 字体16["取图像"](字体16, "玉"):显示(282-7, 17)
    -- 字体16["取图像"](字体16, "银"):显示(475-9, 17)
    -- 字体16["取图像"](字体16, "分"):显示(622, 17)
    -- lssj["显示"](lssj, 25+43, 485-2-20+5)
    lssj["显示"](lssj, 25+43+120, 485-2-13)
    lssj["显示"](lssj, 0+240+43-10+120, 485-2-13)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.选中总类="银子商城"
end
-- 160.68  542  118
function shangcheng:打开(data)
  
  self:置可见(true)
  self.道具网格:置物品()
  self.数量输入["置数值"](self.数量输入, 1)
  local zifu="银子商城"
  if self.选中总类=="特殊商城" then
    zifu="特殊商城"
  elseif self.选中总类=="法宝" then
    zifu="法宝商城"
  end
  self.选中物品=0
  self[zifu.."按钮"]:置选中(true)
  self.zongjiage=0
  self.重置(self)
  -- __置窗口坐标(self)
end

local 银子商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "银子商城按钮", 0, 60, "银子商城")
function 银子商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="银子商城"
  shangcheng.道具网格:置可见(true)
  shangcheng.模型网格:置可见(false)
  shangcheng.道具网格:置物品(11)
end
local 仙玉商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "仙玉商城按钮", 0, 60+39*1, "仙玉商城")
function 仙玉商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="仙玉商城"
  shangcheng.道具网格:置可见(true)
  shangcheng.模型网格:置可见(false)
  shangcheng.道具网格:置物品(1)
end
local 宝宝商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "宝宝商城按钮", 0, 60+39*2, "宝宝商城")
function 宝宝商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="宝宝"
  shangcheng.道具网格:置可见(false)
  shangcheng.模型网格:置可见(true)
  shangcheng.模型网格:置物品(2)
end
local 法宝商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "法宝商城按钮", 0, 60+39*3, "法宝商城")
function 法宝商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="法宝"
  shangcheng.道具网格:置可见(true)
  shangcheng.模型网格:置可见(false)
  shangcheng.道具网格:置物品(1)
end
local 特殊商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "特殊商城按钮", 0, 60+39*4, "特殊商城")
function 特殊商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="特殊商城"
  shangcheng.道具网格:置可见(true)
  shangcheng.模型网格:置可见(false)
  shangcheng.道具网格:置物品(1)
end
local 锦衣商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "锦衣商城按钮", 0, 60+39*5, "锦衣商城")
function 锦衣商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="锦衣"
  shangcheng.道具网格:置可见(false)
  shangcheng.模型网格:置可见(true)
  shangcheng.模型网格:置物品(2)
end
local 光环商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "光环商城按钮", 0, 60+39*6, "光环商城")
function 光环商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="光环"
  shangcheng.道具网格:置可见(false)
  shangcheng.模型网格:置可见(true)
  shangcheng.模型网格:置物品(2)
end
local 足迹商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "足迹商城按钮", 0, 60+39*7, "足迹商城")
function 足迹商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="足迹"
  shangcheng.道具网格:置可见(false)
  shangcheng.模型网格:置可见(true)
  shangcheng.模型网格:置物品(2)
end
local 祥瑞商城按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "祥瑞商城按钮", 0, 60+39*8, "祥瑞商城")
function 祥瑞商城按钮:左键弹起(x, y, msg)
  shangcheng.选中总类="祥瑞"
  shangcheng.道具网格:置可见(false)
  shangcheng.模型网格:置可见(true)
  shangcheng.模型网格:置物品(1)
end
-- local 翻一番按钮 = shangcheng["创建我的单选按钮"](shangcheng, __res:getPNGCC(6, 0, 514, 125, 38, true), __res:getPNGCC(6, 0, 554, 125, 38, true), "翻一番按钮", 0, 60+39*9, "翻一翻")
-- function 翻一番按钮:左键弹起(x, y, msg)
--   if not __UI界面["窗口层"]["翻一番"].是否可见 then
--     发送数据(6601)
--     shangcheng["置可见"](shangcheng, false)
--     shangcheng.选中物品 = 0
--   end
-- end






function shangcheng:重置()
  local nsf = require("SDL.图像")(590, 499)
  if nsf["渲染开始"](nsf) then
    
    local shul=shangcheng["数量输入"]:取数值()
    if shul and shangcheng.选中物品~=0 and shangcheng.数据[shangcheng["选中总类"]] and shangcheng.数据[shangcheng["选中总类"]][shangcheng.选中物品] and shul>0 then
      local zongj=shangcheng.数据[shangcheng["选中总类"]][shangcheng.选中物品].价格--shul*shangcheng.数据[shangcheng["选中总类"]][shangcheng.选中物品].价格
      字体18["置颜色"](字体18, __取颜色("浅黑"))
      字体18["取图像"](字体18, zongj):显示(128, 23+23+2-15-13)
     -- print(zongj)
    end
    nsf["渲染结束"](nsf)
  end
  self.图像 = nsf["到精灵"](nsf)
  self.图像["置中心"](self.图像, -75, -368-84)
  local nsf1 = require("SDL.图像")(645, 52)
  if nsf1["渲染开始"](nsf1) then
    -- 字体16:置颜色(__取颜色("白色")):置样式(6)
    -- 字体16:取图像("暂无仙玉"):显示(199, 17)
    置轮廓文字(字体16,角色信息.仙玉 or 0,"黑色","绿色",195+24, 17)
    -- 字体16:置颜色(__取银子颜色(角色信息.银子))
    -- 字体16:取图像(角色信息.银子):显示(351, 17)
    -- 置轮廓文字(字体16,角色信息.银子,"黑色",__取银子颜色2(角色信息.银子),351+57, 17)
    置轮廓文字(字体16,角色信息.银子,"黑色","紫色",351+57, 17)
    nsf1["渲染结束"](nsf1)
  end
  self.图像2 = nsf1["到精灵"](nsf1)
end


local 关闭 = shangcheng["创建我的按钮"](shangcheng, __res:getPNGCC(6, 800, 11, 31, 31), "关闭", 910-78+5-54-25, 12)
function 关闭:左键弹起(x, y, msg)
  shangcheng["置可见"](shangcheng, false)
  shangcheng.选中物品 = 0
end

local 道具网格 = shangcheng["创建网格"](shangcheng, "道具网格", 177-38, 73-8, 640, 382)
-- local 道具网格 = shangcheng["创建网格"](shangcheng, "道具网格", 223-189, 71, 672, 384)
function 道具网格:初始化()
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if shangcheng["选中物品"]~=0 and self.子控件[shangcheng["选中物品"]] and self.子控件[shangcheng["选中物品"]]._spr then
    self.子控件[shangcheng["选中物品"]]._spr["确定"] = nil
  end
  if self.子控件[a]._spr and self.子控件[a]._spr["物品"] then
    shangcheng["选中物品"] = a
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["确定"] = true
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 0, 86, w, h, "选择", a)
    shangcheng:重置()
  end
end
function 道具网格:置物品()
  local sl = 1
  local 商品=shangcheng.数据[shangcheng.选中总类] 
  if #商品 > 4 then
    sl = #商品 // 4 + 1
  end
  self:创建格子(121, 121, 10, 9, sl, 5, true)
  for i = 1, #self.子控件 do
    local lssj = __商城格子["创建"]()
    if 商品[i] then
      lssj["置物品"](lssj, 商品[i], shangcheng.选中总类)
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    else
      self.子控件[i]["置精灵"](self.子控件[i], nil)
    end
  end
end




local 模型网格 = shangcheng["创建网格"](shangcheng, "模型网格", 177-38, 73-8, 640, 382)
-- local 模型网格 = shangcheng["创建网格"](shangcheng, "模型网格", 223-189, 71, 672, 384)
function 模型网格:初始化()
end

function 模型网格:左键弹起(x, y, a, b, msg)
  if shangcheng["选中物品"]~=0 then
    if self.子控件[shangcheng["选中物品"]] then
      self.子控件[shangcheng["选中物品"]]._spr["确定"] = nil
    end
  end
  if self.子控件[a]._spr and self.子控件[a]._spr["数据"] then
    shangcheng["选中物品"] = a
    local w, h = self.子控件[a]["取宽高"](self.子控件[a])
    self.子控件[a]._spr["确定"] = true
    shangcheng:重置()
  end
end

function 模型网格:置物品()
  local sl = 1
  local 商品=shangcheng.数据[shangcheng.选中总类] 
  -- table.print(商品)
  if #商品 >= 4 then
    sl = #商品 // 4 + 1
  end
  self:创建格子(154, 180, 10, 9, sl, 4, true)
  for i = 1, #self.子控件 do
    local lssj = __商城锦衣格子["创建"]()
    lssj["置物品"](lssj, 商品[i], shangcheng.选中总类)
    if 商品[i] then
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
      if shangcheng.选中总类=="宝宝" then
        local 按钮 = self.子控件[i]["创建我的按钮"](self.子控件[i], __res:getPNGCC(4, 822, 9, 40, 40), "按钮" .. i, 111, 10) 
        按钮:置可见(true,true)
        function  按钮:左键单击(x, y, msg)
            __UI弹出["商城宝宝查看"]:打开(商品[i],"物理")
        end
      end
    else
      self.子控件[i]["置精灵"](self.子控件[i], nil)
    end
  end
end















local 数量输入 = shangcheng["创建我的输入"](shangcheng, "数量输入", 90+202-10+47+120, 445+41-13, 50, 24, 2, 2, "黑色")
-- function 数量输入:初始化()
--   self:置限制字数(5)
--   self:置模式(self.数字模式)
-- end
-- function 数量输入:是否选中()
--   print(数量输入:_消息事件())
--   if self:取数值() then
--     shangcheng:重置()
--   end
-- end
local 数量加 = shangcheng["创建我的按钮"](shangcheng, __res:getPNGCC(1, 641, 320, 29, 29), "数量加", 274+104+120, 485-5-13)
function 数量加:左键弹起(x, y, msg)
  local num=shangcheng["数量输入"]:取数值()
  if num and num<99 then
    num=num+1
    shangcheng.数量输入["置数值"](shangcheng.数量输入, num)
  end
end
local 数量减 = shangcheng["创建我的按钮"](shangcheng, __res:getPNGCC(1, 601, 319, 29, 29), "数量减", 274-5+120, 485-5-13)
function 数量减:左键弹起(x, y, msg)
  local num=shangcheng["数量输入"]:取数值()
  if num and num>1 then
    num=num-1
    shangcheng.数量输入["置数值"](shangcheng.数量输入, num)
  end
end
-- local CDK兑换 = shangcheng["创建我的按钮"](shangcheng, __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(99, 35), "CDK兑换", 15, 485-7-20, "CDK兑换")
-- function CDK兑换:左键弹起(x, y, msg)
--   __UI界面["窗口层"].CDK兑换:打开()
-- end
local 购买 = shangcheng["创建我的按钮"](shangcheng, __res:getPNGCC(3, 2, 507, 124, 41, true):拉伸(151, 35), "购买", 461-20+120, 485-7-13, "购 买")
-- local 上一页 = shangcheng["创建我的按钮"](shangcheng, __res:getPNGCC(3, 335, 561, 96, 35), "上一页", 461-20+180, 485-7+3)
-- local 下一页 = shangcheng["创建我的按钮"](shangcheng, __res:getPNGCC(3, 436, 561, 96, 34), "下一页", 461-20+180+130, 485-7+3)

function 购买:左键弹起(x, y, msg)
  -- print(shangcheng.选中物品~=0 , shangcheng.数据[shangcheng["选中总类"]] , shangcheng.数据[shangcheng["选中总类"]][shangcheng.选中物品] , shangcheng["数量输入"]:取数值())
  if  shangcheng.选中物品~=0 and shangcheng.数据[shangcheng["选中总类"]] and shangcheng.数据[shangcheng["选中总类"]][shangcheng.选中物品] and shangcheng["数量输入"]:取数值() then
    if shangcheng["选中总类"] == "宝宝" then
      shangcheng.发送信息 = {
        编号 = shangcheng.选中物品,--self.物品数据[self.分类][shangcheng.选中物品].编号,
        位置 = shangcheng.选中物品,
        数量 = 1,
        组号 = 18,
        分类 = shangcheng["选中总类"]
      }
      __UI界面["窗口层"]["对话栏"]:打开("","神兽使者 ","少侠您想购买哪种类型的#G"..shangcheng.数据[shangcheng["选中总类"]][shangcheng.选中物品].名称.."#W呢？",{"物理型（单点爆伤）","法术型（秒伤）"})
    else
      发送数据(30, {
        ["编号"] =  shangcheng.选中物品,
        ["位置"] =  shangcheng.选中物品,
        ["数量"] = shangcheng["数量输入"]["取数值"](shangcheng["数量输入"]),
        ["组号"] = 18,
        ["分类"] = shangcheng["选中总类"]
      })
    end
  end
end