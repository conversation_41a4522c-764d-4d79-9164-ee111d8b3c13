local 摊位制造 = __UI界面["窗口层"]["创建我的窗口"](__UI界面["窗口层"], "摊位制造", 536 + abbr.py.x, 5 + abbr.py.y, 417, 519)
function 摊位制造:初始化()
  local nsf = require("SDL.图像")(417, 519)
  if nsf["渲染开始"](nsf) then
    置窗口背景("制造", 0, 12, 409, 505, true)["显示"](置窗口背景("制造", 0, 12, 409, 505, true), 0, 0)
    取灰色背景(0, 0, 365, 275, true)["显示"](取灰色背景(0, 0, 365, 275, true), 24, 118)
    字体18["置颜色"](字体18, __取颜色("白色"))
    字体18["取图像"](字体18, "材\n料")["显示"](字体18["取图像"](字体18, "材\n料"), 106, 56)
    字体18["取图像"](字体18, "材\n料")["显示"](字体18["取图像"](字体18, "材\n料"), 206, 56)
    local lssj = 取输入背景(0, 0, 92, 23)
    字体18["取图像"](字体18, "支付工钱")["显示"](字体18["取图像"](字体18, "支付工钱"), 21, 406)
    字体18["取图像"](字体18, "中介费用")["显示"](字体18["取图像"](字体18, "中介费用"), 214, 406)
    字体18["取图像"](字体18, "制作费用")["显示"](字体18["取图像"](字体18, "制作费用"), 21, 438)
    字体18["取图像"](字体18, "现有金钱")["显示"](字体18["取图像"](字体18, "现有金钱"), 214, 438)
    lssj["显示"](lssj, 105, 404)
    lssj["显示"](lssj, 298, 404)
    lssj["显示"](lssj, 105, 436)
    lssj["显示"](lssj, 298, 436)
  end
  self:置精灵(nsf["到精灵"](nsf))
end
function 摊位制造:打开(data)
  self:置可见(true)
  self.数据 = data
  self.重置(self)
  self.道具网格["置物品"](self.道具网格, __主控["道具列表"])
  self.材料网格["置物品"](self.材料网格, {})
end
function 摊位制造:重置()
  local nsf = require("SDL.图像")(409, 69)
  if nsf["渲染开始"](nsf) then
    字体18["置颜色"](字体18, __取银子颜色(self.数据["价格"]))
    字体18["取图像"](字体18, self.数据["价格"])["显示"](字体18["取图像"](字体18, self.数据["价格"]), 113, 14)
    字体18["置颜色"](字体18, __取银子颜色(角色信息["银子"]))
    字体18["取图像"](字体18, 角色信息["银子"])["显示"](字体18["取图像"](字体18, 角色信息["银子"]), 305, 45)
    nsf["渲染结束"](nsf)
  end
  摊位制造["图像"] = nsf["到精灵"](nsf)
  摊位制造["图像"]["置中心"](摊位制造["图像"], 0, -398)
end
local 关闭 = 摊位制造["创建我的按钮"](摊位制造, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 367, 0)
function 关闭:左键弹起(x, y, msg)
  摊位制造["置可见"](摊位制造, false)
end
local 道具网格 = 摊位制造["创建网格"](摊位制造, "道具网格", 43, 128, 318, 252)
function 道具网格:初始化()
  self:创建格子(55, 55, 10, 10, 4, 5)
end
function 道具网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    for i = 1, #摊位制造["材料网格"]["子控件"] do
      if not 摊位制造["材料网格"]["子控件"][i]._spr["物品"] then
        self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
        摊位制造["材料网格"]["置物品"](摊位制造["材料网格"], self.子控件[a]._spr["物品"], i)
        摊位制造["材料网格"]["子控件"][i]._spr["物品"]["原始编号"] = a
        self:置物品(nil, a)
        break
      end
    end
  end
end
function 道具网格:置物品(data, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __物品格子["创建"]()
      lssj["置物品"](lssj, data[i], "白格子", "战斗道具")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __物品格子["创建"]()
    lssj["置物品"](lssj, data, "白格子", "战斗道具")
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end
local 材料网格 = 摊位制造["创建网格"](摊位制造, "材料网格", 134, 55, 155, 55)
function 材料网格:初始化()
  self:创建格子(55, 55, 0, 45, 1, 2)
end
function 材料网格:左键弹起(x, y, a, b, msg)
  if self.子控件[a]._spr["物品"] then
    self.子控件[a]._spr["详情打开"](self.子控件[a]._spr, 170, 86, w, h, "选择", a)
    摊位制造["道具网格"]["置物品"](摊位制造["道具网格"], self.子控件[a]._spr["物品"], self.子控件[a]._spr["物品"]["原始编号"])
    self:置物品(nil, a)
  end
end
function 材料网格:置物品(数据, bh)
  if not bh then
    for i = 1, #self.子控件 do
      local lssj = __商店格子["创建"]()
      lssj["置物品"](lssj, 数据[i], "制造")
      self.子控件[i]["置精灵"](self.子控件[i], lssj)
    end
  else
    local lssj = __商店格子["创建"]()
    lssj["置物品"](lssj, 数据, "制造")
    self.子控件[bh]["置精灵"](self.子控件[bh], lssj)
  end
end
for i, v in ipairs({
  {
    name = "确定",
    x = 146,
    y = 466,
    tcp = __res:getPNGCC(3, 511, 11, 117, 43, true)["拉伸"](__res:getPNGCC(3, 511, 11, 117, 43, true), 118, 41),
    font = "确定"
  }
}) do
  local 临时函数 = 摊位制造["创建我的按钮"](摊位制造, v.tcp, v.name, v.x, v.y, v.font)
 function  临时函数:左键弹起(x, y)
    if v.name == "确定" then
      if 摊位制造["材料网格"]["子控件"][1]._spr["物品"] and 摊位制造["材料网格"]["子控件"][2]._spr["物品"] then
        发送数据(4501, {
          ["序列"] = 摊位制造["材料网格"]["子控件"][1]._spr["物品"]["原始编号"],
          ["序列1"] = 摊位制造["材料网格"]["子控件"][2]._spr["物品"]["原始编号"],
          ["对方id"] = 摊位制造["数据"]["对方id"],
          ["分类标识"] = 摊位制造["数据"]["分类标识"],
          ["功能标识"] = 摊位制造["数据"]["功能标识"],
          ["工钱"] = 摊位制造["数据"]["价格"]
        })
      else
        __UI弹出["提示框"]["打开"](__UI弹出["提示框"], "#Y请放入材料")
      end
    end
  end
end
