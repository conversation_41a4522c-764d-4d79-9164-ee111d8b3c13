--[[
Author: GGELUA
Date: 2024-09-04 16:18:56
Last Modified by: GGELUA
Last Modified time: 2024-11-13 14:55:36
--]]
local 人物称谓 = __UI界面["窗口层"]["创建窗口"](__UI界面["窗口层"], "人物称谓", 194 + abbr.py.x, 22 + abbr.py.y, 380, 480)
function 人物称谓:初始化()
  local nsf = require("SDL.图像")(360, 480)
  if nsf["渲染开始"](nsf) then
    置窗口背景("人物称谓", 0, 12, 360, 460, true)["显示"](置窗口背景("人物称谓", 0, 12, 360, 460, true), 0, 0)
    取属性背景(0, 0, 320, 320, true)["显示"](取属性背景(0, 0, 320, 320, true), 20, 80)
    字体18:置颜色(__取颜色(紫色))
    字体18:取图像("当前称谓："):显示(20, 50)
    nsf["渲染结束"](nsf)
  end
  self:置精灵(nsf["到精灵"](nsf))
  self.选中 = nil
end
function 人物称谓:显示(x, y)
  字体18["显示"](字体18, x + 110, y + 50, 角色信息["当前称谓"])
end
function 人物称谓:打开()
  self:置可见(true)
  人物称谓["称谓列表"]["重置"](人物称谓["称谓列表"])
  self.选中 = nil
end
local 关闭 = 人物称谓["创建我的按钮"](人物称谓, __res:getPNGCC(1, 401, 0, 46, 46), "关闭", 320, 0)
function 关闭:左键弹起(x, y, msg)
  人物称谓["置可见"](人物称谓, false)
end
local 称谓列表 = 人物称谓["创建列表"](人物称谓, "称谓列表", 30, 90, 300, 300)
function 称谓列表:初始化()
  self:置文字(字体18)
  self.行间距 = 15
  self.选中精灵 = require('SDL.精灵')(0, 0, 0, self.宽度, 0):置颜色(1,255,1, 128)
end
function 称谓列表:重置()
  称谓列表["清空"](称谓列表)
  称谓列表["置颜色"](称谓列表, 255, 255, 255)
  for i = 1, #角色信息["称谓"] do
    称谓列表["添加"](称谓列表, 角色信息["称谓"][i])
  end
end
function 称谓列表:左键弹起(x, y, i, item, msg)
  人物称谓["选中"] = i
end
local 删除称谓 = 人物称谓["创建我的按钮"](人物称谓, __res:getPNGCC(3, 2, 507, 124, 41, true), "删除称谓", 15, 420, "删除称谓")
function 删除称谓:左键弹起(x, y, msg)
  发送数据(74, {
    ["称谓"] = 人物称谓["称谓列表"][人物称谓["选中"]]
  })
  人物称谓:置可见(false)
  -- 角色信息["当前称谓"] = ""
  -- __主显["主角"]["置名称"](__主显["主角"])
end
local 改变 = 人物称谓["创建我的按钮"](人物称谓, __res:getPNGCC(3, 2, 507, 124, 41, true), "改变", 186+36, 420, "改变")
function 改变:左键弹起(x, y, msg)
  if 人物称谓["选中"] then
    发送数据(31, {
      ["称谓ID"] = 人物称谓["选中"]
    })
    角色信息["当前称谓"] = 角色信息["称谓"][人物称谓["选中"]]
    -- print(角色信息["当前称谓"])
    __主显["主角"]["置名称"](__主显["主角"])
    __主显["主角"]:置称谓(角色信息.当前称谓)
  end
end
-- elseif self.资源组[4]:事件判断() then --隐藏
--   tp.当前称谓 = "无称谓"
--   发送数据(31,{称谓ID=0})
